import { HandlerList } from './core/HandlerList.js';
import { handleRetriesFor, retryAfter } from './util/Retries.js';
import { OptionList } from './util/Options.js';
import { MathDocument } from './core/MathDocument.js';
export declare const mathjax: {
    version: string;
    handlers: HandlerList<any, any, any>;
    document: (document: any, options: OptionList) => MathDocument<any, any, any>;
    handleRetriesFor: typeof handleRetriesFor;
    retryAfter: typeof retryAfter;
    asyncLoad: (file: string) => any;
};
