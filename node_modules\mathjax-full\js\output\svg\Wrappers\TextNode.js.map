{"version": 3, "file": "TextNode.js", "sourceRoot": "", "sources": ["../../../../ts/output/svg/Wrappers/TextNode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,+DAA0D;AAC1D,4CAAyD;AACzD,iEAAsE;AAYtE;IACA,+BAA8D;IAD9D;;IAsCA,CAAC;IAlBQ,2BAAK,GAAZ,UAAa,MAAS;;QACpB,IAAM,IAAI,GAAI,IAAI,CAAC,IAAiB,CAAC,OAAO,EAAE,CAAC;QAC/C,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAC9B,IAAI,OAAO,KAAK,eAAe,EAAE;YAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;SACjF;aAAM;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAC,eAAe,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC;aAC/F;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;;gBACV,KAAgB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;oBAAlB,IAAM,CAAC,kBAAA;oBACV,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;iBAC/C;;;;;;;;;SACF;IACH,CAAC;IA9Ba,gBAAI,GAAG,qBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;IAK/B,kBAAM,GAAc;QAChC,6EAA6E,EAAE;YAC7E,cAAc,EAAE,CAAC;SAClB;KACF,CAAC;IAuBJ,kBAAC;CAAA,AAtCD,CACA,IAAA,iCAAmB,EAAgC,uBAAU,CAAC,GAqC7D;AAtCY,kCAAW"}