{"version": 3, "sources": ["../../mathjax-full/ts/core/OutputJax.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/math.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mi.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mn.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mtext.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mspace.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/ms.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mrow.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mfrac.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/msqrt.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mroot.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/merror.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mpadded.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mphantom.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mfenced.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/menclose.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/maction.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/msubsup.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/munderover.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mmultiscripts.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mtable.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mtr.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mtd.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mglyph.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/semantics.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/TeXAtom.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for the OutputJax\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {userOptions, defaultOptions, OptionList} from '../util/Options.js';\nimport {MathDocument} from './MathDocument.js';\nimport {MathItem} from './MathItem.js';\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\nimport {FunctionList} from '../util/FunctionList.js';\n\n/*****************************************************************/\n/**\n *  The OutputJax interface\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface OutputJax<N, T, D> {\n  /**\n   * The name of this output jax class\n   */\n  name: string;\n\n  /**\n   * The options for the instance\n   */\n  options: OptionList;\n\n  /**\n   * Lists of post-filters to call after typesetting the math\n   */\n  postFilters: FunctionList;\n\n  /**\n   * The DOM adaptor for managing HTML elements\n   */\n  adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * @param {DOMAdaptor} adaptor The adaptor to use in this jax\n   */\n  setAdaptor(adaptor: DOMAdaptor<N, T, D>): void;\n\n  /**\n   * Do any initialization that depends on the document being set up\n   */\n  initialize(): void;\n\n  /**\n   * Reset any needed features of the output jax\n   *\n   * @param {any[]} args   The arguments needed by the reset operation\n   */\n  reset(...args: any[]): void;\n\n  /**\n   * Typset a given MathItem\n   *\n   * @param {MathItem} math          The MathItem to be typeset\n   * @param {MathDocument} document  The MathDocument in which the typesetting should occur\n   * @return {N}                     The DOM tree for the typeset math\n   */\n  typeset(math: MathItem<N, T, D>, document?: MathDocument<N, T, D>): N;\n\n  /**\n   * Handle an escaped character (e.g., \\$ from the TeX input jax preventing it from being a delimiter)\n   *\n   * @param {MathItem} math          The MathItem to be escaped\n   * @param {MathDocument} document  The MathDocument in which the math occurs\n   * @return {N}                     The DOM tree for the escaped item\n   */\n  escaped(math: MathItem<N, T, D>, document?: MathDocument<N, T, D>): N;\n\n  /**\n   * Get the metric information for all math in the given document\n   *\n   * @param {MathDocument} document  The MathDocument being processed\n   */\n  getMetrics(document: MathDocument<N, T, D>): void;\n\n  /**\n   * Produce the stylesheet needed for this output jax\n   *\n   * @param {MathDocument} document  The MathDocument being processed\n   */\n  styleSheet(document: MathDocument<N, T, D>): N;\n\n  /**\n   * Produce any page-specific elements needed for this output jax\n   *\n   * @param {MathDocument} document  The MathDocument being processed\n   */\n  pageElements(document: MathDocument<N, T, D>): N;\n}\n\n\n/*****************************************************************/\n/**\n *  The OutputJax abstract class\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractOutputJax<N, T, D> implements OutputJax<N, T, D> {\n\n  /**\n   * The name for the output jax\n   */\n  public static NAME: string = 'generic';\n\n  /**\n   * The default options for the output jax\n   */\n  public static OPTIONS: OptionList = {};\n\n  /**\n   * The actual options supplied to the output jax\n   */\n  public options: OptionList;\n\n  /**\n   * Filters to run after the output is processed\n   */\n  public postFilters: FunctionList;\n\n  /**\n   * The MathDocument's DOMAdaptor\n   */\n  public adaptor: DOMAdaptor<N, T, D> = null;  // set by the handler\n\n  /**\n   * @param {OptionList} options  The options for this instance\n   */\n  constructor(options: OptionList = {}) {\n    let CLASS = this.constructor as typeof AbstractOutputJax;\n    this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), options);\n    this.postFilters = new FunctionList();\n  }\n\n  /**\n   * @return {string}  The name for this output jax class\n   */\n  public get name(): string {\n    return (this.constructor as typeof AbstractOutputJax).NAME;\n  }\n\n  /**\n   * @override\n   */\n  public setAdaptor(adaptor: DOMAdaptor<N, T, D>) {\n    this.adaptor = adaptor;\n  }\n\n  /**\n   * @override\n   */\n  public initialize() {\n  }\n\n  /**\n   * @override\n   */\n  public reset(..._args: any[]) {\n  }\n\n  /**\n   * @override\n   */\n  public abstract typeset(math: MathItem<N, T, D>, document?: MathDocument<N, T, D>): N;\n\n  /**\n   * @override\n   */\n  public abstract escaped(math: MathItem<N, T, D>, document?: MathDocument<N, T, D>): N;\n\n  /**\n   * @override\n   */\n  public getMetrics(_document: MathDocument<N, T, D>) {\n  }\n\n  /**\n   * @override\n   */\n  public styleSheet(_document: MathDocument<N, T, D>) {\n    return null as N;\n  }\n\n  /**\n   * @override\n   */\n  public pageElements(_document: MathDocument<N, T, D>) {\n    return null as N;\n  }\n\n  /**\n   * Execute a set of filters, passing them the MathItem and any needed data,\n   *  and return the (possibly modified) data\n   *\n   * @param {FunctionList} filters   The list of functions to be performed\n   * @param {MathItem} math          The math item that is being processed\n   * @param {MathDocument} document  The math document contaiing the math item\n   * @param {any} data               Whatever other data is needed\n   * @return {any}                   The (possibly modified) data\n   */\n  protected executeFilters(\n    filters: FunctionList, math: MathItem<N, T, D>,\n    document: MathDocument<N, T, D>, data: any\n  ): any {\n    let args = {math, document, data};\n    filters.execute(args);\n    return args.data;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMath node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlLayoutNode, AttributeList} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMath node class (subclass of AbstractMmlLayoutNode)\n */\n\nexport class MmlMath extends AbstractMmlLayoutNode {\n\n  /**\n   *  These are used as the defaults for any attributes marked INHERIT in other classes\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlLayoutNode.defaults,\n    mathvariant: 'normal',\n    mathsize: 'normal',\n    mathcolor: '', // Should be 'black', but allow it to inherit from surrounding text\n    mathbackground: 'transparent',\n    dir: 'ltr',\n    scriptlevel: 0,\n    displaystyle: false,\n    display: 'inline',\n    maxwidth: '',\n    overflow: 'linebreak',\n    altimg: '',\n    'altimg-width': '',\n    'altimg-height': '',\n    'altimg-valign': '',\n    alttext: '',\n    cdgroup: '',\n    scriptsizemultiplier: 1 / Math.sqrt(2),\n    scriptminsize: '8px',        // Should be 8pt, but that's too big\n    infixlinebreakstyle: 'before',\n    lineleading: '1ex',\n    linebreakmultchar: '\\u2062', // Invisible times\n    indentshift: 'auto',         // Use user configuration\n    indentalign: 'auto',\n    indenttarget: '',\n    indentalignfirst: 'indentalign',\n    indentshiftfirst: 'indentshift',\n    indentalignlast:  'indentalign',\n    indentshiftlast:  'indentshift'\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'math';\n  }\n\n  /**\n   * Linebreaking can occur in math nodes\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * The attributes of math nodes are inherited, so add them into the list.\n   * The displaystyle attribute comes from the display attribute if not given explicitly\n   * The scriptlevel comes from the scriptlevel attribute or default\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    if (this.attributes.get('mode') === 'display') {\n      this.attributes.setInherited('display', 'block');\n    }\n    attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n    display = (!!this.attributes.get('displaystyle') ||\n               (!this.attributes.get('displaystyle') && this.attributes.get('display') === 'block'));\n    this.attributes.setInherited('displaystyle', display);\n    level = (this.attributes.get('scriptlevel') ||\n             (this.constructor as typeof MmlMath).defaults['scriptlevel']) as number;\n    super.setChildInheritedAttributes(attributes, display, level, prime);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMi node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, AbstractMmlNode, AttributeList, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMi node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMi extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults\n  };\n\n  /**\n   * Pattern for operator names\n   */\n  public static operatorName: RegExp = /^[a-z][a-z0-9]*$/i;\n  /**\n   * Pattern for single-character texts\n   */\n  public static singleCharacter: RegExp =\n    /^[\\uD800-\\uDBFF]?.[\\u0300-\\u036F\\u1AB0-\\u1ABE\\u1DC0-\\u1DFF\\u20D0-\\u20EF]*$/;\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mi';\n  }\n\n  /**\n   * Do the usual inheritance, then check the text length to see\n   *   if mathvariant should be normal or italic.\n   *\n   * @override\n   */\n  public setInheritedAttributes(attributes: AttributeList = {},\n                                display: boolean = false, level: number = 0, prime: boolean = false) {\n    super.setInheritedAttributes(attributes, display, level, prime);\n    let text = this.getText();\n    if (text.match(MmlMi.singleCharacter) && !attributes.mathvariant) {\n      this.attributes.setInherited('mathvariant', 'italic');\n    }\n  }\n\n  /**\n   * Mark multi-character texts as OP rather than ORD for spacing purposes\n   *\n   * @override\n   */\n  public setTeXclass(prev: AbstractMmlNode) {\n    this.getPrevClass(prev);\n    let name = this.getText();\n    if (name.length > 1 && name.match(MmlMi.operatorName) &&\n        this.attributes.get('mathvariant') === 'normal' &&\n        this.getProperty('autoOP') === undefined &&\n        this.getProperty('texClass') === undefined) {\n      this.texClass = TEXCLASS.OP;\n      this.setProperty('autoOP', true);\n    }\n    return this;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMn node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMn node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMn extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mn';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMtext node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMtext node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMtext extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mtext';\n  }\n\n  /**\n   * <mtext> is always space-like according to the spec\n   * @override\n   */\n  public get isSpacelike() {\n    return true;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMspace node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlTokenNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMspace node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMspace extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults,\n    width:  '0em',\n    height: '0ex',\n    depth:  '0ex',\n    linebreak: 'auto'\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.NONE;\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode): MmlNode {\n    return prev;\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mspace';\n  }\n\n  /**\n   * mspace can't have children\n   * @override\n   */\n  public get arity() {\n    return 0;\n  }\n\n  /**\n   * @override\n   */\n  public get isSpacelike() {\n    return true;\n  }\n\n  /**\n   * Only process linebreak if the space has no explicit dimensions (according to spec)\n   *\n   * @override\n   */\n  public get hasNewline() {\n    let attributes = this.attributes;\n    return (attributes.getExplicit('width') == null && attributes.getExplicit('height') == null &&\n            attributes.getExplicit('depth') == null && attributes.get('linebreak') === 'newline');\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMs node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMs node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMs extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults,\n    lquote: '\"',\n    rquote: '\"'\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'ms';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMrow node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMrow node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMrow extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * The index of the core child, when acting as an embellish mrow\n   */\n  protected _core: number = null;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mrow';\n  }\n\n  /**\n   * An mrow is space-like if all its children are.\n   *\n   * @override\n   */\n  public get isSpacelike() {\n    for (const child of this.childNodes) {\n      if (!child.isSpacelike) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * An mrow is embellished if it contains one embellished operator\n   * and any number of space-like nodes\n   *\n   * @override\n   */\n  public get isEmbellished() {\n    let embellished = false;\n    let i = 0;\n    for (const child of this.childNodes) {\n      if (child) {\n        if (child.isEmbellished) {\n          if (embellished) {\n            return false;\n          }\n          embellished = true;\n          this._core = i;\n        } else if (!child.isSpacelike) {\n          return false;\n        }\n      }\n      i++;\n    }\n    return embellished;\n  }\n\n  /**\n   * @override\n   */\n  public core(): MmlNode {\n    if (!this.isEmbellished || this._core == null) {\n      return this;\n    }\n    return this.childNodes[this._core];\n  }\n\n  /**\n   * @override\n   */\n  public coreMO(): MmlNode {\n    if (!this.isEmbellished || this._core == null) {\n      return this;\n    }\n    return this.childNodes[this._core].coreMO();\n  }\n\n  /**\n   * @return {number}  The number of non-spacelike child nodes\n   */\n  public nonSpaceLength(): number {\n    let n = 0;\n    for (const child of this.childNodes) {\n      if (child && !child.isSpacelike) {\n        n++;\n      }\n    }\n    return n;\n  }\n\n  /**\n   * @return {MmlNode|null}  The first non-space-like child node\n   */\n  public firstNonSpace(): MmlNode | null {\n    for (const child of this.childNodes) {\n      if (child && !child.isSpacelike) {\n        return child;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * @return {MmlNode|null}  The last non-space-like child node\n   */\n  public lastNonSpace(): MmlNode | null {\n    let i = this.childNodes.length;\n    while (--i >= 0) {\n      let child = this.childNodes[i];\n      if (child && !child.isSpacelike) {\n        return child;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    if (this.getProperty('open') != null || this.getProperty('close') != null) {\n      //\n      // <mrow> looks like it came from \\left...\\right\n      //   so treat as subexpression (TeX class INNER).\n      // Use prev = null for the initial element in the\n      //   delimiters, since there is nothing previous to\n      //   it in what would be the TeX math list.\n      //\n      this.getPrevClass(prev);\n      prev = null;\n      for (const child of this.childNodes) {\n        prev = child.setTeXclass(prev);\n      }\n      if (this.texClass == null) {\n        this.texClass = TEXCLASS.INNER;\n      }\n    } else {\n      //\n      //  Normal <mrow>, so treat as though mrow is not there\n      //\n      for (const child of this.childNodes) {\n        prev = child.setTeXclass(prev);\n      }\n      if (this.childNodes[0]) {\n        this.updateTeXclass(this.childNodes[0]);\n      }\n    }\n    return prev;\n  }\n\n}\n\n\n/*****************************************************************/\n/**\n *  Implements the MmlInferredMrow node class (subclass of MmlMrow)\n */\n\nexport class MmlInferredMrow extends MmlMrow {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = MmlMrow.defaults;\n\n  /**\n   * @return {string}  The inferred-mrow kind\n   */\n  public get kind(): string {\n    return 'inferredMrow';\n  }\n\n  /**\n   * @return {boolean}  This is inferred\n   */\n  public get isInferred(): boolean {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public get notParent() {\n    return true;\n  }\n\n  /**\n   * Show the child nodes in brackets\n   */\n  public toString() {\n    return '[' + this.childNodes.join(',') + ']';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMfrac node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlBaseNode, AttributeList} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMfrac node class (subclass of AbstractMmlBaseNode)\n */\n\nexport class MmlMfrac extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults,\n    linethickness: 'medium',\n    numalign: 'center',\n    denomalign: 'center',\n    bevelled: false\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mfrac';\n  }\n\n  /**\n   * <mfrac> requires two children\n   * @override\n   */\n  public get arity() {\n    return 2;\n  }\n\n  /**\n   * The children of <mfrac> can include line breaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * Update the children separately\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    for (const child of this.childNodes) {\n      child.setTeXclass(null);\n    }\n    return this;\n  }\n\n  /**\n   * Adjust the display level, and use prime style in denominator\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    if (!display || level > 0) {\n      level++;\n    }\n    this.childNodes[0].setInheritedAttributes(attributes, false, level, prime);\n    this.childNodes[1].setInheritedAttributes(attributes, false, level, true);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMsqrt node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, AttributeList, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMsqrt node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMsqrt extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'msqrt';\n  }\n\n  /**\n   * <msqrt> has an inferred mrow\n   * @override\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * <msqrt> can contain line breaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    this.childNodes[0].setTeXclass(null);\n    return this;\n  }\n\n  /**\n   * The contents of sqrt are in TeX prime style.\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, _prime: boolean) {\n    this.childNodes[0].setInheritedAttributes(attributes, display, level, true);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMroot node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, AttributeList, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMroot node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMroot extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mroot';\n  }\n\n  /**\n   * <mroot> requires two children\n   * @override\n   */\n  public get arity() {\n    return 2;\n  }\n\n  /**\n   * Set the TeX class for the content of the root and the root separately.\n   * Return ourself as the previous item.\n   *\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    this.childNodes[0].setTeXclass(null);\n    this.childNodes[1].setTeXclass(null);\n    return this;\n  }\n\n  /**\n   * Set the children display/level/prime for the base and root.\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    this.childNodes[0].setInheritedAttributes(attributes, display, level, true);\n    this.childNodes[1].setInheritedAttributes(attributes, false, level + 2, prime);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMerror node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMerror node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMerror extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'merror';\n  }\n\n  /**\n   * <merror> gets an inferred mrow\n   * @override\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * <merror> can contain line breaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMpadded node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlLayoutNode} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMpadded node class (subclass of AbstractMmlLayoutNode)\n */\n\nexport class MmlMpadded extends AbstractMmlLayoutNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlLayoutNode.defaults,\n    width: '',\n    height: '',\n    depth: '',\n    lspace: 0,\n    voffset: 0\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mpadded';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMphantom node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlLayoutNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMphantom node class (subclass of AbstractMmlLayoutNode)\n */\n\nexport class MmlMphantom extends AbstractMmlLayoutNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlLayoutNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mphantom';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMfenced node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, TextNode, AbstractMmlNode, AttributeList, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMfenced node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMfenced extends AbstractMmlNode {\n\n  /**\n   * @overeride\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    open: '(',\n    close: ')',\n    separators: ','\n  };\n\n  /**\n   * TeX class is INNER\n   */\n  protected texclass = TEXCLASS.INNER;\n\n  /**\n   * Storage for \"fake\" nodes for the separators\n   */\n  public separators: MmlNode[] = [];\n  /**\n   * Storage for \"fake\" open node\n   */\n  public open: MmlNode = null;\n  /**\n   * Storage for \"fake\" close node\n   */\n  public close: MmlNode = null;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mfenced';\n  }\n\n  /**\n   * Include the fake nodes in the process, since they will be used\n   *  to produce the output.\n   *\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    if (this.open) {\n      prev = this.open.setTeXclass(prev);\n    }\n    if (this.childNodes[0]) {\n      prev = this.childNodes[0].setTeXclass(prev);\n    }\n    for (let i = 1, m = this.childNodes.length; i < m; i++) {\n      if (this.separators[i - 1]) {\n        prev = this.separators[i - 1].setTeXclass(prev);\n      }\n      if (this.childNodes[i]) {\n        prev = this.childNodes[i].setTeXclass(prev);\n      }\n    }\n    if (this.close) {\n      prev = this.close.setTeXclass(prev);\n    }\n    this.updateTeXclass(this.open);\n    return prev;\n  }\n\n  /**\n   * Create the fake nodes and do their inheritance\n   * Then do inheridence of usual children\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    this.addFakeNodes();\n    for (const child of [this.open, this.close].concat(this.separators)) {\n      if (child) {\n        child.setInheritedAttributes(attributes, display, level, prime);\n      }\n    }\n    super.setChildInheritedAttributes(attributes, display, level, prime);\n  }\n\n  /**\n   * Create <mo> elements for the open and close delimiters, and for the separators (if any)\n   */\n  protected addFakeNodes() {\n    let {open, close, separators} = this.attributes.getList('open', 'close', 'separators') as\n    {open: string, close: string, separators: string};\n    open = open.replace(/[ \\t\\n\\r]/g, '');\n    close = close.replace(/[ \\t\\n\\r]/g, '');\n    separators = separators.replace(/[ \\t\\n\\r]/g, '');\n    //\n    // Create open node\n    //\n    if (open) {\n      this.open = this.fakeNode(open, {fence: true, form: 'prefix'}, TEXCLASS.OPEN);\n    }\n    //\n    // Create nodes for the separators\n    //\n    if (separators) {\n      while (separators.length < this.childNodes.length - 1) {\n        separators += separators.charAt(separators.length - 1);\n      }\n      let i = 0;\n      for (const child of this.childNodes.slice(1)) {\n        if (child) {\n          this.separators.push(this.fakeNode(separators.charAt(i++)));\n        }\n      }\n    }\n    //\n    //  Create close node\n    //\n    if (close) {\n      this.close = this.fakeNode(close, {fence: true, form: 'postfix'}, TEXCLASS.CLOSE);\n    }\n  }\n\n  /**\n   * @param {string} c                 The character for the text of the node\n   * @param {PropertyList} properties  The attributes for the node\n   * @param {number} texClass          The TeX class for the node\n   * @return {MmlNode}                 The generated <mo> node\n   */\n  protected fakeNode(c: string, properties: PropertyList = {}, texClass: number = null): MmlNode {\n    let text = (this.factory.create('text') as TextNode).setText(c);\n    let node = this.factory.create('mo', properties, [text]);\n    node.texClass = texClass;\n    node.parent = this;\n    return node;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMenclose node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlEnclose node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMenclose extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    notation: 'longdiv'\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * The menclose kind\n   * @override\n   */\n  public get kind() {\n    return 'menclose';\n  }\n\n  /**\n   * <menclose> has an inferred mrow\n   * @override\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * <menclose> is a linebreak container\n   * @override\n   */\n  public get linebreakContininer() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    prev = this.childNodes[0].setTeXclass(prev);\n    this.updateTeXclass(this.childNodes[0]);\n    return prev;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMaction node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMaction node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMaction extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    actiontype: 'toggle',\n    selection: 1\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'maction';\n  }\n\n  /**\n   * At least one child\n   * @override\n   */\n  public get arity() {\n    return 1;\n  }\n\n  /**\n   * @return {MmlNode}  The selected child node (or an mrow if none selected)\n   */\n  public get selected(): MmlNode {\n    const selection = this.attributes.get('selection') as number;\n    const i = Math.max(1, Math.min(this.childNodes.length, selection)) - 1;\n    return this.childNodes[i] || this.factory.create('mrow');\n  }\n\n  /**\n   * @override\n   */\n  public get isEmbellished() {\n    return this.selected.isEmbellished;\n  }\n\n  /**\n   * @override\n   */\n  public get isSpacelike() {\n    return this.selected.isSpacelike;\n  }\n\n  /**\n   * @override\n   */\n  public core(): MmlNode {\n    return this.selected.core();\n  }\n\n  /**\n   * @override\n   */\n  public coreMO(): MmlNode {\n    return this.selected.coreMO();\n  }\n\n  /**\n   * @override\n   */\n  protected verifyAttributes(options: PropertyList) {\n    super.verifyAttributes(options);\n    if (this.attributes.get('actiontype') !== 'toggle' &&\n        this.attributes.getExplicit('selection') !== undefined) {\n      const attributes = this.attributes.getAllAttributes();\n      delete attributes.selection;\n    }\n  }\n\n  /**\n   * Get the TeX class from the selceted node\n   * For tooltips, set TeX classes within the tip as a separate math list\n   *\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    if (this.attributes.get('actiontype') === 'tooltip' && this.childNodes[1]) {\n      this.childNodes[1].setTeXclass(null);\n    }\n    let selected = this.selected;\n    prev = selected.setTeXclass(prev);\n    this.updateTeXclass(selected);\n    return prev;\n  }\n\n  /**\n   * Select the next child for a toggle action\n   */\n  public nextToggleSelection() {\n    let selection = Math.max(1, (this.attributes.get('selection') as number) + 1);\n    if (selection > this.childNodes.length) {\n      selection = 1;\n    }\n    this.attributes.set('selection', selection);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMsubsup, MmlMsub, and MmlMsup nodes\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlBaseNode, AttributeList} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMsubsup node class (subclass of AbstractMmlBaseNode)\n */\n\nexport class MmlMsubsup extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults,\n    subscriptshift: '',\n    superscriptshift: ''\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'msubsup';\n  }\n\n  /**\n   * <msubsup> requires three children\n   * @override\n   */\n  public get arity() {\n    return 3;\n  }\n\n  /**\n   * @return {number}  The position of the base element\n   */\n  public get base(): number {\n    return 0;\n  }\n\n  /**\n   * @return {number}  The position of the subscript (overridden in msup below)\n   */\n  public get sub(): number {\n    return 1;\n  }\n\n  /**\n   * @return {number}  The position of the superscript (overridden in msup below)\n   */\n  public get sup(): number {\n    return 2;\n  }\n\n  /**\n   * Super- and subscripts are not in displaymode, have scriptlevel increased, and prime style in subscripts.\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    let nodes = this.childNodes;\n    nodes[0].setInheritedAttributes(attributes, display, level, prime);\n    nodes[1].setInheritedAttributes(attributes, false, level + 1, prime || this.sub === 1);\n    if (!nodes[2]) {\n      return;\n    }\n    nodes[2].setInheritedAttributes(attributes, false, level + 1, prime || this.sub === 2);\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMsub node class (subclass of MmlMsubsup)\n */\n\nexport class MmlMsub extends MmlMsubsup {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...MmlMsubsup.defaults\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'msub';\n  }\n\n  /**\n   * <msub> only gets two children\n   * @override\n   */\n  public get arity() {\n    return 2;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMsup node class (subclass of MmlMsubsup)\n */\n\nexport class MmlMsup extends MmlMsubsup {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...MmlMsubsup.defaults\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'msup';\n  }\n\n  /**\n   * <msup> only gets two children\n   * @override\n   */\n  get arity() {\n    return 2;\n  }\n\n  /**\n   * child 1 is superscript\n   * @override\n   */\n  get sup() {\n    return 1;\n  }\n\n  /**\n   * child 2 is null (no subscript)\n   * @override\n   */\n  get sub() {\n    return 2;\n  }\n\n}\n\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMunderover node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlBaseNode, AttributeList} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMunderover node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMunderover extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults,\n    accent: false,\n    accentunder: false,\n    align: 'center'\n  };\n\n  /**\n   * The names of attributes controling accents for each child node (reversed for mover below)\n   */\n  protected static ACCENTS = ['', 'accentunder', 'accent'];\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'munderover';\n  }\n\n  /**\n   * <munderover> requires three children\n   * @override\n   */\n  public get arity() {\n    return 3;\n  }\n\n  /**\n   * @return {number}  The base is child 0\n   */\n  public get base(): number {\n    return 0;\n  }\n\n  /**\n   * @return {number}  Child 1 goes under (overridden by mover below)\n   */\n  public get under(): number {\n    return 1;\n  }\n\n  /**\n   * @return {number}  Child 2 goes over (overridden by mover below)\n   */\n  public get over(): number {\n    return 2;\n  }\n\n  /**\n   * <munderover> can contain line breaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * Base is in prime style if there is an over node\n   * Force scriptlevel change if converted to sub-sup by movablelimits on the base in non-display mode\n   * Adjust displaystyle, scriptlevel, and primestyle for the under/over nodes and check if accent\n   *   values have changed due to the inheritance (e.g., settings in operator dictionary)\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    let nodes = this.childNodes;\n    nodes[0].setInheritedAttributes(attributes, display, level, prime || !!nodes[this.over]);\n    let force = !!(!display && nodes[0].coreMO().attributes.get('movablelimits'));\n    let ACCENTS = (this.constructor as typeof MmlMunderover).ACCENTS;\n    nodes[1].setInheritedAttributes(attributes, false,\n                                    this.getScriptlevel(ACCENTS[1], force, level),\n                                    prime || this.under === 1);\n    this.setInheritedAccent(1, ACCENTS[1], display, level, prime, force);\n    if (!nodes[2]) {\n      return;\n    }\n    nodes[2].setInheritedAttributes(attributes, false,\n                                    this.getScriptlevel(ACCENTS[2], force, level),\n                                    prime || this.under === 2);\n    this.setInheritedAccent(2, ACCENTS[2], display, level, prime, force);\n  }\n\n  /**\n   * @param {string} accent  The name of the accent attribute to check (\"accent\" or \"accentunder\")\n   * @param {boolean} force  True if the scriptlevel change is to be forced to occur\n   * @param {number} level   The current scriptlevel\n   * @return {number}        The new script level based on the accent attribute\n   */\n  protected getScriptlevel(accent: string, force: boolean, level: number): number {\n    if (force || !this.attributes.get(accent)) {\n      level++;\n    }\n    return level;\n  }\n\n  /**\n   * Check if an under or over accent should cause the appropriate accent attribute to be inherited\n   *   on the munderover node, and if it is not the default, re-inherit the scriptlevel, since that\n   *   is affected by the accent attribute\n   *\n   * @param {number} n         The index of the node to check\n   * @param {string} accent    The name of the accent attribute to check (\"accent\" or \"accentunder\")\n   * @param {boolean} display  The displaystyle\n   * @param {number} level     The scriptlevel\n   * @param {number} prime     The TeX prime style\n   * @param {boolean} force    Whether to force the scriptlevel change\n   */\n  protected setInheritedAccent(n: number, accent: string, display: boolean, level: number,\n                               prime: boolean, force: boolean) {\n    let node = this.childNodes[n];\n    if (this.attributes.getExplicit(accent) == null && node.isEmbellished) {\n      let value = node.coreMO().attributes.get('accent');\n      this.attributes.setInherited(accent, value);\n      if (value !== this.attributes.getDefault(accent)) {\n        node.setInheritedAttributes({}, display, this.getScriptlevel(accent, force, level), prime);\n      }\n    }\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMunder node class (subclass of MmlMunderover)\n */\n\nexport class MmlMunder extends MmlMunderover {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n      ...MmlMunderover.defaults\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'munder';\n  }\n\n  /**\n   * <munder> has only two children\n   * @override\n   */\n  public get arity() {\n    return 2;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMover node class (subclass of MmlMunderover)\n */\n\nexport class MmlMover extends MmlMunderover {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n      ...MmlMunderover.defaults\n  };\n  /**\n   *  The first child is the over accent (second never occurs)\n   */\n  protected static ACCENTS = ['', 'accent', 'accentunder'];\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mover';\n  }\n\n  /**\n   * <mover> has only two children\n   * @override\n   */\n  get arity() {\n    return 2;\n  }\n\n  /**\n   * Child 1 is the over node\n   * @override\n   */\n  public get over() {\n    return 1;\n  }\n\n  /**\n   * Child 2 is the null (the under node)\n   * @override\n   */\n  public get under() {\n    return 2;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMmultiscripts node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlNode, AttributeList} from '../MmlNode.js';\nimport {MmlMsubsup} from './msubsup.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMmultiscripts node class (subclass of MmlMsubsup)\n */\n\nexport class MmlMmultiscripts extends MmlMsubsup {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...MmlMsubsup.defaults\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mmultiscripts';\n  }\n\n  /**\n   * <mmultiscripts> requires at least one child (the base)\n   * @override\n   */\n  public get arity() {\n    return 1;\n  }\n\n  /**\n   * Push the inherited values to the base\n   * Make sure the number of pre- and post-scripts are even by adding mrows, if needed.\n   * For the scripts, use displaystyle = false, scriptlevel + 1, and\n   *   set the primestyle in the subscripts.\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    this.childNodes[0].setInheritedAttributes(attributes, display, level, prime);\n    let prescripts = false;\n    for (let i = 1, n = 0; i < this.childNodes.length; i++) {\n      let child = this.childNodes[i];\n      if (child.isKind('mprescripts')) {\n        if (!prescripts) {\n          prescripts = true;\n          if (i % 2 === 0) {\n            let mrow = this.factory.create('mrow');\n            this.childNodes.splice(i, 0, mrow);\n            mrow.parent = this;\n            i++;\n          }\n        }\n      } else {\n        let primestyle = prime || (n % 2 === 0);\n        child.setInheritedAttributes(attributes, false, level + 1, primestyle);\n        n++;\n      }\n    }\n    if (this.childNodes.length % 2 === (prescripts ? 1 : 0)) {\n      this.appendChild(this.factory.create('mrow'));\n      this.childNodes[this.childNodes.length - 1].setInheritedAttributes(attributes, false, level + 1, prime);\n    }\n  }\n\n  /**\n   * Check that mprescripts only occurs once, and that the number of pre- and post-scripts are even.\n   *\n   * @override\n   */\n  protected verifyChildren(options: PropertyList) {\n    let prescripts = false;\n    let fix = options['fixMmultiscripts'];\n    for (let i = 0; i < this.childNodes.length; i++) {\n      let child = this.childNodes[i];\n      if (child.isKind('mprescripts')) {\n        if (prescripts) {\n          child.mError(child.kind + ' can only appear once in ' + this.kind, options, true);\n        } else {\n          prescripts = true;\n          if (i % 2 === 0 && !fix) {\n            this.mError('There must be an equal number of prescripts of each type', options);\n          }\n        }\n      }\n    }\n    if (this.childNodes.length % 2 === (prescripts ? 1 : 0) && !fix) {\n      this.mError('There must be an equal number of scripts of each type', options);\n    }\n    super.verifyChildren(options);\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMprescripts node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMprescripts extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * @return {string}  The mprescripts kind\n   */\n  public get kind(): string {\n    return 'mprescripts';\n  }\n\n  /**\n   * @return {number}  <mprescripts> can have no children\n   */\n  public get arity(): number {\n    return 0;\n  }\n\n  /**\n   * Check that parent is mmultiscripts\n   *\n   * @override\n   */\n  public verifyTree(options: PropertyList) {\n    super.verifyTree(options);\n    if (this.parent && !this.parent.isKind('mmultiscripts')) {\n      this.mError(this.kind + ' must be a child of mmultiscripts', options, true);\n    }\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlNone node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlNone extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults\n  };\n\n  /**\n   * @return {string}  The none kind\n   */\n  public get kind(): string {\n    return 'none';\n  }\n\n  /**\n   * @return {number}  <none> can have no children\n   */\n  public get arity(): number {\n    return 0;\n  }\n\n  /**\n   * Check that parent is mmultiscripts\n   *\n   * @override\n   */\n  public verifyTree(options: PropertyList) {\n    super.verifyTree(options);\n    if (this.parent && !this.parent.isKind('mmultiscripts')) {\n      this.mError(this.kind + ' must be a child of mmultiscripts', options, true);\n    }\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMtable node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, AttributeList, TEXCLASS, indentAttributes} from '../MmlNode.js';\nimport {split} from '../../../util/string.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMtable node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMtable extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    align: 'axis',\n    rowalign: 'baseline',\n    columnalign: 'center',\n    groupalign: '{left}',\n    alignmentscope: true,\n    columnwidth: 'auto',\n    width: 'auto',\n    rowspacing: '1ex',\n    columnspacing: '.8em',\n    rowlines: 'none',\n    columnlines: 'none',\n    frame: 'none',\n    framespacing: '0.4em 0.5ex',\n    equalrows: false,\n    equalcolumns: false,\n    displaystyle: false,\n    side: 'right',\n    minlabelspacing: '0.8em'\n  };\n\n  /**\n   * Extra properties for this node\n   */\n  public properties = {\n    useHeight: true\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mtable';\n  }\n\n  /**\n   * Linebreaks are allowed in tables\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public setInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    //\n    // Force inheritance of shift and align values (since they are needed to output tables with labels)\n    //   but make sure they are not given explicitly on the <mtable> tag.\n    //\n    for (const name of indentAttributes) {\n      if (attributes[name]) {\n        this.attributes.setInherited(name, attributes[name][1]);\n      }\n      if (this.attributes.getExplicit(name) !== undefined) {\n        delete (this.attributes.getAllAttributes())[name];\n      }\n    }\n    super.setInheritedAttributes(attributes, display, level, prime);\n  }\n\n  /**\n   * Make sure all children are mtr or mlabeledtr nodes\n   * Inherit the table attributes, and set the display attribute based on the table's displaystyle attribute\n   * Reset the prime value to false\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, _prime: boolean) {\n    for (const child of this.childNodes) {\n      if (!child.isKind('mtr')) {\n        this.replaceChild(this.factory.create('mtr'), child)\n          .appendChild(child);\n      }\n    }\n    level = this.getProperty('scriptlevel') as number || level;\n    display = !!(this.attributes.getExplicit('displaystyle') || this.attributes.getDefault('displaystyle'));\n    attributes = this.addInheritedAttributes(attributes, {\n      columnalign: this.attributes.get('columnalign'),\n      rowalign: 'center'\n    });\n    const cramped = this.attributes.getExplicit('data-cramped') as boolean;\n    const ralign = split(this.attributes.get('rowalign') as string);\n    for (const child of this.childNodes) {\n      attributes.rowalign[1] = ralign.shift() || attributes.rowalign[1];\n      child.setInheritedAttributes(attributes, display, level, !!cramped);\n    }\n  }\n\n  /**\n   * Check that children are mtr or mlabeledtr\n   *\n   * @override\n   */\n  protected verifyChildren(options: PropertyList) {\n    let mtr: MmlNode = null;      // all consecutive non-mtr elements are collected into one mtr\n    const factory = this.factory;\n    for (let i = 0; i < this.childNodes.length; i++) {\n      const child = this.childNodes[i];\n      if (child.isKind('mtr')) {\n        mtr = null;               // start a new row if there are non-mtr children\n      } else {\n        const isMtd = child.isKind('mtd');\n        //\n        //  If there is already an mtr for previous children, just remove the child\n        //    otherwise replace the child with a new mtr\n        //\n        if (mtr) {\n          this.removeChild(child);\n          i--;   // there is one fewer child now\n        } else {\n          mtr = this.replaceChild(factory.create('mtr'), child) as MmlNode;\n        }\n        mtr.appendChild(isMtd ? child : factory.create('mtd', {}, [child]));  // Move the child into the mtr\n        if (!options['fixMtables']) {\n          child.parent.removeChild(child);  // remove the child from its mtd or mtr\n          child.parent = this;              // ... and make it think it is a child of the table again\n          isMtd && mtr.appendChild(factory.create('mtd'));  // child will be replaced, so make sure there is an mtd\n          const merror = child.mError('Children of ' + this.kind + ' must be mtr or mlabeledtr', options, isMtd);\n          mtr.childNodes[mtr.childNodes.length - 1].appendChild(merror);   // append the error to the mtd in the mtr\n        }\n      }\n    }\n    super.verifyChildren(options);\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    for (const child of this.childNodes) {\n      child.setTeXclass(null);\n    }\n    return this;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMtr and MmlMlabeledtr nodes\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {MmlNode, AbstractMmlNode, AttributeList} from '../MmlNode.js';\nimport {INHERIT} from '../Attributes.js';\nimport {split} from '../../../util/string.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMtr node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMtr extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    rowalign: INHERIT,\n    columnalign: INHERIT,\n    groupalign: INHERIT\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mtr';\n  }\n\n  /**\n   * <mtr> can contain linebreaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * Inherit the mtr attributes\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    for (const child of this.childNodes) {\n      if (!child.isKind('mtd')) {\n        this.replaceChild(this.factory.create('mtd'), child)\n            .appendChild(child);\n      }\n    }\n    const calign = split(this.attributes.get('columnalign') as string);\n    if (this.arity === 1) {\n      calign.unshift(this.parent.attributes.get('side') as string);\n    }\n    attributes = this.addInheritedAttributes(attributes, {\n      rowalign: this.attributes.get('rowalign'),\n      columnalign: 'center'\n    });\n    for (const child of this.childNodes) {\n      attributes.columnalign[1] = calign.shift() || attributes.columnalign[1];\n      child.setInheritedAttributes(attributes, display, level, prime);\n    }\n  }\n\n  /**\n   * Check that parent is mtable and children are mtd\n   *\n   * @override\n   */\n  protected verifyChildren(options: PropertyList) {\n    if (this.parent && !this.parent.isKind('mtable')) {\n      this.mError(this.kind + ' can only be a child of an mtable', options, true);\n      return;\n    }\n    for (const child of this.childNodes) {\n      if (!child.isKind('mtd')) {\n        let mtd = this.replaceChild(this.factory.create('mtd'), child) as MmlNode;\n        mtd.appendChild(child);\n        if (!options['fixMtables']) {\n          child.mError('Children of ' + this.kind + ' must be mtd', options);\n        }\n      }\n    }\n    super.verifyChildren(options);\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    for (const child of this.childNodes) {\n      child.setTeXclass(null);\n    }\n    return this;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMlabeledtr node class (subclass of MmlMtr)\n */\n\nexport class MmlMlabeledtr extends MmlMtr {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mlabeledtr';\n  }\n\n  /**\n   * <mlabeledtr> requires at least one child (the label)\n   * @override\n   */\n  get arity() {\n    return 1;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMtd node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlBaseNode, MmlNode} from '../MmlNode.js';\nimport {INHERIT} from '../Attributes.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMtd node class (subclass of AbstractMmlBaseNode)\n */\n\nexport class MmlMtd extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults,\n    rowspan: 1,\n    columnspan: 1,\n    rowalign: INHERIT,\n    columnalign: INHERIT,\n    groupalign: INHERIT\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mtd';\n  }\n\n  /**\n   * <mtd> has an inferred mrow\n   * @overrride\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * <mtd> can contain line breaks\n   * @override\n   */\n  public get linebreakContainer() {\n    return true;\n  }\n\n  /**\n   * Check that parent is mtr\n   *\n   * @override\n   */\n  protected verifyChildren(options: PropertyList) {\n    if (this.parent && !this.parent.isKind('mtr')) {\n      this.mError(this.kind + ' can only be a child of an mtr or mlabeledtr', options, true);\n      return;\n    }\n    super.verifyChildren(options);\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    this.childNodes[0].setTeXclass(null);\n    return this;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMglyph node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, TEXCLASS} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMglyph node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMglyph extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults,\n    alt: '',\n    src: '',\n    index: '',\n    width: 'auto',\n    height: 'auto',\n    valign: '0em'\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mglyph';\n  }\n\n  /**\n   * @override\n   */\n  public verifyAttributes(options: PropertyList) {\n    const {src, fontfamily, index} = this.attributes.getList('src', 'fontfamily', 'index');\n    if (src === '' && (fontfamily === '' || index === '')) {\n      this.mError('mglyph must have either src or fontfamily and index attributes', options, true);\n    } else {\n      super.verifyAttributes(options);\n    }\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlSemantics, MmlAnnotation, and MmlAnnotationXML nodes\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlNode, AbstractMmlBaseNode} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMroot node class (subclass of AbstractMmlBaseNode)\n */\n\nexport class MmlSemantics extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults,\n    definitionUrl: null,\n    encoding: null\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'semantics';\n  }\n\n  /**\n   * <semantics> requires at least one node\n   * @override\n   */\n  public get arity() {\n    return 1;\n  }\n\n  /**\n   * Ignore <semantics> when looking for partent node\n   * @override\n   */\n  public get notParent() {\n    return true;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMroot node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlAnnotationXML extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    definitionUrl: null,\n    encoding: null,\n    cd: 'mathmlkeys',\n    name: '',\n    src: null\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'annotation-xml';\n  }\n\n  /**\n   * Children are XMLNodes, so don't bother inheritting to them\n   * @override\n   */\n  protected setChildInheritedAttributes() {}\n\n}\n\n/*****************************************************************/\n/**\n *  Implements the MmlMroot node class (subclass of MmlAnnotationXML)\n */\n\nexport class MmlAnnotation extends MmlAnnotationXML {\n\n  /**\n   * @override\n   */\n  public static defaults = {\n    ...MmlAnnotationXML.defaults\n  };\n\n  /**\n   * Extra properties for this node\n   */\n  public properties = {\n    isChars: true\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'annotation';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the TeXAtom node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {MmlFactory} from '../MmlFactory.js';\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlBaseNode, MmlNode, TEXCLASS} from '../MmlNode.js';\nimport {MmlMo} from './mo.js';\n\n/*****************************************************************/\n/**\n *  Implements the TeXAtom node class (subclass of AbstractMmlBaseNode)\n */\n\nexport class TeXAtom extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults\n  };\n\n  /**\n   * TeX class is ORD\n   */\n  protected texclass = TEXCLASS.ORD;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'TeXAtom';\n  }\n\n  /**\n   * Inferred mrow with any number of children\n   * @override\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * This element is not considered a MathML container\n   * @override\n   */\n  public get notParent() {\n    return this.childNodes[0] && this.childNodes[0].childNodes.length === 1;\n  }\n\n  /**\n   * @override\n   */\n  constructor(factory: MmlFactory, attributes: PropertyList, children: MmlNode[]) {\n    super(factory, attributes, children);\n    this.setProperty('texClass', this.texClass);   // needed for serialization to include the texClass\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.childNodes[0].setTeXclass(null);\n    return this.adjustTeXclass(prev);\n  }\n\n  /**\n   * (Replaced below by the version from the MmlMo node)\n   *\n   * @override\n   */\n  public adjustTeXclass(prev: MmlNode) {\n    return prev;\n  }\n\n}\n/**\n *  Use the method from the MmlMo class\n */\nTeXAtom.prototype.adjustTeXclass = MmlMo.prototype.adjustTeXclass;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAuBA,QAAA,eAAA;AAIA,QAAA,oBAAA;AAiGA,QAAA,oBAAA,WAAA;AA8BE,eAAAA,mBAAY,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AAL7B,aAAA,UAA+B;AAMpC,YAAI,QAAQ,KAAK;AACjB,aAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,OAAO;AACrE,aAAK,cAAc,IAAI,kBAAA,aAAY;MACrC;AAKA,aAAA,eAAWA,mBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAQ,KAAK,YAAyC;QACxD;;;;AAKO,MAAAA,mBAAA,UAAA,aAAP,SAAkB,SAA4B;AAC5C,aAAK,UAAU;MACjB;AAKO,MAAAA,mBAAA,UAAA,aAAP,WAAA;MACA;AAKO,MAAAA,mBAAA,UAAA,QAAP,WAAA;AAAa,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAe;AAAf,gBAAA,EAAA,IAAA,UAAA,EAAA;;MACb;AAeO,MAAAA,mBAAA,UAAA,aAAP,SAAkB,WAAgC;MAClD;AAKO,MAAAA,mBAAA,UAAA,aAAP,SAAkB,WAAgC;AAChD,eAAO;MACT;AAKO,MAAAA,mBAAA,UAAA,eAAP,SAAoB,WAAgC;AAClD,eAAO;MACT;AAYU,MAAAA,mBAAA,UAAA,iBAAV,SACE,SAAuB,MACvB,UAAiC,MAAS;AAE1C,YAAI,OAAO,EAAC,MAAM,UAAU,KAAI;AAChC,gBAAQ,QAAQ,IAAI;AACpB,eAAO,KAAK;MACd;AAxGc,MAAAA,mBAAA,OAAe;AAKf,MAAAA,mBAAA,UAAsB,CAAA;AAqGtC,aAAAA;MA/GA;AAAsB,YAAA,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpGtB,QAAA,eAAA;AAOA,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAwEA;AAhCE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,SAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AASU,MAAAA,SAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,YAAI,KAAK,WAAW,IAAI,MAAM,MAAM,WAAW;AAC7C,eAAK,WAAW,aAAa,WAAW,OAAO;;AAEjD,qBAAa,KAAK,uBAAuB,YAAY,KAAK,WAAW,iBAAgB,CAAE;AACvF,kBAAW,CAAC,CAAC,KAAK,WAAW,IAAI,cAAc,KACnC,CAAC,KAAK,WAAW,IAAI,cAAc,KAAK,KAAK,WAAW,IAAI,SAAS,MAAM;AACvF,aAAK,WAAW,aAAa,gBAAgB,OAAO;AACpD,gBAAS,KAAK,WAAW,IAAI,aAAa,KAChC,KAAK,YAA+B,SAAS,aAAa;AACpE,eAAA,UAAM,4BAA2B,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;MACrE;AAjEc,MAAAA,SAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,sBAAsB,QAAQ,GAAA,EACjC,aAAa,UACb,UAAU,UACV,WAAW,IACX,gBAAgB,eAChB,KAAK,OACL,aAAa,GACb,cAAc,OACd,SAAS,UACT,UAAU,IACV,UAAU,aACV,QAAQ,IACR,gBAAgB,IAChB,iBAAiB,IACjB,iBAAiB,IACjB,SAAS,IACT,SAAS,IACT,sBAAsB,IAAI,KAAK,KAAK,CAAC,GACrC,eAAe,OACf,qBAAqB,UACrB,aAAa,OACb,mBAAmB,KACnB,aAAa,QACb,aAAa,QACb,cAAc,IACd,kBAAkB,eAClB,kBAAkB,eAClB,iBAAkB,eAClB,iBAAkB,cAAa,CAAA;AAsCnC,aAAAA;MAxE6B,aAAA,qBAAqB;AAArC,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,QAAA,SAAA,QAAA;AAA2B,gBAAAC,QAAA,MAAA;AAA3B,eAAAA,SAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAsBY,cAAA,WAAW,aAAA,SAAS;;MA0ChC;AArCE,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAQO,MAAAA,OAAA,UAAA,yBAAP,SAA8B,YACA,SAA0B,OAAmB,OAAsB;AADnE,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA8B;AAC9B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAiB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAC/F,eAAA,UAAM,uBAAsB,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;AAC9D,YAAI,OAAO,KAAK,QAAO;AACvB,YAAI,KAAK,MAAMA,OAAM,eAAe,KAAK,CAAC,WAAW,aAAa;AAChE,eAAK,WAAW,aAAa,eAAe,QAAQ;;MAExD;AAOO,MAAAA,OAAA,UAAA,cAAP,SAAmB,MAAqB;AACtC,aAAK,aAAa,IAAI;AACtB,YAAI,OAAO,KAAK,QAAO;AACvB,YAAI,KAAK,SAAS,KAAK,KAAK,MAAMA,OAAM,YAAY,KAChD,KAAK,WAAW,IAAI,aAAa,MAAM,YACvC,KAAK,YAAY,QAAQ,MAAM,UAC/B,KAAK,YAAY,UAAU,MAAM,QAAW;AAC9C,eAAK,WAAW,aAAA,SAAS;AACzB,eAAK,YAAY,UAAU,IAAI;;AAEjC,eAAO;MACT;AAzDc,MAAAA,OAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ;AAMpB,MAAAA,OAAA,eAAuB;AAIvB,MAAAA,OAAA,kBACZ;AA+CJ,aAAAA;MAhE2B,aAAA,oBAAoB;AAAlC,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,QAAA,SAAA,QAAA;AAA2B,gBAAAC,QAAA,MAAA;AAA3B,eAAAA,SAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MAShC;AAJE,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAdc,MAAAA,OAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ;AAepC,aAAAA;MArB2B,aAAA,oBAAoB;AAAlC,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MAiBhC;AAZE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AAtBc,MAAAA,UAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ;AAuBpC,aAAAA;MA7B8B,aAAA,oBAAoB;AAArC,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAgBY,cAAA,WAAW,aAAA,SAAS;;MA0ChC;AArCS,MAAAA,WAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,eAAO;MACT;AAKA,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,WAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,WAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AAOA,aAAA,eAAWA,WAAA,WAAA,cAAU;aAArB,WAAA;AACE,cAAI,aAAa,KAAK;AACtB,iBAAQ,WAAW,YAAY,OAAO,KAAK,QAAQ,WAAW,YAAY,QAAQ,KAAK,QAC/E,WAAW,YAAY,OAAO,KAAK,QAAQ,WAAW,IAAI,WAAW,MAAM;QACrF;;;;AAnDc,MAAAA,WAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ,GAAA,EAChC,OAAQ,OACR,QAAQ,OACR,OAAQ,OACR,WAAW,OAAM,CAAA;AAgDrB,aAAAA;MA1D+B,aAAA,oBAAoB;AAAtC,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,QAAA,SAAA,QAAA;AAA2B,gBAAAC,QAAA,MAAA;AAA3B,eAAAA,SAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAcY,cAAA,WAAW,aAAA,SAAS;;MAShC;AAJE,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAhBc,MAAAA,OAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ,GAAA,EAChC,QAAQ,KACR,QAAQ,IAAG,CAAA;AAef,aAAAA;MAvB2B,aAAA,oBAAoB;AAAlC,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,QAAgB;;MA8I5B;AAzIE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAOA,aAAA,eAAWA,SAAA,WAAA,eAAW;aAAtB,WAAA;;;AACE,qBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,kBAAM,QAAK,GAAA;AACd,kBAAI,CAAC,MAAM,aAAa;AACtB,uBAAO;;;;;;;;;;;;AAGX,iBAAO;QACT;;;;AAQA,aAAA,eAAWA,SAAA,WAAA,iBAAa;aAAxB,WAAA;;AACE,cAAI,cAAc;AAClB,cAAI,IAAI;;AACR,qBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,kBAAM,QAAK,GAAA;AACd,kBAAI,OAAO;AACT,oBAAI,MAAM,eAAe;AACvB,sBAAI,aAAa;AACf,2BAAO;;AAET,gCAAc;AACd,uBAAK,QAAQ;2BACJ,CAAC,MAAM,aAAa;AAC7B,yBAAO;;;AAGX;;;;;;;;;;;AAEF,iBAAO;QACT;;;;AAKO,MAAAA,SAAA,UAAA,OAAP,WAAA;AACE,YAAI,CAAC,KAAK,iBAAiB,KAAK,SAAS,MAAM;AAC7C,iBAAO;;AAET,eAAO,KAAK,WAAW,KAAK,KAAK;MACnC;AAKO,MAAAA,SAAA,UAAA,SAAP,WAAA;AACE,YAAI,CAAC,KAAK,iBAAiB,KAAK,SAAS,MAAM;AAC7C,iBAAO;;AAET,eAAO,KAAK,WAAW,KAAK,KAAK,EAAE,OAAM;MAC3C;AAKO,MAAAA,SAAA,UAAA,iBAAP,WAAA;;AACE,YAAI,IAAI;;AACR,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,SAAS,CAAC,MAAM,aAAa;AAC/B;;;;;;;;;;;;AAGJ,eAAO;MACT;AAKO,MAAAA,SAAA,UAAA,gBAAP,WAAA;;;AACE,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,SAAS,CAAC,MAAM,aAAa;AAC/B,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAKO,MAAAA,SAAA,UAAA,eAAP,WAAA;AACE,YAAI,IAAI,KAAK,WAAW;AACxB,eAAO,EAAE,KAAK,GAAG;AACf,cAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,cAAI,SAAS,CAAC,MAAM,aAAa;AAC/B,mBAAO;;;AAGX,eAAO;MACT;AAKO,MAAAA,SAAA,UAAA,cAAP,SAAmB,MAAa;;AAC9B,YAAI,KAAK,YAAY,MAAM,KAAK,QAAQ,KAAK,YAAY,OAAO,KAAK,MAAM;AAQzE,eAAK,aAAa,IAAI;AACtB,iBAAO;;AACP,qBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,kBAAM,QAAK,GAAA;AACd,qBAAO,MAAM,YAAY,IAAI;;;;;;;;;;;AAE/B,cAAI,KAAK,YAAY,MAAM;AACzB,iBAAK,WAAW,aAAA,SAAS;;eAEtB;;AAIL,qBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,kBAAM,QAAK,GAAA;AACd,qBAAO,MAAM,YAAY,IAAI;;;;;;;;;;;AAE/B,cAAI,KAAK,WAAW,CAAC,GAAG;AACtB,iBAAK,eAAe,KAAK,WAAW,CAAC,CAAC;;;AAG1C,eAAO;MACT;AAnJc,MAAAA,SAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AAoJ/B,aAAAA;MA1J6B,aAAA,eAAe;AAA/B,YAAA,UAAA;AAkKb,QAAA,kBAAA,SAAA,QAAA;AAAqC,gBAAAC,kBAAA,MAAA;AAArC,eAAAA,mBAAA;;MAmCA;AAzBE,aAAA,eAAWA,iBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,iBAAA,UAAA,WAAP,WAAA;AACE,eAAO,MAAM,KAAK,WAAW,KAAK,GAAG,IAAI;MAC3C;AA5Bc,MAAAA,iBAAA,WAAyB,QAAQ;AA8BjD,aAAAA;MAnCqC,OAAO;AAA/B,YAAA,kBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKb,QAAA,eAAA;AAOA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MA4DA;AA5CE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAa;;AAC9B,aAAK,aAAa,IAAI;;AACtB,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,kBAAM,YAAY,IAAI;;;;;;;;;;;AAExB,eAAO;MACT;AAMU,MAAAA,UAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,YAAI,CAAC,WAAW,QAAQ,GAAG;AACzB;;AAEF,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,OAAO,OAAO,KAAK;AACzE,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,OAAO,OAAO,IAAI;MAC1E;AArDc,MAAAA,UAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ,GAAA,EAC/B,eAAe,UACf,UAAU,UACV,YAAY,UACZ,UAAU,MAAK,CAAA;AAkDnB,aAAAA;MA5D8B,aAAA,mBAAmB;AAApC,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MA2ChC;AAtCE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,aAAa,IAAI;AACtB,aAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AACnC,eAAO;MACT;AAOU,MAAAA,UAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,QAAe;AAC/G,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,IAAI;MAC5E;AAhDc,MAAAA,UAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AAiD/B,aAAAA;MAvD8B,aAAA,eAAe;AAAhC,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MAwChC;AAnCE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAQO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,aAAa,IAAI;AACtB,aAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AACnC,aAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AACnC,eAAO;MACT;AAOU,MAAAA,UAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,IAAI;AAC1E,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,OAAO,QAAQ,GAAG,KAAK;MAC/E;AA7Cc,MAAAA,UAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AA8C/B,aAAAA;MApD8B,aAAA,eAAe;AAAhC,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MAyBhC;AApBE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,WAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,WAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AA9Bc,MAAAA,WAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AA+B/B,aAAAA;MArC+B,aAAA,eAAe;AAAjC,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MAqBA;AAJE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAdc,MAAAA,YAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,sBAAsB,QAAQ,GAAA,EACjC,OAAO,IACP,QAAQ,IACR,OAAO,IACP,QAAQ,GACR,SAAS,EAAC,CAAA;AAUd,aAAAA;MArBgC,aAAA,qBAAqB;AAAxC,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,cAAA,SAAA,QAAA;AAAiC,gBAAAC,cAAA,MAAA;AAAjC,eAAAA,eAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYY,cAAA,WAAW,aAAA,SAAS;;MAShC;AAJE,aAAA,eAAWA,aAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAdc,MAAAA,aAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,sBAAsB,QAAQ;AAerC,aAAAA;MArBiC,aAAA,qBAAqB;AAAzC,YAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAeY,cAAA,WAAW,aAAA,SAAS;AAKvB,cAAA,aAAwB,CAAA;AAIxB,cAAA,OAAgB;AAIhB,cAAA,QAAiB;;MAyG1B;AApGE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAQO,MAAAA,YAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,aAAa,IAAI;AACtB,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK,KAAK,YAAY,IAAI;;AAEnC,YAAI,KAAK,WAAW,CAAC,GAAG;AACtB,iBAAO,KAAK,WAAW,CAAC,EAAE,YAAY,IAAI;;AAE5C,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,IAAI,GAAG,KAAK;AACtD,cAAI,KAAK,WAAW,IAAI,CAAC,GAAG;AAC1B,mBAAO,KAAK,WAAW,IAAI,CAAC,EAAE,YAAY,IAAI;;AAEhD,cAAI,KAAK,WAAW,CAAC,GAAG;AACtB,mBAAO,KAAK,WAAW,CAAC,EAAE,YAAY,IAAI;;;AAG9C,YAAI,KAAK,OAAO;AACd,iBAAO,KAAK,MAAM,YAAY,IAAI;;AAEpC,aAAK,eAAe,KAAK,IAAI;AAC7B,eAAO;MACT;AAQU,MAAAA,YAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;;AAC9G,aAAK,aAAY;;AACjB,mBAAoB,KAAA,SAAA,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhE,gBAAM,QAAK,GAAA;AACd,gBAAI,OAAO;AACT,oBAAM,uBAAuB,YAAY,SAAS,OAAO,KAAK;;;;;;;;;;;;AAGlE,eAAA,UAAM,4BAA2B,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;MACrE;AAKU,MAAAA,YAAA,UAAA,eAAV,WAAA;;AACM,YAAA,KAA4B,KAAK,WAAW,QAAQ,QAAQ,SAAS,YAAY,GAAhF,OAAI,GAAA,MAAE,QAAK,GAAA,OAAE,aAAU,GAAA;AAE5B,eAAO,KAAK,QAAQ,cAAc,EAAE;AACpC,gBAAQ,MAAM,QAAQ,cAAc,EAAE;AACtC,qBAAa,WAAW,QAAQ,cAAc,EAAE;AAIhD,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,SAAS,MAAM,EAAC,OAAO,MAAM,MAAM,SAAQ,GAAG,aAAA,SAAS,IAAI;;AAK9E,YAAI,YAAY;AACd,iBAAO,WAAW,SAAS,KAAK,WAAW,SAAS,GAAG;AACrD,0BAAc,WAAW,OAAO,WAAW,SAAS,CAAC;;AAEvD,cAAI,IAAI;;AACR,qBAAoB,KAAA,SAAA,KAAK,WAAW,MAAM,CAAC,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,kBAAM,QAAK,GAAA;AACd,kBAAI,OAAO;AACT,qBAAK,WAAW,KAAK,KAAK,SAAS,WAAW,OAAO,GAAG,CAAC,CAAC;;;;;;;;;;;;;AAOhE,YAAI,OAAO;AACT,eAAK,QAAQ,KAAK,SAAS,OAAO,EAAC,OAAO,MAAM,MAAM,UAAS,GAAG,aAAA,SAAS,KAAK;;MAEpF;AAQU,MAAAA,YAAA,UAAA,WAAV,SAAmB,GAAW,YAA+B,UAAuB;AAAtD,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA6B;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAuB;AAClF,YAAI,OAAQ,KAAK,QAAQ,OAAO,MAAM,EAAe,QAAQ,CAAC;AAC9D,YAAI,OAAO,KAAK,QAAQ,OAAO,MAAM,YAAY,CAAC,IAAI,CAAC;AACvD,aAAK,WAAW;AAChB,aAAK,SAAS;AACd,eAAO;MACT;AA9Hc,MAAAA,YAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,MAAM,KACN,OAAO,KACP,YAAY,IAAG,CAAA;AA4HnB,aAAAA;MArIgC,aAAA,eAAe;AAAlC,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,cAAA,SAAA,QAAA;AAAiC,gBAAAC,cAAA,MAAA;AAAjC,eAAAA,eAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAaY,cAAA,WAAW,aAAA,SAAS;;MAmChC;AA7BE,aAAA,eAAWA,aAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,aAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,aAAA,WAAA,uBAAmB;aAA9B,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,aAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,eAAO,KAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AAC1C,aAAK,eAAe,KAAK,WAAW,CAAC,CAAC;AACtC,eAAO;MACT;AAzCc,MAAAA,aAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,UAAU,UAAS,CAAA;AAyCvB,aAAAA;MAhDiC,aAAA,eAAe;AAAnC,YAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MAsGA;AAxFE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,YAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,YAAQ;aAAnB,WAAA;AACE,cAAM,YAAY,KAAK,WAAW,IAAI,WAAW;AACjD,cAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,WAAW,QAAQ,SAAS,CAAC,IAAI;AACrE,iBAAO,KAAK,WAAW,CAAC,KAAK,KAAK,QAAQ,OAAO,MAAM;QACzD;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO,KAAK,SAAS;QACvB;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO,KAAK,SAAS;QACvB;;;;AAKO,MAAAA,YAAA,UAAA,OAAP,WAAA;AACE,eAAO,KAAK,SAAS,KAAI;MAC3B;AAKO,MAAAA,YAAA,UAAA,SAAP,WAAA;AACE,eAAO,KAAK,SAAS,OAAM;MAC7B;AAKU,MAAAA,YAAA,UAAA,mBAAV,SAA2B,SAAqB;AAC9C,eAAA,UAAM,iBAAgB,KAAA,MAAC,OAAO;AAC9B,YAAI,KAAK,WAAW,IAAI,YAAY,MAAM,YACtC,KAAK,WAAW,YAAY,WAAW,MAAM,QAAW;AAC1D,cAAM,aAAa,KAAK,WAAW,iBAAgB;AACnD,iBAAO,WAAW;;MAEtB;AAQO,MAAAA,YAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,YAAI,KAAK,WAAW,IAAI,YAAY,MAAM,aAAa,KAAK,WAAW,CAAC,GAAG;AACzE,eAAK,WAAW,CAAC,EAAE,YAAY,IAAI;;AAErC,YAAI,WAAW,KAAK;AACpB,eAAO,SAAS,YAAY,IAAI;AAChC,aAAK,eAAe,QAAQ;AAC5B,eAAO;MACT;AAKO,MAAAA,YAAA,UAAA,sBAAP,WAAA;AACE,YAAI,YAAY,KAAK,IAAI,GAAI,KAAK,WAAW,IAAI,WAAW,IAAe,CAAC;AAC5E,YAAI,YAAY,KAAK,WAAW,QAAQ;AACtC,sBAAY;;AAEd,aAAK,WAAW,IAAI,aAAa,SAAS;MAC5C;AA/Fc,MAAAA,YAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,YAAY,UACZ,WAAW,EAAC,CAAA;AA8FhB,aAAAA;MAtGgC,aAAA,eAAe;AAAlC,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MA8DA;AAhDE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,YAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,OAAG;aAAd,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,YAAA,WAAA,OAAG;aAAd,WAAA;AACE,iBAAO;QACT;;;;AAOU,MAAAA,YAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,YAAI,QAAQ,KAAK;AACjB,cAAM,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,KAAK;AACjE,cAAM,CAAC,EAAE,uBAAuB,YAAY,OAAO,QAAQ,GAAG,SAAS,KAAK,QAAQ,CAAC;AACrF,YAAI,CAAC,MAAM,CAAC,GAAG;AACb;;AAEF,cAAM,CAAC,EAAE,uBAAuB,YAAY,OAAO,QAAQ,GAAG,SAAS,KAAK,QAAQ,CAAC;MACvF;AAvDc,MAAAA,YAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ,GAAA,EAC/B,gBAAgB,IAChB,kBAAkB,GAAE,CAAA;AAsDxB,aAAAA;MA9DgC,aAAA,mBAAmB;AAAtC,YAAA,aAAA;AAqEb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAwBA;AAZE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,SAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAjBc,MAAAA,SAAA,WAAQ,SAAA,CAAA,GACjB,WAAW,QAAQ;AAkB1B,aAAAA;MAxB6B,UAAU;AAA1B,YAAA,UAAA;AA+Bb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAwCA;AA5BE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,SAAA,WAAA,SAAK;aAAT,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,SAAA,WAAA,OAAG;aAAP,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,SAAA,WAAA,OAAG;aAAP,WAAA;AACE,iBAAO;QACT;;;;AAjCc,MAAAA,SAAA,WAAQ,SAAA,CAAA,GACjB,WAAW,QAAQ;AAkC1B,aAAAA;MAxC6B,UAAU;AAA1B,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Gb,QAAA,eAAA;AAOA,QAAA,gBAAA,SAAA,QAAA;AAAmC,gBAAAC,gBAAA,MAAA;AAAnC,eAAAA,iBAAA;;MA4HA;AAxGE,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,eAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,eAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,eAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAUU,MAAAA,eAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,YAAI,QAAQ,KAAK;AACjB,cAAM,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC;AACvF,YAAI,QAAQ,CAAC,EAAE,CAAC,WAAW,MAAM,CAAC,EAAE,OAAM,EAAG,WAAW,IAAI,eAAe;AAC3E,YAAI,UAAW,KAAK,YAAqC;AACzD,cAAM,CAAC,EAAE,uBAAuB,YAAY,OACZ,KAAK,eAAe,QAAQ,CAAC,GAAG,OAAO,KAAK,GAC5C,SAAS,KAAK,UAAU,CAAC;AACzD,aAAK,mBAAmB,GAAG,QAAQ,CAAC,GAAG,SAAS,OAAO,OAAO,KAAK;AACnE,YAAI,CAAC,MAAM,CAAC,GAAG;AACb;;AAEF,cAAM,CAAC,EAAE,uBAAuB,YAAY,OACZ,KAAK,eAAe,QAAQ,CAAC,GAAG,OAAO,KAAK,GAC5C,SAAS,KAAK,UAAU,CAAC;AACzD,aAAK,mBAAmB,GAAG,QAAQ,CAAC,GAAG,SAAS,OAAO,OAAO,KAAK;MACrE;AAQU,MAAAA,eAAA,UAAA,iBAAV,SAAyB,QAAgB,OAAgB,OAAa;AACpE,YAAI,SAAS,CAAC,KAAK,WAAW,IAAI,MAAM,GAAG;AACzC;;AAEF,eAAO;MACT;AAcU,MAAAA,eAAA,UAAA,qBAAV,SAA6B,GAAW,QAAgB,SAAkB,OAC7C,OAAgB,OAAc;AACzD,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,KAAK,WAAW,YAAY,MAAM,KAAK,QAAQ,KAAK,eAAe;AACrE,cAAI,QAAQ,KAAK,OAAM,EAAG,WAAW,IAAI,QAAQ;AACjD,eAAK,WAAW,aAAa,QAAQ,KAAK;AAC1C,cAAI,UAAU,KAAK,WAAW,WAAW,MAAM,GAAG;AAChD,iBAAK,uBAAuB,CAAA,GAAI,SAAS,KAAK,eAAe,QAAQ,OAAO,KAAK,GAAG,KAAK;;;MAG/F;AArHc,MAAAA,eAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ,GAAA,EAC/B,QAAQ,OACR,aAAa,OACb,OAAO,SAAQ,CAAA;AAMA,MAAAA,eAAA,UAAU,CAAC,IAAI,eAAe,QAAQ;AA6GzD,aAAAA;MA5HmC,aAAA,mBAAmB;AAAzC,YAAA,gBAAA;AAmIb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MAwBA;AAZE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,WAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAjBc,MAAAA,WAAA,WAAQ,SAAA,CAAA,GACf,cAAc,QAAQ;AAkB/B,aAAAA;MAxB+B,aAAa;AAA/B,YAAA,YAAA;AA+Bb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MA4CA;AA5BE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,SAAK;aAAT,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AArCc,MAAAA,UAAA,WAAQ,SAAA,CAAA,GACf,cAAc,QAAQ;AAKZ,MAAAA,UAAA,UAAU,CAAC,IAAI,UAAU,aAAa;AAiCzD,aAAAA;MA5C8B,aAAa;AAA9B,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKb,QAAA,eAAA;AACA,QAAA,eAAA;AAOA,QAAA,mBAAA,SAAA,QAAA;AAAsC,gBAAAC,mBAAA,MAAA;AAAtC,eAAAA,oBAAA;;MAsFA;AA1EE,aAAA,eAAWA,kBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,kBAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAUU,MAAAA,kBAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,KAAK;AAC3E,YAAI,aAAa;AACjB,iBAAS,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AACtD,cAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,cAAI,MAAM,OAAO,aAAa,GAAG;AAC/B,gBAAI,CAAC,YAAY;AACf,2BAAa;AACb,kBAAI,IAAI,MAAM,GAAG;AACf,oBAAI,OAAO,KAAK,QAAQ,OAAO,MAAM;AACrC,qBAAK,WAAW,OAAO,GAAG,GAAG,IAAI;AACjC,qBAAK,SAAS;AACd;;;iBAGC;AACL,gBAAI,aAAa,SAAU,IAAI,MAAM;AACrC,kBAAM,uBAAuB,YAAY,OAAO,QAAQ,GAAG,UAAU;AACrE;;;AAGJ,YAAI,KAAK,WAAW,SAAS,OAAO,aAAa,IAAI,IAAI;AACvD,eAAK,YAAY,KAAK,QAAQ,OAAO,MAAM,CAAC;AAC5C,eAAK,WAAW,KAAK,WAAW,SAAS,CAAC,EAAE,uBAAuB,YAAY,OAAO,QAAQ,GAAG,KAAK;;MAE1G;AAOU,MAAAA,kBAAA,UAAA,iBAAV,SAAyB,SAAqB;AAC5C,YAAI,aAAa;AACjB,YAAI,MAAM,QAAQ,kBAAkB;AACpC,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,cAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,cAAI,MAAM,OAAO,aAAa,GAAG;AAC/B,gBAAI,YAAY;AACd,oBAAM,OAAO,MAAM,OAAO,8BAA8B,KAAK,MAAM,SAAS,IAAI;mBAC3E;AACL,2BAAa;AACb,kBAAI,IAAI,MAAM,KAAK,CAAC,KAAK;AACvB,qBAAK,OAAO,4DAA4D,OAAO;;;;;AAKvF,YAAI,KAAK,WAAW,SAAS,OAAO,aAAa,IAAI,MAAM,CAAC,KAAK;AAC/D,eAAK,OAAO,yDAAyD,OAAO;;AAE9E,eAAA,UAAM,eAAc,KAAA,MAAC,OAAO;MAC9B;AA/Ec,MAAAA,kBAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,WAAW,QAAQ;AAgF1B,aAAAA;MAtFsC,aAAA,UAAU;AAAnC,YAAA,mBAAA;AA6Fb,QAAA,iBAAA,SAAA,QAAA;AAAoC,gBAAAC,iBAAA,MAAA;AAApC,eAAAA,kBAAA;;MAmCA;AAvBE,aAAA,eAAWA,gBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,gBAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAOO,MAAAA,gBAAA,UAAA,aAAP,SAAkB,SAAqB;AACrC,eAAA,UAAM,WAAU,KAAA,MAAC,OAAO;AACxB,YAAI,KAAK,UAAU,CAAC,KAAK,OAAO,OAAO,eAAe,GAAG;AACvD,eAAK,OAAO,KAAK,OAAO,qCAAqC,SAAS,IAAI;;MAE9E;AA5Bc,MAAAA,gBAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AA6B/B,aAAAA;MAnCoC,aAAA,eAAe;AAAtC,YAAA,iBAAA;AA0Cb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAmCA;AAvBE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,SAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAOO,MAAAA,SAAA,UAAA,aAAP,SAAkB,SAAqB;AACrC,eAAA,UAAM,WAAU,KAAA,MAAC,OAAO;AACxB,YAAI,KAAK,UAAU,CAAC,KAAK,OAAO,OAAO,eAAe,GAAG;AACvD,eAAK,OAAO,KAAK,OAAO,qCAAqC,SAAS,IAAI;;MAE9E;AA5Bc,MAAAA,SAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ;AA6B/B,aAAAA;MAnC6B,aAAA,eAAe;AAA/B,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Ib,QAAA,eAAA;AACA,QAAA,cAAA;AAOA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AA8BS,cAAA,aAAa;UAClB,WAAW;;AAMH,cAAA,WAAW,aAAA,SAAS;;MAgHhC;AA3GE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,WAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,yBAAP,SAA8B,YAA2B,SAAkB,OAAe,OAAc;;;AAKtG,mBAAmB,qBAAA,SAAA,aAAA,gBAAgB,GAAA,uBAAA,mBAAA,KAAA,GAAA,CAAA,qBAAA,MAAA,uBAAA,mBAAA,KAAA,GAAE;AAAhC,gBAAM,SAAI,qBAAA;AACb,gBAAI,WAAW,MAAI,GAAG;AACpB,mBAAK,WAAW,aAAa,QAAM,WAAW,MAAI,EAAE,CAAC,CAAC;;AAExD,gBAAI,KAAK,WAAW,YAAY,MAAI,MAAM,QAAW;AACnD,qBAAQ,KAAK,WAAW,iBAAgB,EAAI,MAAI;;;;;;;;;;;;AAGpD,eAAA,UAAM,uBAAsB,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;MAChE;AASU,MAAAA,WAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,QAAe;;;AAC/G,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,CAAC,MAAM,OAAO,KAAK,GAAG;AACxB,mBAAK,aAAa,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK,EAChD,YAAY,KAAK;;;;;;;;;;;;AAGxB,gBAAQ,KAAK,YAAY,aAAa,KAAe;AACrD,kBAAU,CAAC,EAAE,KAAK,WAAW,YAAY,cAAc,KAAK,KAAK,WAAW,WAAW,cAAc;AACrG,qBAAa,KAAK,uBAAuB,YAAY;UACnD,aAAa,KAAK,WAAW,IAAI,aAAa;UAC9C,UAAU;SACX;AACD,YAAM,UAAU,KAAK,WAAW,YAAY,cAAc;AAC1D,YAAM,UAAS,GAAA,YAAA,OAAM,KAAK,WAAW,IAAI,UAAU,CAAW;;AAC9D,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,uBAAW,SAAS,CAAC,IAAI,OAAO,MAAK,KAAM,WAAW,SAAS,CAAC;AAChE,kBAAM,uBAAuB,YAAY,SAAS,OAAO,CAAC,CAAC,OAAO;;;;;;;;;;;MAEtE;AAOU,MAAAA,WAAA,UAAA,iBAAV,SAAyB,SAAqB;AAC5C,YAAI,MAAe;AACnB,YAAM,UAAU,KAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,cAAM,QAAQ,KAAK,WAAW,CAAC;AAC/B,cAAI,MAAM,OAAO,KAAK,GAAG;AACvB,kBAAM;iBACD;AACL,gBAAM,QAAQ,MAAM,OAAO,KAAK;AAKhC,gBAAI,KAAK;AACP,mBAAK,YAAY,KAAK;AACtB;mBACK;AACL,oBAAM,KAAK,aAAa,QAAQ,OAAO,KAAK,GAAG,KAAK;;AAEtD,gBAAI,YAAY,QAAQ,QAAQ,QAAQ,OAAO,OAAO,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC;AAClE,gBAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,oBAAM,OAAO,YAAY,KAAK;AAC9B,oBAAM,SAAS;AACf,uBAAS,IAAI,YAAY,QAAQ,OAAO,KAAK,CAAC;AAC9C,kBAAM,SAAS,MAAM,OAAO,iBAAiB,KAAK,OAAO,8BAA8B,SAAS,KAAK;AACrG,kBAAI,WAAW,IAAI,WAAW,SAAS,CAAC,EAAE,YAAY,MAAM;;;;AAIlE,eAAA,UAAM,eAAc,KAAA,MAAC,OAAO;MAC9B;AAKO,MAAAA,WAAA,UAAA,cAAP,SAAmB,MAAa;;AAC9B,aAAK,aAAa,IAAI;;AACtB,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,kBAAM,YAAY,IAAI;;;;;;;;;;;AAExB,eAAO;MACT;AA9Ic,MAAAA,WAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,OAAO,QACP,UAAU,YACV,aAAa,UACb,YAAY,UACZ,gBAAgB,MAChB,aAAa,QACb,OAAO,QACP,YAAY,OACZ,eAAe,QACf,UAAU,QACV,aAAa,QACb,OAAO,QACP,cAAc,eACd,WAAW,OACX,cAAc,OACd,cAAc,OACd,MAAM,SACN,iBAAiB,QAAO,CAAA;AA6H5B,aAAAA;MArJ+B,aAAA,eAAe;AAAjC,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRb,QAAA,eAAA;AACA,QAAA,kBAAA;AACA,QAAA,cAAA;AAOA,QAAA,SAAA,SAAA,QAAA;AAA4B,gBAAAC,SAAA,MAAA;AAA5B,eAAAA,UAAA;;MAsFA;AAvEE,aAAA,eAAWA,QAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,QAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAOU,MAAAA,QAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;;;AAC9G,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,CAAC,MAAM,OAAO,KAAK,GAAG;AACxB,mBAAK,aAAa,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK,EAC9C,YAAY,KAAK;;;;;;;;;;;;AAG1B,YAAM,UAAS,GAAA,YAAA,OAAM,KAAK,WAAW,IAAI,aAAa,CAAW;AACjE,YAAI,KAAK,UAAU,GAAG;AACpB,iBAAO,QAAQ,KAAK,OAAO,WAAW,IAAI,MAAM,CAAW;;AAE7D,qBAAa,KAAK,uBAAuB,YAAY;UACnD,UAAU,KAAK,WAAW,IAAI,UAAU;UACxC,aAAa;SACd;;AACD,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,uBAAW,YAAY,CAAC,IAAI,OAAO,MAAK,KAAM,WAAW,YAAY,CAAC;AACtE,kBAAM,uBAAuB,YAAY,SAAS,OAAO,KAAK;;;;;;;;;;;MAElE;AAOU,MAAAA,QAAA,UAAA,iBAAV,SAAyB,SAAqB;;AAC5C,YAAI,KAAK,UAAU,CAAC,KAAK,OAAO,OAAO,QAAQ,GAAG;AAChD,eAAK,OAAO,KAAK,OAAO,qCAAqC,SAAS,IAAI;AAC1E;;;AAEF,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,CAAC,MAAM,OAAO,KAAK,GAAG;AACxB,kBAAI,MAAM,KAAK,aAAa,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK;AAC7D,kBAAI,YAAY,KAAK;AACrB,kBAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,sBAAM,OAAO,iBAAiB,KAAK,OAAO,gBAAgB,OAAO;;;;;;;;;;;;;AAIvE,eAAA,UAAM,eAAc,KAAA,MAAC,OAAO;MAC9B;AAKO,MAAAA,QAAA,UAAA,cAAP,SAAmB,MAAa;;AAC9B,aAAK,aAAa,IAAI;;AACtB,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,kBAAM,YAAY,IAAI;;;;;;;;;;;AAExB,eAAO;MACT;AA/Ec,MAAAA,QAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,UAAU,gBAAA,SACV,aAAa,gBAAA,SACb,YAAY,gBAAA,QAAO,CAAA;AA6EvB,aAAAA;MAtF4B,aAAA,eAAe;AAA9B,YAAA,SAAA;AA6Fb,QAAA,gBAAA,SAAA,QAAA;AAAmC,gBAAAC,gBAAA,MAAA;AAAnC,eAAAA,iBAAA;;MAiBA;AAZE,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,eAAA,WAAA,SAAK;aAAT,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EAjBmC,MAAM;AAA5B,YAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtGb,QAAA,eAAA;AACA,QAAA,kBAAA;AAOA,QAAA,SAAA,SAAA,QAAA;AAA4B,gBAAAC,SAAA,MAAA;AAA5B,eAAAA,UAAA;;MA2DA;AA1CE,aAAA,eAAWA,QAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,QAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,QAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAOU,MAAAA,QAAA,UAAA,iBAAV,SAAyB,SAAqB;AAC5C,YAAI,KAAK,UAAU,CAAC,KAAK,OAAO,OAAO,KAAK,GAAG;AAC7C,eAAK,OAAO,KAAK,OAAO,gDAAgD,SAAS,IAAI;AACrF;;AAEF,eAAA,UAAM,eAAc,KAAA,MAAC,OAAO;MAC9B;AAKO,MAAAA,QAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,aAAa,IAAI;AACtB,aAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AACnC,eAAO;MACT;AApDc,MAAAA,QAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ,GAAA,EAC/B,SAAS,GACT,YAAY,GACZ,UAAU,gBAAA,SACV,aAAa,gBAAA,SACb,YAAY,gBAAA,QAAO,CAAA;AAgDvB,aAAAA;MA3D4B,aAAA,mBAAmB;AAAlC,YAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRb,QAAA,eAAA;AAOA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAkBY,cAAA,WAAW,aAAA,SAAS;;MAqBhC;AAhBE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,mBAAP,SAAwB,SAAqB;AACrC,YAAA,KAA2B,KAAK,WAAW,QAAQ,OAAO,cAAc,OAAO,GAA9E,MAAG,GAAA,KAAE,aAAU,GAAA,YAAE,QAAK,GAAA;AAC7B,YAAI,QAAQ,OAAO,eAAe,MAAM,UAAU,KAAK;AACrD,eAAK,OAAO,kEAAkE,SAAS,IAAI;eACtF;AACL,iBAAA,UAAM,iBAAgB,KAAA,MAAC,OAAO;;MAElC;AAhCc,MAAAA,WAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ,GAAA,EAChC,KAAK,IACL,KAAK,IACL,OAAO,IACP,OAAO,QACP,QAAQ,QACR,QAAQ,MAAK,CAAA;AA2BjB,aAAAA;MAvC+B,aAAA,oBAAoB;AAAtC,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAOA,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAAlC,eAAAA,gBAAA;;MAkCA;AApBE,aAAA,eAAWA,cAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,cAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,cAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AA3Bc,MAAAA,cAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ,GAAA,EAC/B,eAAe,MACf,UAAU,KAAI,CAAA;AA0BlB,aAAAA;MAlCkC,aAAA,mBAAmB;AAAxC,YAAA,eAAA;AAyCb,QAAA,mBAAA,SAAA,QAAA;AAAsC,gBAAAC,mBAAA,MAAA;AAAtC,eAAAA,oBAAA;;MA2BA;AAVE,aAAA,eAAWA,kBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMU,MAAAA,kBAAA,UAAA,8BAAV,WAAA;MAAyC;AApB3B,MAAAA,kBAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,eAAe,MACf,UAAU,MACV,IAAI,cACJ,MAAM,IACN,KAAK,KAAI,CAAA;AAgBb,aAAAA;MA3BsC,aAAA,eAAe;AAAxC,YAAA,mBAAA;AAkCb,QAAA,gBAAA,SAAA,QAAA;AAAmC,gBAAAC,gBAAA,MAAA;AAAnC,eAAAA,iBAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAYS,cAAA,aAAa;UAClB,SAAS;;;MAUb;AAJE,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAhBc,MAAAA,eAAA,WAAQ,SAAA,CAAA,GACjB,iBAAiB,QAAQ;AAiBhC,aAAAA;MAvBmC,gBAAgB;AAAtC,YAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjFb,QAAA,eAAA;AACA,QAAA,UAAA;AAOA,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAwC3B,eAAAA,SAAY,SAAqB,YAA0B,UAAmB;AAA9E,YAAA,QACE,OAAA,KAAA,MAAM,SAAS,YAAY,QAAQ,KAAC;AA7B5B,cAAA,WAAW,aAAA,SAAS;AA8B5B,cAAK,YAAY,YAAY,MAAK,QAAQ;;MAC5C;AA1BA,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,SAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,SAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,EAAE,WAAW,WAAW;QACxE;;;;AAaO,MAAAA,SAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AACnC,eAAO,KAAK,eAAe,IAAI;MACjC;AAOO,MAAAA,SAAA,UAAA,iBAAP,SAAsB,MAAa;AACjC,eAAO;MACT;AAvDc,MAAAA,SAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ;AAwDnC,aAAAA;MA9D6B,aAAA,mBAAmB;AAAnC,YAAA,UAAA;AAkEb,YAAQ,UAAU,iBAAiB,QAAA,MAAM,UAAU;;;", "names": ["AbstractOutputJax", "MmlMath", "MmlMi", "MmlMn", "MmlMtext", "MmlMspace", "MmlMs", "MmlMrow", "MmlInferredMrow", "MmlMfrac", "MmlMsqrt", "MmlMroot", "MmlMerror", "MmlMpadded", "MmlMphantom", "MmlMfenced", "MmlMenclose", "MmlMaction", "MmlMsubsup", "MmlMsub", "MmlMsup", "MmlMunderover", "MmlMunder", "MmlMover", "MmlMmultiscripts", "MmlMprescripts", "MmlNone", "MmlMtable", "MmlMtr", "MmlMlabeledtr", "MmlMtd", "MmlMglyph", "MmlSemantics", "MmlAnnotationXML", "MmlAnnotation", "TeXAtom"]}