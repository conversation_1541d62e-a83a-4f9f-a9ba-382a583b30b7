import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/centernot/CenternotConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/centernot', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      centernot: {
        CenternotConfiguration: module1
      }
    }
  }
}});
