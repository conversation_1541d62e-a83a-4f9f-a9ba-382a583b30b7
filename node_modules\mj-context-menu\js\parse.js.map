{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../ts/parse.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,qDAA0C;AAE1C,qDAA8C;AAC9C,6CAAuC;AACvC,uDAA4C;AAC5C,iDAAsC;AACtC,iDAAsC;AACtC,iDAAsC;AACtC,qDAA0C;AAC1C,+CAAoC;AAEpC,mDAAwC;AACxC,6CAAsC;AACtC,uDAA+D;AAC/D,yDAA+D;AAG/D;IAiCE,gBAAY,IAAkC;QAA9C,iBAEC;QAFW,qBAAA,EAAA,SAAkC;QA/BtC,cAAS,GAA4B;YAC3C,CAAC,SAAS,EAAE,yBAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAO,CAAC,CAAC;YAC3C,CAAC,UAAU,EAAE,2BAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAAQ,CAAC,CAAC;YAC9C,CAAC,OAAO,EAAE,qBAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAK,CAAC,CAAC;YACrC,CAAC,QAAQ,EAAE,uBAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAM,CAAC,CAAC;YACxC,CAAC,OAAO,EAAE,qBAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAK,CAAC,CAAC;YACrC,CAAC,OAAO,EAAE,qBAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAK,CAAC,CAAC;YACrC,CAAC,MAAM,EAAE,mBAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAI,CAAC,CAAC;YAClC,CAAC,SAAS,EAAE,yBAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAO,CAAC,CAAC;YAC3C,CAAC,aAAa,EAAE,6BAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAAW,CAAC,CAAC;YACvD,CAAC,SAAS,EAAE,qBAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAO,CAAC,CAAC;YAC3C,CAAC,UAAU,EAAE,sBAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAQ,CAAC,CAAC;YAC9C,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,eAAe,EAAE,gCAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,gCAAa,CAAC,CAAC;YAC7D,CAAC,cAAc,EAAE,+BAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAAY,CAAC,CAAC;SAC3D,CAAC;QAQe,aAAQ,GAAkB,IAAI,iCAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAS3E,IAAI,CAAC,OAAO,CAAC,UAAC,EAAM;gBAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;YAAM,OAAA,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAAtB,CAAsB,CAAC,CAAC;IACnD,CAAC;IAKD,sBAAW,2BAAO;aAAlB;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IAMM,sBAAK,GAAZ,UAAa,QAAuB,EAAE,GAAU,EAAE,IAAU;;QAC1D,IAAI,MAAM,GAAG,EAAE,CAAC;;YAChB,KAAiB,IAAA,QAAA,SAAA,GAAG,CAAA,wBAAA,yCAAE;gBAAjB,IAAI,IAAI,gBAAA;gBACX,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE;oBACV,SAAS;iBACV;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,KAAK,CAAC,OAAO,EAAE,CAAC;iBACjB;gBACD,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;aACF;;;;;;;;;QACD,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAQM,sBAAK,GAAZ,UAAa,EACgC;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAD/C,IAAM,IAAI,UAAA,EAAK,IAAI,cAApB,QAAqB,CAAD;QAE/B,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,yBAAC,IAAI,CAAC,OAAO,EAAE,IAAI,GAAK,IAAI,GAAE,CAAC,CAAC,IAAI,CAAC;IACzD,CAAC;IAEH,aAAC;AAAD,CAAC,AA/ED,IA+EC;AA/EY,wBAAM"}