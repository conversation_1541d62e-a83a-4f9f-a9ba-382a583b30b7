"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.texSize4 = void 0;
var FontData_js_1 = require("../../FontData.js");
var tex_size4_js_1 = require("../../../common/fonts/tex/tex-size4.js");
exports.texSize4 = (0, FontData_js_1.AddCSS)(tex_size4_js_1.texSize4, {
    0x2044: { c: '/' },
    0x2329: { c: '\\27E8' },
    0x232A: { c: '\\27E9' },
    0x3008: { c: '\\27E8' },
    0x3009: { c: '\\27E9' },
    0xE155: { c: '\\E153\\E152' },
    0xE156: { c: '\\E151\\E150' },
});
//# sourceMappingURL=tex-size4.js.map