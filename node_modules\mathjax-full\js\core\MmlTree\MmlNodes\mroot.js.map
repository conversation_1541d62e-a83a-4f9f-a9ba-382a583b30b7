{"version": 3, "file": "mroot.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mroot.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAgF;AAOhF;IAA8B,4BAAe;IAA7C;QAAA,qEAoDC;QAxCW,cAAQ,GAAG,qBAAQ,CAAC,GAAG,CAAC;;IAwCpC,CAAC;IAnCC,sBAAW,0BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAW,2BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAQM,8BAAW,GAAlB,UAAmB,IAAa;QAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAOS,8CAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IACjF,CAAC;IA7Ca,iBAAQ,gBACjB,4BAAe,CAAC,QAAQ,EAC3B;IA6CJ,eAAC;CAAA,AApDD,CAA8B,4BAAe,GAoD5C;AApDY,4BAAQ"}