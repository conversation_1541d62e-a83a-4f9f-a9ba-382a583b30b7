{"version": 3, "file": "HTMLMathItem.js", "sourceRoot": "", "sources": ["../../../ts/handlers/html/HTMLMathItem.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,sDAAyE;AAYzE;IAA2C,gCAAyB;IAYlE,sBAAY,IAAY,EAAE,GAAsB,EAAE,OAAuB,EAC7D,KAAqD,EACrD,GAAmD;QAFb,wBAAA,EAAA,cAAuB;QAC7D,sBAAA,EAAA,UAAyB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC;QACrD,oBAAA,EAAA,QAAuB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC;eAC7D,kBAAM,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC;IACvC,CAAC;IAXD,sBAAI,iCAAO;aAAX;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC/B,CAAC;;;OAAA;IAyBM,qCAAc,GAArB,UAAsB,KAA4B;QAChD,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,mBAAK,CAAC,QAAQ,EAAE;YACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAChC,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAS,CAAC;gBAChC,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;oBAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;wBACvE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;qBAC/C;oBACD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBAChB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC/D;oBACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;iBAC9C;qBAAM;oBACL,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBAChB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC/C;oBACD,OAAO,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;wBAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAM,CAAC;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBAC1B,IAAI,GAAG,IAAI,CAAC;qBACb;oBACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;oBAC5C,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;wBAChD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;qBACtC;oBACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAC3B;aACF;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,CAAC,CAAC;SAC5B;IACH,CAAC;IAOM,uCAAgB,GAAvB,UAAwB,QAA+B;QACrD,QAAQ,CAAC,aAAa,EAAE,CAAC;IAC3B,CAAC;IAQM,yCAAkB,GAAzB,UAA0B,OAAwB;QAAxB,wBAAA,EAAA,eAAwB;QAChD,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,OAAO,EAAE;YACjC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAC3B,IAAI,IAAI,GAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,IAAI,OAAO,EAAE;gBACX,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBACzD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;oBAChC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC3B;qBAAM;oBACL,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;oBAC7C,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC9C;aACF;YACD,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aAC7B;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SAC/B;IACH,CAAC;IAEH,mBAAC;AAAD,CAAC,AAzGD,CAA2C,8BAAgB,GAyG1D;AAzGY,oCAAY"}