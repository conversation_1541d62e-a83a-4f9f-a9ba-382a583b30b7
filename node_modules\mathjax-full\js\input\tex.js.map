{"version": 3, "file": "tex.js", "sourceRoot": "", "sources": ["../../ts/input/tex.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,mDAAqD;AACrD,iDAA4E;AAM5E,+CAAyC;AAEzC,sEAA6C;AAC7C,kEAAyC;AACzC,oEAA2C;AAC3C,kEAAyC;AACzC,0EAAiD;AACjD,yCAA0C;AAC1C,2DAA2D;AAE3D,2CAAyC;AAazC;IAAkC,uBAAyB;IA6EzD,aAAY,OAAwB;QAAxB,wBAAA,EAAA,YAAwB;QAApC,iBAiBC;QAhBO,IAAA,KAAA,OAAoB,IAAA,4BAAe,EAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,oBAAO,CAAC,OAAO,CAAC,IAAA,EAAzE,IAAI,QAAA,EAAE,GAAG,QAAA,EAAE,IAAI,QAA0D,CAAC;gBACjF,kBAAM,GAAG,CAAC;QACV,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,oBAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACvC,IAAM,aAAa,GAAG,KAAI,CAAC,aAAa,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAM,YAAY,GAAG,KAAI,CAAC,aAAa;YACrC,IAAI,yBAAY,CAAC,aAAa,EAAE,CAAC,KAAI,CAAC,OAAO,EAAE,qBAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACvE,IAAA,wBAAW,EAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxC,aAAa,CAAC,MAAM,CAAC,KAAI,CAAC,CAAC;QAC3B,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QACtC,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;QACjD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC;QACrD,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,uBAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;;IACxD,CAAC;IAzCgB,aAAS,GAA1B,UAA2B,QAAuC;QAChE,IAAI,aAAa,GAAG,IAAI,sCAAmB,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/D,aAAa,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO,aAAa,CAAC;IACvB,CAAC;IASgB,QAAI,GAArB,UAAsB,OAAqB,EAAE,aAAkC;QAC7E,qBAAW,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACxC,qBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,GAAG,qBAAW,CAAC,UAAU,EAAE,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IACvC,CAAC;IA4BM,2BAAa,GAApB,UAAqB,UAAsB;QACzC,iBAAM,aAAa,YAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;IAMD,sBAAW,6BAAY;aAAvB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAKM,mBAAK,GAAZ,UAAa,GAAe;QAAf,oBAAA,EAAA,OAAe;QAC1B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAMM,qBAAO,GAAd,UAAe,IAAuB,EAAE,QAA+B;QACrE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACxE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,IAAa,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,SAAS,CAAC;QACd,IAAI;YACF,IAAI,MAAM,GAAG,IAAI,sBAAS,CAAC,IAAI,CAAC,KAAK,EACV,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,EAClC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,IAAI,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACpB,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;SACjC;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,CAAC,GAAG,YAAY,qBAAQ,CAAC,EAAE;gBAC9B,MAAM,GAAG,CAAC;aACX;YACD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;YAC/B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC5C;QACD,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACpE,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,WAAW,EAAE;YAC1B,qBAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;SACnE;QACD,IAAI,OAAO,EAAE;YACX,qBAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACzE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QACvC,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAMM,sBAAQ,GAAf,UAAgB,OAAiB;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAQM,yBAAW,GAAlB,UAAmB,GAAa;QAC9B,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CACzC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IA1Ka,QAAI,GAAW,KAAK,CAAC;IAMrB,WAAO,yBAChB,8BAAgB,CAAC,OAAO,KAC3B,OAAO,EAAE,IAAI,EACb,QAAQ,EAAE,CAAC,MAAM,CAAC,EAElB,MAAM,EAAE,qDAAqD,EAE7D,SAAS,EAAE,CAAC,GAAG,IAAI,EACnB,WAAW,EAAE,UAAC,GAAuB,EAAE,GAAa,IAAK,OAAA,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAApB,CAAoB,IAC7E;IA6JJ,UAAC;CAAA,AAlLD,CAAkC,8BAAgB,GAkLjD;AAlLY,kBAAG"}