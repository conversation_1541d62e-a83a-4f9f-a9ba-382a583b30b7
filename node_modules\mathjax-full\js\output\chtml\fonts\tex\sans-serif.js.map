{"version": 3, "file": "sans-serif.js", "sourceRoot": "", "sources": ["../../../../../ts/output/chtml/fonts/tex/sans-serif.ts"], "names": [], "mappings": ";;;AAgBA,iDAAuD;AACvD,yEAA0E;AAE7D,QAAA,SAAS,GAAiB,IAAA,oBAAM,EAAC,yBAAI,EAAE;IAChD,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;I<PERSON><PERSON>,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IAC<PERSON>,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,KAAK,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IACf,MAAM,EAAE,EAAC,CAAC,EAAE,QAAQ,EAAC;IACrB,MAAM,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IAChB,MAAM,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC;IAChB,MAAM,EAAE,EAAC,CAAC,EAAE,OAAO,EAAC;CACvB,CAAC,CAAC"}