{"version": 3, "file": "TexParser.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/TexParser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,gEAAuC;AAEvC,wDAA+B;AAG/B,8DAAqC;AACrC,4DAAuE;AAWvE;IAiCE,mBAAoB,OAAe,EAAE,GAAY,EAAS,aAA2B;;QAAjE,YAAO,GAAP,OAAO,CAAQ;QAAuB,kBAAa,GAAb,aAAa,CAAc;QA3B9E,eAAU,GAAW,CAAC,CAAC;QAYvB,MAAC,GAAW,CAAC,CAAC;QAMd,cAAS,GAAW,EAAE,CAAC;QAU5B,IAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAY,CAAC;QAC1C,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC;QACtB,IAAI,GAAY,CAAC;QACjB,IAAI,GAAG,EAAE;YACP,GAAG,GAAG,EAAE,CAAC;;gBACT,KAAiB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;oBAA9B,IAAM,EAAE,WAAA;oBACX,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;iBACnB;;;;;;;;;SACF;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,kBAAK,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7C,CAAC;IAKD,sBAAI,8BAAO;aAAX;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACpC,CAAC;;;OAAA;IAKD,sBAAI,kCAAW;aAAf;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QACxC,CAAC;;;OAAA;IAKD,sBAAI,2BAAI;aAAR;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACjC,CAAC;;;OAAA;IAMD,sBAAI,6BAAM;aAOV;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;aATD,UAAW,GAAW;YACpB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QACrB,CAAC;;;OAAA;IAgBM,yBAAK,GAAZ,UAAa,IAAiB,EAAE,KAAiB;QAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IASM,0BAAM,GAAb,UAAc,IAAiB,EAAE,MAAc;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IAWM,4BAAQ,GAAf,UAAgB,IAAiB,EAAE,MAAc;QAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;IAMM,4BAAQ,GAAf;;QACE,IAAI,GAAG,GAAG,EAAE,CAAC;;YACb,KAAqB,IAAA,KAAA,SAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA,gBAAA,4BAAE;gBAAhE,IAAM,MAAM,WAAA;gBACf,GAAG,IAAI,MAAM,GAAG,IAAI;oBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAqB,CAAC,GAAG,IAAI,CAAC;aACjE;;;;;;;;;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAMM,yBAAK,GAAZ;QACE,IAAI,CAAS,CAAC;QACd,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SACpC;IACH,CAAC;IAQM,wBAAI,GAAX,UAAY,GAAwB;QAClC,IAAI,GAAG,YAAY,4BAAe,IAAI,GAAG,CAAC,UAAU,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACtB;IACH,CAAC;IAOM,2BAAO,GAAd,UAAe,IAA6B;;;YAC1C,KAAkB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAAnB,IAAM,GAAG,iBAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACtB;;;;;;;;;IACH,CAAC;IAMM,uBAAG,GAAV;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAYM,oCAAgB,GAAvB,UAAwB,CAAS;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAW,CAAC;QACrD,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC,CAAC;IAKM,gCAAY,GAAnB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAKM,+BAAW,GAAlB;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAKM,2BAAO,GAAd;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,EAAE;YACzB,IAAI,CAAC,CAAC,EAAE,CAAC;SACV;QACD,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IAC7B,CAAC;IAKM,yBAAK,GAAZ;QACE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAC9E,IAAI,EAAE,EAAE;YACN,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACvB,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SACvB;aAAM;YACL,IAAI,CAAC,CAAC,EAAE,CAAC;YACT,OAAO,GAAG,CAAC;SACZ;IACH,CAAC;IASM,+BAAW,GAAlB,UAAmB,KAAa,EAAE,MAAgB;QAChD,QAAQ,IAAI,CAAC,OAAO,EAAE,EAAE;YACxB,KAAK,EAAE;gBACL,IAAI,CAAC,MAAM,EAAE;oBAEX,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;iBAChF;gBACD,OAAO,IAAI,CAAC;YACd,KAAK,GAAG;gBACN,IAAI,CAAC,MAAM,EAAE;oBAEX,MAAM,IAAI,qBAAQ,CAAC,uBAAuB,EACtB,yCAAyC,CAAC,CAAC;iBAChE;gBACD,OAAO,IAAI,CAAC;YACd,KAAK,IAAI;gBACP,IAAI,CAAC,CAAC,EAAE,CAAC;gBACT,OAAO,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC7B,KAAK,GAAG;gBACN,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;gBAC7B,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAClC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;wBACtC,KAAK,IAAI;4BAAG,IAAI,CAAC,CAAC,EAAE,CAAC;4BAAC,MAAM;wBAC5B,KAAK,GAAG;4BAAI,MAAM,EAAE,CAAC;4BAAC,MAAM;wBAC5B,KAAK,GAAG;4BACN,IAAI,EAAE,MAAM,KAAK,CAAC,EAAE;gCAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;6BACzC;4BACD,MAAM;qBACP;iBACF;gBAED,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;SAChE;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9B,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QACnB,OAAO,CAAC,CAAC;IACX,CAAC;IASM,+BAAW,GAAlB,UAAmB,KAAa,EAAE,GAAY;QAC5C,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;YAC1B,OAAO,GAAG,CAAC;SACZ;QACD,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;gBACtC,KAAK,GAAG;oBAAI,MAAM,EAAE,CAAC;oBAAC,MAAM;gBAC5B,KAAK,IAAI;oBAAG,IAAI,CAAC,CAAC,EAAE,CAAC;oBAAC,MAAM;gBAC5B,KAAK,GAAG;oBACN,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;wBAEjB,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAClB,wCAAwC,EAAE,OAAO,CAAC,CAAC;qBACxE;oBACD,MAAM;gBACR,KAAK,GAAG;oBACN,IAAI,MAAM,KAAK,CAAC,EAAE;wBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qBACzC;oBACD,MAAM;aACP;SACF;QAED,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACpB,iDAAiD,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACzF,CAAC;IAQM,gCAAY,GAAnB,UAAoB,IAAY,EAAE,OAAiB;QACjD,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAC3C,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;aACnB;iBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,EAAE;gBAC/B,IAAI,CAAC,CAAC,EAAE,CAAC;gBACT,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;gBACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;aACjC;SACF;QAED,MAAM,IAAI,qBAAQ,CAAC,4BAA4B,EAC3B,0CAA0C,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAClF,CAAC;IAOM,4BAAQ,GAAf,UAAgB,IAAY;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;YAC1B,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAA,KAAA,OAAgB,sBAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAA,EAA1C,KAAK,QAAA,EAAE,IAAI,QAA+B,CAAC;YAChD,IAAI,KAAK,EAAE;gBAET,OAAO,KAAK,GAAG,IAAI,CAAC;aACrB;SACF;aAAM;YAEL,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,IAAA,KAAA,OAAwB,sBAAS,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,IAAA,EAAxD,KAAK,QAAA,EAAE,IAAI,QAAA,EAAE,QAAM,QAAqC,CAAC;YAC9D,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,CAAC,IAAI,QAAM,CAAC;gBACjB,OAAO,KAAK,GAAG,IAAI,CAAC;aACrB;SACF;QAED,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAClB,uCAAuC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/E,CAAC;IAQM,2BAAO,GAAd,UAAe,KAAa,EAAE,KAAa;QACzC,OAAO,IAAI,CAAC,WAAW,EAAE,EAAE;YACzB,IAAI,CAAC,CAAC,EAAE,CAAC;SACV;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACf,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC3C,QAAQ,CAAC,EAAE;gBACX,KAAK,IAAI;oBAAG,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAAC,MAAM;gBACrC,KAAK,GAAG;oBAAI,MAAM,EAAE,CAAC;oBAAC,MAAM;gBAC5B,KAAK,GAAG;oBACN,IAAI,MAAM,KAAK,CAAC,EAAE;wBAEhB,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAClB,wCAAwC,EAAE,KAAK,CAAC,CAAC;qBACtE;oBACD,MAAM,EAAE,CAAC;oBACT,MAAM;aACP;YACD,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAChC;SACF;QAED,MAAM,IAAI,qBAAQ,CAAC,yBAAyB,EACxB,0BAA0B,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACzE,CAAC;IAOM,4BAAQ,GAAf,UAAgB,IAAY;QAC1B,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EACtC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC;IAQM,6BAAS,GAAhB,UAAiB,IAAY,EAAE,KAAa;QAC1C,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EACzC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC;IAQM,mCAAe,GAAtB,UAAuB,IAAY;QACjC,IAAI,CAAC,GAAG,sBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE;YACjC,OAAO,CAAC,CAAC;SACV;QAED,MAAM,IAAI,qBAAQ,CAAC,4BAA4B,EAC3B,0CAA0C,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAClF,CAAC;IAKM,2BAAO,GAAd;QACE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,CAAC,EAAE,CAAC;SACV;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAUM,0BAAM,GAAb,UAAc,IAAY;;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QACxC,OAAO,CAAA,KAAA,IAAI,CAAC,aAAa,CAAC,WAAW,CAAA,CAAC,MAAM,0BAAC,IAAI,UAAK,IAAI,WAAE;IAC9D,CAAC;IAGH,gBAAC;AAAD,CAAC,AAvdD,IAudC"}