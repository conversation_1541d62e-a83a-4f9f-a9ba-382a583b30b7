{"version": 3, "file": "BBox.js", "sourceRoot": "", "sources": ["../../ts/util/BBox.ts"], "names": [], "mappings": ";;;AAuBA,2CAAsC;AAgBtC;IAwDE,cAAY,GAAkD;QAAlD,oBAAA,EAAA,QAAiB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,qBAAQ,EAAE,CAAC,EAAE,CAAC,qBAAQ,EAAC;QAC5D,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAvBa,SAAI,GAAlB;QACE,OAAO,IAAI,IAAI,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC;IACtC,CAAC;IAKa,UAAK,GAAnB;QACE,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAoBM,oBAAK,GAAZ;QACE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,qBAAQ,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,oBAAK,GAAZ;QACE,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,qBAAQ;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,qBAAQ;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,qBAAQ;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAKM,sBAAO,GAAd,UAAe,KAAa;QAC1B,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;QAChB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;QAChB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;IAClB,CAAC;IAOM,sBAAO,GAAd,UAAe,IAAU,EAAE,CAAa,EAAE,CAAa;QAA5B,kBAAA,EAAA,KAAa;QAAE,kBAAA,EAAA,KAAa;QACrD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAKM,qBAAM,GAAb,UAAc,IAAU;QACtB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;SACzB;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;SACzB;IACH,CAAC;IAKM,yBAAU,GAAjB,UAAkB,IAAU;QAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SAC3B;IACH,CAAC;IAhIa,cAAS,GAAG,MAAM,CAAC;IAKnB,gBAAW,GAAgC;QACvD,CAAC,gBAAgB,EAAE,GAAG,CAAC;QACvB,CAAC,kBAAkB,EAAE,GAAG,CAAC;QACzB,CAAC,mBAAmB,EAAE,GAAG,CAAC;QAC1B,CAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;QAC3B,CAAC,YAAY,EAAE,GAAG,CAAC;QACnB,CAAC,cAAc,EAAE,GAAG,CAAC;QACrB,CAAC,eAAe,EAAE,GAAG,CAAC;QACtB,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC;KACxB,CAAC;IAoHJ,WAAC;CAAA,AAtID,IAsIC;AAtIY,oBAAI"}