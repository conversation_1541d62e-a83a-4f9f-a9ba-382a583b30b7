<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// AI助手功能列表
const aiFeatures = ref([
  { id: 1, title: '数理辅导', icon: '📚' },
  { id: 2, title: '旅游定制', icon: '🌍' },
  { id: 3, title: '电影推荐', icon: '🎬' },
  { id: 4, title: '短视频推荐', icon: '📱' },
  { id: 5, title: '音乐推荐', icon: '🎵' },
  { id: 6, title: '创漫空间', icon: '🎨' },
  { id: 7, title: '音乐定制', icon: '🎹' },
  { id: 8, title: '新闻推荐', icon: '📰' }
])

// 跳转到聊天页面
const navigateToChat = (id: number) => {
  router.push(`/chat/${id}`)
}
</script>

<template>
  <div class="home-page">
    <h1 class="page-title">AI助手功能</h1>
    
    <div class="features-grid">
      <div 
        v-for="feature in aiFeatures" 
        :key="feature.id"
        class="feature-card"
        @click="navigateToChat(feature.id)"
      >
        <div class="feature-icon">{{ feature.icon }}</div>
        <div class="feature-title">{{ feature.title }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f8f9fa;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 40px;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 800px;
  width: 100%;
}

.feature-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.feature-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-title {
  font-weight: 500;
  color: #111827;
  font-size: 16px;
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style> 