{"version": 3, "file": "math.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/math.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAmE;AAOnE;IAA6B,2BAAqB;IAAlD;;IAwEA,CAAC;IAhCC,sBAAW,yBAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAW,uCAAkB;aAA7B;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IASS,6CAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;YAC7C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAClD;QACD,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACzF,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;QACjG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACtD,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,WAA8B,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAW,CAAC;QACjF,iBAAM,2BAA2B,YAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAjEa,gBAAQ,yBACjB,kCAAqB,CAAC,QAAQ,KACjC,WAAW,EAAE,QAAQ,EACrB,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,EAAE,EACb,cAAc,EAAE,aAAa,EAC7B,GAAG,EAAE,KAAK,EACV,WAAW,EAAE,CAAC,EACd,YAAY,EAAE,KAAK,EACnB,OAAO,EAAE,QAAQ,EACjB,QAAQ,EAAE,EAAE,EACZ,QAAQ,EAAE,WAAW,EACrB,MAAM,EAAE,EAAE,EACV,cAAc,EAAE,EAAE,EAClB,eAAe,EAAE,EAAE,EACnB,eAAe,EAAE,EAAE,EACnB,OAAO,EAAE,EAAE,EACX,OAAO,EAAE,EAAE,EACX,oBAAoB,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EACtC,aAAa,EAAE,KAAK,EACpB,mBAAmB,EAAE,QAAQ,EAC7B,WAAW,EAAE,KAAK,EAClB,iBAAiB,EAAE,QAAQ,EAC3B,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EACnB,YAAY,EAAE,EAAE,EAChB,gBAAgB,EAAE,aAAa,EAC/B,gBAAgB,EAAE,aAAa,EAC/B,eAAe,EAAG,aAAa,EAC/B,eAAe,EAAG,aAAa,IAC/B;IAqCJ,cAAC;CAAA,AAxED,CAA6B,kCAAqB,GAwEjD;AAxEY,0BAAO"}