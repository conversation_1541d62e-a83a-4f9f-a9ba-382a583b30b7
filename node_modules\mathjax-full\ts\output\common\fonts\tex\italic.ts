/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import {CharMap, CharOptions} from '../../FontData.js';

export const italic: CharMap<CharOptions> = {
    0x21: [.716, 0, .307, {ic: .073}],
    0x22: [.694, -0.379, .514, {ic: .024}],
    0x23: [.694, .194, .818, {ic: .01}],
    0x25: [.75, .056, .818, {ic: .029}],
    0x26: [.716, .022, .767, {ic: .035}],
    0x27: [.694, -0.379, .307, {ic: .07}],
    0x28: [.75, .25, .409, {ic: .108}],
    0x29: [.75, .25, .409],
    0x2A: [.75, -0.32, .511, {ic: .073}],
    0x2B: [.557, .057, .767],
    0x2C: [.121, .194, .307],
    0x2D: [.251, -0.18, .358],
    0x2E: [.121, 0, .307],
    0x2F: [.716, .215, .778],
    0x30: [.665, .021, .511, {ic: .051}],
    0x31: [.666, 0, .511],
    0x32: [.666, .022, .511, {ic: .04}],
    0x33: [.666, .022, .511, {ic: .051}],
    0x34: [.666, .194, .511],
    0x35: [.666, .022, .511, {ic: .056}],
    0x36: [.665, .022, .511, {ic: .054}],
    0x37: [.666, .022, .511, {ic: .123}],
    0x38: [.666, .021, .511, {ic: .042}],
    0x39: [.666, .022, .511, {ic: .042}],
    0x3A: [.431, 0, .307],
    0x3B: [.431, .194, .307],
    0x3D: [.367, -0.133, .767],
    0x3F: [.716, 0, .511, {ic: .04}],
    0x40: [.705, .011, .767, {ic: .022}],
    0x5B: [.75, .25, .307, {ic: .139}],
    0x5D: [.75, .25, .307, {ic: .052}],
    0x5E: [.694, -0.527, .511, {ic: .017}],
    0x5F: [-0.025, .062, .511, {ic: .043}],
    0x7E: [.318, -0.208, .511, {ic: .06}],
    0x131: [.441, .01, .307, {ic: .033}],
    0x237: [.442, .204, .332],
    0x300: [.697, -0.5, 0],
    0x301: [.697, -0.5, 0, {ic: .039}],
    0x302: [.694, -0.527, 0, {ic: .017}],
    0x303: [.668, -0.558, 0, {ic: .06}],
    0x304: [.589, -0.544, 0, {ic: .054}],
    0x306: [.694, -0.515, 0, {ic: .062}],
    0x307: [.669, -0.548, 0],
    0x308: [.669, -0.554, 0, {ic: .045}],
    0x30A: [.716, -0.542, 0],
    0x30B: [.697, -0.503, 0, {ic: .065}],
    0x30C: [.638, -0.502, 0, {ic: .029}],
    0x3DD: [.605, .085, .778],
    0x2013: [.285, -0.248, .511, {ic: .043}],
    0x2014: [.285, -0.248, 1.022, {ic: .016}],
    0x2015: [.285, -0.248, 1.022, {ic: .016}],
    0x2017: [-0.025, .062, .511, {ic: .043}],
    0x2018: [.694, -0.379, .307, {ic: .055}],
    0x2019: [.694, -0.379, .307, {ic: .07}],
    0x201C: [.694, -0.379, .514, {ic: .092}],
    0x201D: [.694, -0.379, .514, {ic: .024}],
    0x2044: [.716, .215, .778],
    0x210F: [.695, .013, .54, {ic: .022}],
    0x2206: [.716, 0, .833, {sk: .167}],
    0x29F8: [.716, .215, .778],
};
