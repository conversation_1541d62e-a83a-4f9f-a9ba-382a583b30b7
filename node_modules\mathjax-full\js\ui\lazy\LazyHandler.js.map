{"version": 3, "file": "LazyHandler.js", "sourceRoot": "", "sources": ["../../../ts/ui/lazy/LazyHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,sDAAiE;AAIjE,oDAAuD;AA6BvD;IAAA;QAKY,OAAE,GAAW,CAAC,CAAC;QAKf,UAAK,GAAuC,IAAI,GAAG,EAAE,CAAC;IAiClE,CAAC;IAzBQ,sBAAG,GAAV,UAAW,IAA2B;QACpC,IAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAG,IAAI,CAAC,CAAC;QAC1B,OAAO,EAAE,CAAC;IACZ,CAAC;IAQM,sBAAG,GAAV,UAAW,EAAU;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAOM,yBAAM,GAAb,UAAc,EAAU;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEH,eAAC;AAAD,CAAC,AA3CD,IA2CC;AA3CY,4BAAQ;AA+CrB,IAAA,sBAAQ,EAAC,YAAY,EAAE,mBAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAK9B,QAAA,MAAM,GAAG,eAAe,CAAC;AA4CtC,SAAgB,iBAAiB,CAC/B,YAAe;IAGf;QAAqB,2BAAY;QA4B/B;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBASd;YA/BM,iBAAW,GAAY,IAAI,CAAC;YAM5B,iBAAW,GAAY,IAAI,CAAC;YAU5B,aAAO,GAAY,KAAK,CAAC;YAO9B,IAAI,CAAC,KAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBAMlB,KAAI,CAAC,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;aAC7C;;QACH,CAAC;QAUM,yBAAO,GAAd,UAAe,QAAmC;YAChD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,iBAAM,OAAO,YAAC,QAAQ,CAAC,CAAC;gBACxB,OAAO;aACR;YACD,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,mBAAK,CAAC,QAAQ,EAAE;gBACjC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;gBAC9C,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,CAAC,CAAC;aAC5B;QACH,CAAC;QAMM,uBAAK,GAAZ,UAAa,KAAoB,EAAE,OAAwB;YAA9C,sBAAA,EAAA,YAAoB;YAAE,wBAAA,EAAA,eAAwB;YACzD,IAAI,OAAO,KAAK,IAAI;gBAAE,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClD,OAAO,iBAAM,KAAK,WAAE,CAAC;QACvB,CAAC;QASM,yBAAO,GAAd,UAAe,QAAmC;;YAChD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,iBAAM,OAAO,YAAC,QAAQ,CAAC,CAAC;gBACxB,OAAO;aACR;YACD,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,mBAAK,CAAC,OAAO,EAAE;gBAChC,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACpB,IAAM,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACvC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,YAAG,GAAC,cAAM,IAAG,EAAE,MAAE,CAAC;oBAC3D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;iBACzE;gBACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,OAAO,CAAC,CAAC;aAC3B;QACH,CAAC;QAQM,gCAAc,GAArB,UAAsB,QAAmC;YACvD,iBAAM,cAAc,YAAC,QAAQ,CAAC,CAAC;YAC/B,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAA4B,CAAC,CAAC;aAClE;QACH,CAAC;QAEH,cAAC;IAAD,CAAC,AAzGM,CAAc,YAAY,GAyG/B;AAEJ,CAAC;AA/GD,8CA+GC;AAmDD,SAAgB,qBAAqB,CAEnC,YAAe;;IAGf;YAA+B,6BAAY;YAkEzC;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAgCd;gBAvEM,0BAAoB,GAAQ,IAAI,CAAC;gBAKjC,qBAAe,GAAW,CAAC,CAAC;gBAKzB,iBAAW,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;gBAW/C,cAAQ,GAAY,KAAK,CAAC;gBAK1B,aAAO,GAAY,IAAI,GAAG,EAAE,CAAC;gBAiBrC,KAAI,CAAC,OAAO,CAAC,QAAQ;oBACnB,iBAAiB,CAA8C,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAIxF,IAAM,WAAW,GAAI,KAAI,CAAC,WAAmC,CAAC,WAAW,CAAC;gBAC1E,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAIrE,KAAI,CAAC,YAAY,GAAG,IAAI,oBAAoB,CAAC,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,EAAE,EAAC,UAAU,EAAE,KAAI,CAAC,OAAO,CAAC,UAAU,EAAC,CAAC,CAAC;gBACjH,KAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAW,CAAC;gBACxC,IAAM,QAAQ,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;gBAC/C,KAAI,CAAC,cAAc,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;oBACtC,cAAM,OAAA,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAApC,CAAoC,CAAC,CAAC;oBAC5C,cAAM,OAAA,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAxB,CAAwB,CAAC,CAAC;gBAIvD,IAAI,MAAM,EAAE;oBACV,IAAI,MAAI,GAAG,KAAK,CAAC;oBACjB,IAAM,OAAO,GAAG;wBACd,CAAC,MAAI,IAAI,KAAI,CAAC,cAAc,EAAE,CAAC;wBAC/B,MAAI,GAAG,IAAI,CAAC;oBACd,CAAC,CAAC;oBACF,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAChD,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;iBACjD;;YACH,CAAC;YAKM,8BAAU,GAAjB;;gBACE,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC;oBAAE,OAAO;;oBAC7E,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;wBAAzB,IAAM,IAAI,WAAA;wBACb,IAAM,IAAI,GAAG,IAA6B,CAAC;wBAC3C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;4BAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;yBAC7C;qBACF;;;;;;;;;gBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACnC,CAAC;YAUS,gCAAY,GAAtB,UAAuB,IAA2B;gBAChD,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,mBAAK,CAAC,UAAU,EAAE;oBACnC,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,UAAU,CAAC,CAAC;oBAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;oBACnC,IAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBAC7C,GAAG;wBACD,IAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAClE,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC;4BAAE,OAAO,IAAI,CAAC;wBACnD,IAAI,EAAE,IAAI,CAAC,eAAe,IAAI,GAAG,EAAE;4BACjC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;yBAC1B;qBACF,QAAQ,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;iBAC1C;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAKM,yBAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;gBAAxB,wBAAA,EAAA,eAAwB;gBAClD,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,GAAG,mBAAK,CAAC,UAAU,EAAE;oBAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;iBACpC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAOY,kCAAc,GAA3B;;;;;;wBAIM,KAAK,GAAG,mBAAK,CAAC,IAAI,CAAC;;4BAIvB,KAAmB,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,4CAAE;gCAAnB,IAAI;gCACP,IAAI,GAAG,IAA6B,CAAC;gCAI3C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW;oCAAE,SAAS;gCAIrD,IAAI,IAAI,CAAC,WAAW,EAAE;oCACpB,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;oCAC/B,KAAK,GAAG,mBAAK,CAAC,QAAQ,CAAC;iCACxB;qCAAM;oCACL,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;oCAC9B,IAAI,mBAAK,CAAC,OAAO,GAAG,KAAK;wCAAE,KAAK,GAAG,mBAAK,CAAC,OAAO,CAAC;iCAClD;gCAID,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gCAC5C,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,UAA4B,CAAC,CAAC;6BACnF;;;;;;;;;wBAID,IAAI,KAAK,KAAK,mBAAK,CAAC,IAAI;4BAAE,WAAO,OAAO,CAAC,OAAO,EAAE,EAAC;wBAInD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;wBAMtB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC;wBACnD,IAAI,SAAS;4BAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC;wBAIzD,IAAI,CAAC,KAAK,EAAE,CAAC;wBACb,WAAO,IAAA,6BAAgB,EAAC,cAAM,OAAA,KAAI,CAAC,MAAM,EAAE,EAAb,CAAa,CAAC,CAAC,IAAI,CAAC;gCAChD,IAAI,SAAS;oCAAE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;4BAC9D,CAAC,CAAC,EAAC;;;aACJ;YAyBS,+BAAW,GAArB,UAAsB,OAAoC;;;oBACxD,KAAoB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;wBAAxB,IAAM,KAAK,oBAAA;wBACd,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,MAAkB,EAAE,cAAM,CAAC,CAAC;wBACvE,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACnC,IAAI,CAAC,IAAI;4BAAE,SAAS;wBACpB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;4BACzB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;4BACxB,SAAS;yBACV;wBACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;4BACrB,IAAI,CAAC,cAAc,EAAE,CAAC;yBACvB;qBACF;;;;;;;;;YACH,CAAC;YAQS,iCAAa,GAAvB;gBAAA,iBAYC;gBAXC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;gBACzB,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;oBACvC,IAAI,KAAK,GAAG,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAK,CAAC,OAAO,CAAC;oBAC3E,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;oBACrC,KAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC5B,OAAO,IAAA,6BAAgB,EAAC;wBACtB,KAAI,CAAC,MAAM,EAAE,CAAC;wBACd,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACxB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;YAWS,+BAAW,GAArB,UAAsB,GAAY,EAAE,KAAa;;;oBAC/C,KAAiB,IAAA,KAAA,SAAA,GAAG,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;wBAA1B,IAAM,EAAE,WAAA;wBACX,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACnC,IAAI,IAAI,CAAC,WAAW,EAAE;4BACpB,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;4BAC/B,KAAK,GAAG,mBAAK,CAAC,QAAQ,CAAC;yBACxB;6BAAM;4BACL,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;yBAC/B;wBACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;wBAC5C,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,UAA4B,CAAC,CAAC;qBACnF;;;;;;;;;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAQS,uCAAmB,GAA7B,UAA8B,GAAY;;gBACxC,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,IAAI;oBAAE,OAAO,KAAK,CAAC;gBACxB,IAAI,OAAO,GAAG,KAAK,CAAC;;oBACpB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;wBAAzB,IAAM,IAAI,WAAA;wBACb,IAAM,OAAO,GAAG,IAA6B,CAAC;wBAC9C,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;4BACjE,MAAM;yBACP;wBACD,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;wBAC5B,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,UAA4B,CAAC,CAAC;wBACxF,OAAO,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;wBAClC,OAAO,GAAG,IAAI,CAAC;qBAChB;;;;;;;;;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC;YAQS,+BAAW,GAArB,UAAsB,GAAY;;gBAChC,IAAI,GAAG,GAAW,IAAI,CAAC;gBACvB,IAAI,OAAO,GAAG,IAAI,CAAC;;oBACnB,KAAiB,IAAA,KAAA,SAAA,GAAG,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;wBAA1B,IAAM,EAAE,WAAA;wBACX,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACnC,IAAI,CAAC,IAAI,CAAC,OAAO;4BAAE,SAAS;wBAC5B,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE;4BACtC,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;4BACnB,OAAO,GAAG,IAAI,CAAC;yBAChB;qBACF;;;;;;;;;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC;YAOM,wCAAoB,GAA3B,UAA4B,UAA4B;;gBACtD,IAAM,KAAK,GAAG,iBAAM,oBAAoB,YAAC,UAAU,CAA4B,CAAC;;oBAChF,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;wBAArB,IAAM,IAAI,kBAAA;wBACb,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;wBAC/B,IAAI,MAAM,EAAE;4BACV,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAwB,CAAC,CAAC;4BACtD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,cAAM,CAAC,CAAC,CAAC;yBACjE;qBACF;;;;;;;;;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAKM,0BAAM,GAAb;gBAIE,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAC9C,IAAI,CAAC,oBAAoB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrF,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;gBACzB,iBAAM,MAAM,WAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YAEH,gBAAC;QAAD,CAAC,AAlXM,CAAwB,YAAY;QAK3B,UAAO,yBAChB,YAAY,CAAC,OAAO,KACvB,UAAU,EAAE,OAAO,EACnB,iBAAiB,EAAE,IAAI,EACvB,aAAa,wBACR,YAAY,CAAC,OAAO,CAAC,aAAa,KACrC,UAAU,EAAE,CAAC,mBAAK,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,KAAK,CAAC,MAEzD;WAqWF;AAEJ,CAAC;AAzXD,sDAyXC;AAcD,SAAgB,WAAW,CAAU,OAA6B;IAIhE,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;QAC/C,OAAO,CAAC,aAAa;YACnB,qBAAqB,CACnB,OAAO,CAAC,aAAa,CACC,CAAC;KAC5B;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAXD,kCAWC"}