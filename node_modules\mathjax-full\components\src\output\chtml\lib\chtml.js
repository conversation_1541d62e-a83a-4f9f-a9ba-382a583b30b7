import {combineWithMathJax} from '../../../../../js/components/global.js';
import {VERSION} from '../../../../../js/components/version.js';

import * as module1 from '../../../../../js/output/chtml.js';
import * as module2 from '../../../../../js/output/chtml/FontData.js';
import * as module3 from '../../../../../js/output/chtml/Notation.js';
import * as module4 from '../../../../../js/output/chtml/Usage.js';
import * as module5 from '../../../../../js/output/chtml/Wrapper.js';
import * as module6 from '../../../../../js/output/chtml/WrapperFactory.js';
import * as module7 from '../../../../../js/output/chtml/Wrappers.js';
import * as module8 from '../../../../../js/output/chtml/Wrappers/TeXAtom.js';
import * as module9 from '../../../../../js/output/chtml/Wrappers/TextNode.js';
import * as module10 from '../../../../../js/output/chtml/Wrappers/maction.js';
import * as module11 from '../../../../../js/output/chtml/Wrappers/math.js';
import * as module12 from '../../../../../js/output/chtml/Wrappers/menclose.js';
import * as module13 from '../../../../../js/output/chtml/Wrappers/mfenced.js';
import * as module14 from '../../../../../js/output/chtml/Wrappers/mfrac.js';
import * as module15 from '../../../../../js/output/chtml/Wrappers/mglyph.js';
import * as module16 from '../../../../../js/output/chtml/Wrappers/mi.js';
import * as module17 from '../../../../../js/output/chtml/Wrappers/mmultiscripts.js';
import * as module18 from '../../../../../js/output/chtml/Wrappers/mn.js';
import * as module19 from '../../../../../js/output/chtml/Wrappers/mo.js';
import * as module20 from '../../../../../js/output/chtml/Wrappers/mpadded.js';
import * as module21 from '../../../../../js/output/chtml/Wrappers/mroot.js';
import * as module22 from '../../../../../js/output/chtml/Wrappers/mrow.js';
import * as module23 from '../../../../../js/output/chtml/Wrappers/ms.js';
import * as module24 from '../../../../../js/output/chtml/Wrappers/mspace.js';
import * as module25 from '../../../../../js/output/chtml/Wrappers/msqrt.js';
import * as module26 from '../../../../../js/output/chtml/Wrappers/msubsup.js';
import * as module27 from '../../../../../js/output/chtml/Wrappers/mtable.js';
import * as module28 from '../../../../../js/output/chtml/Wrappers/mtd.js';
import * as module29 from '../../../../../js/output/chtml/Wrappers/mtext.js';
import * as module30 from '../../../../../js/output/chtml/Wrappers/mtr.js';
import * as module31 from '../../../../../js/output/chtml/Wrappers/munderover.js';
import * as module32 from '../../../../../js/output/chtml/Wrappers/scriptbase.js';
import * as module33 from '../../../../../js/output/chtml/Wrappers/semantics.js';
import * as module34 from '../../../../../js/output/common/FontData.js';
import * as module35 from '../../../../../js/output/common/Notation.js';
import * as module36 from '../../../../../js/output/common/OutputJax.js';
import * as module37 from '../../../../../js/output/common/Wrapper.js';
import * as module38 from '../../../../../js/output/common/WrapperFactory.js';
import * as module39 from '../../../../../js/output/common/Wrappers/TeXAtom.js';
import * as module40 from '../../../../../js/output/common/Wrappers/TextNode.js';
import * as module41 from '../../../../../js/output/common/Wrappers/maction.js';
import * as module42 from '../../../../../js/output/common/Wrappers/math.js';
import * as module43 from '../../../../../js/output/common/Wrappers/menclose.js';
import * as module44 from '../../../../../js/output/common/Wrappers/mfenced.js';
import * as module45 from '../../../../../js/output/common/Wrappers/mfrac.js';
import * as module46 from '../../../../../js/output/common/Wrappers/mglyph.js';
import * as module47 from '../../../../../js/output/common/Wrappers/mi.js';
import * as module48 from '../../../../../js/output/common/Wrappers/mmultiscripts.js';
import * as module49 from '../../../../../js/output/common/Wrappers/mn.js';
import * as module50 from '../../../../../js/output/common/Wrappers/mo.js';
import * as module51 from '../../../../../js/output/common/Wrappers/mpadded.js';
import * as module52 from '../../../../../js/output/common/Wrappers/mroot.js';
import * as module53 from '../../../../../js/output/common/Wrappers/mrow.js';
import * as module54 from '../../../../../js/output/common/Wrappers/ms.js';
import * as module55 from '../../../../../js/output/common/Wrappers/mspace.js';
import * as module56 from '../../../../../js/output/common/Wrappers/msqrt.js';
import * as module57 from '../../../../../js/output/common/Wrappers/msubsup.js';
import * as module58 from '../../../../../js/output/common/Wrappers/mtable.js';
import * as module59 from '../../../../../js/output/common/Wrappers/mtd.js';
import * as module60 from '../../../../../js/output/common/Wrappers/mtext.js';
import * as module61 from '../../../../../js/output/common/Wrappers/mtr.js';
import * as module62 from '../../../../../js/output/common/Wrappers/munderover.js';
import * as module63 from '../../../../../js/output/common/Wrappers/scriptbase.js';
import * as module64 from '../../../../../js/output/common/Wrappers/semantics.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('output/chtml', VERSION, 'output');
}

combineWithMathJax({_: {
  output: {
    chtml_ts: module1,
    chtml: {
      FontData: module2,
      Notation: module3,
      Usage: module4,
      Wrapper: module5,
      WrapperFactory: module6,
      Wrappers_ts: module7,
      Wrappers: {
        TeXAtom: module8,
        TextNode: module9,
        maction: module10,
        math: module11,
        menclose: module12,
        mfenced: module13,
        mfrac: module14,
        mglyph: module15,
        mi: module16,
        mmultiscripts: module17,
        mn: module18,
        mo: module19,
        mpadded: module20,
        mroot: module21,
        mrow: module22,
        ms: module23,
        mspace: module24,
        msqrt: module25,
        msubsup: module26,
        mtable: module27,
        mtd: module28,
        mtext: module29,
        mtr: module30,
        munderover: module31,
        scriptbase: module32,
        semantics: module33
      }
    },
    common: {
      FontData: module34,
      Notation: module35,
      OutputJax: module36,
      Wrapper: module37,
      WrapperFactory: module38,
      Wrappers: {
        TeXAtom: module39,
        TextNode: module40,
        maction: module41,
        math: module42,
        menclose: module43,
        mfenced: module44,
        mfrac: module45,
        mglyph: module46,
        mi: module47,
        mmultiscripts: module48,
        mn: module49,
        mo: module50,
        mpadded: module51,
        mroot: module52,
        mrow: module53,
        ms: module54,
        mspace: module55,
        msqrt: module56,
        msubsup: module57,
        mtable: module58,
        mtd: module59,
        mtext: module60,
        mtr: module61,
        munderover: module62,
        scriptbase: module63,
        semantics: module64
      }
    }
  }
}});
