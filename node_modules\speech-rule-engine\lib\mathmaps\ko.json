{"ko/messages/alphabets.min": {"kind": "alphabets", "locale": "ko", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["나블라", "알파", "베타", "감마", "델타", "엡실론", "제타", "에타", "쎄타", "이오타", "카파", "람다", "뮤", "뉴", "크시", "오미크론", "파이", "로", "시그마", "시그마", "타우", "입실론", "파이", "키", "프사이", "오메가", "편미분", "엡실론", "쎄타", "카파", "파이", "로", "파이"], "greekCap": ["알파", "베타", "감마", "델타", "엡실론", "제타", "에타", "쎄타", "이오타", "카파", "람다", "뮤", "뉴", "크시", "오미크론", "파이", "로", "쎄타", "시그마", "타우", "입실론", "파이", "키", "프사이", "오메가"], "capPrefix": {"default": "대문자", "mathspeak": "대문자", "clearspeak": "대문자"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "ko/messages/messages.min": {"kind": "messages", "locale": "ko", "messages": {"MS": {"START": "시작", "FRAC_V": "분수", "FRAC_B": "분수", "FRAC_S": "분수", "END": "끝", "FRAC_OVER": "분의", "TWICE": "이", "NEST_FRAC": "중첩", "ENDFRAC": "분수끝", "SUPER": "위", "SUB": "아래", "SUP": "위", "SUPERSCRIPT": "위첨자", "SUBSCRIPT": "아래첨자", "BASELINE": "기준", "BASE": "기준", "NESTED": "중첩", "NEST_ROOT": "중첩", "STARTROOT": "루트시작", "ENDROOT": "루트끝", "ROOTINDEX": "제곱근", "ROOT": "루트", "INDEX": "제곱근", "UNDER": "상단", "UNDERSCRIPT": "하단첨자", "OVER": "하단", "OVERSCRIPT": "상단첨자", "ENDSCRIPTS": "첨자끝"}, "MSroots": {"2": "제곱근", "3": "세제곱근", "4": "네제곱근"}, "font": {"bold": "진한", "bold-fraktur": "진한 프락투어", "bold-italic": "진한 기울임체", "bold-script": "진한 필기체", "caligraphic": "흘림체", "caligraphic-bold": "진한 흘림체", "double-struck": "칠판볼드체", "double-struck-italic": "기울인 칠판볼드체", "fraktur": "프락투어", "fullwidth": "전각", "italic": "기울임체", "monospace": "고정폭", "normal": "기본", "oldstyle": "옛체", "oldstyle-bold": "진한 옛체", "script": "필기체", "sans-serif": "고딕", "sans-serif-italic": "기울인 고딕", "sans-serif-bold": "진한 고딕", "sans-serif-bold-italic": "진한 고딕 기울임체", "unknown": "알 수 없음"}, "embellish": {"super": "위첨자", "sub": "아래첨자", "circled": "원문자", "parenthesized": "괄호문자", "period": ["마침표", "postfix<PERSON><PERSON><PERSON>"], "negative-circled": "검은 원문자", "double-circled": "겹원문자", "circled-sans-serif": "고딕 원문자", "negative-circled-sans-serif": "검은 고딕 원문자", "comma": ["콤마", "postfix<PERSON><PERSON><PERSON>"], "squared": "사각문자", "negative-squared": "검은 사각문자"}, "role": {"addition": "덧셈", "multiplication": "곱셈", "subtraction": "뺄셈", "division": "나눗셈", "equality": "등식", "inequality": "부등식", "element": "원소", "arrow": "화살표", "determinant": "행렬식", "rowvector": "행 벡터", "binomial": "이항계수", "squarematrix": "정방행렬", "multiline": "연립", "matrix": "행렬", "vector": "벡터", "cases": "케이스", "table": "표", "unknown": "알 수 없음"}, "enclose": {"longdiv": "나눗셈법", "actuarial": "첨자", "radical": "제곱근", "box": "상자", "roundedbox": "둥근 상자", "circle": "동그라미", "left": "왼쪽 세로선", "right": "오른쪽 세로선", "top": "바", "bottom": "언더바", "updiagonalstrike": "소거", "downdiagonalstrike": "소거", "verticalstrike": "소거", "horizontalstrike": "소거", "madruwb": "팩토리얼", "updiagonalarrow": "대각선 화살표", "phasorangle": "위상각", "unknown": "나눗셈법"}, "navigate": {"COLLAPSIBLE": "접기", "EXPANDABLE": "열기", "LEVEL": "레벨"}, "regexp": {"TEXT": "a-zA-Z", "NUMBER": "((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+", "DECIMAL_MARK": "\\.", "DIGIT_GROUP": ",", "JOINER_SUBSUPER": " ", "JOINER_FRAC": ""}, "unitTimes": ""}}, "ko/messages/numbers.min": {"kind": "numbers", "locale": "ko", "messages": {"zero": "영", "ones": ["", "일", "이", "삼", "사", "오", "육", "칠", "팔", "구", "", "한", "두", "세", "네", "다섯", "여섯", "일곱", "여덟", "아홉"], "tens": ["", "십", "이십", "삼십", "사십", "오십", "육십", "칠십", "팔십", "구십", "", "열", "스물", "서른", "마흔", "쉰", "예순", "일흔", "여든", "아흔"], "large": ["", "만", "억", "조", "경", "해", "자", "양", "구", "간"], "vulgarSep": " ", "numSep": " "}}, "ko/si/prefixes.min": [{"Y": "요타", "Z": "제타", "E": "엑사", "P": "페타", "T": "테라", "G": "기가", "M": "메가", "k": "킬로", "h": "헥토", "da": "데카", "d": "데시", "c": "센티", "m": "밀리", "µ": "마이크로", "μ": "마이크로", "n": "나노", "p": "피코", "f": "펨토", "a": "아토", "z": "젭토", "y": "욕토"}], "ko/functions/algebra.min": [{"locale": "ko"}, {"category": "Algebra", "mappings": {"default": {"default": "도"}}, "key": "deg", "names": ["deg"]}, {"category": "Algebra", "mappings": {"default": {"default": "행렬식"}, "mathspeak": {"default": "행렬식"}}, "key": "det", "names": ["det"]}, {"category": "Algebra", "mappings": {"default": {"default": "차원"}}, "key": "dim", "names": ["dim"]}, {"category": "Algebra", "mappings": {"default": {"default": "준동형"}, "mathspeak": {"default": "준동형"}, "clearspeak": {"default": "준동형"}}, "key": "hom", "names": ["hom", "Hom"]}, {"category": "Algebra", "mappings": {"default": {"default": "커널"}}, "key": "ker", "names": ["ker"]}, {"category": "Algebra", "mappings": {"default": {"default": "트레이스"}}, "key": "Tr", "names": ["Tr", "tr"]}], "ko/functions/elementary.min": [{"locale": "ko"}, {"category": "Logarithm", "mappings": {"default": {"default": "로그"}}, "key": "log", "names": ["log"]}, {"category": "Logarithm", "mappings": {"default": {"default": "자연로그"}, "mathspeak": {"default": "자연로그"}, "clearspeak": {"default": "엘 엔", "Log_LnAsNaturalLog": "자연로그"}}, "key": "ln", "names": ["ln"]}, {"category": "Logarithm", "mappings": {"default": {"default": "상용로그"}}, "key": "lg", "names": ["lg"]}, {"category": "Elementary", "mappings": {"default": {"default": "자연지수함수"}, "mathspeak": {"default": "자연지수함수"}, "clearspeak": {"default": "자연지수함수"}}, "key": "exp", "names": ["exp", "expt"]}, {"category": "Elementary", "mappings": {"default": {"default": "최대공약수"}, "mathspeak": {"default": "최대공약수"}, "clearspeak": {"default": "최대공약수"}}, "key": "gcd", "names": ["gcd"]}, {"category": "Elementary", "mappings": {"default": {"default": "최소공배수"}, "mathspeak": {"default": "최소공배수"}, "clearspeak": {"default": "최소공배수"}}, "key": "lcm", "names": ["lcm"]}, {"category": "Complex", "mappings": {"default": {"default": "편각"}, "mathspeak": {"default": "편각"}, "clearspeak": {"default": "편각"}}, "key": "arg", "names": ["arg"]}, {"category": "Complex", "mappings": {"default": {"default": "허수 부분"}, "mathspeak": {"default": "허수 부분"}, "clearspeak": {"default": "허수 부분"}}, "key": "im", "names": ["im"]}, {"category": "Complex", "mappings": {"default": {"default": "실수 부분"}, "mathspeak": {"default": "실수 부분"}, "clearspeak": {"default": "실수 부분"}}, "key": "re", "names": ["re"]}, {"category": "Limits", "mappings": {"default": {"default": "하한"}, "mathspeak": {"default": "하한"}, "clearspeak": {"default": "하한"}}, "key": "inf", "names": ["inf"]}, {"category": "Limits", "mappings": {"default": {"default": "리미트"}, "mathspeak": {"default": "리미트"}, "clearspeak": {"default": "리미트"}}, "key": "lim", "names": ["lim"]}, {"category": "Limits", "mappings": {"default": {"default": "하극한"}, "mathspeak": {"default": "하극한"}, "clearspeak": {"default": "하극한"}}, "key": "liminf", "names": ["lim inf", "liminf"]}, {"category": "Limits", "mappings": {"default": {"default": "상극한"}, "mathspeak": {"default": "상극한"}, "clearspeak": {"default": "상극한"}}, "key": "limsup", "names": ["lim sup", "limsup"]}, {"category": "Limits", "mappings": {"default": {"default": "최댓값"}, "mathspeak": {"default": "최댓값"}, "clearspeak": {"default": "최댓값"}}, "key": "max", "names": ["max"]}, {"category": "Limits", "mappings": {"default": {"default": "최솟값"}, "mathspeak": {"default": "최솟값"}, "clearspeak": {"default": "최솟값"}}, "key": "min", "names": ["min"]}, {"category": "Limits", "mappings": {"default": {"default": "상한"}, "mathspeak": {"default": "상한"}, "clearspeak": {"default": "상한"}}, "key": "sup", "names": ["sup"]}, {"category": "Limits", "mappings": {"default": {"default": "쌍대극한"}}, "key": "<PERSON><PERSON><PERSON>", "names": ["<PERSON><PERSON><PERSON>", "inj lim"]}, {"category": "Limits", "mappings": {"default": {"default": "역 극한"}}, "key": "proj<PERSON>", "names": ["proj<PERSON>", "proj lim"]}, {"category": "Elementary", "mappings": {"default": {"default": "모듈로"}, "mathspeak": {"default": "모듈로"}, "clearspeak": {"default": "모듈로"}}, "key": "mod", "names": ["mod"]}, {"category": "Probability", "mappings": {"default": {"default": "확률"}}, "key": "Pr", "names": ["Pr"]}], "ko/functions/hyperbolic.min": [{"locale": "ko"}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 코싸인"}}, "key": "cosh", "names": ["cosh"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 코탄젠트"}}, "key": "coth", "names": ["coth"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 코시컨트"}}, "key": "csch", "names": ["csch"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 시컨트"}}, "key": "sech", "names": ["sech"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 싸인"}}, "key": "sinh", "names": ["sinh"]}, {"category": "Hyperbolic", "mappings": {"default": {"default": "하이퍼볼릭 탄젠트"}}, "key": "tanh", "names": ["tanh"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크코싸인"}}, "key": "arcosh", "names": ["arcosh", "arccosh"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크코탄젠트"}}, "key": "arcoth", "names": ["arcoth", "arccoth"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크코시컨트"}}, "key": "<PERSON><PERSON>", "names": ["<PERSON><PERSON>", "arc<PERSON>ch"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크시컨트"}}, "key": "arsech", "names": ["arsech", "arcsech"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크싸인"}}, "key": "a<PERSON><PERSON><PERSON>", "names": ["a<PERSON><PERSON><PERSON>", "arcsinh"]}, {"category": "Area", "mappings": {"default": {"default": "하이퍼볼릭 아크탄젠트"}}, "key": "artanh", "names": ["artanh", "arctanh"]}], "ko/functions/trigonometry.min": [{"locale": "ko"}, {"category": "Trigonometric", "mappings": {"default": {"default": "코싸인"}}, "key": "cos", "names": ["cos", "cosine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "코탄젠트"}}, "key": "cot", "names": ["cot"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "코시컨트"}}, "key": "csc", "names": ["csc"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "시컨트"}}, "key": "sec", "names": ["sec"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "싸인"}}, "key": "sin", "names": ["sin", "sine"]}, {"category": "Trigonometric", "mappings": {"default": {"default": "탄젠트"}}, "key": "tan", "names": ["tan"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 코싸인"}, "clearspeak": {"Trig_TrigInverse": "코싸인 역함수"}}, "key": "arccos", "names": ["arccos"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 코탄젠트"}, "clearspeak": {"Trig_TrigInverse": "코탄젠트 역함수"}}, "key": "<PERSON><PERSON>", "names": ["<PERSON><PERSON>"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 코시컨트"}, "clearspeak": {"Trig_TrigInverse": "코시컨트 역함수"}}, "key": "arccsc", "names": ["arccsc"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 시컨트"}, "clearspeak": {"Trig_TrigInverse": "시컨트 역함수"}}, "key": "arcsec", "names": ["arcsec"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 싸인"}, "clearspeak": {"Trig_TrigInverse": "싸인 역함수"}}, "key": "arcsin", "names": ["arcsin"]}, {"category": "Cyclometric", "mappings": {"default": {"default": "아크 탄젠트"}, "clearspeak": {"Trig_TrigInverse": "탄젠트 역함수"}}, "key": "arctan", "names": ["arctan"]}], "ko/symbols/digits_rest.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "제곱"}, "mathspeak": {"default": "제곱"}, "clearspeak": {"default": "제곱"}}, "key": "00B2"}, {"mappings": {"default": {"default": "세제곱"}, "mathspeak": {"default": "세제곱"}, "clearspeak": {"default": "세제곱"}}, "key": "00B3"}, {"mappings": {"default": {"default": "4 분의 1"}}, "key": "00BC"}, {"mappings": {"default": {"default": "2 분의 1"}}, "key": "00BD"}, {"mappings": {"default": {"default": "4 분의 3"}}, "key": "00BE"}, {"mappings": {"default": {"default": "7 분의 1"}}, "key": "2150"}, {"mappings": {"default": {"default": "9 분의 1"}}, "key": "2151"}, {"mappings": {"default": {"default": "10 분의 1"}}, "key": "2152"}, {"mappings": {"default": {"default": "3 분의 1"}}, "key": "2153"}, {"mappings": {"default": {"default": "3 분의 2"}}, "key": "2154"}, {"mappings": {"default": {"default": "5 분의 1"}}, "key": "2155"}, {"mappings": {"default": {"default": "5 분의 2"}}, "key": "2156"}, {"mappings": {"default": {"default": "5 분의 3"}}, "key": "2157"}, {"mappings": {"default": {"default": "5 분의 4"}}, "key": "2158"}, {"mappings": {"default": {"default": "6 분의 1"}}, "key": "2159"}, {"mappings": {"default": {"default": "6 분의 5"}}, "key": "215A"}, {"mappings": {"default": {"default": "8 분의 1"}}, "key": "215B"}, {"mappings": {"default": {"default": "8 분의 3"}}, "key": "215C"}, {"mappings": {"default": {"default": "8 분의 5"}}, "key": "215D"}, {"mappings": {"default": {"default": "8 분의 7"}}, "key": "215E"}, {"mappings": {"default": {"default": "분자 1"}}, "key": "215F"}, {"mappings": {"default": {"default": "3 분의 0"}}, "key": "2189"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 10"}}, "key": "3248"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 20"}}, "key": "3249"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 30"}}, "key": "324A"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 40"}}, "key": "324B"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 50"}}, "key": "324C"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 60"}}, "key": "324D"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 70"}}, "key": "324E"}, {"mappings": {"default": {"default": "검은 테두리 상자 원문자 80"}}, "key": "324F"}], "ko/symbols/greek-rest.min": [{"locale": "ko"}, {"key": "0394", "mappings": {"clearspeak": {"default": "삼각형", "TriangleSymbol_Delta": "델타"}}}], "ko/symbols/greek-scripts.min": [{"locale": "ko"}, {"key": "1D26", "mappings": {"default": {"default": "작은 대문자 감마"}, "mathspeak": {"default": "작은 대문자 감마"}}}, {"key": "1D27", "mappings": {"default": {"default": "작은 대문자 람다"}, "mathspeak": {"default": "작은 대문자 람다"}}}, {"key": "1D28", "mappings": {"default": {"default": "작은 대문자 파이"}, "mathspeak": {"default": "작은 대문자 파이"}}}, {"key": "1D29", "mappings": {"default": {"default": "작은 대문자 로"}, "mathspeak": {"default": "작은 대문자 로"}}}, {"key": "1D2A", "mappings": {"default": {"default": "작은 대문자 프사이"}, "mathspeak": {"default": "작은 대문자 프사이"}}}, {"key": "1D5E", "mappings": {"default": {"default": "위첨자 감마"}}}, {"key": "1D60", "mappings": {"default": {"default": "위첨자 파이"}}}, {"key": "1D66", "mappings": {"default": {"default": "아래첨자 베타"}}}, {"key": "1D67", "mappings": {"default": {"default": "아래첨자 감마"}}}, {"key": "1D68", "mappings": {"default": {"default": "아래첨자 로"}}}, {"key": "1D69", "mappings": {"default": {"default": "아래첨자 파이"}}}, {"key": "1D6A", "mappings": {"default": {"default": "아래첨자 키"}}}], "ko/symbols/greek-symbols.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "베타"}}, "key": "03D0"}, {"mappings": {"default": {"default": "카이"}}, "key": "03D7"}, {"mappings": {"default": {"default": "반전된 입실론"}}, "key": "03F6"}, {"mappings": {"default": {"default": "진한 대문자 디감마"}, "mathspeak": {"default": "진한 대문자 디감마"}}, "key": "1D7CA"}, {"mappings": {"default": {"default": "진한 디감마"}}, "key": "1D7CB"}], "ko/symbols/hebrew_letters.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "알레프", "alternative": "알레프"}}, "key": "2135"}, {"mappings": {"default": {"default": "벳", "alternative": "벳"}}, "key": "2136"}, {"mappings": {"default": {"default": "기멜", "alternative": "기멜"}}, "key": "2137"}, {"mappings": {"default": {"default": "달렛", "alternative": "달렛"}}, "key": "2138"}], "ko/symbols/latin-lower-double-accent.min": [{"locale": "ko"}, {"key": "01D6", "mappings": {"default": {"default": "두 점 위에 선이 있는 u"}}}, {"key": "01D8", "mappings": {"default": {"default": "두 점 위에 양음 부호가 있는 u"}}}, {"key": "01DA", "mappings": {"default": {"default": "두 점 위에 반대 곡절 부호가 있는 u"}}}, {"key": "01DC", "mappings": {"default": {"default": "두 점 위에 억음 부호가 있는 u"}}}, {"key": "01DF", "mappings": {"default": {"default": "두 점 위에 선이 있는 a"}}}, {"key": "01E1", "mappings": {"default": {"default": "한 점 위에 선이 있는 a"}}}, {"key": "01ED", "mappings": {"default": {"default": "오고넥과 위에 선이 있는 o"}}}, {"key": "01FB", "mappings": {"default": {"default": "링 위에 양음 부호가 있는 a"}}}, {"key": "022B", "mappings": {"default": {"default": "두 점 위에 선이 있는 o"}}}, {"key": "022D", "mappings": {"default": {"default": "물결표 위에 선이 있는 o"}}}, {"key": "0231", "mappings": {"default": {"default": "한 점 위에 선이 있는 o"}}}, {"key": "1E09", "mappings": {"default": {"default": "세디유 위에 양음 부호가 있는 c"}}}, {"key": "1E15", "mappings": {"default": {"default": "선 위에 억음 부호가 있는 e"}}}, {"key": "1E17", "mappings": {"default": {"default": "선 위에 양음 부호가 있는 e"}}}, {"key": "1E1D", "mappings": {"default": {"default": "세디유 위에 단음 부호가 있는 e"}}}, {"key": "1E2F", "mappings": {"default": {"default": "두 점 위에 양음 부호가 있는 i"}}}, {"key": "1E39", "mappings": {"default": {"default": "아래점과 위에 선이 있는 l"}}}, {"key": "1E4D", "mappings": {"default": {"default": "물결표 위에 양음 부호가 있는 o"}}}, {"key": "1E4F", "mappings": {"default": {"default": "물결표 위에 두 점이 있는 o"}}}, {"key": "1E51", "mappings": {"default": {"default": "선 위에 억음 부호가 있는 o"}}}, {"key": "1E53", "mappings": {"default": {"default": "선 위에 양음 부호가 있는 o"}}}, {"key": "1E5D", "mappings": {"default": {"default": "아래점과 위에 선이 있는 r"}}}, {"key": "1E65", "mappings": {"default": {"default": "양음 부호 위에 점이 있는 s"}}}, {"key": "1E67", "mappings": {"default": {"default": "반대 곡절 부호 위에 점이 있는 s"}}}, {"key": "1E69", "mappings": {"default": {"default": "아래와 위에 점이 있는 s"}}}, {"key": "1E79", "mappings": {"default": {"default": "물결표 위에 양음 부호가 있는 u"}}}, {"key": "1E7B", "mappings": {"default": {"default": "선 위에 두 점이 있는 u"}}}, {"key": "1EA5", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 a"}}}, {"key": "1EA7", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 a"}}}, {"key": "1EA9", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 a"}}}, {"key": "1EAB", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 a"}}}, {"key": "1EAD", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 a"}}}, {"key": "1EAF", "mappings": {"default": {"default": "단음 부호와 양음 부호가 있는 a"}}}, {"key": "1EB1", "mappings": {"default": {"default": "단음 부호와 억음 부호가 있는 a"}}}, {"key": "1EB3", "mappings": {"default": {"default": "단음 부호 위에 후크가 있는 a"}}}, {"key": "1EB5", "mappings": {"default": {"default": "단음 부호와 물결표가 있는 a"}}}, {"key": "1EB7", "mappings": {"default": {"default": "단음 부호와 아래점이 있는 a"}}}, {"key": "1EBF", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 e"}}}, {"key": "1EC1", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 e"}}}, {"key": "1EC3", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 e"}}}, {"key": "1EC5", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 e"}}}, {"key": "1EC7", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 e"}}}, {"key": "1ED1", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 o"}}}, {"key": "1ED3", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 o"}}}, {"key": "1ED5", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 o"}}}, {"key": "1ED7", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 o"}}}, {"key": "1ED9", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 o"}}}, {"key": "1EDB", "mappings": {"default": {"default": "양음 부호와 프라임이 있는 o"}}}, {"key": "1EDD", "mappings": {"default": {"default": "억음 부호와 프라임이 있는 o"}}}, {"key": "1EDF", "mappings": {"default": {"default": "후크 위에 프라임이 있는 o"}}}, {"key": "1EE1", "mappings": {"default": {"default": "물결표와 프라임이 있는 o"}}}, {"key": "1EE3", "mappings": {"default": {"default": "아래점과 프라임이 있는 o"}}}, {"key": "1EE9", "mappings": {"default": {"default": "양음 부호와 프라임이 있는 u"}}}, {"key": "1EEB", "mappings": {"default": {"default": "억음 부호와 프라임이 있는 u"}}}, {"key": "1EED", "mappings": {"default": {"default": "후크 위에 프라임이 있는 u"}}}, {"key": "1EEF", "mappings": {"default": {"default": "물결표와 프라임이 있는 u"}}}, {"key": "1EF1", "mappings": {"default": {"default": "아래점과 프라임이 있는 u"}}}], "ko/symbols/latin-lower-phonetic.min": [{"locale": "ko"}, {"key": "00F8", "mappings": {"default": {"default": "획을 그은 o"}}}, {"key": "0111", "mappings": {"default": {"default": "획을 그은 d"}}}, {"key": "0127", "mappings": {"default": {"default": "획을 그은 h"}}}, {"key": "0142", "mappings": {"default": {"default": "획을 그은 l"}}}, {"key": "0167", "mappings": {"default": {"default": "획을 그은 t"}}}, {"key": "0180", "mappings": {"default": {"default": "획을 그은 b"}}}, {"key": "019B", "mappings": {"default": {"default": "획을 그은 람다"}}}, {"key": "01B6", "mappings": {"default": {"default": "획을 그은 z"}}}, {"key": "01BE", "mappings": {"default": {"default": "획을 그은 성문 파열음"}}}, {"key": "01E5", "mappings": {"default": {"default": "획을 그은 g"}}}, {"key": "01FF", "mappings": {"default": {"default": "획을 그은 양음부호 o"}}}, {"key": "023C", "mappings": {"default": {"default": "획을 그은 c"}}}, {"key": "0247", "mappings": {"default": {"default": "획을 그은 e"}}}, {"key": "0249", "mappings": {"default": {"default": "획을 그은 j"}}}, {"key": "024D", "mappings": {"default": {"default": "획을 그은 r"}}}, {"key": "024F", "mappings": {"default": {"default": "획을 그은 y"}}}, {"key": "025F", "mappings": {"default": {"default": "획을 그은 점이 없는 j"}}}, {"key": "0268", "mappings": {"default": {"default": "획을 그은 i"}}}, {"key": "0284", "mappings": {"default": {"default": "획과 후크가 있는 점없는 j"}}}, {"key": "02A1", "mappings": {"default": {"default": "획이 있는 라틴 문자 성문 파열음"}}}, {"key": "02A2", "mappings": {"default": {"default": "반전된 획이 있는 라틴 문자 성문 파열음"}}}, {"key": "1D13", "mappings": {"default": {"default": "획을 그은 옆으로 누운 o"}}}, {"key": "1D7C", "mappings": {"default": {"default": "획을 그은 이오타"}}}, {"key": "1D7D", "mappings": {"default": {"default": "획을 그은 p"}}}, {"key": "1D7F", "mappings": {"default": {"default": "획을 그은 입실론"}}}, {"key": "1E9C", "mappings": {"default": {"default": "대각선 획이 있는 긴 s"}}}, {"key": "1E9D", "mappings": {"default": {"default": "높은 획이 있는 긴 s"}}}, {"key": "018D", "mappings": {"default": {"default": "뒤집힌 델타"}}}, {"key": "1E9B", "mappings": {"default": {"default": "위에 점이 있는 긴 s"}}}, {"key": "1E9F", "mappings": {"default": {"default": "델타"}}}, {"key": "0138", "mappings": {"default": {"default": "크라"}}}, {"key": "017F", "mappings": {"default": {"default": "긴 s"}}}, {"key": "0183", "mappings": {"default": {"default": "위에 막대가 있는 b"}}}, {"key": "0185", "mappings": {"default": {"default": "톤 6"}}}, {"key": "0188", "mappings": {"default": {"default": "고리가 있는 c"}}}, {"key": "018C", "mappings": {"default": {"default": "상단막대가 있는 d"}}}, {"key": "0192", "mappings": {"default": {"default": "고리가 있는 f"}}}, {"key": "0195", "mappings": {"default": {"default": "hv"}}}, {"key": "0199", "mappings": {"default": {"default": "고리가 있는 k"}}}, {"key": "019A", "mappings": {"default": {"default": "막대가 있는 l"}}}, {"key": "019E", "mappings": {"default": {"default": "오른쪽 다리가 긴 n"}}}, {"key": "01A1", "mappings": {"default": {"default": "뿔이 있는 o"}}}, {"key": "01A3", "mappings": {"default": {"default": "oi"}}}, {"key": "01A5", "mappings": {"default": {"default": "고리가 있는 p"}}}, {"key": "01A8", "mappings": {"default": {"default": "톤 2"}}}, {"key": "01AA", "mappings": {"default": {"default": "라틴문자 반전된 esh 루프"}}}, {"key": "01AB", "mappings": {"default": {"default": "구개음 고리가 있는 t"}}}, {"key": "01AD", "mappings": {"default": {"default": "고리가 있는 t"}}}, {"key": "01B0", "mappings": {"default": {"default": "뿔이 있는 u"}}}, {"key": "01B4", "mappings": {"default": {"default": "뿔이 있는 y"}}}, {"key": "01B9", "mappings": {"default": {"default": "반전된 ezh"}}}, {"key": "01BA", "mappings": {"default": {"default": "꼬리가 있는 ezh"}}}, {"key": "01BD", "mappings": {"default": {"default": "톤 파이브"}}}, {"key": "01BF", "mappings": {"default": {"default": "라틴문자 wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "반대 곡절 부호가 있는 dz"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "장음기호가 있는 ae"}}}, {"key": "01EF", "mappings": {"default": {"default": "반대 곡절 부호가 있는 ezh"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "요흐"}}}, {"key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"key": "0293", "mappings": {"default": {"default": "컬이 있는 ezh"}}}, {"key": "02A4", "mappings": {"default": {"default": "이중문자 dezh"}}}, {"key": "01DD", "mappings": {"default": {"default": "반전된 e"}}}, {"key": "01FD", "mappings": {"default": {"default": "양음부호 ae"}}}, {"key": "0221", "mappings": {"default": {"default": "컬이 있는 d"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "고리가 있는 z"}}}, {"key": "0234", "mappings": {"default": {"default": "컬이 있는 l"}}}, {"key": "0235", "mappings": {"default": {"default": "컬이 있는 n"}}}, {"key": "0236", "mappings": {"default": {"default": "컬이 있는 t"}}}, {"key": "0238", "mappings": {"default": {"default": "이중문자 db"}}}, {"key": "0239", "mappings": {"default": {"default": "이중문자 qp"}}}, {"key": "023F", "mappings": {"default": {"default": "스와시 테일이 있는 s"}}}, {"key": "0240", "mappings": {"default": {"default": "스와시 테일이 있는 z"}}}, {"key": "0242", "mappings": {"default": {"default": "성문 파열음"}}}, {"key": "024B", "mappings": {"default": {"default": "후크 꼬리가 있는 q"}}}, {"key": "0250", "mappings": {"default": {"default": "반전된 a"}}}, {"key": "0251", "mappings": {"default": {"default": "알파"}}}, {"key": "0252", "mappings": {"default": {"default": "반전된 알파"}}}, {"key": "0253", "mappings": {"default": {"default": "후크가 있는 b"}}}, {"key": "0254", "mappings": {"default": {"default": "열린 o"}}}, {"key": "0255", "mappings": {"default": {"default": "컬이 있는 c"}}}, {"key": "0256", "mappings": {"default": {"default": "꼬리가 있는 d"}}}, {"key": "0257", "mappings": {"default": {"default": "후크가 있는 d"}}}, {"key": "0258", "mappings": {"default": {"default": "반전된 e"}}}, {"key": "0259", "mappings": {"default": {"default": "슈와"}}}, {"key": "025A", "mappings": {"default": {"default": "후크가 있는 슈와"}}}, {"key": "025B", "mappings": {"default": {"default": "열린 e"}}}, {"key": "025C", "mappings": {"default": {"default": "반전된 열린 e"}}}, {"key": "025D", "mappings": {"default": {"default": "후크가 있는 반전된 열린 e"}}}, {"key": "025E", "mappings": {"default": {"default": "닫힌 반전된 열린 e"}}}, {"key": "0260", "mappings": {"default": {"default": "후크가 있는 g"}}}, {"key": "0261", "mappings": {"default": {"default": "스크립트 g"}}}, {"key": "0263", "mappings": {"default": {"default": "감마"}}}, {"key": "0264", "mappings": {"default": {"default": "아기 감마"}}}, {"key": "0265", "mappings": {"default": {"default": "반전된 h"}}}, {"key": "0266", "mappings": {"default": {"default": "후크가 있는 h"}}}, {"key": "0267", "mappings": {"default": {"default": "후크가 있는 heng"}}}, {"key": "0269", "mappings": {"default": {"default": "이오타"}}}, {"key": "026B", "mappings": {"default": {"default": "가운데 물결표가 있는 l"}}}, {"key": "026C", "mappings": {"default": {"default": "벨트가 있는 l"}}}, {"key": "026D", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 l"}}}, {"key": "026F", "mappings": {"default": {"default": "반전된 m"}}}, {"key": "0270", "mappings": {"default": {"default": "긴 다리가 있는 반전된 m"}}}, {"key": "0271", "mappings": {"default": {"default": "후크가 있는 m"}}}, {"key": "0272", "mappings": {"default": {"default": "왼쪽 후크가 있는 n"}}}, {"key": "0273", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 n"}}}, {"key": "0275", "mappings": {"default": {"default": "막힌 o"}}}, {"key": "0277", "mappings": {"default": {"default": "닫힌 오메가"}}}, {"key": "0278", "mappings": {"default": {"default": "파이"}}}, {"key": "0279", "mappings": {"default": {"default": "반전된 r"}}}, {"key": "027A", "mappings": {"default": {"default": "긴 다리가 있는 반전된 r"}}}, {"key": "027B", "mappings": {"default": {"default": "후크가 있는 반전된 r"}}}, {"key": "027C", "mappings": {"default": {"default": "긴 다리가 있는 r"}}}, {"key": "027D", "mappings": {"default": {"default": "꼬리가 있는 r"}}}, {"key": "027E", "mappings": {"default": {"default": "낚시후크가 있는 r"}}}, {"key": "027F", "mappings": {"default": {"default": "낚시후크가 있는 반전된 r"}}}, {"key": "0282", "mappings": {"default": {"default": "후크가 있는 s"}}}, {"key": "0283", "mappings": {"default": {"default": "esh"}}}, {"key": "0285", "mappings": {"default": {"default": "쪼그린 반전된 esh"}}}, {"key": "0286", "mappings": {"default": {"default": "컬이 있는 esh"}}}, {"key": "0287", "mappings": {"default": {"default": "반전된 t"}}}, {"key": "0288", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 t"}}}, {"key": "0289", "mappings": {"default": {"default": "u 바"}}}, {"key": "028A", "mappings": {"default": {"default": "입실론"}}}, {"key": "028B", "mappings": {"default": {"default": "후크가 있는 v"}}}, {"key": "028C", "mappings": {"default": {"default": "반전된 v"}}}, {"key": "028D", "mappings": {"default": {"default": "반전된 w"}}}, {"key": "028E", "mappings": {"default": {"default": "반전된 y"}}}, {"key": "0290", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 z"}}}, {"key": "0291", "mappings": {"default": {"default": "컬이 있는 z"}}}, {"key": "0295", "mappings": {"default": {"default": "라틴문자 인두 음성 마찰음"}}}, {"key": "0296", "mappings": {"default": {"default": "라틴문자 역성문 파열음"}}}, {"key": "0297", "mappings": {"default": {"default": "라틴문자 늘어난 c"}}}, {"key": "0298", "mappings": {"default": {"default": "라틴문자 양순 흡착음"}}}, {"key": "029A", "mappings": {"default": {"default": "닫힌 열린 e"}}}, {"key": "029E", "mappings": {"default": {"default": "반전된 k"}}}, {"key": "02A0", "mappings": {"default": {"default": "후크가 있는 q"}}}, {"key": "02A3", "mappings": {"default": {"default": "이중문자 dz"}}}, {"key": "02A5", "mappings": {"default": {"default": "컬이 있는 이중문자 dz"}}}, {"key": "02A6", "mappings": {"default": {"default": "이중문자 ts"}}}, {"key": "02A7", "mappings": {"default": {"default": "이중문자 tesh"}}}, {"key": "02A8", "mappings": {"default": {"default": "컬이 있는 이중문자 tc"}}}, {"key": "02A9", "mappings": {"default": {"default": "이중문자 feng"}}}, {"key": "02AA", "mappings": {"default": {"default": "이중문자 ls"}}}, {"key": "02AB", "mappings": {"default": {"default": "이중문자 lz"}}}, {"key": "02AC", "mappings": {"default": {"default": "라틴문자 양순 충격음"}}}, {"key": "02AD", "mappings": {"default": {"default": "라틴문자 양치 충격음"}}}, {"key": "02AE", "mappings": {"default": {"default": "낚시후크가 있는 반전된 h"}}}, {"key": "02AF", "mappings": {"default": {"default": "낚시후크와 꼬리가 있는 반전된 h"}}}, {"key": "1D02", "mappings": {"default": {"default": "반전된 ae"}}}, {"key": "1D08", "mappings": {"default": {"default": "반전된 열린 e"}}}, {"key": "1D09", "mappings": {"default": {"default": "반전된 i"}}}, {"key": "1D11", "mappings": {"default": {"default": "옆으로 누운 o"}}}, {"key": "1D12", "mappings": {"default": {"default": "옆으로 누운 열린 o"}}}, {"key": "1D14", "mappings": {"default": {"default": "반전된 oe"}}}, {"key": "1D16", "mappings": {"default": {"default": "위 절반 o"}}}, {"key": "1D17", "mappings": {"default": {"default": "아래 절반 o"}}}, {"key": "1D1D", "mappings": {"default": {"default": "옆으로 누운 u"}}}, {"key": "1D1E", "mappings": {"default": {"default": "옆으로 누운 diaeresized u"}}}, {"key": "1D1F", "mappings": {"default": {"default": "옆으로 누운 반전된 m"}}}, {"key": "1D24", "mappings": {"default": {"default": "라틴문자 유성 후두 마찰음"}}}, {"key": "1D25", "mappings": {"default": {"default": "라틴문자 ain"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "중간 물결표가 있는 b"}}}, {"key": "1D6D", "mappings": {"default": {"default": "중간 물결표가 있는 d"}}}, {"key": "1D6E", "mappings": {"default": {"default": "중간 물결표가 있는 f"}}}, {"key": "1D6F", "mappings": {"default": {"default": "중간 물결표가 있는 m"}}}, {"key": "1D70", "mappings": {"default": {"default": "중간 물결표가 있는 n"}}}, {"key": "1D71", "mappings": {"default": {"default": "중간 물결표가 있는 p"}}}, {"key": "1D72", "mappings": {"default": {"default": "중간 물결표가 있는 r"}}}, {"key": "1D73", "mappings": {"default": {"default": "낚시후크와 중간 물결표가 있는 r"}}}, {"key": "1D74", "mappings": {"default": {"default": "중간 물결표가 있는 s"}}}, {"key": "1D75", "mappings": {"default": {"default": "중간 물결표가 있는 t"}}}, {"key": "1D76", "mappings": {"default": {"default": "중간 물결표가 있는 케트"}}}, {"key": "1D77", "mappings": {"default": {"default": "반전된 g"}}}, {"key": "1D79", "mappings": {"default": {"default": "insular g"}}}, {"key": "1D7A", "mappings": {"default": {"default": "취소선이 있는 th"}}}, {"key": "1D80", "mappings": {"default": {"default": "구강음 후크가 있는 b"}}}, {"key": "1D81", "mappings": {"default": {"default": "구개음 후크가 있는 d"}}}, {"key": "1D82", "mappings": {"default": {"default": "구개음 후크가 있는 f"}}}, {"key": "1D83", "mappings": {"default": {"default": "구개음 후크가 있는 g"}}}, {"key": "1D84", "mappings": {"default": {"default": "구개음 후크가 있는 k"}}}, {"key": "1D85", "mappings": {"default": {"default": "구개음 후크가 있는 l"}}}, {"key": "1D86", "mappings": {"default": {"default": "구개음 후크가 있는 m"}}}, {"key": "1D87", "mappings": {"default": {"default": "구개음 후크가 있는 n"}}}, {"key": "1D88", "mappings": {"default": {"default": "구개음 후크가 있는 p"}}}, {"key": "1D89", "mappings": {"default": {"default": "구개음 후크가 있는 r"}}}, {"key": "1D8A", "mappings": {"default": {"default": "구개음 후크가 있는 s"}}}, {"key": "1D8B", "mappings": {"default": {"default": "구개음 후크가 있는 esh"}}}, {"key": "1D8C", "mappings": {"default": {"default": "구개음 후크가 있는 v"}}}, {"key": "1D8D", "mappings": {"default": {"default": "구개음 후크가 있는 x"}}}, {"key": "1D8E", "mappings": {"default": {"default": "구개음 후크가 있는 z"}}}, {"key": "1D8F", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 a"}}}, {"key": "1D90", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 알파"}}}, {"key": "1D91", "mappings": {"default": {"default": "꼬리와 후크가 있는 d"}}}, {"key": "1D92", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 e"}}}, {"key": "1D93", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 열린 e"}}}, {"key": "1D94", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 반전된 열린 e"}}}, {"key": "1D95", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 슈와"}}}, {"key": "1D96", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 i"}}}, {"key": "1D97", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 열린 o"}}}, {"key": "1D98", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 esh"}}}, {"key": "1D99", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 u"}}}, {"key": "1D9A", "mappings": {"default": {"default": "뒤로 휜 후크가 있는 ezh"}}}, {"key": "0149", "mappings": {"default": {"default": "아포스트로피 앞에 n"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "ko/symbols/latin-lower-single-accent.min": [{"locale": "ko"}, {"key": "00E0", "mappings": {"default": {"default": "억음 a"}, "mathspeak": {"default": "억음 부호가 올려진 a", "brief": "억음 부호가 올려진 a", "sbrief": "억음 부호가 올려진 a"}}}, {"key": "00E1", "mappings": {"default": {"default": "양음 a"}, "mathspeak": {"default": "양음 부호가 올려진 a", "brief": "양음 부호가 올려진 a", "sbrief": "양음 부호가 올려진 a"}}}, {"key": "00E2", "mappings": {"default": {"default": "곡절음 a"}, "mathspeak": {"default": "삽입 기호가 올려진 a", "brief": "삽입 기호가 올려진 a", "sbrief": "삽입 기호가 올려진 a"}}}, {"key": "00E3", "mappings": {"default": {"default": "물결표 a"}, "mathspeak": {"default": "물결표가 올려진 a", "brief": "물결표가 올려진 a", "sbrief": "물결표가 올려진 a"}}}, {"key": "00E4", "mappings": {"default": {"default": "분음 부호표 a"}, "mathspeak": {"default": "분음 부호가 올려진 a", "brief": "분음 부호가 올려진 a", "sbrief": "분음 부호가 올려진 a"}}}, {"key": "00E5", "mappings": {"default": {"default": "반지 부호 a"}, "mathspeak": {"default": "반지 부호가 올려진 a", "brief": "반지 부호가 올려진 a", "sbrief": "반지 부호가 올려진 a"}}}, {"key": "00E7", "mappings": {"default": {"default": "갈고리형 c"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 c", "brief": "갈고리형 부호가 붙여진 c", "sbrief": "갈고리형 부호가 붙여진 c"}}}, {"key": "00E8", "mappings": {"default": {"default": "억음 e"}, "mathspeak": {"default": "억음 부호가 올려진 e", "brief": "억음 부호가 올려진 e", "sbrief": "억음 부호가 올려진 e"}}}, {"key": "00E9", "mappings": {"default": {"default": "양음 e"}, "mathspeak": {"default": "양음 부호가 올려진 e", "brief": "양음 부호가 올려진 e", "sbrief": "양음 부호가 올려진 e"}}}, {"key": "00EA", "mappings": {"default": {"default": "곡절음 e"}, "mathspeak": {"default": "삽입 기호가 올려진 e", "brief": "삽입 기호가 올려진 e", "sbrief": "삽입 기호가 올려진 e"}}}, {"key": "00EB", "mappings": {"default": {"default": "분음 부호표 e"}, "mathspeak": {"default": "분음 부호가 올려진 e", "brief": "분음 부호가 올려진 e", "sbrief": "분음 부호가 올려진 e"}}}, {"key": "00EC", "mappings": {"default": {"default": "억음 i"}, "mathspeak": {"default": "억음 부호가 올려진 i", "brief": "억음 부호가 올려진 i", "sbrief": "억음 부호가 올려진 i"}}}, {"key": "00ED", "mappings": {"default": {"default": "양음 i"}, "mathspeak": {"default": "양음 부호가 올려진 i", "brief": "양음 부호가 올려진 i", "sbrief": "양음 부호가 올려진 i"}}}, {"key": "00EE", "mappings": {"default": {"default": "곡절음 i"}, "mathspeak": {"default": "삽입 기호가  올려진 i", "brief": "삽입 기호가 올려진 i", "sbrief": "삽입 기호가 올려진 i"}}}, {"key": "00EF", "mappings": {"default": {"default": "분음 부호표 i"}, "mathspeak": {"default": "분음 부호가 올려진 i", "brief": "분음 부호가 올려진 i", "sbrief": "분음 부호가 올려진 i"}}}, {"key": "00F1", "mappings": {"default": {"default": "물결표 n"}, "mathspeak": {"default": "물결표가 올려진 n", "brief": "물결표가 올려진 n", "sbrief": "물결표가 올려진 n"}}}, {"key": "00F2", "mappings": {"default": {"default": "억음 o"}, "mathspeak": {"default": "억음 부호가 올려진 o", "brief": "억음 부호가 올려진 o", "sbrief": "억음 부호가 올려진 o"}}}, {"key": "00F3", "mappings": {"default": {"default": "양음 o"}, "mathspeak": {"default": "양음 부호가 올려진 o", "brief": "양음 부호가 올려진 o", "sbrief": "양음 부호가 올려진 o"}}}, {"key": "00F4", "mappings": {"default": {"default": "곡절음 o"}, "mathspeak": {"default": "삽입 기호가 올려진 o", "brief": "삽입 기호가 올려진 o", "sbrief": "삽입 기호가 올려진 o"}}}, {"key": "00F5", "mappings": {"default": {"default": "물결표 o"}, "mathspeak": {"default": "물결표가 올려진 o", "brief": "물결표가 올려진 o", "sbrief": "물결표가 올려진 o"}}}, {"key": "00F6", "mappings": {"default": {"default": "분음 부호표 o"}, "mathspeak": {"default": "분음 부호가 올려진 o", "brief": "분음 부호가 올려진 o", "sbrief": "분음 부호가 올려진 o"}}}, {"key": "00F9", "mappings": {"default": {"default": "억음 u"}, "mathspeak": {"default": "억음 부호가 올려진 u", "brief": "억음 부호가 올려진 u", "sbrief": "억음 부호가 올려진 u"}}}, {"key": "00FA", "mappings": {"default": {"default": "양음 u"}, "mathspeak": {"default": "양음 부호가 올려진 u", "brief": "양음 부호가 올려진 u", "sbrief": "양음 부호가 올려진 u"}}}, {"key": "00FB", "mappings": {"default": {"default": "곡절음 u"}, "mathspeak": {"default": "삽입 기호가 올려진 u", "brief": "삽입 기호가 올려진 u", "sbrief": "삽입 기호가 올려진 u"}}}, {"key": "00FC", "mappings": {"default": {"default": "분음 부호표 u"}, "mathspeak": {"default": "분음 부호가 올려진 u", "brief": "분음 부호가 올려진 u", "sbrief": "분음 부호가 올려진 u"}}}, {"key": "00FD", "mappings": {"default": {"default": "양음 y"}, "mathspeak": {"default": "양음 부호가 올려진 y", "brief": "양음 부호가 올려진 y", "sbrief": "양음 부호가 올려진 y"}}}, {"key": "00FF", "mappings": {"default": {"default": "분음 부호표 y"}, "mathspeak": {"default": "분음 부호가 올려진 y", "brief": "분음 부호가 올려진 y", "sbrief": "분음 부호가 올려진 y"}}}, {"key": "0101", "mappings": {"default": {"default": "오버바 a"}, "mathspeak": {"default": "오버바 부호가 올려진 a", "brief": "오버바 부호가 올려진 a", "sbrief": "오버바 부호가 올려진 a"}}}, {"key": "0103", "mappings": {"default": {"default": "겹온음표 a"}, "mathspeak": {"default": "겹온음표 부호가 올려진 a", "brief": "겹온음표 부호가 올려진 a", "sbrief": "겹온음표 부호가 올려진 a"}}}, {"key": "0105", "mappings": {"default": {"default": "아가뇩 a"}, "mathspeak": {"default": "아가뇩 부호가 있는 a", "brief": "아가뇩 부호가 있는 a", "sbrief": "아가뇩 부호가 있는 a"}}}, {"key": "0107", "mappings": {"default": {"default": "양음 c"}, "mathspeak": {"default": "양음 부호가 올려진 c", "brief": "양음 부호가 올려진 c", "sbrief": "양음 부호가 올려진 c"}}}, {"key": "0109", "mappings": {"default": {"default": "곡절음 c"}, "mathspeak": {"default": "삽입 기호가 올려진 c", "brief": "삽입 기호가 올려진 c", "sbrief": "삽입 기호가 올려진 c"}}}, {"key": "010B", "mappings": {"default": {"default": "윗점 c"}, "mathspeak": {"default": "마침점 부호가 올려진 c", "brief": "마침점 부호가 올려진 c", "sbrief": "마침점 부호가 올려진 c"}}}, {"key": "010D", "mappings": {"default": {"default": "카론 c"}, "mathspeak": {"default": "카론 부호가 올려진 c", "brief": "카론 부호가 올려진 c", "sbrief": "카론 부호가 올려진 c"}}}, {"key": "010F", "mappings": {"default": {"default": "카론 d"}, "mathspeak": {"default": "카론 부호가 올려진 d", "brief": "카론 부호가 올려진 d", "sbrief": "카론 부호가 올려진 d"}}}, {"key": "0113", "mappings": {"default": {"default": "오버바 e"}, "mathspeak": {"default": "오버바 부호가 올려진 e", "brief": "오버바 부호가 올려진 e", "sbrief": "오버바 부호가 올려진 e"}}}, {"key": "0115", "mappings": {"default": {"default": "겹온음표 e"}, "mathspeak": {"default": "겹온음표 부호가 올려진 e", "brief": "겹온음표 부호가 올려진 e", "sbrief": "겹온음표 부호가 올려진 e"}}}, {"key": "0117", "mappings": {"default": {"default": "윗점 e"}, "mathspeak": {"default": "마침점 부호가 올려진 e", "brief": "마침점 부호가 올려진 e", "sbrief": "마침점 부호가 올려진 e"}}}, {"key": "0119", "mappings": {"default": {"default": "아가뇩 e"}, "mathspeak": {"default": "아가뇩 부호가 있는 e", "brief": "아가뇩 부호가 있는 e", "sbrief": "아가뇩 부호가 있는 e"}}}, {"key": "011B", "mappings": {"default": {"default": "카론 e"}, "mathspeak": {"default": "카론 부호가 올려진 e", "brief": "카론 부호가 올려진 e", "sbrief": "카론 부호가 올려진 e"}}}, {"key": "011D", "mappings": {"default": {"default": "곡절음 g"}, "mathspeak": {"default": "삽입 기호가 올려진 g", "brief": "삽입 기호가 올려진 g", "sbrief": "삽입 기호가 올려진 g"}}}, {"key": "011F", "mappings": {"default": {"default": "겹온음표 g"}, "mathspeak": {"default": "겹온음표 부호가 올려진 g", "brief": "겹온음표 부호가 올려진 g", "sbrief": "겹온음표 부호가 올려진 g"}}}, {"key": "0121", "mappings": {"default": {"default": "윗점 g"}, "mathspeak": {"default": "마침점 부호가 올려진 g", "brief": "마침점 부호가 올려진 g", "sbrief": "마침점 부호가 올려진 g"}}}, {"key": "0123", "mappings": {"default": {"default": "갈고리형 g"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 g", "brief": "갈고리형 부호가 붙여진 g", "sbrief": "갈고리형 부호가 붙여진 g"}}}, {"key": "0125", "mappings": {"default": {"default": "곡절음 h"}, "mathspeak": {"default": "삽입 기호가 올려진 h", "brief": "삽입 기호가 올려진 h", "sbrief": "삽입 기호가 올려진 h"}}}, {"key": "0129", "mappings": {"default": {"default": "물결표 i"}, "mathspeak": {"default": "물결표가 올려진 i", "brief": "물결표가 올려진 i", "sbrief": "물결표가 올려진 i"}}}, {"key": "012B", "mappings": {"default": {"default": "오버바 i"}, "mathspeak": {"default": "오버바 부호가 올려진 i", "brief": "오버바 부호가 올려진 i", "sbrief": "오버바 부호가 올려진 i"}}}, {"key": "012D", "mappings": {"default": {"default": "겹온음표 i"}, "mathspeak": {"default": "겹온음표 부호가 올려진 i", "brief": "겹온음표 부호가 올려진 i", "sbrief": "겹온음표 부호가 올려진 i"}}}, {"key": "012F", "mappings": {"default": {"default": "아가뇩 i"}, "mathspeak": {"default": "아가뇩 부호가 있는 i", "brief": "아가뇩 부호가 있는 i", "sbrief": "아가뇩 부호가 있는 i"}}}, {"key": "0131", "mappings": {"default": {"default": "윗점 없는 i"}, "mathspeak": {"default": "위에 점이 없는 i", "brief": "위에 점이 없는 i", "sbrief": "위에 점이 없는 i"}}}, {"key": "0135", "mappings": {"default": {"default": "곡절음 j"}, "mathspeak": {"default": "삽입 기호가 올려진 j", "brief": "삽입 기호가 올려진 j", "sbrief": "삽입 기호가 올려진 j"}}}, {"key": "0137", "mappings": {"default": {"default": "갈고리형 k"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 k", "brief": "갈고리형 부호가 붙여진 k", "sbrief": "갈고리형 부호가 붙여진 k"}}}, {"key": "013A", "mappings": {"default": {"default": "양음 l"}, "mathspeak": {"default": "양음 부호가 올려진 l", "brief": "양음 부호가 올려진 l", "sbrief": "양음 부호가 올려진 l"}}}, {"key": "013C", "mappings": {"default": {"default": "갈고리형 l"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 l", "brief": "갈고리형 부호가 붙여진 l", "sbrief": "갈고리형 부호가 붙여진 l"}}}, {"key": "013E", "mappings": {"default": {"default": "카론 l"}, "mathspeak": {"default": "카론 부호가 올려진 l", "brief": "카론 부호가 올려진 l", "sbrief": "카론 부호가 올려진 l"}}}, {"key": "0140", "mappings": {"default": {"default": "중간점 l"}, "mathspeak": {"default": "중간점 부호가 있는 l", "brief": "중간점 부호가 있는 l", "sbrief": "중간점 부호가 있는 l"}}}, {"key": "0144", "mappings": {"default": {"default": "양음 n"}, "mathspeak": {"default": "양음 부호가 올려진 n", "brief": "양음 부호가 올려진 n", "sbrief": "양음 부호가 올려진 n"}}}, {"key": "0146", "mappings": {"default": {"default": "갈고리형 n"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 n", "brief": "갈고리형 부호가 붙여진 n", "sbrief": "갈고리형 부호가 붙여진 n"}}}, {"key": "0148", "mappings": {"default": {"default": "카론 n"}, "mathspeak": {"default": "카론 부호가 올려진 n", "brief": "카론 부호가 올려진 n", "sbrief": "카론 부호가 올려진 n"}}}, {"key": "014D", "mappings": {"default": {"default": "오버바 o"}, "mathspeak": {"default": "오버바 부호가 올려진 o", "brief": "오버바 부호가 올려진 o", "sbrief": "오버바 부호가 올려진 o"}}}, {"key": "014F", "mappings": {"default": {"default": "겹온음표 o"}, "mathspeak": {"default": "겹온음표 부호가 올려진 o", "brief": "겹온음표 부호가 올려진 o", "sbrief": "겹온음표 부호가 올려진 o"}}}, {"key": "0151", "mappings": {"default": {"default": "이중 양음 o"}, "mathspeak": {"default": "이중 양음 부호가 올려진 o", "brief": "이중 양음 부호가 올려진 o", "sbrief": "이중 양음 부호가 올려진 o"}}}, {"key": "0155", "mappings": {"default": {"default": "양음 r"}, "mathspeak": {"default": "양음 부호가 올려진 r", "brief": "양음 부호가 올려진 r", "sbrief": "양음 부호가 올려진 r"}}}, {"key": "0157", "mappings": {"default": {"default": "갈고리형 r"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 r", "brief": "갈고리형 부호가 붙여진 r", "sbrief": "갈고리형 부호가 붙여진 r"}}}, {"key": "0159", "mappings": {"default": {"default": "카론 r"}, "mathspeak": {"default": "카론 부호가 올려진 r", "brief": "카론 부호가 올려진 r", "sbrief": "카론 부호가 올려진 r"}}}, {"key": "015B", "mappings": {"default": {"default": "양음 s"}, "mathspeak": {"default": "양음 부호가 올려진 s", "brief": "양음 부호가 올려진 s", "sbrief": "양음 부호가 올려진 s"}}}, {"key": "015D", "mappings": {"default": {"default": "곡절음 s"}, "mathspeak": {"default": "삽입 기호가 올려진 s", "brief": "삽입 기호가 올려진 s", "sbrief": "삽입 기호가 올려진 s"}}}, {"key": "015F", "mappings": {"default": {"default": "갈고리형 s"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 s", "brief": "갈고리형 부호가 붙여진 s", "sbrief": "갈고리형 부호가 붙여진 s"}}}, {"key": "0161", "mappings": {"default": {"default": "카론 s"}, "mathspeak": {"default": "카론 부호가 올려진 s", "brief": "카론 부호가 올려진 s", "sbrief": "카론 부호가 올려진 s"}}}, {"key": "0163", "mappings": {"default": {"default": "갈고리형 t"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 t", "brief": "갈고리형 부호가 붙여진 t", "sbrief": "갈고리형 부호가 붙여진 t"}}}, {"key": "0165", "mappings": {"default": {"default": "카론 t"}, "mathspeak": {"default": "카론 부호가 올려진 t", "brief": "카론 부호가 올려진 t", "sbrief": "카론 부호가 올려진 t"}}}, {"key": "0169", "mappings": {"default": {"default": "물결표 u"}, "mathspeak": {"default": "물결표가 올려진 u", "brief": "물결표가 올려진 u", "sbrief": "물결표가 올려진 u"}}}, {"key": "016B", "mappings": {"default": {"default": "오버바 u"}, "mathspeak": {"default": "오버바 부호가 올려진 u", "brief": "오버바 부호가 올려진 u", "sbrief": "오버바 부호가 올려진 u"}}}, {"key": "016D", "mappings": {"default": {"default": "겹온음표 u"}, "mathspeak": {"default": "겹온음표 부호가 올려진 u", "brief": "겹온음표 부호가 올려진 u", "sbrief": "겹온음표 부호가 올려진 u"}}}, {"key": "016F", "mappings": {"default": {"default": "반지 부호 u"}, "mathspeak": {"default": "반지 부호가 올려진 u", "brief": "반지 부호가 올려진 u", "sbrief": "반지 부호가 올려진 u"}}}, {"key": "0171", "mappings": {"default": {"default": "이중 양음 u"}, "mathspeak": {"default": "이중 양음 부호가 올려진 u", "brief": "이중 양음 부호가 올려진 u", "sbrief": "이중 양음 부호가 올려진 u"}}}, {"key": "0173", "mappings": {"default": {"default": "아가뇩 u"}, "mathspeak": {"default": "아가뇩 부호가 있는 u", "brief": "아가뇩 부호가 있는 u", "sbrief": "아가뇩 부호가 있는 u"}}}, {"key": "0175", "mappings": {"default": {"default": "곡절음 w"}, "mathspeak": {"default": "삽입 기호가 올려진 w", "brief": "삽입 기호가 올려진 w", "sbrief": "삽입 기호가 올려진 w"}}}, {"key": "0177", "mappings": {"default": {"default": "곡절음 y"}, "mathspeak": {"default": "삽입 기호가 올려진 y", "brief": "삽입 기호가 올려진 y", "sbrief": "삽입 기호가 올려진 y"}}}, {"key": "017A", "mappings": {"default": {"default": "양음 z"}, "mathspeak": {"default": "양음 부호가 올려진 z", "brief": "양음 부호가 올려진 z", "sbrief": "양음 부호가 올려진 z"}}}, {"key": "017C", "mappings": {"default": {"default": "윗점 z"}, "mathspeak": {"default": "마침점 부호가 올려진 z", "brief": "마침점 부호가 올려진 z", "sbrief": "마침점 부호가 올려진 z"}}}, {"key": "017E", "mappings": {"default": {"default": "카론 z"}, "mathspeak": {"default": "카론 부호가 올려진 z", "brief": "카론 부호가 올려진 z", "sbrief": "카론 부호가 올려진 z"}}}, {"key": "01CE", "mappings": {"default": {"default": "카론 a"}, "mathspeak": {"default": "카론 부호가 올려진 a", "brief": "카론 부호가 올려진 a", "sbrief": "카론 부호가 올려진 a"}}}, {"key": "01D0", "mappings": {"default": {"default": "카론 i"}, "mathspeak": {"default": "카론 부호가 올려진 i", "brief": "카론 부호가 올려진 i", "sbrief": "카론 부호가 올려진 i"}}}, {"key": "01D2", "mappings": {"default": {"default": "카론 o"}, "mathspeak": {"default": "카론 부호가 올려진 o", "brief": "카론 부호가 올려진 o", "sbrief": "카론 부호가 올려진 o"}}}, {"key": "01D4", "mappings": {"default": {"default": "카론 u"}, "mathspeak": {"default": "카론 부호가 올려진 u", "brief": "카론 부호가 올려진 u", "sbrief": "카론 부호가 올려진 u"}}}, {"key": "01E7", "mappings": {"default": {"default": "카론 g"}, "mathspeak": {"default": "카론 부호가 올려진 g", "brief": "카론 부호가 올려진 g", "sbrief": "카론 부호가 올려진 g"}}}, {"key": "01E9", "mappings": {"default": {"default": "카론 k"}, "mathspeak": {"default": "카론 부호가 올려진 k", "brief": "카론 부호가 올려진 k", "sbrief": "카론 부호가 올려진 k"}}}, {"key": "01EB", "mappings": {"default": {"default": "아가뇩 o"}, "mathspeak": {"default": "아가뇩 부호가 있는 o", "brief": "아가뇩 부호가 있는 o", "sbrief": "아가뇩 부호가 있는 o"}}}, {"key": "01F0", "mappings": {"default": {"default": "카론 j"}, "mathspeak": {"default": "카론 부호가 올려진 j", "brief": "카론 부호가 올려진 j", "sbrief": "카론 부호가 올려진 j"}}}, {"key": "01F5", "mappings": {"default": {"default": "양음 g"}, "mathspeak": {"default": "양음 부호가 올려진 g", "brief": "양음 부호가 올려진 g", "sbrief": "양음 부호가 올려진 g"}}}, {"key": "01F9", "mappings": {"default": {"default": "억음 n"}, "mathspeak": {"default": "억음 부호가 올려진 n", "brief": "억음 부호가 올려진 n", "sbrief": "억음 부호가 올려진 n"}}}, {"key": "0201", "mappings": {"default": {"default": "이중 억음 a"}, "mathspeak": {"default": "이중 억음 부호가 올려진 a", "brief": "이중 억음 부호가 올려진 a", "sbrief": "이중 억음 부호가 올려진 a"}}}, {"key": "0203", "mappings": {"default": {"default": "반전 겹온음표 a"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 a", "brief": "반전된 겹온음표 부호가 올려진 a", "sbrief": "반전된 겹온음표 부호가 올려진 a"}}}, {"key": "0205", "mappings": {"default": {"default": "이중 억음 e"}, "mathspeak": {"default": "이중 억음 부호가 올려진 e", "brief": "이중 억음 부호가 올려진 e", "sbrief": "이중 억음 부호가 올려진 e"}}}, {"key": "0207", "mappings": {"default": {"default": "반전 겹온음표 e"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 e", "brief": "반전된 겹온음표 부호가 올려진 e", "sbrief": "반전된 겹온음표 부호가 올려진 e"}}}, {"key": "0209", "mappings": {"default": {"default": "이중 억음 i"}, "mathspeak": {"default": "이중 억음 부호가 올려진 i", "brief": "이중 억음 부호가 올려진 i", "sbrief": "이중 억음 부호가 올려진 i"}}}, {"key": "020B", "mappings": {"default": {"default": "반전 겹온음표 i"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 i", "brief": "반전된 겹온음표 부호가 올려진 i", "sbrief": "반전된 겹온음표 부호가 올려진 i"}}}, {"key": "020D", "mappings": {"default": {"default": "이중 억음 o"}, "mathspeak": {"default": "이중 억음 부호가 올려진 o", "brief": "이중 억음 부호가 올려진 o", "sbrief": "이중 억음 부호가 올려진 o"}}}, {"key": "020F", "mappings": {"default": {"default": "반전 겹온음표 o"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 o", "brief": "반전된 겹온음표 부호가 올려진 o", "sbrief": "반전된 겹온음표 부호가 올려진 o"}}}, {"key": "0211", "mappings": {"default": {"default": "이중 억음 r"}, "mathspeak": {"default": "이중 억음 부호가 올려진 r", "brief": "이중 억음 부호가 올려진 r", "sbrief": "이중 억음 부호가 올려진 r"}}}, {"key": "0213", "mappings": {"default": {"default": "반전 겹온음표 r"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 r", "brief": "반전된 겹온음표 부호가 올려진 r", "sbrief": "반전된 겹온음표 부호가 올려진 r"}}}, {"key": "0215", "mappings": {"default": {"default": "이중 억음 u"}, "mathspeak": {"default": "이중 억음 부호가 올려진 u", "brief": "이중 억음 부호가 올려진 u", "sbrief": "이중 억음 부호가 올려진 u"}}}, {"key": "0217", "mappings": {"default": {"default": "반전 겹온음표 u"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 u", "brief": "반전된 겹온음표 부호가 올려진 u", "sbrief": "반전된 겹온음표 부호가 올려진 u"}}}, {"key": "0219", "mappings": {"default": {"default": "아래 쉼표 s"}, "mathspeak": {"default": "아래에 쉼표 부호가 있는 s", "brief": "아래에 쉼표 부호가 있는 s", "sbrief": "아래에 쉼표 부호가 있는 s"}}}, {"key": "021B", "mappings": {"default": {"default": "아래 쉼표 t"}, "mathspeak": {"default": "아래에 쉼표 부호가 있는 t", "brief": "아래에 쉼표 부호가 있는 t", "sbrief": "아래에 쉼표 부호가 있는 t"}}}, {"key": "021F", "mappings": {"default": {"default": "카론 h"}, "mathspeak": {"default": "카론 부호가 올려진 h", "brief": "카론 부호가 올려진 h", "sbrief": "카론 부호가 올려진 h"}}}, {"key": "0227", "mappings": {"default": {"default": "윗점 a"}, "mathspeak": {"default": "마침점 부호가 올려진 a", "brief": "마침점 부호가 올려진 a", "sbrief": "마침점 부호가 올려진 a"}}}, {"key": "0229", "mappings": {"default": {"default": "갈고리형 e"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 e", "brief": "갈고리형 부호가 붙여진 e", "sbrief": "갈고리형 부호가 붙여진 e"}}}, {"key": "022F", "mappings": {"default": {"default": "윗점 o"}, "mathspeak": {"default": "마침점 부호가 올려진 o", "brief": "마침점 부호가 올려진 o", "sbrief": "마침점 부호가 올려진 o"}}}, {"key": "0233", "mappings": {"default": {"default": "오버바 y"}, "mathspeak": {"default": "오버바 부호가 올려진 y", "brief": "오버바 부호가 올려진 y", "sbrief": "오버바 부호가 올려진 y"}}}, {"key": "0237", "mappings": {"default": {"default": "윗점 없는 j"}, "mathspeak": {"default": "위에 점이 없는 j", "brief": "위에 점이 없는 j", "sbrief": "위에 점이 없는 j"}}}, {"key": "1E01", "mappings": {"default": {"default": "아래 반지 부호 a"}, "mathspeak": {"default": "아래에 반지 부호가 있는 a", "brief": "아래에 반지 부호가 있는 a", "sbrief": "아래에 반지 부호가 있는 a"}}}, {"key": "1E03", "mappings": {"default": {"default": "윗점 b"}, "mathspeak": {"default": "마침점 부호가 올려진 b", "brief": "마침점 부호가 올려진 b", "sbrief": "마침점 부호가 올려진 b"}}}, {"key": "1E05", "mappings": {"default": {"default": "아랫점 b"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 b", "brief": "아래에 마침표 부호가 있는 b", "sbrief": "아래에 마침표 부호가 있는 b"}}}, {"key": "1E07", "mappings": {"default": {"default": "언더바 b"}, "mathspeak": {"default": "언더바 부호가 있는 b", "brief": "언더바 부호가 있는 b", "sbrief": "언더바 부호가 있는 b"}}}, {"key": "1E0B", "mappings": {"default": {"default": "윗점 d"}, "mathspeak": {"default": "마침점 부호가 올려진 d", "brief": "마침점 부호가 올려진 d", "sbrief": "마침점 부호가 올려진 d"}}}, {"key": "1E0D", "mappings": {"default": {"default": "아랫점 d"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 d", "brief": "아래에 마침표 부호가 있는 d", "sbrief": "아래에 마침표 부호가 있는 d"}}}, {"key": "1E0F", "mappings": {"default": {"default": "언더바 d"}, "mathspeak": {"default": "언더바 부호가 있는 d", "brief": "언더바 부호가 있는 d", "sbrief": "언더바 부호가 있는 d"}}}, {"key": "1E11", "mappings": {"default": {"default": "갈고리형 d"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 d", "brief": "갈고리형 부호가 붙여진 d", "sbrief": "갈고리형 부호가 붙여진 d"}}}, {"key": "1E13", "mappings": {"default": {"default": "아래 곡절음 d"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 d", "brief": "아래에 곡절음 부호가 있는 d", "sbrief": "아래에 곡절음 부호가 있는 d"}}}, {"key": "1E19", "mappings": {"default": {"default": "아래 곡절음 e"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 e", "brief": "아래에 곡절음 부호가 있는 e", "sbrief": "아래에 곡절음 부호가 있는 e"}}}, {"key": "1E1B", "mappings": {"default": {"default": "아래 물결표 e"}, "mathspeak": {"default": "아래에 물결표가 있는 e", "brief": "아래에 물결표가 있는 e", "sbrief": "아래에 물결표가 있는 e"}}}, {"key": "1E1F", "mappings": {"default": {"default": "윗점 f"}, "mathspeak": {"default": "마침점 부호가 올려진 f", "brief": "마침점 부호가 올려진 f", "sbrief": "마침점 부호가 올려진 f"}}}, {"key": "1E21", "mappings": {"default": {"default": "오버바 g"}, "mathspeak": {"default": "오버바 부호가 올려진 g", "brief": "오버바 부호가 올려진 g", "sbrief": "오버바 부호가 올려진 g"}}}, {"key": "1E23", "mappings": {"default": {"default": "윗점 h"}, "mathspeak": {"default": "마침점 부호가 올려진 h", "brief": "마침점 부호가 올려진 h", "sbrief": "마침점 부호가 올려진 h"}}}, {"key": "1E25", "mappings": {"default": {"default": "아랫점 h"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 h", "brief": "아래에 마침표 부호가 있는 h", "sbrief": "아래에 마침표 부호가 있는 h"}}}, {"key": "1E27", "mappings": {"default": {"default": "분음 부호표 h"}, "mathspeak": {"default": "분음 부호가 올려진 h", "brief": "분음 부호가 올려진 h", "sbrief": "분음 부호가 올려진 h"}}}, {"key": "1E29", "mappings": {"default": {"default": "갈고리형 h"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 h", "brief": "갈고리형 부호가 붙여진 h", "sbrief": "갈고리형 부호가 붙여진 h"}}}, {"key": "1E2B", "mappings": {"default": {"default": "아래 겹온음표 h"}, "mathspeak": {"default": "아래에 겹온음표 부호가 있는 h", "brief": "아래에 겹온음표 부호가 있는 h", "sbrief": "아래에 겹온음표 부호가 있는 h"}}}, {"key": "1E2D", "mappings": {"default": {"default": "아래 물결표 i"}, "mathspeak": {"default": "아래에 물결표가 있는 i", "brief": "아래에 물결표가 있는 i", "sbrief": "아래에 물결표가 있는 i"}}}, {"key": "1E31", "mappings": {"default": {"default": "양음 k"}, "mathspeak": {"default": "양음 부호가 올려진 k", "brief": "양음 부호가 올려진 k", "sbrief": "양음 부호가 올려진 k"}}}, {"key": "1E33", "mappings": {"default": {"default": "아랫점 k"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 k", "brief": "아래에 마침표 부호가 있는 k", "sbrief": "아래에 마침표 부호가 있는 k"}}}, {"key": "1E35", "mappings": {"default": {"default": "언더바 k"}, "mathspeak": {"default": "언더바 부호가 있는 k", "brief": "언더바 부호가 있는 k", "sbrief": "언더바 부호가 있는 k"}}}, {"key": "1E37", "mappings": {"default": {"default": "아랫점 l"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 l", "brief": "아래에 마침표 부호가 있는 l", "sbrief": "아래에 마침표 부호가 있는 l"}}}, {"key": "1E3B", "mappings": {"default": {"default": "언더바 l"}, "mathspeak": {"default": "언더바 부호가 있는 l", "brief": "언더바 부호가 있는 l", "sbrief": "언더바 부호가 있는 l"}}}, {"key": "1E3D", "mappings": {"default": {"default": "아래 곡절음 l"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 l", "brief": "아래에 곡절음 부호가 있는 l", "sbrief": "아래에 곡절음 부호가 있는 l"}}}, {"key": "1E3F", "mappings": {"default": {"default": "양음 m"}, "mathspeak": {"default": "양음 부호가 올려진 m", "brief": "양음 부호가 올려진 m", "sbrief": "양음 부호가 올려진 m"}}}, {"key": "1E41", "mappings": {"default": {"default": "윗점 m"}, "mathspeak": {"default": "마침점 부호가 올려진 m", "brief": "마침점 부호가 올려진 m", "sbrief": "마침점 부호가 올려진 m"}}}, {"key": "1E43", "mappings": {"default": {"default": "아랫점 m"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 m", "brief": "아래에 마침표 부호가 있는 m", "sbrief": "아래에 마침표 부호가 있는 m"}}}, {"key": "1E45", "mappings": {"default": {"default": "윗점 n"}, "mathspeak": {"default": "마침점 부호가 올려진 n", "brief": "마침점 부호가 올려진 n", "sbrief": "마침점 부호가 올려진 n"}}}, {"key": "1E47", "mappings": {"default": {"default": "아랫점 n"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 n", "brief": "아래에 마침표 부호가 있는 n", "sbrief": "아래에 마침표 부호가 있는 n"}}}, {"key": "1E49", "mappings": {"default": {"default": "언더바 n"}, "mathspeak": {"default": "언더바 부호가 있는 n", "brief": "언더바 부호가 있는 n", "sbrief": "언더바 부호가 있는 n"}}}, {"key": "1E4B", "mappings": {"default": {"default": "아래 곡절음 n"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 n", "brief": "아래에 곡절음 부호가 있는 n", "sbrief": "아래에 곡절음 부호가 있는 n"}}}, {"key": "1E55", "mappings": {"default": {"default": "양음 p"}, "mathspeak": {"default": "양음 부호가 올려진 p", "brief": "양음 부호가 올려진 p", "sbrief": "양음 부호가 올려진 p"}}}, {"key": "1E57", "mappings": {"default": {"default": "윗점 p"}, "mathspeak": {"default": "마침점 부호가 올려진 p", "brief": "마침점 부호가 올려진 p", "sbrief": "마침점 부호가 올려진 p"}}}, {"key": "1E59", "mappings": {"default": {"default": "윗점 r"}, "mathspeak": {"default": "마침점 부호가 올려진 r", "brief": "마침점 부호가 올려진 r", "sbrief": "마침점 부호가 올려진 r"}}}, {"key": "1E5B", "mappings": {"default": {"default": "아랫점 r"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 r", "brief": "아래에 마침표 부호가 있는 r", "sbrief": "아래에 마침표 부호가 있는 r"}}}, {"key": "1E5F", "mappings": {"default": {"default": "언더바 r"}, "mathspeak": {"default": "언더바 부호가 있는 r", "brief": "언더바 부호가 있는 r", "sbrief": "언더바 부호가 있는 r"}}}, {"key": "1E61", "mappings": {"default": {"default": "윗점 s"}, "mathspeak": {"default": "마침점 부호가 올려진 s", "brief": "마침점 부호가 올려진 s", "sbrief": "마침점 부호가 올려진 s"}}}, {"key": "1E63", "mappings": {"default": {"default": "아랫점 s"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 s", "brief": "아래에 마침표 부호가 있는 s", "sbrief": "아래에 마침표 부호가 있는 s"}}}, {"key": "1E6B", "mappings": {"default": {"default": "윗점 t"}, "mathspeak": {"default": "마침점 부호가 올려진 t", "brief": "마침점 부호가 올려진 t", "sbrief": "마침점 부호가 올려진 t"}}}, {"key": "1E6D", "mappings": {"default": {"default": "아랫점 t"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 t", "brief": "아래에 마침표 부호가 있는 t", "sbrief": "아래에 마침표 부호가 있는 t"}}}, {"key": "1E6F", "mappings": {"default": {"default": "언더바 t"}, "mathspeak": {"default": "언더바 부호가 있는 t", "brief": "언더바 부호가 있는 t", "sbrief": "언더바 부호가 있는 t"}}}, {"key": "1E71", "mappings": {"default": {"default": "아래 곡절음 t"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 t", "brief": "아래에 곡절음 부호가 있는 t", "sbrief": "아래에 곡절음 부호가 있는 t"}}}, {"key": "1E73", "mappings": {"default": {"default": "아래 분음 부호표 u"}, "mathspeak": {"default": "아래에 분음 부호가 있는 u", "brief": "아래에 분음 부호가 있는 u", "sbrief": "아래에 분음 부호가 있는 u"}}}, {"key": "1E75", "mappings": {"default": {"default": "아래 물결표 u"}, "mathspeak": {"default": "아래에 물결표가 있는 u", "brief": "아래에 물결표가 있는 u", "sbrief": "아래에 물결표가 있는 u"}}}, {"key": "1E77", "mappings": {"default": {"default": "아래 곡절음 u"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 u", "brief": "아래에 곡절음 부호가 있는 u", "sbrief": "아래에 곡절음 부호가 있는 u"}}}, {"key": "1E7D", "mappings": {"default": {"default": "물결표 v"}, "mathspeak": {"default": "물결표가 올려진 v", "brief": "물결표가 올려진 v", "sbrief": "물결표가 올려진 v"}}}, {"key": "1E7F", "mappings": {"default": {"default": "아랫점 v"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 v", "brief": "아래에 마침표 부호가 있는 v", "sbrief": "아래에 마침표 부호가 있는 v"}}}, {"key": "1E81", "mappings": {"default": {"default": "억음 w"}, "mathspeak": {"default": "억음 부호가 올려진 w", "brief": "억음 부호가 올려진 w", "sbrief": "억음 부호가 올려진 w"}}}, {"key": "1E83", "mappings": {"default": {"default": "양음 w"}, "mathspeak": {"default": "양음 부호가 올려진 w", "brief": "양음 부호가 올려진 w", "sbrief": "양음 부호가 올려진 w"}}}, {"key": "1E85", "mappings": {"default": {"default": "분음 부호표 w"}, "mathspeak": {"default": "분음 부호가 올려진 w", "brief": "분음 부호가 올려진 w", "sbrief": "분음 부호가 올려진 w"}}}, {"key": "1E87", "mappings": {"default": {"default": "윗점 w"}, "mathspeak": {"default": "마침점 부호가 올려진 w", "brief": "마침점 부호가 올려진 w", "sbrief": "마침점 부호가 올려진 w"}}}, {"key": "1E89", "mappings": {"default": {"default": "아랫점 w"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 w", "brief": "아래에 마침표 부호가 있는 w", "sbrief": "아래에 마침표 부호가 있는 w"}}}, {"key": "1E8B", "mappings": {"default": {"default": "윗점 x"}, "mathspeak": {"default": "마침점 부호가 올려진 x", "brief": "마침점 부호가 올려진 x", "sbrief": "마침점 부호가 올려진 x"}}}, {"key": "1E8D", "mappings": {"default": {"default": "분음 부호표 x"}, "mathspeak": {"default": "분음 부호가 올려진 x", "brief": "분음 부호가 올려진 x", "sbrief": "분음 부호가 올려진 x"}}}, {"key": "1E8F", "mappings": {"default": {"default": "윗점 y"}, "mathspeak": {"default": "마침점 부호가 올려진 y", "brief": "마침점 부호가 올려진 y", "sbrief": "마침점 부호가 올려진 y"}}}, {"key": "1E91", "mappings": {"default": {"default": "곡절 악센트 z"}, "mathspeak": {"default": "곡절 악센트 부호가 올려진 z", "brief": "곡절 악센트 부호가 올려진 z", "sbrief": "곡절 악센트 부호가 올려진 z"}}}, {"key": "1E93", "mappings": {"default": {"default": "아랫점 z"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 z", "brief": "아래에 마침표 부호가 있는 z", "sbrief": "아래에 마침표 부호가 있는 z"}}}, {"key": "1E95", "mappings": {"default": {"default": "언더바 z"}, "mathspeak": {"default": "언더바 부호가 있는 z", "brief": "언더바 부호가 있는 z", "sbrief": "언더바 부호가 있는 z"}}}, {"key": "1E96", "mappings": {"default": {"default": "언더바 h"}, "mathspeak": {"default": "언더바 부호가 있는 h", "brief": "언더바 부호가 있는 h", "sbrief": "언더바 부호가 있는 h"}}}, {"key": "1E97", "mappings": {"default": {"default": "분음 부호표 t"}, "mathspeak": {"default": "분음 부호가 올려진 t", "brief": "분음 부호가 올려진 t", "sbrief": "분음 부호가 올려진 t"}}}, {"key": "1E98", "mappings": {"default": {"default": "반지 부호 w"}, "mathspeak": {"default": "반지 부호가 올려진 w", "brief": "반지 부호가 올려진 w", "sbrief": "반지 부호가 올려진 w"}}}, {"key": "1E99", "mappings": {"default": {"default": "반지 부호 y"}, "mathspeak": {"default": "반지 부호가 올려진 y", "brief": "반지 부호가 올려진 y", "sbrief": "반지 부호가 올려진 y"}}}, {"key": "1E9A", "mappings": {"default": {"default": "오른쪽 반원 부호 a"}, "mathspeak": {"default": "오른쪽 반원 부호가 올려진 a", "brief": "오른쪽 반원 부호가 올려진 a", "sbrief": "오른쪽 반원 부호가 올려진 a"}}}, {"key": "1EA1", "mappings": {"default": {"default": "아랫점 a"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 a", "brief": "아래에 마침표 부호가 있는 a", "sbrief": "아래에 마침표 부호가 있는 a"}}}, {"key": "1EA3", "mappings": {"default": {"default": "후크 a"}, "mathspeak": {"default": "후크 부호가 올려진 a", "brief": "후크 부호가 올려진 a", "sbrief": "후크 부호가 올려진 a"}}}, {"key": "1EB9", "mappings": {"default": {"default": "아랫점 e"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 e", "brief": "아래에 마침표 부호가 있는 e", "sbrief": "아래에 마침표 부호가 있는 e"}}}, {"key": "1EBB", "mappings": {"default": {"default": "후크 e"}, "mathspeak": {"default": "후크 부호가 올려진 e", "brief": "후크 부호가 올려진 e", "sbrief": "후크 부호가 올려진 e"}}}, {"key": "1EBD", "mappings": {"default": {"default": "물결표 e"}, "mathspeak": {"default": "물결표가 올려진 e", "brief": "물결표가 올려진 e", "sbrief": "물결표가 올려진 e"}}}, {"key": "1EC9", "mappings": {"default": {"default": "후크 i"}, "mathspeak": {"default": "후크 부호가 올려진 i", "brief": "후크 부호가 올려진 i", "sbrief": "후크 부호가 올려진 i"}}}, {"key": "1ECB", "mappings": {"default": {"default": "아랫점 i"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 i", "brief": "아래에 마침표 부호가 있는 i", "sbrief": "아래에 마침표 부호가 있는 i"}}}, {"key": "1ECD", "mappings": {"default": {"default": "아랫점 o"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 o", "brief": "아래에 마침표 부호가 있는 o", "sbrief": "아래에 마침표 부호가 있는 o"}}}, {"key": "1ECF", "mappings": {"default": {"default": "후크 o"}, "mathspeak": {"default": "후크 부호가 올려진 o", "brief": "후크 부호가 올려진 o", "sbrief": "후크 부호가 올려진 o"}}}, {"key": "1EE5", "mappings": {"default": {"default": "아랫점 u"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 u", "brief": "아래에 마침표 부호가 있는 u", "sbrief": "아래에 마침표 부호가 있는 u"}}}, {"key": "1EE7", "mappings": {"default": {"default": "후크 u"}, "mathspeak": {"default": "후크 부호가 올려진 u", "brief": "후크 부호가 올려진 u", "sbrief": "후크 부호가 올려진 u"}}}, {"key": "1EF3", "mappings": {"default": {"default": "억음 y"}, "mathspeak": {"default": "억음 부호가 올려진 y", "brief": "억음 부호가 올려진 y", "sbrief": "억음 부호가 올려진 y"}}}, {"key": "1EF5", "mappings": {"default": {"default": "아랫점 y"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 y", "brief": "아래에 마침표 부호가 있는 y", "sbrief": "아래에 마침표 부호가 있는 y"}}}, {"key": "1EF7", "mappings": {"default": {"default": "후크 y"}, "mathspeak": {"default": "후크 부호가 올려진 y", "brief": "후크 부호가 올려진 y", "sbrief": "후크 부호가 올려진 y"}}}, {"key": "1EF9", "mappings": {"default": {"default": "물결표 y"}, "mathspeak": {"default": "물결표가 올려진 y", "brief": "물결표가 올려진 y", "sbrief": "물결표가 올려진 y"}}}], "ko/symbols/latin-rest.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "이텔릭체 h", "physics": "플랑크 상수"}}, "key": "210E"}, {"key": "0363", "mappings": {"default": {"default": "소문자 a 윗 결합"}}}, {"key": "0364", "mappings": {"default": {"default": "소문자 e 윗 결합"}}}, {"key": "0365", "mappings": {"default": {"default": "소문자 i 윗 결합"}}}, {"key": "0366", "mappings": {"default": {"default": "소문자 o 윗 결합"}}}, {"key": "0367", "mappings": {"default": {"default": "소문자 u 윗 결합"}}}, {"key": "0368", "mappings": {"default": {"default": "소문자 c 윗 결합"}}}, {"key": "0369", "mappings": {"default": {"default": "소문자 d 윗 결합"}}}, {"key": "036A", "mappings": {"default": {"default": "소문자 h 윗 결합"}}}, {"key": "036B", "mappings": {"default": {"default": "소문자 m 윗 결합"}}}, {"key": "036C", "mappings": {"default": {"default": "소문자 r 윗 결합"}}}, {"key": "036D", "mappings": {"default": {"default": "소문자 t 윗 결합"}}}, {"key": "036E", "mappings": {"default": {"default": "소문자 v 윗 결합"}}}, {"key": "036F", "mappings": {"default": {"default": "소문자 x 윗 결합"}}}, {"key": "1D62", "mappings": {"default": {"default": "아래첨자 i"}}}, {"key": "1D63", "mappings": {"default": {"default": "아래첨자 r"}}}, {"key": "1D64", "mappings": {"default": {"default": "아래첨자 u"}}}, {"key": "1D65", "mappings": {"default": {"default": "아래첨자 v"}}}, {"key": "1DCA", "mappings": {"default": {"default": "소문자 r 아래 결합"}}}, {"key": "1DD3", "mappings": {"default": {"default": "a 자를 평평하게 편 문자 윗 결합"}}}, {"key": "1DD4", "mappings": {"default": {"default": "라틴 문자 ae 윗 결합"}}}, {"key": "1DD5", "mappings": {"default": {"default": "라틴 문자 ao 윗 결합"}}}, {"key": "1DD6", "mappings": {"default": {"default": "라틴 문자 av 윗 결합"}}}, {"key": "1DD7", "mappings": {"default": {"default": "라틴 문자 갈고리형 c 윗 결합"}}}, {"key": "1DD8", "mappings": {"default": {"default": "라틴 문자 소문자 d 윗 결합"}}}, {"key": "1DD9", "mappings": {"default": {"default": "라틴 문자 eth 윗 결합"}}}, {"key": "1DDA", "mappings": {"default": {"default": "소문자 g 윗 결합"}}}, {"key": "1DDB", "mappings": {"default": {"default": "대문자 G 윗 결합"}, "mathspeak": {"default": "대문자 G 윗 결합"}}}, {"key": "1DDC", "mappings": {"default": {"default": "소문자 k 윗 결합"}}}, {"key": "1DDD", "mappings": {"default": {"default": "소문자 l 윗 결합"}}}, {"key": "1DDE", "mappings": {"default": {"default": "대문자 L 윗 결합"}, "mathspeak": {"default": "대문자 L 윗 결합"}}}, {"key": "1DDF", "mappings": {"default": {"default": "대문자 M 윗 결합"}, "mathspeak": {"default": "대문자 M 윗 결합"}}}, {"key": "1DE0", "mappings": {"default": {"default": "소문자 n 윗 결합"}}}, {"key": "1DE1", "mappings": {"default": {"default": "대문자 N 윗 결합"}, "mathspeak": {"default": "대문자 N 윗 결합"}}}, {"key": "1DE2", "mappings": {"default": {"default": "대문자 R 윗 결합"}, "mathspeak": {"default": "대문자 R 윗 결합"}}}, {"key": "1DE3", "mappings": {"default": {"default": "라틴 문자 r 로툰다 윗 결합"}}}, {"key": "1DE4", "mappings": {"default": {"default": "소문자 s 윗 결합"}}}, {"key": "1DE5", "mappings": {"default": {"default": "라틴 문자 long s 윗 결합"}}}, {"key": "1DE6", "mappings": {"default": {"default": "소문자 z 윗 결합"}}}, {"key": "2071", "mappings": {"default": {"default": "위첨자 i"}}}, {"key": "207F", "mappings": {"default": {"default": "위첨자 n"}}}, {"key": "2090", "mappings": {"default": {"default": "아래첨자 a"}}}, {"key": "2091", "mappings": {"default": {"default": "아래첨자 e"}}}, {"key": "2092", "mappings": {"default": {"default": "아래첨자 o"}}}, {"key": "2093", "mappings": {"default": {"default": "아래첨자 x"}}}, {"key": "2094", "mappings": {"default": {"default": "아래첨자 슈와"}}}, {"key": "2095", "mappings": {"default": {"default": "아래첨자 h"}}}, {"key": "2096", "mappings": {"default": {"default": "아래첨자 k"}}}, {"key": "2097", "mappings": {"default": {"default": "아래첨자 l"}}}, {"key": "2098", "mappings": {"default": {"default": "아래첨자 m"}}}, {"key": "2099", "mappings": {"default": {"default": "아래첨자 n"}}}, {"key": "209A", "mappings": {"default": {"default": "아래첨자 p"}}}, {"key": "209B", "mappings": {"default": {"default": "아래첨자 s"}}}, {"key": "209C", "mappings": {"default": {"default": "아래첨자 t"}}}, {"key": "2C7C", "mappings": {"default": {"default": "아래첨자 j"}}}, {"key": "1F12A", "mappings": {"default": {"default": "거북이 등껍질형 대괄호로 묶은 대문자 S"}, "mathspeak": {"default": "거북이 등껍질형 대괄호로 묶은 대문자 S"}}}, {"key": "1F12B", "mappings": {"default": {"default": "원 안의 이텔릭체 대문자 C"}, "mathspeak": {"default": "원 안의 이텔릭체 대문자 C"}}}, {"key": "1F12C", "mappings": {"default": {"default": "원 안에 이텔릭체 대문자 R"}, "mathspeak": {"default": "원 안에 이텔릭체 대문자 R"}}}, {"key": "1F18A", "mappings": {"default": {"default": "사각형 안에 x자로 교차된 대문자 P"}, "mathspeak": {"default": "사각형 안에 x자로 교차된 대문자 P"}}}], "ko/symbols/latin-upper-double-accent.min": [{"locale": "ko"}, {"key": "01D5", "mappings": {"default": {"default": "두 점 위에 선이 있는 대문자 U"}, "mathspeak": {"default": "두 점 위에 선이 있는 대문자 U"}}}, {"key": "01D7", "mappings": {"default": {"default": "두 점 위에 양음 부호가 있는 대문자 U"}, "mathspeak": {"default": "두 점 위에 양음 부호가 있는 대문자 U"}}}, {"key": "01D9", "mappings": {"default": {"default": "두 점 위에 반대 곡절 부호가 있는 대문자 U"}, "mathspeak": {"default": "두 점 위에 반대 곡절 부호가 있는 대문자 U"}}}, {"key": "01DB", "mappings": {"default": {"default": "두 점 위에 억음 부호가 있는 대문자 U"}, "mathspeak": {"default": "두 점 위에 억음 부호가 있는 대문자 U"}}}, {"key": "01DE", "mappings": {"default": {"default": "두 점 위에 선이 있는 대문자 A"}, "mathspeak": {"default": "두 점 위에 선이 있는 대문자 A"}}}, {"key": "01E0", "mappings": {"default": {"default": "한 점 위에 선이 있는 대문자 A"}, "mathspeak": {"default": "한 점 위에 선이 있는 대문자 A"}}}, {"key": "01EC", "mappings": {"default": {"default": "오고넥과 위에 선이 있는 대문자 O"}, "mathspeak": {"default": "오고넥과 위에 선이 있는 대문자 O"}}}, {"key": "01FA", "mappings": {"default": {"default": "링 위에 양음 부호가 있는 대문자 A"}, "mathspeak": {"default": "링 위에 양음 부호가 있는 대문자 A"}}}, {"key": "022A", "mappings": {"default": {"default": "두 점 위에 선이 있는 대문자 O"}, "mathspeak": {"default": "두 점 위에 선이 있는 대문자 O"}}}, {"key": "022C", "mappings": {"default": {"default": "물결표 위에 선이 있는 대문자 O"}, "mathspeak": {"default": "물결표 위에 선이 있는 대문자 O"}}}, {"key": "0230", "mappings": {"default": {"default": "한 점 위에 선이 있는 대문자 O"}, "mathspeak": {"default": "한 점 위에 선이 있는 대문자 O"}}}, {"key": "1E08", "mappings": {"default": {"default": "세디유 위에 양음 부호가 있는 대문자 C"}, "mathspeak": {"default": "세디유 위에 양음 부호가 있는 대문자 C"}}}, {"key": "1E14", "mappings": {"default": {"default": "선 위에 억음 부호가 있는 대문자 E"}, "mathspeak": {"default": "선 위에 억음 부호가 있는 대문자 E"}}}, {"key": "1E16", "mappings": {"default": {"default": "선 위에 양음 부호가 있는 대문자 E"}, "mathspeak": {"default": "선 위에 양음 부호가 있는 대문자 E"}}}, {"key": "1E1C", "mappings": {"default": {"default": "세디유 위에 단음 부호가 있는 대문자 E"}, "mathspeak": {"default": "세디유 위에 단음 부호가 있는 대문자 E"}}}, {"key": "1E2E", "mappings": {"default": {"default": "두 점 위에 양음 부호가 있는 대문자 I"}, "mathspeak": {"default": "두 점 위에 양음 부호가 있는 대문자 I"}}}, {"key": "1E38", "mappings": {"default": {"default": "아래점과 위에 선이 있는 대문자 L"}, "mathspeak": {"default": "아래점과 위에 선이 있는 대문자 L"}}}, {"key": "1E4C", "mappings": {"default": {"default": "물결표 위에 양음 부호가 있는 대문자 O"}, "mathspeak": {"default": "물결표 위에 양음 부호가 있는 대문자 O"}}}, {"key": "1E4E", "mappings": {"default": {"default": "물결표 위에 두 점이 있는 대문자 O"}, "mathspeak": {"default": "물결표 위에 두 점이 있는 대문자 O"}}}, {"key": "1E50", "mappings": {"default": {"default": "선 위에 억음 부호가 있는 대문자 O"}, "mathspeak": {"default": "선 위에 억음 부호가 있는 대문자 O"}}}, {"key": "1E52", "mappings": {"default": {"default": "선 위에 양음 부호가 있는 대문자 O"}, "mathspeak": {"default": "선 위에 양음 부호가 있는 대문자 O"}}}, {"key": "1E5C", "mappings": {"default": {"default": "아래점과 위에 선이 있는 대문자 R"}, "mathspeak": {"default": "아래점과 위에 선이 있는 대문자 R"}}}, {"key": "1E64", "mappings": {"default": {"default": "양음 부호 위에 점이 있는 대문자 S"}, "mathspeak": {"default": "양음 부호 위에 점이 있는 대문자 S"}}}, {"key": "1E66", "mappings": {"default": {"default": "반대 곡절 부호 위에 점이 있는 대문자 S"}, "mathspeak": {"default": "반대 곡절 부호 위에 점이 있는 대문자 S"}}}, {"key": "1E68", "mappings": {"default": {"default": "아래점과 위에 점이 있는 대문자 S"}, "mathspeak": {"default": "아래점과 위에 점이 있는 대문자 S"}}}, {"key": "1E78", "mappings": {"default": {"default": "물결표 위에 양음 부호가 있는 대문자 U"}, "mathspeak": {"default": "물결표 위에 양음 부호가 있는 대문자 U"}}}, {"key": "1E7A", "mappings": {"default": {"default": "선 위에 두 점이 있는 대문자 U"}, "mathspeak": {"default": "선 위에 두 점이 있는 대문자 U"}}}, {"key": "1EA4", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 대문자 A"}, "mathspeak": {"default": "곡절 부호와 양음 부호가 있는 대문자 A"}}}, {"key": "1EA6", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 대문자 A"}, "mathspeak": {"default": "곡절 부호와 억음 부호가 있는 대문자 A"}}}, {"key": "1EA8", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 대문자 A"}, "mathspeak": {"default": "곡절 부호와 후크가 위에 있는 대문자 A"}}}, {"key": "1EAA", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 대문자 A"}, "mathspeak": {"default": "곡절 부호와 물결표가 있는 대문자 A"}}}, {"key": "1EAC", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 대문자 A"}, "mathspeak": {"default": "곡절 부호와 아래점이 있는 대문자 A"}}}, {"key": "1EAE", "mappings": {"default": {"default": "단음 부호와 양음 부호가 있는 대문자 A"}, "mathspeak": {"default": "단음 부호와 양음 부호가 있는 대문자 A"}}}, {"key": "1EB0", "mappings": {"default": {"default": "단음 부호와 억음 부호가 있는 대문자 A"}, "mathspeak": {"default": "단음 부호와 억음 부호가 있는 대문자 A"}}}, {"key": "1EB2", "mappings": {"default": {"default": "단음 부호 위에 후크가 있는 대문자 A"}, "mathspeak": {"default": "단음 부호 위에 후크가 있는 대문자 A"}}}, {"key": "1EB4", "mappings": {"default": {"default": "단음 부호와 물결표가 있는 대문자 A"}, "mathspeak": {"default": "단음 부호와 물결표가 있는 대문자 A"}}}, {"key": "1EB6", "mappings": {"default": {"default": "단음 부호와 아래점이 있는 대문자 A"}, "mathspeak": {"default": "단음 부호와 아래점이 있는 대문자 A"}}}, {"key": "1EBE", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 대문자 E"}, "mathspeak": {"default": "곡절 부호와 양음 부호가 있는 대문자 E"}}}, {"key": "1EC0", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 대문자 E"}, "mathspeak": {"default": "곡절 부호와 억음 부호가 있는 대문자 E"}}}, {"key": "1EC2", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 대문자 E"}, "mathspeak": {"default": "곡절 부호와 후크가 위에 있는 대문자 E"}}}, {"key": "1EC4", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 대문자 E"}, "mathspeak": {"default": "곡절 부호와 물결표가 있는 대문자 E"}}}, {"key": "1EC6", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 대문자 E"}, "mathspeak": {"default": "곡절 부호와 아래점이 있는 대문자 E"}}}, {"key": "1ED0", "mappings": {"default": {"default": "곡절 부호와 양음 부호가 있는 대문자 O"}, "mathspeak": {"default": "곡절 부호와 양음 부호가 있는 대문자 O"}}}, {"key": "1ED2", "mappings": {"default": {"default": "곡절 부호와 억음 부호가 있는 대문자 O"}, "mathspeak": {"default": "곡절 부호와 억음 부호가 있는 대문자 O"}}}, {"key": "1ED4", "mappings": {"default": {"default": "곡절 부호와 후크가 위에 있는 대문자 O"}, "mathspeak": {"default": "곡절 부호와 후크가 위에 있는 대문자 O"}}}, {"key": "1ED6", "mappings": {"default": {"default": "곡절 부호와 물결표가 있는 대문자 O"}, "mathspeak": {"default": "곡절 부호와 물결표가 있는 대문자 O"}}}, {"key": "1ED8", "mappings": {"default": {"default": "곡절 부호와 아래점이 있는 대문자 O"}, "mathspeak": {"default": "곡절 부호와 아래점이 있는 대문자 O"}}}, {"key": "1EDA", "mappings": {"default": {"default": "양음 부호와 프라임이 있는 대문자 O"}, "mathspeak": {"default": "양음 부호와 프라임이 있는 대문자 O"}}}, {"key": "1EDC", "mappings": {"default": {"default": "억음 부호와 프라임이 있는 대문자 O"}, "mathspeak": {"default": "억음 부호와 프라임이 있는 대문자 O"}}}, {"key": "1EDE", "mappings": {"default": {"default": "후크 위에 프라임이 있는 대문자 O"}, "mathspeak": {"default": "후크 위에 프라임이 있는 대문자 O"}}}, {"key": "1EE0", "mappings": {"default": {"default": "물결표와 프라임이 있는 대문자 O"}, "mathspeak": {"default": "물결표와 프라임이 있는 대문자 O"}}}, {"key": "1EE2", "mappings": {"default": {"default": "아래점과 프라임이 있는 대문자 O"}, "mathspeak": {"default": "아래점과 프라임이 있는 대문자 O"}}}, {"key": "1EE8", "mappings": {"default": {"default": "양음 부호와 프라임이 있는 대문자 U"}, "mathspeak": {"default": "양음 부호와 프라임이 있는 대문자 U"}}}, {"key": "1EEA", "mappings": {"default": {"default": "억음 부호와 프라임이 있는 대문자 U"}, "mathspeak": {"default": "억음 부호와 프라임이 있는 대문자 U"}}}, {"key": "1EEC", "mappings": {"default": {"default": "후크 위에 프라임이 있는 대문자 U"}, "mathspeak": {"default": "후크 위에 프라임이 있는 대문자 U"}}}, {"key": "1EEE", "mappings": {"default": {"default": "물결표와 프라임이 있는 대문자 U"}, "mathspeak": {"default": "물결표와 프라임이 있는 대문자 U"}}}, {"key": "1EF0", "mappings": {"default": {"default": "아래점과 프라임이 있는 대문자 U"}, "mathspeak": {"default": "아래점과 프라임이 있는 대문자 U"}}}], "ko/symbols/latin-upper-single-accent.min": [{"locale": "ko"}, {"key": "00C0", "mappings": {"default": {"default": "대문자 억음 A"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 A", "brief": "억음 부호가 올려진 대문자 A", "sbrief": "억음 부호가 올려진 대문자 A"}}}, {"key": "00C1", "mappings": {"default": {"default": "대문자 양음 A"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 A", "brief": "양음 부호가 올려진 대문자 A", "sbrief": "양음 부호가 올려진 대문자 A"}}}, {"key": "00C2", "mappings": {"default": {"default": "대문자 곡절음 A"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 A", "brief": "삽입 기호가 올려진 대문자 A", "sbrief": "삽입 기호가 올려진 대문자 A"}}}, {"key": "00C3", "mappings": {"default": {"default": "대문자 물결표 A"}, "mathspeak": {"default": "물결표가 올려진 대문자 A", "brief": "물결표가 올려진 대문자 A", "sbrief": "물결표가 올려진 대문자 A"}}}, {"key": "00C4", "mappings": {"default": {"default": "대문자 분음 부호표 A"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 A", "brief": "분음 부호가 올려진 대문자 A", "sbrief": "분음 부호가 올려진 대문자 A"}}}, {"key": "00C5", "mappings": {"default": {"default": "대문자 반지 부호 A"}, "mathspeak": {"default": "반지 부호가 올려진 대문자 A", "brief": "반지 부호가 올려진 대문자 A", "sbrief": "반지 부호가 올려진 대문자 A"}}}, {"key": "00C7", "mappings": {"default": {"default": "대문자 갈고리형 C"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 C", "brief": "갈고리형 부호가 붙여진 대문자 C", "sbrief": "갈고리형 부호가 붙여진 대문자 C"}}}, {"key": "00C8", "mappings": {"default": {"default": "대문자 억음 E"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 E", "brief": "억음 부호가 올려진 대문자 E", "sbrief": "억음 부호가 올려진 대문자 E"}}}, {"key": "00C9", "mappings": {"default": {"default": "대문자 양음 E"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 E", "brief": "양음 부호가 올려진 대문자 E", "sbrief": "양음 부호가 올려진 대문자 E"}}}, {"key": "00CA", "mappings": {"default": {"default": "대문자 곡절음 E"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 E", "brief": "삽입 기호가 올려진 대문자 E", "sbrief": "삽입 기호가 올려진 대문자 E"}}}, {"key": "00CB", "mappings": {"default": {"default": "대문자 분음 부호표 E"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 E", "brief": "분음 부호가 올려진 대문자 E", "sbrief": "분음 부호가 올려진 대문자 E"}}}, {"key": "00CC", "mappings": {"default": {"default": "대문자 억음 I"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 I", "brief": "억음 부호가 올려진 대문자 I", "sbrief": "억음 부호가 올려진 대문자 I"}}}, {"key": "00CD", "mappings": {"default": {"default": "대문자 양음 I"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 I", "brief": "양음 부호가 올려진 대문자 I", "sbrief": "양음 부호가 올려진 대문자 I"}}}, {"key": "00CE", "mappings": {"default": {"default": "대문자 곡절음 I"}, "mathspeak": {"default": "삽입 기호가  올려진 대문자 I", "brief": "삽입 기호가 올려진 대문자 I", "sbrief": "삽입 기호가 올려진 대문자 I"}}}, {"key": "00CF", "mappings": {"default": {"default": "대문자 분음 부호표 I"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 I", "brief": "분음 부호가 올려진 대문자 I", "sbrief": "분음 부호가 올려진 대문자 I"}}}, {"key": "00D1", "mappings": {"default": {"default": "대문자 물결표 N"}, "mathspeak": {"default": "물결표가 올려진 대문자 N", "brief": "물결표가 올려진 대문자 N", "sbrief": "물결표가 올려진 대문자 N"}}}, {"key": "00D2", "mappings": {"default": {"default": "대문자 억음 O"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 O", "brief": "억음 부호가 올려진 대문자 O", "sbrief": "억음 부호가 올려진 대문자 O"}}}, {"key": "00D3", "mappings": {"default": {"default": "대문자 양음 O"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 O", "brief": "양음 부호가 올려진 대문자 O", "sbrief": "양음 부호가 올려진 대문자 O"}}}, {"key": "00D4", "mappings": {"default": {"default": "대문자 곡절음 O"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 O", "brief": "삽입 기호가 올려진 대문자 O", "sbrief": "삽입 기호가 올려진 대문자 O"}}}, {"key": "00D5", "mappings": {"default": {"default": "대문자 물결표 O"}, "mathspeak": {"default": "물결표가 올려진 대문자 O", "brief": "물결표가 올려진 대문자 O", "sbrief": "물결표가 올려진 대문자 O"}}}, {"key": "00D6", "mappings": {"default": {"default": "대문자 분음 부호표 O"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 O", "brief": "분음 부호가 올려진 대문자 O", "sbrief": "분음 부호가 올려진 대문자 O"}}}, {"key": "00D9", "mappings": {"default": {"default": "대문자 억음 U"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 U", "brief": "억음 부호가 올려진 대문자 U", "sbrief": "억음 부호가 올려진 대문자 U"}}}, {"key": "00DA", "mappings": {"default": {"default": "대문자 양음 U"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 U", "brief": "양음 부호가 올려진 대문자 U", "sbrief": "양음 부호가 올려진 대문자 U"}}}, {"key": "00DB", "mappings": {"default": {"default": "대문자 곡절음 U"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 U", "brief": "삽입 기호가 올려진 대문자 U", "sbrief": "삽입 기호가 올려진 대문자 U"}}}, {"key": "00DC", "mappings": {"default": {"default": "대문자 분음 부호표 U"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 U", "brief": "분음 부호가 올려진 대문자 U", "sbrief": "분음 부호가 올려진 대문자 U"}}}, {"key": "00DD", "mappings": {"default": {"default": "대문자 양음 Y"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 Y", "brief": "양음 부호가 올려진 대문자 Y", "sbrief": "양음 부호가 올려진 대문자 Y"}}}, {"key": "0100", "mappings": {"default": {"default": "대문자 오버바 A"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 A", "brief": "오버바 부호가 올려진 대문자 A", "sbrief": "오버바 부호가 올려진 대문자 A"}}}, {"key": "0102", "mappings": {"default": {"default": "대문자 겹온음표 A"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 A", "brief": "겹온음표 부호가 올려진 대문자 A", "sbrief": "겹온음표 부호가 올려진 대문자 A"}}}, {"key": "0104", "mappings": {"default": {"default": "대문자 아가뇩 A"}, "mathspeak": {"default": "아가뇩 부호가 있는 대문자 A", "brief": "아가뇩 부호가 있는 대문자 A", "sbrief": "아가뇩 부호가 있는 대문자 A"}}}, {"key": "0106", "mappings": {"default": {"default": "대문자 양음 C"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 C", "brief": "양음 부호가 올려진 대문자 C", "sbrief": "양음 부호가 올려진 대문자 C"}}}, {"key": "0108", "mappings": {"default": {"default": "대문자 곡절음 C"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 C", "brief": "삽입 기호가 올려진 대문자 C", "sbrief": "삽입 기호가 올려진 대문자 C"}}}, {"key": "010A", "mappings": {"default": {"default": "대문자 윗점 C"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 C", "brief": "마침점 부호가 올려진 대문자 C", "sbrief": "마침점 부호가 올려진 대문자 C"}}}, {"key": "010C", "mappings": {"default": {"default": "대문자 카론 C"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 C", "brief": "카론 부호가 올려진 대문자 C", "sbrief": "카론 부호가 올려진 대문자 C"}}}, {"key": "010E", "mappings": {"default": {"default": "대문자 카론 D"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 D", "brief": "카론 부호가 올려진 대문자 D", "sbrief": "카론 부호가 올려진 대문자 D"}}}, {"key": "0112", "mappings": {"default": {"default": "대문자 오버바 E"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 E", "brief": "오버바 부호가 올려진 대문자 E", "sbrief": "오버바 부호가 올려진 대문자 E"}}}, {"key": "0114", "mappings": {"default": {"default": "대문자 겹온음표 E"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 E", "brief": "겹온음표 부호가 올려진 대문자 E", "sbrief": "겹온음표 부호가 올려진 대문자 E"}}}, {"key": "0116", "mappings": {"default": {"default": "대문자 윗점 E"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 E", "brief": "마침점 부호가 올려진 대문자 E", "sbrief": "마침점 부호가 올려진 대문자 E"}}}, {"key": "0118", "mappings": {"default": {"default": "대문자 아가뇩 E"}, "mathspeak": {"default": "아가뇩 부호가 있는 대문자 E", "brief": "아가뇩 부호가 있는 대문자 E", "sbrief": "아가뇩 부호가 있는 대문자 E"}}}, {"key": "011A", "mappings": {"default": {"default": "대문자 카론 E"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 E", "brief": "카론 부호가 올려진 대문자 E", "sbrief": "카론 부호가 올려진 대문자 E"}}}, {"key": "011C", "mappings": {"default": {"default": "대문자 곡절음 G"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 G", "brief": "삽입 기호가 올려진 대문자 G", "sbrief": "삽입 기호가 올려진 대문자 G"}}}, {"key": "011E", "mappings": {"default": {"default": "대문자 겹온음표 G"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 G", "brief": "겹온음표 부호가 올려진 대문자 G", "sbrief": "겹온음표 부호가 올려진 대문자 G"}}}, {"key": "0120", "mappings": {"default": {"default": "대문자 윗점 G"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 G", "brief": "마침점 부호가 올려진 대문자 G", "sbrief": "마침점 부호가 올려진 대문자 G"}}}, {"key": "0122", "mappings": {"default": {"default": "대문자 갈고리형 G"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 G", "brief": "갈고리형 부호가 붙여진 대문자 G", "sbrief": "갈고리형 부호가 붙여진 대문자 G"}}}, {"key": "0124", "mappings": {"default": {"default": "대문자 곡절음 H"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 H", "brief": "삽입 기호가 올려진 대문자 H", "sbrief": "삽입 기호가 올려진 대문자 H"}}}, {"key": "0128", "mappings": {"default": {"default": "대문자 물결표 I"}, "mathspeak": {"default": "물결표가 올려진 대문자 I", "brief": "물결표가 올려진 대문자 I", "sbrief": "물결표가 올려진 대문자 I"}}}, {"key": "012A", "mappings": {"default": {"default": "대문자 오버바 I"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 I", "brief": "오버바 부호가 올려진 대문자 I", "sbrief": "오버바 부호가 올려진 대문자 I"}}}, {"key": "012C", "mappings": {"default": {"default": "대문자 겹온음표 I"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 I", "brief": "겹온음표 부호가 올려진 대문자 I", "sbrief": "겹온음표 부호가 올려진 대문자 I"}}}, {"key": "012E", "mappings": {"default": {"default": "대문자 아가뇩 I"}, "mathspeak": {"default": "아가뇩 부호가 있는 대문자 I", "brief": "아가뇩 부호가 있는 대문자 I", "sbrief": "아가뇩 부호가 있는 대문자 I"}}}, {"key": "0130", "mappings": {"default": {"default": "대문자 윗점 I"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 I", "brief": "마침점 부호가 올려진 대문자 I", "sbrief": "마침점 부호가 올려진 대문자 I"}}}, {"key": "0134", "mappings": {"default": {"default": "대문자 곡절음 J"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 J", "brief": "삽입 기호가 올려진 대문자 J", "sbrief": "삽입 기호가 올려진 대문자 J"}}}, {"key": "0136", "mappings": {"default": {"default": "대문자 갈고리형 K"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 K", "brief": "갈고리형 부호가 붙여진 대문자 K", "sbrief": "갈고리형 부호가 붙여진 대문자 K"}}}, {"key": "0139", "mappings": {"default": {"default": "대문자 양음 L"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 L", "brief": "양음 부호가 올려진 대문자 L", "sbrief": "양음 부호가 올려진 대문자 L"}}}, {"key": "013B", "mappings": {"default": {"default": "대문자 갈고리형 L"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 L", "brief": "갈고리형 부호가 붙여진 대문자 L", "sbrief": "갈고리형 부호가 붙여진 대문자 L"}}}, {"key": "013D", "mappings": {"default": {"default": "대문자 카론 L"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 L", "brief": "카론 부호가 올려진 대문자 L", "sbrief": "카론 부호가 올려진 대문자 L"}}}, {"key": "013F", "mappings": {"default": {"default": "대문자 중간점 L"}, "mathspeak": {"default": "중간점 부호가 있는 대문자 L", "brief": "중간점 부호가 있는 대문자 L", "sbrief": "중간점 부호가 있는 대문자 L"}}}, {"key": "0143", "mappings": {"default": {"default": "대문자 양음 N"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 N", "brief": "양음 부호가 올려진 대문자 N", "sbrief": "양음 부호가 올려진 대문자 N"}}}, {"key": "0145", "mappings": {"default": {"default": "대문자 갈고리형 N"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 N", "brief": "갈고리형 부호가 붙여진 대문자 N", "sbrief": "갈고리형 부호가 붙여진 대문자 N"}}}, {"key": "0147", "mappings": {"default": {"default": "대문자 카론 N"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 N", "brief": "카론 부호가 올려진 대문자 N", "sbrief": "카론 부호가 올려진 대문자 N"}}}, {"key": "014C", "mappings": {"default": {"default": "대문자 오버바 O"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 O", "brief": "오버바 부호가 올려진 대문자 O", "sbrief": "오버바 부호가 올려진 대문자 O"}}}, {"key": "014E", "mappings": {"default": {"default": "대문자 겹온음표 O"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 O", "brief": "겹온음표 부호가 올려진 대문자 O", "sbrief": "겹온음표 부호가 올려진 대문자 O"}}}, {"key": "0150", "mappings": {"default": {"default": "대문자 이중 양음 O"}, "mathspeak": {"default": "이중 양음 부호가 올려진 대문자 O", "brief": "이중 양음 부호가 올려진 대문자 O", "sbrief": "이중 양음 부호가 올려진 대문자 O"}}}, {"key": "0154", "mappings": {"default": {"default": "대문자 양음 R"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 R", "brief": "양음 부호가 올려진 대문자 R", "sbrief": "양음 부호가 올려진 대문자 R"}}}, {"key": "0156", "mappings": {"default": {"default": "대문자 갈고리형 R"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 R", "brief": "갈고리형 부호가 붙여진 대문자 R", "sbrief": "갈고리형 부호가 붙여진 대문자 R"}}}, {"key": "0158", "mappings": {"default": {"default": "대문자 카론 R"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 R", "brief": "카론 부호가 올려진 대문자 R", "sbrief": "카론 부호가 올려진 대문자 R"}}}, {"key": "015A", "mappings": {"default": {"default": "대문자 양음 S"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 S", "brief": "양음 부호가 올려진 대문자 S", "sbrief": "양음 부호가 올려진 대문자 S"}}}, {"key": "015C", "mappings": {"default": {"default": "대문자 곡절음 S"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 S", "brief": "삽입 기호가 올려진 대문자 S", "sbrief": "삽입 기호가 올려진 대문자 S"}}}, {"key": "015E", "mappings": {"default": {"default": "대문자 갈고리형 S"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 S", "brief": "갈고리형 부호가 붙여진 대문자 S", "sbrief": "갈고리형 부호가 붙여진 대문자 S"}}}, {"key": "0160", "mappings": {"default": {"default": "대문자 카론 S"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 S", "brief": "카론 부호가 올려진 대문자 S", "sbrief": "카론 부호가 올려진 대문자 S"}}}, {"key": "0162", "mappings": {"default": {"default": "대문자 갈고리형 T"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 T", "brief": "갈고리형 부호가 붙여진 대문자 T", "sbrief": "갈고리형 부호가 붙여진 대문자 T"}}}, {"key": "0164", "mappings": {"default": {"default": "대문자 카론 T"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 T", "brief": "카론 부호가 올려진 대문자 T", "sbrief": "카론 부호가 올려진 대문자 T"}}}, {"key": "0168", "mappings": {"default": {"default": "대문자 물결표 U"}, "mathspeak": {"default": "물결표가 올려진 대문자 U", "brief": "물결표가 올려진 대문자 U", "sbrief": "물결표가 올려진 대문자 U"}}}, {"key": "016A", "mappings": {"default": {"default": "대문자 오버바 U"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 U", "brief": "오버바 부호가 올려진 대문자 U", "sbrief": "오버바 부호가 올려진 대문자 U"}}}, {"key": "016C", "mappings": {"default": {"default": "대문자 겹온음표 U"}, "mathspeak": {"default": "겹온음표 부호가 올려진 대문자 U", "brief": "겹온음표 부호가 올려진 대문자 U", "sbrief": "겹온음표 부호가 올려진 대문자 U"}}}, {"key": "016E", "mappings": {"default": {"default": "대문자 반지 부호 U"}, "mathspeak": {"default": "반지 부호가 올려진 대문자 U", "brief": "반지 부호가 올려진 대문자 U", "sbrief": "반지 부호가 올려진 대문자 U"}}}, {"key": "0170", "mappings": {"default": {"default": "대문자 이중 양음 U"}, "mathspeak": {"default": "이중 양음 부호가 올려진 대문자 U", "brief": "이중 양음 부호가 올려진 대문자 U", "sbrief": "이중 양음 부호가 올려진 대문자 U"}}}, {"key": "0172", "mappings": {"default": {"default": "대문자 아가뇩 U"}, "mathspeak": {"default": "아가뇩 부호가 있는 대문자 U", "brief": "아가뇩 부호가 있는 대문자 U", "sbrief": "아가뇩 부호가 있는 대문자 U"}}}, {"key": "0174", "mappings": {"default": {"default": "대문자 곡절음 W"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 W", "brief": "삽입 기호가 올려진 대문자 W", "sbrief": "삽입 기호가 올려진 대문자 W"}}}, {"key": "0176", "mappings": {"default": {"default": "대문자 곡절음 Y"}, "mathspeak": {"default": "삽입 기호가 올려진 대문자 Y", "brief": "삽입 기호가 올려진 대문자 Y", "sbrief": "삽입 기호가 올려진 대문자 Y"}}}, {"key": "0178", "mappings": {"default": {"default": "대문자 분음 부호표 Y"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 Y", "brief": "분음 부호가 올려진 대문자 Y", "sbrief": "분음 부호가 올려진 대문자 Y"}}}, {"key": "0179", "mappings": {"default": {"default": "대문자 양음 Z"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 Z", "brief": "양음 부호가 올려진 대문자 Z", "sbrief": "양음 부호가 올려진 대문자 Z"}}}, {"key": "017B", "mappings": {"default": {"default": "대문자 윗점 Z"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 Z", "brief": "마침점 부호가 올려진 대문자 Z", "sbrief": "마침점 부호가 올려진 대문자 Z"}}}, {"key": "017D", "mappings": {"default": {"default": "대문자 카론 Z"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 Z", "brief": "카론 부호가 올려진 대문자 Z", "sbrief": "카론 부호가 올려진 대문자 Z"}}}, {"key": "01CD", "mappings": {"default": {"default": "대문자 카론 A"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 A", "brief": "카론 부호가 올려진 대문자 A", "sbrief": "카론 부호가 올려진 대문자 A"}}}, {"key": "01CF", "mappings": {"default": {"default": "대문자 카론 I"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 I", "brief": "카론 부호가 올려진 대문자 I", "sbrief": "카론 부호가 올려진 대문자 I"}}}, {"key": "01D1", "mappings": {"default": {"default": "대문자 카론 O"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 O", "brief": "카론 부호가 올려진 대문자 O", "sbrief": "카론 부호가 올려진 대문자 O"}}}, {"key": "01D3", "mappings": {"default": {"default": "대문자 카론 U"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 U", "brief": "카론 부호가 올려진 대문자 U", "sbrief": "카론 부호가 올려진 대문자 U"}}}, {"key": "01E6", "mappings": {"default": {"default": "대문자 카론 G"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 G", "brief": "카론 부호가 올려진 대문자 G", "sbrief": "카론 부호가 올려진 대문자 G"}}}, {"key": "01E8", "mappings": {"default": {"default": "대문자 카론 K"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 K", "brief": "카론 부호가 올려진 대문자 K", "sbrief": "카론 부호가 올려진 대문자 K"}}}, {"key": "01EA", "mappings": {"default": {"default": "대문자 아가뇩 O"}, "mathspeak": {"default": "아가뇩 부호가 있는 대문자 O", "brief": "아가뇩 부호가 있는 대문자 O", "sbrief": "아가뇩 부호가 있는 대문자 O"}}}, {"key": "01F4", "mappings": {"default": {"default": "대문자 양음 G"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 G", "brief": "양음 부호가 올려진 대문자 G", "sbrief": "양음 부호가 올려진 대문자 G"}}}, {"key": "01F8", "mappings": {"default": {"default": "대문자 억음 N"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 N", "brief": "억음 부호가 올려진 대문자 N", "sbrief": "억음 부호가 올려진 대문자 N"}}}, {"key": "0200", "mappings": {"default": {"default": "대문자 이중 억음 A"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 A", "brief": "이중 억음 부호가 올려진 대문자 A", "sbrief": "이중 억음 부호가 올려진 대문자 A"}}}, {"key": "0202", "mappings": {"default": {"default": "대문자 반전 겹온음표 A"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 A", "brief": "반전된 겹온음표 부호가 올려진 대문자 A", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 A"}}}, {"key": "0204", "mappings": {"default": {"default": "대문자 이중 억음 E"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 E", "brief": "이중 억음 부호가 올려진 대문자 E", "sbrief": "이중 억음 부호가 올려진 대문자 E"}}}, {"key": "0206", "mappings": {"default": {"default": "대문자 반전 겹온음표 E"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 E", "brief": "반전된 겹온음표 부호가 올려진 대문자 E", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 E"}}}, {"key": "0208", "mappings": {"default": {"default": "대문자 이중 억음 I"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 I", "brief": "이중 억음 부호가 올려진 대문자 I", "sbrief": "이중 억음 부호가 올려진 대문자 I"}}}, {"key": "020A", "mappings": {"default": {"default": "대문자 반전 겹온음표 I"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 I", "brief": "반전된 겹온음표 부호가 올려진 대문자 I", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 I"}}}, {"key": "020C", "mappings": {"default": {"default": "대문자 이중 억음 O"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 O", "brief": "이중 억음 부호가 올려진 대문자 O", "sbrief": "이중 억음 부호가 올려진 대문자 O"}}}, {"key": "020E", "mappings": {"default": {"default": "대문자 반전 겹온음표 O"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 O", "brief": "반전된 겹온음표 부호가 올려진 대문자 O", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 O"}}}, {"key": "0210", "mappings": {"default": {"default": "대문자 이중 억음 R"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 R", "brief": "이중 억음 부호가 올려진 대문자 R", "sbrief": "이중 억음 부호가 올려진 대문자 R"}}}, {"key": "0212", "mappings": {"default": {"default": "대문자 반전 겹온음표 R"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 R", "brief": "반전된 겹온음표 부호가 올려진 대문자 R", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 R"}}}, {"key": "0214", "mappings": {"default": {"default": "대문자 이중 억음 U"}, "mathspeak": {"default": "이중 억음 부호가 올려진 대문자 U", "brief": "이중 억음 부호가 올려진 대문자 U", "sbrief": "이중 억음 부호가 올려진 대문자 U"}}}, {"key": "0216", "mappings": {"default": {"default": "대문자 반전 겹온음표 U"}, "mathspeak": {"default": "반전된 겹온음표 부호가 올려진 대문자 U", "brief": "반전된 겹온음표 부호가 올려진 대문자 U", "sbrief": "반전된 겹온음표 부호가 올려진 대문자 U"}}}, {"key": "0218", "mappings": {"default": {"default": "대문자 아래 쉼표 S"}, "mathspeak": {"default": "아래에 쉼표 부호가 있는 대문자 S", "brief": "아래에 쉼표 부호가 있는 대문자 S", "sbrief": "아래에 쉼표 부호가 있는 대문자 S"}}}, {"key": "021A", "mappings": {"default": {"default": "대문자 아래 쉼표 T"}, "mathspeak": {"default": "아래에 쉼표 부호가 있는 대문자 T", "brief": "아래에 쉼표 부호가 있는 대문자 T", "sbrief": "아래에 쉼표 부호가 있는 대문자 T"}}}, {"key": "021E", "mappings": {"default": {"default": "대문자 카론 H"}, "mathspeak": {"default": "카론 부호가 올려진 대문자 H", "brief": "카론 부호가 올려진 대문자 H", "sbrief": "카론 부호가 올려진 대문자 H"}}}, {"key": "0226", "mappings": {"default": {"default": "대문자 윗점 A"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 A", "brief": "마침점 부호가 올려진 대문자 A", "sbrief": "마침점 부호가 올려진 대문자 A"}}}, {"key": "0228", "mappings": {"default": {"default": "대문자 갈고리형 E"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 E", "brief": "갈고리형 부호가 붙여진 대문자 E", "sbrief": "갈고리형 부호가 붙여진 대문자 E"}}}, {"key": "022E", "mappings": {"default": {"default": "대문자 윗점 O"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 O", "brief": "마침점 부호가 올려진 대문자 O", "sbrief": "마침점 부호가 올려진 대문자 O"}}}, {"key": "0232", "mappings": {"default": {"default": "대문자 오버바 Y"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 Y", "brief": "오버바 부호가 올려진 대문자 Y", "sbrief": "오버바 부호가 올려진 대문자 Y"}}}, {"key": "1E00", "mappings": {"default": {"default": "대문자 아래 반지 부호 A"}, "mathspeak": {"default": "아래에 반지 부호가 있는 대문자 A", "brief": "아래에 반지 부호가 있는 대문자 A", "sbrief": "아래에 반지 부호가 있는 대문자 A"}}}, {"key": "1E02", "mappings": {"default": {"default": "대문자 윗점 B"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 B", "brief": "마침점 부호가 올려진 대문자 B", "sbrief": "마침점 부호가 올려진 대문자 B"}}}, {"key": "1E04", "mappings": {"default": {"default": "대문자 아랫점 B"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 B", "brief": "아래에 마침표 부호가 있는 대문자 B", "sbrief": "아래에 마침표 부호가 있는 대문자 B"}}}, {"key": "1E06", "mappings": {"default": {"default": "대문자 언더바 B"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 B", "brief": "언더바 부호가 있는 대문자 B", "sbrief": "언더바 부호가 있는 대문자 B"}}}, {"key": "1E0A", "mappings": {"default": {"default": "대문자 윗점 D"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 D", "brief": "마침점 부호가 올려진 대문자 D", "sbrief": "마침점 부호가 올려진 대문자 D"}}}, {"key": "1E0C", "mappings": {"default": {"default": "대문자 아랫점 D"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 D", "brief": "아래에 마침표 부호가 있는 대문자 D", "sbrief": "아래에 마침표 부호가 있는 대문자 D"}}}, {"key": "1E0E", "mappings": {"default": {"default": "대문자 언더바 D"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 D", "brief": "언더바 부호가 있는 대문자 D", "sbrief": "언더바 부호가 있는 대문자 D"}}}, {"key": "1E10", "mappings": {"default": {"default": "대문자 갈고리형 D"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 D", "brief": "갈고리형 부호가 붙여진 대문자 D", "sbrief": "갈고리형 부호가 붙여진 대문자 D"}}}, {"key": "1E12", "mappings": {"default": {"default": "대문자 아래 곡절음 D"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 D", "brief": "아래에 곡절음 부호가 있는 대문자 D", "sbrief": "아래에 곡절음 부호가 있는 대문자 D"}}}, {"key": "1E18", "mappings": {"default": {"default": "대문자 아래 곡절음 E"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 E", "brief": "아래에 곡절음 부호가 있는 대문자 E", "sbrief": "아래에 곡절음 부호가 있는 대문자 E"}}}, {"key": "1E1A", "mappings": {"default": {"default": "대문자 아래 물결표 E"}, "mathspeak": {"default": "아래에 물결표가 있는 대문자 E", "brief": "아래에 물결표가 있는 대문자 E", "sbrief": "아래에 물결표가 있는 대문자 E"}}}, {"key": "1E1E", "mappings": {"default": {"default": "대문자 윗점 F"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 F", "brief": "마침점 부호가 올려진 대문자 F", "sbrief": "마침점 부호가 올려진 대문자 F"}}}, {"key": "1E20", "mappings": {"default": {"default": "대문자 오버바 G"}, "mathspeak": {"default": "오버바 부호가 올려진 대문자 G", "brief": "오버바 부호가 올려진 대문자 G", "sbrief": "오버바 부호가 올려진 대문자 G"}}}, {"key": "1E22", "mappings": {"default": {"default": "대문자 윗점 H"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 H", "brief": "마침점 부호가 올려진 대문자 H", "sbrief": "마침점 부호가 올려진 대문자 H"}}}, {"key": "1E24", "mappings": {"default": {"default": "대문자 아랫점 H"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 H", "brief": "아래에 마침표 부호가 있는 대문자 H", "sbrief": "아래에 마침표 부호가 있는 대문자 H"}}}, {"key": "1E26", "mappings": {"default": {"default": "대문자 분음 부호표 H"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 H", "brief": "분음 부호가 올려진 대문자 H", "sbrief": "분음 부호가 올려진 대문자 H"}}}, {"key": "1E28", "mappings": {"default": {"default": "대문자 갈고리형 H"}, "mathspeak": {"default": "갈고리형 부호가 붙여진 대문자 H", "brief": "갈고리형 부호가 붙여진 대문자 H", "sbrief": "갈고리형 부호가 붙여진 대문자 H"}}}, {"key": "1E2A", "mappings": {"default": {"default": "대문자 아래 겹온음표 H"}, "mathspeak": {"default": "아래에 겹온음표 부호가 있는 대문자 H", "brief": "아래에 겹온음표 부호가 있는 대문자 H", "sbrief": "아래에 겹온음표 부호가 있는 대문자 H"}}}, {"key": "1E2C", "mappings": {"default": {"default": "대문자 아래 물결표 I"}, "mathspeak": {"default": "아래에 물결표가 있는 대문자 I", "brief": "아래에 물결표가 있는 대문자 I", "sbrief": "아래에 물결표가 있는 대문자 I"}}}, {"key": "1E30", "mappings": {"default": {"default": "대문자 양음 K"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 K", "brief": "양음 부호가 올려진 대문자 K", "sbrief": "양음 부호가 올려진 대문자 K"}}}, {"key": "1E32", "mappings": {"default": {"default": "대문자 아랫점 K"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 K", "brief": "아래에 마침표 부호가 있는 대문자 K", "sbrief": "아래에 마침표 부호가 있는 대문자 K"}}}, {"key": "1E34", "mappings": {"default": {"default": "대문자 언더바 K"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 K", "brief": "언더바 부호가 있는 대문자 K", "sbrief": "언더바 부호가 있는 대문자 K"}}}, {"key": "1E36", "mappings": {"default": {"default": "대문자 아랫점 L"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 L", "brief": "아래에 마침표 부호가 있는 대문자 L", "sbrief": "아래에 마침표 부호가 있는 대문자 L"}}}, {"key": "1E3A", "mappings": {"default": {"default": "대문자 언더바 L"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 L", "brief": "언더바 부호가 있는 대문자 L", "sbrief": "언더바 부호가 있는 대문자 L"}}}, {"key": "1E3C", "mappings": {"default": {"default": "대문자 아래 곡절음 L"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 L", "brief": "아래에 곡절음 부호가 있는 대문자 L", "sbrief": "아래에 곡절음 부호가 있는 대문자 L"}}}, {"key": "1E3E", "mappings": {"default": {"default": "대문자 양음 M"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 M", "brief": "양음 부호가 올려진 대문자 M", "sbrief": "양음 부호가 올려진 대문자 M"}}}, {"key": "1E40", "mappings": {"default": {"default": "대문자 윗점 M"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 M", "brief": "마침점 부호가 올려진 대문자 M", "sbrief": "마침점 부호가 올려진 대문자 M"}}}, {"key": "1E42", "mappings": {"default": {"default": "대문자 아랫점 M"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 M", "brief": "아래에 마침표 부호가 있는 대문자 M", "sbrief": "아래에 마침표 부호가 있는 대문자 M"}}}, {"key": "1E44", "mappings": {"default": {"default": "대문자 윗점 N"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 N", "brief": "마침점 부호가 올려진 대문자 N", "sbrief": "마침점 부호가 올려진 대문자 N"}}}, {"key": "1E46", "mappings": {"default": {"default": "대문자 아랫점 N"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 N", "brief": "아래에 마침표 부호가 있는 대문자 N", "sbrief": "아래에 마침표 부호가 있는 대문자 N"}}}, {"key": "1E48", "mappings": {"default": {"default": "대문자 언더바 N"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 N", "brief": "언더바 부호가 있는 대문자 N", "sbrief": "언더바 부호가 있는 대문자 N"}}}, {"key": "1E4A", "mappings": {"default": {"default": "대문자 아래 곡절음 N"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 N", "brief": "아래에 곡절음 부호가 있는 대문자 N", "sbrief": "아래에 곡절음 부호가 있는 대문자 N"}}}, {"key": "1E54", "mappings": {"default": {"default": "대문자 양음 P"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 P", "brief": "양음 부호가 올려진 대문자 P", "sbrief": "양음 부호가 올려진 대문자 P"}}}, {"key": "1E56", "mappings": {"default": {"default": "대문자 윗점 P"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 P", "brief": "마침점 부호가 올려진 대문자 P", "sbrief": "마침점 부호가 올려진 대문자 P"}}}, {"key": "1E58", "mappings": {"default": {"default": "대문자 윗점 R"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 R", "brief": "마침점 부호가 올려진 대문자 R", "sbrief": "마침점 부호가 올려진 대문자 R"}}}, {"key": "1E5A", "mappings": {"default": {"default": "대문자 아랫점 R"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 R", "brief": "아래에 마침표 부호가 있는 대문자 R", "sbrief": "아래에 마침표 부호가 있는 대문자 R"}}}, {"key": "1E5E", "mappings": {"default": {"default": "대문자 언더바 R"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 R", "brief": "언더바 부호가 있는 대문자 R", "sbrief": "언더바 부호가 있는 대문자 R"}}}, {"key": "1E60", "mappings": {"default": {"default": "대문자 윗점 S"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 S", "brief": "마침점 부호가 올려진 대문자 S", "sbrief": "마침점 부호가 올려진 대문자 S"}}}, {"key": "1E62", "mappings": {"default": {"default": "대문자 아랫점 S"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 S", "brief": "아래에 마침표 부호가 있는 대문자 S", "sbrief": "아래에 마침표 부호가 있는 대문자 S"}}}, {"key": "1E6A", "mappings": {"default": {"default": "대문자 윗점 T"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 T", "brief": "마침점 부호가 올려진 대문자 T", "sbrief": "마침점 부호가 올려진 대문자 T"}}}, {"key": "1E6C", "mappings": {"default": {"default": "대문자 아랫점 T"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 T", "brief": "아래에 마침표 부호가 있는 대문자 T", "sbrief": "아래에 마침표 부호가 있는 대문자 T"}}}, {"key": "1E6E", "mappings": {"default": {"default": "대문자 언더바 T"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 T", "brief": "언더바 부호가 있는 대문자 T", "sbrief": "언더바 부호가 있는 대문자 T"}}}, {"key": "1E70", "mappings": {"default": {"default": "대문자 아래 곡절음 T"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 T", "brief": "아래에 곡절음 부호가 있는 대문자 T", "sbrief": "아래에 곡절음 부호가 있는 대문자 T"}}}, {"key": "1E72", "mappings": {"default": {"default": "대문자 아래 분음 부호표 U"}, "mathspeak": {"default": "아래에 분음 부호가 있는 대문자 U", "brief": "아래에 분음 부호가 있는 대문자 U", "sbrief": "아래에 분음 부호가 있는 대문자 U"}}}, {"key": "1E74", "mappings": {"default": {"default": "대문자 아래 물결표 U"}, "mathspeak": {"default": "아래에 물결표가 있는 대문자 U", "brief": "아래에 물결표가 있는 대문자 U", "sbrief": "아래에 물결표가 있는 대문자 U"}}}, {"key": "1E76", "mappings": {"default": {"default": "대문자 아래 곡절음 U"}, "mathspeak": {"default": "아래에 곡절음 부호가 있는 대문자 U", "brief": "아래에 곡절음 부호가 있는 대문자 U", "sbrief": "아래에 곡절음 부호가 있는 대문자 U"}}}, {"key": "1E7C", "mappings": {"default": {"default": "대문자 물결표 V"}, "mathspeak": {"default": "물결표가 올려진 대문자 V", "brief": "물결표가 올려진 대문자 V", "sbrief": "물결표가 올려진 대문자 V"}}}, {"key": "1E7E", "mappings": {"default": {"default": "대문자 아랫점 V"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 V", "brief": "아래에 마침표 부호가 있는 대문자 V", "sbrief": "아래에 마침표 부호가 있는 대문자 V"}}}, {"key": "1E80", "mappings": {"default": {"default": "대문자 억음 W"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 W", "brief": "억음 부호가 올려진 대문자 W", "sbrief": "억음 부호가 올려진 대문자 W"}}}, {"key": "1E82", "mappings": {"default": {"default": "대문자 양음 W"}, "mathspeak": {"default": "양음 부호가 올려진 대문자 W", "brief": "양음 부호가 올려진 대문자 W", "sbrief": "양음 부호가 올려진 대문자 W"}}}, {"key": "1E84", "mappings": {"default": {"default": "대문자 분음 부호표 W"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 W", "brief": "분음 부호가 올려진 대문자 W", "sbrief": "분음 부호가 올려진 대문자 W"}}}, {"key": "1E86", "mappings": {"default": {"default": "대문자 윗점 W"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 W", "brief": "마침점 부호가 올려진 대문자 W", "sbrief": "마침점 부호가 올려진 대문자 W"}}}, {"key": "1E88", "mappings": {"default": {"default": "대문자 아랫점 W"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 W", "brief": "아래에 마침표 부호가 있는 대문자 W", "sbrief": "아래에 마침표 부호가 있는 대문자 W"}}}, {"key": "1E8A", "mappings": {"default": {"default": "대문자 윗점 X"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 X", "brief": "마침점 부호가 올려진 대문자 X", "sbrief": "마침점 부호가 올려진 대문자 X"}}}, {"key": "1E8C", "mappings": {"default": {"default": "대문자 분음 부호표 X"}, "mathspeak": {"default": "분음 부호가 올려진 대문자 X", "brief": "분음 부호가 올려진 대문자 X", "sbrief": "분음 부호가 올려진 대문자 X"}}}, {"key": "1E8E", "mappings": {"default": {"default": "대문자 윗점 Y"}, "mathspeak": {"default": "마침점 부호가 올려진 대문자 Y", "brief": "마침점 부호가 올려진 대문자 Y", "sbrief": "마침점 부호가 올려진 대문자 Y"}}}, {"key": "1E90", "mappings": {"default": {"default": "대문자 곡절 악센트 Z"}, "mathspeak": {"default": "곡절 악센트 부호가 올려진 대문자 Z", "brief": "곡절 악센트 부호가 올려진 대문자 Z", "sbrief": "곡절 악센트 부호가 올려진 대문자 Z"}}}, {"key": "1E92", "mappings": {"default": {"default": "대문자 아랫점 Z"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 Z", "brief": "아래에 마침표 부호가 있는 대문자 Z", "sbrief": "아래에 마침표 부호가 있는 대문자 Z"}}}, {"key": "1E94", "mappings": {"default": {"default": "대문자 언더바 Z"}, "mathspeak": {"default": "언더바 부호가 있는 대문자 Z", "brief": "언더바 부호가 있는 대문자 Z", "sbrief": "언더바 부호가 있는 대문자 Z"}}}, {"key": "1EA0", "mappings": {"default": {"default": "대문자 아랫점 A"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 A", "brief": "아래에 마침표 부호가 있는 대문자 A", "sbrief": "아래에 마침표 부호가 있는 대문자 A"}}}, {"key": "1EA2", "mappings": {"default": {"default": "대문자 후크 A"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 A", "brief": "후크 부호가 올려진 대문자 A", "sbrief": "후크 부호가 올려진 대문자 A"}}}, {"key": "1EB8", "mappings": {"default": {"default": "대문자 아랫점 E"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 E", "brief": "아래에 마침표 부호가 있는 대문자 E", "sbrief": "아래에 마침표 부호가 있는 대문자 E"}}}, {"key": "1EBA", "mappings": {"default": {"default": "대문자 후크 E"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 E", "brief": "후크 부호가 올려진 대문자 E", "sbrief": "후크 부호가 올려진 대문자 E"}}}, {"key": "1EBC", "mappings": {"default": {"default": "대문자 물결표 E"}, "mathspeak": {"default": "물결표가 올려진 대문자 E", "brief": "물결표가 올려진 대문자 E", "sbrief": "물결표가 올려진 대문자 E"}}}, {"key": "1EC8", "mappings": {"default": {"default": "대문자 후크 I"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 I", "brief": "후크 부호가 올려진 대문자 I", "sbrief": "후크 부호가 올려진 대문자 I"}}}, {"key": "1ECA", "mappings": {"default": {"default": "대문자 아랫점 I"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 I", "brief": "아래에 마침표 부호가 있는 대문자 I", "sbrief": "아래에 마침표 부호가 있는 대문자 I"}}}, {"key": "1ECC", "mappings": {"default": {"default": "대문자 아랫점 O"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 O", "brief": "아래에 마침표 부호가 있는 대문자 O", "sbrief": "아래에 마침표 부호가 있는 대문자 O"}}}, {"key": "1ECE", "mappings": {"default": {"default": "대문자 후크 O"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 O", "brief": "후크 부호가 올려진 대문자 O", "sbrief": "후크 부호가 올려진 대문자 O"}}}, {"key": "1EE4", "mappings": {"default": {"default": "대문자 아랫점 U"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 U", "brief": "아래에 마침표 부호가 있는 대문자 U", "sbrief": "아래에 마침표 부호가 있는 대문자 U"}}}, {"key": "1EE6", "mappings": {"default": {"default": "대문자 후크 U"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 U", "brief": "후크 부호가 올려진 대문자 U", "sbrief": "후크 부호가 올려진 대문자 U"}}}, {"key": "1EF2", "mappings": {"default": {"default": "대문자 억음 Y"}, "mathspeak": {"default": "억음 부호가 올려진 대문자 Y", "brief": "억음 부호가 올려진 대문자 Y", "sbrief": "억음 부호가 올려진 대문자 Y"}}}, {"key": "1EF4", "mappings": {"default": {"default": "대문자 아랫점 Y"}, "mathspeak": {"default": "아래에 마침표 부호가 있는 대문자 Y", "brief": "아래에 마침표 부호가 있는 대문자 Y", "sbrief": "아래에 마침표 부호가 있는 대문자 Y"}}}, {"key": "1EF6", "mappings": {"default": {"default": "대문자 후크 Y"}, "mathspeak": {"default": "후크 부호가 올려진 대문자 Y", "brief": "후크 부호가 올려진 대문자 Y", "sbrief": "후크 부호가 올려진 대문자 Y"}}}, {"key": "1EF8", "mappings": {"default": {"default": "대문자 물결표 Y"}, "mathspeak": {"default": "물결표가 올려진 대문자 Y", "brief": "물결표가 올려진 대문자 Y", "sbrief": "물결표가 올려진 대문자 Y"}}}], "ko/symbols/math_angles.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "호와 직각"}}, "key": "22BE"}, {"mappings": {"default": {"default": "직각과 아래 쪽 지그재그형 화살표"}}, "key": "237C"}, {"mappings": {"default": {"default": "삼차원의 각"}}, "key": "27C0"}, {"mappings": {"default": {"default": "왼쪽으로 측정된 각도"}}, "key": "299B"}, {"mappings": {"default": {"default": "사각형의 직각"}}, "key": "299C"}, {"mappings": {"default": {"default": "점으로 측정된 직각"}}, "key": "299D"}, {"mappings": {"default": {"default": "S 자가 안에 있는 각도"}}, "key": "299E"}, {"mappings": {"default": {"default": "예각"}}, "key": "299F"}, {"mappings": {"default": {"default": "좌측 구면각"}}, "key": "29A0"}, {"mappings": {"default": {"default": "상측 구면각"}}, "key": "29A1"}, {"mappings": {"default": {"default": "회전 각도"}}, "key": "29A2"}, {"mappings": {"default": {"default": "반전된 각도"}}, "key": "29A3"}, {"mappings": {"default": {"default": "각도 언더바"}}, "key": "29A4"}, {"mappings": {"default": {"default": "반전된 각도 언더바"}}, "key": "29A5"}, {"mappings": {"default": {"default": "상측으로 비스듬한 각도"}}, "key": "29A6"}, {"mappings": {"default": {"default": "하측으로 비스듬한 각도"}}, "key": "29A7"}, {"mappings": {"default": {"default": "위쪽 화살표와 오른쪽 선분으로 측정된 각도"}}, "key": "29A8"}, {"mappings": {"default": {"default": "위쪽 화살표와 왼쪽 선분으로 측정된 각도"}}, "key": "29A9"}, {"mappings": {"default": {"default": "아래쪽 화살표와 오른쪽 선분으로 측정된 각도"}}, "key": "29AA"}, {"mappings": {"default": {"default": "아래쪽 화살표와 왼쪽 선분으로 측정된 각도"}}, "key": "29AB"}, {"mappings": {"default": {"default": "오른쪽 화살표와 위쪽 선분으로 측정된 각도"}}, "key": "29AC"}, {"mappings": {"default": {"default": "왼쪽 화살표와 위쪽 선분으로 측정된 각도"}}, "key": "29AD"}, {"mappings": {"default": {"default": "오른쪽 화살표와 아래쪽 선분으로 측정된 각도"}}, "key": "29AE"}, {"mappings": {"default": {"default": "왼쪽 화살표와 아래쪽 선분으로 측정된 각도"}}, "key": "29AF"}], "ko/symbols/math_arrows.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "왼쪽 화살표"}, "mathspeak": {"sbrief": "왼쪽 화살표"}}, "key": "2190"}, {"mappings": {"default": {"default": "위쪽 화살표"}, "mathspeak": {"sbrief": "위쪽 화살표"}}, "key": "2191"}, {"mappings": {"default": {"default": "오른쪽 화살표"}, "mathspeak": {"sbrief": "오른쪽 화살표"}}, "key": "2192"}, {"mappings": {"default": {"default": "아래쪽 화살표"}, "mathspeak": {"sbrief": "아래쪽 화살표"}}, "key": "2193"}, {"mappings": {"default": {"default": "좌우 화살표"}, "mathspeak": {"sbrief": "좌우 화살표"}}, "key": "2194"}, {"mappings": {"default": {"default": "상하 화살표"}, "mathspeak": {"sbrief": "상하 화살표"}}, "key": "2195"}, {"mappings": {"default": {"default": "좌상향 화살표"}, "mathspeak": {"sbrief": "좌상향 화살표"}}, "key": "2196"}, {"mappings": {"default": {"default": "우상향 화살표"}, "mathspeak": {"sbrief": "우상향 화살표"}}, "key": "2197"}, {"mappings": {"default": {"default": "우하향 화살표"}, "mathspeak": {"sbrief": "우하향 화살표"}}, "key": "2198"}, {"mappings": {"default": {"default": "좌하향 화살표"}, "mathspeak": {"sbrief": "좌하향 화살표"}}, "key": "2199"}, {"mappings": {"default": {"default": "사선이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "사선이 붙은 왼쪽 화살표"}}, "key": "219A"}, {"mappings": {"default": {"default": "사선이 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "사선이 붙은 오른쪽 화살표"}}, "key": "219B"}, {"mappings": {"default": {"default": "왼쪽 물결 화살표"}, "mathspeak": {"sbrief": "왼쪽 물결 화살표"}}, "key": "219C"}, {"mappings": {"default": {"default": "오른쪽 물결 화살표"}, "mathspeak": {"sbrief": "오른쪽 물결 화살표"}}, "key": "219D"}, {"mappings": {"default": {"default": "왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "왼쪽 쌍화살표"}}, "key": "219E"}, {"mappings": {"default": {"default": "위쪽 쌍화살표"}, "mathspeak": {"sbrief": "위쪽 쌍화살표"}}, "key": "219F"}, {"mappings": {"default": {"default": "오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "오른쪽 쌍화살표"}}, "key": "21A0"}, {"mappings": {"default": {"default": "아래쪽 쌍화살표"}, "mathspeak": {"sbrief": "아래쪽 쌍화살표"}}, "key": "21A1"}, {"mappings": {"default": {"default": "꼬리가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "꼬리가 있는 왼쪽 화살표"}}, "key": "21A2"}, {"mappings": {"default": {"default": "꼬리가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "꼬리가 있는 오른쪽 화살표"}}, "key": "21A3"}, {"mappings": {"default": {"default": "세로선이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 붙은 왼쪽 화살표"}}, "key": "21A4"}, {"mappings": {"default": {"default": "가로선이 붙은 위쪽 화살표"}, "mathspeak": {"sbrief": "가로선이 붙은 위쪽 화살표"}}, "key": "21A5"}, {"mappings": {"default": {"default": "세로선이 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 붙은 오른쪽 화살표"}}, "key": "21A6"}, {"mappings": {"default": {"default": "가로선이 붙은 아래쪽 화살표"}, "mathspeak": {"sbrief": "가로선이 붙은 아래쪽 화살표"}}, "key": "21A7"}, {"mappings": {"default": {"default": "밑줄이 있는 상하 화살표"}, "mathspeak": {"sbrief": "밑줄이 있는 상하 화살표"}}, "key": "21A8"}, {"mappings": {"default": {"default": "후크가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "후크가 있는 왼쪽 화살표"}}, "key": "21A9"}, {"mappings": {"default": {"default": "후크가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "후크가 있는 오른쪽 화살표"}}, "key": "21AA"}, {"mappings": {"default": {"default": "루프가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "루프가 있는 왼쪽 화살표"}}, "key": "21AB"}, {"mappings": {"default": {"default": "루프가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "루프가 있는 오른쪽 화살표"}}, "key": "21AC"}, {"mappings": {"default": {"default": "좌우 물결 화살표"}, "mathspeak": {"sbrief": "좌우 물결 화살표"}}, "key": "21AD"}, {"mappings": {"default": {"default": "사선이 그어진 좌우 화살표"}, "mathspeak": {"sbrief": "사선이 그어진 좌우 화살표"}}, "key": "21AE"}, {"mappings": {"default": {"default": "아래쪽 지그재그 화살표"}, "mathspeak": {"sbrief": "아래쪽 지그재그 화살표"}}, "key": "21AF"}, {"mappings": {"default": {"default": "끝이 왼쪽으로 꺾인 위쪽 화살표"}, "mathspeak": {"sbrief": "끝이 왼쪽으로 꺾인 위쪽 화살표"}}, "key": "21B0"}, {"mappings": {"default": {"default": "끝이 오른쪽으로 꺾인 위쪽 화살표"}, "mathspeak": {"sbrief": "끝이 오른쪽으로 꺾인 위쪽 화살표"}}, "key": "21B1"}, {"mappings": {"default": {"default": "끝이 왼쪽으로 꺾인 아래쪽 화살표"}, "mathspeak": {"sbrief": "끝이 왼쪽으로 꺾인 아래쪽 화살표"}}, "key": "21B2"}, {"mappings": {"default": {"default": "끝이 오른쪽으로 꺾인 아래쪽 화살표"}, "mathspeak": {"sbrief": "끝이 오른쪽으로 꺾인 아래쪽 화살표"}}, "key": "21B3"}, {"mappings": {"default": {"default": "아래쪽 코너가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "아래쪽 코너가 있는 오른쪽 화살표"}}, "key": "21B4"}, {"mappings": {"default": {"default": "왼쪽 코너가 있는 아래쪽 화살표"}, "mathspeak": {"sbrief": "왼쪽 코너가 있는 아래쪽 화살표"}}, "key": "21B5"}, {"mappings": {"default": {"default": "반시계 방향 반원 화살표"}}, "key": "21B6"}, {"mappings": {"default": {"default": "시계 방향 반원 화살표"}}, "key": "21B7"}, {"mappings": {"default": {"default": "윗줄이 있는 좌상향 화살표"}, "mathspeak": {"sbrief": "윗줄이 있는 좌상향 화살표"}}, "key": "21B8"}, {"mappings": {"default": {"default": "왼쪽 화살표 바와 그 위의 오른쪽 화살표 바"}, "mathspeak": {"sbrief": "왼쪽 화살표 바와 그 위의 오른쪽 화살표 바"}}, "key": "21B9"}, {"mappings": {"default": {"default": "반시계 방향 원 화살표"}}, "key": "21BA"}, {"mappings": {"default": {"default": "시계 방향 원 화살표"}}, "key": "21BB"}, {"mappings": {"default": {"default": "왼쪽 화살표와 그 위의 오른쪽 화살표"}, "mathspeak": {"sbrief": "왼쪽 화살표와 그 위의 오른쪽 화살표"}}, "key": "21C4"}, {"mappings": {"default": {"default": "위쪽 화살표와 그 옆의 아래쪽 화살표"}, "mathspeak": {"sbrief": "위쪽 화살표와 그 옆의 아래쪽 화살표"}}, "key": "21C5"}, {"mappings": {"default": {"default": "오른쪽 화살표와 그 위의 왼쪽 화살표"}, "mathspeak": {"sbrief": "오른쪽 화살표와 그 위의 왼쪽 화살표"}}, "key": "21C6"}, {"mappings": {"default": {"default": "두 개의 왼쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 왼쪽 화살표"}}, "key": "21C7"}, {"mappings": {"default": {"default": "두 개의 위쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 위쪽 화살표"}}, "key": "21C8"}, {"mappings": {"default": {"default": "두 개의 오른쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 오른쪽 화살표"}}, "key": "21C9"}, {"mappings": {"default": {"default": "두 개의 아래쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 아래쪽 화살표"}}, "key": "21CA"}, {"mappings": {"default": {"default": "사선이 그어진 왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "사선이 그어진 왼쪽 이중 화살표"}}, "key": "21CD"}, {"mappings": {"default": {"default": "사선이 그어진 좌우 이중 화살표"}, "mathspeak": {"sbrief": "사선이 그어진 좌우 이중 화살표"}}, "key": "21CE"}, {"mappings": {"default": {"default": "사선이 그어진 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "사선이 그어진 오른쪽 이중 화살표"}}, "key": "21CF"}, {"mappings": {"default": {"default": "왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "왼쪽 이중 화살표"}}, "key": "21D0"}, {"mappings": {"default": {"default": "위쪽 이중 화살표"}, "mathspeak": {"sbrief": "위쪽 이중 화살표"}}, "key": "21D1"}, {"mappings": {"default": {"default": "오른쪽 이중 화살표"}, "clearspeak": {"sbrief": "충분조건"}}, "key": "21D2"}, {"mappings": {"default": {"default": "아래쪽 이중 화살표"}, "clearspeak": {"sbrief": "필요조건"}}, "key": "21D3"}, {"mappings": {"default": {"default": "좌우 이중 화살표"}, "clearspeak": {"sbrief": "필요충분조건"}}, "key": "21D4"}, {"mappings": {"default": {"default": "상하 이중 화살표"}, "mathspeak": {"sbrief": "상하 이중 화살표"}}, "key": "21D5"}, {"mappings": {"default": {"default": "좌상향 이중 화살표"}, "mathspeak": {"sbrief": "좌상향 이중 화살표"}}, "key": "21D6"}, {"mappings": {"default": {"default": "우상향 이중 화살표"}, "mathspeak": {"sbrief": "우상향 이중 화살표"}}, "key": "21D7"}, {"mappings": {"default": {"default": "우하향 이중 화살표"}, "mathspeak": {"sbrief": "우하향 이중 화살표"}}, "key": "21D8"}, {"mappings": {"default": {"default": "좌하향 이중 화살표"}, "mathspeak": {"sbrief": "좌하향 이중 화살표"}}, "key": "21D9"}, {"mappings": {"default": {"default": "왼쪽 삼중 화살표"}, "mathspeak": {"sbrief": "왼쪽 삼중 화살표"}}, "key": "21DA"}, {"mappings": {"default": {"default": "오른쪽 삼중 화살표"}, "mathspeak": {"sbrief": "오른쪽 삼중 화살표"}}, "key": "21DB"}, {"mappings": {"default": {"default": "왼쪽 흔들리는 화살표"}, "mathspeak": {"sbrief": "왼쪽 흔들리는 화살표"}}, "key": "21DC"}, {"mappings": {"default": {"default": "오른쪽 흔들리는 화살표"}, "mathspeak": {"sbrief": "오른쪽 흔들리는 화살표"}}, "key": "21DD"}, {"mappings": {"default": {"default": "두 줄이 그어진 위쪽 화살표"}, "mathspeak": {"sbrief": "두 줄이 그어진 위쪽 화살표"}}, "key": "21DE"}, {"mappings": {"default": {"default": "두 줄이 그어진 아래쪽 화살표"}, "mathspeak": {"sbrief": "두 줄이 그어진 아래쪽 화살표"}}, "key": "21DF"}, {"mappings": {"default": {"default": "왼쪽 점선 화살표"}, "mathspeak": {"sbrief": "왼쪽 점선 화살표"}}, "key": "21E0"}, {"mappings": {"default": {"default": "위쪽 점선 화살표"}, "mathspeak": {"sbrief": "위쪽 점선 화살표"}}, "key": "21E1"}, {"mappings": {"default": {"default": "오른쪽 점선 화살표"}, "mathspeak": {"sbrief": "오른쪽 점선 화살표"}}, "key": "21E2"}, {"mappings": {"default": {"default": "아래쪽 점선 화살표"}, "mathspeak": {"sbrief": "아래쪽 점선 화살표"}}, "key": "21E3"}, {"mappings": {"default": {"default": "세로선을 가리키는 왼쪽 화샇표"}, "mathspeak": {"sbrief": "세로선을 가리키는 왼쪽 화샇표"}}, "key": "21E4"}, {"mappings": {"default": {"default": "세로선을 가리키는 오른쪽 화샇표"}, "mathspeak": {"sbrief": "세로선을 가리키는 오른쪽 화샇표"}}, "key": "21E5"}, {"mappings": {"default": {"default": "왼쪽 흰색 화살표"}, "mathspeak": {"sbrief": "왼쪽 흰색 화살표"}}, "key": "21E6"}, {"mappings": {"default": {"default": "위쪽 흰색 화살표"}, "mathspeak": {"sbrief": "위쪽 흰색 화살표"}}, "key": "21E7"}, {"mappings": {"default": {"default": "오른쪽 흰색 화살표"}, "mathspeak": {"sbrief": "오른쪽 흰색 화살표"}}, "key": "21E8"}, {"mappings": {"default": {"default": "아래쪽 흰색 화살표"}, "mathspeak": {"sbrief": "아래쪽 흰색 화살표"}}, "key": "21E9"}, {"mappings": {"default": {"default": "흰색 막대와 위쪽 흰색 화살표"}, "mathspeak": {"sbrief": "흰색 막대와 위쪽 흰색 화살표"}}, "key": "21EA"}, {"mappings": {"default": {"default": "받침대가 있는 위쪽 흰색 화살표"}, "mathspeak": {"sbrief": "받침대가 있는 위쪽 흰색 화살표"}}, "key": "21EB"}, {"mappings": {"default": {"default": "받침대가 있고 화살촉에 가로선이 추가된 위쪽 흰색 화살표"}, "mathspeak": {"sbrief": "받침대가 있고 화살촉에 가로선이 추가된 위쪽 흰색 화살표"}}, "key": "21EC"}, {"mappings": {"default": {"default": "받침대가 있고 안에 세로줄이 그어진 위쪽 흰색 화살표"}, "mathspeak": {"sbrief": "받침대가 있고 안에 세로줄이 그어진 위쪽 흰색 화살표"}}, "key": "21ED"}, {"mappings": {"default": {"default": "위쪽 흰색 쌍화살표"}, "mathspeak": {"sbrief": "위쪽 흰색 쌍화살표"}}, "key": "21EE"}, {"mappings": {"default": {"default": "받침대가 있는 위쪽 흰색 쌍화살표"}, "mathspeak": {"sbrief": "받침대가 있는 위쪽 흰색 쌍화살표"}}, "key": "21EF"}, {"mappings": {"default": {"default": "받침대가 있는 오른쪽 흰색 화살표"}, "mathspeak": {"sbrief": "받침대가 있는 오른쪽 흰색 화살표"}}, "key": "21F0"}, {"mappings": {"default": {"default": "모서리를 가리키는 좌상향 화살표"}}, "key": "21F1"}, {"mappings": {"default": {"default": "모서리를 가리키는 우하향 화살표"}}, "key": "21F2"}, {"mappings": {"default": {"default": "상하 흰색 화살표"}, "mathspeak": {"sbrief": "상하 흰색 화살표"}}, "key": "21F3"}, {"mappings": {"default": {"default": "작은 원이 겹쳐진 오른쪽 화살표"}, "mathspeak": {"sbrief": "작은 원이 겹쳐진 오른쪽 화살표"}}, "key": "21F4"}, {"mappings": {"default": {"default": "아래쪽 화살표와 그 옆에 위쪽 화살표"}, "mathspeak": {"sbrief": "아래쪽 화살표와 그 옆에 위쪽 화살표"}}, "key": "21F5"}, {"mappings": {"default": {"default": "세 개의 오른쪽 화살표"}, "mathspeak": {"sbrief": "세 개의 오른쪽 화살표"}}, "key": "21F6"}, {"mappings": {"default": {"default": "세로선이 그어진 왼쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 왼쪽 화살표"}}, "key": "21F7"}, {"mappings": {"default": {"default": "세로선이 그어진 오른쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 오른쪽 화살표"}}, "key": "21F8"}, {"mappings": {"default": {"default": "세로선이 그어진 좌우 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 좌우 화살표"}}, "key": "21F9"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 왼쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 왼쪽 화살표"}}, "key": "21FA"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 오른쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 오른쪽 화살표"}}, "key": "21FB"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 좌우 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 좌우 화살표"}}, "key": "21FC"}, {"mappings": {"default": {"default": "촉이 빈 왼쪽 화살표"}, "mathspeak": {"sbrief": "촉이 빈 왼쪽 화살표"}}, "key": "21FD"}, {"mappings": {"default": {"default": "촉이 빈 오른쪽 화살표"}, "mathspeak": {"sbrief": "촉이 빈 오른쪽 화살표"}}, "key": "21FE"}, {"mappings": {"default": {"default": "촉이 빈 좌우 화살표"}, "mathspeak": {"sbrief": "촉이 빈 좌우 화살표"}}, "key": "21FF"}, {"mappings": {"default": {"default": "전기 화살표"}}, "key": "2301"}, {"mappings": {"default": {"default": "위쪽 화살촉"}, "mathspeak": {"sbrief": "위쪽 화살촉"}}, "key": "2303"}, {"mappings": {"default": {"default": "아래쪽 화살촉"}, "mathspeak": {"sbrief": "아래쪽 화살촉"}}, "key": "2304"}, {"mappings": {"default": {"default": "두 가로선 사이의 위쪽 화살촉", "alternative": "엔터 키"}, "mathspeak": {"sbrief": "두 가로선 사이의 위쪽 화살촉"}}, "key": "2324"}, {"mappings": {"default": {"default": "좌상향 화살표가 있는 깨진 원"}}, "key": "238B"}, {"mappings": {"default": {"default": "굵고 넓은 오른쪽 화살표"}, "mathspeak": {"sbrief": "굵고 넓은 오른쪽 화살표"}}, "key": "2794"}, {"mappings": {"default": {"default": "진한 우하향 화살표"}, "mathspeak": {"sbrief": "진한 우하향 화살표"}}, "key": "2798"}, {"mappings": {"default": {"default": "진한 오른쪽 화살표"}, "mathspeak": {"sbrief": "진한 오른쪽 화살표"}}, "key": "2799"}, {"mappings": {"default": {"default": "진한 우상향 화살표"}, "mathspeak": {"sbrief": "진한 우상향 화살표"}}, "key": "279A"}, {"mappings": {"default": {"default": "화살촉이 큰 오른쪽 화살표"}, "mathspeak": {"sbrief": "화살촉이 큰 오른쪽 화살표"}}, "key": "279B"}, {"mappings": {"default": {"default": "굵은 오른쪽 화살표"}, "mathspeak": {"sbrief": "굵은 오른쪽 화살표"}}, "key": "279C"}, {"mappings": {"default": {"default": "삼각형 화살촉을 가진 오른쪽 화살표"}, "mathspeak": {"sbrief": "삼각형 화살촉을 가진 오른쪽 화살표"}}, "key": "279D"}, {"mappings": {"default": {"default": "삼각형 화살촉을 가진 진한 오른쪽 화살표"}, "mathspeak": {"sbrief": "삼각형 화살촉을 가진 진한 오른쪽 화살표"}}, "key": "279E"}, {"mappings": {"default": {"default": "삼각형 화살촉을 가진 오른쪽 점선 화살표"}, "mathspeak": {"sbrief": "삼각형 화살촉을 가진 오른쪽 점선 화살표"}}, "key": "279F"}, {"mappings": {"default": {"default": "삼각형 화살촉을 가진 진한 오른쪽 점선 화살표"}, "mathspeak": {"sbrief": "삼각형 화살촉을 가진 진한 오른쪽 점선 화살표"}}, "key": "27A0"}, {"mappings": {"default": {"default": "검은 오른쪽 화살표"}, "mathspeak": {"sbrief": "검은 오른쪽 화살표"}}, "key": "27A1"}, {"mappings": {"default": {"default": "삼차원 오른쪽 화살촉"}, "mathspeak": {"sbrief": "삼차원 오른쪽 화살촉"}}, "key": "27A2"}, {"mappings": {"default": {"default": "위쪽에 그림자가 있는 삼차원 오른쪽 화살촉"}, "mathspeak": {"sbrief": "위쪽에 그림자가 있는 삼차원 오른쪽 화살촉"}}, "key": "27A3"}, {"mappings": {"default": {"default": "검은 오른쪽 화살촉"}, "mathspeak": {"sbrief": "검은 오른쪽 화살촉"}}, "key": "27A4"}, {"mappings": {"default": {"default": "위에서 오른쪽으로 휘어지는 진한 검은 화살표"}, "mathspeak": {"sbrief": "위에서 오른쪽으로 휘어지는 진한 검은 화살표"}}, "key": "27A5"}, {"mappings": {"default": {"default": "밑에서 오른쪽으로 휘어지는 진한 검은 화살표"}, "mathspeak": {"sbrief": "밑에서 오른쪽으로 휘어지는 진한 검은 화살표"}}, "key": "27A6"}, {"mappings": {"default": {"default": "검은 오른쪽 스쿼트 화살표"}, "mathspeak": {"sbrief": "검은 오른쪽 스쿼트 화살표"}}, "key": "27A7"}, {"mappings": {"default": {"default": "화살대가 굵은 오른쪽 화살표"}, "mathspeak": {"sbrief": "화살대가 굵은 오른쪽 화살표"}}, "key": "27A8"}, {"mappings": {"default": {"default": "음영 처리된 오른쪽 화살표"}, "mathspeak": {"sbrief": "음영 처리된 오른쪽 화살표"}}, "key": "27A9"}, {"mappings": {"default": {"default": "우측으로 음영 처리된 오른쪽 화살표"}, "mathspeak": {"sbrief": "우측으로 음영 처리된 오른쪽 화살표"}}, "key": "27AA"}, {"mappings": {"default": {"default": "기울어진 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "기울어진 흰색 오른쪽 화살표"}}, "key": "27AB"}, {"mappings": {"default": {"default": "앞쪽으로 기울어진 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "앞쪽으로 기울어진 흰색 오른쪽 화살표"}}, "key": "27AC"}, {"mappings": {"default": {"default": "짦은 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "짦은 흰색 오른쪽 화살표"}}, "key": "27AD"}, {"mappings": {"default": {"default": "위쪽으로 음영 처리된 짦은 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "위쪽으로 음영 처리된 짦은 흰색 오른쪽 화살표"}}, "key": "27AE"}, {"mappings": {"default": {"default": "끝이 파인 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "끝이 파인 흰색 오른쪽 화살표"}}, "key": "27AF"}, {"mappings": {"default": {"default": "끝이 파였고 흰색 위쪽으로 음영 처리된 오른쪽 화살표"}, "mathspeak": {"sbrief": "끝이 파였고 흰색 위쪽으로 음영 처리된 오른쪽 화살표"}}, "key": "27B1"}, {"mappings": {"default": {"default": "진한 원 안의 흰색 오른쪽 화살표"}, "mathspeak": {"sbrief": "진한 원 안의 흰색 오른쪽 화살표"}}, "key": "27B2"}, {"mappings": {"default": {"default": "화살깃이 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 오른쪽 화살표"}}, "key": "27B3"}, {"mappings": {"default": {"default": "화살깃이 있는 우하향 화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 우하향 화살표"}}, "key": "27B4"}, {"mappings": {"default": {"default": "화살깃이 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 오른쪽 화살표"}}, "key": "27B5"}, {"mappings": {"default": {"default": "화살깃이 있는 우상향 화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 우상향 화살표"}}, "key": "27B6"}, {"mappings": {"default": {"default": "진한 화살깃이 있는 우하향 화살표"}, "mathspeak": {"sbrief": "진한 화살깃이 있는 우하향 화살표"}}, "key": "27B7"}, {"mappings": {"default": {"default": "진한 화살깃이 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "진한 화살깃이 있는 오른쪽 화살표"}}, "key": "27B8"}, {"mappings": {"default": {"default": "진한 화살깃이 있는 우상향 화살표"}, "mathspeak": {"sbrief": "진한 화살깃이 있는 우상향 화살표"}}, "key": "27B9"}, {"mappings": {"default": {"default": "눈물 방울 모양의 오른쪽 화살표"}, "mathspeak": {"sbrief": "눈물 방울 모양의 오른쪽 화살표"}}, "key": "27BA"}, {"mappings": {"default": {"default": "화살깃이 두꺼운 눈물 모양의 오른쪽 화살표"}, "mathspeak": {"sbrief": "화살깃이 두꺼운 눈물 모양의 오른쪽 화살표"}}, "key": "27BB"}, {"mappings": {"default": {"default": "꼬리가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "꼬리가 있는 오른쪽 화살표"}}, "key": "27BC"}, {"mappings": {"default": {"default": "꼬리가 있는 굵은 오른쪽 화살표"}, "mathspeak": {"sbrief": "꼬리가 있는 굵은 오른쪽 화살표"}}, "key": "27BD"}, {"mappings": {"default": {"default": "열린 윤곽선의 오른쪽 화살표"}, "mathspeak": {"sbrief": "열린 윤곽선의 오른쪽 화살표"}}, "key": "27BE"}, {"mappings": {"default": {"default": "위쪽 쿼드러플 화살표"}, "mathspeak": {"sbrief": "위쪽 쿼드러플 화살표"}}, "key": "27F0"}, {"mappings": {"default": {"default": "아래쪽 쿼드러플 화살표"}, "mathspeak": {"sbrief": "아래쪽 쿼드러플 화살표"}}, "key": "27F1"}, {"mappings": {"default": {"default": "반시계 방향의 원 화살표"}}, "key": "27F2"}, {"mappings": {"default": {"default": "시계 방향의 원 화살표"}}, "key": "27F3"}, {"mappings": {"default": {"default": "원과 플러스 부호 안에 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "원과 플러스 부호 안에 있는 오른쪽 화살표"}}, "key": "27F4"}, {"mappings": {"default": {"default": "긴 왼쪽 화살표"}, "mathspeak": {"sbrief": "긴 왼쪽 화살표"}}, "key": "27F5"}, {"mappings": {"default": {"default": "긴 오른쪽 화살표"}, "mathspeak": {"sbrief": "긴 오른쪽 화살표"}}, "key": "27F6"}, {"mappings": {"default": {"default": "긴 좌우 화살표"}, "mathspeak": {"sbrief": "긴 좌우 화살표"}}, "key": "27F7"}, {"mappings": {"default": {"default": "긴 왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "긴 왼쪽 이중 화살표"}}, "key": "27F8"}, {"mappings": {"default": {"default": "긴 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "긴 오른쪽 이중 화살표"}}, "key": "27F9"}, {"mappings": {"default": {"default": "긴 좌우 이중 화살표"}, "mathspeak": {"sbrief": "긴 좌우 이중 화살표"}}, "key": "27FA"}, {"mappings": {"default": {"default": "세로선에서 시작하는 긴 왼쪽 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 긴 왼쪽 화살표"}}, "key": "27FB"}, {"mappings": {"default": {"default": "세로선에서 시작하는 긴 오른쪽 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 긴 오른쪽 화살표"}}, "key": "27FC"}, {"mappings": {"default": {"default": "세로선에서 시작하는 긴 왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 긴 왼쪽 이중 화살표"}}, "key": "27FD"}, {"mappings": {"default": {"default": "세로선에서 시작하는 긴 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 긴 오른쪽 이중 화살표"}}, "key": "27FE"}, {"mappings": {"default": {"default": "흔들리는 긴 오른쪽 화살표"}, "mathspeak": {"sbrief": "흔들리는 긴 오른쪽 화살표"}}, "key": "27FF"}, {"mappings": {"default": {"default": "세로선이 그어진 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 오른쪽 쌍화살표"}}, "key": "2900"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 오른쪽 쌍화살표"}}, "key": "2901"}, {"mappings": {"default": {"default": "세로선이 그어진 왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 왼쪽 이중 화살표"}}, "key": "2902"}, {"mappings": {"default": {"default": "세로선이 그어진 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 오른쪽 이중 화살표"}}, "key": "2903"}, {"mappings": {"default": {"default": "세로선이 그어진 좌우 이중 화살표"}, "mathspeak": {"sbrief": "세로선이 그어진 좌우 이중 화살표"}}, "key": "2904"}, {"mappings": {"default": {"default": "세로선에서 시작하는 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 오른쪽 쌍화살표"}}, "key": "2905"}, {"mappings": {"default": {"default": "세로선에서 시작하는 왼쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 왼쪽 이중 화살표"}}, "key": "2906"}, {"mappings": {"default": {"default": "세로선에서 시작하는 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 오른쪽 이중 화살표"}}, "key": "2907"}, {"mappings": {"default": {"default": "가로선이 그어진 아래쪽 화살표"}}, "key": "2908"}, {"mappings": {"default": {"default": "가로선이 그어진 위쪽 화살표"}, "mathspeak": {"sbrief": "가로선이 그어진 위쪽 화살표"}}, "key": "2909"}, {"mappings": {"default": {"default": "위쪽 삼중 화살표"}, "mathspeak": {"sbrief": "위쪽 삼중 화살표"}}, "key": "290A"}, {"mappings": {"default": {"default": "아래쪽 삼중 화살표"}, "mathspeak": {"sbrief": "아래쪽 삼중 화살표"}}, "key": "290B"}, {"mappings": {"default": {"default": "왼쪽 더블 대시 화살표"}, "mathspeak": {"sbrief": "왼쪽 더블 대시 화살표"}}, "key": "290C"}, {"mappings": {"default": {"default": "오른쪽 더블 대시 화살표"}, "mathspeak": {"sbrief": "오른쪽 더블 대시 화살표"}}, "key": "290D"}, {"mappings": {"default": {"default": "왼쪽 트리플 대시 화살표"}, "mathspeak": {"sbrief": "왼쪽 트리플 대시 화살표"}}, "key": "290E"}, {"mappings": {"default": {"default": "오른쪽 트리플 대시 화살표"}, "mathspeak": {"sbrief": "오른쪽 트리플 대시 화살표"}}, "key": "290F"}, {"mappings": {"default": {"default": "화살깃이 있는 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 오른쪽 쌍화살표"}}, "key": "2910"}, {"mappings": {"default": {"default": "오른쪽 점선 화살표"}, "mathspeak": {"sbrief": "오른쪽 점선 화살표"}}, "key": "2911"}, {"mappings": {"default": {"default": "가로선을 가리키는 위쪽 화살표"}, "mathspeak": {"sbrief": "가로선을 가리키는 위쪽 화살표"}}, "key": "2912"}, {"mappings": {"default": {"default": "가로선을 가리키는 아래쪽 화살표"}, "mathspeak": {"sbrief": "가로선을 가리키는 아래쪽 화살표"}}, "key": "2913"}, {"mappings": {"default": {"default": "세로선이 그어졌고 화살깃이 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 그어졌고 화살깃이 있는 오른쪽 화살표"}}, "key": "2914"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어졌고 화살깃이 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어졌고 화살깃이 있는 오른쪽 화살표"}}, "key": "2915"}, {"mappings": {"default": {"default": "화살깃이 있는 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "화살깃이 있는 오른쪽 쌍화살표"}}, "key": "2916"}, {"mappings": {"default": {"default": "세로선이 그어졌고 화살깃이 있는 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "세로선이 그어졌고 화살깃이 있는 오른쪽 쌍화살표"}}, "key": "2917"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어졌고 화살깃이 있는 오른쪽 쌍화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어졌고 화살깃이 있는 오른쪽 쌍화살표"}}, "key": "2918"}, {"mappings": {"default": {"default": "꼬리만 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "꼬리만 있는 왼쪽 화살표"}}, "key": "2919"}, {"mappings": {"default": {"default": "꼬리만 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "꼬리만 있는 오른쪽 화살표"}}, "key": "291A"}, {"mappings": {"default": {"default": "두 개의 꼬리만 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 꼬리만 있는 왼쪽 화살표"}}, "key": "291B"}, {"mappings": {"default": {"default": "두 개의 꼬리만 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 꼬리만 있는 오른쪽 화살표"}}, "key": "291C"}, {"mappings": {"default": {"default": "검은 다이아몬드를 가리키고 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "검은 다이아몬드를 가리키고 있는 왼쪽 화살표"}}, "key": "291D"}, {"mappings": {"default": {"default": "검은 다이아몬드를 가리키고 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "검은 다이아몬드를 가리키고 있는 오른쪽 화살표"}}, "key": "291E"}, {"mappings": {"default": {"default": "세로선부터 검은 다이아몬드까지 가리키고 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "세로선부터 검은 다이아몬드까지 가리키고 있는 왼쪽 화살표"}}, "key": "291F"}, {"mappings": {"default": {"default": "세로선부터 검은 다이아몬드까지 가리키는 오른쪽 화살표"}, "mathspeak": {"sbrief": "세로선부터 검은 다이아몬드까지 가리키는 오른쪽 화살표"}}, "key": "2920"}, {"mappings": {"default": {"default": "좌상향과 우하향이 결합된 화살표"}}, "key": "2921"}, {"mappings": {"default": {"default": "좌하향과 우상향이 결합된 화살표"}}, "key": "2922"}, {"mappings": {"default": {"default": "후크가 있는 좌상향 화살표"}}, "key": "2923"}, {"mappings": {"default": {"default": "후크가 있는 우상향 화살표"}}, "key": "2924"}, {"mappings": {"default": {"default": "후크가 있는 우하향 화살표"}}, "key": "2925"}, {"mappings": {"default": {"default": "후크가 있는 좌하향 화살표"}}, "key": "2926"}, {"mappings": {"default": {"default": "좌상향 화살표와 우상향 화살표의 교차"}}, "key": "2927"}, {"mappings": {"default": {"default": "우상향 화살표와 우하향 화살표의 교차"}}, "key": "2928"}, {"mappings": {"default": {"default": "좌하향 화살표와 우하향 화살표의 교차"}}, "key": "2929"}, {"mappings": {"default": {"default": "좌상향 화살표와 좌하향 화살표의 교차"}}, "key": "292A"}, {"mappings": {"default": {"default": "우상향 화살표가 위에 있는 우하향 화살표와의 교차"}}, "key": "292D"}, {"mappings": {"default": {"default": "우하향 화살표가 위에 있는 우상향 화살표와의 교차"}}, "key": "292E"}, {"mappings": {"default": {"default": "대각선이 위에 있는 우상향 화살표와의 교차"}}, "key": "292F"}, {"mappings": {"default": {"default": "대각선이 위에 있는 우하향 화살표와의 교차"}}, "key": "2930"}, {"mappings": {"default": {"default": "우상향 화살표가 위에 있는 좌상향 화살표와의 교차"}}, "key": "2931"}, {"mappings": {"default": {"default": "좌상향 화살표가 위에 있는 우상향 화살표와의 교차"}}, "key": "2932"}, {"mappings": {"default": {"default": "오른쪽 웨이브 화살표"}}, "key": "2933"}, {"mappings": {"default": {"default": "왼쪽에서 위로 휘어지는 화살표"}}, "key": "2934"}, {"mappings": {"default": {"default": "왼쪽에서 아래로 휘어지는 화살표"}}, "key": "2935"}, {"mappings": {"default": {"default": "위쪽에서 왼쪽으로 휘아지는 화살표"}}, "key": "2936"}, {"mappings": {"default": {"default": "위쪽에서 오른쪽으로 휘아지는 화살표"}}, "key": "2937"}, {"mappings": {"default": {"default": "시계 방향 원호 화살표"}, "mathspeak": {"sbrief": "시계 방향 원호 화살표"}}, "key": "2938"}, {"mappings": {"default": {"default": "반시계 방향 원호 화살표"}, "mathspeak": {"sbrief": "반시계 방향 원호 화살표"}}, "key": "2939"}, {"mappings": {"default": {"default": "반시계 방향 윗 원호 화살표"}}, "key": "293A"}, {"mappings": {"default": {"default": "반시계 방향 아랫 원호 화살표"}}, "key": "293B"}, {"mappings": {"default": {"default": "시계 방향 윗 원호 화살표와 마이너스 부호"}}, "key": "293C"}, {"mappings": {"default": {"default": "반시계 방향 윗 원호 화살표와 플러스 부호"}}, "key": "293D"}, {"mappings": {"default": {"default": "시계 방향 반원 화살표"}, "mathspeak": {"sbrief": "시계 방향 반원 화살표"}}, "key": "293E"}, {"mappings": {"default": {"default": "반시계 방향 반원 화살표"}, "mathspeak": {"sbrief": "반시계 방향 반원 화살표"}}, "key": "293F"}, {"mappings": {"default": {"default": "반시계 방향 닫힌 원 화살표"}}, "key": "2940"}, {"mappings": {"default": {"default": "시계 방향 닫힌 원 화살표"}}, "key": "2941"}, {"mappings": {"default": {"default": "짦은 왼쪽 화살표와 그 위에 오른쪽 화살표"}, "mathspeak": {"sbrief": "짦은 왼쪽 화살표와 그 위에 오른쪽 화살표"}}, "key": "2942"}, {"mappings": {"default": {"default": "짦은 오른쪽 화살표와 그 위에 왼쪽 화살표"}, "mathspeak": {"sbrief": "짦은 오른쪽 화살표와 그 위에 왼쪽 화살표"}}, "key": "2943"}, {"mappings": {"default": {"default": "왼쪽 화살표와 그 위에 짦은 오른쪽 화살표"}, "mathspeak": {"sbrief": "왼쪽 화살표와 그 위에 짦은 오른쪽 화살표"}}, "key": "2944"}, {"mappings": {"default": {"default": "오른쪽 화살표와 그 아래에 플러스 기호"}, "mathspeak": {"sbrief": "오른쪽 화살표와 그 아래에 플러스 기호"}}, "key": "2945"}, {"mappings": {"default": {"default": "왼쪽 화살표와 그 아래에 플러스 기호"}, "mathspeak": {"sbrief": "왼쪽 화살표와 그 아래에 플러스 기호"}}, "key": "2946"}, {"mappings": {"default": {"default": "엑스 자를 통과하는 오른쪽 화살표"}, "mathspeak": {"sbrief": "엑스 자를 통과하는 오른쪽 화살표"}}, "key": "2947"}, {"mappings": {"default": {"default": "작은 원이 붙은 좌우 화살표"}, "mathspeak": {"sbrief": "작은 원이 붙은 좌우 화살표"}}, "key": "2948"}, {"mappings": {"default": {"default": "작은 원에서 시작하는 위쪽 쌍화살표"}, "mathspeak": {"sbrief": "작은 원에서 시작하는 위쪽 쌍화살표"}}, "key": "2949"}, {"mappings": {"default": {"default": "끝이 뭉툭한 오른쪽 이중 화살표"}, "mathspeak": {"sbrief": "끝이 뭉툭한 오른쪽 이중 화살표"}}, "key": "2970"}, {"mappings": {"default": {"default": "위에 등호가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "위에 등호가 붙은 오른쪽 화살표"}}, "key": "2971"}, {"mappings": {"default": {"default": "위에 물결표가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "위에 물결표가 붙은 오른쪽 화살표"}}, "key": "2972"}, {"mappings": {"default": {"default": "밑에 물결표가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "밑에 물결표가 붙은 왼쪽 화살표"}}, "key": "2973"}, {"mappings": {"default": {"default": "밑에 물결표가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "밑에 물결표가 붙은 오른쪽 화살표"}}, "key": "2974"}, {"mappings": {"default": {"default": "밑에 근사 등호가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "밑에 근사 등호가 붙은 오른쪽 화살표"}}, "key": "2975"}, {"mappings": {"default": {"default": "위에 미만 부등호가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 미만 부등호가 붙은 왼쪽 화살표"}}, "key": "2976"}, {"mappings": {"default": {"default": "미만 부등호가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "미만 부등호가 붙은 왼쪽 화살표"}}, "key": "2977"}, {"mappings": {"default": {"default": "위에 초과 부등호가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "위에 초과 부등호가 붙은 오른쪽 화살표"}}, "key": "2978"}, {"mappings": {"default": {"default": "위에 부분 집합이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 부분 집합이 붙은 왼쪽 화살표"}}, "key": "2979"}, {"mappings": {"default": {"default": "부분 집합이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "부분 집합이 붙은 왼쪽 화살표"}}, "key": "297A"}, {"mappings": {"default": {"default": "위에 상위 집합이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 상위 집합이 붙은 왼쪽 화살표"}}, "key": "297B"}, {"mappings": {"default": {"default": "위에 오른쪽 화살표가 붙은 공집합"}, "mathspeak": {"sbrief": "위에 오른쪽 화살표가 붙은 공집합"}}, "key": "29B3"}, {"mappings": {"default": {"default": "위에 왼쪽 화살표가 붙은 공집합"}, "mathspeak": {"sbrief": "위에 왼쪽 화살표가 붙은 공집합"}}, "key": "29B4"}, {"mappings": {"default": {"default": "공집합이 겹쳐진 화살표"}, "mathspeak": {"sbrief": "공집합이 겹쳐진 화살표"}}, "key": "29BD"}, {"mappings": {"default": {"default": "검은 다이아몬드가 겹쳐진 아래쪽 화살표"}, "mathspeak": {"sbrief": "검은 다이아몬드가 겹쳐진 아래쪽 화살표"}}, "key": "29EA"}, {"mappings": {"default": {"default": "흰 원이 겹쳐진 아래쪽 화살표"}, "mathspeak": {"sbrief": "흰 원이 겹쳐진 아래쪽 화살표"}}, "key": "29EC"}, {"mappings": {"default": {"default": "검은 원이 겹쳐진 아래쪽 화살표"}, "mathspeak": {"sbrief": "검은 원이 겹쳐진 아래쪽 화살표"}}, "key": "29ED"}, {"mappings": {"default": {"default": "인테그럴이 겹쳐진 후크가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "인테그럴이 겹쳐진 후크가 있는 왼쪽 화살표"}}, "key": "2A17"}, {"mappings": {"default": {"default": "우상향 흰색 화살표"}}, "key": "2B00"}, {"mappings": {"default": {"default": "좌상향 흰색 화살표"}}, "key": "2B01"}, {"mappings": {"default": {"default": "우하향 흰색 화살표"}}, "key": "2B02"}, {"mappings": {"default": {"default": "좌하향 흰색 화살표"}}, "key": "2B03"}, {"mappings": {"default": {"default": "좌우 흰색 화살표"}, "mathspeak": {"sbrief": "좌우 흰색 화살표"}}, "key": "2B04"}, {"mappings": {"default": {"default": "왼쪽 진한 화살표"}, "mathspeak": {"sbrief": "왼쪽 진한 화살표"}}, "key": "2B05"}, {"mappings": {"default": {"default": "위쪽 진한 화살표"}, "mathspeak": {"sbrief": "위쪽 진한 화살표"}}, "key": "2B06"}, {"mappings": {"default": {"default": "아래쪽 진한 화살표"}, "mathspeak": {"sbrief": "아래쪽 진한 화살표"}}, "key": "2B07"}, {"mappings": {"default": {"default": "우상향 진한 화살표"}}, "key": "2B08"}, {"mappings": {"default": {"default": "좌상향 진한 화살표"}}, "key": "2B09"}, {"mappings": {"default": {"default": "우하향 진한 화살표"}}, "key": "2B0A"}, {"mappings": {"default": {"default": "좌하향 진한 화살표"}}, "key": "2B0B"}, {"mappings": {"default": {"default": "좌우 진한 화살표"}, "mathspeak": {"sbrief": "좌우 진한 화살표"}}, "key": "2B0C"}, {"mappings": {"default": {"default": "상하 진한 화살표"}, "mathspeak": {"sbrief": "상하 진한 화살표"}}, "key": "2B0D"}, {"mappings": {"default": {"default": "아래쪽으로 꺾인 오른쪽 화살표"}, "mathspeak": {"sbrief": "아래쪽으로 꺾인 오른쪽 화살표"}}, "key": "2B0E"}, {"mappings": {"default": {"default": "위쪽으로 꺾인 오른쪽 화살표"}, "mathspeak": {"sbrief": "위쪽으로 꺾인 오른쪽 화살표"}}, "key": "2B0F"}, {"mappings": {"default": {"default": "아래쪽으로 꺾인 왼쪽 화살표"}, "mathspeak": {"sbrief": "아래쪽으로 꺾인 왼쪽 화살표"}}, "key": "2B10"}, {"mappings": {"default": {"default": "위쪽으로 꺾인 왼쪽 화살표"}, "mathspeak": {"sbrief": "위쪽으로 꺾인 왼쪽 화살표"}}, "key": "2B11"}, {"mappings": {"default": {"default": "작은 원이 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "작은 원이 붙은 왼쪽 화살표"}}, "key": "2B30"}, {"mappings": {"default": {"default": "세 개의 왼쪽 화살표"}, "mathspeak": {"sbrief": "세 개의 왼쪽 화살표"}}, "key": "2B31"}, {"mappings": {"default": {"default": "원과 플러스 부호 안의 왼쪽 화살표"}, "mathspeak": {"sbrief": "원과 플러스 부호 안의 왼쪽 화살표"}}, "key": "2B32"}, {"mappings": {"default": {"default": "흔들리는 긴 왼쪽 화살표"}, "mathspeak": {"sbrief": "흔들리는 긴 왼쪽 화살표"}}, "key": "2B33"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 왼쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 왼쪽 화살표"}}, "key": "2B34"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어진 왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어진 왼쪽 쌍화살표"}}, "key": "2B35"}, {"mappings": {"default": {"default": "세로선에서 시작하는 왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "세로선에서 시작하는 왼쪽 쌍화살표"}}, "key": "2B36"}, {"mappings": {"default": {"default": "왼쪽 트리플 대시 쌍화살표"}, "mathspeak": {"sbrief": "왼쪽 트리플 대시 쌍화살표"}}, "key": "2B37"}, {"mappings": {"default": {"default": "왼쪽 점선 화살표"}, "mathspeak": {"sbrief": "왼쪽 점선 화살표"}}, "key": "2B38"}, {"mappings": {"default": {"default": "세로선이 그어졌고 꼬리가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "세로선이 그어졌고 꼬리가 있는 왼쪽 화살표"}}, "key": "2B39"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어졌고 꼬리가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어졌고 꼬리가 있는 왼쪽 화살표"}}, "key": "2B3A"}, {"mappings": {"default": {"default": "꼬리가 있는 왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "꼬리가 있는 왼쪽 쌍화살표"}}, "key": "2B3B"}, {"mappings": {"default": {"default": "세로선이 그어졌고 꼬리가 있는 왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "세로선이 그어졌고 꼬리가 있는 왼쪽 쌍화살표"}}, "key": "2B3C"}, {"mappings": {"default": {"default": "두 개의 세로선이 그어졌고 꼬리가 있는 왼쪽 쌍화살표"}, "mathspeak": {"sbrief": "두 개의 세로선이 그어졌고 꼬리가 있는 왼쪽 쌍화살표"}}, "key": "2B3D"}, {"mappings": {"default": {"default": "엑스 자가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "엑스 자가 붙은 왼쪽 화살표"}}, "key": "2B3E"}, {"mappings": {"default": {"default": "왼쪽 웨이브 화살표"}}, "key": "2B3F"}, {"mappings": {"default": {"default": "위에 등호가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 등호가 붙은 왼쪽 화살표"}}, "key": "2B40"}, {"mappings": {"default": {"default": "위에 물결표가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 물결표가 붙은 왼쪽 화살표"}}, "key": "2B41"}, {"mappings": {"default": {"default": "밑에 근사 등호가 붙은 왼쪽 화살표"}, "mathspeak": {"sbrief": "밑에 근사 등호가 붙은 왼쪽 화살표"}}, "key": "2B42"}, {"mappings": {"default": {"default": "오른쪽 부등호가 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "오른쪽 부등호가 붙은 오른쪽 화살표"}}, "key": "2B43"}, {"mappings": {"default": {"default": "상위 집합이 붙은 오른쪽 화살표"}, "mathspeak": {"sbrief": "상위 집합이 붙은 오른쪽 화살표"}}, "key": "2B44"}, {"mappings": {"default": {"default": "왼쪽 쿼드러플 화살표"}, "mathspeak": {"sbrief": "왼쪽 쿼드러플 화살표"}}, "key": "2B45"}, {"mappings": {"default": {"default": "오른쪽 쿼드러플 화살표"}, "mathspeak": {"sbrief": "오른쪽 쿼드러플 화살표"}}, "key": "2B46"}, {"mappings": {"default": {"default": "위에 물결표가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "위에 물결표가 있는 오른쪽 화살표"}}, "key": "2B47"}, {"mappings": {"default": {"default": "밑에 근사 등호가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "밑에 근사 등호가 있는 오른쪽 화살표"}}, "key": "2B48"}, {"mappings": {"default": {"default": "위에 물결표가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "위에 물결표가 있는 왼쪽 화살표"}}, "key": "2B49"}, {"mappings": {"default": {"default": "밑에 근사 등호가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "밑에 근사 등호가 있는 왼쪽 화살표"}}, "key": "2B4A"}, {"mappings": {"default": {"default": "밑에 물결표가 있는 왼쪽 화살표"}, "mathspeak": {"sbrief": "밑에 물결표가 있는 왼쪽 화살표"}}, "key": "2B4B"}, {"mappings": {"default": {"default": "밑에 물결표가 있는 오른쪽 화살표"}, "mathspeak": {"sbrief": "밑에 물결표가 있는 오른쪽 화살표"}}, "key": "2B4C"}, {"mappings": {"default": {"default": "반쪽짜리 왼쪽 화살표"}, "mathspeak": {"sbrief": "반쪽짜리 왼쪽 화살표"}}, "key": "FFE9"}, {"mappings": {"default": {"default": "반쪽짜리 위쪽 화살표"}, "mathspeak": {"sbrief": "반쪽짜리 위쪽 화살표"}}, "key": "FFEA"}, {"mappings": {"default": {"default": "반쪽짜리 오른쪽 화살표"}, "mathspeak": {"sbrief": "반쪽짜리 오른쪽 화살표"}}, "key": "FFEB"}, {"mappings": {"default": {"default": "반쪽짜리 아래쪽 화살표"}, "mathspeak": {"sbrief": "반쪽짜리 아래쪽 화살표"}}, "key": "FFEC"}], "ko/symbols/math_characters.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "필기체 l"}}, "key": "2113"}, {"mappings": {"default": {"default": "필기체 대문자 P"}, "mathspeak": {"default": "필기체 대문자 P"}}, "key": "2118"}, {"mappings": {"default": {"default": "칠판 볼드체 파이"}}, "key": "213C"}, {"mappings": {"default": {"default": "칠판 볼드체 감마"}}, "key": "213D"}, {"mappings": {"default": {"default": "칠판 볼드체 대문자 감마"}, "mathspeak": {"default": "칠판 볼드체 대문자 감마"}}, "key": "213E"}, {"mappings": {"default": {"default": "칠판 볼드체 대문자 파이"}, "mathspeak": {"default": "칠판 볼드체 대문자 파이"}}, "key": "213F"}, {"mappings": {"default": {"default": "칠판 볼드체 시그마"}, "mathspeak": {"default": "칠판 볼드체 시그마"}}, "key": "2140"}, {"mappings": {"default": {"default": "칠판 볼드 이텔릭체 대문자 D"}, "mathspeak": {"default": "칠판 볼드 이텔릭체 대문자 D"}}, "key": "2145"}, {"mappings": {"default": {"default": "칠판 볼드 이텔릭체 e"}}, "key": "2146"}, {"mappings": {"default": {"default": "칠판 볼드 이텔릭체 e"}}, "key": "2147"}, {"mappings": {"default": {"default": "칠판 볼드 이텔릭체 i"}}, "key": "2148"}, {"mappings": {"default": {"default": "칠판 볼드 이텔릭체 j"}}, "key": "2149"}, {"mappings": {"default": {"default": "점 없는 이텔릭체 i"}}, "key": "1D6A4"}, {"mappings": {"default": {"default": "점 없는 이텔릭체 j"}}, "key": "1D6A5"}], "ko/symbols/math_delimiters.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "괄호 열고"}, "mathspeak": {"brief": "괄열", "sbrief": "괄열"}, "clearspeak": {"default": "괄호 열고"}}, "key": "0028"}, {"mappings": {"default": {"default": "괄호 닫고"}, "mathspeak": {"brief": "괄닫", "sbrief": "괄닫"}, "clearspeak": {"default": "괄호 닫고"}}, "key": "0029"}, {"mappings": {"default": {"default": "대괄호 열고"}, "mathspeak": {"brief": "대괄열", "sbrief": "대괄열"}, "clearspeak": {"default": "대괄호 열고"}}, "key": "005B"}, {"mappings": {"default": {"default": "대괄호 닫고"}, "mathspeak": {"brief": "대괄닫", "sbrief": "대괄닫"}, "clearspeak": {"default": "대괄호 닫고"}}, "key": "005D"}, {"mappings": {"default": {"default": "중괄호 열고"}, "mathspeak": {"sbrief": "중괄열"}, "clearspeak": {"default": "중괄호 열고"}}, "key": "007B"}, {"mappings": {"default": {"default": "중괄호 닫고"}, "mathspeak": {"sbrief": "중괄닫"}, "clearspeak": {"default": "중괄호 닫고"}}, "key": "007D"}, {"mappings": {"default": {"default": "꺾쇠 괄호 열고"}, "mathspeak": {"brief": "꺾쇠 괄열", "sbrief": "꺾쇠 괄열"}}, "key": "2045"}, {"mappings": {"default": {"default": "꺾쇠 괄호 닫고"}, "mathspeak": {"brief": "꺾쇠 괄닫", "sbrief": "꺾쇠 괄닫"}}, "key": "2046"}, {"mappings": {"default": {"default": "왼쪽 천장"}}, "key": "2308"}, {"mappings": {"default": {"default": "오른쪽 천장"}}, "key": "2309"}, {"mappings": {"default": {"default": "왼쪽 층"}}, "key": "230A"}, {"mappings": {"default": {"default": "오른쪽 층"}}, "key": "230B"}, {"mappings": {"default": {"default": "오른쪽 아래 크랍"}}, "key": "230C"}, {"mappings": {"default": {"default": "왼쪽 아래 크랍"}}, "key": "230D"}, {"mappings": {"default": {"default": "오른쪽 위 크랍"}}, "key": "230E"}, {"mappings": {"default": {"default": "왼쪽 위 크랍"}}, "key": "230F"}, {"mappings": {"default": {"default": "왼쪽 상단 모서리"}}, "key": "231C"}, {"mappings": {"default": {"default": "오른쪽 상단 모서리"}}, "key": "231D"}, {"mappings": {"default": {"default": "왼쪽 하단 모서리"}}, "key": "231E"}, {"mappings": {"default": {"default": "오른쪽 하단 모서리"}}, "key": "231F"}, {"mappings": {"default": {"default": "상단 절반 적분"}}, "key": "2320"}, {"mappings": {"default": {"default": "하단 절반 적분"}}, "key": "2321"}, {"mappings": {"default": {"default": "꺾쇠 괄호 열고", "physics": "bra"}}, "key": "2329"}, {"mappings": {"default": {"default": "꺾쇠 괄호 닫고", "physics": "ket"}}, "key": "232A"}, {"mappings": {"default": {"default": "위쪽이 구부러진 괄호 열고"}, "mathspeak": {"brief": "위쪽이 구부러진 괄호 열고", "sbrief": "위쪽이 구부러진 괄호 열고"}}, "key": "239B"}, {"mappings": {"default": {"default": "괄호 열고 확장"}, "mathspeak": {"brief": "괄호 열고 확장", "sbrief": "괄호 열고 확장"}}, "key": "239C"}, {"mappings": {"default": {"default": "아래쪽이 구부러진 괄호 열고"}, "mathspeak": {"brief": "아래쪽이 구부러진 괄호 열고", "sbrief": "아래쪽이 구부러진 괄호 열고"}}, "key": "239D"}, {"mappings": {"default": {"default": "위쪽이 구부러진 괄호 닫고"}, "mathspeak": {"brief": "위쪽이 구부러진 괄호 닫고", "sbrief": "위쪽이 구부러진 괄호 닫고"}}, "key": "239E"}, {"mappings": {"default": {"default": "괄호 닫고 확장"}, "mathspeak": {"brief": "괄호 닫고 확장", "sbrief": "괄호 닫고 확장"}}, "key": "239F"}, {"mappings": {"default": {"default": "아래쪽이 구부러진 괄호 닫고"}, "mathspeak": {"brief": "아래쪽이 구부러진 괄호 닫고", "sbrief": "아래쪽이 구부러진 괄호 닫고"}}, "key": "23A0"}, {"mappings": {"default": {"default": "위쪽만 구부러진 대괄호 열고"}, "mathspeak": {"brief": "위쪽만 구부러진 대괄호 열고", "sbrief": "위쪽만 구부러진 대괄호 열고"}}, "key": "23A1"}, {"mappings": {"default": {"default": "확장 대괄호 열고"}, "mathspeak": {"brief": "확장 대괄호 열고", "sbrief": "확장 대괄호 열고"}}, "key": "23A2"}, {"mappings": {"default": {"default": "아래쪽만 구부러진 대괄호 열고"}, "mathspeak": {"brief": "아래쪽만 구부러진 대괄호 열고", "sbrief": "아래쪽만 구부러진 대괄호 열고"}}, "key": "23A3"}, {"mappings": {"default": {"default": "위쪽만 구부러진 대괄호 닫고"}, "mathspeak": {"brief": "위쪽만 구부러진 대괄호 닫고", "sbrief": "위쪽만 구부러진 대괄호 닫고"}}, "key": "23A4"}, {"mappings": {"default": {"default": "확장 대괄호 닫고"}, "mathspeak": {"brief": "확장 대괄호 닫고", "sbrief": "확장 대괄호 닫고"}}, "key": "23A5"}, {"mappings": {"default": {"default": "아래쪽만 구부러진 대괄호 닫고"}, "mathspeak": {"brief": "아래쪽만 구부러진 대괄호 닫고", "sbrief": "아래쪽만 구부러진 대괄호 닫고"}}, "key": "23A6"}, {"mappings": {"default": {"default": "위쪽만 구부러진 중괄호 열고"}, "mathspeak": {"sbrief": "위쪽만 구부러진 중괄호 열고"}}, "key": "23A7"}, {"mappings": {"default": {"default": "위아래 열려진 중괄호 열고"}, "mathspeak": {"sbrief": "위아래 열려진 중괄호 열고"}}, "key": "23A8"}, {"mappings": {"default": {"default": "아래쪽만 구부러진 중괄호 열고"}, "mathspeak": {"sbrief": "아래쪽만 구부러진 중괄호 열고"}}, "key": "23A9"}, {"mappings": {"default": {"default": "확장 중괄호"}}, "key": "23AA"}, {"mappings": {"default": {"default": "위쪽만 구부러진 중괄호 닫고"}, "mathspeak": {"sbrief": "위쪽만 구부러진 중괄호 닫고"}}, "key": "23AB"}, {"mappings": {"default": {"default": "아래쪽만 구부러진 중괄호 닫고"}, "mathspeak": {"sbrief": "아래쪽만 구부러진 중괄호 닫고"}}, "key": "23AC"}, {"mappings": {"default": {"default": "위아래 열려진 중괄호 닫고"}, "mathspeak": {"sbrief": "위아래 열려진 중괄호 닫고"}}, "key": "23AD"}, {"mappings": {"default": {"default": "확장된 인테그랄"}}, "key": "23AE"}, {"mappings": {"default": {"default": "확장된 수평선"}}, "key": "23AF"}, {"mappings": {"default": {"default": "상단 왼쪽 하단 오른쪽으로 굽혀진 중괄호"}}, "key": "23B0"}, {"mappings": {"default": {"default": "상단 오른쪽 하단 쪽으로 굽혀진 중괄호"}}, "key": "23B1"}, {"mappings": {"default": {"default": "상단 절반 시그마"}}, "key": "23B2"}, {"mappings": {"default": {"default": "하단 절반 시그마"}}, "key": "23B3"}, {"mappings": {"default": {"default": "시계방향으로 90도 돌린 중괄호"}, "mathspeak": {"brief": "시계방향으로 90도 돌린 중괄호", "sbrief": "시계방향으로 90도 돌린 중괄호"}}, "key": "23B4"}, {"mappings": {"default": {"default": "반시계방향으로 90도 돌린 중괄호"}, "mathspeak": {"brief": "반시계방향으로 90도 돌린 중괄호", "sbrief": "반시계방향으로 90도 돌린 중괄호"}}, "key": "23B5"}, {"mappings": {"default": {"default": "반시계방향으로 90도 돌린 중괄호 위에 시계방향으로 90도 돌린 중괄호"}, "mathspeak": {"brief": "반시계방향으로 90도 돌린 중괄호 위에 시계방향으로 90도 돌린 중괄호", "sbrief": "반시계방향으로 90도 돌린 중괄호 위에 시계방향으로 90도 돌린 중괄호"}}, "key": "23B6"}, {"mappings": {"default": {"default": "눈금 기호 하단"}}, "key": "23B7"}, {"mappings": {"default": {"default": "왼쪽 세로선"}}, "key": "23B8"}, {"mappings": {"default": {"default": "오른쪽 세로선"}}, "key": "23B9"}, {"mappings": {"default": {"default": "위쪽 괄호"}, "mathspeak": {"brief": "위쪽 괄호", "sbrief": "위쪽 괄호"}}, "key": "23DC"}, {"mappings": {"default": {"default": "아래쪽 괄호"}, "mathspeak": {"brief": "아래쪽 괄호", "sbrief": "아래쪽 괄호"}}, "key": "23DD"}, {"mappings": {"default": {"default": "위쪽 중괄호"}, "mathspeak": {"sbrief": "위쪽 중괄호"}}, "key": "23DE"}, {"mappings": {"default": {"default": "아래쪽 중괄호"}, "mathspeak": {"sbrief": "아래쪽 중괄호"}}, "key": "23DF"}, {"mappings": {"default": {"default": "상단 거북이 모양 중괄호"}}, "key": "23E0"}, {"mappings": {"default": {"default": "하단 거북이 모양 중괄호"}}, "key": "23E1"}, {"mappings": {"default": {"default": "중간 괄호 열고"}, "mathspeak": {"brief": "중간 괄호 열고", "sbrief": "중간 괄호 열고"}}, "key": "2768"}, {"mappings": {"default": {"default": "중간 괄호 닫고"}, "mathspeak": {"brief": "중간 괄호 닫고", "sbrief": "중간 괄호 닫고"}}, "key": "2769"}, {"mappings": {"default": {"default": "중간 평평한 괄호 열고"}, "mathspeak": {"brief": "중간 평평한 괄호 열고", "sbrief": "중간 평평한 괄호 열고"}}, "key": "276A"}, {"mappings": {"default": {"default": "중간 평평한 괄호 닫고"}, "mathspeak": {"brief": "중간 평평한 괄호 닫고", "sbrief": "중간 평평한 괄호 닫고"}}, "key": "276B"}, {"mappings": {"default": {"default": "중간 포인팅 각도 중괄호 열고"}}, "key": "276C"}, {"mappings": {"default": {"default": "중간 포인팅 각도 중괄호 닫고"}}, "key": "276D"}, {"mappings": {"default": {"default": "두꺼운 왼쪽 포인팅 앵글 따옴표"}}, "key": "276E"}, {"mappings": {"default": {"default": "두꺼운 오른쪽 각도 마크"}}, "key": "276F"}, {"mappings": {"default": {"default": "두꺼운 포인팅 앵글 중괄호 열고"}}, "key": "2770"}, {"mappings": {"default": {"default": "두꺼운 포인팅 앵글 중괄호 닫고"}}, "key": "2771"}, {"mappings": {"default": {"default": "얇은 거북이 모양 중괄호 열고"}}, "key": "2772"}, {"mappings": {"default": {"default": "얇은 쪽 거북이 모양 중괄호"}}, "key": "2773"}, {"mappings": {"default": {"default": "중간 중괄호 열고"}, "mathspeak": {"sbrief": "중간 중괄호 열고"}}, "key": "2774"}, {"mappings": {"default": {"default": "중간 중괄호 닫고"}, "mathspeak": {"sbrief": "중간 중괄호 닫고"}}, "key": "2775"}, {"mappings": {"default": {"default": "왼쪽 S 자형 가방모양 구분 기호"}}, "key": "27C5"}, {"mappings": {"default": {"default": "오른쪽 S 자형 가방모양 구분 기호"}}, "key": "27C6"}, {"mappings": {"default": {"default": "두겹 대괄호 열고"}}, "key": "27E6"}, {"mappings": {"default": {"default": "두겹 대괄호 닫고"}}, "key": "27E7"}, {"mappings": {"default": {"default": "구부러진 대괄호 열고"}, "mathspeak": {"sbrief": "구부러진 대괄호 열고"}}, "key": "27E8"}, {"mappings": {"default": {"default": "구부러진 대괄호 닫고"}, "mathspeak": {"sbrief": "구부러진 대괄호 닫고"}}, "key": "27E9"}, {"mappings": {"default": {"default": "두겹 구부러진 대괄호 열고"}}, "key": "27EA"}, {"mappings": {"default": {"default": "두겹 구부러진 대괄호 닫고"}}, "key": "27EB"}, {"mappings": {"default": {"default": "두겹 구부러진 거북이 모양 대괄호 열고"}}, "key": "27EC"}, {"mappings": {"default": {"default": "두겹 구부러진 거북이 모양 대괄호 닫고"}}, "key": "27ED"}, {"mappings": {"default": {"default": "수학적 편평한 괄호 열고"}, "mathspeak": {"brief": "수학적 편평한 괄호 열고", "sbrief": "수학적 편평한 괄호 열고"}}, "key": "27EE"}, {"mappings": {"default": {"default": "수학적 편평한 괄호 닫고"}, "mathspeak": {"brief": "수학적 편평한 괄호 닫고", "sbrief": "수학적 편평한 괄호 닫고"}}, "key": "27EF"}, {"mappings": {"default": {"default": "두겹 흰색 중괄호 열고"}}, "key": "2983"}, {"mappings": {"default": {"default": "두겹 흰색 중괄호 닫고"}}, "key": "2984"}, {"mappings": {"default": {"default": "두겹 괄호 열고"}, "mathspeak": {"brief": "두겹 괄호 열고", "sbrief": "두겹 괄호 열고"}}, "key": "2985"}, {"mappings": {"default": {"default": "두겹 괄호 닫고"}, "mathspeak": {"brief": "두겹 괄호 닫고", "sbrief": "두겹 괄호 닫고"}}, "key": "2986"}, {"mappings": {"default": {"default": "Z 표기법 두겹 대괄호 열고"}}, "key": "2987"}, {"mappings": {"default": {"default": "Z 표기법 두겹 대괄호 닫고"}}, "key": "2988"}, {"mappings": {"default": {"default": "Z 표기법 삼각형 괄호 열고"}}, "key": "2989"}, {"mappings": {"default": {"default": "Z 표기법 삼각형 괄호 닫고"}}, "key": "298A"}, {"mappings": {"default": {"default": "밑줄이 있는 대괄호 열고"}, "mathspeak": {"brief": "밑줄이 있는 대괄호 열고", "sbrief": "밑줄이 있는 대괄호 열고"}}, "key": "298B"}, {"mappings": {"default": {"default": "밑줄이 있는 대괄호 닫고"}, "mathspeak": {"brief": "밑줄이 있는 대괄호 닫고", "sbrief": "밑줄이 있는 대괄호 닫고"}}, "key": "298C"}, {"mappings": {"default": {"default": "상단 모서리에 대각선이 있는 대괄호 열고"}, "mathspeak": {"brief": "상단 모서리에 대각선이 있는 대괄호 열고", "sbrief": "상단 모서리에 대각선이 있는 대괄호 열고"}}, "key": "298D"}, {"mappings": {"default": {"default": "상단 모서리에 대각선이 있는 대괄호 닫고"}, "mathspeak": {"brief": "상단 모서리에 대각선이 있는 대괄호 닫고", "sbrief": "상단 모서리에 대각선이 있는 대괄호 닫고"}}, "key": "298E"}, {"mappings": {"default": {"default": "하단 모서리에 대각선이 있는 대괄호 열고"}, "mathspeak": {"brief": "하단 모서리에 대각선이 있는 대괄호 열고", "sbrief": "하단 모서리에 대각선이 있는 대괄호 열고"}}, "key": "298F"}, {"mappings": {"default": {"default": "하단 모서리에 대각선이 있는 대괄호 닫고"}, "mathspeak": {"brief": "하단 모서리에 대각선이 있는 대괄호 닫고", "sbrief": "하단 모서리에 대각선이 있는 대괄호 닫고"}}, "key": "2990"}, {"mappings": {"default": {"default": "점이 있는 대괄호 열고"}, "mathspeak": {"sbrief": "점이 있는 대괄호 열고"}}, "key": "2991"}, {"mappings": {"default": {"default": "점이 있는 대괄호 닫고"}, "mathspeak": {"sbrief": "점이 있는 대괄호 닫고"}}, "key": "2992"}, {"mappings": {"default": {"default": "아치보다 작은 대괄호 열고"}}, "key": "2993"}, {"mappings": {"default": {"default": "아치보다 큰 대괄호 닫고"}}, "key": "2994"}, {"mappings": {"default": {"default": "대괄호보다 큰 두개 왼쪽 방향 호"}}, "key": "2995"}, {"mappings": {"default": {"default": "대괄호보다 작은 두개 왼쪽 방향 호"}}, "key": "2996"}, {"mappings": {"default": {"default": "검은 거북이 모양 대괄호 열고"}}, "key": "2997"}, {"mappings": {"default": {"default": "검은 거북이 모양 대괄호 닫고"}}, "key": "2998"}, {"mappings": {"default": {"default": "왼쪽 방향 흔들리는 모양"}}, "key": "29D8"}, {"mappings": {"default": {"default": "오른쪽 방향 흔들리는 모양"}}, "key": "29D9"}, {"mappings": {"default": {"default": "왼쪽 방향 이중 흔들는 모양"}}, "key": "29DA"}, {"mappings": {"default": {"default": "오른쪽 방향 이중 흔들는 모양"}}, "key": "29DB"}, {"mappings": {"default": {"default": "포인팅 휘어진 각도 중괄호 열고"}}, "key": "29FC"}, {"mappings": {"default": {"default": "포인팅 휘어진 각도 중괄호 닫고"}}, "key": "29FD"}, {"mappings": {"default": {"default": "상단 절반 대괄호 열고"}, "mathspeak": {"brief": "상단 절반 대괄호 열고", "sbrief": "상단 절반 대괄호 열고"}}, "key": "2E22"}, {"mappings": {"default": {"default": "상단 절반 대괄호 닫고"}, "mathspeak": {"brief": "상단 절반 대괄호 닫고", "sbrief": "상단 절반 대괄호 닫고"}}, "key": "2E23"}, {"mappings": {"default": {"default": "하단 절반 대괄호 열고"}, "mathspeak": {"brief": "하단 절반 대괄호 열고", "sbrief": "하단 절반 대괄호 열고"}}, "key": "2E24"}, {"mappings": {"default": {"default": "하단 절반 대괄호 닫고"}, "mathspeak": {"brief": "하단 절반 대괄호 닫고", "sbrief": "하단 절반 대괄호 닫고"}}, "key": "2E25"}, {"mappings": {"default": {"default": "시계 방향으로 90도 회전한 U 모양 괄호"}}, "key": "2E26"}, {"mappings": {"default": {"default": "반시계 방향으로 90도 회전한 U 모양 괄호"}}, "key": "2E27"}, {"mappings": {"default": {"default": "이중 괄호 열고"}, "mathspeak": {"brief": "이중 괄호 열고", "sbrief": "이중 괄호 열고"}}, "key": "2E28"}, {"mappings": {"default": {"default": "이중 괄호 닫고"}, "mathspeak": {"brief": "이중 괄호 닫고", "sbrief": "이중 괄호 닫고"}}, "key": "2E29"}, {"mappings": {"default": {"default": "꺾쇠 괄호 열고"}, "mathspeak": {"sbrief": "꺾쇠 괄호 열고"}}, "key": "3008"}, {"mappings": {"default": {"default": "꺾쇠 괄호 닫고"}, "mathspeak": {"sbrief": "꺾쇠 괄호 닫고"}}, "key": "3009"}, {"mappings": {"default": {"default": "이중 꺾쇠 괄호 열고"}}, "key": "300A"}, {"mappings": {"default": {"default": "이중 꺾쇠 괄호 닫고"}}, "key": "300B"}, {"mappings": {"default": {"default": "절반 대괄호 열고"}}, "key": "300C"}, {"mappings": {"default": {"default": "절반 대괄호 닫고"}}, "key": "300D"}, {"mappings": {"default": {"default": "속이 빈 절반 대괄호 열고"}}, "key": "300E"}, {"mappings": {"default": {"default": "속이 빈 절반 대괄호 닫고"}}, "key": "300F"}, {"mappings": {"default": {"default": "속이 차있는 절반 대괄호 열고"}}, "key": "3010"}, {"mappings": {"default": {"default": "속이 차있는 절반 대괄호 열고"}}, "key": "3011"}, {"mappings": {"default": {"default": "거북이 모양 대괄호 열고"}}, "key": "3014"}, {"mappings": {"default": {"default": "거북이 모양 대괄호 닫고"}}, "key": "3015"}, {"mappings": {"default": {"default": "속이 빈 오목렌즈 모양 괄호 열고"}}, "key": "3016"}, {"mappings": {"default": {"default": "속이 빈 오목렌즈 모양 괄호 닫고"}}, "key": "3017"}, {"mappings": {"default": {"default": "속이 빈 거북이 모양 대괄호 열고"}}, "key": "3018"}, {"mappings": {"default": {"default": "속이 빈 거북이 모양 대괄호 닫고"}}, "key": "3019"}, {"mappings": {"default": {"default": "속이 빈 대괄호 열고"}}, "key": "301A"}, {"mappings": {"default": {"default": "속이 빈 대괄호 닫고"}}, "key": "301B"}, {"mappings": {"default": {"default": "반전된 이중 프라임 따옴표"}}, "key": "301D"}, {"mappings": {"default": {"default": "이중 프라임 따옴표"}}, "key": "301E"}, {"mappings": {"default": {"default": "하단 이중 프라임 따옴표"}}, "key": "301F"}, {"mappings": {"default": {"default": "가운데 꽃무늬가 있는 괄호 열고"}, "mathspeak": {"brief": "가운데 꽃무늬가 있는 괄호 열고", "sbrief": "가운데 꽃무늬가 있는 괄호 열고"}}, "key": "FD3E"}, {"mappings": {"default": {"default": "가운데 꽃무늬가 있는 괄호 닫고"}, "mathspeak": {"brief": "o가운데 꽃무늬가 있는 괄호 닫고", "sbrief": "가운데 꽃무늬가 있는 괄호 닫고"}}, "key": "FD3F"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 속이 빈 오목렌즈 모양의 괄호의 발표 형식"}}, "key": "FE17"}, {"mappings": {"default": {"default": "반시계 방향으로 회전한 속이 빈 오목렌즈 모양의 괄호의 발표 형식"}}, "key": "FE18"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 열린 괄호의 발표 형식"}, "mathspeak": {"brief": "시계 방향으로 회전한 열린 괄호의 발표 형식", "sbrief": "시계 방향으로 회전한 열린 괄호의 발표 형식"}}, "key": "FE35"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 열린 괄호의 발표 형식"}, "mathspeak": {"brief": "시계 방향으로 회전한 열린 괄호의 발표 형식", "sbrief": "시계 방향으로 회전한 열린 괄호의 발표 형식"}}, "key": "FE36"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 열린 중괄호의 발표 형식"}, "mathspeak": {"sbrief": "시계 방향으로 회전한 열린 중괄호의 발표 형식"}}, "key": "FE37"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 닫힌 중괄호의 발표 형식"}, "mathspeak": {"sbrief": "시계 방향으로 회전한 닫힌 중괄호의 발표 형식"}}, "key": "FE38"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 거북이 모양 열린 괄호의 발표 형식"}}, "key": "FE39"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 거북이 모양 닫힌 괄호의 발표 형식"}}, "key": "FE3A"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 속이 찬 오목렌즈 모양 열린 괄호의 발표 형식"}}, "key": "FE3B"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 속이 찬 오목렌즈 모양 닫힌 괄호의 발표 형식"}}, "key": "FE3C"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 이중 각진 모양 열린 괄호의 발표 형식"}}, "key": "FE3D"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 이중 각진 모양 닫힌 괄호의 발표 형식"}}, "key": "FE3E"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 각진 모양 열린 괄호의 발표 형식"}, "mathspeak": {"sbrief": "시계 방향으로 회전한 각진 모양 열린 괄호의 발표 형식"}}, "key": "FE3F"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 각진 모양 닫힌 괄호의 발표 형식"}, "mathspeak": {"sbrief": "시계 방향으로 회전한 각진 닫힌 모양 괄호의 발표 형식"}}, "key": "FE40"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 상단 절반 열린 대괄호의 발표 형식"}}, "key": "FE41"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 상단 절반 닫힌 대괄호의 발표 형식"}}, "key": "FE42"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 속이 비어 있는 상단 절반 열린 대괄호의 발표 형식"}}, "key": "FE43"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 속이 비어 있는 상단 절반 닫힌 대괄호의 발표 형식"}}, "key": "FE44"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 열린 대괄호의 발표 형식"}, "mathspeak": {"brief": "시계 방향으로 회전한 열린 대괄호의 발표 형식", "sbrief": "시계 방향으로 회전한 열린 대괄호의 발표 형식"}}, "key": "FE47"}, {"mappings": {"default": {"default": "시계 방향으로 회전한 닫힌 대괄호의 발표 형식"}, "mathspeak": {"brief": "시계 방향으로 회전한 닫힌 대괄호의 발표 형식", "sbrief": "시계 방향으로 회전한 닫힌 대괄호의 발표 형식"}}, "key": "FE48"}, {"mappings": {"default": {"default": "작은 괄호 열고"}, "mathspeak": {"brief": "작은 괄호 열고", "sbrief": "작은 괄호 열고"}}, "key": "FE59"}, {"mappings": {"default": {"default": "작은 괄호 닫고"}, "mathspeak": {"brief": "작은 괄호 닫고", "sbrief": "작은 괄호 닫고"}}, "key": "FE5A"}, {"mappings": {"default": {"default": "작은 중괄호 열고"}, "mathspeak": {"sbrief": "작은 중괄호 열고"}}, "key": "FE5B"}, {"mappings": {"default": {"default": "작은 중괄호 닫고"}, "mathspeak": {"sbrief": "작은 중괄호 닫고"}}, "key": "FE5C"}, {"mappings": {"default": {"default": "작은 거북이 모양 괄호 열고"}}, "key": "FE5D"}, {"mappings": {"default": {"default": "작은 거북이 모양 괄호 닫고"}}, "key": "FE5E"}, {"mappings": {"default": {"default": "전각 괄호 열고"}, "mathspeak": {"brief": "전각 괄호 열고", "sbrief": "전각 괄호 열고"}}, "key": "FF08"}, {"mappings": {"default": {"default": "전각 괄호 닫고"}, "mathspeak": {"brief": "전각 괄호 닫고", "sbrief": "전각 괄호 닫고"}}, "key": "FF09"}, {"mappings": {"default": {"default": "전각 대괄호 열고"}, "mathspeak": {"brief": "전각 대괄호 열고", "sbrief": "전각 대괄호 열고"}}, "key": "FF3B"}, {"mappings": {"default": {"default": "전각 대괄호 닫고"}, "mathspeak": {"brief": "전각 대괄호 닫고", "sbrief": "전각 대괄호 닫고"}}, "key": "FF3D"}, {"mappings": {"default": {"default": "전각 중괄호 열고"}, "mathspeak": {"sbrief": "전각 중괄호 열고"}}, "key": "FF5B"}, {"mappings": {"default": {"default": "전각 중괄호 닫고"}, "mathspeak": {"sbrief": "전각 중괄호 닫고"}}, "key": "FF5D"}, {"mappings": {"default": {"default": "전각 속이 빈 괄호 열고"}, "mathspeak": {"brief": "전각 속이 빈 괄호 열고", "sbrief": "전각 속이 빈 괄호 열고"}}, "key": "FF5F"}, {"mappings": {"default": {"default": "전각 속이 빈 괄호 닫고"}, "mathspeak": {"brief": "전각 속이 빈 쪽 괄호", "sbrief": "전각 속이 빈 괄호 닫고"}}, "key": "FF60"}, {"mappings": {"default": {"default": "반각 왼쪽 상단 절반 대괄호"}}, "key": "FF62"}, {"mappings": {"default": {"default": "반각 오른쪽 상단 절반 대괄호"}}, "key": "FF63"}], "ko/symbols/math_geometry.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "얇은 가로 상자 그림"}}, "key": "2500"}, {"mappings": {"default": {"default": "진한 가로 상자 그림"}}, "key": "2501"}, {"mappings": {"default": {"default": "얇은 세로 상자 그림"}}, "key": "2502"}, {"mappings": {"default": {"default": "진한 수직 상자 그림"}}, "key": "2503"}, {"mappings": {"default": {"default": "얇은 트리플 대시 수평 상자 그림"}}, "key": "2504"}, {"mappings": {"default": {"default": "진한 트리플 대시 수평 상자 그림"}}, "key": "2505"}, {"mappings": {"default": {"default": "얇은 트리플 대시 수직 상자 그림"}}, "key": "2506"}, {"mappings": {"default": {"default": "진한 트리플 대시 수직 상자 그림"}}, "key": "2507"}, {"mappings": {"default": {"default": "얇은 쿼드러플 대시 수평 상자 그림"}}, "key": "2508"}, {"mappings": {"default": {"default": "진한 쿼드러플 대시 수평 상자 그림"}}, "key": "2509"}, {"mappings": {"default": {"default": "얇은 쿼드러플 대쉬 수직 상자 그림"}}, "key": "250A"}, {"mappings": {"default": {"default": "진한 쿼드러플 대쉬 수직 상자 그림"}}, "key": "250B"}, {"mappings": {"default": {"default": "아래 오른쪽 상자 그림"}}, "key": "250C"}, {"mappings": {"default": {"default": "얇은 아래 진한 오른쪽 상자 그림"}}, "key": "250D"}, {"mappings": {"default": {"default": "진한 아래 얇은 오른쪽 상자 그림"}}, "key": "250E"}, {"mappings": {"default": {"default": "진한 아래와 오른쪽 상자 그림"}}, "key": "250F"}, {"mappings": {"default": {"default": "얇은 아래 및 왼쪽 상자 그림"}}, "key": "2510"}, {"mappings": {"default": {"default": "얇은 아래 진한 왼쪽 상자 그림"}}, "key": "2511"}, {"mappings": {"default": {"default": "진한 아래 얇은 왼쪽 상자 그림"}}, "key": "2512"}, {"mappings": {"default": {"default": "진한 아래 및 왼쪽 상자 그림"}}, "key": "2513"}, {"mappings": {"default": {"default": "얇은 위 및 오른쪽 상자 그림"}}, "key": "2514"}, {"mappings": {"default": {"default": "얇은 위 진한 오른쪽 상자 그림"}}, "key": "2515"}, {"mappings": {"default": {"default": "진한 위 얇은 오른쪽 상자 그림"}}, "key": "2516"}, {"mappings": {"default": {"default": "진한 위 및 오른쪽 상자 그림"}}, "key": "2517"}, {"mappings": {"default": {"default": "얇은 위 및 왼쪽 상자 그림"}}, "key": "2518"}, {"mappings": {"default": {"default": "얇은 위 진한 왼쪽 상자 그림"}}, "key": "2519"}, {"mappings": {"default": {"default": "진한 위 얇은 왼쪽 상자 그림"}}, "key": "251A"}, {"mappings": {"default": {"default": "진한 위 및 왼쪽 상자 그림"}}, "key": "251B"}, {"mappings": {"default": {"default": "얇은 수직 및 오른쪽 상자 그림"}}, "key": "251C"}, {"mappings": {"default": {"default": "얇은 수직 진한 오른쪽 상자 그림"}}, "key": "251D"}, {"mappings": {"default": {"default": "진한 위 얇은 오른쪽 및 아래 상자 그림"}}, "key": "251E"}, {"mappings": {"default": {"default": "진한 아래 얇은 오른쪽 및 위 상자 그림"}}, "key": "251F"}, {"mappings": {"default": {"default": "진한 수직 얇은 오른쪽 상자 그림"}}, "key": "2520"}, {"mappings": {"default": {"default": "얇은 아래 진한 오른쪽 및 위 상자 그림"}}, "key": "2521"}, {"mappings": {"default": {"default": "얇은 위, 진한 오른쪽 및 아래 상자 그림"}}, "key": "2522"}, {"mappings": {"default": {"default": "진한 수직 및 오른쪽 상자 그림"}}, "key": "2523"}, {"mappings": {"default": {"default": "얇은 수직 및 왼쪽 상자 그림"}}, "key": "2524"}, {"mappings": {"default": {"default": "얇은 수직 진한 왼쪽 상자 그림"}}, "key": "2525"}, {"mappings": {"default": {"default": "진한 위 얇은 왼쪽 및 아래 상자 그림"}}, "key": "2526"}, {"mappings": {"default": {"default": "진한 아래 얇은 왼쪽 및 위 상자 그림"}}, "key": "2527"}, {"mappings": {"default": {"default": "진한 수직 얇은 왼쪽 상자 그림"}}, "key": "2528"}, {"mappings": {"default": {"default": "얇은 아래 진한 왼쪽 및 위 상자 그림"}}, "key": "2529"}, {"mappings": {"default": {"default": "얇은 위 진한 왼쪽 및 아래 상자 그림"}}, "key": "252A"}, {"mappings": {"default": {"default": "진한 수직 및 왼쪽 상자 그림"}}, "key": "252B"}, {"mappings": {"default": {"default": "얇은 아래 및 수평 상자 그림"}}, "key": "252C"}, {"mappings": {"default": {"default": "진한 왼쪽 얇은 오른쪽 아래 상자 그림"}}, "key": "252D"}, {"mappings": {"default": {"default": "진한 오른쪽 얇은 왼쪽 아래 상자 그림"}}, "key": "252E"}, {"mappings": {"default": {"default": "얇은 아래 진한 수평 상자 그림"}}, "key": "252F"}, {"mappings": {"default": {"default": "진한 아래 얇은 수평 상자 그림"}}, "key": "2530"}, {"mappings": {"default": {"default": "얇은 오른쪽 진한 왼쪽 아래 상자 그림"}}, "key": "2531"}, {"mappings": {"default": {"default": "얇은 왼쪽 진한 오른쪽 아래 상자 그림"}}, "key": "2532"}, {"mappings": {"default": {"default": "진한 아래 수평 상자 그림"}}, "key": "2533"}, {"mappings": {"default": {"default": "얇은 위 수평 상자 그림"}}, "key": "2534"}, {"mappings": {"default": {"default": "진한 왼쪽 얇은 오른쪽 위 상자 그림"}}, "key": "2535"}, {"mappings": {"default": {"default": "진한 오른쪽 얇은 왼쪽 위 상자 그림"}}, "key": "2536"}, {"mappings": {"default": {"default": "얇은 위 진한 수평 상자 그림"}}, "key": "2537"}, {"mappings": {"default": {"default": "진한 위 얇은 수평 상자 그림"}}, "key": "2538"}, {"mappings": {"default": {"default": "얇은 오른쪽 진한 왼쪽 위 상자 그림"}}, "key": "2539"}, {"mappings": {"default": {"default": "얇은 왼쪽 진한 오른쪽 위 상자 그림"}}, "key": "253A"}, {"mappings": {"default": {"default": "진한 위 수평 상자 그림"}}, "key": "253B"}, {"mappings": {"default": {"default": "얇은 수직 수평 상자 그림"}}, "key": "253C"}, {"mappings": {"default": {"default": "진한 왼쪽 얇은 오른쪽 수직 상자 그림"}}, "key": "253D"}, {"mappings": {"default": {"default": "진한 오른쪽 얇은 왼쪽 수직 상자 그림"}}, "key": "253E"}, {"mappings": {"default": {"default": "얇은 수직 진한 수평 상자 그림"}}, "key": "253F"}, {"mappings": {"default": {"default": "진한 위 얇은 아래 수평 상자 그림"}}, "key": "2540"}, {"mappings": {"default": {"default": "진한 아래 얇은 위 수평 상자 그림"}}, "key": "2541"}, {"mappings": {"default": {"default": "진한 수직 얇은 수평 상자 그림"}}, "key": "2542"}, {"mappings": {"default": {"default": "진한 오른쪽 위 얇은 왼쪽 아래 상자 그림"}}, "key": "2543"}, {"mappings": {"default": {"default": "진한 오른쪽 위 얇은 왼쪽 아래 상자 그림"}}, "key": "2544"}, {"mappings": {"default": {"default": "진한 왼쪽 아래 얇은 오른쪽 위 상자 그림"}}, "key": "2545"}, {"mappings": {"default": {"default": "진한 오른쪽 아래, 얇은 왼쪽 위 상자 그림"}}, "key": "2546"}, {"mappings": {"default": {"default": "얇은 아래, 진한 위 수평 상자 그림"}}, "key": "2547"}, {"mappings": {"default": {"default": "얇은 위, 진한 아래 수평 상자 그림"}}, "key": "2548"}, {"mappings": {"default": {"default": "얇은 오른쪽, 진한 왼쪽 수직 상자 그림"}}, "key": "2549"}, {"mappings": {"default": {"default": "얇은 왼쪽, 진한 오른쪽 수직 상자 그림"}}, "key": "254A"}, {"mappings": {"default": {"default": "진한 수직 수평 상자 그림"}}, "key": "254B"}, {"mappings": {"default": {"default": "얇은 수평 더블 대시 상자 그림"}}, "key": "254C"}, {"mappings": {"default": {"default": "진한 수평 더블 대시 상자 그림"}}, "key": "254D"}, {"mappings": {"default": {"default": "얇은 수직 더블 대시 상자 그림"}}, "key": "254E"}, {"mappings": {"default": {"default": "진한 수평 더블 대시 상자 그림"}}, "key": "254F"}, {"mappings": {"default": {"default": "이중 수평 상자 그림"}}, "key": "2550"}, {"mappings": {"default": {"default": "이중 수직 상자 그림"}}, "key": "2551"}, {"mappings": {"default": {"default": "하나 아래, 오른쪽 이중 상자 그림"}}, "key": "2552"}, {"mappings": {"default": {"default": "이중 아래 오른쪽 하나 상자 그리기"}}, "key": "2553"}, {"mappings": {"default": {"default": "이중 아래 이중 오른쪽 상자 그리기"}}, "key": "2554"}, {"mappings": {"default": {"default": "단일 아래 이중 왼쪽 상자 그림"}}, "key": "2555"}, {"mappings": {"default": {"default": "이중 아래 단일 왼쪽 상자 그림"}}, "key": "2556"}, {"mappings": {"default": {"default": "이중 아래 왼쪽 상자 그림"}}, "key": "2557"}, {"mappings": {"default": {"default": "단일 위 이중 오른쪽 상자 그림"}}, "key": "2558"}, {"mappings": {"default": {"default": "이중 위 단일 왼쪽 상자 그림"}}, "key": "2559"}, {"mappings": {"default": {"default": "이중 위 오른쪽 상자 그림"}}, "key": "255A"}, {"mappings": {"default": {"default": "단일 위 이중 왼쪽 상자 그림"}}, "key": "255B"}, {"mappings": {"default": {"default": "이중 위 단일 왼쪽 상자 그림"}}, "key": "255C"}, {"mappings": {"default": {"default": "이중 위 왼쪽 상자 그림"}}, "key": "255D"}, {"mappings": {"default": {"default": "단일 수직 이중 오른쪽 상자 그림"}}, "key": "255E"}, {"mappings": {"default": {"default": "이중 수직 단일 오른쪽 상자 그림"}}, "key": "255F"}, {"mappings": {"default": {"default": "이중 수직 오른쪽 상자 그림"}}, "key": "2560"}, {"mappings": {"default": {"default": "단일 수직 이중 왼쪽 상자 그림"}}, "key": "2561"}, {"mappings": {"default": {"default": "이중 수직 단일 왼쪽 상자 그림"}}, "key": "2562"}, {"mappings": {"default": {"default": "이중 수직 및 왼쪽 상자 그림"}}, "key": "2563"}, {"mappings": {"default": {"default": "단일 아래 이중 수평 상자 그림"}}, "key": "2564"}, {"mappings": {"default": {"default": "이중 아래 단일 수평 상자 그림"}}, "key": "2565"}, {"mappings": {"default": {"default": "이중 아래 및 수평 상자 그림"}}, "key": "2566"}, {"mappings": {"default": {"default": "단일 위 이중 수평 상자 그림"}}, "key": "2567"}, {"mappings": {"default": {"default": "이중 위 단일 수평 상자 그림"}}, "key": "2568"}, {"mappings": {"default": {"default": "이중 위 및 수평 상자 그림"}}, "key": "2569"}, {"mappings": {"default": {"default": "단일 수직 이중 수평 상자 그림"}}, "key": "256A"}, {"mappings": {"default": {"default": "이중 수직 단일 수평 상자 그림"}}, "key": "256B"}, {"mappings": {"default": {"default": "이중 수평 및 수직 상자 그림"}}, "key": "256C"}, {"mappings": {"default": {"default": "얇은 오른쪽 아래 호 상자 그림"}}, "key": "256D"}, {"mappings": {"default": {"default": "얇은 아래 왼쪽 호 상자 그림"}}, "key": "256E"}, {"mappings": {"default": {"default": "얇은 위 왼쪽 호 상자 그림"}}, "key": "256F"}, {"mappings": {"default": {"default": "얇은 위 오른쪽 호 상자 그림"}}, "key": "2570"}, {"mappings": {"default": {"default": "얇은 오른위 왼아래 대각선 상자 그림"}}, "key": "2571"}, {"mappings": {"default": {"default": "얇은 왼위 오른아래 대각선 상자 그림"}}, "key": "2572"}, {"mappings": {"default": {"default": "얇은 대각선 십자형 상자 그림"}}, "key": "2573"}, {"mappings": {"default": {"default": "얇은 왼쪽 상자 그림"}}, "key": "2574"}, {"mappings": {"default": {"default": "얇은 위 상자 그림"}}, "key": "2575"}, {"mappings": {"default": {"default": "얇은 오른쪽 상자 그림"}}, "key": "2576"}, {"mappings": {"default": {"default": "얇은 아래 상자 그림"}}, "key": "2577"}, {"mappings": {"default": {"default": "진한 왼쪽 상자 그림"}}, "key": "2578"}, {"mappings": {"default": {"default": "진한 위 상자 그림"}}, "key": "2579"}, {"mappings": {"default": {"default": "진한 오른쪽 상자 그림"}}, "key": "257A"}, {"mappings": {"default": {"default": "진한 아래 상자 그림"}}, "key": "257B"}, {"mappings": {"default": {"default": "얇은 왼쪽 진한 오른쪽 상자 그림"}}, "key": "257C"}, {"mappings": {"default": {"default": "얇은 위 진한 아래 상자 그림"}}, "key": "257D"}, {"mappings": {"default": {"default": "진한 왼쪽 얇은 오른쪽 상자 그림"}}, "key": "257E"}, {"mappings": {"default": {"default": "진한 위 얇은 아래 상자 그림"}}, "key": "257F"}, {"mappings": {"default": {"default": "수평 이등분 중 맨 아래 블록"}}, "key": "2580"}, {"mappings": {"default": {"default": "수평 팔등분 중 맨 아래 블록"}}, "key": "2581"}, {"mappings": {"default": {"default": "수평 사등분 중 아래 한 블록"}}, "key": "2582"}, {"mappings": {"default": {"default": "수평 팔등분 중 아래 세 블록"}}, "key": "2583"}, {"mappings": {"default": {"default": "수평 이등분 중 아래 블록"}}, "key": "2584"}, {"mappings": {"default": {"default": "수평 팔등분 중 아래 다섯 블록"}}, "key": "2585"}, {"mappings": {"default": {"default": "수평 사등분 중 아래 세 블록"}}, "key": "2586"}, {"mappings": {"default": {"default": "수평 팔등분 중 아래 일곱 블록"}}, "key": "2587"}, {"mappings": {"default": {"default": "온전한 블록"}}, "key": "2588"}, {"mappings": {"default": {"default": "수직 팔등분 중 왼쪽 일곱 블록"}}, "key": "2589"}, {"mappings": {"default": {"default": "수직 사등분 중 왼쪽 세 블록"}}, "key": "258A"}, {"mappings": {"default": {"default": "수직 팔등분 중 왼쪽 다섯 블록"}}, "key": "258B"}, {"mappings": {"default": {"default": "수직 이등분 중 왼쪽 블록"}}, "key": "258C"}, {"mappings": {"default": {"default": "수직 팔등분 중 왼쪽 세 블록"}}, "key": "258D"}, {"mappings": {"default": {"default": "수직 사등분 중 맨 왼쪽 블록"}}, "key": "258E"}, {"mappings": {"default": {"default": "수직 팔등분 중 맨 왼쪽 블록"}}, "key": "258F"}, {"mappings": {"default": {"default": "수직 이등분 중 오른쪽 블록"}}, "key": "2590"}, {"mappings": {"default": {"default": "밝은 그림자"}}, "key": "2591"}, {"mappings": {"default": {"default": "중간 그림자"}}, "key": "2592"}, {"mappings": {"default": {"default": "어두운 그림자"}}, "key": "2593"}, {"mappings": {"default": {"default": "수평 팔등분 중 맨 위 블록"}}, "key": "2594"}, {"mappings": {"default": {"default": "수직 팔등분 중 맨 오른쪽 블록"}}, "key": "2595"}, {"mappings": {"default": {"default": "왼쪽 아래 사분면"}}, "key": "2596"}, {"mappings": {"default": {"default": "오른쪽 아래 사분면"}}, "key": "2597"}, {"mappings": {"default": {"default": "왼쪽 위 사분면"}}, "key": "2598"}, {"mappings": {"default": {"default": "왼위 및 왼아래 및 오른아래 사분면"}}, "key": "2599"}, {"mappings": {"default": {"default": "왼위 및 오른아래 사분면"}}, "key": "259A"}, {"mappings": {"default": {"default": "왼위 및 오른위 및 왼아래 사분면"}}, "key": "259B"}, {"mappings": {"default": {"default": "왼위 및 오른위 및 오른아래 사분면"}}, "key": "259C"}, {"mappings": {"default": {"default": "오른위 사분면"}}, "key": "259D"}, {"mappings": {"default": {"default": "오른위 및 왼아래 사분면"}}, "key": "259E"}, {"mappings": {"default": {"default": "오른위 및 왼아래 및 오른아래 사분면"}}, "key": "259F"}, {"mappings": {"default": {"default": "검은 정사각형"}}, "key": "25A0"}, {"mappings": {"default": {"default": "흰색 정사각형"}}, "key": "25A1"}, {"mappings": {"default": {"default": "둥근 모서리의 흰색 정사각형"}}, "key": "25A2"}, {"mappings": {"default": {"default": "검은 색 작은 정사각형을 포함하는 흰색 정사각형"}}, "key": "25A3"}, {"mappings": {"default": {"default": "수평으로 채워진 정사각형"}}, "key": "25A4"}, {"mappings": {"default": {"default": "수직으로 채워진 정사각형"}}, "key": "25A5"}, {"mappings": {"default": {"default": "직교로 채워진 정사각형"}}, "key": "25A6"}, {"mappings": {"default": {"default": "왼위 오른아래 사선으로 채워진 정사각형"}}, "key": "25A7"}, {"mappings": {"default": {"default": "오른위 왼아래 사선으로 채워진 정사각형"}}, "key": "25A8"}, {"mappings": {"default": {"default": "대각선 직교로 채워진 정사각형"}}, "key": "25A9"}, {"mappings": {"default": {"default": "검은 작은 정사각형"}}, "key": "25AA"}, {"mappings": {"default": {"default": "흰 작은 정사각형"}}, "key": "25AB"}, {"mappings": {"default": {"default": "검은 직사각형"}}, "key": "25AC"}, {"mappings": {"default": {"default": "힌 직사각형"}}, "key": "25AD"}, {"mappings": {"default": {"default": "검은 수직 직사각형"}}, "key": "25AE"}, {"mappings": {"default": {"default": "하얀 수직 직사각형"}}, "key": "25AF"}, {"mappings": {"default": {"default": "검은 평행사변형"}}, "key": "25B0"}, {"mappings": {"default": {"default": "흰 평행사변형"}}, "key": "25B1"}, {"mappings": {"default": {"default": "검은 상향 삼각형"}}, "key": "25B2"}, {"mappings": {"default": {"default": "흰색 상향 삼각형"}}, "key": "25B3"}, {"mappings": {"default": {"default": "작은 검은 상향 삼각형"}}, "key": "25B4"}, {"mappings": {"default": {"default": "작은 흰색 상향 삼각형"}}, "key": "25B5"}, {"mappings": {"default": {"default": "검은 우향 삼각형"}}, "key": "25B6"}, {"mappings": {"default": {"default": "흰색 우향 삼각형"}}, "key": "25B7"}, {"mappings": {"default": {"default": "작은 검은 우향 삼각형"}}, "key": "25B8"}, {"mappings": {"default": {"default": "작은 흰색 우향 삼각형"}}, "key": "25B9"}, {"mappings": {"default": {"default": "검은색 오른쪽 포인터"}}, "key": "25BA"}, {"mappings": {"default": {"default": "흰색 오른쪽 포인터"}}, "key": "25BB"}, {"mappings": {"default": {"default": "검은색 하향 삼각형"}}, "key": "25BC"}, {"mappings": {"default": {"default": "흰색 하향 삼각형"}}, "key": "25BD"}, {"mappings": {"default": {"default": "작은 검은색 하향 삼각형"}}, "key": "25BE"}, {"mappings": {"default": {"default": "작은 흰색 하향 삼각형"}}, "key": "25BF"}, {"mappings": {"default": {"default": "검은색 좌향 삼각형"}}, "key": "25C0"}, {"mappings": {"default": {"default": "흰색 좌향 삼각형"}}, "key": "25C1"}, {"mappings": {"default": {"default": "작은 검은색 좌향 포인팅 삼각형"}}, "key": "25C2"}, {"mappings": {"default": {"default": "작은 흰색 좌향 포인팅 삼각형"}}, "key": "25C3"}, {"mappings": {"default": {"default": "검은색 좌향 포인팅 포인터"}}, "key": "25C4"}, {"mappings": {"default": {"default": "흰색 좌향 포인팅 포인터"}}, "key": "25C5"}, {"mappings": {"default": {"default": "검은 다이아몬드"}}, "key": "25C6"}, {"mappings": {"default": {"default": "흰 다이아몬드"}}, "key": "25C7"}, {"mappings": {"default": {"default": "검은 다이아몬드가 들어있는 흰 다이아몬드"}}, "key": "25C8"}, {"mappings": {"default": {"default": "검은 원를 포함한 흰 원"}}, "key": "25C9"}, {"mappings": {"default": {"default": "마름모꼴"}}, "key": "25CA"}, {"mappings": {"default": {"default": "흰색 원"}}, "key": "25CB"}, {"mappings": {"default": {"default": "점선 원"}}, "key": "25CC"}, {"mappings": {"default": {"default": "수직으로 채워진 원"}}, "key": "25CD"}, {"mappings": {"default": {"default": "흰 원를 포함한 흰 원"}}, "key": "25CE"}, {"mappings": {"default": {"default": "검은색 원"}}, "key": "25CF"}, {"mappings": {"default": {"default": "왼쪽 절반 검은 원"}}, "key": "25D0"}, {"mappings": {"default": {"default": "오른쪽 절반 검은 원"}}, "key": "25D1"}, {"mappings": {"default": {"default": "아래 절반이 검은 원"}}, "key": "25D2"}, {"mappings": {"default": {"default": "위 절반이 검은 원"}}, "key": "25D3"}, {"mappings": {"default": {"default": "오른위 사분면이 검은 원"}}, "key": "25D4"}, {"mappings": {"default": {"default": "왼위 사분면 제외 검은 원"}}, "key": "25D5"}, {"mappings": {"default": {"default": "왼쪽 절반 검정 원"}}, "key": "25D6"}, {"mappings": {"default": {"default": "오른쪽 절반 검정 원"}}, "key": "25D7"}, {"mappings": {"default": {"default": "역 탄환"}}, "key": "25D8"}, {"mappings": {"default": {"default": "반전된 흰색 원"}}, "key": "25D9"}, {"mappings": {"default": {"default": "상반 반전된 흰색 원"}}, "key": "25DA"}, {"mappings": {"default": {"default": "하반 반전된 흰색 원"}}, "key": "25DB"}, {"mappings": {"default": {"default": "왼위 사분면 원호"}}, "key": "25DC"}, {"mappings": {"default": {"default": "오른위 사분면 원호"}}, "key": "25DD"}, {"mappings": {"default": {"default": "오른아래 사분면 원호"}}, "key": "25DE"}, {"mappings": {"default": {"default": "왼아래 사분면 원호"}}, "key": "25DF"}, {"mappings": {"default": {"default": "원의 상반"}}, "key": "25E0"}, {"mappings": {"default": {"default": "원의 하반"}}, "key": "25E1"}, {"mappings": {"default": {"default": "검은색 오른아래 삼각형"}}, "key": "25E2"}, {"mappings": {"default": {"default": "검은색 왼아래 삼각형"}}, "key": "25E3"}, {"mappings": {"default": {"default": "검은색 왼위 삼각형"}}, "key": "25E4"}, {"mappings": {"default": {"default": "검은색 오른위 삼각형"}}, "key": "25E5"}, {"mappings": {"default": {"default": "흰색 총알"}}, "key": "25E6"}, {"mappings": {"default": {"default": "왼쪽 절반이 검은 사각형"}}, "key": "25E7"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 사각형"}}, "key": "25E8"}, {"mappings": {"default": {"default": "대각선 왼위 절반이 검은 사각형"}}, "key": "25E9"}, {"mappings": {"default": {"default": "대각선 오른아래 절반이 검은 사각형"}}, "key": "25EA"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 사각형"}}, "key": "25EB"}, {"mappings": {"default": {"default": "점을 포함한 흰색 상향 삼각형"}}, "key": "25EC"}, {"mappings": {"default": {"default": "왼쪽이 검은 상향 삼각형"}}, "key": "25ED"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 상향 삼각형"}}, "key": "25EE"}, {"mappings": {"default": {"default": "큰 원"}}, "key": "25EF"}, {"mappings": {"default": {"default": "왼위 사분면이 있는 흰색 사각형"}}, "key": "25F0"}, {"mappings": {"default": {"default": "왼아래 사분면이 있는 흰색 사각형"}}, "key": "25F1"}, {"mappings": {"default": {"default": "오른아래 사분면이 있는 흰색 사각형"}}, "key": "25F2"}, {"mappings": {"default": {"default": "오른위 사분면이 있는 흰색 사각형"}}, "key": "25F3"}, {"mappings": {"default": {"default": "왼위 사분면이 있는 흰색 원"}}, "key": "25F4"}, {"mappings": {"default": {"default": "왼아래 사분면이 있는 흰색 원"}}, "key": "25F5"}, {"mappings": {"default": {"default": "오른아래 사분면이 있는 흰색 원"}}, "key": "25F6"}, {"mappings": {"default": {"default": "오른위 사분면이 있는 흰색 원"}}, "key": "25F7"}, {"mappings": {"default": {"default": "왼위 삼각형"}}, "key": "25F8"}, {"mappings": {"default": {"default": "오른위 삼각형"}}, "key": "25F9"}, {"mappings": {"default": {"default": "왼아래 삼각형"}}, "key": "25FA"}, {"mappings": {"default": {"default": "흰색 중간 정사각형"}}, "key": "25FB"}, {"mappings": {"default": {"default": "검은색 중간 정사각형"}}, "key": "25FC"}, {"mappings": {"default": {"default": "흰색 중형 작은 사각형"}}, "key": "25FD"}, {"mappings": {"default": {"default": "검은색 중형 작은 사각형"}}, "key": "25FE"}, {"mappings": {"default": {"default": "오른아래 삼각형"}}, "key": "25FF"}, {"mappings": {"default": {"default": "상반이 검은 정사각형"}}, "key": "2B12"}, {"mappings": {"default": {"default": "하반이 검은 정사각형"}}, "key": "2B13"}, {"mappings": {"default": {"default": "대각선 오른위가 검은 정사각형"}}, "key": "2B14"}, {"mappings": {"default": {"default": "대각선 왼아래가 검은 정사각형"}}, "key": "2B15"}, {"mappings": {"default": {"default": "왼쪽 절반이 검은 다이아몬드"}}, "key": "2B16"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 다이아몬드"}}, "key": "2B17"}, {"mappings": {"default": {"default": "위쪽 절반이 검은 다이아몬드"}}, "key": "2B18"}, {"mappings": {"default": {"default": "아래쪽 절반이 검은 다이아몬드"}}, "key": "2B19"}, {"mappings": {"default": {"default": "점선으로된 정사각형"}}, "key": "2B1A"}, {"mappings": {"default": {"default": "검은색 큰 정사각형"}}, "key": "2B1B"}, {"mappings": {"default": {"default": "흰색 큰 정사각형"}}, "key": "2B1C"}, {"mappings": {"default": {"default": "아주 작은 검정색 정사각형"}}, "key": "2B1D"}, {"mappings": {"default": {"default": "아주 작은 흰색 정사각형"}}, "key": "2B1E"}, {"mappings": {"default": {"default": "검은색 오각형"}}, "key": "2B1F"}, {"mappings": {"default": {"default": "흰색 오각형"}}, "key": "2B20"}, {"mappings": {"default": {"default": "흰색 육각형"}}, "key": "2B21"}, {"mappings": {"default": {"default": "검은색 육각형"}}, "key": "2B22"}, {"mappings": {"default": {"default": "검은색 수평 육각형"}}, "key": "2B23"}, {"mappings": {"default": {"default": "검은색 큰 원"}}, "key": "2B24"}, {"mappings": {"default": {"default": "검은 중형 다이아몬드"}}, "key": "2B25"}, {"mappings": {"default": {"default": "흰 중형 다이아몬드"}}, "key": "2B26"}, {"mappings": {"default": {"default": "검은 중형 로젠지"}}, "key": "2B27"}, {"mappings": {"default": {"default": "흰 중형 로젠지"}}, "key": "2B28"}, {"mappings": {"default": {"default": "검은 작은 다이아몬드"}}, "key": "2B29"}, {"mappings": {"default": {"default": "검은 작은 로젠지"}}, "key": "2B2A"}, {"mappings": {"default": {"default": "흰 작은 로젠지"}}, "key": "2B2B"}, {"mappings": {"default": {"default": "검은 수평 타원"}}, "key": "2B2C"}, {"mappings": {"default": {"default": "흰 수평 타원"}}, "key": "2B2D"}, {"mappings": {"default": {"default": "검은 수직 타원"}}, "key": "2B2E"}, {"mappings": {"default": {"default": "흰 수직 타원"}}, "key": "2B2F"}, {"mappings": {"default": {"default": "흰 중형 별"}}, "key": "2B50"}, {"mappings": {"default": {"default": "검은 작은 별"}}, "key": "2B51"}, {"mappings": {"default": {"default": "흰 작은 별"}}, "key": "2B52"}, {"mappings": {"default": {"default": "검은 우향 오각형"}}, "key": "2B53"}, {"mappings": {"default": {"default": "흰 우향 오각형"}}, "key": "2B54"}, {"mappings": {"default": {"default": "큰 진한 원"}}, "key": "2B55"}, {"mappings": {"default": {"default": "타원을 포함하는 진한 타원"}}, "key": "2B56"}, {"mappings": {"default": {"default": "원을 포함하는 진한 원"}}, "key": "2B57"}, {"mappings": {"default": {"default": "진한 원"}}, "key": "2B58"}, {"mappings": {"default": {"default": "saltire 있는 진한 원"}}, "key": "2B59"}], "ko/symbols/math_harpoons.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "바브가 위쪽인 왼쪽 작살"}}, "key": "21BC"}, {"mappings": {"default": {"default": "바브가 아래쪽인 왼쪽 작살"}}, "key": "21BD"}, {"mappings": {"default": {"default": "바브가 오른쪽인 위쪽 작살"}}, "key": "21BE"}, {"mappings": {"default": {"default": "바브가 왼쪽인 위쪽 작살"}}, "key": "21BF"}, {"mappings": {"default": {"default": "바브 위쪽인 오른쪽 작살"}}, "key": "21C0"}, {"mappings": {"default": {"default": "바브 아래쪽인 오른쪽 작살"}}, "key": "21C1"}, {"mappings": {"default": {"default": "바브가 오른쪽인 아래쪽 작살"}}, "key": "21C2"}, {"mappings": {"default": {"default": "바브가 왼쪽인 아래쪽 작살"}}, "key": "21C3"}, {"mappings": {"default": {"default": "오른쪽 작살 위 왼쪽 작살"}}, "key": "21CB"}, {"mappings": {"default": {"default": "왼쪽 작살 위 오른쪽 작살"}}, "key": "21CC"}, {"mappings": {"default": {"default": "왼쪽 바브는 위쪽, 오른쪽 바브는 아래쪽인 작살"}}, "key": "294A"}, {"mappings": {"default": {"default": "왼쪽 바브는 아래쪽, 오른쪽 바브는 위쪽인 작살"}}, "key": "294B"}, {"mappings": {"default": {"default": "위쪽 바브는 오른쪽, 아래쪽 바브는 왼쪽인 작살"}}, "key": "294C"}, {"mappings": {"default": {"default": "위쪽 바브는 왼쪽, 아래쪽 바브는 오른쪽인 작살"}}, "key": "294D"}, {"mappings": {"default": {"default": "왼쪽 바브는 위쪽, 오른쪽 바브도 위쪽인 하푼"}}, "key": "294E"}, {"mappings": {"default": {"default": "위쪽 바브는 오른쪽, 아래쪽 바브도 오른쪽인 작살"}}, "key": "294F"}, {"mappings": {"default": {"default": "왼쪽 바브는 아래쪽, 오른쪽 바브도 아래쪽인 작살"}}, "key": "2950"}, {"mappings": {"default": {"default": "위쪽 바브는 왼쪽, 아래쪽 바브도 왼쪽인 작살"}}, "key": "2951"}, {"mappings": {"default": {"default": "바로 향하는 바브가 위쪽인 왼쪽 작살"}}, "key": "2952"}, {"mappings": {"default": {"default": "바로 향하는 바브가 위쪽인 오른쪽 작살"}}, "key": "2953"}, {"mappings": {"default": {"default": "바로 향하는 바브가 오른쪽인 위쪽 작살"}}, "key": "2954"}, {"mappings": {"default": {"default": "바로 향하는 바브가 오른쪽인 아래쪽 작살"}}, "key": "2955"}, {"mappings": {"default": {"default": "바로 향하는 바브가 아래쪽인 왼쪽 작살"}}, "key": "2956"}, {"mappings": {"default": {"default": "바로 향하는 바브가 아래쪽인 오른쪽 작살"}}, "key": "2957"}, {"mappings": {"default": {"default": "바로 향하는 바브가 왼쪽인 위쪽 작살"}}, "key": "2958"}, {"mappings": {"default": {"default": "바로 향하는 바브가 왼쪽인 아래쪽 작살"}}, "key": "2959"}, {"mappings": {"default": {"default": "바로 향하는 바브가 위쪽인 왼쪽 작살"}}, "key": "295A"}, {"mappings": {"default": {"default": "바로 향하는 바브가 위쪽인 오른쪽 작살"}}, "key": "295B"}, {"mappings": {"default": {"default": "바로 향하는 바브가 오른쪽인 위쪽 작살"}}, "key": "295C"}, {"mappings": {"default": {"default": "바로 향하는 바브가 오른쪽인 아래쪽 작살"}}, "key": "295D"}, {"mappings": {"default": {"default": "바로 향하는 바브가 아래쪽인 왼쪽 작살"}}, "key": "295E"}, {"mappings": {"default": {"default": "바로 향하는 바브가 아래쪽인 오른쪽 작살"}}, "key": "295F"}, {"mappings": {"default": {"default": "바로 향하는 바브가 왼쪽인 위쪽 작살"}}, "key": "2960"}, {"mappings": {"default": {"default": "바로 향하는 바브가 왼쪽인 아래쪽 작살"}}, "key": "2961"}, {"mappings": {"default": {"default": "바브가 아래쪽인 왼쪽 작살 위에 바브가 위쪽인 왼쪽 작살"}}, "key": "2962"}, {"mappings": {"default": {"default": "바브가 왼쪽인 위쪽 작살 옆에 바브가 오른쪽인 위쪽 작살"}}, "key": "2963"}, {"mappings": {"default": {"default": "바브가 아래쪽인 오른쪽 작살 위에 바브가 위쪽인 오른쪽 작살"}}, "key": "2964"}, {"mappings": {"default": {"default": "바브가 왼쪽인 아래쪽 작살 옆에 바브가 오른쪽인 아래쪽 작살"}}, "key": "2965"}, {"mappings": {"default": {"default": "바브가 위쪽인 오른쪽 작살 위에 바브가 위쪽인 왼쪽 작살"}}, "key": "2966"}, {"mappings": {"default": {"default": "바브가 아래쪽인 오른쪽 작살 위에 바브가 아래쪽인 왼쪽 작살"}}, "key": "2967"}, {"mappings": {"default": {"default": "바브가 위쪽인 왼쪽 작살 위에 바브가 위쪽인 오른쪽 작살"}}, "key": "2968"}, {"mappings": {"default": {"default": "바브가 아래쪽인 왼쪽 작살 위에 바브가 아래쪽인 오른쪽 작살"}}, "key": "2969"}, {"mappings": {"default": {"default": "긴 직선 위에 바브가 위쪽인 왼쪽 작살"}}, "key": "296A"}, {"mappings": {"default": {"default": "긴 직선 위에 바브가 아래쪽인 왼쪽 작살"}}, "key": "296B"}, {"mappings": {"default": {"default": "긴 직선 위에 바브가 위쪽인 오른쪽 작살"}}, "key": "296C"}, {"mappings": {"default": {"default": "긴 직선 위에 바브가 아래쪽인 오른쪽 작살"}}, "key": "296D"}, {"mappings": {"default": {"default": "바브가 왼쪽인 위쪽 작살 옆에 바브가 오른쪽인 아래쪽 작살"}}, "key": "296E"}, {"mappings": {"default": {"default": "바브가 왼쪽인 아래쪽 작살 옆에 바브가 오른쪽인 위쪽 작살"}}, "key": "296F"}, {"mappings": {"default": {"default": "왼쪽 물고기 꼬리"}}, "key": "297C"}, {"mappings": {"default": {"default": "오른쪽 물고기 꼬리"}}, "key": "297D"}, {"mappings": {"default": {"default": "위쪽 물고기 꼬리"}}, "key": "297E"}, {"mappings": {"default": {"default": "아래쪽 물고기 꼬리"}}, "key": "297F"}], "ko/symbols/math_non_characters.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "2 파이 분의 기울인 h", "physics": "디랙 상수"}}, "key": "210F"}, {"mappings": {"default": {"default": "L B 막대기 기호"}}, "key": "2114"}, {"mappings": {"default": {"default": "서수 기호"}}, "key": "2116"}, {"mappings": {"default": {"default": "소리 녹음 저작권"}}, "key": "2117"}, {"mappings": {"default": {"default": "처방전"}}, "key": "211E"}, {"mappings": {"default": {"default": "응답"}}, "key": "211F"}, {"mappings": {"default": {"default": "서비스 마크"}}, "key": "2120"}, {"mappings": {"default": {"default": "전화 사인", "alternative": "T E L 기호"}}, "key": "2121"}, {"mappings": {"default": {"default": "상표 표시"}}, "key": "2122"}, {"mappings": {"default": {"default": "버시클"}}, "key": "2123"}, {"mappings": {"default": {"default": "온스"}}, "key": "2125"}, {"mappings": {"default": {"default": "옴"}}, "key": "2126"}, {"mappings": {"default": {"default": "반전된 옴 기호"}}, "key": "2127"}, {"mappings": {"default": {"default": "켈빈"}}, "key": "212A"}, {"mappings": {"default": {"default": "옹 스트롬"}}, "key": "212B"}, {"mappings": {"default": {"default": "추정 가호"}}, "key": "212E"}, {"mappings": {"default": {"default": "반전된 대문자 F"}, "mathspeak": {"default": "반전된 대문자 F"}}, "key": "2132"}, {"mappings": {"default": {"default": "정보 출처"}}, "key": "2139"}, {"mappings": {"default": {"default": "회전된 대문자 Q"}, "mathspeak": {"default": "회전된 대문자 Q"}}, "key": "213A"}, {"mappings": {"default": {"default": "팩시밀리 사인"}}, "key": "213B"}, {"mappings": {"default": {"default": "반전된 산세리프 대문자 G"}, "mathspeak": {"default": "반전된 산세리프 대문자 G"}}, "key": "2141"}, {"mappings": {"default": {"default": "반전된 산세리프 대문자 L"}, "mathspeak": {"default": "반전된 산세리프 대문자 L"}}, "key": "2142"}, {"mappings": {"default": {"default": "반전된 산세리프 대문자 L"}, "mathspeak": {"default": "반전된 산세리프 대문자 L"}}, "key": "2143"}, {"mappings": {"default": {"default": "반전된 산세리프 대문자 Y"}, "mathspeak": {"default": "반전된 산세리프 대문자 Y"}}, "key": "2144"}], "ko/symbols/math_symbols.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "느낌표"}}, "key": "0021"}, {"mappings": {"default": {"default": "큰따옴표"}}, "key": "0022"}, {"mappings": {"default": {"default": "번호 기호", "alternative": "해시"}, "mathspeak": {"brief": "숫자 기호", "sbrief": "숫자 기호"}}, "key": "0023"}, {"mappings": {"default": {"default": "달러"}}, "key": "0024"}, {"mappings": {"default": {"default": "퍼센트"}}, "key": "0025"}, {"mappings": {"default": {"default": "앰퍼샌드"}}, "key": "0026"}, {"mappings": {"default": {"default": "프라임"}}, "key": "0027"}, {"mappings": {"default": {"default": "별표"}}, "key": "002A"}, {"mappings": {"default": {"default": "더하기"}}, "key": "002B"}, {"mappings": {"default": {"default": "콤마"}}, "key": "002C"}, {"mappings": {"default": {"default": "빼기"}, "mathspeak": {"default": "하이픈"}}, "key": "002D"}, {"mappings": {"default": {"default": "마침표"}}, "key": "002E"}, {"mappings": {"default": {"default": "슬래시", "alternative": "사선"}, "emacspeak": {"default": "슬래시"}}, "key": "002F"}, {"mappings": {"default": {"default": "콜론"}}, "key": "003A"}, {"mappings": {"default": {"default": "세미콜론"}}, "key": "003B"}, {"mappings": {"default": {"default": "작다"}, "clearspeak": {"default": "작다"}}, "key": "003C"}, {"mappings": {"default": {"default": "같다"}}, "key": "003D"}, {"mappings": {"default": {"default": "크다"}, "clearspeak": {"default": "크다"}}, "key": "003E"}, {"mappings": {"default": {"default": "물음표"}}, "key": "003F"}, {"mappings": {"default": {"default": "골뱅이표"}}, "key": "0040"}, {"mappings": {"default": {"default": "백슬래시"}}, "key": "005C"}, {"mappings": {"default": {"default": "곡절 악센트"}, "mathspeak": {"default": "삽입기호"}}, "key": "005E"}, {"mappings": {"default": {"default": "바", "alternative": "밑줄"}}, "key": "005F"}, {"mappings": {"default": {"default": "억음 악센트"}, "mathspeak": {"default": "억음 악센트"}}, "key": "0060"}, {"mappings": {"default": {"default": "세로선"}}, "key": "007C"}, {"mappings": {"default": {"default": "물결표"}}, "key": "007E"}, {"mappings": {"default": {"default": "역 느낌표"}}, "key": "00A1"}, {"mappings": {"default": {"default": "센트"}}, "key": "00A2"}, {"mappings": {"default": {"default": "파운드"}}, "key": "00A3"}, {"mappings": {"default": {"default": "통화 기호"}}, "key": "00A4"}, {"mappings": {"default": {"default": "엔"}}, "key": "00A5"}, {"mappings": {"default": {"default": "끊어진 세로선"}}, "key": "00A6"}, {"mappings": {"default": {"default": "섹션 기호"}}, "key": "00A7"}, {"mappings": {"default": {"default": "분음부호표"}}, "key": "00A8"}, {"mappings": {"default": {"default": "저작권 기호"}}, "key": "00A9"}, {"mappings": {"default": {"default": "여성형 서수 지시자"}}, "key": "00AA"}, {"mappings": {"default": {"default": "겹화살괄호 열고"}}, "key": "00AB"}, {"mappings": {"default": {"default": "부정 기호"}}, "key": "00AC"}, {"mappings": {"default": {"default": "등록 상표 기호"}, "mathspeak": {"default": "등록 상표 기호"}, "clearspeak": {"default": "트레이드 마크"}}, "key": "00AE"}, {"mappings": {"default": {"default": "바"}}, "key": "00AF"}, {"mappings": {"default": {"default": "도"}, "clearspeak": {"default": "도"}}, "key": "00B0"}, {"mappings": {"default": {"default": "플러스마이너스"}}, "key": "00B1"}, {"mappings": {"default": {"default": "양음 악센트"}}, "key": "00B4"}, {"mappings": {"default": {"default": "마이크로"}}, "key": "00B5"}, {"mappings": {"default": {"default": "단락 기호"}}, "key": "00B6"}, {"mappings": {"default": {"default": "닷"}, "clearspeak": {"default": "곱하기"}}, "key": "00B7"}, {"mappings": {"default": {"default": "세딜라"}}, "key": "00B8"}, {"mappings": {"default": {"default": "남성형 서수 지시자"}}, "key": "00BA"}, {"mappings": {"default": {"default": "겹화살괄호 닫고"}}, "key": "00BB"}, {"mappings": {"default": {"default": "역 물음표"}}, "key": "00BF"}, {"mappings": {"default": {"default": "곱하기"}, "clearspeak": {"MultsymbolX_By": "곱하기", "MultsymbolX_Cross": "외적"}}, "key": "00D7"}, {"mappings": {"default": {"default": "나누기"}}, "key": "00F7"}, {"mappings": {"default": {"default": "단음표"}}, "key": "02D8"}, {"key": "02B9", "mappings": {"default": {"default": "프라임"}}}, {"key": "02BA", "mappings": {"default": {"default": "더블 프라임"}}}, {"mappings": {"default": {"default": "위의 점"}, "mathspeak": {"default": "점"}}, "key": "02D9"}, {"mappings": {"default": {"default": "위의 고리"}}, "key": "02DA"}, {"mappings": {"default": {"default": "아가뇩"}}, "key": "02DB"}, {"mappings": {"default": {"default": "작은 물결표"}}, "key": "02DC"}, {"mappings": {"default": {"default": "이중 양음 악센트"}}, "key": "02DD"}, {"mappings": {"default": {"default": "하이픈"}}, "key": "2010"}, {"mappings": {"default": {"default": "하이픈"}}, "key": "2011"}, {"mappings": {"default": {"default": "대시"}}, "key": "2012"}, {"mappings": {"default": {"default": "붙임표"}}, "key": "2013"}, {"mappings": {"default": {"default": "긴줄표"}}, "key": "2014"}, {"mappings": {"default": {"default": "가로선"}, "mathspeak": {"default": "인용 대시"}}, "key": "2015"}, {"mappings": {"default": {"default": "이중 세로선"}}, "key": "2016"}, {"mappings": {"default": {"default": "겹밑줄"}}, "key": "2017"}, {"mappings": {"default": {"default": "왼쪽 작은따옴표"}}, "key": "2018"}, {"mappings": {"default": {"default": "오른쪽 작은따옴표"}}, "key": "2019"}, {"mappings": {"default": {"default": "왼쪽 낮은 따옴표"}}, "key": "201A"}, {"mappings": {"default": {"default": "왼쪽 역 따옴표"}}, "key": "201B"}, {"mappings": {"default": {"default": "왼쪽 큰따옴표"}}, "key": "201C"}, {"mappings": {"default": {"default": "오른쪽 큰따옴표"}}, "key": "201D"}, {"mappings": {"default": {"default": "오른쪽 낮은 따옴표"}}, "key": "201E"}, {"mappings": {"default": {"default": "왼쪽 역 큰따옴표"}}, "key": "201F"}, {"mappings": {"default": {"default": "칼표"}}, "key": "2020"}, {"mappings": {"default": {"default": "이중 칼표"}}, "key": "2021"}, {"mappings": {"default": {"default": "글머리표"}}, "key": "2022"}, {"mappings": {"default": {"default": "삼각형 글머리표"}}, "key": "2023"}, {"mappings": {"default": {"default": "점"}}, "key": "2024"}, {"mappings": {"default": {"default": "쌍점"}}, "key": "2025"}, {"mappings": {"default": {"default": "줄임표"}, "clearspeak": {"default": "점 점 점"}}, "key": "2026"}, {"mappings": {"default": {"default": "가운뎃점"}}, "key": "2027"}, {"mappings": {"default": {"default": "퍼밀"}}, "key": "2030"}, {"mappings": {"default": {"default": "만분율"}}, "key": "2031"}, {"mappings": {"default": {"default": "프라임"}}, "key": "2032"}, {"mappings": {"default": {"default": "더블 프라임"}}, "key": "2033"}, {"mappings": {"default": {"default": "트리플 프라임"}}, "key": "2034"}, {"mappings": {"default": {"default": "역 프라임"}}, "key": "2035"}, {"mappings": {"default": {"default": "역 더블 프라임"}}, "key": "2036"}, {"mappings": {"default": {"default": "역 트리플 프라임"}}, "key": "2037"}, {"mappings": {"default": {"default": "카렛"}}, "key": "2038"}, {"mappings": {"default": {"default": "홑화살괄호 열고"}}, "key": "2039"}, {"mappings": {"default": {"default": "홑화살괄호 닫고"}}, "key": "203A"}, {"mappings": {"default": {"default": "참조표"}}, "key": "203B"}, {"mappings": {"default": {"default": "이중 느낌표"}}, "key": "203C"}, {"mappings": {"default": {"default": "물음느낌표"}}, "key": "203D"}, {"mappings": {"default": {"default": "윗줄"}, "mathspeak": {"default": "바"}}, "key": "203E"}, {"mappings": {"default": {"default": "아랫 붙임표"}}, "key": "203F"}, {"mappings": {"default": {"default": "윗 붙임표"}}, "key": "2040"}, {"mappings": {"default": {"default": "부호 삽입점"}}, "key": "2041"}, {"mappings": {"default": {"default": "세 별표"}}, "key": "2042"}, {"mappings": {"default": {"default": "하이픈 글머리표"}}, "key": "2043"}, {"mappings": {"default": {"default": "분수 슬래시"}}, "key": "2044"}, {"mappings": {"default": {"default": "이중 물음표"}}, "key": "2047"}, {"mappings": {"default": {"default": "물음표 느낌표"}}, "key": "2048"}, {"mappings": {"default": {"default": "느낌표 물음표"}}, "key": "2049"}, {"mappings": {"default": {"default": "역 단락 기호"}}, "key": "204B"}, {"mappings": {"default": {"default": "검은 왼쪽 글머리표"}}, "key": "204C"}, {"mappings": {"default": {"default": "검은 오른쪽 글머리표"}}, "key": "204D"}, {"mappings": {"default": {"default": "낮은 별표"}}, "key": "204E"}, {"mappings": {"default": {"default": "역 세미콜론"}}, "key": "204F"}, {"mappings": {"default": {"default": "삭제 기호"}}, "key": "2050"}, {"mappings": {"default": {"default": "세로 이중 별표"}}, "key": "2051"}, {"mappings": {"default": {"default": "상업 마이너스 부호"}}, "key": "2052"}, {"mappings": {"default": {"default": "물결표"}}, "key": "2053"}, {"mappings": {"default": {"default": "역 아랫묶음"}}, "key": "2054"}, {"mappings": {"default": {"default": "꽃"}}, "key": "2055"}, {"mappings": {"default": {"default": "삼점"}}, "key": "2056"}, {"mappings": {"default": {"default": "쿼드 프라임"}}, "key": "2057"}, {"mappings": {"default": {"default": "사점"}}, "key": "2058"}, {"mappings": {"default": {"default": "오점"}}, "key": "2059"}, {"mappings": {"default": {"default": "이점"}}, "key": "205A"}, {"mappings": {"default": {"default": "사점 기호"}}, "key": "205B"}, {"mappings": {"default": {"default": "사점 십자가"}}, "key": "205C"}, {"mappings": {"default": {"default": "세로 줄임표"}}, "key": "205D"}, {"mappings": {"default": {"default": "세로 사점"}}, "key": "205E"}, {"mappings": {"default": {"default": "위첨자 플러스"}}, "key": "207A"}, {"mappings": {"default": {"default": "위첨자 마이너스"}}, "key": "207B"}, {"mappings": {"default": {"default": "위첨자 등호"}}, "key": "207C"}, {"mappings": {"default": {"default": "위첨자 괄호 열고"}}, "key": "207D"}, {"mappings": {"default": {"default": "위첨자 괄호 닫고"}}, "key": "207E"}, {"mappings": {"default": {"default": "아래첨자 플러스"}}, "key": "208A"}, {"mappings": {"default": {"default": "아래첨자 마이너스"}}, "key": "208B"}, {"mappings": {"default": {"default": "아래첨자 등호"}}, "key": "208C"}, {"mappings": {"default": {"default": "아래첨자 괄호 열고"}}, "key": "208D"}, {"mappings": {"default": {"default": "아래첨자 괄호 닫고"}}, "key": "208E"}, {"mappings": {"default": {"default": "특성 줄"}}, "key": "214A"}, {"mappings": {"default": {"default": "역 앰퍼샌드"}}, "key": "214B"}, {"mappings": {"default": {"default": "퍼"}}, "key": "214C"}, {"mappings": {"default": {"default": "에이 에스 기호"}}, "key": "214D"}, {"mappings": {"default": {"default": "작은 역 에프"}}, "key": "214E"}, {"mappings": {"default": {"default": "모든"}}, "key": "2200"}, {"mappings": {"default": {"default": "보어"}}, "key": "2201"}, {"mappings": {"default": {"default": "존재한다"}}, "key": "2203"}, {"mappings": {"default": {"default": "존재하지 않는다"}}, "key": "2204"}, {"mappings": {"default": {"default": "공집합"}}, "key": "2205"}, {"mappings": {"default": {"default": "델타"}}, "key": "2206"}, {"mappings": {"default": {"default": "다음의 원소이다"}, "clearspeak": {"default": "다음의 원소이다"}}, "key": "2208"}, {"mappings": {"default": {"default": "다음의 원소가 아니다"}, "clearspeak": {"default": "다음의 원소가 아니다"}}, "key": "2209"}, {"mappings": {"default": {"default": "다음의 원소이다"}, "clearspeak": {"default": "다음의 원소이다."}}, "key": "220A"}, {"mappings": {"default": {"default": "다음을 포함한다"}, "clearspeak": {"default": "포함한다"}}, "key": "220B"}, {"mappings": {"default": {"default": "다음을 포함하지 않는다"}, "clearspeak": {"default": "포함하지 않는다"}}, "key": "220C"}, {"mappings": {"default": {"default": "다음을 포함한다"}, "clearspeak": {"default": "포함한다"}}, "key": "220D"}, {"mappings": {"default": {"default": "증명끝"}}, "key": "220E"}, {"mappings": {"default": {"default": "파이"}}, "key": "220F"}, {"mappings": {"default": {"default": "쌍대곱"}}, "key": "2210"}, {"mappings": {"default": {"default": "시그마"}, "mathspeak": {"default": "시그마 합"}}, "key": "2211"}, {"mappings": {"default": {"default": "빼기"}}, "key": "2212"}, {"mappings": {"default": {"default": "마이너스플러스"}}, "key": "2213"}, {"mappings": {"default": {"default": "닷 플러스"}}, "key": "2214"}, {"mappings": {"default": {"default": "나누기 슬래시"}}, "key": "2215"}, {"mappings": {"default": {"default": "마이너스"}}, "key": "2216"}, {"mappings": {"default": {"default": "별표 연산자"}}, "key": "2217"}, {"mappings": {"default": {"default": "고리"}, "clearspeak": {"default": "합성 함수"}}, "key": "2218"}, {"mappings": {"default": {"default": "닷"}}, "key": "2219"}, {"mappings": {"default": {"default": "제곱근"}}, "key": "221A"}, {"mappings": {"default": {"default": "세제곱근"}}, "key": "221B"}, {"mappings": {"default": {"default": "네제곱근"}}, "key": "221C"}, {"mappings": {"default": {"default": "비례"}}, "key": "221D"}, {"mappings": {"default": {"default": "무한대"}}, "key": "221E"}, {"mappings": {"default": {"default": "직각"}}, "key": "221F"}, {"mappings": {"default": {"default": "각"}}, "key": "2220"}, {"mappings": {"default": {"default": "각의 크기"}}, "key": "2221"}, {"mappings": {"default": {"default": "구면각"}}, "key": "2222"}, {"mappings": {"default": {"default": "세로선", "alternative": "다음의 약수이다"}}, "key": "2223"}, {"mappings": {"default": {"default": "다음의 약수가 아니다"}}, "key": "2224"}, {"mappings": {"default": {"default": "평행하다"}}, "key": "2225"}, {"mappings": {"default": {"default": "평행하지 않다"}}, "key": "2226"}, {"mappings": {"default": {"default": "논리곱"}}, "key": "2227"}, {"mappings": {"default": {"default": "논리합"}}, "key": "2228"}, {"mappings": {"default": {"default": "교집합"}}, "key": "2229"}, {"mappings": {"default": {"default": "합집합"}}, "key": "222A"}, {"mappings": {"default": {"default": "인테그럴"}}, "key": "222B"}, {"mappings": {"default": {"default": "이중 인테그럴"}}, "key": "222C"}, {"mappings": {"default": {"default": "삼중 인테그럴"}}, "key": "222D"}, {"mappings": {"default": {"default": "경로적분"}}, "key": "222E"}, {"mappings": {"default": {"default": "면적분"}}, "key": "222F"}, {"mappings": {"default": {"default": "부피적분"}}, "key": "2230"}, {"mappings": {"default": {"default": "시계방향 적분"}}, "key": "2231"}, {"mappings": {"default": {"default": "시계방향 선적분"}}, "key": "2232"}, {"mappings": {"default": {"default": "반시계방향 선적분"}}, "key": "2233"}, {"mappings": {"default": {"default": "따라서"}}, "key": "2234"}, {"mappings": {"default": {"default": "왜냐하면"}}, "key": "2235"}, {"mappings": {"default": {"default": "대"}}, "key": "2236"}, {"mappings": {"default": {"default": "대"}}, "key": "2237"}, {"mappings": {"default": {"default": "닷 마이너스"}}, "key": "2238"}, {"mappings": {"default": {"default": "초과"}}, "key": "2239"}, {"mappings": {"default": {"default": "기하학적 비율"}}, "key": "223A"}, {"mappings": {"default": {"default": "동조"}}, "key": "223B"}, {"mappings": {"default": {"default": "물결표"}}, "key": "223C"}, {"mappings": {"default": {"default": "역 물결표"}}, "key": "223D"}, {"mappings": {"default": {"default": "누운 S"}}, "key": "223E"}, {"mappings": {"default": {"default": "싸인파"}}, "key": "223F"}, {"mappings": {"default": {"default": "화환곱"}}, "key": "2240"}, {"mappings": {"default": {"default": "부정 물결표"}}, "key": "2241"}, {"mappings": {"default": {"default": "마이너스 물결표"}}, "key": "2242"}, {"mappings": {"default": {"default": "점근적으로 같다"}}, "key": "2243"}, {"mappings": {"default": {"default": "점근적으로 같지 않다"}}, "key": "2244"}, {"mappings": {"default": {"default": "거의 같다"}}, "key": "2245"}, {"mappings": {"default": {"default": "거의 같다"}}, "key": "2246"}, {"mappings": {"default": {"default": "근사하지도 같지도 않다"}}, "key": "2247"}, {"mappings": {"default": {"default": "거의 같다"}}, "key": "2248"}, {"mappings": {"default": {"default": "거의 같지 않다"}}, "key": "2249"}, {"mappings": {"default": {"default": "근사하거나 같다"}}, "key": "224A"}, {"mappings": {"default": {"default": "삼중 물결표"}}, "key": "224B"}, {"mappings": {"default": {"default": "모두 같다"}}, "key": "224C"}, {"mappings": {"default": {"default": "동등하다"}}, "key": "224D"}, {"mappings": {"default": {"default": "기하학적으로 동등하다"}}, "key": "224E"}, {"mappings": {"default": {"default": "차"}}, "key": "224F"}, {"mappings": {"default": {"default": "한계에 접근하다"}}, "key": "2250"}, {"mappings": {"default": {"default": "기하학적으로 같다"}}, "key": "2251"}, {"mappings": {"default": {"default": "거의 같다"}}, "key": "2252"}, {"mappings": {"default": {"default": "거의 같다"}}, "key": "2253"}, {"mappings": {"default": {"default": "콜론 등호"}}, "key": "2254"}, {"mappings": {"default": {"default": "등호 콜론"}}, "key": "2255"}, {"mappings": {"default": {"default": "고리점이 들어간 등호"}}, "key": "2256"}, {"mappings": {"default": {"default": "고리점이 붙은 등호"}}, "key": "2257"}, {"mappings": {"default": {"default": "일치하다"}, "clearspeak": {"default": "일치하다"}}, "key": "2258"}, {"mappings": {"default": {"default": "추정하다"}}, "key": "2259"}, {"mappings": {"default": {"default": "등각"}, "clearspeak": {"default": "등각이다"}}, "key": "225A"}, {"mappings": {"default": {"default": "별표 등호"}}, "key": "225B"}, {"mappings": {"default": {"default": "델타 등호"}}, "key": "225C"}, {"mappings": {"default": {"default": "정의상 같다"}, "clearspeak": {"default": "정의된다"}}, "key": "225D"}, {"mappings": {"default": {"default": "측정된다"}, "clearspeak": {"default": "측정된다"}}, "key": "225E"}, {"mappings": {"default": {"default": "명제"}}, "key": "225F"}, {"mappings": {"default": {"default": "같지 않다"}, "clearspeak": {"default": "같지 않다"}}, "key": "2260"}, {"mappings": {"default": {"default": "합동이다"}, "clearspeak": {"default": "합동이다"}}, "key": "2261"}, {"mappings": {"default": {"default": "합동이 아니다"}, "clearspeak": {"default": "합동이 아니다"}}, "key": "2262"}, {"mappings": {"default": {"default": "매우 동등하다"}, "clearspeak": {"default": "매우 동등하다"}}, "key": "2263"}, {"mappings": {"default": {"default": "작거나 같다"}, "clearspeak": {"default": "작거나 같다"}}, "key": "2264"}, {"mappings": {"default": {"default": "크거나 같다"}, "clearspeak": {"default": "크거나 같다"}}, "key": "2265"}, {"mappings": {"default": {"default": "같거나 작다"}, "clearspeak": {"default": "같거나 작다"}}, "key": "2266"}, {"mappings": {"default": {"default": "크거나 같다"}, "clearspeak": {"default": "크거나 같다"}}, "key": "2267"}, {"mappings": {"default": {"default": "작지만 같지 않다"}, "clearspeak": {"default": "작지만 같지 않다"}}, "key": "2268"}, {"mappings": {"default": {"default": "크지만 같지 않다"}, "clearspeak": {"default": "크지만 같지 않다"}}, "key": "2269"}, {"mappings": {"default": {"default": "훨씬 작다"}, "clearspeak": {"default": "훨씬 작다"}}, "key": "226A"}, {"mappings": {"default": {"default": "훨씬 크다"}, "clearspeak": {"default": "훨씬 크다"}}, "key": "226B"}, {"mappings": {"default": {"default": "중에서"}}, "key": "226C"}, {"mappings": {"default": {"default": "동등하지 않다"}, "clearspeak": {"default": "동등하지 않다"}}, "key": "226D"}, {"mappings": {"default": {"default": "작지 않다"}, "clearspeak": {"default": "작지 않다"}}, "key": "226E"}, {"mappings": {"default": {"default": "크지 않다"}, "clearspeak": {"default": "크지 않다"}}, "key": "226F"}, {"mappings": {"default": {"default": "작지도 같지도 않다"}, "clearspeak": {"default": "작지도 같지도 않다"}}, "key": "2270"}, {"mappings": {"default": {"default": "크지도 같지도 않다"}, "clearspeak": {"default": "크지도 같지도 않다"}}, "key": "2271"}, {"mappings": {"default": {"default": "작거나 동등하다"}, "clearspeak": {"default": "작거나 동등하다"}}, "key": "2272"}, {"mappings": {"default": {"default": "크거나 동등하다"}, "clearspeak": {"default": "크거나 동등하다"}}, "key": "2273"}, {"mappings": {"default": {"default": "작지도 동등하지도 않다"}, "clearspeak": {"default": "작지도 동등하지도 않다"}}, "key": "2274"}, {"mappings": {"default": {"default": "크지도 동등하지도 않다"}, "clearspeak": {"default": "크지도 동등하지도 않다"}}, "key": "2275"}, {"mappings": {"default": {"default": "작거나 크다"}, "clearspeak": {"default": "작거나 크다"}}, "key": "2276"}, {"mappings": {"default": {"default": "크거나 작다"}, "clearspeak": {"default": "크거나 작다"}}, "key": "2277"}, {"mappings": {"default": {"default": "작지도 크지도 않다"}, "clearspeak": {"default": "작지도 크지도 않다"}}, "key": "2278"}, {"mappings": {"default": {"default": "크지도 작지도 않다"}, "clearspeak": {"default": "크지도 작지도 않다"}}, "key": "2279"}, {"mappings": {"default": {"default": "선행한다"}}, "key": "227A"}, {"mappings": {"default": {"default": "후행한다"}}, "key": "227B"}, {"mappings": {"default": {"default": "선행하거나 같다"}}, "key": "227C"}, {"mappings": {"default": {"default": "후행하거나 같다"}}, "key": "227D"}, {"mappings": {"default": {"default": "선행하거나 동등하다"}}, "key": "227E"}, {"mappings": {"default": {"default": "후행하거나 동등하다"}}, "key": "227F"}, {"mappings": {"default": {"default": "선행하지 않는다"}}, "key": "2280"}, {"mappings": {"default": {"default": "후행하지 않는다"}}, "key": "2281"}, {"mappings": {"default": {"default": "부분집합"}, "clearspaek": {"default": "부분집합"}}, "key": "2282"}, {"mappings": {"default": {"default": "상위집합"}, "clearspaek": {"default": "상위집합"}}, "key": "2283"}, {"mappings": {"default": {"default": "부분집합이 아니다"}, "clearspaek": {"default": "부분집합이 아니다"}}, "key": "2284"}, {"mappings": {"default": {"default": "상위집합이 아니다"}, "clearspaek": {"default": "상위집합이 아니다"}}, "key": "2285"}, {"mappings": {"default": {"default": "부분집합이거나 같다"}, "clearspeak": {"default": "부분집합이거나 같다"}}, "key": "2286"}, {"mappings": {"default": {"default": "상위집합이거나 같다"}, "clearspeak": {"default": "상위집합이거나 같다"}}, "key": "2287"}, {"mappings": {"default": {"default": "부분집합이지도 같지도 않다"}, "clearspeak": {"default": "부분집합이지도 같지도 않다"}}, "key": "2288"}, {"mappings": {"default": {"default": "상위집합이지도 같지도 않다"}, "clearspeak": {"default": "상위집합이지도 같지도 않다"}}, "key": "2289"}, {"mappings": {"default": {"default": "부분집합이지만 같지 않다"}, "clearspeak": {"default": "부분집합이지만 같지 않다"}}, "key": "228A"}, {"mappings": {"default": {"default": "상위집합이지만 같지 않다"}, "clearspeak": {"default": "상위집합이지만 같지 않다"}}, "key": "228B"}, {"mappings": {"default": {"default": "다중집합"}}, "key": "228C"}, {"mappings": {"default": {"default": "다중집합 곱셈"}}, "key": "228D"}, {"mappings": {"default": {"default": "다중집합 덧셈"}}, "key": "228E"}, {"mappings": {"default": {"default": "각진 부분집합"}}, "key": "228F"}, {"mappings": {"default": {"default": "각진 상위집합"}}, "key": "2290"}, {"mappings": {"default": {"default": "각진 부분집합이거나 같다"}}, "key": "2291"}, {"mappings": {"default": {"default": "각진 상위집합이거나 같다"}}, "key": "2292"}, {"mappings": {"default": {"default": "교집합"}}, "key": "2293"}, {"mappings": {"default": {"default": "합집합"}}, "key": "2294"}, {"mappings": {"default": {"default": "원문자 더하기"}}, "key": "2295"}, {"mappings": {"default": {"default": "원문자 빼기"}}, "key": "2296"}, {"mappings": {"default": {"default": "원문자 곱하기"}}, "key": "2297"}, {"mappings": {"default": {"default": "원문자 나누기"}}, "key": "2298"}, {"mappings": {"default": {"default": "원문자 닷"}}, "key": "2299"}, {"mappings": {"default": {"default": "원문자 고리"}}, "key": "229A"}, {"mappings": {"default": {"default": "원문자 별표"}}, "key": "229B"}, {"mappings": {"default": {"default": "원문자 등호"}}, "key": "229C"}, {"mappings": {"default": {"default": "원문자 대시"}}, "key": "229D"}, {"mappings": {"default": {"default": "사각문자 더하기"}}, "key": "229E"}, {"mappings": {"default": {"default": "사각문자 빼기"}}, "key": "229F"}, {"mappings": {"default": {"default": "사각문자 곱하기"}}, "key": "22A0"}, {"mappings": {"default": {"default": "사각문자 닷"}}, "key": "22A1"}, {"mappings": {"default": {"default": "오른 턴스틸"}}, "key": "22A2"}, {"mappings": {"default": {"default": "왼 턴스틸"}}, "key": "22A3"}, {"mappings": {"default": {"default": "참 기호"}}, "key": "22A4"}, {"mappings": {"default": {"default": "거짓 기호"}}, "key": "22A5"}, {"mappings": {"default": {"default": "역설"}}, "key": "22A6"}, {"mappings": {"default": {"default": "모델"}}, "key": "22A7"}, {"mappings": {"default": {"default": "참"}}, "key": "22A8"}, {"mappings": {"default": {"default": "강제"}}, "key": "22A9"}, {"mappings": {"default": {"default": "삼중 세로선 오른 턴스틸"}}, "key": "22AA"}, {"mappings": {"default": {"default": "이중 세로선 이중 오른 턴스틸"}}, "key": "22AB"}, {"mappings": {"default": {"default": "증명하지 않는다"}}, "key": "22AC"}, {"mappings": {"default": {"default": "참이 아니다"}}, "key": "22AD"}, {"mappings": {"default": {"default": "강제하지 않는다"}}, "key": "22AE"}, {"mappings": {"default": {"default": "부정 이중 세로선 이중 오른 턴스틸"}}, "key": "22AF"}, {"mappings": {"default": {"default": "관계상 선행한다"}}, "key": "22B0"}, {"mappings": {"default": {"default": "관계상 후행한다"}}, "key": "22B1"}, {"mappings": {"default": {"default": "다음의 일반 하위그룹이다"}}, "key": "22B2"}, {"mappings": {"default": {"default": "다음을 일반 하위그룹으로 갖는다"}}, "key": "22B3"}, {"mappings": {"default": {"default": "일반 하위그룹이거나 같다"}}, "key": "22B4"}, {"mappings": {"default": {"default": "일반 하위그룹으로 갖거나 같다"}}, "key": "22B5"}, {"mappings": {"default": {"default": "원본"}}, "key": "22B6"}, {"mappings": {"default": {"default": "이미지"}}, "key": "22B7"}, {"mappings": {"default": {"default": "멀티맵"}}, "key": "22B8"}, {"mappings": {"default": {"default": "켤레 행렬"}}, "key": "22B9"}, {"mappings": {"default": {"default": "층 구조"}}, "key": "22BA"}, {"mappings": {"default": {"default": "배타적 논리합"}}, "key": "22BB"}, {"mappings": {"default": {"default": "부정 논리곱"}}, "key": "22BC"}, {"mappings": {"default": {"default": "부정 논리합"}}, "key": "22BD"}, {"mappings": {"default": {"default": "직삼각형"}}, "key": "22BF"}, {"mappings": {"default": {"default": "n 항 논리곱"}, "mathspeak": {"default": "논리곱"}}, "key": "22C0"}, {"mappings": {"default": {"default": "n 항 논리합"}, "mathspeak": {"default": "논리합"}}, "key": "22C1"}, {"mappings": {"default": {"default": "n 항 교집합"}, "mathspeak": {"default": "교집합"}}, "key": "22C2"}, {"mappings": {"default": {"default": "n 항 합집합"}, "mathspeak": {"default": "합집합"}}, "key": "22C3"}, {"mappings": {"default": {"default": "다이아몬드 연산자"}}, "key": "22C4"}, {"mappings": {"default": {"default": "닷"}, "clearspeak": {"default": "닷", "MultsymbolDot_Dot": "내적"}}, "key": "22C5"}, {"mappings": {"default": {"default": "별"}}, "key": "22C6"}, {"mappings": {"default": {"default": "나눗셈 곱셈"}}, "key": "22C7"}, {"mappings": {"default": {"default": "나비 넥타이"}}, "key": "22C8"}, {"mappings": {"default": {"default": "왼 정상 계수 반직접곱"}}, "key": "22C9"}, {"mappings": {"default": {"default": "오른 정상 계수 반직접곱"}}, "key": "22CA"}, {"mappings": {"default": {"default": "왼 반직접곱"}}, "key": "22CB"}, {"mappings": {"default": {"default": "오른 반직접곱"}}, "key": "22CC"}, {"mappings": {"default": {"default": "역 물결 등호"}}, "key": "22CD"}, {"mappings": {"default": {"default": "휜 논리합"}}, "key": "22CE"}, {"mappings": {"default": {"default": "휜 논리곱"}}, "key": "22CF"}, {"mappings": {"default": {"default": "이중 부분집합"}}, "key": "22D0"}, {"mappings": {"default": {"default": "이중 상위집합"}}, "key": "22D1"}, {"mappings": {"default": {"default": "이중 교집합"}}, "key": "22D2"}, {"mappings": {"default": {"default": "이중 합집합"}}, "key": "22D3"}, {"mappings": {"default": {"default": "피치포크"}}, "key": "22D4"}, {"mappings": {"default": {"default": "같거나 평행하다"}, "clearspeak": {"default": "같거나 평행하다"}}, "key": "22D5"}, {"mappings": {"default": {"default": "작다"}, "clearspeak": {"default": "작다"}}, "key": "22D6"}, {"mappings": {"default": {"default": "크다"}, "clearspeak": {"default": "크다"}}, "key": "22D7"}, {"mappings": {"default": {"default": "훨씬훨씬 작다"}, "clearspeak": {"default": "훨씬훨씬 작다"}}, "key": "22D8"}, {"mappings": {"default": {"default": "훨씬훨씬 크다"}, "clearspeak": {"default": "훨씬훨씬 크다"}}, "key": "22D9"}, {"mappings": {"default": {"default": "작거나 같거나 크다"}, "clearspeak": {"default": "작거나 같거나 크다"}}, "key": "22DA"}, {"mappings": {"default": {"default": "크거나 같거나 작다"}, "clearspeak": {"default": "크거나 같거나 작다"}}, "key": "22DB"}, {"mappings": {"default": {"default": "같거나 작다"}, "clearspeak": {"default": "같거나 작다"}}, "key": "22DC"}, {"mappings": {"default": {"default": "같거나 크다"}, "clearspeak": {"default": "같거나 크다"}}, "key": "22DD"}, {"mappings": {"default": {"default": "같거나 선행한다"}, "clearspeak": {"default": "같거나 선행한다"}}, "key": "22DE"}, {"mappings": {"default": {"default": "같거나 후행한다"}, "clearspeak": {"default": "같거나 후행한다"}}, "key": "22DF"}, {"mappings": {"default": {"default": "선행하지도 같지도 않다"}}, "key": "22E0"}, {"mappings": {"default": {"default": "후행하지도 같지도 않다"}}, "key": "22E1"}, {"mappings": {"default": {"default": "각진 부분집합이지도 같지도 않다"}, "clearspeak": {"default": "각진 부분집합이지도 같지도 않다"}}, "key": "22E2"}, {"mappings": {"default": {"default": "각진 상위집합이지도 같지도 않다"}, "clearspeak": {"default": "각진 상위집합이지도 같지도 않다"}}, "key": "22E3"}, {"mappings": {"default": {"default": "각진 부분집합이지만 같지 않다"}, "clearspeak": {"default": "각진 부분집합이지만 같지 않다"}}, "key": "22E4"}, {"mappings": {"default": {"default": "각진 상위집합이지만 같지 않다"}, "clearspeak": {"default": "각진 상위집합이지만 같지 않다"}}, "key": "22E5"}, {"mappings": {"default": {"default": "작지만 동등하지 않다"}, "clearspeak": {"default": "작지만 동등하지 않다"}}, "key": "22E6"}, {"mappings": {"default": {"default": "크지만 동등하지 않다"}, "clearspeak": {"default": "크지만 동등하지 않다"}}, "key": "22E7"}, {"mappings": {"default": {"default": "선행하지만 동등하지 않다"}}, "key": "22E8"}, {"mappings": {"default": {"default": "후행하지만 동등하지 않다"}}, "key": "22E9"}, {"mappings": {"default": {"default": "다음의 일반 하위그룹이 아니다"}, "clearspeak": {"default": "다음의 일반 하위그룹이 아니다"}}, "key": "22EA"}, {"mappings": {"default": {"default": "다음을 일반 하위그룹으로 갖지 않는다"}}, "key": "22EB"}, {"mappings": {"default": {"default": "일반 하위그룹이지도 같지도 않다"}, "clearspeak": {"default": "일반 하위그룹이지도 같지도 않다"}}, "key": "22EC"}, {"mappings": {"default": {"default": "일반 하위그룹으로 갖지도 같지도 않다"}}, "key": "22ED"}, {"mappings": {"default": {"default": "세로 줄임표"}}, "key": "22EE"}, {"mappings": {"default": {"default": "줄임표"}, "clearspeak": {"default": "점 점 점"}}, "key": "22EF"}, {"mappings": {"default": {"default": "우상향 대각선 줄임표"}}, "key": "22F0"}, {"mappings": {"default": {"default": "우하향 대각선 줄임표"}}, "key": "22F1"}, {"mappings": {"default": {"default": "긴 가로선이 있는 원소 기호"}}, "key": "22F2"}, {"mappings": {"default": {"default": "가로선 끝에 세로 막대가 있는 원소 기호"}}, "key": "22F3"}, {"mappings": {"default": {"default": "가로선 끝에 세로 막대가 있는 원소 기호"}}, "key": "22F4"}, {"mappings": {"default": {"default": "점이 붙은 원소 기호"}}, "key": "22F5"}, {"mappings": {"default": {"default": "윗줄이 있는 원소 기호"}}, "key": "22F6"}, {"mappings": {"default": {"default": "윗줄이 있는 원소 기호"}}, "key": "22F7"}, {"mappings": {"default": {"default": "밑줄이 있는 원소 기호"}}, "key": "22F8"}, {"mappings": {"default": {"default": "두개의 가로선이 있는 원소 기호"}}, "key": "22F9"}, {"mappings": {"default": {"default": "긴 가로선이 있는 포함 기호"}}, "key": "22FA"}, {"mappings": {"default": {"default": "가로선 끝에 세로 막대가 있는 포함 기호"}}, "key": "22FB"}, {"mappings": {"default": {"default": "가로선 끝에 세로 막대가 있는 포함 기호"}}, "key": "22FC"}, {"mappings": {"default": {"default": "윗줄이 있는 포함 기호"}}, "key": "22FD"}, {"mappings": {"default": {"default": "윗줄이 있는 포함 기호"}}, "key": "22FE"}, {"mappings": {"default": {"default": "Z 표기법"}}, "key": "22FF"}, {"mappings": {"default": {"default": "지름 기호"}}, "key": "2300"}, {"mappings": {"default": {"default": "집"}}, "key": "2302"}, {"mappings": {"default": {"default": "사영"}}, "key": "2305"}, {"mappings": {"default": {"default": "원근법"}}, "key": "2306"}, {"mappings": {"default": {"default": "세로 톱니선"}}, "key": "2307"}, {"mappings": {"default": {"default": "역 부정 기호"}}, "key": "2310"}, {"mappings": {"default": {"default": "사각 마름모꼴"}}, "key": "2311"}, {"mappings": {"default": {"default": "호"}}, "key": "2312"}, {"mappings": {"default": {"default": "활꼴"}}, "key": "2313"}, {"mappings": {"default": {"default": "부채꼴"}}, "key": "2314"}, {"mappings": {"default": {"default": "진한 더하기"}}, "key": "2795"}, {"mappings": {"default": {"default": "진한 빼기"}}, "key": "2796"}, {"mappings": {"default": {"default": "진한 나누기"}}, "key": "2797"}, {"mappings": {"default": {"default": "루프"}}, "key": "27B0"}, {"mappings": {"default": {"default": "이중 루프"}}, "key": "27BF"}, {"mappings": {"default": {"default": "겹세모"}}, "key": "27C1"}, {"mappings": {"default": {"default": "수직"}, "clearspeak": {"default": "수직이다"}}, "key": "27C2"}, {"mappings": {"default": {"default": "열린 부분집합"}}, "key": "27C3"}, {"mappings": {"default": {"default": "열린 상위집합"}}, "key": "27C4"}, {"mappings": {"default": {"default": "점이 들어간 논리합"}}, "key": "27C7"}, {"mappings": {"default": {"default": "하위집합 앞에 백슬래시"}}, "key": "27C8"}, {"mappings": {"default": {"default": "슬래시 앞에 상위집합"}}, "key": "27C9"}, {"mappings": {"default": {"default": "가로 막대가 있는 세로선"}}, "key": "27CA"}, {"mappings": {"default": {"default": "우상향 대각선"}}, "key": "27CB"}, {"mappings": {"default": {"default": "나눗셈법 기호"}}, "key": "27CC"}, {"mappings": {"default": {"default": "우하향 대각선"}}, "key": "27CD"}, {"mappings": {"default": {"default": "사각문자 논리곱"}}, "key": "27CE"}, {"mappings": {"default": {"default": "사각문자 논리합"}}, "key": "27CF"}, {"mappings": {"default": {"default": "점이 들어간 다이아몬드"}}, "key": "27D0"}, {"mappings": {"default": {"default": "점이 들어간 논리곱"}}, "key": "27D1"}, {"mappings": {"default": {"default": "위를 바라보는 요소 기호"}}, "key": "27D2"}, {"mappings": {"default": {"default": "점이 있는 오른쪽 아래 모서리"}}, "key": "27D3"}, {"mappings": {"default": {"default": "점이 있는 왼쪽 위 모서리"}}, "key": "27D4"}, {"mappings": {"default": {"default": "왼쪽 외부 조인"}}, "key": "27D5"}, {"mappings": {"default": {"default": "오른쪽 외부 조인"}}, "key": "27D6"}, {"mappings": {"default": {"default": "양쪽 외부 조인"}}, "key": "27D7"}, {"mappings": {"default": {"default": "큰 거짓 기호"}}, "key": "27D8"}, {"mappings": {"default": {"default": "큰 참 기호"}}, "key": "27D9"}, {"mappings": {"default": {"default": "양쪽 이중 턴스틸"}}, "key": "27DA"}, {"mappings": {"default": {"default": "양쪽 턴스틸"}}, "key": "27DB"}, {"mappings": {"default": {"default": "왼쪽 멀티맵"}}, "key": "27DC"}, {"mappings": {"default": {"default": "긴 오른 턴스틸"}}, "key": "27DD"}, {"mappings": {"default": {"default": "긴 왼 턴스틸"}}, "key": "27DE"}, {"mappings": {"default": {"default": "원이 붙은 윗 압정"}}, "key": "27DF"}, {"mappings": {"default": {"default": "나침반"}}, "key": "27E0"}, {"mappings": {"default": {"default": "하얀 오목한 다이아몬드"}}, "key": "27E1"}, {"mappings": {"default": {"default": "왼쪽이 튀어나온 하얀 오목한 다이아몬드"}}, "key": "27E2"}, {"mappings": {"default": {"default": "오른쪽이 튀어나온 하얀 오목한 다이아몬드"}}, "key": "27E3"}, {"mappings": {"default": {"default": "하얀 사각형과 왼쪽 틱"}}, "key": "27E4"}, {"mappings": {"default": {"default": "하얀 사각형과 오른쪽 틱"}}, "key": "27E5"}, {"mappings": {"default": {"default": "우상향 대각선 밑으로 교차 우하향 대각선"}}, "key": "292B"}, {"mappings": {"default": {"default": "우하향 대각선 밑으로 교차 우상향 대각선"}}, "key": "292C"}, {"mappings": {"default": {"default": "삼중 세로선"}}, "key": "2980"}, {"mappings": {"default": {"default": "Z 표기법"}}, "key": "2981"}, {"mappings": {"default": {"default": "Z 표기법 하얀 콜론"}}, "key": "2982"}, {"mappings": {"default": {"default": "세로 사점"}}, "key": "2999"}, {"mappings": {"default": {"default": "세로 지그재그선"}}, "key": "299A"}, {"mappings": {"default": {"default": "역 공집합"}}, "key": "29B0"}, {"mappings": {"default": {"default": "윗줄이 있는 공집합"}}, "key": "29B1"}, {"mappings": {"default": {"default": "작은 원이 붙은 공집합"}}, "key": "29B2"}, {"mappings": {"default": {"default": "가로선이 있는 원"}}, "key": "29B5"}, {"mappings": {"default": {"default": "원문자 세로선"}}, "key": "29B6"}, {"mappings": {"default": {"default": "원문자 평행"}}, "key": "29B7"}, {"mappings": {"default": {"default": "원문자 백슬래시"}}, "key": "29B8"}, {"mappings": {"default": {"default": "원문자 수직"}}, "key": "29B9"}, {"mappings": {"default": {"default": "가로선으로 나눠지고 위 반원은 세로선으로 나눠진 원"}}, "key": "29BA"}, {"mappings": {"default": {"default": "X가 겹쳐진 원"}}, "key": "29BB"}, {"mappings": {"default": {"default": "원문자 반시계로 회전한 나누기 기호"}}, "key": "29BC"}, {"mappings": {"default": {"default": "원문자 하얀 글머리표"}}, "key": "29BE"}, {"mappings": {"default": {"default": "원문자 글머리표"}}, "key": "29BF"}, {"mappings": {"default": {"default": "원문자 작다"}}, "key": "29C0"}, {"mappings": {"default": {"default": "원문자 크다"}}, "key": "29C1"}, {"mappings": {"default": {"default": "오른쪽에 작은 원이 있는 원"}}, "key": "29C2"}, {"mappings": {"default": {"default": "오른쪽에 이중 가로선이 있는 원"}}, "key": "29C3"}, {"mappings": {"default": {"default": "사각문자 우상향 대각선"}}, "key": "29C4"}, {"mappings": {"default": {"default": "사각문자 우하향 대각선"}}, "key": "29C5"}, {"mappings": {"default": {"default": "사각문자 별표"}}, "key": "29C6"}, {"mappings": {"default": {"default": "사각문자 작은 원"}}, "key": "29C7"}, {"mappings": {"default": {"default": "사각문자 정사각형"}}, "key": "29C8"}, {"mappings": {"default": {"default": "두 개의 겹쳐진 정사각형"}}, "key": "29C9"}, {"mappings": {"default": {"default": "점이 붙은 삼각형"}}, "key": "29CA"}, {"mappings": {"default": {"default": "밑줄이 있는 삼각형"}}, "key": "29CB"}, {"mappings": {"default": {"default": "삼각형 안에 S"}}, "key": "29CC"}, {"mappings": {"default": {"default": "밑변이 돌출된 삼각형"}}, "key": "29CD"}, {"mappings": {"default": {"default": "우향 삼각형 밑에 좌향 삼각형"}}, "key": "29CE"}, {"mappings": {"default": {"default": "좌향 삼각형 오른쪽에 세로선"}}, "key": "29CF"}, {"mappings": {"default": {"default": "우향 삼각형 왼쪽에 세로선"}}, "key": "29D0"}, {"mappings": {"default": {"default": "왼쪽 절반이 검은 나비 넥타이"}}, "key": "29D1"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 나비넥타이"}}, "key": "29D2"}, {"mappings": {"default": {"default": "검은 나비넥타이"}}, "key": "29D3"}, {"mappings": {"default": {"default": "왼쪽 절반이 검은 곱하기"}}, "key": "29D4"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 곱하기"}}, "key": "29D5"}, {"mappings": {"default": {"default": "하얀 모래시계"}}, "key": "29D6"}, {"mappings": {"default": {"default": "검은 모래시계"}}, "key": "29D7"}, {"mappings": {"default": {"default": "불완전 무한대"}}, "key": "29DC"}, {"mappings": {"default": {"default": "묶음기호가 붙은 무한대"}}, "key": "29DD"}, {"mappings": {"default": {"default": "수직선으로 부정된 무한대"}}, "key": "29DE"}, {"mappings": {"default": {"default": "양끝 멀티맵"}}, "key": "29DF"}, {"mappings": {"default": {"default": "윤곽선이 있는 정사각형"}}, "key": "29E0"}, {"mappings": {"default": {"default": "로 증가한다"}}, "key": "29E1"}, {"mappings": {"default": {"default": "셔플 곱"}}, "key": "29E2"}, {"mappings": {"default": {"default": "등호와 평행 기호"}}, "key": "29E3"}, {"mappings": {"default": {"default": "등호와 평행 기호 위에 물결표"}}, "key": "29E4"}, {"mappings": {"default": {"default": "합동과 평행 기호"}}, "key": "29E5"}, {"mappings": {"default": {"default": "글리치 스탁"}}, "key": "29E6"}, {"mappings": {"default": {"default": "열역학적"}}, "key": "29E7"}, {"mappings": {"default": {"default": "왼쪽 절반이 검은 하향 삼각형"}}, "key": "29E8"}, {"mappings": {"default": {"default": "오른쪽 절반이 검은 하향 삼각형"}}, "key": "29E9"}, {"mappings": {"default": {"default": "검은 마름모"}}, "key": "29EB"}, {"mappings": {"default": {"default": "에러 바 하얀 정사각형"}}, "key": "29EE"}, {"mappings": {"default": {"default": "에러 바 검은 사각형"}}, "key": "29EF"}, {"mappings": {"default": {"default": "에러 바 하얀 다이아몬드"}}, "key": "29F0"}, {"mappings": {"default": {"default": "에러 바 검은 다이아몬드"}}, "key": "29F1"}, {"mappings": {"default": {"default": "에러 바 하얀 원"}}, "key": "29F2"}, {"mappings": {"default": {"default": "에러 바 검은 원"}}, "key": "29F3"}, {"mappings": {"default": {"default": "지연 규칙"}}, "key": "29F4"}, {"mappings": {"default": {"default": "백슬래시"}}, "key": "29F5"}, {"mappings": {"default": {"default": "윗줄이 있는 슬래시"}}, "key": "29F6"}, {"mappings": {"default": {"default": "가로선이 있는 백슬래시"}}, "key": "29F7"}, {"mappings": {"default": {"default": "큰 슬래시"}, "mathspeak": {"default": "슬래시"}}, "key": "29F8"}, {"mappings": {"default": {"default": "큰 백슬래시"}, "mathspeak": {"default": "백슬래시"}}, "key": "29F9"}, {"mappings": {"default": {"default": "이중 더하기"}}, "key": "29FA"}, {"mappings": {"default": {"default": "삼중 더하기"}}, "key": "29FB"}, {"mappings": {"default": {"default": "작은"}}, "key": "29FE"}, {"mappings": {"default": {"default": "미니"}}, "key": "29FF"}, {"mappings": {"default": {"default": "n 항 원문자 내적"}, "mathspeak": {"default": "원문자 내적"}}, "key": "2A00"}, {"mappings": {"default": {"default": "n 항 원문자 더하기"}, "mathspeak": {"default": "원문자 더하기"}}, "key": "2A01"}, {"mappings": {"default": {"default": "n 항 원문자 곱하기"}, "mathspeak": {"default": "원문자 곱하기"}}, "key": "2A02"}, {"mappings": {"default": {"default": "닷이 붙은 n 항 합집합"}, "mathspeak": {"default": "닷이 붙은 합집합"}}, "key": "2A03"}, {"mappings": {"default": {"default": "플러스가 붙은 n 항 합집합"}, "mathspeak": {"default": "플러스가 붙은 합집합"}}, "key": "2A04"}, {"mappings": {"default": {"default": "n 항 서로소 교집합"}, "mathspeak": {"default": "서로소 교집합"}}, "key": "2A05"}, {"mappings": {"default": {"default": "n 항 서로소 합집합"}, "mathspeak": {"default": "서로소 합집합"}}, "key": "2A06"}, {"mappings": {"default": {"default": "이중 논리곱"}}, "key": "2A07"}, {"mappings": {"default": {"default": "이중 논리합"}}, "key": "2A08"}, {"mappings": {"default": {"default": "n 항 곱하기"}, "mathspeak": {"default": "곱하기"}}, "key": "2A09"}, {"mappings": {"default": {"default": "모듈로 2 의 합"}}, "key": "2A0A"}, {"mappings": {"default": {"default": "리만합"}}, "key": "2A0B"}, {"mappings": {"default": {"default": "쿼드 인테그럴"}}, "key": "2A0C"}, {"mappings": {"default": {"default": "유한 적분"}}, "key": "2A0D"}, {"mappings": {"default": {"default": "두 줄이 그어진 적분"}}, "key": "2A0E"}, {"mappings": {"default": {"default": "사선이 그어진 적분 평균"}}, "key": "2A0F"}, {"mappings": {"default": {"default": "순환 함수"}}, "key": "2A10"}, {"mappings": {"default": {"default": "반시계 방향 적분"}}, "key": "2A11"}, {"mappings": {"default": {"default": "극점 주위를 사각형 모양이 둘러싼 선적분"}}, "key": "2A12"}, {"mappings": {"default": {"default": "극점 주위를 반원 모양이 둘러싼 선적분"}}, "key": "2A13"}, {"mappings": {"default": {"default": "극점을 포함하지 않는 선적분"}}, "key": "2A14"}, {"mappings": {"default": {"default": "닷을 둘러싼 인테그럴"}}, "key": "2A15"}, {"mappings": {"default": {"default": "사원수에 대한 인테그럴"}}, "key": "2A16"}, {"mappings": {"default": {"default": "곱하기가 붙은 인테그럴"}}, "key": "2A18"}, {"mappings": {"default": {"default": "교집합이 붙은 인테그럴"}}, "key": "2A19"}, {"mappings": {"default": {"default": "합집합이 붙은 인테그럴"}}, "key": "2A1A"}, {"mappings": {"default": {"default": "윗줄이 붙은 인테그럴"}}, "key": "2A1B"}, {"mappings": {"default": {"default": "밑줄이 붙은 인테그럴"}}, "key": "2A1C"}, {"mappings": {"default": {"default": "결합 구문"}}, "key": "2A1D"}, {"mappings": {"default": {"default": "큰 왼쪽 삼각형 연산자"}}, "key": "2A1E"}, {"mappings": {"default": {"default": "Z 표기법에서 스키마 합성 기호"}}, "key": "2A1F"}, {"mappings": {"default": {"default": "Z 표기법에서 스키마 파이핑 기호"}}, "key": "2A20"}, {"mappings": {"default": {"default": "Z 표기법에서 스키마 사영 기호"}}, "key": "2A21"}, {"mappings": {"default": {"default": "작은 원이 붙은 플러스"}}, "key": "2A22"}, {"mappings": {"default": {"default": "곡절 기호가 붙은 플러스"}}, "key": "2A23"}, {"mappings": {"default": {"default": "물결표가 붙은 플러스"}}, "key": "2A24"}, {"mappings": {"default": {"default": "아래에 점이 붙은 플러스"}}, "key": "2A25"}, {"mappings": {"default": {"default": "아래에 물결표가 붙은 플러스"}}, "key": "2A26"}, {"mappings": {"default": {"default": "아래첨자 2가 붙은 플러스"}}, "key": "2A27"}, {"mappings": {"default": {"default": "검은 삼각형이 붙은 플러스"}}, "key": "2A28"}, {"mappings": {"default": {"default": "쉼표가 붙은 마이너스"}}, "key": "2A29"}, {"mappings": {"default": {"default": "아래에 점이 붙은 마이너스"}}, "key": "2A2A"}, {"mappings": {"default": {"default": "하락 점들이 붙은 마이너스"}}, "key": "2A2B"}, {"mappings": {"default": {"default": "상승 점들이 붙은 마이너스"}}, "key": "2A2C"}, {"mappings": {"default": {"default": "왼쪽 반원이 붙은 플러스"}}, "key": "2A2D"}, {"mappings": {"default": {"default": "오른쪽 반원이 붙은 플러스"}}, "key": "2A2E"}, {"mappings": {"default": {"default": "외적"}}, "key": "2A2F"}, {"mappings": {"default": {"default": "닷이 붙은 곱하기"}}, "key": "2A30"}, {"mappings": {"default": {"default": "밑줄이 붙은 곱하기"}}, "key": "2A31"}, {"mappings": {"default": {"default": "아래가 닫힌 반직접곱"}}, "key": "2A32"}, {"mappings": {"default": {"default": "분쇄곱"}}, "key": "2A33"}, {"mappings": {"default": {"default": "왼쪽 반원이 붙은 곱하기"}}, "key": "2A34"}, {"mappings": {"default": {"default": "오른쪽 반원이 붙은 곱하기"}}, "key": "2A35"}, {"mappings": {"default": {"default": "곡절 악센트가 붙고 곱하기가 들어간 원"}}, "key": "2A36"}, {"mappings": {"default": {"default": "겹원문자 곱하기"}}, "key": "2A37"}, {"mappings": {"default": {"default": "원문자 나누기"}}, "key": "2A38"}, {"mappings": {"default": {"default": "플러스 기호가 들어간 삼각형"}}, "key": "2A39"}, {"mappings": {"default": {"default": "마이너스 기호가 들어간 삼각형"}}, "key": "2A3A"}, {"mappings": {"default": {"default": "곱하기가 들어간 삼각형"}}, "key": "2A3B"}, {"mappings": {"default": {"default": "내부곱"}}, "key": "2A3C"}, {"mappings": {"default": {"default": "역 내부곱"}}, "key": "2A3D"}, {"mappings": {"default": {"default": "Z 표기법에서 이항관계식의 합성"}}, "key": "2A3E"}, {"mappings": {"default": {"default": "쌍대곱"}}, "key": "2A3F"}, {"mappings": {"default": {"default": "닷이 들어간 교집합"}}, "key": "2A40"}, {"mappings": {"default": {"default": "마이너스가 들어간 합집합"}}, "key": "2A41"}, {"mappings": {"default": {"default": "윗줄이 있는 합집합"}}, "key": "2A42"}, {"mappings": {"default": {"default": "윗줄이 있는 교집합"}}, "key": "2A43"}, {"mappings": {"default": {"default": "논리곱이 들어간 교집합"}}, "key": "2A44"}, {"mappings": {"default": {"default": "논리합이 들어간 합집합"}}, "key": "2A45"}, {"mappings": {"default": {"default": "합집합이 붙은 교집합"}}, "key": "2A46"}, {"mappings": {"default": {"default": "교집합이 붙은 합집합"}}, "key": "2A47"}, {"mappings": {"default": {"default": "윗줄 위에 합집합이 있는 교집합"}}, "key": "2A48"}, {"mappings": {"default": {"default": "윗줄 위에 교집합이 있는 합집합"}}, "key": "2A49"}, {"mappings": {"default": {"default": "나란히 합쳐진 두 합집합 기호"}}, "key": "2A4A"}, {"mappings": {"default": {"default": "나란히 합쳐진 두 교집합 기호"}}, "key": "2A4B"}, {"mappings": {"default": {"default": "닫힌 합집합"}}, "key": "2A4C"}, {"mappings": {"default": {"default": "닫힌 교집합"}}, "key": "2A4D"}, {"mappings": {"default": {"default": "이중 교집합"}}, "key": "2A4E"}, {"mappings": {"default": {"default": "이중 합집합"}}, "key": "2A4F"}, {"mappings": {"default": {"default": "분쇄곱이 들어간 닫힌 합집합"}}, "key": "2A50"}, {"mappings": {"default": {"default": "닷이 붙은 논리곱"}}, "key": "2A51"}, {"mappings": {"default": {"default": "닷이 붙은 논리합"}}, "key": "2A52"}, {"mappings": {"default": {"default": "이중 논리곱"}}, "key": "2A53"}, {"mappings": {"default": {"default": "이중 논리합"}}, "key": "2A54"}, {"mappings": {"default": {"default": "교차된 두 논리곱"}}, "key": "2A55"}, {"mappings": {"default": {"default": "교차된 두 논리합"}}, "key": "2A56"}, {"mappings": {"default": {"default": "기울어진 논리합"}}, "key": "2A57"}, {"mappings": {"default": {"default": "기울어진 논리곱"}}, "key": "2A58"}, {"mappings": {"default": {"default": "논리곱과 논리합의 교차"}}, "key": "2A59"}, {"mappings": {"default": {"default": "세로선이 그어진 논리곱"}}, "key": "2A5A"}, {"mappings": {"default": {"default": "세로선이 그어진 논리합"}}, "key": "2A5B"}, {"mappings": {"default": {"default": "가로선이 그어진 논리곱"}}, "key": "2A5C"}, {"mappings": {"default": {"default": "가로선이 그어진 논리합"}}, "key": "2A5D"}, {"mappings": {"default": {"default": "이중 윗줄이 그어진 논리곱"}}, "key": "2A5E"}, {"mappings": {"default": {"default": "밑줄이 그어진 논리곱"}}, "key": "2A5F"}, {"mappings": {"default": {"default": "이중 밑줄이 그어진 논리곱"}}, "key": "2A60"}, {"mappings": {"default": {"default": "밑줄 그어진 작은 브이"}}, "key": "2A61"}, {"mappings": {"default": {"default": "이중 윗줄이 그어진 논리합"}}, "key": "2A62"}, {"mappings": {"default": {"default": "이중 밑줄이 그어진 논리합"}}, "key": "2A63"}, {"mappings": {"default": {"default": "Z 표기법에서 정의역 비제한 기호"}}, "key": "2A64"}, {"mappings": {"default": {"default": "Z 표기법에서 치역 비제한 기호"}}, "key": "2A65"}, {"mappings": {"default": {"default": "아래에 점이 붙은 등호"}}, "key": "2A66"}, {"mappings": {"default": {"default": "점이 붙은 합동"}}, "key": "2A67"}, {"mappings": {"default": {"default": "이중 세로선이 그어진 삼중 가로선"}}, "key": "2A68"}, {"mappings": {"default": {"default": "삼중 세로선이 그어진 삼중 가로선"}}, "key": "2A69"}, {"mappings": {"default": {"default": "점근적으로 근사하다"}}, "key": "2A6A"}, {"mappings": {"default": {"default": "매우 근사하다"}}, "key": "2A6B"}, {"mappings": {"default": {"default": "마이너스가 사이에 있는 근사 기호"}}, "key": "2A6C"}, {"mappings": {"default": {"default": "점근적으로 합동이다"}}, "key": "2A6D"}, {"mappings": {"default": {"default": "별표가 붙은 등호"}}, "key": "2A6E"}, {"mappings": {"default": {"default": "곡절 기호가 붙은 근사 기호"}}, "key": "2A6F"}, {"mappings": {"default": {"default": "거의 같거나 같다"}}, "key": "2A70"}, {"mappings": {"default": {"default": "증가"}}, "key": "2A71"}, {"mappings": {"default": {"default": "증가"}}, "key": "2A72"}, {"mappings": {"default": {"default": "근사 기호"}}, "key": "2A73"}, {"mappings": {"default": {"default": "이중 콜론이 붙은 등호"}}, "key": "2A74"}, {"mappings": {"default": {"default": "이중 등호"}}, "key": "2A75"}, {"mappings": {"default": {"default": "삼중 등호"}}, "key": "2A76"}, {"mappings": {"default": {"default": "위아래에 두 개씩 점이 붙은 등호"}}, "key": "2A77"}, {"mappings": {"default": {"default": "위에 네 개의 점이 붙은 등호"}}, "key": "2A78"}, {"mappings": {"default": {"default": "원이 들어간 작다"}}, "key": "2A79"}, {"mappings": {"default": {"default": "원이 들어간 크다"}}, "key": "2A7A"}, {"mappings": {"default": {"default": "물음표가 붙은 작다"}}, "key": "2A7B"}, {"mappings": {"default": {"default": "물음표가 붙은 크다"}}, "key": "2A7C"}, {"mappings": {"default": {"default": "작거나 같다"}}, "key": "2A7D"}, {"mappings": {"default": {"default": "크거나 같다"}}, "key": "2A7E"}, {"mappings": {"default": {"default": "닷이 들어간 작거나 같다"}}, "key": "2A7F"}, {"mappings": {"default": {"default": "닷이 들어간 크거나 같다"}}, "key": "2A80"}, {"mappings": {"default": {"default": "위에 닷이 붙은 작거나 같다"}}, "key": "2A81"}, {"mappings": {"default": {"default": "위에 닷이 붙은 크거나 같다"}}, "key": "2A82"}, {"mappings": {"default": {"default": "오른쪽 상단에 닷이 붙은 작거나 같다"}}, "key": "2A83"}, {"mappings": {"default": {"default": "왼쪽 상단에 닷이 붙은 크거나 같다"}}, "key": "2A84"}, {"mappings": {"default": {"default": "거의 같거나 작다"}}, "key": "2A85"}, {"mappings": {"default": {"default": "거의 같거나 크다"}}, "key": "2A86"}, {"mappings": {"default": {"default": "작다"}}, "key": "2A87"}, {"mappings": {"default": {"default": "크다"}}, "key": "2A88"}, {"mappings": {"default": {"default": "작지만 근사하지 않다"}}, "key": "2A89"}, {"mappings": {"default": {"default": "크지만 근사하지 않다"}}, "key": "2A8A"}, {"mappings": {"default": {"default": "작거나 크거나 같다"}}, "key": "2A8B"}, {"mappings": {"default": {"default": "크거나 작거나 같다"}}, "key": "2A8C"}, {"mappings": {"default": {"default": "작거나 거의 같거나 같다"}}, "key": "2A8D"}, {"mappings": {"default": {"default": "크거나 거의 같거나 같다"}}, "key": "2A8E"}, {"mappings": {"default": {"default": "작거나 크거나 거의 같다"}}, "key": "2A8F"}, {"mappings": {"default": {"default": "크거나 작거나 거의 같다"}}, "key": "2A90"}, {"mappings": {"default": {"default": "작거나 크거나 같다"}}, "key": "2A91"}, {"mappings": {"default": {"default": "크거나 작거나 같다"}}, "key": "2A92"}, {"mappings": {"default": {"default": "작거나 크거나 같다"}}, "key": "2A93"}, {"mappings": {"default": {"default": "크거나 작거나 같다"}}, "key": "2A94"}, {"mappings": {"default": {"default": "크거나 같다"}}, "key": "2A95"}, {"mappings": {"default": {"default": "작거나 같다"}}, "key": "2A96"}, {"mappings": {"default": {"default": "닷이 들어간 작거나 같다"}}, "key": "2A97"}, {"mappings": {"default": {"default": "닷이 들어간 크거나 같다"}}, "key": "2A98"}, {"mappings": {"default": {"default": "크거나 같다"}}, "key": "2A99"}, {"mappings": {"default": {"default": "작거나 같다"}}, "key": "2A9A"}, {"mappings": {"default": {"default": "크거나 같다"}}, "key": "2A9B"}, {"mappings": {"default": {"default": "작거나 같다"}}, "key": "2A9C"}, {"mappings": {"default": {"default": "작거나 거의 같다"}}, "key": "2A9D"}, {"mappings": {"default": {"default": "크거나 거의 같다"}}, "key": "2A9E"}, {"mappings": {"default": {"default": "작거나 거의 같다"}}, "key": "2A9F"}, {"mappings": {"default": {"default": "크거나 거의 같다"}}, "key": "2AA0"}, {"mappings": {"default": {"default": "훨씬 크다"}}, "key": "2AA1"}, {"mappings": {"default": {"default": "훨씬 작다"}}, "key": "2AA2"}, {"mappings": {"default": {"default": "훨씬 크거나 같다"}}, "key": "2AA3"}, {"mappings": {"default": {"default": "같지 않다"}}, "key": "2AA4"}, {"mappings": {"default": {"default": "같지 않다"}}, "key": "2AA5"}, {"mappings": {"default": {"default": "닫힌 작다"}}, "key": "2AA6"}, {"mappings": {"default": {"default": "닫힌 크다"}}, "key": "2AA7"}, {"mappings": {"default": {"default": "같거나 닫힌 작다"}}, "key": "2AA8"}, {"mappings": {"default": {"default": "같거나 닫힌 크다"}}, "key": "2AA9"}, {"mappings": {"default": {"default": "크다"}, "clearspeak": {"default": "크다"}}, "key": "2AAA"}, {"mappings": {"default": {"default": "작다"}, "clearspeak": {"default": "작다"}}, "key": "2AAB"}, {"mappings": {"default": {"default": "크거나 같다"}, "clearspeak": {"default": "크거나 같다"}}, "key": "2AAC"}, {"mappings": {"default": {"default": "작거나 같다"}, "clearspeak": {"default": "작거나 같다"}}, "key": "2AAD"}, {"mappings": {"default": {"default": "방지턱이 붙은 등호"}}, "key": "2AAE"}, {"mappings": {"default": {"default": "같거나 선행한다"}}, "key": "2AAF"}, {"mappings": {"default": {"default": "같거나 후행한다"}}, "key": "2AB0"}, {"mappings": {"default": {"default": "같지 않고 선행한다"}}, "key": "2AB1"}, {"mappings": {"default": {"default": "같지 않고 후행한다"}}, "key": "2AB2"}, {"mappings": {"default": {"default": "같거나 선행한다"}}, "key": "2AB3"}, {"mappings": {"default": {"default": "같거나 후행한다"}}, "key": "2AB4"}, {"mappings": {"default": {"default": "같지 않고 선행한다"}}, "key": "2AB5"}, {"mappings": {"default": {"default": "같지 않고 후행한다"}}, "key": "2AB6"}, {"mappings": {"default": {"default": "거의 같거나 선행한다"}}, "key": "2AB7"}, {"mappings": {"default": {"default": "거의 같거나 후행한다"}}, "key": "2AB8"}, {"mappings": {"default": {"default": "근사하지 않고 선행한다"}}, "key": "2AB9"}, {"mappings": {"default": {"default": "근사하지 않고 후행한다"}}, "key": "2ABA"}, {"mappings": {"default": {"default": "훨씬 선행한다"}}, "key": "2ABB"}, {"mappings": {"default": {"default": "훨씬 후행한다"}}, "key": "2ABC"}, {"mappings": {"default": {"default": "닷이 들어간 부분집합"}}, "key": "2ABD"}, {"mappings": {"default": {"default": "닷이 들어간 상위집합"}}, "key": "2ABE"}, {"mappings": {"default": {"default": "플러스가 붙은 부분집합"}}, "key": "2ABF"}, {"mappings": {"default": {"default": "플러스가 붙은 상위집합"}}, "key": "2AC0"}, {"mappings": {"default": {"default": "곱하기가 붙은 부분집합"}}, "key": "2AC1"}, {"mappings": {"default": {"default": "곱하기가 붙은 상위집합"}}, "key": "2AC2"}, {"mappings": {"default": {"default": "닷이 붙은 부분집합"}}, "key": "2AC3"}, {"mappings": {"default": {"default": "닷이 붙은 상위집합"}}, "key": "2AC4"}, {"mappings": {"default": {"default": "부분집합"}, "clearspeak": {"default": "부분집합"}}, "key": "2AC5"}, {"mappings": {"default": {"default": "상위집합"}, "clearspeak": {"default": "상위집합"}}, "key": "2AC6"}, {"mappings": {"default": {"default": "거의 같거나 부분집합"}}, "key": "2AC7"}, {"mappings": {"default": {"default": "거의 같거나 상위집합"}}, "key": "2AC8"}, {"mappings": {"default": {"default": "거의 같거나 부분집합"}}, "key": "2AC9"}, {"mappings": {"default": {"default": "거의 같거나 상위집합"}}, "key": "2ACA"}, {"mappings": {"default": {"default": "진부분집합"}}, "key": "2ACB"}, {"mappings": {"default": {"default": "진상위집합"}}, "key": "2ACC"}, {"mappings": {"default": {"default": "오른쪽으로 열린 박스 기호"}}, "key": "2ACD"}, {"mappings": {"default": {"default": "왼쪽으로 열린 박스 기호"}}, "key": "2ACE"}, {"mappings": {"default": {"default": "닫힌 부분집합"}}, "key": "2ACF"}, {"mappings": {"default": {"default": "닫힌 상위집합"}}, "key": "2AD0"}, {"mappings": {"default": {"default": "닫힌 부분집합이거나 같다"}}, "key": "2AD1"}, {"mappings": {"default": {"default": "닫힌 상위집합이거나 같다"}}, "key": "2AD2"}, {"mappings": {"default": {"default": "위에 부분집합이 붙은 상위집합"}}, "key": "2AD3"}, {"mappings": {"default": {"default": "위에 상위집합이 붙은 부분집합"}}, "key": "2AD4"}, {"mappings": {"default": {"default": "위에 부분집합이 붙은 부분집합"}}, "key": "2AD5"}, {"mappings": {"default": {"default": "위에 상위집합이 붙은 상위집합"}}, "key": "2AD6"}, {"mappings": {"default": {"default": "나란히 겹쳐진 상위집합과 부분집합"}}, "key": "2AD7"}, {"mappings": {"default": {"default": "사이에 가로선으로 이어진 상위집합과 부분집합"}}, "key": "2AD8"}, {"mappings": {"default": {"default": "위로 볼록 기호"}}, "key": "2AD9"}, {"mappings": {"default": {"default": "윗줄이 붙은 피치포크"}}, "key": "2ADA"}, {"mappings": {"default": {"default": "세로선이 그어진 교집합"}}, "key": "2ADB"}, {"mappings": {"default": {"default": "분기"}}, "key": "2ADC"}, {"mappings": {"default": {"default": "비분기"}}, "key": "2ADD"}, {"mappings": {"default": {"default": "짦은 왼 턴스틸"}}, "key": "2ADE"}, {"mappings": {"default": {"default": "짦은 참 기호"}}, "key": "2ADF"}, {"mappings": {"default": {"default": "짦은 거짓 기호"}}, "key": "2AE0"}, {"mappings": {"default": {"default": "수직 오른쪽에 S 자"}}, "key": "2AE1"}, {"mappings": {"default": {"default": "세로선이 그어진 삼중 오른 턴스틸"}}, "key": "2AE2"}, {"mappings": {"default": {"default": "세로선이 그어진 왼 턴스틸"}}, "key": "2AE3"}, {"mappings": {"default": {"default": "이중 왼 턴스틸"}}, "key": "2AE4"}, {"mappings": {"default": {"default": "세로선이 그어진 이중 왼 턴스틸"}}, "key": "2AE5"}, {"mappings": {"default": {"default": "세로선이 그어진 오른 턴스틸"}}, "key": "2AE6"}, {"mappings": {"default": {"default": "윗줄이 붙은 참 기호"}}, "key": "2AE7"}, {"mappings": {"default": {"default": "밑줄이 붙은 거짓 기호"}}, "key": "2AE8"}, {"mappings": {"default": {"default": "참 거짓 기호"}}, "key": "2AE9"}, {"mappings": {"default": {"default": "조건부 종속"}}, "key": "2AEA"}, {"mappings": {"default": {"default": "조건부 독립"}}, "key": "2AEB"}, {"mappings": {"default": {"default": "이중 부정 기호"}}, "key": "2AEC"}, {"mappings": {"default": {"default": "역 이중 부정 기호"}}, "key": "2AED"}, {"mappings": {"default": {"default": "다음의 약수가 아니다"}}, "key": "2AEE"}, {"mappings": {"default": {"default": "위에 원이 붙은 세로선"}}, "key": "2AEF"}, {"mappings": {"default": {"default": "아래에 원이 붙은 세로선"}}, "key": "2AF0"}, {"mappings": {"default": {"default": "원이 붙은 아랫 압정"}}, "key": "2AF1"}, {"mappings": {"default": {"default": "가로선이 그어진 평행"}}, "key": "2AF2"}, {"mappings": {"default": {"default": "물결표가 그어진 평행"}}, "key": "2AF3"}, {"mappings": {"default": {"default": "삼중 세로선"}}, "key": "2AF4"}, {"mappings": {"default": {"default": "가로선이 붙은 삼중 세로선"}}, "key": "2AF5"}, {"mappings": {"default": {"default": "삼중 콜론"}}, "key": "2AF6"}, {"mappings": {"default": {"default": "훨씬훨씬 작다"}}, "key": "2AF7"}, {"mappings": {"default": {"default": "훨씬훨씬 크다"}}, "key": "2AF8"}, {"mappings": {"default": {"default": "작거나 같다"}}, "key": "2AF9"}, {"mappings": {"default": {"default": "크거나 같다"}}, "key": "2AFA"}, {"mappings": {"default": {"default": "삼중 사선"}}, "key": "2AFB"}, {"mappings": {"default": {"default": "큰 삼중 세로선"}}, "key": "2AFC"}, {"mappings": {"default": {"default": "이중 사선"}}, "key": "2AFD"}, {"mappings": {"default": {"default": "하얀 세로선"}}, "key": "2AFE"}, {"mappings": {"default": {"default": "n 항 하얀 세로선"}, "mathspeak": {"default": "하얀 세로선"}}, "key": "2AFF"}, {"mappings": {"default": {"default": "물결표"}}, "key": "301C"}, {"mappings": {"default": {"default": "세로쓰기용 쉼표"}}, "key": "FE10"}, {"mappings": {"default": {"default": "세로쓰기용 콜론"}}, "key": "FE13"}, {"mappings": {"default": {"default": "세로쓰기용 세미콜론"}}, "key": "FE14"}, {"mappings": {"default": {"default": "세로쓰기용 느낌표"}}, "key": "FE15"}, {"mappings": {"default": {"default": "세로쓰기용 물음표"}}, "key": "FE16"}, {"mappings": {"default": {"default": "세로쓰기용 줄임표"}}, "key": "FE19"}, {"mappings": {"default": {"default": "세로쓰기용 이점"}}, "key": "FE30"}, {"mappings": {"default": {"default": "세로쓰기용 긴줄표"}}, "key": "FE31"}, {"mappings": {"default": {"default": "세로쓰기용 짧은줄표"}}, "key": "FE32"}, {"mappings": {"default": {"default": "세로쓰기용 밑줄"}}, "key": "FE33"}, {"mappings": {"default": {"default": "세로쓰기용 물결 밑줄"}}, "key": "FE34"}, {"mappings": {"default": {"default": "모점"}}, "key": "FE45"}, {"mappings": {"default": {"default": "하얀 모점"}}, "key": "FE46"}, {"mappings": {"default": {"default": "파선 윗줄"}}, "key": "FE49"}, {"mappings": {"default": {"default": "쇄선 윗줄"}}, "key": "FE4A"}, {"mappings": {"default": {"default": "물결 윗줄"}}, "key": "FE4B"}, {"mappings": {"default": {"default": "이중 물결 윗줄"}}, "key": "FE4C"}, {"mappings": {"default": {"default": "파선 밑줄"}}, "key": "FE4D"}, {"mappings": {"default": {"default": "쇄선 밑줄"}}, "key": "FE4E"}, {"mappings": {"default": {"default": "물결 밑줄"}}, "key": "FE4F"}, {"mappings": {"default": {"default": "작은 쉼표"}}, "key": "FE50"}, {"mappings": {"default": {"default": "작은 마침표"}}, "key": "FE52"}, {"mappings": {"default": {"default": "작은 세미콜론"}}, "key": "FE54"}, {"mappings": {"default": {"default": "작은 콜론"}}, "key": "FE55"}, {"mappings": {"default": {"default": "작은 물음표"}}, "key": "FE56"}, {"mappings": {"default": {"default": "작은 느낌표"}}, "key": "FE57"}, {"mappings": {"default": {"default": "작은 긴줄표"}}, "key": "FE58"}, {"mappings": {"default": {"default": "작은 샵"}}, "key": "FE5F"}, {"mappings": {"default": {"default": "작은 앰퍼샌드"}}, "key": "FE60"}, {"mappings": {"default": {"default": "작은 별표"}}, "key": "FE61"}, {"mappings": {"default": {"default": "작은 플러스"}}, "key": "FE62"}, {"mappings": {"default": {"default": "작은 마이너스"}}, "key": "FE63"}, {"mappings": {"default": {"default": "작은 미만 부등호"}}, "key": "FE64"}, {"mappings": {"default": {"default": "작은 초과 부등호"}}, "key": "FE65"}, {"mappings": {"default": {"default": "작은 등호"}}, "key": "FE66"}, {"mappings": {"default": {"default": "작은 백슬래시", "alternative": "작은 역 사선"}}, "key": "FE68"}, {"mappings": {"default": {"default": "작은 달러"}}, "key": "FE69"}, {"mappings": {"default": {"default": "작은 퍼센트"}}, "key": "FE6A"}, {"mappings": {"default": {"default": "작은 골뱅이표"}}, "key": "FE6B"}, {"mappings": {"default": {"default": "느낌표"}}, "key": "FF01"}, {"mappings": {"default": {"default": "큰따옴표"}}, "key": "FF02"}, {"mappings": {"default": {"default": "샵"}}, "key": "FF03"}, {"mappings": {"default": {"default": "달러"}}, "key": "FF04"}, {"mappings": {"default": {"default": "퍼센트"}}, "key": "FF05"}, {"mappings": {"default": {"default": "앰퍼샌드"}}, "key": "FF06"}, {"mappings": {"default": {"default": "아포스트로피"}}, "key": "FF07"}, {"mappings": {"default": {"default": "별표"}}, "key": "FF0A"}, {"mappings": {"default": {"default": "더하기"}}, "key": "FF0B"}, {"mappings": {"default": {"default": "쉼표"}}, "key": "FF0C"}, {"mappings": {"default": {"default": "빼기"}}, "key": "FF0D"}, {"mappings": {"default": {"default": "마침표"}}, "key": "FF0E"}, {"mappings": {"default": {"default": "슬래시", "alternative": "사선"}}, "key": "FF0F"}, {"mappings": {"default": {"default": "콜론"}}, "key": "FF1A"}, {"mappings": {"default": {"default": "세미콜론"}}, "key": "FF1B"}, {"mappings": {"default": {"default": "작다"}}, "key": "FF1C"}, {"mappings": {"default": {"default": "같다"}}, "key": "FF1D"}, {"mappings": {"default": {"default": "크다"}}, "key": "FF1E"}, {"mappings": {"default": {"default": "물음표"}}, "key": "FF1F"}, {"mappings": {"default": {"default": "골뱅이표"}}, "key": "FF20"}, {"mappings": {"default": {"default": "백 슬래시", "alternative": "반전된 사선"}}, "key": "FF3C"}, {"mappings": {"default": {"default": "삽입 기호", "alternative": "곡절 악센트"}}, "key": "FF3E"}, {"mappings": {"default": {"default": "밑줄", "alternative": "밑줄"}}, "key": "FF3F"}, {"mappings": {"default": {"default": "억음 악센트"}}, "key": "FF40"}, {"mappings": {"default": {"default": "세로선"}}, "key": "FF5C"}, {"mappings": {"default": {"default": "물결표"}}, "key": "FF5E"}, {"mappings": {"default": {"default": "센트"}}, "key": "FFE0"}, {"mappings": {"default": {"default": "파운드"}}, "key": "FFE1"}, {"mappings": {"default": {"default": "부정"}}, "key": "FFE2"}, {"mappings": {"default": {"default": "장음 기호"}, "mathspeak": {"default": "바"}}, "key": "FFE3"}, {"mappings": {"default": {"default": "끊어진 세로선"}}, "key": "FFE4"}, {"mappings": {"default": {"default": "엔"}}, "key": "FFE5"}, {"mappings": {"default": {"default": "원"}}, "key": "FFE6"}, {"mappings": {"default": {"default": "반각 얇은 세로선"}}, "key": "FFE8"}, {"mappings": {"default": {"default": "반각 검은 사각형"}}, "key": "FFED"}, {"mappings": {"default": {"default": "반각 하얀 원"}}, "key": "FFEE"}], "ko/symbols/math_whitespace.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "공간"}}, "key": "0020"}, {"mappings": {"default": {"default": "중간 없는 공간", "alternative": "중간 없는 공간"}}, "key": "00A0"}, {"mappings": {"default": {"default": "소프트 하이픈"}}, "key": "00AD"}, {"mappings": {"default": {"default": "엔 쿼드"}}, "key": "2000"}, {"mappings": {"default": {"default": "엠 쿼드"}}, "key": "2001"}, {"mappings": {"default": {"default": "엔 스페이스"}}, "key": "2002"}, {"mappings": {"default": {"default": "엠 스페이스"}}, "key": "2003"}, {"mappings": {"default": {"default": "3PM 공간"}}, "key": "2004"}, {"mappings": {"default": {"default": "4PM 공간"}}, "key": "2005"}, {"mappings": {"default": {"default": "6MP 공간간"}}, "key": "2006"}, {"mappings": {"default": {"default": "그림 공간"}}, "key": "2007"}, {"mappings": {"default": {"default": "구두점"}}, "key": "2008"}, {"mappings": {"default": {"default": "얇은 공간"}}, "key": "2009"}, {"mappings": {"default": {"default": "헤어 스페이스"}}, "key": "200A"}, {"mappings": {"default": {"default": "제로 너비 공간"}}, "key": "200B"}, {"mappings": {"default": {"default": "제로 폭 비결합자"}}, "key": "200C"}, {"mappings": {"default": {"default": "제로 너비 결합자"}}, "key": "200D"}, {"mappings": {"default": {"default": "왼쪽에서 오른쪽으로 표시"}}, "key": "200E"}, {"mappings": {"default": {"default": "오른쪽에서 왼쪽으로 표시"}}, "key": "200F"}, {"mappings": {"default": {"default": "라인 구분 기호"}}, "key": "2028"}, {"mappings": {"default": {"default": "단락 구분 기호"}}, "key": "2029"}, {"mappings": {"default": {"default": "왼쪽에서 오른쪽으로 포함"}}, "key": "202A"}, {"mappings": {"default": {"default": "오른쪽에서 왼쪽으로 임베딩"}}, "key": "202B"}, {"mappings": {"default": {"default": "팝 방향 지정"}}, "key": "202C"}, {"mappings": {"default": {"default": "왼쪽에서 오른쪽으로 재정의"}}, "key": "202D"}, {"mappings": {"default": {"default": "오른쪽에서 왼쪽으로 재정의"}}, "key": "202E"}, {"mappings": {"default": {"default": "좁은 브레이크 공간 없음"}}, "key": "202F"}, {"mappings": {"default": {"default": "중간 수학 공간"}}, "key": "205F"}, {"mappings": {"default": {"default": "단어 결합 자"}}, "key": "2060"}, {"mappings": {"default": {"default": "의", "alternative": "기능 응용"}}, "key": "2061"}, {"mappings": {"default": {"default": "곱셈", "alternative": "보이지 않는 곱셈"}, "clearspeak": {"default": "곱하기"}}, "key": "2062"}, {"mappings": {"default": {"default": "콤마", "alternative": "보이지 않는 구분 기호"}}, "key": "2063"}, {"mappings": {"default": {"default": "플러스", "alternative": "보이지 않는 플러스"}}, "key": "2064"}, {"mappings": {"default": {"default": "대칭 스와핑 금지"}}, "key": "206A"}, {"mappings": {"default": {"default": "대칭 교환 활성화"}}, "key": "206B"}, {"mappings": {"default": {"default": "내셔널 디지트 셰이프"}}, "key": "206E"}, {"mappings": {"default": {"default": "공칭 자리 모양"}}, "key": "206F"}, {"mappings": {"default": {"default": "제로 너비 브레이크 공간 없음", "alternative": "byte order mark"}}, "key": "FEFF"}, {"mappings": {"default": {"default": "선간 주석 앵커"}}, "key": "FFF9"}, {"mappings": {"default": {"default": "선간 주석 구분 기호"}}, "key": "FFFA"}, {"mappings": {"default": {"default": "선간 주석 종결 기호"}}, "key": "FFFB"}], "ko/symbols/other_stars.min": [{"locale": "ko"}, {"mappings": {"default": {"default": "십진 지수 기호"}}, "key": "23E8"}, {"mappings": {"default": {"default": "검은색 별"}}, "key": "2605"}, {"mappings": {"default": {"default": "흰색 별"}}, "key": "2606"}, {"mappings": {"default": {"default": "흰색 원", "alternative": "흰색 원"}}, "key": "26AA"}, {"mappings": {"default": {"default": "검은색 원", "alternative": "검은색 원"}}, "key": "26AB"}, {"mappings": {"default": {"default": "흰색 체크 마크", "alternative": "흰색 진한 체크 마크"}}, "key": "2705"}, {"mappings": {"default": {"default": "체크 마크"}}, "key": "2713"}, {"mappings": {"default": {"default": "진한 체크 마크"}}, "key": "2714"}, {"mappings": {"default": {"default": "곱셈 X"}}, "key": "2715"}, {"mappings": {"default": {"default": "진한 곱셈 X"}}, "key": "2716"}, {"mappings": {"default": {"default": "투표 용지 X"}}, "key": "2717"}, {"mappings": {"default": {"default": "진한 투표 X"}}, "key": "2718"}, {"mappings": {"default": {"default": "오픈 센터 크로스"}}, "key": "271B"}, {"mappings": {"default": {"default": "진한 오픈 센터 크로스"}}, "key": "271C"}, {"mappings": {"default": {"default": "몰타 크로스"}}, "key": "2720"}, {"mappings": {"default": {"default": "다윗의 별"}}, "key": "2721"}, {"mappings": {"default": {"default": "네 눈물 방울 별표"}}, "key": "2722"}, {"mappings": {"default": {"default": "네개의 풍선이 달린 별표"}}, "key": "2723"}, {"mappings": {"default": {"default": "진한 네개의 풍선이 달린 별표"}}, "key": "2724"}, {"mappings": {"default": {"default": "네개 클럽 오키드 별표"}}, "key": "2725"}, {"mappings": {"default": {"default": "검은색 네개의 뾰족한 별"}}, "key": "2726"}, {"mappings": {"default": {"default": "흰색 개의 뾰족한 별"}}, "key": "2727"}, {"mappings": {"default": {"default": "반짝임"}}, "key": "2728"}, {"mappings": {"default": {"default": "스트레스 윤곽선 화이트 스타"}}, "key": "2729"}, {"mappings": {"default": {"default": "흰색 동그라미"}}, "key": "272A"}, {"mappings": {"default": {"default": "오픈 센터 블랙 스타"}}, "key": "272B"}, {"mappings": {"default": {"default": "블랙 센터 화이트 스타"}}, "key": "272C"}, {"mappings": {"default": {"default": "블랙 스타 윤곽선"}}, "key": "272D"}, {"mappings": {"default": {"default": "헤비 윤곽선 블랙 스타"}}, "key": "272E"}, {"mappings": {"default": {"default": "바람개비 별"}}, "key": "272F"}, {"mappings": {"default": {"default": "그림자 달린 흰색 별"}}, "key": "2730"}, {"mappings": {"default": {"default": "진한 별표"}}, "key": "2731"}, {"mappings": {"default": {"default": "오픈 센터 별표"}}, "key": "2732"}, {"mappings": {"default": {"default": "여덟개의 스포크 별표"}}, "key": "2733"}, {"mappings": {"default": {"default": "여덟 뾰족한 블랙 스타"}}, "key": "2734"}, {"mappings": {"default": {"default": "여덟개 뾰족한 바람개비 별"}}, "key": "2735"}, {"mappings": {"default": {"default": "여섯개 포인티드 블랙 스타"}}, "key": "2736"}, {"mappings": {"default": {"default": "열두개 포인티드 검은색 별"}}, "key": "2739"}, {"mappings": {"default": {"default": "개의 별표"}}, "key": "273A"}, {"mappings": {"default": {"default": "눈물 방울 별표"}}, "key": "273B"}, {"mappings": {"default": {"default": "오픈 센터 눈물 방울 별표"}}, "key": "273C"}, {"mappings": {"default": {"default": "진한 눈물 방울 별표"}}, "key": "273D"}, {"mappings": {"default": {"default": "여섯 개의 흑백 흑백 플로레트"}}, "key": "273E"}, {"mappings": {"default": {"default": "블랙 플로레트"}}, "key": "273F"}, {"mappings": {"default": {"default": "화이트 플로레"}}, "key": "2740"}, {"mappings": {"default": {"default": "여덟 바퀴가 달린 검은 색 플로트"}}, "key": "2741"}, {"mappings": {"default": {"default": "둥근 오픈 센터 여덟개 뾰족한 별"}}, "key": "2742"}, {"mappings": {"default": {"default": "진한 눈물 방울뱀 바람개비 별표"}}, "key": "2743"}, {"mappings": {"default": {"default": "눈송이"}}, "key": "2744"}, {"mappings": {"default": {"default": "단단한 삼엽 자리의 눈송이"}}, "key": "2745"}, {"mappings": {"default": {"default": "헤비 쉐브론 눈송이"}}, "key": "2746"}, {"mappings": {"default": {"default": "불꽃"}}, "key": "2747"}, {"mappings": {"default": {"default": "헤비 스파클"}}, "key": "2748"}, {"mappings": {"default": {"default": "풍선이 달린 별표"}}, "key": "2749"}, {"mappings": {"default": {"default": "여덟 눈물 방패 프로펠러 별표"}}, "key": "274A"}, {"mappings": {"default": {"default": "진한 여덟개의 눈물 방울 프로펠러 별표"}}, "key": "274B"}, {"mappings": {"default": {"default": "크로스 마크"}}, "key": "274C"}, {"mappings": {"default": {"default": "그림자가 있는 흰색 원"}}, "key": "274D"}], "ko/units/area.min": [{"locale": "ko"}, {"category": "other", "mappings": {"default": {"default": "제곱미터"}}, "key": "sq", "names": ["sq", "sq."]}, {"category": "area", "mappings": {"default": {"default": "제곱인치", "plural": "제곱인치"}}, "key": "sq inch", "names": ["sq in", "sq. in.", "sq inch", "sq. inch", "square inch", "square inches"]}, {"category": "area", "mappings": {"default": {"default": "제곱로드"}}, "key": "sq rd", "names": ["sq rd", "sq. rd.", "square rod", "square rods"]}, {"category": "area", "mappings": {"default": {"default": "제곱피트", "plural": "제곱피트"}}, "key": "sq ft", "names": ["sq ft", "sq. ft.", "square foot", "square feet"]}, {"category": "area", "mappings": {"default": {"default": "제곱야드"}}, "key": "sq yd", "names": ["sq yd", "sq. yd.", "square yard", "square yards"]}, {"category": "area", "mappings": {"default": {"default": "제곱마일"}}, "key": "sq mi", "names": ["sq mi", "sq. mi.", "square mile", "square miles"]}, {"category": "area", "mappings": {"default": {"default": "에이커"}}, "key": "acr", "names": ["ac", "ac.", "acr", "acr.", "acre", "acres"]}, {"category": "area", "mappings": {"default": {"default": "헥타르"}}, "key": "ha", "names": ["ha", "hectare", "hectares"]}], "ko/units/currency.min": [{"locale": "ko"}, {"category": "currency", "key": "$", "mappings": {"default": {"default": "달러"}}, "names": ["$", "💲", "＄", "﹩", "USD"]}, {"category": "currency", "key": "£", "mappings": {"default": {"default": "파운드"}}, "names": ["£", "￡", "GBP"]}, {"category": "currency", "key": "¥", "mappings": {"default": {"default": "엔"}}, "names": ["¥", "￥", "JPY"]}, {"category": "currency", "key": "€", "mappings": {"default": {"default": "유로"}}, "names": ["€", "EUR"]}, {"category": "currency", "key": "₡", "mappings": {"default": {"default": "콜론"}}, "names": ["₡", "CRC"]}, {"category": "currency", "key": "₢", "mappings": {"default": {"default": "크루제이루"}}, "names": ["₢"]}, {"category": "currency", "key": "₣", "mappings": {"default": {"default": "프랑"}}, "names": ["₣"]}, {"category": "currency", "key": "₤", "mappings": {"default": {"default": "리라"}}, "names": ["₤"]}, {"category": "currency", "key": "₥", "mappings": {"default": {"default": "밀"}}, "names": ["₥"]}, {"category": "currency", "key": "₦", "mappings": {"default": {"default": "나이라"}}, "names": ["₦", "NGN"]}, {"category": "currency", "key": "₧", "mappings": {"default": {"default": "페세타"}}, "names": ["₧"]}, {"category": "currency", "key": "₨", "mappings": {"default": {"default": "루피"}}, "names": ["₨", "₹", "INR", "NPR", "PKR", "LKR"]}, {"category": "currency", "key": "₩", "mappings": {"default": {"default": "원"}}, "names": ["₩", "￦", "KRW"]}, {"category": "currency", "key": "₪", "mappings": {"default": {"default": "세겔"}}, "names": ["₪"]}, {"category": "currency", "key": "₫", "mappings": {"default": {"default": "동"}}, "names": ["₫"]}, {"category": "currency", "key": "₭", "mappings": {"default": {"default": "낍"}}, "names": ["₭"]}, {"category": "currency", "key": "₮", "mappings": {"default": {"default": "투그릭"}}, "names": ["₮"]}, {"category": "currency", "key": "₯", "mappings": {"default": {"default": "드라크마"}}, "names": ["₯"]}, {"category": "currency", "key": "₰", "mappings": {"default": {"default": "페니히"}}, "names": ["₰"]}, {"category": "currency", "key": "₱", "mappings": {"default": {"default": "페소"}}, "names": ["₱"]}, {"category": "currency", "key": "₲", "mappings": {"default": {"default": "과라니"}}, "names": ["₲"]}, {"category": "currency", "key": "₳", "mappings": {"default": {"default": "오스트랄"}}, "names": ["₳"]}, {"category": "currency", "key": "₴", "mappings": {"default": {"default": "그리브나"}}, "names": ["₴", "UAH"]}, {"category": "currency", "key": "₵", "mappings": {"default": {"default": "세디"}}, "names": ["₵", "GHS"]}, {"category": "currency", "key": "₶", "mappings": {"default": {"default": "리브르 투르누아"}}, "names": ["₶"]}, {"category": "currency", "key": "₷", "mappings": {"default": {"default": "스페스밀로"}}, "names": ["₷"]}, {"category": "currency", "key": "₸", "mappings": {"default": {"default": "텡게"}}, "names": ["₸", "KZT"]}, {"category": "currency", "key": "₺", "mappings": {"default": {"default": "터키 리라"}}, "names": ["₺", "TRY"]}, {"category": "currency", "key": "元", "mappings": {"default": {"default": "위안"}}, "names": ["元"]}, {"category": "currency", "key": "¢", "mappings": {"default": {"default": "센트"}}, "names": ["￠", "¢"]}], "ko/units/energy.min": [{"locale": "ko"}, {"category": "energy", "si": true, "mappings": {"default": {"default": "와트"}}, "key": "W", "names": ["W", "w", "watt", "watts"]}, {"category": "energy", "mappings": {"default": {"default": "킬로와트시"}}, "key": "kwh", "names": ["kwh", "kWh", "kilowatt hour", "kilowatt hours"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "줄"}}, "key": "J", "names": ["J", "joule", "joules"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "뉴턴"}}, "key": "N", "names": ["N", "<PERSON>", "<PERSON><PERSON>"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "암페어"}}, "key": "A", "names": ["A", "ampere", "amperes"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "볼트"}}, "key": "V", "names": ["V", "volt", "volts"]}, {"category": "energy", "mappings": {"default": {"default": "옴"}}, "key": "ohm", "names": ["Ohm", "ohm", "ohms"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "옴"}}, "key": "Ω", "names": ["Ω"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "메가옴"}}, "key": "MΩ", "names": ["MΩ", "megohm", "me<PERSON><PERSON>s"]}, {"category": "energy", "si": true, "mappings": {"default": {"default": "킬로옴"}}, "key": "kΩ", "names": ["kΩ", "kilohm", "kilohms"]}], "ko/units/length.min": [{"locale": "ko"}, {"category": "length", "mappings": {"default": {"default": "피트", "plural": "피트"}}, "key": "ft", "names": ["ft", "ft.", "foot", "feet"]}, {"category": "length", "mappings": {"default": {"default": "인치", "plural": "인치"}}, "key": "in", "names": ["in", "in.", "inch", "inches"]}, {"category": "length", "mappings": {"default": {"default": "마일"}}, "key": "mi", "names": ["mi", "mi.", "mile", "miles"]}, {"category": "length", "mappings": {"default": {"default": "야드"}}, "key": "yd", "names": ["yd", "yd.", "yard", "yards"]}, {"category": "length", "mappings": {"default": {"default": "링크"}}, "key": "link", "names": ["li", "li.", "link", "links"]}, {"category": "length", "mappings": {"default": {"default": "로드"}}, "key": "rod", "names": ["rd", "rd.", "rod", "rods"]}, {"category": "length", "mappings": {"default": {"default": "체인"}}, "key": "chain", "names": ["ch", "ch.", "chain", "chains"]}, {"category": "length", "mappings": {"default": {"default": "펄롱"}}, "key": "furlong", "names": ["fur", "fur.", "furlong", "furlongs"]}, {"category": "length", "mappings": {"default": {"default": "해리"}}, "key": "n.m.", "names": ["n.m.", "nautical mile", "nautical miles"]}, {"category": "length", "si": true, "mappings": {"default": {"default": "미터"}}, "key": "m", "names": ["m", "meter", "meters"]}], "ko/units/memory.min": [{"locale": "ko"}, {"category": "memory", "mappings": {"default": {"default": "비트"}}, "key": "b", "names": ["b", "bit", "bits"]}, {"category": "memory", "si": true, "mappings": {"default": {"default": "바이트"}}, "key": "B", "names": ["B", "byte", "bytes"]}, {"category": "memory", "mappings": {"default": {"default": "킬로바이트"}}, "key": "KB", "names": ["KB", "kilobyte", "kilobytes"]}], "ko/units/other.min": [{"locale": "ko"}, {"category": "other", "mappings": {"default": {"default": "다스"}}, "key": "doz", "names": ["doz", "doz.", "dz", "dz.", "dozen"]}], "ko/units/speed.min": [{"locale": "ko"}, {"category": "speed", "mappings": {"default": {"default": "노트"}}, "key": "kt", "names": ["kt", "kt.", "knot", "knots"]}, {"category": "speed", "mappings": {"default": {"default": "마일 퍼 아워"}}, "key": "mph", "names": ["mph", "mile per hour"]}, {"category": "speed", "mappings": {"default": {"default": "레볼루션 퍼 미닛"}}, "key": "rpm", "names": ["rpm", "revolution per minute"]}, {"category": "speed", "mappings": {"default": {"default": "킬로미터 퍼 아워"}}, "key": "kmh", "names": ["kmh", "kph", "kilometer per hour"]}], "ko/units/temperature.min": [{"locale": "ko"}, {"category": "temperature", "mappings": {"default": {"default": "화씨"}}, "key": "F", "names": ["F", "F.", "°F", "Fahrenheit"]}, {"category": "temperature", "mappings": {"default": {"default": "섭씨", "alternative": "섭씨"}}, "key": "C", "names": ["C", "°C", "<PERSON><PERSON><PERSON>", "Centigrade"]}, {"category": "temperature", "mappings": {"default": {"default": "켈빈"}}, "key": "K", "names": ["K", "°K", "<PERSON><PERSON>"]}], "ko/units/time.min": [{"locale": "ko"}, {"category": "time", "si": true, "mappings": {"default": {"default": "초"}}, "key": "s", "names": ["s", "second", "seconds"]}, {"category": "time", "mappings": {"default": {"default": "초"}}, "key": "″", "names": ["″", "second", "seconds"]}, {"category": "time", "mappings": {"default": {"default": "분"}}, "key": "min", "names": ["min", "′", "minute", "minutes"]}, {"category": "time", "mappings": {"default": {"default": "도"}}, "key": "°", "names": ["°", "degree", "degrees"]}, {"category": "time", "mappings": {"default": {"default": "시간"}}, "key": "h", "names": ["h", "hr", "hour", "hours"]}], "ko/units/volume.min": [{"locale": "ko"}, {"category": "volume", "mappings": {"default": {"default": "세제곱미터"}}, "key": "cu", "names": ["cu", "cu.", "cubic", "cubics"]}, {"category": "volume", "mappings": {"default": {"default": "세제곱인치", "plural": "세제곱인치"}}, "key": "cu inch", "names": ["cu in", "cu. in.", "cubic inch", "cubic inches"]}, {"category": "volume", "mappings": {"default": {"default": "세제곱피트", "plural": "세제곱피트"}}, "key": "cu ft", "names": ["cu ft", "cu. ft.", "cubic foot", "cubic feet"]}, {"category": "volume", "mappings": {"default": {"default": "세제곱야드"}}, "key": "cu yd", "names": ["cu yd", "cu. yd.", "cubic yard", "cubic yards"]}, {"category": "volume", "mappings": {"default": {"default": "배럴"}}, "key": "bbl", "names": ["bbl.", "bbl", "barrel", "barrels"]}, {"category": "volume", "mappings": {"default": {"default": "액량 온스"}}, "key": "fl. oz.", "names": ["fl. oz.", "fl oz", "fluid ounce", "fluid ounces"]}, {"category": "volume", "mappings": {"default": {"default": "갤런"}}, "key": "gal", "names": ["gal", "gal.", "gallon", "gallons"]}, {"category": "volume", "mappings": {"default": {"default": "파인트"}}, "key": "pt", "names": ["pt", "pt.", "pint", "pints"]}, {"category": "volume", "mappings": {"default": {"default": "쿼트"}}, "key": "qt", "names": ["qt", "qt.", "quart", "quarts"]}, {"category": "volume", "mappings": {"default": {"default": "액량 드램"}}, "key": "fluid dram", "names": ["fl dr", "fl. dr.", "fluid dram", "fluid drams"]}, {"category": "volume", "mappings": {"default": {"default": "테이블스푼"}}, "key": "tbsp", "names": ["tbsp", "tbsp.", "Tbsp", "Tbsp.", "tablespoon", "tablespoons"]}, {"category": "volume", "mappings": {"default": {"default": "티스푼"}}, "key": "tsp", "names": ["tsp", "tsp.", "teaspoon", "teaspoons"]}, {"category": "volume", "mappings": {"default": {"default": "컵"}}, "key": "cup", "names": ["cp", "cp.", "cup", "cups"]}, {"category": "volume", "mappings": {"default": {"default": "세제곱센티미터"}}, "key": "cc", "names": ["cc", "ccm", "cubic centimeter", "cubic centimeters"]}, {"category": "volume", "si": true, "mappings": {"default": {"default": "리터"}}, "key": "l", "names": ["l", "liter", "liters"]}], "ko/units/weight.min": [{"locale": "ko"}, {"category": "weight", "mappings": {"default": {"default": "드램"}}, "key": "dram", "names": ["dr", "dr.", "dram", "drams"]}, {"category": "weight", "mappings": {"default": {"default": "온스"}}, "key": "oz", "names": ["oz", "oz.", "ounce", "ounces"]}, {"category": "weight", "mappings": {"default": {"default": "파운드"}}, "key": "lb", "names": ["lb", "lb.", "pound", "pounds"]}, {"category": "weight", "mappings": {"default": {"default": "스톤"}}, "key": "st", "names": ["st", "st.", "stone", "stones"]}, {"category": "weight", "mappings": {"default": {"default": "쿼터"}}, "key": "qtr", "names": ["qtr", "qtr.", "quarter", "quarters"]}, {"category": "weight", "mappings": {"default": {"default": "헌드레드웨이트"}}, "key": "cwt", "names": ["cwt", "cwt.", "hundredweight", "hundredweights"]}, {"category": "weight", "mappings": {"default": {"default": "롱톤"}}, "key": "LT", "names": ["LT", "L.T.", "long ton", "long tons"]}, {"category": "weight", "mappings": {"default": {"default": "그램"}}, "key": "gr", "names": ["gr", "gram", "grams"]}, {"category": "weight", "si": true, "mappings": {"default": {"default": "그램"}}, "key": "g", "names": ["g", "gram", "grams"]}, {"category": "weight", "mappings": {"default": {"default": "마이크로그램"}}, "key": "mcg", "names": ["mcg", "microgram", "micrograms"]}, {"category": "weight", "mappings": {"default": {"default": "톤"}}, "key": "t", "names": ["t", "T", "ton", "tons"]}], "ko/rules/clearspeak_korean.min": {"domain": "clearspeak", "locale": "ko", "modality": "speech", "inherits": "base", "rules": [["Precondition", "german-font", "default", "self::*[@font=\"fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "german-font-bold", "default", "self::*[@font=\"bold-fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "set-member-parent", "default", "self::infixop", "content/*[1][@role=\"element\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-member-parent", "SetMemberSymbol_Member", "self::infixop", "content/*[1][@role=\"element\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-element-parent", "SetMemberSymbol_Element", "self::infixop", "content/*[1][@role=\"element\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-in-parent", "SetMemberSymbol_In", "self::infixop", "content/*[1][@role=\"element\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-belongs-parent", "SetMemberSymbol_Belongs", "self::infixop", "content/*[1][@role=\"element\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-parent", "default", "self::infixop", "content/*[1][@role=\"nonelement\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-member-parent", "SetMemberSymbol_Member", "self::infixop", "content/*[1][@role=\"nonelement\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-element-parent", "SetMemberSymbol_Element", "self::infixop", "content/*[1][@role=\"nonelement\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-in-parent", "SetMemberSymbol_In", "self::infixop", "content/*[1][@role=\"nonelement\"]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-belongs-parent", "SetMemberSymbol_Belongs", "self::infixop", "content/*[1][@role=\"nonelement\"]", "contains(@annotation, \"set:intensional\")"], ["Ignore", "interval-open-inf-lr"], ["Ignore", "natural-numbers-super"], ["Ignore", "integers-super"], ["Ignore", "rational-numbers-super"], ["Ignore", "real-numbers-super"], ["Ignore", "complex-numbers-super"], ["Precondition", "quad", "default", "self::superscript", "@role!=\"unit\"", "children/*[2][text()=\"4\"]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "self::*"], ["Precondition", "quadric", "default", "self::root", "children/*[1][text()=\"4\"]"], ["Precondition", "quadric-nested", "default", "self::root", "children/*[1][text()=\"4\"]", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "unit-quad", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=4]", "name(children/*[1])=\"identifier\"", "children/*[1][@category=\"unit:length\"]"], ["Precondition", "vbar-divides", "default", "self::punctuated", "@role=\"sequence\"", "count(./children/*)=3", "children/*[2][@role=\"vbar\"]"], ["Precondition", "vbar-always-divides", "VerticalLine_Divides", "self::punctuated", "@role=\"sequence\"", "count(./children/*)=3", "children/*[2][@role=\"vbar\"]"], ["Precondition", "equality", "default", "self::relseq", "count(./children/*)=2", "text()=\"=\" or text()=\"＝\""], ["Precondition", "not-equal-to", "default", "self::relseq", "count(./children/*)=2", "text()=\"≠\""], ["Precondition", "simple-relseq", "default", "self::relseq", "count(./children/*)=2", "text()=\"<\" or text()=\">\" or text()=\"≤\" or text()=\"≥\""], ["Precondition", "simple-member-set", "default", "self::infixop", "count(./children/*)=2", "@role=\"element\""], ["Precondition", "simple-set-member", "default", "self::infixop", "count(./children/*)=2", "text()=\"∋\" or text()=\"∍\""], ["Precondition", "simple-not-member-set", "default", "self::infixop", "count(./children/*)=2", "@role=\"nonelement\""], ["Precondition", "simple-not-set-member", "default", "self::infixop", "count(./children/*)=2", "text()=\"∌\""], ["Precondition", "simple-relset", "default", "self::relseq", "count(./children/*)=2", "text()=\"⊂\" or text()=\"⊃\""], ["Precondition", "simple-parallel", "default", "self::punctuated", "count(./children/*)=3", "content/*[1][text()]=\"∥\""], ["Precondition", "simple-vertical", "default", "self::infixop", "count(./children/*)=2", "text()=\"⊥\""], ["Precondition", "simple-condition", "default", "self::relseq", "count(./children/*)=2", "text()=\"⇒\" or text()=\"⇐\" or text()=\"⇔\""], ["Precondition", "line-segment", "default", "self::overscore", "@role=\"implicit\"", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]", "name(children/*[1])=\"infixop\"", "count(./children/*[1]/children/*)=2"]]}, "ko/rules/clearspeak_korean_actions.min": {"domain": "clearspeak", "locale": "ko", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"여닫기\"; [n] . (engine:modality=summary, grammar:?collapsed)"], ["Action", "font", "[t] @font (grammar:localFont); [n] . (pause:short, grammar:ignoreFont=@font)"], ["Action", "german-font", "[t] \"프락투어\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font-bold", "[t] \"진한 프락투어\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "ellipsis", "[t] \"점 점 점\""], ["Action", "ellipsis-andsoon", "[t] \"점 점 점\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"조건\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [n] content/*[1]/children/*[2] (pause:short); [t] \"일 때\"; [t] \"빼기 같은 수식\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short); [t] \"일 때\""], ["Action", "vbar-such-that", "[t] \"다음을 만족한다\""], ["Action", "vbar-divides", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[3]; [t] \"의 약수이다\""], ["Action", "vbar-always-divides", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[3]; [t] \"의 약수이다\""], ["Action", "vbar-given", "[t] \"바\""], ["Action", "member", "[t] \"다음의 원소이다\""], ["Action", "member-member", "[t] \"다음의 원소이다\""], ["Action", "member-element", "[t] \"다음의 원소이다\""], ["Action", "member-in", "[t] \"다음에 포함된다\""], ["Action", "member-belongs", "[t] \"다음에 속한다\""], ["Action", "not-member", "[t] \"다음의 원소가 아니다\""], ["Action", "not-member-member", "[t] \"다음의 원소가 아니다\""], ["Action", "not-member-element", "[t] \"다음의 원소가 아니다\""], ["Action", "not-member-in", "[t] \"다음에 포함되지 않는다\""], ["Action", "not-member-belongs", "[t] \"다음에 속하지 않는다\""], ["Action", "set-member", ""], ["Action", "set-member-parent", "[n] children/*[2]; [t] \"의 원소인\"; [n] children/*[1]"], ["Action", "set-member-member", ""], ["Action", "set-member-member-parent", "[n] children/*[2]; [t] \"의 원소인\"; [n] children/*[1]"], ["Action", "set-member-element", ""], ["Action", "set-member-element-parent", "[n] children/*[2]; [t] \"의 원소인\"; [n] children/*[1]"], ["Action", "set-member-in", ""], ["Action", "set-member-in-parent", "[n] children/*[2]; [t] \"에 포함되는\"; [n] children/*[1]"], ["Action", "set-member-belongs", ""], ["Action", "set-member-belongs-parent", "[n] children/*[2]; [t] \"에 속하는\"; [n] children/*[1]"], ["Action", "set-not-member", ""], ["Action", "set-not-member-parent", "[n] children/*[2]; [t] \"의 원소가 아닌\"; [n] children/*[1]"], ["Action", "set-not-member-member", ""], ["Action", "set-not-member-member-parent", "[n] children/*[2]; [t] \"의 원소가 아닌\"; [n] children/*[1]"], ["Action", "set-not-member-element", ""], ["Action", "set-not-member-element-parent", "[n] children/*[2]; [t] \"의 원소가 아닌\"; [n] children/*[1]"], ["Action", "set-not-member-in", ""], ["Action", "set-not-member-in-parent", "[n] children/*[2]; [t] \"에 포함되지 않는\"; [n] children/*[1]"], ["Action", "set-not-member-belongs", ""], ["Action", "set-not-member-belongs-parent", "[n] children/*[2]; [t] \"에 속하지 않는\"; [n] children/*[1]"], ["Action", "article", ""], ["Action", "appl", "[n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"의\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"의\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1]; [t] \"의\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"의\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"의\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"역함수\"; [n] children/*[1]/children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"아크\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"아크\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"아크\"; [n] children/*[1]/children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[t] \"역함수\"; [n] children/*[1]"], ["Action", "superscript-prefix-function", "[n] children/*[2]; [t] \"제곱\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"지수시작\" (pause:short); [n] children/*[2] (pause:short); [t] \"지수끝\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\""], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-simple-function", "[n] children/*[1]; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-simple-function-none", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short); [t] \"제곱\""], ["Action", "superscript-ordinal-negative", "[n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short); [t] \"제곱\""], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"지수시작\" (pause:short); [n] children/*[2] (pause:short); [t] \"지수끝\" (pause:short)"], ["Action", "superscript-ordinal-power-number", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-ordinal-power-negative", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-ordinal-power-identifier", "[n] children/*[1]; [t] \"의\"; [n] children/*[2]; [t] \"제곱\" (pause:short)"], ["Action", "superscript-ordinal-power-default", "[n] children/*[1]; [t] \"지수시작\" (pause:short); [n] children/*[2] (pause:short); [t] \"지수끝\" (pause:short)"], ["Action", "superscript-power", "[n] children/*[1]; [t] \"의\"; [n] children/*[2] (pause:short, grammar:afterPower); [t] \"제곱\""], ["Action", "superscript-power-default", "[n] children/*[1]; [t] \"지수시작\" (pause:short); [n] children/*[2] (pause:short); [t] \"지수끝\" (pause:short)"], ["Action", "exponent", "[n] text()"], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"영\""], ["Action", "exponent-ordinalpower", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinalpower-zero", "[t] \"영\""], ["Action", "square", "[n] children/*[1]; [t] \"제곱\" (span:children/*[2])"], ["Action", "cube", "[n] children/*[1]; [t] \"세제곱\" (span:children/*[2])"], ["Action", "quad", "[n] children/*[1]; [t] \"네제곱\" (span:children/*[2])"], ["Action", "fences-points", "[t] \"좌표\"; [n] children/*[1]"], ["Action", "fences-metric", "[p] (pause:short); [t] \"메트릭\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"메트릭시작\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"메트릭끝\" (span:content/*[1], pause: short)"], ["Action", "fences-interval", "[n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"열린 구간\"; [n] children/*[1]/children/*[1]; [t] \"에서\"; [n] children/*[1]/children/*[3]; [t] \"까지\""], ["Action", "interval-closed-open", "[t] \"반 열린 구간\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"이상\"; [n] children/*[1]/children/*[3]; [t] \"미만\""], ["Action", "interval-open-closed", "[t] \"반 열린 구간\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"초과\"; [n] children/*[1]/children/*[3]; [t] \"이하\""], ["Action", "interval-closed", "[t] \"닫힌 구간\"; [n] children/*[1]/children/*[1]; [t] \"에서\"; [n] children/*[1]/children/*[3]; [t] \"까지\""], ["Action", "interval-open-inf-r", "[t] \"열린 구간\"; [n] children/*[1]/children/*[1]; [t] \"에서 무한대까지\""], ["Action", "interval-open-inf-l", "[t] \"열린 구간 음의 무한대에서\"; [n] children/*[1]/children/*[3]; [t] \"까지\""], ["Action", "interval-open-inf-lr", "[t] \"전 구간\""], ["Action", "interval-closed-open-inf", "[t] \"닫힌 구간\"; [n] children/*[1]/children/*[1]; [t] \"에서 무한대까지\""], ["Action", "interval-open-closed-inf", "[t] \"닫힌 구간 음의 무한대에서\"; [n] children/*[1]/children/*[3]; [t] \"까지\""], ["Action", "set-empty", "[t] \"공집합\""], ["Action", "set-extended", "[n] children/*[1]/children/*[3] (grammar:postposition); [t] \"를\" (grammar:article); [t] \"만족하는 모든\"; [n] children/*[1]/children/*[1]; [t] \"의 집합\""], ["Action", "set-collection", "[t] \"집합\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[n] children/*[1]/children/*[3] (grammar:postposition); [t] \"를\" (grammar:article); [t] \"만족하는\"; [n] children/*[1]/children/*[1]; [t] \"의 집합\""], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"아래첨자\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[t] \"밑이\"; [n] children/*[2]; [t] \"인\"; [n] children/*[1]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"아래첨자\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"분모가\"; [n] children/*[2] (pause:short); [t] \"이고 분자가\"; [n] children/*[1] (pause:short); [t] \"인 분수\""], ["Action", "fraction-none", "[p] (pause:short); [t] \"분모가\"; [n] children/*[2] (pause:short); [t] \"이고 분자가\"; [n] children/*[1] (pause:short); [t] \"인 분수\""], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "vulgar-fraction", "[n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short); [t] \"분수끝\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"분수\"; [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"퍼\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"분수시작\"; [p] (pause:short); [t] \"분모가\"; [n] children/*[2] (pause:short); [t] \"이고 분자가\"; [n] children/*[1] (pause:short); [t] \"인 분수끝\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"분모가\"; [n] children/*[2] (pause:short); [t] \"이고 분자가\"; [n] children/*[1] (pause:short); [t] \"인 분수\" (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"분수끝\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[2]; [t] \"분의\"; [n] children/*[1] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "sqrt", "[t] \"루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested", "[p] (pause:\"short\"); [t] \"루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "negative-sqrt", "[t] \"마이너스 루트\"; [n] children/*[1]/children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "negative-sqrt-default", "[p] (pause:\"short\"); [t] \"마이너스 루트\"; [n] children/*[1]/children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-plus-minus", "[t] \"플러스 루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:\"short\"); [t] \"플러스 루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"플러스 루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:\"short\"); [t] \"플러스 루트\"; [n] children/*[1] (pause:short, grammar:!EndRoot)"], ["Action", "sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "cubic", "[t] \"세제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"세제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "quadric", "[t] \"네제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "quadric-nested", "[p] (pause:short); [t] \"네제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root", "[n] children/*[1]; [t] \"제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root-nested", "[p] (pause:short); [n] children/*[1]; [t] \"제곱근\"; [n] children/*[2] (pause:short, grammar:!EndRoot)"], ["Action", "root-endroot", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"루트끝\" (pause:short)"], ["Action", "negative", "[t] \"마이너스\" (span:content/*[1]); [n] children/*[1]"], ["Action", "positive", "[t] \"플러스\" (span:content/*[1]); [n] children/*[1]"], ["Action", "angle-measure", "[n] content/*[1]; [n] children/*[2] (grammar:angle); [t] \"의 크기\""], ["Action", "set-prefix-operators", "[n] self::* (grammar:!prefix);"], ["Action", "division", "[n] children/*[1]; [t] \"나누기\" (span:content/*[1]); [n] children/*[2]"], ["Action", "operators-after-power", "[m] children/* (rate:\"0.5\")"], ["Action", "natural-numbers", "[t] \"자연수 집합\" (span:.)"], ["Action", "integers", "[t] \"정수 집합\" (span:.)"], ["Action", "rational-numbers", "[t] \"유리수 집합\" (span:.)"], ["Action", "real-numbers", "[t] \"실수 집합\" (span:.)"], ["Action", "complex-numbers", "[t] \"복소수 집합\" (span:.)"], ["Action", "natural-numbers-super", "[n] children/*[2] (grammar:numbers2alpha); [t] \"차원 자연수 집합\""], ["Action", "integers-super", "[n] children/*[2] (grammar:numbers2alpha); [t] \"차원 정수 집합\""], ["Action", "rational-numbers-super", "[n] children/*[2] (grammar:numbers2alpha); [t] \"차원 유리수 집합\""], ["Action", "real-numbers-super", "[n] children/*[2] (grammar:numbers2alpha); [t] \"차원 실수 집합\""], ["Action", "complex-numbers-super", "[n] children/*[2] (grammar:numbers2alpha); [t] \"차원 복소수 집합\""], ["Action", "natural-numbers-with-zero", "[t] \"영을 포함한 자연수 집합\" (span:.)"], ["Action", "positive-integers", "[t] \"양의 정수 집합\" (span:.)"], ["Action", "negative-integers", "[t] \"음의 정수 집합\" (span:.)"], ["Action", "positive-rational-numbers", "[t] \"양의 유리수 집합\" (span:.)"], ["Action", "negative-rational-numbers", "[t] \"음의 유리수 집합\" (span:.)"], ["Action", "fences-neutral", "[p] (pause:short); [t] \"절댓값\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"절댓값시작\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"절댓값끝\"  (span:content/*[1], pause: short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [n] children/*[1]; [t] \"의 기수\" (span:.); [p] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"행렬식\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "matrix", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long)"], ["Action", "matrix-simple", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"1 행 1 열의 행렬\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"행렬식\"; [t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"행렬식\"; [t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long)"], ["Action", "matrix-vector", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"-열:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] count(children/*);  [t] \"행\";[t] count(children/*[1]/children/*); [t] \"열의 행렬\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter,context:\"-열,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:EndMatrix); [t] \"행렬끝\""], ["Action", "matrix-end-vector", "[n] . (grammar:EndMatrix); [t] \"행렬끝\""], ["Action", "matrix-end-determinant", "[n] . (grammar:EndMatrix); [t] \"행렬식끝\""], ["Action", "vector", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 벡터\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 벡터\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 벡터\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"-열:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 벡터\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:EndMatrix); [t] \"행렬끝\""], ["Action", "vector-end-vector", "[n] . (grammar:EndMatrix); [t] \"벡터끝\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:EndMatrix); [t] \"벡터끝\""], ["Action", "vector-end-determinant", "[n] . (grammar:EndMatrix); [t] \"행렬식끝\""], ["Action", "binomial", "[t] \"조합\"; [n] children/*[1]/children/*[1]; [t] \"씨\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] \"총 줄 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] \"총 케이스 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"-행:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"빈 칸\""], ["Action", "blank-line", "[t] \"빈 칸\""], ["Action", "blank-cell-empty", "[t] \"빈 칸\""], ["Action", "blank-line-empty", "[t] \"빈 칸\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"케이스-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] \"총 케이스 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"케이스-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] \"총 방정식 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"방정식-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] \"총 단계 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter,context:\"-단계:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] \"총\"; [t] count(children/*); [t] \"행\";  [n] . (grammar:layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter,context:\"-행:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] \"총 조건문 수\"; [t] count(children/*); [n] . (grammar:layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"조건문-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [n] children/*[2]; [t] \"에서\"; [n] children/*[3]; [t] \"까지\""], ["Action", "limlower", "[n] children/*[2]; [t] \"에 대한\"; [n] children/*[1] (pause:short)"], ["Action", "limupper", "[n] children/*[2]; [t] \"에 대한\"; [n] children/*[1] (pause:short)"], ["Action", "integral", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [n] children/*[2]; [t] \"까지\"; [p] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [n] children/*[2]; [t] \"까지\""], ["Action", "underscript", "[t] \"조건\"; [n] children/*[2]; [t] \"에 대한\"; [n] children/*[1]; [p] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [n] children/*[2]; [t] \"에서\""], ["Action", "mixed-number", "[n] children/*[1] (grammar:postposition); [t] \"와\" (grammar:article); [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"수\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"순환소수\"; [n] children/*[1] (grammar:spaceout); [t] \"점 순환마디\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-float", "[t] \"순환소수\"; [n] children/*[1] (grammar:spaceout); [t] \"순환마디\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular", "[t] \"순환소수\"; [n] children/*[1] (grammar:spaceout); [t] \"점 순환마디\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular-float", "[t] \"순환소수\"; [n] children/*[1] (grammar:spaceout); [t] \"순환마디\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-point", "[t] \"점\""], ["Action", "line-segment", "[t] \"선분\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"켤레복소수\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"다음으로 정의된다\""], ["Action", "adorned-sign", "[n] children/*[2]; [t] \"기호가 위에 있는\"; [n] children/*[1];"], ["Action", "factorial", "[t] \"팩토리얼\""], ["Action", "left-super", "[t] \"왼쪽 위첨자\"; [n] text()"], ["Action", "left-super-list", "[t] \"왼쪽 위첨자\"; [m] children/*"], ["Action", "left-sub", "[t] \"왼쪽 아래첨자\"; [n] text()"], ["Action", "left-sub-list", "[t] \"왼쪽 아래첨자\"; [m] children/*"], ["Action", "right-super", "[t] \"오른쪽 위첨자\"; [n] text()"], ["Action", "right-super-list", "[t] \"오른쪽 위첨자\"; [m] children/*"], ["Action", "right-sub", "[t] \"오른쪽 아래첨자\"; [n] text()"], ["Action", "right-sub-list", "[t] \"오른쪽 아래첨자\"; [m] children/*"], ["Action", "choose", "[t] \"조합\"; [n] children/*[2] (grammar:combinatorics); [t] \"씨\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[t] \"순열\"; [n] children/*[2] (grammar:combinatorics); [t] \"피\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[t] \"제곱\"; [n] children/*[1]"], ["Action", "unit-cubic", "[t] \"세제곱\"; [n] children/*[1]"], ["Action", "unit-quad", "[t] \"네제곱\"; [n] children/*[1]"], ["Action", "unit-reciprocal", "[t] \"역수\"; [n] children/*[1]"], ["Action", "unit-reciprocal-singular", "[t] \"퍼\"; [n] children/*[1] (grammar:singular)"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"퍼\"; [n] children/*[2] (grammar:singular)"], ["Action", "overbar", "[n] children/*[1]; [t] \"윗줄\""], ["Action", "underbar", "[n] children/*[1]; [t] \"아랫 줄\""], ["Action", "leftbar", "[t] \"세로선\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"세로선\""], ["Action", "crossout", "[t] \"소거된\"; [n] children/*[1]"], ["Action", "crossout-end", "[t] \"소거시작\"; [n] children/*[1]; [t] \"소거끝\""], ["Action", "cancel-over", "[n] children/*[1]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거\""], ["Action", "cancel-under", "[n] children/*[2]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거\""], ["Action", "cancel-over-end", "[n] children/*[1]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거\""], ["Action", "cancel-under-end", "[n] children/*[2]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거\""], ["Action", "equality", "[n] children/*[1] (grammar:postposition); [n] content/*[1] (grammar:article:postposition); [n] children/*[2]"], ["Action", "not-equal-to", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"와\" (grammar:article); [t] \"같지 않다\""], ["Action", "simple-relseq", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2]; [t] \"보다\"; [n] content/*[1]"], ["Action", "simple-member-set", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2]; [t] \"의 원소이다\""], ["Action", "simple-set-member", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"를\" (grammar:article); [t] \"원소로 가진다\""], ["Action", "simple-not-member-set", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2]; [t] \"의 원소가 아니다\""], ["Action", "simple-not-set-member", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"를\" (grammar:article); [t] \"원소로 갖지 않는다\""], ["Action", "simple-relset", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2]; [t] \"의\"; [n] content/*[1]; [t] \"이다\""], ["Action", "simple-parallel", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[3] (grammar:postposition); [t] \"와\" (grammar:article); [t] \"평행하다\""], ["Action", "simple-vertical", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"와\" (grammar:article); [t] \"수직이다\""], ["Action", "simple-condition", "[n] children/*[1] (grammar:postposition); [t] \"는\" (grammar:article); [n] children/*[2]; [t] \"이기 위한\"; [n] content/*[1]"], ["Action", "line-segment", "[t] \"선분\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"]], "annotators": ["simple", "unit"]}, "ko/rules/mathspeak_korean.min": {"domain": "mathspeak", "locale": "ko", "modality": "speech", "inherits": "base", "rules": [["Ignore", "fraction"], ["Ignore", "fraction-brief"], ["Ignore", "fraction-sbrief"], ["Ignore", "vulgar-fraction"], ["Ignore", "root-small"], ["Ignore", "root-small-brief"], ["Ignore", "root-small-sbrief"], ["Ignore", "root"], ["Ignore", "root-brief"], ["Ignore", "root-sbrief"], ["Precondition", "german-font", "default", "self::*[@font=\"fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "german-font-bold", "default", "self::*[@font=\"bold-fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "quad", "default", "self::superscript", "children/*[2]", "children/*[2][text()=4]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "name(children/*[1])!=\"subscript\" or (name(children/*[1])=\"subscript\" and name(children/*[1]/children/*[1])=\"identifier\" and name(children/*[1]/children/*[2])=\"number\" and children/*[1]/children/*[2][@role!=\"mixed\"] and children/*[1]/children/*[2][@role!=\"othernumber\"])", "not(@embellished)"], ["Specialized", "quad", "default", "brief"], ["Specialized", "quad", "default", "sbrief"], ["<PERSON><PERSON>", "quad", "self::superscript", "children/*[2]", "children/*[2][text()=4]", "@embellished", "children/*[1][@role=\"prefix operator\"]"], ["Precondition", "currency", "default", "self::infixop", "children/*[1][@role=\"unit\"]", "children/*[1][@category=\"unit:currency\"]"], ["Precondition", "line-segment", "default", "self::overscore", "@role=\"implicit\"", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]", "name(children/*[1])=\"infixop\"", "count(./children/*[1]/children/*)=2"], ["Ignore", "equality"]]}, "ko/rules/mathspeak_korean_actions.min": {"domain": "mathspeak", "locale": "ko", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"여닫기\"; [n] . (engine:modality=summary, grammar:?collapsed)"], ["Action", "blank-cell-empty", "[t] \"빈 칸\""], ["Action", "blank-line-empty", "[t] \"빈 칸\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font", "[t] \"프락투어\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font-bold", "[t] \"진한 프락투어\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "mixed-number", "[n] children/*[1] (grammar:postposition); [t] \"와\" (grammar:article); [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"수\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"수\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"대문자묶음\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"기준\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"기준\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"기준\"; [t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "number-baseline-font-brief", "[t] \"기준\"; [t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "negative-number", "[t] \"마이너스\"; [n] children/*[1]"], ["Action", "negative", "[t] \"빼기\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"나누기\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"빼기\")"], ["Action", "fences-neutral", "[t] \"절댓값시작\"; [n] children/*[1]; [t] \"절댓값끝\""], ["Action", "fences-neutral-sbrief", "[t] \"절댓값\"; [n] children/*[1]; [t] \"절댓값끝\""], ["Action", "fences-metric", "[t] \"메트릭시작\"; [n] children/*[1]; [t] \"메트릭끝\""], ["Action", "fences-metric-sbrief", "[t] \"메트릭\"; [n] children/*[1]; [t] \"메트릭끝\""], ["Action", "empty-set", "[t] \"공집합\""], ["Action", "fences-set", "[t] \"집합시작\"; [n] children/*[1]; [t] \"집합끝\""], ["Action", "fences-set-sbrief", "[t] \"집합\"; [n] children/*[1]; [t] \"집합끝\""], ["Action", "factorial", "[t] \"팩토리얼\""], ["Action", "minus", "[t] \"빼기\""], ["Action", "fraction", "[t] CSFopenFracVerbose; [n] children/*[2]; [t] CSFoverFracVerbose; [n] children/*[1]; [t] CSFcloseFracVerbose"], ["Action", "fraction-brief", "[t] CSFopenFracBrief; [n] children/*[2]; [t] CSFoverFracVerbose; [n] children/*[1]; [t] CSFcloseFracBrief"], ["Action", "fraction-sbrief", "[t] CSFopenFracSbrief; [n] children/*[2]; [t] CSFoverFracSbrief; [n] children/*[1]; [t] CSFcloseFracSbrief"], ["Action", "vulgar-fraction", "[n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "continued-fraction-outer", "[t] \"연속분수\"; [n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "continued-fraction-outer-brief", "[t] \"연분수\"; [n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "continued-fraction-inner", "[t] \"분수시작\"; [n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "continued-fraction-inner-brief", "[t] \"분수시작\"; [n] children/*[2]; [t] \"분의\"; [n] children/*[1]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"분수\"; [n] children/*[2];[t] \"분의\"; [n] children/*[1]"], ["Action", "root-small", "[t] CSFindexRadicalVerbose; [t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-small-brief", "[t] CSFindexRadicalBrief; [t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-small-sbrief", "[t] CSFindexRadicalSbrief; [t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root", "[n] children/*[1]; [t] \"제곱근\"; [t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-brief", "[n] children/*[1]; [t] \"제곱근\"; [t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-sbrief", "[n] children/*[1]; [t] \"제곱근\"; [t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "integral", "[n] children/*[1]; [t] \"아래첨자\"; [n] children/*[2];[t] \"위첨자\"; [n] children/*[3]; [t] \"기준\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"아래\"; [n] children/*[2];[t] \"위\"; [n] children/*[3]; [t] \"기준\""], ["Action", "currency", "[m] children/*[position()>1]; [n] children/*[1];"], ["Action", "square", "[n] children/*[1]; [t] \"제곱\""], ["Action", "cube", "[n] children/*[1]; [t] \"세제곱\""], ["Action", "quad", "[n] children/*[1]; [t] \"네제곱\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"프라임\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"프라임\""], ["Action", "overscore", "[n] children/*[1]; [t] \"위에 있는\"; [n] children/*[2]"], ["Action", "overscore-brief", "[n] children/*[1]; [t] \"위에\"; [n] children/*[2]"], ["Action", "double-overscore", "[n] children/*[1]; [t] \"위에 있는\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[n] children/*[1]; [t] \"위에\"; [n] children/*[2]"], ["Action", "underscore", "[n] children/*[1]; [t] \"아래에 있는\"; [n] children/*[2]"], ["Action", "underscore-brief", "[n] children/*[1]; [t] \"아래에\"; [n] children/*[2]"], ["Action", "double-underscore", "[n] children/*[1]; [t] \"아래에 있는\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[n] children/*[1]; [t] \"아래에\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"윗줄\""], ["Action", "underbar", "[n] children/*[1]; [t] \"밑줄\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"윗물결\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"밑물결\""], ["Action", "matrix", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬시작\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"행렬끝\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"행렬끝\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFwordCounter,context:\"열\", pause:200)"], ["Action", "row-with-label", "[t] \"꼬리표시작\"; [n] content/*[1]; [t] \"꼬리표끝\" (pause:200); [m] children/* (ctxtFunc:CTFwordCounter, context:\"열\")"], ["Action", "row-with-label-brief", "[t] \"꼬리표\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFwordCounter, context:\"열\")"], ["Action", "row-with-text-label", "[t] \"꼬리표\"; [t] CSFRemoveParens;[m] children/* (ctxtFunc:CTFwordCounter, context:\"열\")"], ["Action", "empty-row", "[t] \"빈 칸\""], ["Action", "empty-cell", "[t] \"빈 칸\" (pause:300)"], ["Action", "determinant", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬식시작\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"행렬식끝\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬식\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"행렬식끝\""], ["Action", "determinant-simple", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬식시작\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"행렬식끝\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의\"; [t] \"행렬식\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행\", grammar:simpleDet); [t] \"행렬식끝\""], ["Action", "layout", "[t] \"배열시작\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"배열끝\""], ["Action", "layout-sbrief", "[t] \"배열\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"배열끝\""], ["Action", "binomial", "[t] \"이항계수시작 조합\"; [n] children/*[1]/children/*[1]; [t] \"씨\"; [n] children/*[2]/children/*[1];  [t] \"이항계수끝\""], ["Action", "binomial-sbrief", "[t] \"이항계수\"; [n] children/*[1]/children/*[1]; [t] \"씨\"; [n] children/*[2]/children/*[1];  [t] \"이항계수끝\""], ["Action", "cases", "[t] \"배열시작\"; [t] \"확장된\"; [n] content/*[1];[m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"배열끝\""], ["Action", "cases-sbrief", "[t] \"배열\"; [t] \"큰\"; [n] content/*[1];[m] children/* (ctxtFunc:CTFwordCounter, context:\"행 \"); [t] \"배열끝\""], ["Action", "line-with-label", "[t] \"꼬리표시작\"; [n] content/*[1]; [t] \"꼬리표끝\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"꼬리표\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"꼬리표\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"빈 칸\""], ["Action", "empty-line-with-label", "[t] \"꼬리표시작\"; [n] content/*[1]; [t] \"꼬리표끝\" (pause:200); [t] \"빈 칸\""], ["Action", "empty-line-with-label-brief", "[t] \"꼬리표\"; [n] content/*[1] (pause:200); [t] \"빈 칸\""], ["Action", "enclose", "[t] \"인클로즈시작\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"인클로즈끝\""], ["Action", "leftbar", "[t] \"세로선\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"세로선\""], ["Action", "crossout", "[t] \"소거시작\"; [n] children/*[1]; [t] \"소거끝\""], ["Action", "cancel", "[t] \"소거\"; [n] children/*[1]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[2] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거끝\""], ["Action", "cancel-reverse", "[t] \"소거\"; [n] children/*[2]/children/*[1] (grammar:postposition); [t] \"를\" (grammar:article); [n] children/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"소거끝\""], ["Action", "multi-inference", "[t] \"추론 규칙\"; [m] content/*; [t] \"결론은\"; [n] children/*[1]; [t] \"이고 전제는\"; [t] count(children/*[2]/children/*); [t] \"개\""], ["Action", "inference", "[t] \"추론 규칙\"; [m] content/*; [t] \"결론은\"; [n] children/*[1]; [t] \"이고 전제는\"; [t] count(children/*[2]/children/*); [t] \"개\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFwordCounter,context:\"전제 \");"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"꼬리표\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"공리\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"빈 공리\""], ["Action", "line-segment", "[t] \"선분\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "equality", "[n] children/*[1] (grammar:postposition); [n] content/*[1] (grammar:postposition:article); [n] children/*[2]"], ["Action", "line-segment", "[t] \"선분\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"]]}, "ko/rules/prefix_korean.min": {"modality": "prefix", "domain": "default", "locale": "ko", "inherits": "base", "rules": []}, "ko/rules/prefix_korean_actions.min": {"modality": "prefix", "domain": "default", "locale": "ko", "kind": "actions", "rules": [["Action", "numerator", "[t] \"분자\" (pause:200)"], ["Action", "denominator", "[t] \"분모\" (pause:200)"], ["Action", "base", "[t] \"밑\" (pause:200)"], ["Action", "exponent", "[t] \"지수\" (pause:200)"], ["Action", "subscript", "[t] \"아래첨자\" (pause:200)"], ["Action", "overscript", "[t] \"위첨자\" (pause:200)"], ["Action", "underscript", "[t] \"하단첨자\" (pause:200)"], ["Action", "radicand", "[t] \"근호 속의 수\" (pause:200)"], ["Action", "index", "[t] \"제곱근\" (pause:200)"], ["Action", "leftsub", "[t] \"왼쪽 아래첨자\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition; [t] \"왼쪽 아래첨자\" (pause:200)"], ["Action", "leftsuper", "[t] \"왼쪽 위첨자\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition; [t] \"왼쪽 위첨자\" (pause:200)"], ["Action", "rightsub", "[t] \"오른쪽 아래첨자\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition; [t] \"오른쪽 아래첨자\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"오른쪽 위첨자\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition; [t] \"오른쪽 위첨자\" (pause:200)"], ["Action", "choice", "[t] \"조합 식\" (pause:200)"], ["Action", "select", "[t] \"분할 식\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition; [t] \"행\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition; [t] \"열\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition; [t] \"열\" (pause:200)"]]}, "ko/rules/summary_korean.min": {"locale": "ko", "modality": "summary", "inherits": "base", "rules": [["Precondition", "abstr-subtraction", "default.default", "self::infixop", "@role=\"subtraction\""], ["Precondition", "abstr-subtraction-brief", "mathspeak.brief", "self::infixop", "@role=\"subtraction\""], ["Specialized", "abstr-subtraction-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-subtraction-var", "default.default", "self::infixop", "@role=\"subtraction\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-simple-root-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "following-sibling::* or ancestor::*/following-sibling::*", "CQFisSimpleIndex"], ["Precondition", "abstr-simple-root", "default.default", "self::root", "CQFisSimpleIndex"], ["Precondition", "abstr-simple-root-brief", "mathspeak.brief", "self::root", "CQFisSimpleIndex"], ["Specialized", "abstr-simple-root-brief", "mathspeak.brief", "mathspeak.sbrief", "CQFisSimpleIndex"], ["Precondition", "abstr-simple-root-nested-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root", "following-sibling::* or ancestor::*/following-sibling::*", "CQFisSimpleIndex"], ["Precondition", "abstr-simple-root-nested", "default.default", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root", "CQFisSimpleIndex"], ["Precondition", "abstr-simple-root-nested-brief", "mathspeak.brief", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root", "CQFisSimpleIndex"], ["Specialized", "abstr-simple-root-nested-brief", "mathspeak.brief", "mathspeak.sbrief", "CQFisSimpleIndex"]]}, "ko/rules/summary_korean_actions.min": {"locale": "ko", "modality": "summary", "kind": "actions", "rules": [["Action", "abstr-identifier-long", "[t] \"긴 변수\""], ["Action", "abstr-identifier", "[t] \"변수\""], ["Action", "abstr-number-long", "[t] \"긴 수\""], ["Action", "abstr-number", "[t] \"수\""], ["Action", "abstr-mixed-number-long", "[t] \"긴 대분수\""], ["Action", "abstr-mixed-number", "[t] \"대분수\""], ["Action", "abstr-text", "[t] \"문자\""], ["Action", "abstr-function", "[t] \"함수\""], ["Action", "abstr-function-brief", "[t] \"함수\""], ["Action", "abstr-lim", "[t] \"리미트\""], ["Action", "abstr-lim-brief", "[t] \"리미트\""], ["Action", "abstr-fraction", "[t] \"분수\""], ["Action", "abstr-fraction-brief", "[t] \"분수\""], ["Action", "abstr-continued-fraction", "[t] \"연분수\""], ["Action", "abstr-continued-fraction-brief", "[t] \"연분수\""], ["Action", "abstr-sqrt", "[t] \"루트\""], ["Action", "abstr-sqrt-nested", "[t] \"중첩루트\""], ["Action", "abstr-root-end", "[n] children/*[1] (engine:modality=speech); [t] \"제곱근\""], ["Action", "abstr-root", "[n] children/*[1] (engine:modality=speech); [t] \"제곱근\""], ["Action", "abstr-root-brief", "[t] \"루트\""], ["Action", "abstr-root-nested-end", "[n] children/*[1] (engine:modality=speech); [t] \"제곱근\"; [t] \"중첩루트\""], ["Action", "abstr-root-nested", "[n] children/*[1] (engine:modality=speech); [t] \"제곱근\"; [t] \"중첩루트\""], ["Action", "abstr-root-nested-brief", "[t] \"중첩루트\""], ["Action", "abstr-simple-root-end", "[t] CSFindexRadicalVerbose; [t] \"루트\""], ["Action", "abstr-simple-root", "[t] CSFindexRadicalSbrief; [t] \"루트\""], ["Action", "abstr-simple-root-brief", "[t] \"루트\""], ["Action", "abstr-simple-root-nested-end", "[t] CSFindexRadicalVerbose; [t] \"중첩루트\""], ["Action", "abstr-simple-root-nested", "[t] CSFindexRadicalSbrief; [t] \"중첩루트\""], ["Action", "abstr-simple-root-nested-brief", "[t] \"중첩루트\""], ["Action", "abstr-superscript", "[t] \"거듭제곱\""], ["Action", "abstr-subscript", "[t] \"아래첨자\""], ["Action", "abstr-subsup", "[t] \"거듭제곱과 아래첨자\""], ["Action", "abstr-infixop", "[t] \"인자가\"; [t] CSFordinalConversion; [t] \"개인\"; [t] @role (grammar:localRole)"], ["Action", "abstr-infixop-var", "[t] \"인자가 여러 개인\"; [t] @role (grammar:localRole)"], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole)"], ["Action", "abstr-addition", "[t] \"항이\"; [t] CSFordinalConversion; [t] \"개인 덧셈\""], ["Action", "abstr-addition-brief", "[t] \"합\""], ["Action", "abstr-addition-var", "[t] \"항이 여러 개인 덧셈\""], ["Action", "abstr-subtraction", "[t] \"항이\"; [t] CSFordinalConversion; [t] \"개인 뺄셈\""], ["Action", "abstr-subtraction-brief", "[t] \"차\""], ["Action", "abstr-subtraction-var", "[t] \"항이 여러 개인 뺄셈\""], ["Action", "abstr-multiplication", "[t] \"인자가\"; [t] CSFordinalConversion; [t] \"개인 곱셈\""], ["Action", "abstr-multiplication-brief", "[t] \"곱\""], ["Action", "abstr-multiplication-var", "[t] \"인자가 여러 개인 곱셈\""], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"차원 벡터\""], ["Action", "abstr-vector-brief", "[t] \"벡터\""], ["Action", "abstr-vector-var", "[t] \"n 차원 벡터\""], ["Action", "abstr-binomial", "[t] \"이항식\""], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"차원 행렬식\""], ["Action", "abstr-determinant-brief", "[t] \"행렬식\""], ["Action", "abstr-determinant-var", "[t] \"n 차원 행렬식\""], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"차원 정방행렬\""], ["Action", "abstr-squarematrix-brief", "[t] \"정방행렬\""], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"차원 행 벡터\""], ["Action", "abstr-rowvector-brief", "[t] \"행 벡터\""], ["Action", "abstr-rowvector-var", "[t] \"n 차원 행 벡터\""], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열의 행렬\""], ["Action", "abstr-matrix-brief", "[t] \"행렬\""], ["Action", "abstr-matrix-var", "[t] \"n 행 m 열의 행렬\""], ["Action", "abstr-cases", "[t] \"케이스가\"; [t] CSFordinalConversion; [t] \"개인 조건문\""], ["Action", "abstr-cases-brief", "[t] \"조건문\""], ["Action", "abstr-cases-var", "[t] \"케이스가 여러 개인 조건문\""], ["Action", "abstr-punctuated", "[t] CSFlistOrdinalConversion; [t] \"개의 원소가\"; [n] content/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"나누어진\"; [t] \"리스트\""], ["Action", "abstr-punctuated-brief", "[n] content/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"나누어진 리스트\""], ["Action", "abstr-punctuated-var", "[t] \"여러 개의 원소가\"; [n] content/*[1] (grammar:postposition); [t] \"로\" (grammar:article); [t] \"나누어진 리스트\""], ["Action", "abstr-bigop", "[n] content/*[1]"], ["Action", "abstr-integral", "[t] \"인테그럴\""], ["Action", "abstr-relation", "[t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq", "[t] CSFdecreasedOrdinalConversion; [t] \"개의 연속된\"; [t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq-brief", "[t] \"연속된\"; [t] @role (grammar:localRole)"], ["Action", "abstr-relation-var", "[t] \"여러 개의 연속된\"; [t] @role (grammar:localRole)"], ["Action", "abstr-multirel", "[t] CSFdecreasedOrdinalConversion; [t] \"개의 연속된 관계식\""], ["Action", "abstr-multirel-brief", "[t] \"연속된 관계식\""], ["Action", "abstr-multirel-var", "[t] \"여러 개의 연속된 관계식\""], ["Action", "abstr-table", "[t] count(children/*); [t] \"행\"; [t] count(children/*[1]/children/*); [t] \"열로 이루어진 표\""], ["Action", "abstr-line", "[t] \"이 다음과 같은\"; [t] @role (grammar:localRole)"], ["Action", "abstr-row", "[t] \"이 다음과 같은\"; [t] count(children/*); [t] \"열의\"; [t] @role (grammar:localRole);[t] count(preceding-sibling::..)"], ["Action", "abstr-cell", "[t] \"이 다음과 같은\"; [t] @role (grammar:localRole)"]]}}