{"version": 3, "file": "mo.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mo.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,iDAA2C;AAC3C,qDAAqD;AAErD,8CAAoD;AAMvC,QAAA,WAAW;IACtB,QAAsB,GAAG;IACzB,QAAwB,GAAG;QAC3B;AA+FF,SAAgB,aAAa,CAA+B,IAAO;IAEjE;QAAqB,2BAAI;QAgBvB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAEd;YAbM,UAAI,GAAW,IAAI,CAAC;YAYzB,KAAI,CAAC,QAAQ,GAAI,KAAI,CAAC,IAAc,CAAC,QAAQ,CAAC;;QAChD,CAAC;QAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,UAA2B;YAA3B,2BAAA,EAAA,kBAA2B;YACxD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACrB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,GAAG,MAAyB,EAAE;gBAC7C,IAAM,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;gBACZ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;aACb;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC;gBACnC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,MAAmB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;gBAC3D,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;aACZ;QACH,CAAC;QAOM,2BAAS,GAAhB,UAAiB,IAAU;YACzB,IAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,MAAmB,CAAC,CAAC;YACvD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;gBAClC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B;YACD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC;gBAAE,OAAO;YACtC,iBAAM,WAAW,YAAC,IAAI,CAAC,CAAC;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAKM,iCAAe,GAAtB;YACE,IAAM,IAAI,GAAG,cAAI,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QAMM,iCAAe,GAAtB,UAAuB,IAAiB;YAAjB,qBAAA,EAAA,WAAiB;YACtC,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,GAAG,cAAI,CAAC,KAAK,EAAE,CAAC;gBACpB,iBAAM,WAAW,YAAC,IAAI,CAAC,CAAC;aACzB;YACD,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACzE,CAAC;QAKM,4BAAU,GAAjB;YACE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBACvC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBACpF,OAAO;aACR;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC;gBAChD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;gBACnD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;gBAC9B,OAAO;aACR;YACD,iBAAM,UAAU,WAAE,CAAC;QACrB,CAAC;QAKM,4BAAU,GAAjB,UAAkB,SAAoB;YACpC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,MAAmB,EAAE;gBACvC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS,CAAC;aACvC;YACD,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC;gBAAE,OAAO,KAAK,CAAC;YAC9C,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;YAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAS,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,MAAmB,CAAC;QAC7C,CAAC;QAQM,qCAAmB,GAA1B,UAA2B,EAAY,EAAE,KAAsB;;YAAtB,sBAAA,EAAA,aAAsB;YAC7D,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,MAAmB,EAAE;gBACvC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACvB,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBACvC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC9C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBAKvD,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;gBACnD,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC/C,IAAM,CAAC,GAAG,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAIjG,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC3B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,IAAI,KAAK,CAAC,KAAK,EAAE;;wBACf,KAAgB,IAAA,KAAA,SAAA,KAAK,CAAC,KAAK,CAAA,gBAAA,4BAAE;4BAAxB,IAAM,CAAC,WAAA;4BACV,IAAI,CAAC,IAAI,CAAC,EAAE;gCACV,IAAI,UAAU,IAAI,CAAC,EAAE;oCACnB,CAAC,EAAE,CAAC;iCACL;gCACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gCAC9C,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;gCACd,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oCACjC,IAAI,CAAC,OAAO,yBAAO,IAAI,CAAC,OAAO,KAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAC,CAAC;iCACrD;gCACD,OAAO;6BACR;4BACD,CAAC,EAAE,CAAC;yBACL;;;;;;;;;iBACF;gBAKD,IAAI,KAAK,CAAC,OAAO,EAAE;oBACjB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;oBACf,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;iBACpE;qBAAM;oBACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;iBACnB;aACF;QACH,CAAC;QAOM,yBAAO,GAAd,UAAe,IAAY,EAAE,KAAa;YACxC,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1B,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACpD;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAMM,uBAAK,GAAZ,UAAa,EAAY;YACvB,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,CAAC,CAAC;YAC9B,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAA,KAAA,OAAS,EAAE,IAAA,EAAV,CAAC,QAAA,EAAE,CAAC,QAAM,CAAC;YAChB,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,CAAC;QAOM,gCAAc,GAArB,UAAsB,GAAa,EAAE,CAAS,EAAE,CAAgB;;YAC9D,IAAI,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE;gBACxC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;aACX;YACG,IAAA,KAAA,OAAY,CAAC,CAAC,GAAG,IAAA,EAAhB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAS,CAAC;YACtB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,MAAuB,EAAE;gBAC3C,KAAA,OAAS,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAA,EAAnC,CAAC,QAAA,EAAE,CAAC,QAAA,CAAgC;aACtC;iBAAM;gBACL,CAAC,GAAG,CAAC,CAAC;aACP;YACD,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC;QAQM,6BAAW,GAAlB,UAAmB,GAAa,EAAE,EAAU,EAAE,CAAgB;YAC5D,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAC5D,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAClD,IAAA,KAAA,OAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAA,EAAhC,CAAC,QAAA,EAAE,CAAC,QAA4B,CAAC;YACpC,IAAA,KAAA,OAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAA,EAAlB,CAAC,QAAA,EAAE,CAAC,QAAc,CAAC;YACxB,IAAI,SAAS,EAAE;gBAIb,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACvC,IAAI,MAAM,EAAE;oBACV,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;iBAChC;gBACD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACf;iBAAM,IAAI,MAAM,EAAE;gBAIjB,CAAC,GAAG,CAAC,CAAC;aACP;iBAAM;gBAKD,IAAA,KAAA,OAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAA,EAA/B,EAAE,QAAA,EAAE,EAAE,QAAyB,CAAC;gBACrC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aAC1B;YACD,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC;QAKM,qCAAmB,GAA1B,UAA2B,CAAS,EAAE,CAAgB;YACpD,IAAI,CAAC,CAAC,OAAO,EAAE;gBACP,IAAA,KAAA,OAAqB,CAAC,CAAC,OAAO,IAAA,EAA7B,OAAO,QAAA,EAAE,OAAO,QAAa,CAAC;gBACrC,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;gBACxD,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC;aAC3B;YACD,OAAO,CAAC,CAAC;QACX,CAAC;QAKM,4BAAU,GAAjB,UAAkB,KAAe;YAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAW,CAAC;YACzD,IAAI,MAAM,EAAE;gBACV,OAAO,IAAA,wBAAY,EAAC,MAAM,CAAC,CAAC;aAC7B;YACD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,IAAM,QAAM,GAAI,IAAI,CAAC,IAAc,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;gBACxD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACzD,IAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,IAAI,IAAI,EAAE;oBACR,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC/C;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAEH,cAAC;IAAD,CAAC,AApRM,CAAc,IAAI,GAoRvB;AAEJ,CAAC;AAxRD,sCAwRC"}