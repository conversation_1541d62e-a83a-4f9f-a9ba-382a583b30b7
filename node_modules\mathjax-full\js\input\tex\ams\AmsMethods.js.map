{"version": 3, "file": "AmsMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/ams/AmsMethods.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,iEAAwC;AACxC,uEAA8C;AAC9C,+DAAsC;AACtC,sDAA+C;AAC/C,iEAAwC;AACxC,+DAAsC;AACtC,0CAAmC;AAInC,0EAAiD;AACjD,+DAA0D;AAM7C,QAAA,UAAU,GAAgC,EAAE,CAAC;AAc1D,kBAAU,CAAC,WAAW,GAAG,UAAS,MAAiB,EAAE,KAAgB,EAC/B,QAAiB,EAAE,QAAiB,EACpC,KAAa,EAAE,OAAe,EAC9B,KAAa;IAEjD,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;IACpE,IAAM,KAAK,GAAG,wBAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7F,OAAO,sBAAS,CAAC,aAAa,CAAC,KAAkB,EAAE,IAAI,CAAC,CAAC;AAC3D,CAAC,CAAC;AAWF,kBAAU,CAAC,OAAO,GAAG,UAAS,MAAiB,EAAE,KAAgB,EACnC,QAAiB,EAAE,QAAiB;IAChE,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IAC7B,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC;IACxC,IAAI,CAAC,QAAQ,EAAE;QAEb,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;KACtD;IACD,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;IAChD,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;QAErB,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EACnB,2CAA2C,EAC3C,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;KAC9C;IACD,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5B,OAAO,KAAK,GAAG,CAAC,EAAE;QAChB,KAAK,IAAK,IAAI,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxB,KAAK,EAAE,CAAC;KACT;IACD,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,QAAQ,EAAE;QAEZ,OAAO,kBAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;KAChF;IAED,IAAI,KAAK,GAAG,kBAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACpF,OAAO,sBAAS,CAAC,aAAa,CAAC,KAAkB,EAAE,MAAM,CAAC,CAAC;AAC7D,CAAC,CAAC;AASF,kBAAU,CAAC,QAAQ,GAAG,UAAU,MAAiB,EAAE,KAAgB,EAAE,QAAiB;IAEpF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,sBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAc,CAAC;IACxF,IAAI,CAAC,QAAQ,GAAG;QACd,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,MAAM;QAClB,aAAa,EAAE,MAAM;QACrB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAC1C,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;QAC/B,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;QAC5C,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACzD,KAAK,EAAE,EAAE;QACT,2BAA2B,EAAE,IAAI;KAClC,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAUF,kBAAU,CAAC,QAAQ,GAAG,UAAS,MAAiB,EAAE,KAAgB,EAChC,QAAiB,EAAE,MAAe;IAClE,IAAI,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;IAC/D,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;QACrB,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EACpB,2CAA2C,EAC3C,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;KACxD;IACD,IAAM,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACvC,IAAM,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;IAC3D,IAAM,IAAI,GAAG,kBAAU,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAgB,CAAC;IAChH,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAcF,kBAAU,CAAC,YAAY,GAAG,UAAS,MAAiB,EAAE,KAAgB,EAAE,QAAiB,EACvD,MAAe,EAAE,MAAe,EAAE,KAAa,EAC/C,KAAa,EAAE,cAA+B;IAA/B,+BAAA,EAAA,sBAA+B;IAC9E,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,sBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC9B,KAAK,GAAG,KAAK;SACV,KAAK,CAAC,EAAE,CAAC;SACT,IAAI,CAAC,GAAG,CAAC;SACT,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;SACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3B,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CACpC,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAgB,CAAC;IACrF,IAAI,CAAC,QAAQ,GAAG;QACd,KAAK,EAAE,MAAM;QACb,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE,KAAK;QAClB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,KAAK;QACjB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;QAC/B,eAAe,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACrE,2BAA2B,EAAE,IAAI;KAClC,CAAC;IACF,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAGW,QAAA,OAAO,GAAG,iBAAiB,CAAC;AAOzC,kBAAU,CAAC,eAAe,GAAI,UAAU,MAAiB,EAAE,IAAY;IACrE,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC,IAAI,EAAE,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACzB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,IAAI,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAO,CAAgB;QAC7D,GAAG,CAAC,EAAE,EAAE,IAAI,iBAAK,CAAC,EAAE,EAAE,kBAAU,CAAC,KAAK,EAAE,CAAC,wBAAiB,IAAI,cAAI,EAAE,MAAG,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC,CAAC;AAQF,kBAAU,CAAC,kBAAkB,GAAG,UAAS,MAAiB,EAAE,IAAY;IAEtE,IAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAI9B,IAAI,EAAE,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,IAAI,GAAG,GAAG,IAAI,sBAAS,CAAC,EAAE,wBACrB,MAAM,CAAC,KAAK,CAAC,GAAG,KACnB,IAAI,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,EAChC,sBAAsB,EAAE,YAAmB,EAC3C,eAAe,EAAE,IAAI,KACpB,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IAI/B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACrB,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;KAC/C;IAID,qBAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,EAAC,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAQ,CAAC,EAAE,EAAC,CAAC,CAAC;IAI5F,IAAI,CAAC,IAAI,EAAE;QACT,IAAM,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,QAAQ,EAAE;YAC3D,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;SACd;KACF;IAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC;AAOF,kBAAU,CAAC,OAAO,GAAG,UAAU,MAAiB,EAAE,IAAY;IAItD,IAAA,KAAA,OAAwB,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAA,EAA1D,UAAU,QAAA,EAAE,OAAO,QAAuC,CAAC;IAC5D,IAAA,KAAA,OAA0B,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAA,EAA5D,WAAW,QAAA,EAAE,QAAQ,QAAuC,CAAC;IACpE,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,GAAG,GAAG,IAAI,CAAC;IAIf,IAAI,UAAU,EAAE;QAId,IAAI,OAAO,EAAE;YAMX,UAAU,CAAC,YAAY,CACrB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;gBAChC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,sBAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,EAAC,KAAK,EAAE,CAAC,EAAC,CAAC;aACjF,CAAC,EACF,qBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CACnC,CAAC;SACH;aAAM;YAIL,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAIrD,IAAI,WAAW,EAAE;gBACf,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE;oBAC3B,qBAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;oBACpE,qBAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;iBACrE,CAAC,CAAC;aACJ;YAID,qBAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;YACjD,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE;gBAC3B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC;gBACpC,qBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;gBACnE,qBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;aACpE,CAAC,CAAC;SACJ;KACF;IAID,IAAI,WAAW,IAAI,GAAG,KAAK,IAAI,EAAE;QAI/B,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,qBAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;QACpE,GAAG,GAAG,WAAW,CAAC;KACnB;IAOD,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAC,CAAC,CAAC;IAClH,IAAI,OAAO,EAAE;QACX,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KAC3B;IACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACtB,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAOF,SAAS,YAAY,CAAC,GAAY;IAC9B,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;QAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjF,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC;QAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACvE,IAAM,KAAK,GAAG,qBAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC9E,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACxB,CAAC;AAOD,SAAS,gBAAgB,CAAC,GAAY;IACpC,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAK,IAA6B,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;AACtF,CAAC;AAQD,kBAAU,CAAC,cAAc,GAAG,UAAU,MAAiB,EAAE,CAAS;IAChE,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,yBAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACrF,CAAC,CAAC;AASF,kBAAU,CAAC,aAAa,GAAG,UAAS,MAAiB,EAAE,IAAY,EAC/B,QAAgB;IAClD,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAC5B,IAAI,IAAI,KAAK,IAAI,EAAE;QAEjB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,IAAI,IAAI,KAAK,UAAU,EAAE;YACvB,IAAI,IAAI,KAAK,YAAY,EAAE;gBAEzB,QAAQ,GAAG,uBAAuB,GAAG,QAAQ,GAAG,GAAG,CAAC;aACrD;iBACI;gBAEH,QAAQ,GAAG,6BAA6B,GAAG,QAAQ,GAAG,GAAG,CAAC;aAC3D;SACF;KACF;IAED,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,CAAC,CAAC;AAWF,kBAAU,CAAC,MAAM,GAAG,UAAS,MAAiB,EAAE,IAAY,EAC/B,GAAW,EAAE,CAAS,EAAE,CAAS;IAC5D,IAAI,GAAG,GAAG,EAAC,KAAK,EAAE,GAAG,GAAG,sBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,sBAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAC,CAAC;IAClF,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC,CAAC;IACnE,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAC/B,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAQ,CAAC,GAAG,EAAC,EAAE,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,EAAC,WAAW,EAAE,CAAC,EAAC,CAAC,CAAC;IACnE,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,CAAkB,CAAC;IACxE,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IACrE,qBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACnD,qBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAClD,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,IAAI,GAAG,EAAE;QAEP,IAAI,MAAM,GAAG,IAAI,sBAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9E,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAC,MAAM,EAAE,OAAO,EAAC,CAAC,CAAC;QACpE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;QAClE,qBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACnD,qBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClD,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC5C;IAGD,qBAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC;AASF,kBAAU,CAAC,WAAW,GAAG,UAAS,MAAiB,EAAE,KAAa,EAChC,KAAa;IAC7C,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE;QAE3B,MAAM,IAAI,qBAAQ,CAAC,yBAAyB,EACxB,mCAAmC,EACnC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;KACnD;IACD,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE;QAEd,MAAM,IAAI,qBAAQ,CAAC,4BAA4B,EAC3B,2CAA2C,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;KACpF;IACD,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAClC,CAAC,CAAC;AAQF,kBAAU,CAAC,KAAK,GAAG,UAAS,MAAiB,EAAE,IAAY;IACzD,IAAI,EAAE,GAAI,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7D,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,KAAK,GAA4B;QACnC,CAAC,EAAE,6BAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,6BAAW,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;KAAC,CAAC;IACjE,IAAI,OAAO,GAAG,IAAI,sBAAS,CAAC,qBAAqB,GAAG,GAAG,GAAG,GAAG,EACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1E,IAAI,OAAO,GAAG,IAAI,sBAAS,CAAC,qBAAqB,GAAG,GAAG,GAAG,GAAG,EACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1E,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAC9D,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;IACf,IAAI,EAAE,IAAI,IAAI,EAAE;QAEd,MAAM,IAAI,qBAAQ,CAAC,cAAc,EAAE,mCAAmC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;KAC3F;IACD,IAAI,EAAE,EAAE;QAEN,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,EAAC,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAC,CAAC,CAAC;KAC9D;IAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAYF,kBAAU,CAAC,OAAO,GAAG,UAAS,MAAiB,EAAE,IAAY,EAAE,IAAY,EAC7C,KAAa,EAAE,KAAa,EAAE,KAAa;IACvE,IAAI,IAAI,IAAK,IAAI,EAAE;QACjB,IAAI,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KACrC;IACD,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KACtC;IACD,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KAClC;IACD,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,KAAK,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;KACxD;IACD,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,KAAK,KAAK,EAAE,EAAE;QAEhB,qBAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;KACrD;IACD,IAAI,IAAI,IAAI,KAAK,EAAE;QAEjB,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAC/C,IAAI,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KACtE;IACD,IAAI,KAAK,KAAK,EAAE,EAAE;QAChB,IAAI,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,UAAU,IAAI,IAAI,EAAE;YAEtB,MAAM,IAAI,qBAAQ,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;SAClF;QACD,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/C,IAAI,UAAU,KAAK,GAAG,EAAE;YAGtB,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,EAAC,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,EAAC,CAAC,CAAC;SACpE;aACI;YAGH,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,EAAC,YAAY,EAAE,KAAK;gBACjB,WAAW,EAAE,UAAU,GAAG,CAAC,EAAC,CAAC,CAAC;SAC/D;KACF;IAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AASF,kBAAU,CAAC,SAAS,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;QAEvD,MAAM,IAAI,qBAAQ,CAAC,wBAAwB,EACvB,kCAAkC,EAClC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACxD;IACD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;QAE9B,MAAM,IAAI,qBAAQ,CAAC,iBAAiB,EAAE,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;KACxE;IACD,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAC5B,IAAI,KAAK,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/B,CAAC,CAAC;AAGF,kBAAU,CAAC,WAAW,GAAG,wBAAW,CAAC,WAAW,CAAC;AAEjD,kBAAU,CAAC,SAAS,GAAG,wBAAW,CAAC,SAAS,CAAC;AAE7C,kBAAU,CAAC,KAAK,GAAG,wBAAW,CAAC,KAAK,CAAC;AAErC,kBAAU,CAAC,MAAM,GAAG,wBAAW,CAAC,MAAM,CAAC;AAEvC,kBAAU,CAAC,KAAK,GAAG,wBAAW,CAAC,KAAK,CAAC;AAErC,kBAAU,CAAC,KAAK,GAAG,wBAAW,CAAC,KAAK,CAAC;AAErC,kBAAU,CAAC,MAAM,GAAG,wBAAW,CAAC,MAAM,CAAC;AAEvC,kBAAU,CAAC,OAAO,GAAG,wBAAW,CAAC,OAAO,CAAC;AAEzC,kBAAU,CAAC,QAAQ,GAAG,wBAAW,CAAC,QAAQ,CAAC;AAE3C,kBAAU,CAAC,QAAQ,GAAG,wBAAW,CAAC,QAAQ,CAAC"}