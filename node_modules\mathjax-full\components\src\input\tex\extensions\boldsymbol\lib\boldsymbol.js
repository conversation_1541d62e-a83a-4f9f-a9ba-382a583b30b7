import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/boldsymbol/BoldsymbolConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/boldsymbol', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      boldsymbol: {
        BoldsymbolConfiguration: module1
      }
    }
  }
}});
