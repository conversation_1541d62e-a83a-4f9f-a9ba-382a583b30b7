{"version": 3, "file": "Configuration.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/Configuration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,oDAA8E;AAC9E,iDAA4C;AAC5C,8DAAwD;AAExD,oEAA8D;AAC9D,qCAAsC;AAatC;IAyHE,uBAA6B,IAAY,EACZ,OAA2B,EAC3B,QAA6B,EAC7B,KAA2B,EAC3B,IAAqB,EACrB,OAAwB,EACxB,KAAgC,EAChC,aAAiC,EACjC,cAAkC,EAClC,UAAwC,EACxC,YAA4C,EAC9C,QAAgB,EACd,MAAc;QAXd,wBAAA,EAAA,YAA2B;QAC3B,yBAAA,EAAA,aAA6B;QAC7B,sBAAA,EAAA,UAA2B;QAC3B,qBAAA,EAAA,SAAqB;QACrB,wBAAA,EAAA,YAAwB;QACxB,sBAAA,EAAA,UAAgC;QAChC,8BAAA,EAAA,kBAAiC;QACjC,+BAAA,EAAA,mBAAkC;QAClC,2BAAA,EAAA,iBAAwC;QACxC,6BAAA,EAAA,mBAA4C;QAV5C,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAoB;QAC3B,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,UAAK,GAAL,KAAK,CAAsB;QAC3B,SAAI,GAAJ,IAAI,CAAiB;QACrB,YAAO,GAAP,OAAO,CAAiB;QACxB,UAAK,GAAL,KAAK,CAA2B;QAChC,kBAAa,GAAb,aAAa,CAAoB;QACjC,mBAAc,GAAd,cAAc,CAAoB;QAClC,eAAU,GAAV,UAAU,CAA8B;QACxC,iBAAY,GAAZ,YAAY,CAAgC;QAC9C,aAAQ,GAAR,QAAQ,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;QAEzC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAC1B,EAAC,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAC,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAhIc,2BAAa,GAA5B,UAAgC,IAAuB,EAAE,QAAgB;QACvE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAQc,qBAAO,GAAtB,UAAuB,IAAY,EACZ,MAYc;QAbrC,iBAiCC;QAhCsB,uBAAA,EAAA,WAYc;QACnC,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,oCAAe,CAAC,eAAe,CAAC;QAClE,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1E,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9E,IAAI,aAAa,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,GAAG,CAClD,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAjC,CAAiC,CAAC,CAAC;QAC5C,IAAI,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,GAAG,CACpD,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAlC,CAAkC,CAAC,CAAC;QAC9C,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;QACpC,OAAO,IAAI,aAAa,CACtB,IAAI,EACJ,MAAM,CAAC,OAAO,IAAI,EAAE,EACpB,MAAM,CAAC,QAAQ,IAAI,EAAE,EACrB,MAAM,CAAC,KAAK,IAAI,EAAE,EAClB,MAAM,CAAC,IAAI,IAAI,EAAE,EACjB,MAAM,CAAC,OAAO,IAAI,EAAE,EACpB,MAAM,CAAC,KAAK,IAAI,EAAE,EAClB,aAAa,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EACnD,MAAM,CACP,CAAC;IACJ,CAAC;IAwBa,oBAAM,GAApB,UAAqB,IAAY,EACZ,MAYc;QAZd,uBAAA,EAAA,WAYc;QACjC,IAAI,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxD,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAC9C,OAAO,aAAa,CAAC;IACvB,CAAC;IAQa,mBAAK,GAAnB,UAAoB,MAYa;QAZb,uBAAA,EAAA,WAYa;QAC/B,OAAO,aAAa,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IA4BD,sBAAW,+BAAI;aAAf;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACrD,CAAC;;;OAAA;IAMD,sBAAW,iCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzD,CAAC;;;OAAA;IAEH,oBAAC;AAAD,CAAC,AA3JD,IA2JC;AA3JY,sCAAa;AA8J1B,IAAiB,oBAAoB,CAgCpC;AAhCD,WAAiB,oBAAoB;IAEnC,IAAI,IAAI,GAA+B,IAAI,GAAG,EAAE,CAAC;IAQtC,wBAAG,GAAG,UAAS,IAAY,EAAE,GAAkB;QACxD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC,CAAC;IASS,wBAAG,GAAG,UAAS,IAAY;QACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC;IAKS,yBAAI,GAAG;QAChB,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC,CAAC;AAEJ,CAAC,EAhCgB,oBAAoB,GAApB,4BAAoB,KAApB,4BAAoB,QAgCpC;AAQD;IA6DE,6BAAY,QAAuC,EAAE,OAA2B;;QAA3B,wBAAA,EAAA,WAAqB,KAAK,CAAC;QAvDtE,eAAU,GAAiB,IAAI,8BAAY,EAAE,CAAC;QAM9C,iBAAY,GAAiB,IAAI,8BAAY,EAAE,CAAC;QAMhD,mBAAc,GAAmC,IAAI,oCAAe,EAAE,CAAC;QAKvE,YAAO,GAAa,EAAE,CAAC;QAM1B,aAAQ,GAAgB,IAAI,2BAAW,EAAE,CAAC;QAM1C,UAAK,GAAoB,EAAE,CAAC;QAM5B,SAAI,GAAe,EAAE,CAAC;QAMtB,YAAO,GAAe,EAAE,CAAC;QAMzB,UAAK,GAA0B,EAAE,CAAC;QASvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;YACvB,KAAkB,IAAA,KAAA,SAAA,QAAQ,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,GAAG,WAAA;gBACZ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACtB;;;;;;;;;;YACD,KAA+C,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAA3D,IAAA,aAAkC,EAA3B,MAAM,UAAA,EAAY,QAAQ,cAAA;gBACxC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aAC/B;;;;;;;;;IACH,CAAC;IAKM,kCAAI,GAAX;QACE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAMM,oCAAM,GAAb,UAAc,GAAuB;;QACnC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;YACrC,KAAqB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAArC,IAAM,MAAM,WAAA;gBACf,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;aACnC;;;;;;;;;IACH,CAAC;IAMM,wCAAU,GAAjB,UAAkB,GAAgC;QAChD,IAAM,IAAI,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,CAAC;IAUM,iCAAG,GAAV,UAAW,IAAY,EAAE,GAAuB,EAAE,OAAwB;;QAAxB,wBAAA,EAAA,YAAwB;QACxE,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACpB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC;QAChC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;YAC7C,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,IAAI,WAAA;gBACb,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aAC3D;;;;;;;;;QACD,qBAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjC,IAAA,2BAAc,EAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAA,wBAAW,EAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC1B;IACH,CAAC;IAQS,wCAAU,GAApB,UAAqB,IAAY;QAC/B,IAAM,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACrD,MAAM,KAAK,CAAC,kBAAW,IAAI,sCAAmC,CAAC,CAAC;SACjE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAOM,oCAAM,GAAb,UAAc,MAAqB,EAAE,QAAiB;QACpD,QAAQ,GAAG,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;QACvC,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE;QACD,IAAI,MAAM,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SACvE;QACH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC7D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACtC,IAAA,2BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAOO,wCAAU,GAAlB,UAAmB,GAAuB,EAAE,MAAqB;;;YAC/D,KAA8B,IAAA,KAAA,SAAA,MAAM,CAAC,aAAa,CAAA,gBAAA,4BAAE;gBAAzC,IAAA,KAAA,mBAAe,EAAd,GAAG,QAAA,EAAE,QAAQ,QAAA;gBACvB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;aACnC;;;;;;;;;;YACD,KAA+B,IAAA,KAAA,SAAA,MAAM,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAA3C,IAAA,KAAA,mBAAgB,EAAf,IAAI,QAAA,EAAE,QAAQ,QAAA;gBACxB,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACrC;;;;;;;;;IACH,CAAC;IAEH,0BAAC;AAAD,CAAC,AA9KD,IA8KC;AA9KY,kDAAmB"}