import { OptionList } from './Options.js';
export declare type EntityList = {
    [name: string]: string;
};
export declare const options: OptionList;
export declare const entities: EntityList;
export declare function add(additions: EntityList, file: string): void;
export declare function remove(entity: string): void;
export declare function translate(text: string): string;
export declare function numeric(entity: string): string;
