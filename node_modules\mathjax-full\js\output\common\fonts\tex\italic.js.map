{"version": 3, "file": "italic.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/italic.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,MAAM,GAAyB;IACxC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACjC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACrC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;IACtB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAChC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACrC,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACzC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACzC,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACvC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1B,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACnC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;CAC7B,CAAC"}