<template>
  <div class="api-test-page">
    <h1>API 代理测试页面</h1>
    
    <div class="test-section">
      <h2>音乐 API 测试</h2>
      <button @click="testMusicApi" :disabled="musicLoading">
        {{ musicLoading ? '测试中...' : '测试音乐 API' }}
      </button>
      <div v-if="musicResult" class="result">
        <h3>结果:</h3>
        <pre>{{ musicResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>旅行 API 测试</h2>
      <button @click="testTravelApi" :disabled="travelLoading">
        {{ travelLoading ? '测试中...' : '测试旅行 API' }}
      </button>
      <div v-if="travelResult" class="result">
        <h3>结果:</h3>
        <pre>{{ travelResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>默认 API 测试</h2>
      <button @click="testDefaultApi" :disabled="defaultLoading">
        {{ defaultLoading ? '测试中...' : '测试默认 API' }}
      </button>
      <div v-if="defaultResult" class="result">
        <h3>结果:</h3>
        <pre>{{ defaultResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const musicLoading = ref(false)
const musicResult = ref('')
const travelLoading = ref(false)
const travelResult = ref('')
const defaultLoading = ref(false)
const defaultResult = ref('')

const testMusicApi = async () => {
  musicLoading.value = true
  musicResult.value = ''
  
  try {
    const response = await fetch('/api/music/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-Id': 'test-user'
      },
      body: JSON.stringify({
        conversationId: 'test-conversation',
        query: '推荐一些流行音乐',
        streaming: false
      })
    })
    
    if (response.ok) {
      const data = await response.text()
      musicResult.value = `成功: ${data}`
    } else {
      musicResult.value = `错误: ${response.status} ${response.statusText}`
    }
  } catch (error) {
    musicResult.value = `网络错误: ${error}`
  } finally {
    musicLoading.value = false
  }
}

const testTravelApi = async () => {
  travelLoading.value = true
  travelResult.value = ''
  
  try {
    const response = await fetch('/api/travel/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'test',
        password: 'test'
      })
    })
    
    if (response.ok) {
      const data = await response.text()
      travelResult.value = `成功: ${data}`
    } else {
      travelResult.value = `错误: ${response.status} ${response.statusText}`
    }
  } catch (error) {
    travelResult.value = `网络错误: ${error}`
  } finally {
    travelLoading.value = false
  }
}

const testDefaultApi = async () => {
  defaultLoading.value = true
  defaultResult.value = ''
  
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: 'test',
        session_id: 'test-session'
      })
    })
    
    if (response.ok) {
      const data = await response.text()
      defaultResult.value = `成功: ${data}`
    } else {
      defaultResult.value = `错误: ${response.status} ${response.statusText}`
    }
  } catch (error) {
    defaultResult.value = `网络错误: ${error}`
  } finally {
    defaultLoading.value = false
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #374151;
}

button {
  padding: 10px 20px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

button:hover:not(:disabled) {
  background-color: #2563eb;
}

button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.result h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #374151;
}

.result pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #1f2937;
}
</style>
