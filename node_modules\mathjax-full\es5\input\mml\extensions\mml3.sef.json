{"N": "package", "version": "10", "packageVersion": "1", "saxonVersion": "Saxon-JS 2.3", "target": "JS", "targetVersion": "2", "name": "TOP-LEVEL", "relocatable": "true", "buildDateTime": "2022-02-18T15:46:36.585-05:00", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "C": [{"N": "co", "id": "0", "binds": "0 3 2 1", "C": [{"N": "mode", "onNo": "TC", "flags": "", "patternSlots": "0", "prec": "", "C": [{"N": "templateRule", "rank": "0", "prec": "0", "seq": "52", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "524", "module": "mml3.xsl", "expand-text": "false", "match": "m:mlongdiv", "prio": "11", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "let", "var": "Q{}ms", "slot": "0", "sType": "* ", "line": "525", "role": "action", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "elem", "name": "m:mstack", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstack ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "526", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "527", "sType": "?NA nQ{}decimalpoint", "C": [{"N": "lastOf", "sType": "?NA nQ{}decimalpoint", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "527", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "axis", "name": "ancestor-or-self", "nodeTest": "*NE"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}decimalpoint"}]}]}]}]}, {"N": "choose", "sType": "*N ", "type": "item()*", "line": "528", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "529", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "left)(right"}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "530", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "531", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "532", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": ")"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "533", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "533", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "534", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "("}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "535", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "535", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "538", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "left/\\right"}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "539", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "540", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "17", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "541", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "/"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "542", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "542", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "543", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "\\"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "544", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "544", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "547", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": ":right=right"}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "548", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "549", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "549", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "550", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": ":"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "551", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "551", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "552", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "="}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "553", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "553", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}, {"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "560", "C": [{"N": "or", "C": [{"N": "or", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "<PERSON><PERSON><PERSON>"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "mediumstacked<PERSON><PERSON>"}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "shortstacked<PERSON><PERSON>"}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "stackedleftleft"}]}]}, {"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "align", "sType": "1NA ", "line": "561", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "top"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "562", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "562", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "564", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "stackedleftlinetop"}]}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "565", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "565", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}, {"N": "elem", "name": "m:msline", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msline ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "566", "C": [{"N": "att", "name": "length", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "1ADI", "C": [{"N": "fn", "name": "string-length", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "566", "C": [{"N": "fn", "name": "string", "C": [{"N": "subscript", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "567", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "568", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "569", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom right"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "570", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "570", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "573", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "573", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "576", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "righttop"}]}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "577", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "577", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}, {"N": "elem", "name": "m:msline", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msline ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "578", "C": [{"N": "att", "name": "length", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "1ADI", "C": [{"N": "fn", "name": "string-length", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "578", "C": [{"N": "fn", "name": "string", "C": [{"N": "subscript", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "579", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "580", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "580", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "581", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top left bottom"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "582", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "582", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "586", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "586", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}, {"N": "elem", "name": "m:msline", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msline ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "587", "C": [{"N": "att", "name": "length", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "1AO", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "587", "C": [{"N": "fn", "name": "string-length", "C": [{"N": "fn", "name": "string", "C": [{"N": "subscript", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}, {"N": "int", "val": "1"}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}, {"N": "elem", "name": "m:msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "588", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "589", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "52", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".2em"}]}]}]}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "590", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "voffset", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".1em"}]}, {"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-.15em"}]}, {"N": "att", "name": "depth", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-.2em"}]}, {"N": "att", "name": "height", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-.2em"}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "591", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "minsize", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "1.2em"}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": ")"}]}]}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "593", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "593", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "597", "sType": "*NE", "C": [{"N": "filter", "flags": "p", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "597", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "gc10", "op": ">", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "3"}]}]}]}]}]}]}, {"N": "choose", "sType": "* ", "type": "item()*", "line": "600", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "601", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "<PERSON><PERSON><PERSON>"}]}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "602", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "right"}]}, {"N": "applyT", "sType": "* ", "line": "603", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "varRef", "name": "Q{}ms", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "603"}]}]}]}, {"N": "elem", "name": "m:mtable", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "605", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "align", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top"}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "606", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "607", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "608", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "608", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "611", "C": [{"N": "elem", "name": "mtd", "sType": "1NE nQ{}mtd ", "nsuri": "", "namespaces": "", "line": "612", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "68", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "616", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "mediumstacked<PERSON><PERSON>"}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "617", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "varRef", "name": "Q{}ms", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "617"}]}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "618", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "left"}]}, {"N": "elem", "name": "m:mtable", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "619", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "align", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top"}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "620", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "621", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "622", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "622", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "625", "C": [{"N": "elem", "name": "mtd", "sType": "1NE nQ{}mtd ", "nsuri": "", "namespaces": "", "line": "626", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "78", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "631", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "shortstacked<PERSON><PERSON>"}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "632", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "varRef", "name": "Q{}ms", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "632"}]}, {"N": "elem", "name": "m:mtable", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "633", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "align", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top"}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "634", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "635", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "left bottom"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "636", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "636", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "639", "C": [{"N": "elem", "name": "mtd", "sType": "1NE nQ{}mtd ", "nsuri": "", "namespaces": "", "line": "640", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "87", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "644", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}longdivstyle"}, {"N": "str", "val": "stackedleftleft"}]}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mtable", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "645", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "align", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top"}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "646", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "647", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "648", "sType": "?NE", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "648", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "651", "C": [{"N": "elem", "name": "mtd", "sType": "1NE nQ{}mtd ", "nsuri": "", "namespaces": "", "line": "652", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "?NE", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "95", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}]}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "655", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "left"}]}, {"N": "applyT", "sType": "* ", "line": "656", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "varRef", "name": "Q{}ms", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "656"}]}]}]}]}, {"N": "true"}, {"N": "applyT", "sType": "* ", "line": "660", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "varRef", "name": "Q{}ms", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "660"}]}]}]}]}, {"N": "templateRule", "rank": "1", "prec": "0", "seq": "38", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "171", "module": "mml3.xsl", "expand-text": "false", "match": "m:mstack", "prio": "11", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "let", "var": "Q{}m", "slot": "0", "sType": "* ", "line": "172", "role": "action", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "elem", "name": "m:mtable", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "173", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "columnspacing", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "rowspacing", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "174", "sType": "*NA nQ{}align", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}align", "sType": "*NA nQ{}align", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "174"}]}, {"N": "let", "var": "Q{}t", "slot": "0", "sType": "*N ", "line": "175", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "applyT", "sType": "* ", "line": "176", "mode": "Q{}mstack1", "bSlot": "1", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "176"}, {"N": "<PERSON><PERSON><PERSON><PERSON>", "name": "Q{}p", "slot": "0", "sType": "1ADI", "C": [{"N": "int", "val": "0", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "177"}]}]}]}, {"N": "let", "var": "Q{}maxl", "slot": "1", "sType": "*N ", "line": "180", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "for<PERSON>ach", "sType": "* ", "line": "181", "C": [{"N": "sort", "sType": "*NA nQ{}l", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}l", "role": "select", "line": "181", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}l", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}t", "slot": "0"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}, {"N": "sortKey", "sType": "*A ", "C": [{"N": "data", "sType": "*A ", "role": "select", "C": [{"N": "dot", "sType": "1NA nQ{}l ", "role": "select"}]}, {"N": "str", "sType": "1AS ", "val": "descending", "role": "order"}, {"N": "str", "sType": "1AS ", "val": "en", "role": "lang"}, {"N": "str", "sType": "1AS ", "val": "#default", "role": "caseOrder"}, {"N": "str", "sType": "1AS ", "val": "true", "role": "stable"}, {"N": "str", "sType": "1AS ", "val": "number", "role": "dataType"}]}]}, {"N": "choose", "sType": "? ", "line": "183", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "183", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "1"}]}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "line": "184", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "dot", "sType": "1NA nQ{}l", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "184"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}]}]}, {"N": "for<PERSON>ach", "sType": "*N ", "line": "188", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "188", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}t", "slot": "0"}]}, {"N": "filter", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "or", "C": [{"N": "fn", "name": "not", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}class"}, {"N": "str", "val": "mscarries"}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "slash", "op": "/", "C": [{"N": "first", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}class"}]}, {"N": "str", "val": "mscarries"}]}]}]}]}]}]}, {"N": "let", "var": "Q{}c", "slot": "2", "sType": "*N ", "line": "189", "C": [{"N": "fn", "name": "reverse", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "189", "C": [{"N": "filter", "C": [{"N": "first", "C": [{"N": "axis", "name": "preceding-sibling", "nodeTest": "*NE"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}class"}, {"N": "str", "val": "mscarries"}]}]}]}, {"N": "sequence", "sType": "?N ", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "\n"}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "191", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "192", "sType": "*NA nQ{}class", "C": [{"N": "filter", "sType": "*NA nQ{}class", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "192", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}class"}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "dot"}, {"N": "str", "val": "msline"}]}]}]}, {"N": "let", "var": "Q{}offset", "slot": "3", "sType": "* ", "line": "193", "C": [{"N": "arith10", "op": "-", "calc": "d-d", "sType": "?AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "193", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}maxl", "slot": "1"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}, {"N": "choose", "sType": "* ", "type": "item()*", "line": "194", "C": [{"N": "and", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "195", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}class"}, {"N": "str", "val": "msline"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}, {"N": "str", "val": "*"}]}]}, {"N": "let", "var": "Q{}msl", "slot": "4", "sType": "* ", "line": "196", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "196", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}, {"N": "for<PERSON>ach", "sType": "*", "line": "197", "C": [{"N": "filter", "flags": "p", "sType": "*N", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "197", "C": [{"N": "slash", "op": "/", "C": [{"N": "root"}, {"N": "axis", "name": "descendant", "nodeTest": "*N u[NT,NP,NC,NE]"}]}, {"N": "gc10", "op": "<=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "varRef", "name": "Q{}maxl", "slot": "1"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "198", "sType": "*", "C": [{"N": "varRef", "name": "Q{}msl", "slot": "4", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "198"}]}]}]}, {"N": "varRef", "name": "Q{}c", "slot": "2", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "201"}, {"N": "let", "var": "Q{}ldiff", "slot": "4", "sType": "*NE ", "line": "202", "C": [{"N": "arith10", "op": "-", "calc": "d-d", "sType": "?AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "202", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}c", "slot": "2"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}, {"N": "let", "var": "Q{}loffset", "slot": "5", "sType": "*NE ", "line": "203", "C": [{"N": "arith10", "op": "-", "calc": "d-d", "sType": "?AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "203", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}maxl", "slot": "1"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}c", "slot": "2"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}]}]}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "for<PERSON>ach", "sType": "*NE ", "line": "204", "C": [{"N": "filter", "flags": "p", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "204", "C": [{"N": "slash", "op": "/", "C": [{"N": "root"}, {"N": "axis", "name": "descendant", "nodeTest": "*NE"}]}, {"N": "gc10", "op": "<=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "varRef", "name": "Q{}offset", "slot": "3"}]}]}, {"N": "let", "var": "Q{}pn", "slot": "6", "sType": "*NE ", "line": "205", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "205"}, {"N": "let", "var": "Q{}cy", "slot": "7", "sType": "*NE ", "line": "206", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "206", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}c", "slot": "2"}]}, {"N": "filter", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "arith10", "op": "-", "calc": "d-d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}pn", "slot": "6"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}loffset", "slot": "5"}]}]}]}]}]}]}]}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "207", "C": [{"N": "choose", "sType": "? ", "line": "208", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "line": "208", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}, {"N": "elem", "name": "m:mover", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mover ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "209", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mphantom", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mphantom ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "elem", "name": "m:mn", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mn ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0"}]}]}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-0.5width"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "210", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "210", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}]}]}]}]}, {"N": "for<PERSON>ach", "sType": "*NE ", "line": "214", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "214"}, {"N": "let", "var": "Q{}pn", "slot": "6", "sType": "*NE ", "line": "215", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "215"}, {"N": "let", "var": "Q{}cy", "slot": "7", "sType": "*NE ", "line": "216", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "216", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}c", "slot": "2"}]}, {"N": "filter", "flags": "p", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "arith10", "op": "+", "calc": "d+d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}pn", "slot": "6"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}ldiff", "slot": "4"}]}]}]}]}]}]}]}]}, {"N": "copy", "sType": "1NE ", "flags": "cin", "line": "217", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "218", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "218"}]}, {"N": "let", "var": "Q{}b", "slot": "8", "sType": "* ", "line": "219", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "*NE ", "type": "item()*", "line": "220", "C": [{"N": "fn", "name": "not", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "221", "C": [{"N": "or", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}crossout"}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}crossout"}]}]}, {"N": "str", "val": "none"}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "45"}]}, {"N": "true"}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "223", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "*NA nQ{}crossout", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}crossout", "line": "223", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}crossout", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}crossout"}]}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "48"}]}]}]}]}]}, {"N": "choose", "sType": "* ", "type": "item()*", "line": "227", "C": [{"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "228", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}none"}]}]}, {"N": "fn", "name": "not", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "51"}]}, {"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "229", "C": [{"N": "fn", "name": "not", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "n"}]}]}, {"N": "elem", "name": "m:mover", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mover ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "230", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "231", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "231"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "231", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-0.5width"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "232", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "232", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "236", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "nw"}]}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "237", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "59"}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:none", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}none ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-1width"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "63", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "239", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "s"}]}, {"N": "elem", "name": "m:munder", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}munder ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "240", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "66"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-0.5width"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "68", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "242", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "sw"}]}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "243", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "71"}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-1width"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "74", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}, {"N": "elem", "name": "m:none", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}none ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "245", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "ne"}]}, {"N": "elem", "name": "m:msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "246", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "78"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "80", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "248", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "se"}]}, {"N": "elem", "name": "m:msub", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msub ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "249", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "83"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "85", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "251", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "w"}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "elem", "name": "m:msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "252", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-1width"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "90", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "253", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "253"}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "255", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}]}, {"N": "str", "val": "e"}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "256", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "256"}]}, {"N": "elem", "name": "m:msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "257", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "97", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}cy", "slot": "7"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "260", "sType": "*", "C": [{"N": "varRef", "name": "Q{}b", "slot": "8", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "260"}]}]}]}]}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "sequence", "sType": "*NE ", "C": [{"N": "for<PERSON>ach", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "line": "267", "C": [{"N": "filter", "flags": "p", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "267", "C": [{"N": "slash", "op": "/", "C": [{"N": "root"}, {"N": "axis", "name": "descendant", "nodeTest": "*NE"}]}, {"N": "gc10", "op": "<=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "varRef", "name": "Q{}offset", "slot": "3"}]}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "268", "sType": "*NE", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "268"}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"N": "applyT", "sType": "* ", "line": "275", "mode": "Q{}ml", "bSlot": "2", "C": [{"N": "varRef", "name": "Q{}m", "slot": "0", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "275"}]}]}]}, {"N": "templateRule", "rank": "2", "prec": "0", "seq": "1", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "13", "module": "mml3.xsl", "expand-text": "false", "match": "m:*[@dir='rtl']", "prio": "10", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}*", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}*"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}dir"}, {"N": "str", "val": "rtl"}]}]}, {"N": "applyT", "sType": "* ", "line": "14", "mode": "Q{}rtl", "role": "action", "bSlot": "3", "C": [{"N": "dot", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "14"}]}]}, {"N": "templateRule", "rank": "3", "prec": "0", "seq": "0", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "7", "module": "mml3.xsl", "expand-text": "false", "match": "*", "prio": "-0.5", "matches": "NE", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "copy", "sType": "1NE ", "flags": "cin", "role": "action", "line": "8", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "9", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9"}]}, {"N": "applyT", "sType": "* ", "line": "10", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "10"}]}]}]}]}]}]}, {"N": "co", "id": "1", "binds": "1 0", "C": [{"N": "mode", "onNo": "TC", "flags": "", "patternSlots": "0", "name": "Q{}rtl", "prec": "", "C": [{"N": "templateRule", "rank": "0", "prec": "0", "seq": "27", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "144", "module": "mml3.xsl", "expand-text": "false", "match": "m:mmultiscripts[not(m:mprescripts)]", "prio": "3", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts"}, {"N": "fn", "name": "not", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts"}]}]}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "145", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "146", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "146", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "147", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "for<PERSON>ach", "sType": "* ", "line": "148", "C": [{"N": "sort", "sType": "*NE", "C": [{"N": "filter", "flags": "p", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "148", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "arith10", "op": "mod", "calc": "d%d", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "2"}]}, {"N": "int", "val": "0"}]}]}, {"N": "sortKey", "sType": "?ADI ", "C": [{"N": "first", "role": "select", "sType": "?ADI ", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "149"}]}, {"N": "str", "sType": "1AS ", "val": "descending", "role": "order"}, {"N": "str", "sType": "1AS ", "val": "en", "role": "lang"}, {"N": "str", "sType": "1AS ", "val": "#default", "role": "caseOrder"}, {"N": "str", "sType": "1AS ", "val": "true", "role": "stable"}, {"N": "str", "sType": "1AS ", "val": "number", "role": "dataType"}]}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "150", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "150"}]}, {"N": "applyT", "sType": "* ", "line": "151", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "151", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "1", "prec": "0", "seq": "26", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "128", "module": "mml3.xsl", "expand-text": "false", "match": "m:mmultiscripts", "prio": "2", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "129", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "130", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "130", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "for<PERSON>ach", "sType": "* ", "line": "131", "C": [{"N": "sort", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "131", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts"}, {"N": "filter", "flags": "p", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "arith10", "op": "mod", "calc": "d%d", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "2"}]}, {"N": "int", "val": "1"}]}]}]}]}]}, {"N": "sortKey", "sType": "?ADI ", "C": [{"N": "first", "role": "select", "sType": "?ADI ", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "132"}]}, {"N": "str", "sType": "1AS ", "val": "descending", "role": "order"}, {"N": "str", "sType": "1AS ", "val": "en", "role": "lang"}, {"N": "str", "sType": "1AS ", "val": "#default", "role": "caseOrder"}, {"N": "str", "sType": "1AS ", "val": "true", "role": "stable"}, {"N": "str", "sType": "1AS ", "val": "number", "role": "dataType"}]}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "133", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "133"}]}, {"N": "applyT", "sType": "* ", "line": "134", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "134", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}]}]}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "136", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "for<PERSON>ach", "sType": "* ", "line": "137", "C": [{"N": "sort", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "137", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts"}, {"N": "fn", "name": "reverse", "C": [{"N": "filter", "flags": "p", "C": [{"N": "filter", "flags": "p", "C": [{"N": "axis", "name": "preceding-sibling", "nodeTest": "*NE"}, {"N": "gc10", "op": "!=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "fn", "name": "last"}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "arith10", "op": "mod", "calc": "d%d", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "2"}]}, {"N": "int", "val": "0"}]}]}]}]}]}]}, {"N": "sortKey", "sType": "?ADI ", "C": [{"N": "first", "role": "select", "sType": "?ADI ", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "138"}]}, {"N": "str", "sType": "1AS ", "val": "descending", "role": "order"}, {"N": "str", "sType": "1AS ", "val": "en", "role": "lang"}, {"N": "str", "sType": "1AS ", "val": "#default", "role": "caseOrder"}, {"N": "str", "sType": "1AS ", "val": "true", "role": "stable"}, {"N": "str", "sType": "1AS ", "val": "number", "role": "dataType"}]}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "139", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "139"}]}, {"N": "applyT", "sType": "* ", "line": "140", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "140", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "2", "prec": "0", "seq": "25", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "120", "module": "mml3.xsl", "expand-text": "false", "match": "m:msubsup", "prio": "2", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msubsup", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msubsup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msubsup", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "121", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "122", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "122", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "123", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "applyT", "sType": "* ", "line": "124", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "124", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}, {"N": "applyT", "sType": "* ", "line": "125", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "125", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "3"}]}]}]}]}]}, {"N": "templateRule", "rank": "3", "prec": "0", "seq": "24", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "112", "module": "mml3.xsl", "expand-text": "false", "match": "m:msub", "prio": "2", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msub", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msub", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msub", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "113", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "114", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "114", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "115", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "applyT", "sType": "* ", "line": "116", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "116", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}, {"N": "elem", "name": "m:none", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}none ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "117", "C": [{"N": "empty", "sType": "0 "}]}]}]}]}, {"N": "templateRule", "rank": "4", "prec": "0", "seq": "23", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "104", "module": "mml3.xsl", "expand-text": "false", "match": "m:msup", "prio": "2", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msup", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mmultiscripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mmultiscripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "105", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "106", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "106", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}, {"N": "elem", "name": "m:mprescripts", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mprescripts ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "107", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:none", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}none ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "108", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "applyT", "sType": "* ", "line": "109", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "109", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}, {"N": "templateRule", "rank": "5", "prec": "0", "seq": "22", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "97", "module": "mml3.xsl", "expand-text": "false", "match": "m:mtable|m:munder|m:mover|m:munderover", "prio": "2", "matches": "NE u[NE u[NE u[NE nQ{http://www.w3.org/1998/Math/MathML}mtable,NE nQ{http://www.w3.org/1998/Math/MathML}munder],NE nQ{http://www.w3.org/1998/Math/MathML}mover],NE nQ{http://www.w3.org/1998/Math/MathML}munderover]", "C": [{"N": "p.venn", "role": "match", "op": "union", "sType": "1NE u[NE u[NE u[NE nQ{http://www.w3.org/1998/Math/MathML}mtable,NE nQ{http://www.w3.org/1998/Math/MathML}munder],NE nQ{http://www.w3.org/1998/Math/MathML}mover],NE nQ{http://www.w3.org/1998/Math/MathML}munderover]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.venn", "op": "union", "C": [{"N": "p.venn", "op": "union", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mtable"}, {"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}munder"}]}, {"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mover"}]}, {"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}munderover"}]}, {"N": "copy", "sType": "1NE u[1NE u[1NE u[1NE nQ{http://www.w3.org/1998/Math/MathML}mtable ,1NE nQ{http://www.w3.org/1998/Math/MathML}munder ] ,1NE nQ{http://www.w3.org/1998/Math/MathML}mover ] ,1NE nQ{http://www.w3.org/1998/Math/MathML}munderover ] ", "flags": "cin", "role": "action", "line": "98", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "99", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "99"}]}, {"N": "applyT", "sType": "* ", "line": "100", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "100"}]}]}]}]}, {"N": "templateRule", "rank": "6", "prec": "0", "seq": "53", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "664", "module": "mml3.xsl", "expand-text": "false", "match": "m:menclose[@notation='madruwb']", "prio": "0.5", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}menclose", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}menclose"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}notation"}, {"N": "str", "val": "madruwb"}]}]}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "665", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom right"}]}, {"N": "applyT", "sType": "* ", "line": "666", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "666"}]}]}]}]}, {"N": "templateRule", "rank": "7", "prec": "0", "seq": "36", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "163", "module": "mml3.xsl", "expand-text": "false", "match": "@notation[.='radical']", "prio": "0.5", "matches": "NA nQ{}notation", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}notation", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}notation"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "radical"}]}]}, {"N": "att", "name": "notation", "sType": "1NA ", "role": "action", "line": "164", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "top right"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "8", "prec": "0", "seq": "35", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "162", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='∋']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "∋"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "∈"}]}]}, {"N": "templateRule", "rank": "9", "prec": "0", "seq": "34", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "161", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='∈']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "∈"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "∋"}]}]}, {"N": "templateRule", "rank": "10", "prec": "0", "seq": "33", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "160", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='>']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": ">"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "<"}]}]}, {"N": "templateRule", "rank": "11", "prec": "0", "seq": "32", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "159", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='<']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "<"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": ">"}]}]}, {"N": "templateRule", "rank": "12", "prec": "0", "seq": "31", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "158", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='}']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "}"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "{"}]}]}, {"N": "templateRule", "rank": "13", "prec": "0", "seq": "30", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "157", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='{']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "{"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "}"}]}]}, {"N": "templateRule", "rank": "14", "prec": "0", "seq": "29", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "156", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.=')']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": ")"}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": "("}]}]}, {"N": "templateRule", "rank": "15", "prec": "0", "seq": "28", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "155", "module": "mml3.xsl", "expand-text": "false", "match": "text()[.='(']", "prio": "0.5", "matches": "NT", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NT", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NT"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "("}]}]}, {"N": "valueOf", "sType": "1NT ", "role": "action", "C": [{"N": "str", "sType": "1AS ", "val": ")"}]}]}, {"N": "templateRule", "rank": "16", "prec": "0", "seq": "18", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "72", "module": "mml3.xsl", "expand-text": "false", "match": "m:mfrac[@bevelled='true']", "prio": "0.5", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mfrac", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mfrac", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mfrac"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}bevelled"}, {"N": "str", "val": "true"}]}]}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "73", "C": [{"N": "sequence", "sType": "*NE ", "C": [{"N": "elem", "name": "m:msub", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msub ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "74", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "elem", "name": "m:mi", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mi ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "applyT", "sType": "* ", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "5", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}, {"N": "elem", "name": "m:mo", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mo ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "75", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "\\"}]}]}, {"N": "elem", "name": "m:msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "76", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "elem", "name": "m:mi", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mi ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "C": [{"N": "empty", "sType": "0 "}]}, {"N": "applyT", "sType": "* ", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "first", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "17", "prec": "0", "seq": "17", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "69", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.='}']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "}"}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "70", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "{"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "18", "prec": "0", "seq": "16", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "66", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.='{']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "{"}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "67", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "}"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "19", "prec": "0", "seq": "15", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "63", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.=']']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "]"}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "64", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "["}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "20", "prec": "0", "seq": "14", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "60", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.='[']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "["}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "61", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "]"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "21", "prec": "0", "seq": "13", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "57", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.=')']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": ")"}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "58", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "("}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "22", "prec": "0", "seq": "12", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "54", "module": "mml3.xsl", "expand-text": "false", "match": "@close[.='(']", "prio": "0.5", "matches": "NA nQ{}close", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}close"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "("}]}]}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "55", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": ")"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "23", "prec": "0", "seq": "10", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "48", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.='}']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "}"}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "49", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "{"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "24", "prec": "0", "seq": "9", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "45", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.='{']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "{"}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "46", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "}"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "25", "prec": "0", "seq": "8", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "42", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.=']']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "]"}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "43", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "["}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "26", "prec": "0", "seq": "7", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "39", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.='[']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "["}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "40", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "]"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "27", "prec": "0", "seq": "6", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "36", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.=')']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": ")"}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "37", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "("}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "28", "prec": "0", "seq": "5", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "33", "module": "mml3.xsl", "expand-text": "false", "match": "@open[.='(']", "prio": "0.5", "matches": "NA nQ{}open", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NA nQ{}open"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "atomSing", "diag": "1|0||gc", "card": "?", "C": [{"N": "dot"}]}, {"N": "str", "val": "("}]}]}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "34", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": ")"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "29", "prec": "0", "seq": "37", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "166", "module": "mml3.xsl", "expand-text": "false", "match": "m:mlongdiv|m:mstack", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv"}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "167", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "dir", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "ltr"}]}, {"N": "applyT", "sType": "* ", "line": "168", "mode": "#unnamed", "bSlot": "1", "C": [{"N": "dot", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mlongdiv", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "168"}]}]}]}]}, {"N": "templateRule", "rank": "30", "prec": "0", "seq": "37", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "166", "module": "mml3.xsl", "expand-text": "false", "match": "m:mlongdiv|m:mstack", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstack"}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "167", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "dir", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "ltr"}]}, {"N": "applyT", "sType": "* ", "line": "168", "mode": "#unnamed", "bSlot": "1", "C": [{"N": "dot", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstack", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "168"}]}]}]}]}, {"N": "templateRule", "rank": "31", "prec": "0", "seq": "21", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "92", "module": "mml3.xsl", "expand-text": "false", "match": "m:msqrt", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msqrt", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msqrt", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msqrt", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "93", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top right"}]}, {"N": "applyT", "sType": "* ", "line": "94", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*N u[NA,NE]", "role": "select", "line": "94", "C": [{"N": "union", "op": "|", "sType": "*N u[NA,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA"}, {"N": "first", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "32", "prec": "0", "seq": "20", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "84", "module": "mml3.xsl", "expand-text": "false", "match": "m:mroot", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mroot", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mroot", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mroot", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:msup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msup ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "85", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "86", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "top right"}]}, {"N": "applyT", "sType": "* ", "line": "87", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*N u[NA,NE]", "role": "select", "line": "87", "C": [{"N": "union", "op": "|", "sType": "*N u[NA,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA"}, {"N": "first", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}, {"N": "applyT", "sType": "* ", "line": "89", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "subscript", "flags": "p", "sType": "?NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "89", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}, {"N": "int", "val": "2"}]}]}]}]}]}, {"N": "templateRule", "rank": "33", "prec": "0", "seq": "19", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "79", "module": "mml3.xsl", "expand-text": "false", "match": "m:mfrac", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mfrac", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mfrac", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mfrac", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "copy", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mfrac ", "flags": "cin", "role": "action", "line": "80", "C": [{"N": "applyT", "sType": "* ", "line": "81", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*N u[NA,NE]", "role": "select", "line": "81", "C": [{"N": "union", "op": "|", "sType": "*N u[NA,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA"}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}, {"N": "templateRule", "rank": "34", "prec": "0", "seq": "11", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "51", "module": "mml3.xsl", "expand-text": "false", "match": "@close", "prio": "0", "matches": "NA nQ{}close", "C": [{"N": "p.nodeTest", "role": "match", "test": "NA nQ{}close", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "att", "name": "open", "sType": "1NA ", "role": "action", "line": "52", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "dot", "sType": "1NA nQ{}close", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "3"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "35", "prec": "0", "seq": "4", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "30", "module": "mml3.xsl", "expand-text": "false", "match": "@open", "prio": "0", "matches": "NA nQ{}open", "C": [{"N": "p.nodeTest", "role": "match", "test": "NA nQ{}open", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "att", "name": "close", "sType": "1NA ", "role": "action", "line": "31", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "dot", "sType": "1NA nQ{}open", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "3"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}, {"N": "templateRule", "rank": "36", "prec": "0", "seq": "3", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "20", "module": "mml3.xsl", "expand-text": "false", "match": "*", "prio": "-0.5", "matches": "NE", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "copy", "sType": "1NE ", "flags": "cin", "role": "action", "line": "21", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "applyT", "sType": "* ", "line": "22", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "22"}]}, {"N": "for<PERSON>ach", "sType": "* ", "line": "23", "C": [{"N": "sort", "sType": "*N u[NT,NP,NC,NE]", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "23"}, {"N": "sortKey", "sType": "?ADI ", "C": [{"N": "first", "role": "select", "sType": "?ADI ", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "24"}]}, {"N": "str", "sType": "1AS ", "val": "descending", "role": "order"}, {"N": "str", "sType": "1AS ", "val": "en", "role": "lang"}, {"N": "str", "sType": "1AS ", "val": "#default", "role": "caseOrder"}, {"N": "str", "sType": "1AS ", "val": "true", "role": "stable"}, {"N": "str", "sType": "1AS ", "val": "number", "role": "dataType"}]}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": " "}]}, {"N": "applyT", "sType": "* ", "line": "26", "mode": "Q{}rtl", "bSlot": "0", "C": [{"N": "dot", "sType": "1N u[N u[N u[NT,NP],NC],NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "26"}]}]}]}]}]}]}, {"N": "templateRule", "rank": "37", "prec": "0", "seq": "2", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "16", "module": "mml3.xsl", "expand-text": "false", "match": "@*", "prio": "-0.5", "matches": "NA", "C": [{"N": "p.nodeTest", "role": "match", "test": "NA", "sType": "1NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "*NA ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "17", "sType": "1NA", "C": [{"N": "dot", "sType": "1NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "17"}]}, {"N": "att", "name": "dir", "sType": "1NA ", "line": "18", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "ltr"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}]}]}]}]}, {"N": "co", "id": "2", "binds": "2", "C": [{"N": "mode", "onNo": "TC", "flags": "", "patternSlots": "0", "name": "Q{}ml", "prec": "", "C": [{"N": "templateRule", "rank": "0", "prec": "0", "seq": "42", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "313", "module": "mml3.xsl", "expand-text": "false", "match": "m:mtr[not(preceding-sibling::*)][@class='msline']", "prio": "3", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}, {"N": "fn", "name": "not", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "axis", "name": "preceding-sibling", "nodeTest": "*NE"}]}]}]}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}class"}, {"N": "str", "val": "msline"}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "314", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "315", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "315"}]}, {"N": "for<PERSON>ach", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "line": "316", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "316"}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "317", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "318", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "318"}]}, {"N": "choose", "sType": "? ", "line": "319", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mpadded", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mpadded", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "319"}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "320", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "321", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "depth", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".1em"}]}, {"N": "att", "name": "height", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "1em"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".5em"}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "322", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".2em"}]}]}]}]}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "1", "prec": "0", "seq": "43", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "330", "module": "mml3.xsl", "expand-text": "false", "match": "m:mtr[@class='msline']", "prio": "2", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}class"}, {"N": "str", "val": "msline"}]}]}, {"N": "empty", "sType": "0 ", "role": "action"}]}, {"N": "templateRule", "rank": "2", "prec": "0", "seq": "41", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "286", "module": "mml3.xsl", "expand-text": "false", "match": "m:mtr[following-sibling::*[1][@class='msline']]", "prio": "0.5", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "C": [{"N": "p.withP<PERSON><PERSON><PERSON>", "role": "match", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "p.nodeTest", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}, {"N": "filter", "C": [{"N": "first", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}, {"N": "gc", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "attVal", "name": "Q{}class"}, {"N": "str", "val": "msline"}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "287", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "288", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "288"}]}, {"N": "let", "var": "Q{}m", "slot": "0", "sType": "*NE ", "line": "289", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "role": "select", "line": "289", "C": [{"N": "slash", "op": "/", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "first", "C": [{"N": "axis", "name": "following-sibling", "nodeTest": "*NE"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd"}]}]}, {"N": "for<PERSON>ach", "sType": "*NE ", "line": "290", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "290"}, {"N": "let", "var": "Q{}p", "slot": "1", "sType": "*NE ", "line": "291", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "291"}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "292", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "293", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "293"}]}, {"N": "choose", "sType": "*NE ", "type": "item()*", "line": "294", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mpadded", "line": "295", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mpadded", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "filter", "flags": "i", "C": [{"N": "varRef", "name": "Q{}m", "slot": "0"}, {"N": "varRef", "name": "Q{}p", "slot": "1"}]}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mpadded"}]}]}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "296", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "depth", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "+.2em"}]}, {"N": "elem", "name": "m:menclose", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}menclose ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "297", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "notation", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "bottom"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "298", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "depth", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".1em"}]}, {"N": "att", "name": "height", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".8em"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".8em"}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "299", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".15em"}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "300", "sType": "*NE", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "300"}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "306", "sType": "*NE", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "306"}]}]}]}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "3", "prec": "0", "seq": "39", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "277", "module": "mml3.xsl", "expand-text": "false", "match": "m:none", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}none", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}none", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}none", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "278", "C": [{"N": "empty", "sType": "0 "}]}]}, {"N": "templateRule", "rank": "4", "prec": "0", "seq": "40", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "280", "module": "mml3.xsl", "expand-text": "false", "match": "*", "prio": "-0.5", "matches": "NE", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "copy", "sType": "1NE ", "flags": "cin", "role": "action", "line": "281", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "282", "sType": "*NA", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA", "sType": "*NA", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "282"}]}, {"N": "applyT", "sType": "* ", "line": "283", "mode": "Q{}ml", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "283"}]}]}]}]}]}]}, {"N": "co", "id": "3", "binds": "4 3 0", "C": [{"N": "mode", "onNo": "TC", "flags": "", "patternSlots": "0", "name": "Q{}mstack1", "prec": "", "C": [{"N": "templateRule", "rank": "0", "prec": "0", "seq": "49", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "481", "module": "mml3.xsl", "expand-text": "false", "match": "m:mscarries", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mscarries", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mscarries", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mscarries", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "482", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "let", "var": "Q{}align1", "slot": "1", "sType": "*NE ", "line": "483", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}stackalign", "role": "select", "line": "483", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}stackalign", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mstack"}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}stackalign"}]}]}, {"N": "let", "var": "Q{}l1", "slot": "2", "sType": "*NE ", "line": "484", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "485", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "486", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align1", "slot": "1"}]}]}, {"N": "str", "val": "left"}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0"}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "fn", "sType": "1ADI", "name": "count", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "8", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "490", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "class", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "mscarries"}]}, {"N": "att", "name": "l", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "?AO", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "sType": "?AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "490", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}l1", "slot": "2"}]}]}]}, {"N": "fn", "name": "sum", "C": [{"N": "attVal", "name": "Q{}position"}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "applyT", "sType": "* ", "line": "491", "mode": "Q{}msc", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "491"}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "1", "prec": "0", "seq": "48", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "428", "module": "mml3.xsl", "expand-text": "false", "match": "m:msline", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msline", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msline", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msline", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "429", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "let", "var": "Q{}align1", "slot": "1", "sType": "*NE ", "line": "430", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}stackalign", "role": "select", "line": "430", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}stackalign", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mstack"}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}stackalign"}]}]}, {"N": "let", "var": "Q{}align", "slot": "2", "sType": "*NE ", "line": "431", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "432", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "433", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align1", "slot": "1"}]}]}, {"N": "str", "val": ""}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "decimalpoint"}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}align1", "slot": "1", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "8"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "437", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "class", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "msline"}]}, {"N": "att", "name": "l", "sType": "1NA ", "line": "438", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "439", "C": [{"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "440", "C": [{"N": "fn", "name": "not", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length"}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length"}, {"N": "int", "val": "0"}]}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "*"}]}, {"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "441", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align", "slot": "2"}]}]}, {"N": "str", "val": "right"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align", "slot": "2"}]}]}, {"N": "str", "val": "decimalpoint"}]}]}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "arith10", "sType": "?AO", "op": "+", "calc": "d+d", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "14", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length"}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}p", "slot": "0", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "16"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "let", "var": "Q{}w", "slot": "3", "sType": "*NE ", "line": "445", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "446", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "447", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}mslinethickness"}, {"N": "str", "val": "thin"}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0.1em"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "448", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}mslinethickness"}, {"N": "str", "val": "medium"}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0.15em"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "449", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}mslinethickness"}, {"N": "str", "val": "thick"}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0.2em"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}mslinethickness", "sType": "*NA nQ{}mslinethickness", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "450"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "axis", "sType": "*NA nQ{}mslinethickness", "name": "attribute", "nodeTest": "*NA nQ{}mslinethickness", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "23"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "true"}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "0.15em"}]}]}]}, {"N": "choose", "sType": "*NE ", "type": "item()*", "line": "454", "C": [{"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "455", "C": [{"N": "fn", "name": "not", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length"}]}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length"}, {"N": "int", "val": "0"}]}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "456", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "class", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "mslinemax"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "457", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-0.2em"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "height", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "elem", "name": "m:mfrac", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mfrac ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "458", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "linethickness", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "*", "C": [{"N": "varRef", "name": "Q{}w", "slot": "3", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "458"}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "459", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".5em"}]}]}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "460", "C": [{"N": "empty", "sType": "0 "}]}]}]}]}]}]}]}, {"N": "true"}, {"N": "let", "var": "Q{}l", "slot": "4", "sType": "*NE ", "line": "466", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}length", "sType": "*NA nQ{}length", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "466"}, {"N": "for<PERSON>ach", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "line": "467", "C": [{"N": "filter", "flags": "p", "sType": "*N", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "467", "C": [{"N": "slash", "op": "/", "C": [{"N": "root"}, {"N": "axis", "name": "descendant", "nodeTest": "*N u[NT,NP,NC,NE]"}]}, {"N": "gc10", "op": "<=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "varRef", "name": "Q{}l", "slot": "4"}]}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "468", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "class", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "msline"}]}, {"N": "elem", "name": "m:mpadded", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mpadded ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "469", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "lspace", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "-0.2em"}]}, {"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "att", "name": "height", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "0em"}]}, {"N": "elem", "name": "m:mfrac", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mfrac ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "470", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "linethickness", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "*", "C": [{"N": "varRef", "name": "Q{}w", "slot": "3", "sType": "*", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "470"}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "471", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".5em"}]}]}, {"N": "elem", "name": "m:mrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mrow ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "472", "C": [{"N": "empty", "sType": "0 "}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "2", "prec": "0", "seq": "47", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "418", "module": "mml3.xsl", "expand-text": "false", "match": "m:msgroup", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msgroup", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msgroup", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msgroup", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "419", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "let", "var": "Q{}s", "slot": "1", "sType": "* ", "line": "420", "C": [{"N": "fn", "name": "number", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "420", "C": [{"N": "fn", "name": "sum", "C": [{"N": "attVal", "name": "Q{}shift"}]}]}, {"N": "let", "var": "Q{}thisp", "slot": "2", "sType": "* ", "line": "421", "C": [{"N": "fn", "name": "number", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "421", "C": [{"N": "fn", "name": "sum", "C": [{"N": "attVal", "name": "Q{}position"}]}]}, {"N": "for<PERSON>ach", "sType": "* ", "line": "422", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "422"}, {"N": "applyT", "sType": "* ", "line": "423", "mode": "Q{}mstack1", "bSlot": "1", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "423"}, {"N": "<PERSON><PERSON><PERSON><PERSON>", "name": "Q{}p", "slot": "0", "sType": "1AO", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "424", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "C": [{"N": "fn", "name": "number", "C": [{"N": "atomSing", "diag": "0|0||number", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}thisp", "slot": "2"}]}]}]}, {"N": "arith10", "op": "*", "calc": "d*d", "C": [{"N": "arith10", "op": "-", "calc": "d-d", "C": [{"N": "fn", "name": "position"}, {"N": "int", "val": "1"}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}s", "slot": "1"}]}]}]}]}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "3", "prec": "0", "seq": "46", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "376", "module": "mml3.xsl", "expand-text": "false", "match": "m:mn", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mn", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mn", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mn", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "377", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "let", "var": "Q{}align1", "slot": "1", "sType": "*NE ", "line": "378", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}stackalign", "role": "select", "line": "378", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}stackalign", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mstack"}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}stackalign"}]}]}, {"N": "let", "var": "Q{}dp1", "slot": "2", "sType": "*NE ", "line": "379", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}decimalpoint", "role": "select", "line": "379", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}decimalpoint", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "filter", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}decimalpoint"}]}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}decimalpoint"}]}]}, {"N": "let", "var": "Q{}align", "slot": "3", "sType": "*NE ", "line": "380", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "381", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "382", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align1", "slot": "1"}]}]}, {"N": "str", "val": ""}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "decimalpoint"}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}align1", "slot": "1", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "let", "var": "Q{}dp", "slot": "4", "sType": "*NE ", "line": "386", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "387", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "388", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}dp1", "slot": "2"}]}]}, {"N": "str", "val": ""}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "."}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}dp1", "slot": "2", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "14"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "392", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "l", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "$p"}]}, {"N": "let", "var": "Q{}mn", "slot": "5", "sType": "* ", "line": "393", "C": [{"N": "fn", "name": "normalize-space", "sType": "1AS", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "393", "C": [{"N": "fn", "name": "string", "C": [{"N": "dot"}]}]}, {"N": "let", "var": "Q{}len", "slot": "6", "sType": "* ", "line": "394", "C": [{"N": "fn", "name": "string-length", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "394", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}mn", "slot": "5"}]}]}]}, {"N": "sequence", "sType": "* ", "C": [{"N": "choose", "sType": "? ", "type": "item()*", "line": "395", "C": [{"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "396", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "right"}]}, {"N": "and", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "decimalpoint"}]}, {"N": "fn", "name": "not", "C": [{"N": "fn", "name": "contains", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}mn", "slot": "5"}]}]}, {"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}dp", "slot": "4"}]}]}, {"N": "str", "val": "http://www.w3.org/2005/xpath-functions/collation/codepoint"}]}]}]}]}, {"N": "att", "name": "l", "sType": "1NA ", "line": "397", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "arith10", "sType": "?AO", "op": "+", "calc": "d+d", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "21", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}len", "slot": "6"}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "399", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "center"}]}, {"N": "att", "name": "l", "sType": "1NA ", "line": "400", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "fn", "sType": "?A m[AO,AD,AF]", "name": "round", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "24", "C": [{"N": "arith10", "op": "div", "calc": "d/d", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}len", "slot": "6"}]}]}]}, {"N": "int", "val": "2"}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "402", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "decimalpoint"}]}, {"N": "att", "name": "l", "sType": "1NA ", "line": "403", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "arith10", "sType": "?AO", "op": "+", "calc": "d+d", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "27", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}, {"N": "fn", "name": "string-length", "C": [{"N": "fn", "name": "substring-before", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}mn", "slot": "5"}]}]}, {"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}dp", "slot": "4"}]}]}, {"N": "str", "val": "http://www.w3.org/2005/xpath-functions/collation/codepoint"}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}, {"N": "for<PERSON>ach", "sType": "*NE ", "line": "406", "C": [{"N": "filter", "flags": "p", "sType": "*N", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "406", "C": [{"N": "slash", "op": "/", "C": [{"N": "root"}, {"N": "axis", "name": "descendant", "nodeTest": "*N u[NT,NP,NC,NE]"}]}, {"N": "gc10", "op": "<=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "fn", "name": "position"}, {"N": "varRef", "name": "Q{}len", "slot": "6"}]}]}, {"N": "let", "var": "Q{}pos", "slot": "7", "sType": "*NE ", "line": "407", "C": [{"N": "fn", "name": "position", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "407"}, {"N": "let", "var": "Q{}digit", "slot": "8", "sType": "*NE ", "line": "408", "C": [{"N": "fn", "name": "substring", "sType": "1AS", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "408", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}mn", "slot": "5"}]}]}, {"N": "fn", "name": "number", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}pos", "slot": "7"}]}]}, {"N": "fn", "name": "number", "C": [{"N": "int", "val": "1"}]}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "409", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "choose", "sType": "? ", "line": "410", "C": [{"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "410", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}digit", "slot": "8"}, {"N": "str", "val": "."}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}digit", "slot": "8"}, {"N": "str", "val": ","}]}]}, {"N": "elem", "name": "m:mspace", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mspace ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "411", "C": [{"N": "att", "name": "width", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": ".15em"}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mn", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mn ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "413", "C": [{"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}digit", "slot": "8", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "35"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "4", "prec": "0", "seq": "45", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "341", "module": "mml3.xsl", "expand-text": "false", "match": "m:msrow", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}msrow", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}msrow", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}msrow", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "342", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "param", "name": "Q{}maxl", "slot": "1", "sType": "* ", "as": "* ", "flags": "", "line": "343", "C": [{"N": "int", "val": "0", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "343"}, {"N": "supplied", "role": "conversion", "slot": "1", "sType": "* "}]}, {"N": "let", "var": "Q{}align1", "slot": "2", "sType": "*N ", "line": "344", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}stackalign", "role": "select", "line": "344", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}stackalign", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mstack"}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}stackalign"}]}]}, {"N": "let", "var": "Q{}align", "slot": "3", "sType": "*N ", "line": "345", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "?NT ", "type": "item()*", "line": "346", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "347", "C": [{"N": "fn", "name": "string", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}align1", "slot": "2"}]}]}, {"N": "str", "val": ""}]}, {"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "decimalpoint"}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}align1", "slot": "2", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "let", "var": "Q{}row", "slot": "4", "sType": "*N ", "line": "351", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "applyT", "sType": "* ", "line": "352", "mode": "Q{}mstack1", "bSlot": "1", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "352"}, {"N": "<PERSON><PERSON><PERSON><PERSON>", "name": "Q{}p", "slot": "0", "sType": "1ADI", "C": [{"N": "int", "val": "0", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "353"}]}]}]}, {"N": "sequence", "sType": "*N ", "C": [{"N": "valueOf", "sType": "1NT ", "C": [{"N": "str", "sType": "1AS ", "val": "\n"}]}, {"N": "let", "var": "Q{}l1", "slot": "5", "sType": "*NE ", "line": "357", "C": [{"N": "doc", "sType": "1ND ", "base": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "role": "select", "C": [{"N": "choose", "sType": "*NT ", "type": "item()*", "line": "358", "C": [{"N": "and", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "359", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "decimalpoint"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mn"}]}, {"N": "for<PERSON>ach", "sType": "*NT ", "line": "360", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "role": "select", "line": "360", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtr", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}row", "slot": "4"}]}, {"N": "first", "C": [{"N": "filter", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}, {"N": "slash", "op": "/", "C": [{"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd"}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mn"}]}]}]}]}]}]}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "line": "361", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "arith10", "sType": "1AO", "op": "+", "calc": "d+d", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "361", "C": [{"N": "fn", "name": "number", "C": [{"N": "fn", "name": "sum", "C": [{"N": "attVal", "name": "Q{}l"}]}]}, {"N": "fn", "name": "count", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "axis", "name": "preceding-sibling", "nodeTest": "*NE"}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}l"}]}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}, {"N": "or", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "364", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "right"}]}, {"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "C": [{"N": "varRef", "name": "Q{}align", "slot": "3"}, {"N": "str", "val": "decimalpoint"}]}]}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "line": "365", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "fn", "sType": "1ADI", "name": "count", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "365", "C": [{"N": "doc<PERSON><PERSON>r", "C": [{"N": "slash", "op": "/", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}row", "slot": "4"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtd"}]}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "true"}, {"N": "valueOf", "flags": "l", "sType": "1NT ", "line": "368", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "int", "sType": "1ADI", "val": "0", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "368"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "372", "C": [{"N": "sequence", "sType": "*N ", "C": [{"N": "att", "name": "class", "nsuri": "", "sType": "1NA ", "C": [{"N": "str", "sType": "1AS ", "val": "msrow"}]}, {"N": "att", "name": "l", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "1AO", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "372", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "C": [{"N": "fn", "name": "number", "C": [{"N": "atomSing", "diag": "0|0||number", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}l1", "slot": "5"}]}]}]}, {"N": "fn", "name": "number", "C": [{"N": "fn", "name": "sum", "C": [{"N": "attVal", "name": "Q{}position"}]}]}]}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "373", "sType": "*NE", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "role": "select", "line": "373", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "slash", "op": "/", "C": [{"N": "treat", "as": "N", "diag": "1|0|XPTY0019|slash", "C": [{"N": "varRef", "name": "Q{}row", "slot": "4"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE nQ{http://www.w3.org/1998/Math/MathML}mtr"}]}, {"N": "axis", "name": "child", "nodeTest": "*NE"}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"N": "templateRule", "rank": "5", "prec": "0", "seq": "44", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "331", "module": "mml3.xsl", "expand-text": "false", "match": "*", "prio": "-0.5", "matches": "NE", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "sequence", "role": "action", "sType": "* ", "C": [{"N": "param", "name": "Q{}p", "slot": "0", "sType": "* ", "as": "* ", "flags": "", "line": "332", "C": [{"N": "str", "sType": "1AS ", "val": "", "role": "select"}, {"N": "supplied", "role": "conversion", "slot": "0", "sType": "* "}]}, {"N": "param", "name": "Q{}maxl", "slot": "1", "sType": "* ", "as": "* ", "flags": "", "line": "333", "C": [{"N": "int", "val": "0", "sType": "1ADI", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "333"}, {"N": "supplied", "role": "conversion", "slot": "1", "sType": "* "}]}, {"N": "elem", "name": "m:mtr", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtr ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "334", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "l", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "1AO", "C": [{"N": "arith10", "op": "+", "calc": "d+d", "sType": "1AO", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "334", "C": [{"N": "int", "val": "1"}, {"N": "atomSing", "diag": "1|1||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "varRef", "name": "Q{}p", "slot": "0"}]}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}, {"N": "choose", "sType": "? ", "line": "335", "C": [{"N": "gc10", "op": "=", "comp": "GAC|http://www.w3.org/2005/xpath-functions/collation/codepoint", "card": "1:1", "sType": "1AB", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "335", "C": [{"N": "slash", "op": "/", "C": [{"N": "fn", "name": "reverse", "C": [{"N": "first", "C": [{"N": "axis", "name": "ancestor", "nodeTest": "*NE nQ{}mstack"}]}]}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}stackalign"}]}, {"N": "str", "val": "left"}]}, {"N": "att", "name": "l", "sType": "1NA ", "line": "336", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "valueOf", "sType": "1NT ", "flags": "l", "C": [{"N": "fn", "name": "string-join", "role": "select", "C": [{"N": "first", "C": [{"N": "for<PERSON>ach", "sType": "*AS ", "C": [{"N": "data", "sType": "*A ", "C": [{"N": "mergeAdj", "C": [{"N": "varRef", "sType": "*", "name": "Q{}p", "slot": "0", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "7"}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}]}]}]}, {"N": "fn", "name": "string", "sType": "1AS ", "C": [{"N": "dot"}]}]}]}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "true"}, {"N": "empty", "sType": "0 "}]}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "338", "C": [{"N": "applyT", "sType": "* ", "mode": "#unnamed", "bSlot": "2", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "9"}]}]}]}]}]}]}]}]}, {"N": "co", "id": "4", "binds": "0", "C": [{"N": "mode", "onNo": "TC", "flags": "", "patternSlots": "0", "name": "Q{}msc", "prec": "", "C": [{"N": "templateRule", "rank": "0", "prec": "0", "seq": "51", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "509", "module": "mml3.xsl", "expand-text": "false", "match": "m:mscarry", "prio": "0", "matches": "NE nQ{http://www.w3.org/1998/Math/MathML}mscarry", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE nQ{http://www.w3.org/1998/Math/MathML}mscarry", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mscarry", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "510", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "511", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "role": "select", "line": "511", "C": [{"N": "union", "op": "|", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}crossout"}]}]}]}, {"N": "choose", "sType": "* ", "type": "item()*", "line": "512", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}scriptsizemultiplier", "line": "513", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}scriptsizemultiplier", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}scriptsizemultiplier"}]}]}, {"N": "elem", "name": "m:mstyle", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstyle ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "514", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "mathsize", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "concat", "sType": "1AS ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "?A m[AO,AD,AF]", "C": [{"N": "fn", "name": "round", "sType": "?A m[AO,AD,AF]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "514", "C": [{"N": "arith10", "op": "div", "calc": "d/d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "slash", "op": "/", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}scriptsizemultiplier"}]}]}]}, {"N": "dec", "val": "0.007"}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}, {"N": "str", "sType": "1AS ", "val": "%"}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "applyT", "sType": "* ", "line": "515", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "515"}]}]}]}, {"N": "true"}, {"N": "applyT", "sType": "* ", "line": "519", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "axis", "name": "child", "nodeTest": "*N u[NT,NP,NC,NE]", "sType": "*N u[NT,NP,NC,NE]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "519"}]}]}]}]}]}, {"N": "templateRule", "rank": "1", "prec": "0", "seq": "50", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common", "minImp": "0", "flags": "s", "baseUri": "file:///Users/<USER>/Desktop/Folders/Tumlook/Data/Code/JavaScript/MathJax/Code/Git/mathjax/MathJax-src/ts/input/mathml/mml3/mml3.xsl", "slots": "200", "line": "494", "module": "mml3.xsl", "expand-text": "false", "match": "*", "prio": "-0.5", "matches": "NE", "C": [{"N": "p.nodeTest", "role": "match", "test": "NE", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common "}, {"N": "elem", "name": "m:mtd", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mtd ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "role": "action", "line": "495", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "copyOf", "flags": "c", "ns": "xml=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ex=http://ns.saxonica.com/xslt/export", "line": "496", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "role": "select", "line": "496", "C": [{"N": "union", "op": "|", "sType": "*NA u[NA nQ{}location,NA nQ{}crossout]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "slash", "op": "/", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}location"}]}, {"N": "slash", "op": "/", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}crossout"}]}]}]}]}, {"N": "choose", "sType": "* ", "type": "item()*", "line": "497", "C": [{"N": "doc<PERSON><PERSON>r", "sType": "*NA nQ{}scriptsizemultiplier", "line": "498", "C": [{"N": "slash", "op": "/", "sType": "*NA nQ{}scriptsizemultiplier", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}scriptsizemultiplier"}]}]}, {"N": "elem", "name": "m:mstyle", "sType": "1NE nQ{http://www.w3.org/1998/Math/MathML}mstyle ", "nsuri": "http://www.w3.org/1998/Math/MathML", "namespaces": "", "line": "499", "C": [{"N": "sequence", "sType": "* ", "C": [{"N": "att", "name": "mathsize", "nsuri": "", "sType": "1NA ", "C": [{"N": "fn", "name": "concat", "sType": "1AS ", "C": [{"N": "fn", "name": "string-join", "sType": "1AS ", "C": [{"N": "first", "C": [{"N": "convert", "type": "AS*", "from": "AZ", "to": "AS", "C": [{"N": "data", "C": [{"N": "mergeAdj", "sType": "?A m[AO,AD,AF]", "C": [{"N": "fn", "name": "round", "sType": "?A m[AO,AD,AF]", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "line": "499", "C": [{"N": "arith10", "op": "div", "calc": "d/d", "C": [{"N": "atomSing", "diag": "1|0||arith", "card": "?", "C": [{"N": "first", "C": [{"N": "slash", "op": "/", "C": [{"N": "axis", "name": "parent", "nodeTest": "?N"}, {"N": "axis", "name": "attribute", "nodeTest": "*NA nQ{}scriptsizemultiplier"}]}]}]}, {"N": "dec", "val": "0.007"}]}]}]}]}]}]}, {"N": "str", "sType": "1AS ", "val": " "}]}, {"N": "str", "sType": "1AS ", "val": "%"}, {"N": "str", "sType": "1AS ", "val": ""}]}]}, {"N": "applyT", "sType": "* ", "line": "500", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "500"}]}]}]}, {"N": "true"}, {"N": "applyT", "sType": "* ", "line": "504", "mode": "#unnamed", "bSlot": "0", "C": [{"N": "dot", "sType": "1NE", "ns": "= xml=~ fn=~ xsl=~ m=http://www.w3.org/1998/Math/MathML c=http://exslt.org/common ", "role": "select", "line": "504"}]}]}]}]}]}]}]}, {"N": "overridden"}, {"N": "output", "C": [{"N": "property", "name": "Q{http://saxon.sf.net/}stylesheet-version", "value": "10"}, {"N": "property", "name": "indent", "value": "yes"}, {"N": "property", "name": "omit-xml-declaration", "value": "yes"}]}, {"N": "decimalFormat"}], "Σ": "9a493c8"}