"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.frakturBold = void 0;
exports.frakturBold = {
    0x21: [.689, .012, .349],
    0x22: [.695, -0.432, .254],
    0x26: [.696, .016, .871],
    0x27: [.695, -0.436, .25],
    0x28: [.737, .186, .459],
    0x29: [.735, .187, .459],
    0x2A: [.692, -0.449, .328],
    0x2B: [.598, .082, .893],
    0x2C: [.107, .191, .328],
    0x2D: [.275, -0.236, .893],
    0x2E: [.102, .015, .328],
    0x2F: [.721, .182, .593],
    0x30: [.501, .012, .593],
    0x31: [.489, 0, .593],
    0x32: [.491, 0, .593],
    0x33: [.487, .193, .593],
    0x34: [.495, .196, .593],
    0x35: [.481, .19, .593],
    0x36: [.704, .012, .593],
    0x37: [.479, .197, .593],
    0x38: [.714, .005, .593],
    0x39: [.487, .195, .593],
    0x3A: [.457, .012, .255],
    0x3B: [.458, .19, .255],
    0x3D: [.343, -0.168, .582],
    0x3F: [.697, .014, .428],
    0x5B: [.74, .13, .257],
    0x5D: [.738, .132, .257],
    0x5E: [.734, -0.452, .59],
    0x2018: [.708, -0.411, .254],
    0x2019: [.692, -0.394, .254],
    0x2044: [.721, .182, .593],
    0xE301: [.63, .027, .587],
    0xE302: [.693, .212, .394, { ic: .014 }],
    0xE303: [.681, .219, .387],
    0xE304: [.473, .212, .593],
    0xE305: [.684, .027, .393],
    0xE308: [.679, .22, .981],
    0xE309: [.717, .137, .727],
};
//# sourceMappingURL=fraktur-bold.js.map