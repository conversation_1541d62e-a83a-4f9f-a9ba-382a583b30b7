{"version": 3, "file": "MapHandler.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/MapHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,oEAA8D;AAC9D,8DAAwD;AASxD,IAAiB,UAAU,CAyB1B;AAzBD,WAAiB,UAAU;IAEzB,IAAI,IAAI,GAA2B,IAAI,GAAG,EAAE,CAAC;IAQlC,mBAAQ,GAAG,UAAS,GAAc;QAC3C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC,CAAC;IASS,iBAAM,GAAG,UAAS,IAAY;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC;AAEJ,CAAC,EAzBgB,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAyB1B;AAMD;IAAA;QAEU,mBAAc,GAA+B,IAAI,oCAAe,EAAa,CAAC;QAC9E,cAAS,GAAiB,IAAI,8BAAY,EAAE,CAAC;IAkHvD,CAAC;IA1GQ,wBAAG,GAAV,UAAW,IAAc,EAAE,QAAqB,EACrC,QAAkD;;QAAlD,yBAAA,EAAA,WAAmB,oCAAe,CAAC,eAAe;;YAC3D,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAAtC,IAAM,MAAI,WAAA;gBACb,IAAI,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,MAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,GAAG,EAAE;oBACR,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAI,GAAG,sBAAsB,CAAC,CAAC;oBAC5D,OAAO;iBACR;gBACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;aACxC;;;;;;;;;QACD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SACxC;IACH,CAAC;IAOM,0BAAK,GAAZ,UAAa,KAAiB;;;YAC5B,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,GAAG,gBAAA;gBACjB,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAChC,IAAI,MAAM,EAAE;oBACV,OAAO,MAAM,CAAC;iBACf;aACF;;;;;;;;;QACG,IAAA,KAAA,OAAgB,KAAK,IAAA,EAApB,GAAG,QAAA,EAAE,MAAM,QAAS,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IASM,2BAAM,GAAb,UAAiB,MAAc;QAC7B,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAyB,CAAC;QAC1D,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzC,CAAC;IAUM,6BAAQ,GAAf,UAAgB,MAAc;QAC5B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAChD,CAAC;IAMM,6BAAQ,GAAf;;QACE,IAAI,KAAK,GAAG,EAAE,CAAC;;YACf,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,GAAG,gBAAA;gBACjB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACtB;;;;;;;;;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAQM,+BAAU,GAAjB,UAAkB,MAAc;;;YAC9B,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,GAAG,gBAAA;gBACjB,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;oBACxB,OAAO,GAAG,CAAC;iBACZ;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAQM,6BAAQ,GAAf,UAAgB,IAAY;;;YAC1B,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,GAAG,gBAAA;gBACjB,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE;oBACrB,OAAO,GAAG,CAAC;iBACZ;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOO,yBAAI,GAAZ,UAAa,OAAe;QAC1B,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,OAAO,CAAC,CAAC;IAC/C,CAAC;IAEH,iBAAC;AAAD,CAAC,AArHD,IAqHC;AArHY,gCAAU;AAwHvB;IAAA;QAEU,QAAG,GAAG,IAAI,GAAG,EAA2B,CAAC;IAgEnD,CAAC;IA1DQ,yBAAG,GAAV,UAAW,QAAuB,EAAE,SAAyB,EAClD,QAAkD;;QAAlD,yBAAA,EAAA,WAAmB,oCAAe,CAAC,eAAe;;YAC3D,KAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA,gBAAA,4BAAE;gBAApC,IAAM,GAAG,WAAA;gBACZ,IAAI,MAAI,GAAG,GAAkB,CAAC;gBAC9B,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC;gBAChC,IAAI,CAAC,UAAU,EAAE;oBACf,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC9B,IAAI,CAAC,GAAG,CAAC,MAAI,EAAE,UAAU,CAAC,CAAC;iBAC5B;gBACD,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAI,CAAC,EAAE,SAAS,CAAC,MAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;aAC3D;;;;;;;;;IACH,CAAC;IAQM,yBAAG,GAAV,UAAW,IAAiB,EAAE,UAAsB;QAClD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACjC,CAAC;IAQM,yBAAG,GAAV,UAAW,IAAiB;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAQM,8BAAQ,GAAf,UAAgB,IAAY;;;YAC1B,KAAsB,IAAA,KAAA,SAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAApC,IAAM,OAAO,WAAA;gBAChB,IAAI,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,GAAG,EAAE;oBACP,OAAO,GAAG,CAAC;iBACZ;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,0BAAI,GAAX;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEH,kBAAC;AAAD,CAAC,AAlED,IAkEC;AAlEY,kCAAW"}