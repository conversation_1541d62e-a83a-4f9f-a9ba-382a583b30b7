{"version": 3, "file": "FontData.js", "sourceRoot": "", "sources": ["../../../ts/output/svg/FontData.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,qDAA2G;AAC3G,wDAAsC;AAqCtC;IAAiC,+BAA0D;IAA3F;;IAsBA,CAAC;IAJe,uBAAW,GAAzB,UAA0B,IAAgB,EAAE,CAAS;QACnD,OAAO,OAAM,WAAW,YAAC,IAAI,EAAE,CAAC,CAAmB,CAAC;IACtD,CAAC;IAfa,mBAAO,yBAChB,sBAAQ,CAAC,OAAO,KACnB,aAAa,EAAE,oBAAoB,IACnC;IAKY,eAAG,GAAG,KAAK,CAAC;IAS5B,kBAAC;CAAA,AAtBD,CAAiC,sBAAQ,GAsBxC;AAtBY,kCAAW;AAkCxB,SAAgB,QAAQ,CAAC,IAAgB,EAAE,KAAoB,EAAE,OAAsB;;;QACrF,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,gBAAA,4BAAE;YAA/B,IAAM,CAAC,WAAA;YACV,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtB,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;SAC/C;;;;;;;;;;QACD,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,gBAAA,4BAAE;YAAjC,IAAM,CAAC,WAAA;YACV,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtB,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;SACjD;;;;;;;;;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAVD,4BAUC"}