import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/cancel/CancelConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/cancel', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      cancel: {
        CancelConfiguration: module1
      }
    }
  }
}});
