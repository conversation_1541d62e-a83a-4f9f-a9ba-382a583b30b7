import axios from 'axios'

// 创建axios实例
const instance = axios.create({
  // 设置API基础URL为相对路径，使用Vite代理
  baseURL: '',
  // 请求超时时间
  timeout: 100000,
  // 请求头
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    // 例如：添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    return response.data
  },
  error => {
    // 对响应错误做点什么
    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      console.error('请求错误:', error.response.status, error.response.data)
      
      // 处理特定状态码
      switch (error.response.status) {
        case 401:
          // 未授权，可能需要重新登录
          console.warn('未授权，请重新登录')
          break
        case 403:
          // 禁止访问
          console.warn('禁止访问')
          break
        case 404:
          // 资源不存在
          console.warn('请求的资源不存在')
          break
        case 500:
          // 服务器错误
          console.warn('服务器错误')
          break
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('网络错误，未收到响应')
    } else {
      // 在设置请求时发生错误
      console.error('请求配置错误', error.message)
    }
    return Promise.reject(error)
  }
)

export default instance 