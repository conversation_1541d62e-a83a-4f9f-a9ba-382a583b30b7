{"version": 3, "file": "SafeMethods.js", "sourceRoot": "", "sources": ["../../../ts/ui/safe/SafeMethods.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,oDAAgD;AAMnC,QAAA,WAAW,GAAoD;IAa1E,SAAS,EAAT,UAAmB,IAAmB,EAAE,GAAW;QACjD,IAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC9E,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAC9B,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM;YAChB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IACjG,CAAC;IAaD,eAAe,EAAf,UAAyB,IAAmB,EAAE,IAAY;QAA1D,iBAGC;QAFC,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9D,OAAO,OAAO,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAlC,CAAkC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC1G,CAAC;IAaD,WAAW,EAAX,UAAqB,IAAmB,EAAE,KAAa;QACrD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1G,CAAC;IAaD,QAAQ,EAAR,UAAkB,IAAmB,EAAE,EAAU;QAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAChC,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACjG,CAAC;IAaD,YAAY,EAAZ,UAAsB,IAAmB,EAAE,MAAc;;QACvD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK;YAAE,OAAO,MAAM,CAAC;QAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM;YAAE,OAAO,IAAI,CAAC;QAC9C,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI;YAIF,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;YAClD,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;gBAKjC,KAAoB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;oBAAhD,IAAM,KAAK,WAAA;oBACd,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;;4BAC7B,KAAoB,IAAA,oBAAA,SAAA,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAA,gBAAA,4BAAE;gCAAnD,IAAM,KAAK,WAAA;gCACd,IAAM,MAAI,GAAG,KAAK,GAAG,KAAK,CAAC;gCAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAI,EAAE,IAAI,CAAC,CAAC;gCACjD,IAAI,KAAK,EAAE;oCACT,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAI,EAAE,KAAK,CAAC,CAAC;iCACrC;6BACF;;;;;;;;;qBACF;yBAAM;wBACL,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;wBAClD,IAAI,KAAK,EAAE;4BACT,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;yBACtC;qBACF;iBACF;;;;;;;;;YAID,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAClC;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,GAAG,EAAE,CAAC;SACb;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAcD,WAAW,EAAX,UAAqB,IAAmB,EAAE,KAAa,EAAE,GAAM;QAC7D,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC;YACpE,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC;YACtE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;QACD,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAeD,gBAAgB,EAAhB,UAA0B,IAAmB,EAAE,KAAa,EAAE,KAAa,EAAE,GAAM;QACjF,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAC;SACd;QACD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACnD;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAcD,iBAAiB,EAAjB,UAA2B,IAAmB,EAAE,KAAa,EAAE,KAAa;QAC1E,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAA0C,CAAC;YAAE,OAAO,IAAI,CAAC;QAC1E,IAAM,EAAE,GAAG,IAAA,sBAAS,EAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAA,KAAA,OAAS,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAA,EAA9F,CAAC,QAAA,EAAE,CAAC,QAA0F,CAAC;QACtG,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACjG,CAAC;IAaD,cAAc,EAAd,UAAwB,IAAmB,EAAE,IAAY;QACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAaD,oBAAoB,EAApB,UAA8B,IAAmB,EAAE,IAAY;QACvD,IAAA,KAAA,OAAS,IAAI,CAAC,OAAO,CAAC,yBAAyB,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAA,EAAvE,CAAC,QAAA,EAAE,CAAC,QAAmE,CAAC;QAC/E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC/D,CAAC;IAaD,iBAAiB,EAAjB,UAA2B,IAAmB,EAAE,KAAa;QACrD,IAAA,KAAA,OAAS,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAA,EAA9D,CAAC,QAAA,EAAE,CAAC,QAA0D,CAAC;QACtE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC9D,CAAC;IAcD,UAAU,EAAV,UAAoB,IAAmB,EAAE,KAAa,EAAE,EAAU;QAChE,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;CAEF,CAAC"}