{"version": 3, "file": "Attributes.js", "sourceRoot": "", "sources": ["../../../ts/core/MmlTree/Attributes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AA4Ba,QAAA,OAAO,GAAG,WAAW,CAAC;AAUnC;IAwBE,oBAAY,QAAsB,EAAE,MAAoB;QACtD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAMM,wBAAG,GAAV,UAAW,IAAY,EAAE,KAAe;QACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAChC,CAAC;IAKM,4BAAO,GAAd,UAAe,IAAkB;QAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAMM,wBAAG,GAAV,UAAW,IAAY;QACrB,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,KAAK,eAAO,EAAE;YACrB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3B;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAOM,gCAAW,GAAlB,UAAmB,IAAY;QAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACzC,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAMM,4BAAO,GAAd;;QAAe,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;QAC/B,IAAI,MAAM,GAAiB,EAAE,CAAC;;YAC9B,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,MAAM,CAAC,MAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC;aAC/B;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAMM,iCAAY,GAAnB,UAAoB,IAAY,EAAE,KAAe;QAC/C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAC/B,CAAC;IAMM,iCAAY,GAAnB,UAAoB,IAAY;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAMM,+BAAU,GAAjB,UAAkB,IAAY;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAOM,0BAAK,GAAZ,UAAa,IAAY;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAMM,+BAAU,GAAjB,UAAkB,IAAY;QAC5B,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAKM,qCAAgB,GAAvB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAKM,sCAAiB,GAAxB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAKM,oCAAe,GAAtB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAKM,mCAAc,GAArB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAKM,qCAAgB,GAAvB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAKM,oCAAe,GAAtB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKM,mCAAc,GAArB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAKM,kCAAa,GAApB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEH,iBAAC;AAAD,CAAC,AApLD,IAoLC;AApLY,gCAAU"}