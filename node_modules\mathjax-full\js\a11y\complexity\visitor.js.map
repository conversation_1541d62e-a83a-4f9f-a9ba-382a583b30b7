{"version": 3, "file": "visitor.js", "sourceRoot": "", "sources": ["../../../ts/a11y/complexity/visitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,kEAA4D;AAE5D,6CAAuC;AACvC,oDAA8E;AAO9E;IAAuC,qCAAU;IAiD7C,2BAAY,OAAmB,EAAE,OAAmB;QAApD,YACI,kBAAM,OAAO,CAAC,SAKjB;QAzCM,gBAAU,GAA6B;YAC5C,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,CAAC;YAER,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;YACV,GAAG,EAAE,CAAC;YACN,KAAK,EAAE,CAAC;SACT,CAAC;QAsBE,IAAI,KAAK,GAAG,KAAI,CAAC,WAAuC,CAAC;QACzD,KAAI,CAAC,OAAO,GAAG,IAAA,wBAAW,EAAC,IAAA,2BAAc,EAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;QACvE,KAAI,CAAC,QAAQ,GAAG,IAAI,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAI,CAAC,CAAC;QAChD,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IAC3B,CAAC;IAOM,qCAAS,GAAhB,UAAiB,IAAa;QAC1B,iBAAM,SAAS,YAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACpC;IACL,CAAC;IAKM,qCAAS,GAAhB,UAAiB,IAAa,EAAE,IAAa;QACzC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC;YAAE,OAAO;QAC5D,OAAO,iBAAM,SAAS,YAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAOM,wCAAY,GAAnB,UAAoB,IAAa,EAAE,IAAa;QAC5C,IAAI,UAAU,CAAC;QACf,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAM,IAAI,GAAI,IAA6B,CAAC,OAAO,EAAE,CAAC;YACtD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;SAC3E;aAAM;YACH,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC9C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IASS,0CAAc,GAAxB,UAAyB,IAAa,EAAE,IAAa;QACjD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACrG,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQS,0CAAc,GAAxB,UAAyB,IAAa,EAAE,IAAa;QACjD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACxE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IASS,0CAAc,GAAxB,UAAyB,IAAc,EAAE,IAAa;QAClD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI;cACjE,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQS,6CAAiB,GAA3B,UAA4B,IAAa,EAAE,IAAa;QACpD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IASS,uCAAW,GAArB,UAAsB,IAAa,EAAE,IAAa;QAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;cAC5B,IAA6B,CAAC,OAAO,EAAE;cACxC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACtD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAUS,4CAAgB,GAA1B,UAA2B,IAAgB,EAAE,IAAa;QACtD,iBAAM,YAAY,YAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CACrB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EACjC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACpC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3B,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACrC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAKS,yCAAa,GAAvB,UAAwB,IAAa,EAAE,IAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAKS,yCAAa,GAAvB,UAAwB,IAAa,EAAE,IAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAWS,+CAAmB,GAA7B,UAA8B,IAAmB,EAAE,IAAa;QAC5D,iBAAM,YAAY,YAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CACrB,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EACrC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACtC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,EAAE;YACN,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;SAC/D;QACD,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1F,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;QACxC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAKS,2CAAe,GAAzB,UAA0B,IAAe,EAAE,IAAa;QACpD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAKS,0CAAc,GAAxB,UAAyB,IAAc,EAAE,IAAa;QAClD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAQS,6CAAiB,GAA3B,UAA4B,IAAa,EAAE,IAAa;QACpD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3E,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQS,4CAAgB,GAA1B,UAA2B,IAAgB,EAAE,IAAa;QACtD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQS,+CAAmB,GAA7B,UAA8B,IAAa,EAAE,IAAa;QACtD,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;QAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC5B,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQS,+CAAmB,GAA7B,UAA8B,IAAa,EAAE,IAAa;QACtD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAQS,mDAAuB,GAAjC,UAAkC,IAAa,EAAE,IAAa;QAC1D,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAQS,2CAAe,GAAzB,UAA0B,IAAa,EAAE,IAAa;QAClD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACjE,CAAC;IAQM,yCAAa,GAApB,UAAqB,IAAa;QAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAC1D,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAW,CAAC;IACvG,CAAC;IAOS,yCAAa,GAAvB,UAAwB,IAAa,EAAE,UAAkB,EAAE,IAAa;QACpE,IAAI,IAAI,EAAE;YACN,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAClC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;aACtD;YACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC;SAC/D;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAMS,8CAAkB,GAA5B,UAA6B,IAAa;;QACtC,iBAAM,YAAY,YAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;;YACnB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACZ,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,KAAgB,CAAC,CAAC;aACtD;;;;;;;;;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;SAChE;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IA/Ua,yBAAO,GAAe;QAChC,mBAAmB,EAAE,IAAI;QACzB,eAAe,EAAE,IAAI;QACrB,QAAQ,EAAE,sBAAQ;KACrB,CAAC;IA6UN,wBAAC;CAAA,AAtVD,CAAuC,0BAAU,GAsVhD;AAtVY,8CAAiB"}