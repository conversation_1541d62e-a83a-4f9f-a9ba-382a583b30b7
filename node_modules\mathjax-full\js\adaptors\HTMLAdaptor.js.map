{"version": 3, "file": "HTMLAdaptor.js", "sourceRoot": "", "sources": ["../../ts/adaptors/HTMLAdaptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,uDAA8F;AA4J9F;IACA,+BAA2B;IAezB,qBAAY,MAAuB;QAAnC,YACE,kBAAM,MAAM,CAAC,QAAQ,CAAC,SAGvB;QAFC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,MAAM,GAAG,IAAK,MAAM,CAAC,SAAiB,EAAE,CAAC;;IAChD,CAAC;IAKM,2BAAK,GAAZ,UAAa,IAAY,EAAE,MAA4B;QAA5B,uBAAA,EAAA,oBAA4B;QACrD,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAKS,4BAAM,GAAhB,UAAiB,IAAY,EAAE,EAAW;QACxC,OAAO,CAAC,EAAE,CAAC,CAAC;YACJ,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC;IAKM,0BAAI,GAAX,UAAY,IAAY;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAKM,0BAAI,GAAX,UAAY,GAAM;QAChB,OAAO,GAAG,CAAC,IAAI,IAAK,GAAgB,CAAC;IACvC,CAAC;IAKM,0BAAI,GAAX,UAAY,GAAM;QAChB,OAAO,GAAG,CAAC,IAAI,IAAK,GAAgB,CAAC;IACvC,CAAC;IAKM,0BAAI,GAAX,UAAY,GAAM;QAChB,OAAO,GAAG,CAAC,eAAe,IAAK,GAAgB,CAAC;IAClD,CAAC;IAKM,6BAAO,GAAd,UAAe,GAAM;QACnB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAa,GAAG,CAAC,OAAO,CAAC,IAAI,MAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/D,CAAC;IAKM,0BAAI,GAAX,UAAY,IAAO,EAAE,IAAY,EAAE,EAAiB;QAAjB,mBAAA,EAAA,SAAiB;QAClD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3F,OAAO,KAAK,CAAC,IAAI,CAAC,KAAY,CAAQ,CAAC;IACzC,CAAC;IAKM,iCAAW,GAAlB,UAAmB,KAA2B,EAAE,SAAY;;QAC1D,IAAI,UAAU,GAAQ,EAAE,CAAC;;YACzB,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,IAAI,kBAAA;gBACb,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAC7B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAClF;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAQ,CAAC,CAAC;iBACzD;qBAAM,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;oBAC7F,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAkB,CAAC,CAAC,CAAC;iBAChE;qBAAM;oBACL,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACvB;aACF;;;;;;;;;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAKM,8BAAQ,GAAf,UAAgB,SAAY,EAAE,IAAW;QACvC,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAKM,4BAAM,GAAb,UAAc,IAAW;QACvB,OAAO,IAAI,CAAC,UAAe,CAAC;IAC9B,CAAC;IAKM,4BAAM,GAAb,UAAc,IAAO,EAAE,KAAY;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAU,CAAC;IAC1C,CAAC;IAKM,4BAAM,GAAb,UAAc,MAAa,EAAE,MAAa;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IAKM,4BAAM,GAAb,UAAc,KAAY;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,CAAU,CAAC;IACxD,CAAC;IAKM,6BAAO,GAAd,UAAe,KAAY,EAAE,KAAY;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAU,CAAC;IAChE,CAAC;IAKM,2BAAK,GAAZ,UAAa,IAAO;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM,CAAC;IACnC,CAAC;IAKM,2BAAK,GAAZ,UAAa,IAAO,EAAE,CAAS;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAKM,0BAAI,GAAX,UAAY,IAAW;QACrB,OAAO,IAAI,CAAC,WAAoB,CAAC;IACnC,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAW;QACzB,OAAO,IAAI,CAAC,eAAwB,CAAC;IACvC,CAAC;IAKM,gCAAU,GAAjB,UAAkB,IAAO;QACvB,OAAO,IAAI,CAAC,UAAmB,CAAC;IAClC,CAAC;IAKM,+BAAS,GAAhB,UAAiB,IAAO;QACtB,OAAO,IAAI,CAAC,SAAkB,CAAC;IACjC,CAAC;IAKM,gCAAU,GAAjB,UAAkB,IAAO;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAuB,CAAC,CAAC;IAClD,CAAC;IAKM,+BAAS,GAAhB,UAAiB,IAAO,EAAE,CAAS;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAU,CAAC;IACrC,CAAC;IAKM,0BAAI,GAAX,UAAY,IAAW;QACrB,IAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QACxB,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;IAKM,2BAAK,GAAZ,UAAa,IAAW;QACtB,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAKM,iCAAW,GAAlB,UAAmB,IAAO;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKM,+BAAS,GAAhB,UAAiB,IAAO;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKM,+BAAS,GAAhB,UAAiB,IAAO;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,kCAAY,GAAnB,UAAoB,IAAO;QACzB,IAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QACnD,OAAO,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAW,CAAC;IACtD,CAAC;IAKM,kCAAY,GAAnB,UAAoB,IAAO,EAAE,IAAY,EAAE,KAAa,EAAE,EAAiB;QAAjB,mBAAA,EAAA,SAAiB;QACzE,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACvC;QACD,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAKM,kCAAY,GAAnB,UAAoB,IAAO,EAAE,IAAY;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAKM,qCAAe,GAAtB,UAAuB,IAAO,EAAE,IAAY;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAKM,kCAAY,GAAnB,UAAoB,IAAO,EAAE,IAAY;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAKM,mCAAa,GAApB,UAAqB,IAAO;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CACpC,UAAC,CAAgB;YACf,OAAO,EAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAkB,CAAC;QACzD,CAAC,CACF,CAAC;IACJ,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO,EAAE,IAAY;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;SACvD;IACH,CAAC;IAKM,iCAAW,GAAlB,UAAmB,IAAO,EAAE,IAAY;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,KAAK,IAAI,EAAV,CAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChF;IACH,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO,EAAE,IAAY;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO,EAAE,IAAY,EAAE,KAAa;QACjD,IAAI,CAAC,KAAoB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAC3C,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO,EAAE,IAAY;QACnC,OAAQ,IAAI,CAAC,KAAoB,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAKM,+BAAS,GAAhB,UAAiB,IAAO;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAKM,iCAAW,GAAlB,UAAmB,IAAO,EAAE,KAAe;;;YACzC,KAAmB,IAAA,KAAA,SAAA,KAAK,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,IAAI,WAAA;gBACb,IAAI;oBACF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;iBAChC;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO,CAAC,IAAI,CAAC,0CAAmC,IAAI,gBAAM,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;iBACxE;aACF;;;;;;;;;IACH,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO;QACrB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACjD,OAAO,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAKM,gCAAU,GAAjB,UAAkB,IAAO;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;IAChC,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO,EAAE,EAAc,EAAE,KAAsB;QAAtC,mBAAA,EAAA,MAAc;QAAE,sBAAA,EAAA,aAAsB;QAC7D,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;YACrB,IAAA,KAAkB,IAAI,CAAC,OAAO,EAAE,EAA/B,KAAK,WAAA,EAAE,MAAM,YAAkB,CAAC;YACrC,OAAO,CAAC,KAAK,GAAG,EAAE,EAAG,MAAM,GAAG,EAAE,CAAqB,CAAC;SACvD;QACD,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAqB,CAAC;IAC7E,CAAC;IAKM,8BAAQ,GAAf,UAAgB,IAAO;QACf,IAAA,KAA6B,IAAI,CAAC,qBAAqB,EAAc,EAApE,IAAI,UAAA,EAAE,KAAK,WAAA,EAAE,GAAG,SAAA,EAAE,MAAM,YAA4C,CAAC;QAC5E,OAAO,EAAC,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,MAAM,QAAA,EAAC,CAAC;IACpC,CAAC;IACH,kBAAC;AAAD,CAAC,AA9XD,CACA,kCAAkB,GA6XjB;AA9XY,kCAAW"}