{"version": 3, "file": "HTMLDocument.js", "sourceRoot": "", "sources": ["../../../ts/handlers/html/HTMLDocument.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,8DAAgE;AAChE,oDAA2F;AAC3F,qDAA+C;AAC/C,qDAA+C;AAC/C,yDAAmD;AAGnD,sDAAkE;AAwBlE;IAA2C,gCAA6B;IAoCtE,sBAAY,QAAa,EAAE,OAA4B,EAAE,OAAmB;QAA5E,iBAMC;QALK,IAAA,KAAA,OAAc,IAAA,4BAAe,EAAC,OAAO,EAAE,kCAAc,CAAC,OAAO,CAAC,IAAA,EAA7D,IAAI,QAAA,EAAE,GAAG,QAAoD,CAAC;gBACnE,kBAAM,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC;QAC9B,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,kCAAc,CAAU,GAAG,CAAC,CAAC;QACjF,KAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,KAAI,CAAC,MAAM,GAAG,EAAE,CAAC;;IACnB,CAAC;IAaS,mCAAY,GAAtB,UAAuB,CAAS,EAAE,KAAa,EAAE,KAAa,EAAE,KAA0B;;QACxF,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;;YAC7B,KAAmB,IAAA,KAAA,SAAA,KAAK,CAAC,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBAAxB,IAAM,IAAI,WAAA;gBACT,IAAA,KAAA,OAAY,IAAI,IAAA,EAAf,IAAI,QAAA,EAAE,CAAC,QAAQ,CAAC;gBACrB,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE;oBAChD,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;iBAC1D;gBACD,KAAK,IAAI,CAAC,CAAC;aACZ;;;;;;;;;QACD,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;IAC1C,CAAC;IAWS,+BAAQ,GAAlB,UAAmB,IAAqB,EAAE,GAAsB,EAC7C,KAA0B;QACxB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACnE,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAA0B,CAAC;IACjG,CAAC;IAmBb,+BAAQ,GAAf,UAAgB,OAAmB;;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACtC,OAAO,GAAG,IAAA,wBAAW,EAAC,EAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAC,EAAE,OAAO,CAAC,CAAC;;gBACxG,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA,gBAAA,4BAAE;oBAAjF,IAAM,SAAS,WAAA;oBACd,IAAA,KAAA,OAAmB,CAAC,IAAI,EAAE,IAAI,CAAoC,IAAA,EAAjE,OAAO,QAAA,EAAE,KAAK,QAAmD,CAAC;;wBACvE,KAAkB,IAAA,oBAAA,SAAA,IAAI,CAAC,QAAQ,CAAA,CAAA,gBAAA,4BAAE;4BAA5B,IAAM,GAAG,WAAA;4BACZ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;4BAC5C,IAAI,GAAG,CAAC,cAAc,EAAE;gCACtB,IAAI,OAAO,KAAK,IAAI,EAAE;oCACpB,KAAA,OAAmB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAA,EAAjD,OAAO,QAAA,EAAE,KAAK,QAAA,CAAoC;iCACpD;;oCACD,KAAmB,IAAA,oBAAA,SAAA,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,CAAA,gBAAA,4BAAE;wCAArC,IAAM,IAAI,WAAA;wCACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;qCAC5C;;;;;;;;;6BACF;iCAAM;;oCACL,KAAmB,IAAA,oBAAA,SAAA,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAA,gBAAA,4BAAE;wCAAvC,IAAM,IAAI,WAAA;wCACb,IAAI,IAAI,GACN,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wCAChF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qCACjB;;;;;;;;;6BACF;4BACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;yBACvB;;;;;;;;;iBACF;;;;;;;;;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAChC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,qCAAc,GAArB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;YAC3C,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,iBAAM,cAAc,WAAE,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKS,sCAAe,GAAzB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACzC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAKM,oCAAa,GAApB;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACnC,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;YACrE,IAAI,MAAM,EAAE;gBACV,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aAChC;iBAAM;gBACL,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC7B;SACF;IACH,CAAC;IAOS,gCAAS,GAAnB,UAAoB,IAAO,EAAE,EAAU;;QACrC,IAAI,EAAE,EAAE;;gBACN,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA,gBAAA,4BAAE;oBAAjD,IAAM,KAAK,WAAA;oBACd,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;wBACjD,OAAO,KAAK,CAAC;qBACd;iBACF;;;;;;;;;SACF;QACD,OAAO,IAAS,CAAC;IACnB,CAAC;IAKM,yCAAkB,GAAzB,UAA0B,OAAwB;;QAAxB,wBAAA,EAAA,eAAwB;QAChD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;;gBAC1C,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;oBAAzB,IAAM,IAAI,WAAA;oBACb,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,QAAQ,EAAE;wBAClC,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;qBACpC;iBACF;;;;;;;;;SACF;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,yCAAkB,GAAzB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAKM,2CAAoB,GAA3B;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAOM,gCAAS,GAAhB,UAAiB,MAAiB;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAKM,gCAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAlOa,iBAAI,GAAW,MAAM,CAAC;IAKtB,oBAAO,yBAChB,sCAAoB,CAAC,OAAO,KAC/B,aAAa,EAAE,IAAA,uBAAU,wBACpB,sCAAoB,CAAC,OAAO,CAAC,aAAa,KAC7C,MAAM,EAAE,CAAC,mBAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,KAAK,CAAC,IAC3D,EACF,QAAQ,EAAE,8BAAY,EACtB,QAAQ,EAAE,8BAAY,EACtB,UAAU,EAAE,IAAI,IAChB;IAsNJ,mBAAC;CAAA,AAzOD,CAA2C,sCAAoB,GAyO9D;AAzOY,oCAAY"}