{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../../../ts/util/asyncLoad/node.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,+CAAyC;AACzC,yCAA6B;AAK7B,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AAEnD,IAAI,CAAC,oBAAO,CAAC,SAAS,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;IACxD,oBAAO,CAAC,SAAS,GAAG,UAAC,IAAY;QAC/B,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3E,CAAC,CAAC;CACH"}