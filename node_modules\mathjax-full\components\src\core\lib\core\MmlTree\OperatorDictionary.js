"use strict";
Object.defineProperty(exports, '__esModule', {value: true});
exports.OPDEF = MathJax._.core.MmlTree.OperatorDictionary.OPDEF;
exports.MO = MathJax._.core.MmlTree.OperatorDictionary.MO;
exports.RANGES = MathJax._.core.MmlTree.OperatorDictionary.RANGES;
exports.getRange = MathJax._.core.MmlTree.OperatorDictionary.getRange;
exports.MMLSPACING = MathJax._.core.MmlTree.OperatorDictionary.MMLSPACING;
exports.OPTABLE = MathJax._.core.MmlTree.OperatorDictionary.OPTABLE;
