{"version": 3, "file": "MmlVisitor.js", "sourceRoot": "", "sources": ["../../../ts/ui/menu/MmlVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAyBA,sFAAgF;AAChF,oDAA8D;AAW9D;IAAyC,8BAAoB;IAA7D;QAAA,qEA8DC;QAzDQ,aAAO,GAAe;YAC3B,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,KAAK;SACjB,CAAC;QAKK,cAAQ,GAAsB,IAAI,CAAC;;IAiD5C,CAAC;IAzCQ,8BAAS,GAAhB,UAAiB,IAAa,EAAE,IAA8B,EAAE,OAAwB;QAAxD,qBAAA,EAAA,WAA8B;QAAE,wBAAA,EAAA,YAAwB;QACtF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAA,wBAAW,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAKM,qCAAgB,GAAvB,UAAwB,IAAa,EAAE,KAAa;QAClD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,OAAO,iBAAM,gBAAgB,YAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACpE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAClD;QACD,OAAO,KAAK,GAAG,OAAO,GAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK;cACtD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,IAAI,CAAC;cAC3C,KAAK,GAAG,SAAS,CAAC;IACxB,CAAC;IAOM,kCAAa,GAApB,UAAqB,IAAa,EAAE,KAAa;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE;YACpE,OAAO,iBAAM,YAAY,YAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACxC;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAClF,OAAO,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK;cAC1C,KAAK,GAAG,iBAAiB;cACzB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;cACtC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;cACnE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;cACvC,KAAK,GAAG,+CAA+C,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,iBAAiB;cAChG,KAAK,GAAG,kBAAkB;cAC1B,KAAK,GAAG,SAAS,CAAC;IACnC,CAAC;IAEH,iBAAC;AAAD,CAAC,AA9DD,CAAyC,8CAAoB,GA8D5D;AA9DY,gCAAU"}