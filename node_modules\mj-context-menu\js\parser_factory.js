"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParserFactory = void 0;
var ParserFactory = (function () {
    function ParserFactory(init) {
        this._parser = new Map(init);
    }
    ParserFactory.prototype.get = function (name) {
        return this._parser.get(name);
    };
    ParserFactory.prototype.add = function (name, method) {
        this._parser.set(name, method);
    };
    return ParserFactory;
}());
exports.ParserFactory = ParserFactory;
//# sourceMappingURL=parser_factory.js.map