import { SVGConstructor } from '../Wrapper.js';
declare const SVGmtd_base: import("../../common/Wrappers/mtd.js").MtdConstructor & SVGConstructor<any, any, any>;
export declare class SVGmtd<N, T, D> extends SVGmtd_base {
    static kind: string;
    placeCell(x: number, y: number, W: number, H: number, D: number): [number, number];
    placeColor(x: number, y: number, W: number, H: number): void;
}
export {};
