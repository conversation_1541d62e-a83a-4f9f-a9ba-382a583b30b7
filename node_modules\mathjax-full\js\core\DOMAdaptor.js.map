{"version": 3, "file": "DOMAdaptor.js", "sourceRoot": "", "sources": ["../../ts/core/DOMAdaptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAuXA;IAWE,4BAAY,QAAkB;QAAlB,yBAAA,EAAA,eAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAUM,iCAAI,GAAX,UAAY,IAAY,EAAE,GAAoB,EAAE,QAAwB,EAAE,EAAW;;QAA3D,oBAAA,EAAA,QAAoB;QAAE,yBAAA,EAAA,aAAwB;QACtE,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;YAC9B,KAAoB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;gBAAzB,IAAM,KAAK,qBAAA;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC1B;;;;;;;;;QACD,OAAO,IAAS,CAAC;IACnB,CAAC;IAkBM,0CAAa,GAApB,UAAqB,IAAO,EAAE,GAAe;;QAC3C,IAAI,GAAG,CAAC,KAAK,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;;gBAC/C,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA,gBAAA,4BAAE;oBAAnC,IAAI,GAAG,WAAA;oBACV,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,UAAC,EAAE,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,EAAE,EAAf,CAAe,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC3F;;;;;;;;;SACF;QACD,IAAI,GAAG,CAAC,UAAU,EAAE;;gBAClB,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;oBAAxC,IAAI,GAAG,WAAA;oBACT,IAAmB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;iBACjD;;;;;;;;;SACF;;YACD,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;gBAA7B,IAAI,GAAG,WAAA;gBACV,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,IAAI,GAAG,KAAK,YAAY,EAAE;oBAC/E,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;iBACxC;aACF;;;;;;;;;IACH,CAAC;IA4DM,oCAAO,GAAd,UAAe,KAAY,EAAE,KAAY;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAwCM,sCAAS,GAAhB,UAAiB,IAAO,EAAE,CAAS;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IA4EM,uCAAU,GAAjB,UAAkB,IAAO;QACvB,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAc,CAAC,CAAC;YAC3B,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACvF,CAAC;IA0CH,yBAAC;AAAD,CAAC,AApSD,IAoSC;AApSqB,gDAAkB"}