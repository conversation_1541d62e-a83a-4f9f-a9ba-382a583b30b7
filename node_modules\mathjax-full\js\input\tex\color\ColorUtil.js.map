{"version": 3, "file": "ColorUtil.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/color/ColorUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwBA,+DAAsC;AACtC,yDAA2C;AAG3C,IAAM,oBAAoB,GAAqC,IAAI,GAAG,EAA+B,CAAC;AAGtG;IAAA;QAQU,eAAU,GAAwB,IAAI,GAAG,EAAkB,CAAC;IA6EtE,CAAC;IApES,mCAAc,GAAtB,UAAuB,KAAa,EAAE,GAAW;QAC/C,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,OAAO,EAAE;YAE/B,OAAO,GAAG,CAAC;SACZ;QAED,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACnC,IAAM,cAAc,GAAG,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACvD,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;SAC5B;QAED,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EAAE,gCAAgC,EAAE,KAAK,CAAC,CAAC;IACrF,CAAC;IASM,6BAAQ,GAAf,UAAgB,KAAa,EAAE,GAAW;QACxC,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAeO,mCAAc,GAAtB,UAAuB,IAAY;QACjC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAClC;QAED,IAAI,0BAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpB,OAAO,0BAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;QAGD,OAAO,IAAI,CAAC;IACd,CAAC;IAWM,gCAAW,GAAlB,UAAmB,KAAa,EAAE,IAAY,EAAE,GAAW;QACzD,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACxC,CAAC;IACH,iBAAC;AAAD,CAAC,AArFD,IAqFC;AArFY,gCAAU;AA+FvB,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,GAAW;;IACnD,IAAM,QAAQ,GAAa,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACvD,IAAI,GAAG,GAAW,GAAG,CAAC;IAEtB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,MAAM,IAAI,qBAAQ,CAAC,WAAW,EAAE,iDAAiD,EAAE,KAAK,CAAC,CAAC;KAC3F;;QAED,KAAsB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;YAA3B,IAAM,OAAO,qBAAA;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE;gBAC3C,MAAM,IAAI,qBAAQ,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;aACtE;YAED,IAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAClB,MAAM,IAAI,qBAAQ,CAAC,WAAW,EACX,yDAAyD,EACzD,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;aACrC;YAED,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;aACf;YAED,GAAG,IAAI,EAAE,CAAC;SACX;;;;;;;;;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC,CAAC;AASH,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,GAAW;;IACnD,IAAM,QAAQ,GAAa,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACvD,IAAI,GAAG,GAAG,GAAG,CAAC;IAEd,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,MAAM,IAAI,qBAAQ,CAAC,WAAW,EAAE,iDAAiD,EAAE,KAAK,CAAC,CAAC;KAC3F;;QAED,KAAsB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;YAA3B,IAAM,OAAO,qBAAA;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBAC3B,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;aACvD;YAED,IAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,GAAG,EAAE;gBACX,MAAM,IAAI,qBAAQ,CAAC,WAAW,EACX,yDAAyD,EACzD,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;aACvC;YAED,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;aACf;YACD,GAAG,IAAI,EAAE,CAAC;SACX;;;;;;;;;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC,CAAC;AASH,oBAAoB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,IAAY;IACrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE;QAC9C,MAAM,IAAI,qBAAQ,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;KACtE;IAED,IAAM,CAAC,GAAW,UAAU,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClB,MAAM,IAAI,qBAAQ,CAAC,WAAW,EACX,yDAAyD,EACzD,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;KACtC;IACD,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1C,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;QACjB,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;KACf;IAED,OAAO,WAAI,EAAE,SAAG,EAAE,SAAG,EAAE,CAAE,CAAC;AAC5B,CAAC,CAAC,CAAC"}