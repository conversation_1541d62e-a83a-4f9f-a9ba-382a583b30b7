import { Configuration } from '../Configuration.js';
import TexParser from '../TexParser.js';
import { ParseMethod } from '../Types.js';
export declare function RequireLoad(parser: TexParser, name: string): void;
export declare const RequireMethods: Record<string, ParseMethod>;
export declare const options: {
    require: {
        allow: any;
        defaultAllow: boolean;
        prefix: string;
    };
};
export declare const RequireConfiguration: Configuration;
