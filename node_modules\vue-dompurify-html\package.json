{"name": "vue-dompurify-html", "version": "5.3.0", "description": "Safe replacement for the v-html directive", "license": "MIT", "author": "<PERSON>", "main": "./dist/vue-dompurify-html.umd.js", "module": "./dist/vue-dompurify-html.mjs", "types": "types/index.d.ts", "web-types": "./web-types.json", "files": ["/dist", "/types", "web-types.json"], "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/vue-dompurify-html.mjs", "require": "./dist/vue-dompurify-html.umd.js"}}, "repository": {"type": "git", "url": "https://github.com/LeSuisse/vue-dompurify-html.git", "directory": "packages/vue-dompurify-html"}, "bugs": {"url": "https://github.com/LeSuisse/vue-dompurify-html/issues"}, "homepage": "https://github.com/LeSuisse/vue-dompurify-html/tree/main/packages/vue-dompurify-html#readme", "keywords": ["vue", "vue-directive", "xss", "dom-xss", "dompurify", "security", "html"], "dependencies": {"dompurify": "^3.2.5"}, "peerDependencies": {"vue": "^3.4.36"}, "devDependencies": {"@stryker-mutator/core": "8.7.1", "@stryker-mutator/typescript-checker": "8.7.1", "@stryker-mutator/vitest-runner": "8.7.1", "@vitest/coverage-v8": "3.1.2", "@vue/test-utils": "2.4.6", "jsdom": "26.1.0", "typescript": "5.8.3", "vite": "6.3.5", "vitest": "3.1.2", "vue": "3.5.13"}, "scripts": {"build": "tsc --declaration --emitDeclarationOnly && vite build", "typecheck": "tsc --noEmit -p ./tsconfig.test.json", "test": "vitest", "test-mutation": "stryker run"}}