{"version": 3, "file": "ColorMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/color/ColorMethods.ts"], "names": [], "mappings": ";;;;;;AAwBA,+DAAsC;AAGtC,iEAAwC;AAYxC,SAAS,OAAO,CAAC,YAAoB;IACnC,IAAM,GAAG,GAAG,WAAI,YAAY,CAAE,CAAC;IAC/B,IAAM,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IACzD,IAAM,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IACjC,OAAO;QACL,KAAK,EAAE,WAAI,IAAI,SAAG,IAAI,CAAE;QACxB,MAAM,EAAE,GAAG;QACX,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,YAAY;KACrB,CAAC;AACJ,CAAC;AAGY,QAAA,YAAY,GAAgC,EAAE,CAAC;AAS5D,oBAAY,CAAC,KAAK,GAAG,UAAU,MAAiB,EAAE,IAAY;IAC5D,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3C,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAM,UAAU,GAAe,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IACnF,IAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAEnD,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC;SAC7C,aAAa,CAAC,EAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,EAAC,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;IAElC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC,CAAC;AASF,oBAAY,CAAC,SAAS,GAAG,UAAU,MAAiB,EAAE,IAAY;IAChE,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3C,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAM,UAAU,GAAe,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IACnF,IAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACnD,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAEtC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;IAClC,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEnC,IAAI,GAAG,EAAE;QACP,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;KACjC;SAAM;QACL,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KAClC;IAED,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,EAAC,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;IACzE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAQF,oBAAY,CAAC,WAAW,GAAG,UAAU,MAAiB,EAAE,IAAY;IAClE,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,IAAM,UAAU,GAAe,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IACnF,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC,CAAC;AAQF,oBAAY,CAAC,QAAQ,GAAG,UAAU,MAAiB,EAAE,IAAY;IAC/D,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,IAAI,GAAG,sBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,IAAM,UAAU,GAAe,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IAEnF,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;QAClD,cAAc,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;KACpD,CAAC,CAAC;IAEH,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAQF,oBAAY,CAAC,SAAS,GAAG,UAAU,MAAiB,EAAE,IAAY;IAChE,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvC,IAAM,IAAI,GAAG,sBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;IACrC,IAAM,UAAU,GAAe,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;IAEnF,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;QAClD,cAAc,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;QACnD,KAAK,EAAE,kBAAW,OAAO,CAAC,WAAW,oBAAU,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAE;KACrF,CAAC,CAAC;IAEH,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC"}