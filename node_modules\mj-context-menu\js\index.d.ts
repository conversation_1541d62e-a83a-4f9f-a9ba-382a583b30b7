import { ContextMenu as obj0 } from './context_menu';
import { CssStyles as obj1 } from './css_util';
import { Info as obj2 } from './info';
import { Popup as obj3 } from './popup';
import { Parser as obj4 } from './parse';
import { SelectionBox as obj5 } from './selection_box';
import { Variable as obj6 } from './variable';
import { ParserFactory as obj7 } from './parser_factory';
export declare let ContextMenu: typeof obj0;
export declare let CssStyles: typeof obj1;
export declare let Info: typeof obj2;
export declare let Popup: typeof obj3;
export declare let Parser: typeof obj4;
export declare let version: string;
export declare let SelectionBox: typeof obj5;
export declare let Variable: typeof obj6;
export declare let ParserFactory: typeof obj7;
