import { fetchEventSource } from '@microsoft/fetch-event-source';

/**
 * 文生文流式对话接口
 * @param baseUrl API基础URL
 * @param userId 用户ID
 * @param conversationId 会话ID
 * @param query 用户输入的文本
 * @param files 附带的文件数组
 * @param onPing 收到ping事件的回调
 * @param onText 收到文本内容的回调
 * @param onDone 文本内容输出完毕的回调
 * @param onCost 收到消耗token数的回调
 * @param onAllDone 整次流式输出结束的回调
 * @param onError 发生错误时的回调
 * @returns AbortController，可用于中断请求
 */
export function streamChat({
  baseUrl,
  userId,
  conversationId,
  query,
  files = [],
  onPing,
  onText,
  onDone,
  onCost,
  onAllDone,
  onError,
  maxRetries = 0 // 默认不重试
}: {
  baseUrl: string;
  userId: string;
  conversationId: string;
  query: string;
  files?: Array<{
    type: 'IMAGE' | 'AUDIO' | 'VIDEO' | 'DOCUMENT';
    fileId: string;
  }>;
  onPing?: () => void;
  onText?: (text: string) => void;
  onDone?: () => void;
  onCost?: (cost: string) => void;
  onAllDone?: () => void;
  onError?: (error: Error) => void;
  maxRetries?: number; // 最大重试次数
}) {
  // 创建AbortController用于中断请求
  const controller = new AbortController();

  // 重试计数器
  let retryCount = 0;
  let hasErrorOccurred = false;
  
  // 构建请求URL
  const url = `${baseUrl}/messages`;
  // 构建请求体
  const body = JSON.stringify({
    conversationId,
    query,
    streaming: true,
    files
  });

  // 发起流式请求
  fetchEventSource(url, {
    method: 'POST',
    headers: {
      'X-User-Id': userId,
      'Content-Type': 'application/json'
    },
    body,
    signal: controller.signal,
    // 禁用自动重试，我们手动控制
    openWhenHidden: true,
    async onopen(response) {
      if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
        console.log('流式连接已建立');
        return; // 连接成功
      } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
        // 4xx 错误（除了 429）通常不应该重试
        hasErrorOccurred = true;
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } else {
        // 其他错误可能需要重试
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    },
    onmessage(event) {
      try {
        // 解析事件数据
        const eventData = JSON.parse(event.data);

        // 根据事件类型调用相应的回调函数
        switch (eventData.event) {
          case 'ping':
            onPing?.();
            break;
          case 'text':
            onText?.(eventData.data);
            break;
          case 'done':
            onDone?.();
            break;
          case 'cost':
            onCost?.(eventData.data);
            break;
          case 'all_done':
            onAllDone?.();
            break;
          default:
            console.warn('未知事件类型:', eventData);
        }
      } catch (error) {
        console.error('处理消息时出错:', error);
        hasErrorOccurred = true;
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    },
    onerror(error) {
      console.error('流式请求出错:', error);
      hasErrorOccurred = true;

      // 检查是否应该重试
      if (retryCount < maxRetries && !controller.signal.aborted) {
        retryCount++;
        console.log(`准备重试，第 ${retryCount} 次重试`);
        return 1000; // 返回重试延迟时间（毫秒）
      } else {
        // 不再重试，调用错误回调
        onError?.(error);
        throw error; // 停止重试
      }
    }
  }).catch(error => {
    if (!hasErrorOccurred) {
      console.error('fetchEventSource 错误:', error);
      onError?.(error);
    }
  });

  // 返回控制器，以便调用方可以中断请求
  return controller;
}

/**
 * 文生图等非文本流式对话接口
 * @param baseUrl API基础URL
 * @param userId 用户ID
 * @param conversationId 会话ID
 * @param query 用户输入的文本
 * @param files 附带的文件数组
 * @returns Promise，解析为任务信息
 */
export async function nonStreamChat({
  baseUrl,
  userId,
  conversationId,
  query,
  files = []
}: {
  baseUrl: string;
  userId: string;
  conversationId: string;
  query: string;
  files?: Array<{
    type: 'IMAGE' | 'AUDIO' | 'VIDEO' | 'DOCUMENT';
    fileId: string;
  }>;
}) {
  // 构建请求URL
  const url = `${baseUrl}/messages`;
  
  // 构建请求体
  const body = JSON.stringify({
    conversationId,
    query,
    streaming: false,
    files
  });

  // 发起请求
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'X-User-Id': userId,
      'Content-Type': 'application/json'
    },
    body
  });

  if (!response.ok) {
    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * 查询任务状态
 * @param baseUrl API基础URL
 * @param userId 用户ID
 * @param conversationId 会话ID
 * @param taskId 任务ID
 * @returns Promise，解析为任务状态信息
 */
export async function checkTaskStatus({
  baseUrl,
  userId,
  conversationId,
  taskId
}: {
  baseUrl: string;
  userId: string;
  conversationId: string;
  taskId: string;
}) {
  // 构建请求URL
  const url = `${baseUrl}/messages/task`;
  
  // 构建请求体
  const body = JSON.stringify({
    conversationId,
    taskId
  });

  // 发起请求
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'X-User-Id': userId,
      'Content-Type': 'application/json'
    },
    body
  });

  if (!response.ok) {
    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
  }

  return response.json();
} 