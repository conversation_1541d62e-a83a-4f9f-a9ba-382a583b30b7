{"version": 3, "file": "BitField.js", "sourceRoot": "", "sources": ["../../ts/util/BitField.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA;IAAA;QAoBY,SAAI,GAAW,CAAC,CAAC;IAmE7B,CAAC;IA9De,iBAAQ,GAAtB;;QAAuB,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;;YACvC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,IAAI,IAAI,CAAC,GAAG,CAAC,MAAI,CAAC,EAAE;oBAClB,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,MAAI,CAAC,CAAC;iBACtD;gBACD,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;iBAC7D;gBACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC;aACjB;;;;;;;;;IACH,CAAC;IAMa,YAAG,GAAjB,UAAkB,IAAY;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAKM,sBAAG,GAAV,UAAW,IAAY;QACrB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAKM,wBAAK,GAAZ,UAAa,IAAY;QACvB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAMM,wBAAK,GAAZ,UAAa,IAAY;QACvB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAKM,wBAAK,GAAZ;QACE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IAChB,CAAC;IAMS,yBAAM,GAAhB,UAAiB,IAAY;QAC3B,IAAM,GAAG,GAAI,IAAI,CAAC,WAA+B,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACpD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAhFgB,eAAM,GAAG,CAAC,IAAI,EAAE,CAAC;IAKjB,aAAI,GAAW,CAAC,CAAC;IAKjB,cAAK,GAAwB,IAAI,GAAG,EAAE,CAAC;IAwE1D,eAAC;CAAA,AAvFD,IAuFC;AAvFY,4BAAQ;AA6FrB,SAAgB,aAAa;IAAC,eAAkB;SAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;QAAlB,0BAAkB;;IAC9C,IAAM,IAAI;QAAiB,wBAAQ;QAAtB;;QAAwB,CAAC;QAAD,WAAC;IAAD,CAAC,AAAzB,CAAc,QAAQ,EAAG,CAAC;IACvC,IAAI,CAAC,QAAQ,OAAb,IAAI,2BAAa,KAAK,WAAE;IACxB,OAAO,IAAI,CAAC;AACd,CAAC;AAJD,sCAIC"}