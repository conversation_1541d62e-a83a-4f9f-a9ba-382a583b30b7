(function(a,C,o,g){"use strict";function v(n){const r=new o(n?n.headers.cookie:null);return(u,{doNotParse:t=!1,autoUpdateDependencies:s=!1}={})=>i(u,{doNotParse:t,autoUpdateDependencies:s},r)}function i(n,{doNotParse:r=!1,autoUpdateDependencies:u=!1}={},t=new o){const s=u?[...n||[]]:n;let f=t.getAll({doNotParse:!0});const l=g.shallowRef(0),h=()=>{const e=t.getAll({doNotParse:!0});c(s||null,e,f)&&l.value++,f=e};return t.addChangeListener(h),C.tryOnScopeDispose(()=>{t.removeChangeListener(h)}),{get:(...e)=>(u&&s&&!s.includes(e[0])&&s.push(e[0]),l.value,t.get(e[0],{doNotParse:r,...e[1]})),getAll:(...e)=>(l.value,t.getAll({doNotParse:r,...e[0]})),set:(...e)=>t.set(...e),remove:(...e)=>t.remove(...e),addChangeListener:(...e)=>t.addChangeListener(...e),removeChangeListener:(...e)=>t.removeChangeListener(...e)}}function c(n,r,u){if(!n)return!0;for(const t of n)if(r[t]!==u[t])return!0;return!1}a.createCookies=v,a.useCookies=i})(this.VueUse=this.VueUse||{},VueUse,UniversalCookie,Vue);
