/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  Implements some numeric utility functions
 *
 * <AUTHOR> (<PERSON><PERSON>)
 */

/**
 * @param {number[]} A  The array to sum
 * @return {number}     The summ of the elements in A
 */
export function sum(A: number[]): number {
  return A.reduce((a, b) => a + b, 0);
}

/**
 * @param {number[]} A  The array whose maximum entry is sought
 * @return {number}     The largest entry in the array
 */
export function max(A: number[]): number {
  return A.reduce((a, b) => Math.max(a, b), 0);
}
