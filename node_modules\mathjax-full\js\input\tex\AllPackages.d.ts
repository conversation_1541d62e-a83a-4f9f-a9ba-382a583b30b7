import './base/BaseConfiguration.js';
import './action/ActionConfiguration.js';
import './ams/AmsConfiguration.js';
import './amscd/AmsCdConfiguration.js';
import './bbox/BboxConfiguration.js';
import './boldsymbol/BoldsymbolConfiguration.js';
import './braket/BraketConfiguration.js';
import './bussproofs/BussproofsConfiguration.js';
import './cancel/CancelConfiguration.js';
import './cases/CasesConfiguration.js';
import './centernot/CenternotConfiguration.js';
import './color/ColorConfiguration.js';
import './colorv2/ColorV2Configuration.js';
import './colortbl/ColortblConfiguration.js';
import './configmacros/ConfigMacrosConfiguration.js';
import './empheq/EmpheqConfiguration.js';
import './enclose/EncloseConfiguration.js';
import './extpfeil/ExtpfeilConfiguration.js';
import './gensymb/GensymbConfiguration.js';
import './html/HtmlConfiguration.js';
import './mathtools/MathtoolsConfiguration.js';
import './mhchem/MhchemConfiguration.js';
import './newcommand/NewcommandConfiguration.js';
import './noerrors/NoErrorsConfiguration.js';
import './noundefined/NoUndefinedConfiguration.js';
import './physics/PhysicsConfiguration.js';
import './setoptions/SetOptionsConfiguration.js';
import './tagformat/TagFormatConfiguration.js';
import './textcomp/TextcompConfiguration.js';
import './textmacros/TextMacrosConfiguration.js';
import './upgreek/UpgreekConfiguration.js';
import './unicode/UnicodeConfiguration.js';
import './verb/VerbConfiguration.js';
export declare const AllPackages: string[];
