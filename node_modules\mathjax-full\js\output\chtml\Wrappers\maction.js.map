{"version": 3, "file": "maction.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/maction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,4CAA6D;AAC7D,+DAAoE;AAEpE,+DAA2E;AAC3E,wEAAqE;AAarE;IACA,gCAA8F;IAD9F;;IAuKA,CAAC;IAdQ,8BAAO,GAAd,UAAe,MAAS;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,sCAAe,GAAtB,UAAuB,IAAY,EAAE,OAAqB;QACvD,IAAI,CAAC,KAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IA/Ja,iBAAI,GAAG,uBAAU,CAAC,SAAS,CAAC,IAAI,CAAC;IAKjC,mBAAM,GAAc;QAChC,aAAa,EAAE;YACb,QAAQ,EAAE,UAAU;SACrB;QACD,wBAAwB,EAAE;YACxB,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE,UAAU;YACpB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;YACnB,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;YACnB,SAAS,EAAE,GAAG;SACf;QACD,oBAAoB,EAAE;YACpB,OAAO,EAAE,cAAc;YACvB,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,gBAAgB;YACxB,WAAW,EAAE,KAAK;YAClB,kBAAkB,EAAE,SAAS;YAC7B,KAAK,EAAE,OAAO;YACd,YAAY,EAAE,qBAAqB;SACpC;QACD,qBAAqB,EAAE;YACrB,MAAM,EAAE,SAAS;SAClB;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,OAAO;YACjB,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,gBAAgB;YACxB,WAAW,EAAE,KAAK;YAClB,kBAAkB,EAAE,SAAS;YAC7B,KAAK,EAAE,OAAO;SACf;KACF,CAAC;IAKY,oBAAO,GAAG,IAAI,GAAG,CAAC;QAC9B,CAAC,QAAQ,EAAE,CAAC,UAAC,IAAI,EAAE,KAAK;oBAItB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAW,CAAC,CAAC;oBAIjG,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;oBACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,IAAkB,CAAC;oBAIpC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,UAAC,KAAY;wBACzC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;4BAKlB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;4BACnD,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;yBAC/B;wBACD,GAAG,CAAC,mBAAmB,EAAE,CAAC;wBAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACxB,KAAK,CAAC,eAAe,EAAE,CAAC;oBAC1B,CAAC,CAAC,CAAC;gBACL,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,CAAC,SAAS,EAAE,CAAC,UAAC,IAAI,EAAE,IAAI;oBACtB,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,CAAC,GAAG;wBAAE,OAAO;oBACjB,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBAI5B,IAAM,IAAI,GAAI,GAAG,CAAC,IAAiB,CAAC,OAAO,EAAE,CAAC;wBAC9C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;qBACtD;yBAAM;wBAIL,IAAM,SAAO,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC7B,IAAM,MAAI,GAAG,SAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;4BAC5D,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC;yBAC7D,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5B,GAAG,CAAC,OAAO,CAAC,SAAO,CAAC,UAAU,CAAC,MAAI,CAAC,CAAC,CAAC;wBAItC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,UAAC,KAAY;4BAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;4BAC5B,IAAM,OAAO,GAAG,UAAU,CAAC,cAAM,OAAA,SAAO,CAAC,QAAQ,CAAC,MAAI,EAAE,SAAS,EAAE,OAAO,CAAC,EAA1C,CAA0C,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;4BAC7F,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BACnC,KAAK,CAAC,eAAe,EAAE,CAAC;wBAC1B,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,eAAe,CAAC,UAAU,EAAG,UAAC,KAAY;4BAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;4BAC5B,IAAM,OAAO,GAAG,UAAU,CAAC,cAAM,OAAA,SAAO,CAAC,QAAQ,CAAC,MAAI,EAAE,SAAS,EAAE,EAAE,CAAC,EAArC,CAAqC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;4BACzF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BACnC,KAAK,CAAC,eAAe,EAAE,CAAC;wBAC1B,CAAC,CAAC,CAAC;qBACJ;gBACH,CAAC,EAAE,wBAAW,CAAC,CAAC;QAEhB,CAAC,YAAY,EAAE,CAAC,UAAC,IAAI,EAAE,IAAI;oBACzB,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,CAAC,GAAG;wBAAE,OAAO;oBACjB,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBAC5B,IAAM,SAAO,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC7B,IAAM,MAAI,GAAI,GAAG,CAAC,IAAiB,CAAC,OAAO,EAAE,CAAC;wBAC9C,SAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,MAAI,CAAC,CAAC;wBAIrD,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,UAAC,KAAY;4BAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;gCACxB,IAAM,IAAI,GAAG,SAAO,CAAC,IAAI,CAAC,SAAO,CAAC,QAAQ,CAAC,CAAC;gCAC5C,IAAI,CAAC,MAAM,GAAG,SAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC;6BACpF;4BACD,KAAK,CAAC,eAAe,EAAE,CAAC;wBAC1B,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAC,KAAY;4BAC5C,IAAI,IAAI,CAAC,MAAM,EAAE;gCACf,SAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;6BACpB;4BACD,KAAK,CAAC,eAAe,EAAE,CAAC;wBAC1B,CAAC,CAAC,CAAC;qBACJ;gBACH,CAAC,EAAE;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC,CAAC;KAEwC,CAAC,CAAC;IAqBjD,mBAAC;CAAA,AAvKD,CACA,IAAA,+BAAkB,EAA+D,yBAAY,CAAC,GAsK7F;AAvKY,oCAAY"}