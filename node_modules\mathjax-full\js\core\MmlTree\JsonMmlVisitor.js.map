{"version": 3, "file": "JsonMmlVisitor.js", "sourceRoot": "", "sources": ["../../../ts/core/MmlTree/JsonMmlVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,iDAA2C;AAgC3C;IAAoC,kCAAU;IAA9C;;IA8FA,CAAC;IAvFQ,kCAAS,GAAhB,UAAiB,IAAa;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAMM,sCAAa,GAApB,UAAqB,IAAc;QACjC,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,EAAC,CAAC;IACjD,CAAC;IAMM,qCAAY,GAAnB,UAAoB,IAAa;QAC/B,OAAO,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAC,CAAC;IAC/C,CAAC;IAYM,qCAAY,GAAnB,UAAoB,IAAa;QAC/B,IAAI,IAAI,GAAY;YAClB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;YACzC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAClC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACpC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;SACnC,CAAC;QACF,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACzB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAMM,oCAAW,GAAlB,UAAmB,IAAa;;QAC9B,IAAI,QAAQ,GAAG,EAAE,CAAC;;YAClB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aACtC;;;;;;;;;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAMM,sCAAa,GAApB,UAAqB,IAAa;QAChC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC/D,CAAC;IAMM,qCAAY,GAAnB,UAAoB,IAAa;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;IAC9D,CAAC;IAMM,sCAAa,GAApB,UAAqB,IAAa;QAChC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACpD,CAAC;IAEH,qBAAC;AAAD,CAAC,AA9FD,CAAoC,0BAAU,GA8F7C;AA9FY,wCAAc"}