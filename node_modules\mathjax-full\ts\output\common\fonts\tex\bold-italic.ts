/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import {CharMap, CharOptions} from '../../FontData.js';

export const boldItalic: CharMap<CharOptions> = {
    0x2F: [.711, .21, .894],
    0x131: [.452, .008, .394, {sk: .0319}],
    0x237: [.451, .201, .439, {sk: .0958}],
    0x2044: [.711, .21, .894],
    0x2206: [.711, 0, .958, {sk: .192}],
    0x29F8: [.711, .21, .894],
};
