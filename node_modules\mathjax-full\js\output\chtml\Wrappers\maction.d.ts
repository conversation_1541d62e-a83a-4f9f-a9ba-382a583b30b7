import { CHTMLWrapper, CHTMLConstructor } from '../Wrapper.js';
import { EventHandler } from '../../common/Wrappers/maction.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmaction_base: import("../../common/Wrappers/maction.js").MactionConstructor<CHTMLWrapper<any, any, any>> & CHTMLConstructor<any, any, any>;
export declare class CHTMLmaction<N, T, D> extends CHTMLmaction_base {
    static kind: string;
    static styles: StyleList;
    static actions: Map<string, [import("../../common/Wrappers/maction.js").ActionHandler<CHTMLmaction<any, any, any>>, import("../../common/Wrappers/maction.js").ActionData]>;
    toCHTML(parent: N): void;
    setEventHandler(type: string, handler: <PERSON>Handler): void;
}
export {};
