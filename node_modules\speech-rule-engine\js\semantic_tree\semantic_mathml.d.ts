import { SemanticAbstractParser } from './semantic_parser.js';
export declare class SemanticMathml extends SemanticAbstractParser<Element> {
    private parseMap_;
    private static getAttribute_;
    constructor();
    parse(mml: Element): any;
    private semantics_;
    private rows_;
    private fraction_;
    private limits_;
    private root_;
    private sqrt_;
    private table_;
    private tableRow_;
    private tableLabeledRow_;
    private tableCell_;
    private space_;
    private text_;
    private identifier_;
    private number_;
    private operator_;
    private fenced_;
    private enclosed_;
    private multiscripts_;
    private empty_;
    private action_;
    private dummy_;
    private leaf_;
}
