{"version": 3, "file": "TextMacrosMappings.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/textmacros/TextMacrosMappings.ts"], "names": [], "mappings": ";;AAwBA,gDAAqD;AACrD,sDAA+C;AAC/C,+DAAyD;AACzD,uDAAmD;AAKnD,IAAI,uBAAQ,CAAC,cAAc,EAAE;IAC3B,GAAG,EAAW,MAAM;IACpB,GAAG,EAAW,SAAS;IACvB,GAAG,EAAW,cAAc;IAC5B,GAAG,EAAW,cAAc;IAC5B,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,OAAO;IACrB,GAAG,EAAW,OAAO;IACrB,IAAI,EAAU,OAAO;IACrB,IAAI,EAAU,OAAO;IACrB,IAAI,EAAU,OAAO;IACrB,QAAQ,EAAM,OAAO;IACrB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,YAAY;IAC1B,GAAG,EAAW,WAAW;IACzB,IAAI,EAAU,YAAY;CAC3B,EAAE,wCAAiB,CAAC,CAAC;AAKtB,IAAI,yBAAU,CAAC,aAAa,EAAE;IAC5B,GAAG,EAAW,MAAM;IAEpB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,GAAG,EAAW,WAAW;IACzB,IAAI,EAAU,WAAW;IAEzB,IAAI,EAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,QAAQ,EAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,QAAQ,EAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC7B,IAAI,EAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC7B,GAAG,EAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,GAAG,EAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAElC,IAAI,EAAU,MAAM;IACpB,EAAE,EAAY,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC;IACrD,GAAG,EAAW,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC;IACrD,QAAQ,EAAM,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,QAAQ,CAAC;IACvD,GAAG,EAAW,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,YAAY,CAAC;IAC3D,EAAE,EAAY,CAAC,SAAS,EAAE,aAAa,CAAC;IACxC,EAAE,EAAY,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,IAAI,CAAC;IACnD,MAAM,EAAQ,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,YAAY,CAAC;IAC3D,GAAG,EAAW,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC;IACrD,IAAI,EAAU,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,OAAO,CAAC;IACtD,EAAE,EAAY,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,SAAS,CAAC;IACxD,EAAE,EAAY,CAAC,SAAS,EAAE,6BAAW,CAAC,OAAO,CAAC,SAAS,CAAC;IAExD,IAAI,EAAU,CAAC,SAAS,EAAE,GAAG,CAAC;IAC9B,IAAI,EAAU,CAAC,SAAS,EAAE,GAAG,CAAC;IAC9B,UAAU,EAAI,CAAC,SAAS,EAAE,GAAG,CAAC;IAC9B,KAAK,EAAS,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/B,UAAU,EAAI,CAAC,SAAS,EAAE,GAAG,CAAC;IAC9B,KAAK,EAAS,CAAC,SAAS,EAAE,GAAG,CAAC;IAC9B,KAAK,EAAS,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/B,KAAK,EAAS,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/B,IAAI,EAAU,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/B,IAAI,EAAU,CAAC,SAAS,EAAE,IAAI,CAAC;IAE/B,GAAG,EAAW,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;IAC3C,UAAU,EAAI,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACvC,MAAM,EAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IAEvC,MAAM,EAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,OAAO,EAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAClC,CAAC,EAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAElC,GAAG,EAAW,CAAC,QAAQ,EAAE,sBAAS,CAAC,aAAa,CAAC;IACjD,GAAG,EAAW,CAAC,QAAQ,EAAE,sBAAS,CAAC,eAAe,CAAC;IACnD,GAAG,EAAW,CAAC,QAAQ,EAAE,sBAAS,CAAC,eAAe,CAAC;IACnD,GAAG,EAAW,CAAC,QAAQ,EAAE,sBAAS,CAAC,cAAc,CAAC;IAClD,GAAG,EAAW,CAAC,QAAQ,EAAE,sBAAS,CAAC,qBAAqB,CAAC;IACzD,OAAO,EAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAC5B,IAAI,EAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3B,KAAK,EAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3B,SAAS,EAAK,CAAC,QAAQ,EAAE,sBAAS,CAAC,aAAa,CAAC;IACjD,YAAY,EAAE,CAAC,QAAQ,EAAE,sBAAS,CAAC,qBAAqB,CAAC;IAEzD,KAAK,EAAS,OAAO;IACrB,MAAM,EAAQ,OAAO;IACrB,IAAI,EAAU,OAAO;IACrB,KAAK,EAAS,OAAO;IACrB,MAAM,EAAQ,OAAO;IACrB,KAAK,EAAS,OAAO;IACrB,IAAI,EAAU,MAAM;IACpB,IAAI,EAAU,CAAC,MAAM,CAAC;IACtB,KAAK,EAAS,CAAC,MAAM,EAAE,OAAO,CAAC;IAE/B,KAAK,EAAS,eAAe;IAC7B,SAAS,EAAK,eAAe;IAC7B,QAAQ,EAAM,eAAe;IAC7B,SAAS,EAAK,eAAe;IAC7B,IAAI,EAAU,eAAe;IAC7B,KAAK,EAAS,eAAe;IAC7B,KAAK,EAAS,eAAe;IAC7B,KAAK,EAAS,eAAe;IAC7B,OAAO,EAAO,eAAe;IAE7B,GAAG,EAAW,CAAC,WAAW,EAAE,KAAK,CAAC;IAClC,KAAK,EAAS,CAAC,WAAW,EAAE,IAAI,CAAC;CAElC,EAAE,wCAAiB,CAAC,CAAC"}