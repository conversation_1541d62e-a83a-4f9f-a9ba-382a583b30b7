import { PropertyList } from '../../Tree/Node.js';
import { MmlNode, AbstractMmlNode, AttributeList } from '../MmlNode.js';
export declare class MmlMsqrt extends AbstractMmlNode {
    static defaults: PropertyList;
    protected texclass: number;
    get kind(): string;
    get arity(): number;
    get linebreakContainer(): boolean;
    setTeXclass(prev: MmlNode): this;
    protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, _prime: boolean): void;
}
