{"version": 3, "file": "maction.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/maction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAuD;AAOvD;IAAgC,8BAAe;IAA/C;;IAsGA,CAAC;IAxFC,sBAAW,4BAAI;aAAf;YACE,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAMD,sBAAW,6BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,gCAAQ;aAAnB;YACE,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAW,CAAC;YAC7D,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;;;OAAA;IAKD,sBAAW,qCAAa;aAAxB;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QACrC,CAAC;;;OAAA;IAKD,sBAAW,mCAAW;aAAtB;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QACnC,CAAC;;;OAAA;IAKM,yBAAI,GAAX;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAKM,2BAAM,GAAb;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IAChC,CAAC;IAKS,qCAAgB,GAA1B,UAA2B,OAAqB;QAC9C,iBAAM,gBAAgB,YAAC,OAAO,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,QAAQ;YAC9C,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;YAC1D,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACtD,OAAO,UAAU,CAAC,SAAS,CAAC;SAC7B;IACH,CAAC;IAQM,gCAAW,GAAlB,UAAmB,IAAa;QAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACzE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,wCAAmB,GAA1B;QACE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAY,GAAG,CAAC,CAAC,CAAC;QAC9E,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACtC,SAAS,GAAG,CAAC,CAAC;SACf;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IA/Fa,mBAAQ,yBACjB,4BAAe,CAAC,QAAQ,KAC3B,UAAU,EAAE,QAAQ,EACpB,SAAS,EAAE,CAAC,IACZ;IA6FJ,iBAAC;CAAA,AAtGD,CAAgC,4BAAe,GAsG9C;AAtGY,gCAAU"}