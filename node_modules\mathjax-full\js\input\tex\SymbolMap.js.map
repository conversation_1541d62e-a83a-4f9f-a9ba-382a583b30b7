{"version": 3, "file": "SymbolMap.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/SymbolMap.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yCAA0C;AAC1C,iDAA2C;AAkD3C,SAAgB,WAAW,CAAC,MAAmB;IAC7C,OAAO,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AAC3C,CAAC;AAFD,kCAEC;AAMD;IAQE,2BAAoB,KAAa,EAAU,OAAoB;QAA3C,UAAK,GAAL,KAAK,CAAQ;QAAU,YAAO,GAAP,OAAO,CAAa;QAC7D,0BAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAMD,sBAAW,mCAAI;aAAf;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAYM,qCAAS,GAAhB,UAAiB,MAAc;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACpD,CAAC;IAMM,iCAAK,GAAZ,UAAa,EAAyB;YAAzB,KAAA,aAAyB,EAAxB,GAAG,QAAA,EAAE,MAAM,QAAA;QACvB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,MAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAGD,sBAAW,qCAAM;aAIjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;aAND,UAAkB,MAAmB;YACnC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACxB,CAAC;;;OAAA;IAaH,wBAAC;AAAD,CAAC,AA5DD,IA4DC;AA5DqB,8CAAiB;AAmEvC;IAA+B,6BAAyB;IAStD,mBAAY,IAAY,EAAE,MAAmB,EAAU,OAAe;QAAtE,YACE,kBAAM,IAAI,EAAE,MAAM,CAAC,SACpB;QAFsD,aAAO,GAAP,OAAO,CAAQ;;IAEtE,CAAC;IAMM,4BAAQ,GAAf,UAAgB,MAAc;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAMM,0BAAM,GAAb,UAAc,MAAc;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,CAAC;IAEH,gBAAC;AAAD,CAAC,AA7BD,CAA+B,iBAAiB,GA6B/C;AA7BY,8BAAS;AAsCtB;IAAkD,oCAAoB;IAAtE;QAAA,qEAmCC;QAjCS,SAAG,GAAmB,IAAI,GAAG,EAAa,CAAC;;IAiCrD,CAAC;IA5BQ,iCAAM,GAAb,UAAc,MAAc;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAKM,mCAAQ,GAAf,UAAgB,MAAc;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAOM,8BAAG,GAAV,UAAW,MAAc,EAAE,MAAS;QAClC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/B,CAAC;IAMM,iCAAM,GAAb,UAAc,MAAc;QAC1B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEH,uBAAC;AAAD,CAAC,AAnCD,CAAkD,iBAAiB,GAmClE;AAnCqB,4CAAgB;AA4CtC;IAAkC,gCAAwB;IAQxD,sBAAY,IAAY,EAAE,MAAmB,EACjC,IAAsD;;QADlE,YAEE,kBAAM,IAAI,EAAE,MAAM,CAAC,SAOpB;;YANC,KAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,GAAG,WAAA;gBACZ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClB,IAAA,KAAA,OAAgB,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAA,EAAnE,IAAI,QAAA,EAAE,KAAK,QAAwD,CAAC;gBACzE,IAAI,SAAS,GAAG,IAAI,kBAAM,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7C,KAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;aAC1B;;;;;;;;;;IACH,CAAC;IAEH,mBAAC;AAAD,CAAC,AAnBD,CAAkC,gBAAgB,GAmBjD;AAnBY,oCAAY;AA4BzB;IAAkC,gCAAY;IAA9C;;IASA,CAAC;IAJQ,4BAAK,GAAZ,UAAa,EAAyB;YAAzB,KAAA,aAAyB,EAAxB,GAAG,QAAA,EAAE,MAAM,QAAA;QACvB,OAAO,iBAAM,KAAK,YAAC,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEH,mBAAC;AAAD,CAAC,AATD,CAAkC,YAAY,GAS7C;AATY,oCAAY;AAkBzB;IAA8B,4BAAuB;IASnD,kBAAY,IAAY,EACZ,IAAwC,EACxC,WAAwC;;QAFpD,YAGE,kBAAM,IAAI,EAAE,IAAI,CAAC,SAOlB;;YANC,KAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,GAAG,WAAA;gBACZ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClB,IAAA,KAAA,OAAmB,CAAC,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA,EAAhE,IAAI,QAAA,EAAK,KAAK,cAAkD,CAAC;gBACtE,IAAI,SAAS,GAAG,IAAI,iBAAK,CAAC,GAAG,EAAE,WAAW,CAAC,IAAc,CAAC,EAAE,KAAK,CAAC,CAAC;gBACnE,KAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;aAC1B;;;;;;;;;;IACH,CAAC;IAMM,4BAAS,GAAhB,UAAiB,MAAc;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACnC,CAAC;IAMM,wBAAK,GAAZ,UAAa,EAAyB;YAAzB,KAAA,aAAyB,EAAxB,GAAG,QAAA,EAAE,MAAM,QAAA;QACvB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,WAAW,CAAC,MAAM,8BAAC,GAAG,EAAE,KAAK,CAAC,MAAM,UAAK,KAAK,CAAC,IAAI,WAAE,CAAC;IAC/D,CAAC;IAEH,eAAC;AAAD,CAAC,AA3CD,CAA8B,gBAAgB,GA2C7C;AA3CY,4BAAQ;AAoDrB;IAAgC,8BAAQ;IAAxC;;IAkBA,CAAC;IAbQ,0BAAK,GAAZ,UAAa,EAAyB;YAAzB,KAAA,aAAyB,EAAxB,GAAG,QAAA,EAAE,MAAM,QAAA;QACvB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC;QAChC,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC;QAC9B,IAAI,MAAM,GAAG,MAAM,8BAAC,GAAG,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,UAAK,KAAK,CAAC,IAAI,UAAC,CAAC;QAC7D,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;QAC5B,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAEH,iBAAC;AAAD,CAAC,AAlBD,CAAgC,QAAQ,GAkBvC;AAlBY,gCAAU;AA6BvB;IAAoC,kCAAQ;IAU1C,wBAAY,IAAY,EACZ,MAAmB,EACnB,IAAwC,EACxC,WAAwC;QAHpD,YAIE,kBAAM,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,SAE/B;QADC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACvB,CAAC;IAMM,8BAAK,GAAZ,UAAa,EAAyB;YAAzB,KAAA,aAAyB,EAAxB,GAAG,QAAA,EAAE,MAAM,QAAA;QACvB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEH,qBAAC;AAAD,CAAC,AA/BD,CAAoC,QAAQ,GA+B3C;AA/BY,wCAAc"}