(function(w,s,u,t){"use strict";const m=u.default||u;function p(y,R,A={}){const{validateOption:F={},immediate:o=!0,manual:i=!1}=A,c=s.toRef(y),a=t.shallowRef(null),l=t.shallowRef(!0),r=t.shallowRef(!o||i),d=t.computed(()=>{var e;return((e=a.value)==null?void 0:e.errors)||[]}),f=t.computed(()=>{var e;return((e=a.value)==null?void 0:e.fields)||{}}),v=t.computed(()=>new m(t.toValue(R))),h=async()=>{l.value=!1,r.value=!1;try{await v.value.validate(c.value,F),r.value=!0,a.value=null}catch(e){a.value=e}finally{l.value=!0}return{pass:r.value,errorInfo:a.value,errors:d.value,errorFields:f.value}};i||t.watch([c,v],()=>h(),{immediate:o,deep:!0});const V={isFinished:l,pass:r,errors:d,errorInfo:a,errorFields:f,execute:h};function U(){return new Promise((e,n)=>{s.until(l).toBe(!0).then(()=>e(V)).catch(I=>n(I))})}return{...V,then(e,n){return U().then(e,n)}}}w.useAsyncValidator=p})(this.VueUse=this.VueUse||{},VueUse,AsyncValidator,Vue);
