export declare function toArray(nodeList: NodeList | NamedNodeMap): any[];
export declare function parseInput(input: string): Element;
export declare enum NodeType {
    ELEMENT_NODE = 1,
    ATTRIBUTE_NODE = 2,
    TEXT_NODE = 3,
    CDATA_SECTION_NODE = 4,
    ENTITY_REFERENCE_NODE = 5,
    ENTITY_NODE = 6,
    PROCESSING_INSTRUCTION_NODE = 7,
    COMMENT_NODE = 8,
    DOCUMENT_NODE = 9,
    DOCUMENT_TYPE_NODE = 10,
    DOCUMENT_FRAGMENT_NODE = 11,
    NOTATION_NODE = 12
}
export declare function replaceNode(oldNode: Node, newNode: Node): void;
export declare function createElement(tag: string): Element;
export declare function createElementNS(url: string, tag: string): Element;
export declare function createTextNode(content: string): Text;
export declare function formatXml(xml: string): string;
export declare function querySelectorAllByAttr(node: Element, attr: string): Element[];
export declare function querySelectorAllByAttrValue(node: Element, attr: string, value: string): Element[];
export declare function querySelectorAll(node: Element, tag: string): Element[];
export declare function tagName(node: Element): string;
export declare function cloneNode(node: Element): Element;
export declare function serializeXml(node: Element): string;
