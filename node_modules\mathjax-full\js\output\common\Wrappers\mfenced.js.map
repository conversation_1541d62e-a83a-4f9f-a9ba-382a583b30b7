{"version": 3, "file": "mfenced.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mfenced.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,SAAgB,kBAAkB,CAA+B,IAAO;IAEtE;QAAqB,2BAAI;QAWvB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAGd;YAVM,UAAI,GAAuB,IAAI,CAAC;YAQrC,KAAI,CAAC,UAAU,EAAE,CAAC;YAClB,KAAI,CAAC,eAAe,EAAE,CAAC;;QACzB,CAAC;QAKM,4BAAU,GAAjB;YACE,IAAM,UAAU,GAAI,IAAI,CAAC,IAAwB,CAAC,OAAO,CAAC;YAC1D,IAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAuB,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAC1B,CAAC;QAOM,iCAAe,GAAtB;;YACE,IAAM,OAAO,GAAG,IAAI,CAAC,IAAkB,CAAC;YACxC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1C;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;;gBACV,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,KAAK,WAAA;oBACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC7B;;;;;;;;;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAOM,uBAAK,GAAZ,UAAa,IAAa;YACxB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAClB,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9B,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,CAAC;QAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YACvD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAC1C,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAEH,cAAC;IAAD,CAAC,AArEM,CAAc,IAAI,GAqEvB;AAEJ,CAAC;AAzED,gDAyEC"}