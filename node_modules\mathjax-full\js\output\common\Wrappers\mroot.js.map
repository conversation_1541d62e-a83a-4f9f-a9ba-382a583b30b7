{"version": 3, "file": "mroot.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mroot.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AA8CA,SAAgB,gBAAgB,CAA6B,IAAO;IAElE;QAAqB,2BAAI;QAAlB;;QAqDP,CAAC;QAhDC,sBAAI,yBAAI;iBAAR;gBACE,OAAO,CAAC,CAAC;YACX,CAAC;;;WAAA;QAKD,sBAAI,yBAAI;iBAAR;gBACE,OAAO,CAAC,CAAC;YACX,CAAC;;;WAAA;QAKM,iCAAe,GAAtB,UAAuB,IAAU,EAAE,IAAU,EAAE,CAAS;YACtD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;YACvD,IAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;QAKM,+BAAa,GAApB,UAAqB,IAAU,EAAE,CAAS;YACxC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAa,CAAC;YACpD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;YACvD,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAC3C,IAAA,CAAC,GAAY,IAAI,EAAhB,EAAE,MAAM,GAAI,IAAI,OAAR,CAAS;YACzB,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;YACvC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9B,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACpD,IAAM,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC;YAC9B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACpB,CAAC;QASM,4BAAU,GAAjB,UAAkB,IAAU,EAAE,IAAU,EAAE,IAAY,EAAE,CAAS;YAC/D,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YAC1B,IAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/C,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;QAEH,cAAC;IAAD,CAAC,AArDM,CAAc,IAAI,GAqDvB;AAEJ,CAAC;AAzDD,4CAyDC"}