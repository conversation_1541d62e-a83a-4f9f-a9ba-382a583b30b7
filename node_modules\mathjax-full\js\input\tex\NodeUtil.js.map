{"version": 3, "file": "NodeUtil.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/NodeUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4DAAgH;AAChH,2DAAwD;AAMxD,IAAU,QAAQ,CAgRjB;AAhRD,WAAU,QAAQ;IAEhB,IAAM,KAAK,GAAyB,IAAI,GAAG,CAAC;QAC1C,CAAC,QAAQ,EAAE,IAAI,CAAC;QAChB,CAAC,MAAM,EAAE,IAAI,CAAC;QACd,CAAC,YAAY,EAAE,IAAI,CAAC;QACpB,CAAC,UAAU,EAAE,IAAI,CAAC;QAClB,CAAC,eAAe,EAAE,IAAI,CAAC;QACvB,CAAC,WAAW,EAAE,IAAI,CAAC;QACnB,CAAC,aAAa,EAAE,IAAI,CAAC;QACrB,CAAC,YAAY,EAAE,IAAI,CAAC;QACpB,CAAC,YAAY,EAAE,IAAI,CAAC;QACpB,CAAC,MAAM,EAAE,IAAI,CAAC;QACd,CAAC,OAAO,EAAE,IAAI,CAAC;KAChB,CAAC,CAAC;IAQH,SAAgB,YAAY,CAAC,IAAY;QACvC,OAAO,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAFe,qBAAY,eAE3B,CAAA;IAQD,SAAgB,WAAW,CAAC,IAAa;QACvC,OAAQ,IAAI,CAAC,UAAwB,CAAC;IACxC,CAAC;IAFe,oBAAW,cAE1B,CAAA;IAQD,SAAgB,OAAO,CAAC,IAAc;QACpC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAFe,gBAAO,UAEtB,CAAA;IAQD,SAAgB,cAAc,CAAC,IAAa,EAAE,QAAmB;;;YAC/D,KAAkB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;gBAAvB,IAAI,KAAK,qBAAA;gBACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACzB;;;;;;;;;IACH,CAAC;IAJe,uBAAc,iBAI7B,CAAA;IASD,SAAgB,YAAY,CAAC,IAAa,EAAE,SAAiB,EAAE,KAAW;QACxE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAFe,qBAAY,eAE3B,CAAA;IASD,SAAgB,WAAW,CAAC,IAAa,EAAE,QAAgB,EAAE,KAAW;QACtE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;IAFe,oBAAW,cAE1B,CAAA;IAQD,SAAgB,aAAa,CAAC,IAAa,EAAE,UAAwB;;;YACnE,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,MAAI,WAAA;gBACb,IAAI,KAAK,GAAG,UAAU,CAAC,MAAI,CAAC,CAAC;gBAC7B,IAAI,MAAI,KAAK,UAAU,EAAE;oBACvB,IAAI,CAAC,QAAQ,GAAI,KAAgB,CAAC;oBAClC,IAAI,CAAC,WAAW,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;iBAC/B;qBAAM,IAAI,MAAI,KAAK,eAAe,EAAE;oBACnC,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;oBACzC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;wBAC9C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;qBAC7C;iBACF;qBAAM,IAAI,MAAI,KAAK,UAAU,EAAE;iBAE/B;qBAAM,IAAI,KAAK,CAAC,GAAG,CAAC,MAAI,CAAC,EAAE;oBAC1B,IAAI,CAAC,WAAW,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;iBAC/B;qBAAM;oBACL,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;iBAClC;aACF;;;;;;;;;IACH,CAAC;IAnBe,sBAAa,gBAmB5B,CAAA;IASD,SAAgB,WAAW,CAAC,IAAa,EAAE,QAAgB;QACzD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAFe,oBAAW,cAE1B,CAAA;IASD,SAAgB,YAAY,CAAC,IAAa,EAAE,IAAY;QACtD,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAFe,qBAAY,eAE3B,CAAA;IAQD,SAAgB,gBAAgB,CAAC,IAAa;QAAE,oBAAuB;aAAvB,UAAuB,EAAvB,qBAAuB,EAAvB,IAAuB;YAAvB,mCAAuB;;QACrE,IAAI,CAAC,cAAc,OAAnB,IAAI,2BAAmB,UAAU,WAAE;IACrC,CAAC;IAFe,yBAAgB,mBAE/B,CAAA;IASD,SAAgB,UAAU,CAAC,IAAa,EAAE,QAAgB;QACxD,OAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAa,CAAC;IAChD,CAAC;IAFe,mBAAU,aAEzB,CAAA;IASD,SAAgB,QAAQ,CAAC,IAAa,EAAE,QAAgB,EAAE,KAAc;QACtE,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;QAC3B,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;SACrB;IACH,CAAC;IANe,iBAAQ,WAMvB,CAAA;IAQD,SAAgB,YAAY,CAAC,OAAgB,EAAE,OAAgB;QAC7D,IAAI,QAAQ,GAAG,OAAO,CAAC,UAAoC,CAAC;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SACnC;IACH,CAAC;IALe,qBAAY,eAK3B,CAAA;IAQD,SAAgB,cAAc,CAAC,OAAgB,EAAE,OAAgB;QAC/D,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACrD,CAAC;IAHe,uBAAc,iBAG7B,CAAA;IASD,SAAgB,MAAM,CAAC,IAAa,EAAE,IAAY;QAChD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAFe,eAAM,SAErB,CAAA;IAQD,SAAgB,aAAa,CAAC,IAAa;QACzC,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAFe,sBAAa,gBAE5B,CAAA;IAQD,SAAgB,WAAW,CAAC,IAAa;QACvC,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAFe,oBAAW,cAE1B,CAAA;IAQD,SAAgB,SAAS,CAAC,IAAa;QACrC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAFe,kBAAS,YAExB,CAAA;IAQD,SAAgB,MAAM,CAAC,IAAS;QAC9B,OAAO,IAAI,YAAY,4BAAe,IAAI,IAAI,YAAY,iCAAoB,CAAC;IACjF,CAAC;IAFe,eAAM,SAErB,CAAA;IAQD,SAAgB,UAAU,CAAC,IAAa;QACtC,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAFe,mBAAU,aAEzB,CAAA;IASD,SAAgB,OAAO,CAAC,IAAa;;QACnC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,EAAE,GAAG,IAAa,CAAC;QACvB,IAAI,KAAK,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;;YAC1B,KAAiB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAAnB,IAAI,IAAI,kBAAA;gBACX,IAAI,MAAM,GAAG,aAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC/C,IAAI,MAAM,EAAE;oBACV,OAAO,MAAM,CAAC;iBACf;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAbe,gBAAO,UAatB,CAAA;AAEH,CAAC,EAhRS,QAAQ,KAAR,QAAQ,QAgRjB;AAED,kBAAe,QAAQ,CAAC"}