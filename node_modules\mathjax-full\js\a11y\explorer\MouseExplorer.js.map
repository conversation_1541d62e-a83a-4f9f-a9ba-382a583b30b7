{"version": 3, "file": "MouseExplorer.js", "sourceRoot": "", "sources": ["../../../ts/a11y/explorer/MouseExplorer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,yCAA8D;AAC9D,6CAAyD;AACzD,qBAAmB;AA+BnB;IAAuD,yCAAmB;IAA1E;QAAA,qEA0BC;QArBW,YAAM,GACd,iBAAM,MAAM,YAAE,CAAC,MAAM,CAAC;YACpB,CAAC,WAAW,EAAE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;YACxC,CAAC,UAAU,EAAE,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;SACvC,CAAC,CAAC;;IAiBP,CAAC;IAZQ,yCAAS,GAAhB,UAAiB,MAAkB;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAMM,wCAAQ,GAAf,UAAgB,MAAkB;QAChC,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEH,4BAAC;AAAD,CAAC,AA1BD,CAAuD,8BAAgB,GA0BtE;AA1BqB,sDAAqB;AAkC3C;IAAyC,2BAAwB;IAsB/D,iBAA6B,QAAsB,EACnB,MAAiB,EACjB,IAAiB,EACjB,SAAyC,EACzC,UAAoC;QAJpE,YAKE,kBAAM,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,SAC9B;QAN4B,cAAQ,GAAR,QAAQ,CAAc;QACnB,YAAM,GAAN,MAAM,CAAW;QACjB,UAAI,GAAJ,IAAI,CAAa;QACjB,eAAS,GAAT,SAAS,CAAgC;QACzC,gBAAU,GAAV,UAAU,CAA0B;;IAEpE,CAAC;IAMM,0BAAQ,GAAf,UAAgB,KAAiB;QAC/B,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/B,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACnC,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACnB,iBAAM,QAAQ,YAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAMM,2BAAS,GAAhB,UAAiB,KAAiB;QAChC,iBAAM,SAAS,YAAC,KAAK,CAAC,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAqB,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,IAAA,KAAA,OAAe,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAA,EAAlC,IAAI,QAAA,EAAE,IAAI,QAAwB,CAAC;QACxC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAaM,yBAAO,GAAd,UAAe,IAAiB;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YACjC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;YACD,IAAI,GAAG,IAAI,CAAC,UAAyB,CAAC;SACvC;QACD,IAAI,GAAG,QAAQ,CAAC;QAChB,OAAO,IAAI,EAAE;YACX,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBACxB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;YACD,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAgB,CAAC;YAC9C,IAAI,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;gBAC5C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAgB,CAAC,CAAC,CAAC,KAAK,CAAC;SAC3C;QACD,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACtB,CAAC;IAEH,cAAC;AAAD,CAAC,AA7FD,CAAyC,qBAAqB,GA6F7D;AA7FqB,0BAAO;AAqG7B;IAAkC,gCAAe;IAAjD;;IAAoD,CAAC;IAAD,mBAAC;AAAD,CAAC,AAArD,CAAkC,OAAO,GAAY;AAAxC,oCAAY;AAQzB;IAAoC,kCAAoB;IAAxD;;IAA2D,CAAC;IAAD,qBAAC;AAAD,CAAC,AAA5D,CAAoC,OAAO,GAAiB;AAA/C,wCAAc;AAQ3B;IAAkC,gCAAa;IAK7C,sBACS,QAAsB,EAC7B,OAAY,EACF,IAAiB;QAH7B,YAIE,kBAAM,QAAQ,EAAE,IAAI,uBAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,EACzC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAjC,CAAiC,EACtC,cAAO,CAAC,CAAC,SAChB;QANQ,cAAQ,GAAR,QAAQ,CAAc;QAEnB,UAAI,GAAJ,IAAI,CAAa;;IAI7B,CAAC;IAEH,mBAAC;AAAD,CAAC,AAdD,CAAkC,OAAO,GAcxC;AAdY,oCAAY"}