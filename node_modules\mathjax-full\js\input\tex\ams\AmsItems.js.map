{"version": 3, "file": "AmsItems.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/ams/AmsItems.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,qDAA6D;AAC7D,iEAAwC;AACxC,+DAAsC;AACtC,+DAAsC;AACtC,sDAA+C;AAW/C;IAAkC,gCAAS;IAKzC,sBAAY,OAAY;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAAxC,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IACnE,CAAC;IAMD,sBAAI,8BAAI;aAAR;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAMM,+BAAQ,GAAf;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,sBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAChE;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EACN,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAC,WAAW,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAKM,6BAAM,GAAb;QACE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAEzB,MAAM,IAAI,qBAAQ,CAChB,oBAAoB,EACpB,iEAAiE,EACjE,UAAU,CAAC,CAAC;SACf;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAKM,+BAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,qBAAQ,CAAC,YAAY,CACxB,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;gBACxD,qBAAQ,CAAC,YAAY,CAAC,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACtC,aAAa,EAAE,6BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,qBAAQ,CAAC,YAAY,CACxB,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;gBACxD,qBAAQ,CAAC,YAAY,CAAC,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACtC,aAAa,EAAE,6BAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;aAC/D;YACD,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,GAAG,EAAE;gBACP,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,6BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpF,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EACpB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpE,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;aAC5B;SACF;QACD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;IACxC,CAAC;IACH,mBAAC;AAAD,CAAC,AA/ED,CAAkC,wBAAS,GA+E1C;AA/EY,oCAAY;AAoFzB;IAAiC,+BAAY;IAa3C,qBAAY,OAAY,EAAS,IAAY,EAAS,QAAiB,EACpD,MAAe,EAAS,MAAe;QAD1D,YAEE,kBAAM,OAAO,CAAC,SAEf;QAJgC,UAAI,GAAJ,IAAI,CAAQ;QAAS,cAAQ,GAAR,QAAQ,CAAS;QACpD,YAAM,GAAN,MAAM,CAAS;QAAS,YAAM,GAAN,MAAM,CAAS;QAExD,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;;IAClE,CAAC;IAZD,sBAAI,6BAAI;aAAR;YACE,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAeM,8BAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAW,CAAC;QACjD,IAAI,CAAC,CAAC;YAAE,OAAO;QACf,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,qBAAQ,CAAC,gBAAgB,EAAE,uBAAuB,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/E;IACH,CAAC;IAMM,4BAAM,GAAb;QACE,IAAI,IAAa,CAAC;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAInB,IAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAW,CAAC;QACjD,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SACtC;QAKD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,IAAI;gBAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;aAC3C;SACF;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;SAC/B;QACD,iBAAM,MAAM,WAAE,CAAC;QAKf,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YAClE,IAAM,GAAG,GAAG,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAM,GAAG,cAAI,KAAK,EAAE,CAAC,IAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,EAAC,MAAM,EAAE,SAAS,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzE,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YAC/E,GAAG,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;SAC5B;IACH,CAAC;IAMM,8BAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YAKf,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;gBACpB,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC1B,OAAO,GAAG,CAAC,KAAK,CAAC;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;aAChC;SACF;IACH,CAAC;IAEH,kBAAC;AAAD,CAAC,AAnGD,CAAiC,2BAAY,GAmG5C;AAnGY,kCAAW"}