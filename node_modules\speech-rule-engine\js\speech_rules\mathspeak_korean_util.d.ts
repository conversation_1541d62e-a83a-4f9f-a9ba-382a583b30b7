import { Span } from '../audio/span.js';
export declare function nestedFraction(node: Element, expr: string, opt_end?: string): string;
export declare function openingFractionVerbose(node: Element): Span[];
export declare function closingFractionVerbose(node: Element): Span[];
export declare function openingFractionBrief(node: Element): Span[];
export declare function closingFractionBrief(node: Element): Span[];
export declare function openingFractionSbrief(node: Element): Span[];
export declare function closingFractionSbrief(node: Element): Span[];
export declare function overFractionSbrief(node: Element): Span[];
export declare function isSimpleIndex(node: Element): Element[];
export declare function nestedRadical(node: Element, prefix: string, postfix: string): string;
export declare function openingRadicalVerbose(node: Element): Span[];
export declare function closingRadicalVerbose(node: Element): Span[];
export declare function openingRadicalBrief(node: Element): Span[];
export declare function closingRadicalBrief(node: Element): Span[];
export declare function openingRadicalSbrief(node: Element): Span[];
export declare function getRootIndex(node: Element): string;
export declare function indexRadical(node: Element, postfix: string): string;
export declare function indexRadicalVerbose(node: Element): Span[];
export declare function indexRadicalBrief(node: Element): Span[];
export declare function indexRadicalSbrief(node: Element): Span[];
export declare function ordinalConversion(node: Element): Span[];
export declare function decreasedOrdinalConversion(node: Element): Span[];
export declare function listOrdinalConversion(node: Element): Span[];
export declare function checkDepth(node: Element): Element[];
export declare function getDepthValue(node: Element, roleList: string[]): number;
