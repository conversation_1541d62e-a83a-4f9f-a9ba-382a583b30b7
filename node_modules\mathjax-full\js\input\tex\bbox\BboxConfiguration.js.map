{"version": 3, "file": "BboxConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/bbox/BboxConfiguration.ts"], "names": [], "mappings": ";;;;;;AAwBA,wDAAkD;AAElD,gDAA2C;AAE3C,+DAAsC;AAI3B,QAAA,WAAW,GAAgC,EAAE,CAAC;AAOzD,mBAAW,CAAC,IAAI,GAAG,UAAS,MAAiB,EAAE,IAAY;IACzD,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1C,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,GAAG,EAAE,UAAU,EAAE,KAAK,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC5C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC3E,IAAI,KAAK,EAAE;YAET,IAAI,GAAG,EAAE;gBAEP,MAAM,IAAI,qBAAQ,CAAC,sBAAsB,EAAE,0BAA0B,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;aACzF;YACD,IAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,GAAG,EAAE;gBAEP,GAAG,GAAG;oBACJ,MAAM,EAAE,GAAG,GAAG,GAAG;oBACjB,KAAK,EAAE,GAAG,GAAG,GAAG;oBAChB,MAAM,EAAE,GAAG;oBACX,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;iBACrD,CAAC;aACH;SACF;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,4CAA4C,CAAC,EAAE;YAEnE,IAAI,UAAU,EAAE;gBAEd,MAAM,IAAI,qBAAQ,CAAC,sBAAsB,EAAE,0BAA0B,EAClD,YAAY,EAAE,IAAI,CAAC,CAAC;aACxC;YACD,UAAU,GAAG,IAAI,CAAC;SACnB;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YAEnC,IAAI,KAAK,EAAE;gBAET,MAAM,IAAI,qBAAQ,CAAC,sBAAsB,EAAE,0BAA0B,EAClD,OAAO,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;SACzB;aAAM,IAAI,IAAI,KAAK,EAAE,EAAE;YAEtB,MAAM,IAAI,qBAAQ,CAChB,qBAAqB,EACrB,kEAAkE,EAClE,IAAI,CAAC,CAAC;SACT;KACF;IACD,IAAI,GAAG,EAAE;QAEP,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;KACtD;IACD,IAAI,UAAU,IAAI,KAAK,EAAE;QACvB,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,UAAU,EAAE;YAEd,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAC,cAAc,EAAE,UAAU,EAAC,CAAC,CAAC;SAClD;QACD,IAAI,KAAK,EAAE;YAET,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC;SACpC;QACD,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;KACrD;IACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAIF,IAAI,SAAS,GAAG,UAAS,MAAc;IACrC,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,IAAI,WAAW,GAAG,UAAS,GAAW;IACpC,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAGF,IAAI,yBAAU,CAAC,MAAM,EAAE,EAAC,IAAI,EAAE,MAAM,EAAC,EAAE,mBAAW,CAAC,CAAC;AAGvC,QAAA,iBAAiB,GAAG,gCAAa,CAAC,MAAM,CACnD,MAAM,EAAE,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAC,EAAC,CACrC,CAAC"}