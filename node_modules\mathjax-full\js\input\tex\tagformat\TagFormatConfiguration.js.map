{"version": 3, "file": "TagFormatConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/tagformat/TagFormatConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAwBA,wDAAuE;AAEvE,sCAAqD;AAMrD,IAAI,KAAK,GAAG,CAAC,CAAC;AAQd,SAAgB,eAAe,CAAC,MAA2B,EAAE,GAAuB;IAMlF,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3C,IAAI,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QACvD,qBAAW,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1C;IAKD,IAAM,QAAQ,GAAG,qBAAW,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAkC,CAAC;IAStG;QAAwB,6BAAQ;QAAhC;;QA6BA,CAAC;QAxBQ,gCAAY,GAAnB,UAAoB,CAAS;YAC3B,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;QAKM,6BAAS,GAAhB,UAAiB,GAAW;YAC1B,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrD,CAAC;QAKM,4BAAQ,GAAf,UAAgB,EAAU;YACxB,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC;QAKM,6BAAS,GAAhB,UAAiB,EAAU,EAAE,IAAY;YACvC,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,CAAC;QACH,gBAAC;IAAD,CAAC,AA7BD,CAAwB,QAAQ,GA6B/B;IAOD,KAAK,EAAE,CAAC;IACR,IAAM,OAAO,GAAG,aAAa,GAAG,KAAK,CAAC;IAItC,qBAAW,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACpC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;AAC1C,CAAC;AAlED,0CAkEC;AAKY,QAAA,sBAAsB,GAAG,gCAAa,CAAC,MAAM,CACxD,WAAW,EAAE;IACX,MAAM,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC;IAC7B,OAAO,EAAE;QACP,SAAS,EAAE;YACT,MAAM,EAAE,UAAC,CAAS,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY;YACnC,GAAG,EAAK,UAAC,GAAW,IAAK,OAAA,GAAG,GAAG,GAAG,GAAG,GAAG,EAAf,CAAe;YACxC,EAAE,EAAM,UAAC,EAAU,IAAK,OAAA,UAAU,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAnC,CAAmC;YAC3D,GAAG,EAAK,UAAC,EAAU,EAAE,IAAY,IAAK,OAAA,IAAI,GAAG,GAAG,GAAG,kBAAkB,CAAC,EAAE,CAAC,EAAnC,CAAmC;SAC1E;KACF;CACF,CACF,CAAC"}