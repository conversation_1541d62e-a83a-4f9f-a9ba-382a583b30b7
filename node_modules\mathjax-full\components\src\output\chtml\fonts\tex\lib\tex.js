import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/output/chtml/fonts/tex.js';
import * as module2 from '../../../../../../../js/output/chtml/fonts/tex/bold-italic.js';
import * as module3 from '../../../../../../../js/output/chtml/fonts/tex/bold.js';
import * as module4 from '../../../../../../../js/output/chtml/fonts/tex/fraktur-bold.js';
import * as module5 from '../../../../../../../js/output/chtml/fonts/tex/fraktur.js';
import * as module6 from '../../../../../../../js/output/chtml/fonts/tex/italic.js';
import * as module7 from '../../../../../../../js/output/chtml/fonts/tex/largeop.js';
import * as module8 from '../../../../../../../js/output/chtml/fonts/tex/monospace.js';
import * as module9 from '../../../../../../../js/output/chtml/fonts/tex/normal.js';
import * as module10 from '../../../../../../../js/output/chtml/fonts/tex/sans-serif-bold-italic.js';
import * as module11 from '../../../../../../../js/output/chtml/fonts/tex/sans-serif-bold.js';
import * as module12 from '../../../../../../../js/output/chtml/fonts/tex/sans-serif-italic.js';
import * as module13 from '../../../../../../../js/output/chtml/fonts/tex/sans-serif.js';
import * as module14 from '../../../../../../../js/output/chtml/fonts/tex/smallop.js';
import * as module15 from '../../../../../../../js/output/chtml/fonts/tex/tex-calligraphic-bold.js';
import * as module16 from '../../../../../../../js/output/chtml/fonts/tex/tex-size3.js';
import * as module17 from '../../../../../../../js/output/chtml/fonts/tex/tex-size4.js';
import * as module18 from '../../../../../../../js/output/chtml/fonts/tex/tex-variant.js';
import * as module19 from '../../../../../../../js/output/common/fonts/tex/bold-italic.js';
import * as module20 from '../../../../../../../js/output/common/fonts/tex/bold.js';
import * as module21 from '../../../../../../../js/output/common/fonts/tex/delimiters.js';
import * as module22 from '../../../../../../../js/output/common/fonts/tex/double-struck.js';
import * as module23 from '../../../../../../../js/output/common/fonts/tex/fraktur-bold.js';
import * as module24 from '../../../../../../../js/output/common/fonts/tex/fraktur.js';
import * as module25 from '../../../../../../../js/output/common/fonts/tex/italic.js';
import * as module26 from '../../../../../../../js/output/common/fonts/tex/largeop.js';
import * as module27 from '../../../../../../../js/output/common/fonts/tex/monospace.js';
import * as module28 from '../../../../../../../js/output/common/fonts/tex/normal.js';
import * as module29 from '../../../../../../../js/output/common/fonts/tex/sans-serif-bold-italic.js';
import * as module30 from '../../../../../../../js/output/common/fonts/tex/sans-serif-bold.js';
import * as module31 from '../../../../../../../js/output/common/fonts/tex/sans-serif-italic.js';
import * as module32 from '../../../../../../../js/output/common/fonts/tex/sans-serif.js';
import * as module33 from '../../../../../../../js/output/common/fonts/tex/script-bold.js';
import * as module34 from '../../../../../../../js/output/common/fonts/tex/script.js';
import * as module35 from '../../../../../../../js/output/common/fonts/tex/smallop.js';
import * as module36 from '../../../../../../../js/output/common/fonts/tex/tex-calligraphic-bold.js';
import * as module37 from '../../../../../../../js/output/common/fonts/tex/tex-calligraphic.js';
import * as module38 from '../../../../../../../js/output/common/fonts/tex/tex-mathit.js';
import * as module39 from '../../../../../../../js/output/common/fonts/tex/tex-oldstyle-bold.js';
import * as module40 from '../../../../../../../js/output/common/fonts/tex/tex-oldstyle.js';
import * as module41 from '../../../../../../../js/output/common/fonts/tex/tex-size3.js';
import * as module42 from '../../../../../../../js/output/common/fonts/tex/tex-size4.js';
import * as module43 from '../../../../../../../js/output/common/fonts/tex/tex-variant.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('output/chtml/fonts/tex', VERSION, 'chtml-font');
}

combineWithMathJax({_: {
  output: {
    chtml: {
      fonts: {
        tex_ts: module1,
        tex: {
          "bold-italic": module2,
          bold: module3,
          "fraktur-bold": module4,
          fraktur: module5,
          italic: module6,
          largeop: module7,
          monospace: module8,
          normal: module9,
          "sans-serif-bold-italic": module10,
          "sans-serif-bold": module11,
          "sans-serif-italic": module12,
          "sans-serif": module13,
          smallop: module14,
          "tex-calligraphic-bold": module15,
          "tex-size3": module16,
          "tex-size4": module17,
          "tex-variant": module18
        }
      }
    },
    common: {
      fonts: {
        tex: {
          "bold-italic": module19,
          bold: module20,
          delimiters: module21,
          "double-struck": module22,
          "fraktur-bold": module23,
          fraktur: module24,
          italic: module25,
          largeop: module26,
          monospace: module27,
          normal: module28,
          "sans-serif-bold-italic": module29,
          "sans-serif-bold": module30,
          "sans-serif-italic": module31,
          "sans-serif": module32,
          "script-bold": module33,
          script: module34,
          smallop: module35,
          "tex-calligraphic-bold": module36,
          "tex-calligraphic": module37,
          "tex-mathit": module38,
          "tex-oldstyle-bold": module39,
          "tex-oldstyle": module40,
          "tex-size3": module41,
          "tex-size4": module42,
          "tex-variant": module43
        }
      }
    }
  }
}});
