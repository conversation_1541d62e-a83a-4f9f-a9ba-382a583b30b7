{"version": 3, "file": "mtable.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mtable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAkG;AAClG,qDAA8C;AAO9C;IAA+B,6BAAe;IAA9C;QAAA,qEAqJC;QAvHQ,gBAAU,GAAG;YAClB,SAAS,EAAE,IAAI;SAChB,CAAC;QAKQ,cAAQ,GAAG,qBAAQ,CAAC,GAAG,CAAC;;IAgHpC,CAAC;IA3GC,sBAAW,2BAAI;aAAf;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAMD,sBAAW,yCAAkB;aAA7B;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,0CAAsB,GAA7B,UAA8B,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;;;YAKtG,KAAmB,IAAA,qBAAA,SAAA,6BAAgB,CAAA,kDAAA,gFAAE;gBAAhC,IAAM,MAAI,6BAAA;gBACb,IAAI,UAAU,CAAC,MAAI,CAAC,EAAE;oBACpB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAI,EAAE,UAAU,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACzD;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAI,CAAC,KAAK,SAAS,EAAE;oBACnD,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAI,CAAC,CAAC;iBACnD;aACF;;;;;;;;;QACD,iBAAM,sBAAsB,YAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IASS,+CAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,MAAe;;;YAC/G,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;yBACjD,WAAW,CAAC,KAAK,CAAC,CAAC;iBACvB;aACF;;;;;;;;;QACD,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAW,IAAI,KAAK,CAAC;QAC3D,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;QACxG,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE;YACnD,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC;YAC/C,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAY,CAAC;QACvE,IAAM,MAAM,GAAG,IAAA,iBAAK,EAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC,CAAC;;YAChE,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAClE,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;aACrE;;;;;;;;;IACH,CAAC;IAOS,kCAAc,GAAxB,UAAyB,OAAqB;QAC5C,IAAI,GAAG,GAAY,IAAI,CAAC;QACxB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACvB,GAAG,GAAG,IAAI,CAAC;aACZ;iBAAM;gBACL,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAKlC,IAAI,GAAG,EAAE;oBACP,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBACxB,CAAC,EAAE,CAAC;iBACL;qBAAM;oBACL,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAY,CAAC;iBAClE;gBACD,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBAC1B,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBAChC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpB,KAAK,IAAI,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;oBAChD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,GAAG,4BAA4B,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBACvG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBAC/D;aACF;SACF;QACD,iBAAM,cAAc,YAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAKM,+BAAW,GAAlB,UAAmB,IAAa;;QAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;YACxB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aACzB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IA9Ia,kBAAQ,yBACjB,4BAAe,CAAC,QAAQ,KAC3B,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,UAAU,EACpB,WAAW,EAAE,QAAQ,EACrB,UAAU,EAAE,QAAQ,EACpB,cAAc,EAAE,IAAI,EACpB,WAAW,EAAE,MAAM,EACnB,KAAK,EAAE,MAAM,EACb,UAAU,EAAE,KAAK,EACjB,aAAa,EAAE,MAAM,EACrB,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,MAAM,EACnB,KAAK,EAAE,MAAM,EACb,YAAY,EAAE,aAAa,EAC3B,SAAS,EAAE,KAAK,EAChB,YAAY,EAAE,KAAK,EACnB,YAAY,EAAE,KAAK,EACnB,IAAI,EAAE,OAAO,EACb,eAAe,EAAE,OAAO,IACxB;IA4HJ,gBAAC;CAAA,AArJD,CAA+B,4BAAe,GAqJ7C;AArJY,8BAAS"}