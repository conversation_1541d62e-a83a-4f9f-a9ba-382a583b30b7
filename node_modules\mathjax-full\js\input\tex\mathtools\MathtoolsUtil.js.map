{"version": 3, "file": "MathtoolsUtil.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/mathtools/MathtoolsUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA,qDAAkD;AAClD,iEAAwC;AACxC,iEAAwC;AACxC,+DAAsC;AAEtC,0CAAmC;AAEnC,uDAAgD;AAGhD,6DAAuD;AACvD,yEAAyD;AAK5C,QAAA,aAAa,GAAG;IAQ3B,eAAe,EAAf,UAAgB,GAAY,EAAE,KAAa;QACzC,IAAI,CAAC,KAAK;YAAE,OAAO;QACb,IAAA,KAAA,OAAoB,IAAA,mBAAM,EAAC,KAAK,EAAE;YACtC,gBAAgB,EAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAChC,aAAa,EAAU,CAAC,KAAK,EAAE,CAAC,CAAC;YACjC,eAAe,EAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YACjC,qBAAqB,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;SAClC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAA,EALT,OAAO,QAAA,EAAE,MAAM,QAKN,CAAC;QACjB,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC5C,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;SAC3C;IACH,CAAC;IASD,cAAc,EAAd,UAAe,MAAiB,EAAE,IAAY;QAC5C,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAkB,CAAC;QAC/C,IAAI,GAAG,CAAC,IAAI,KAAK,2BAAY,CAAC,SAAS,CAAC,IAAI,EAAE;YAC5C,MAAM,IAAI,qBAAQ,CAAC,gBAAgB,EAAE,8CAA8C,EAAE,IAAI,CAAC,CAAC;SAC5F;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAYD,eAAe,EAAf,UAAgB,MAAoB,EAAE,EAAU,EAAE,IAAc;QAC9D,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,wCAAY,CAAe,CAAC;QACpE,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,iBAAK,CAAC,EAAE,EAAE,sCAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;IACzE,CAAC;IAQD,WAAW,EAAX,UAAY,MAAe,EAAE,MAAc;QACzC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAAE,OAAO;QACrC,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAW,CAAC;QAC/D,IAAI,UAAU,EAAE;YACd,IAAM,KAAG,GAAG,sBAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACvC,UAAU,GAAG,UAAU;iBACpB,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,sBAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAG,CAAC,CAAC,EAAtD,CAAsD,CAAC;iBAChE,IAAI,CAAC,GAAG,CAAC,CAAC;SACd;aAAM;YACL,UAAU,GAAG,MAAM,CAAC;SACrB;QACD,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC;IASD,WAAW,EAAX,UAAY,IAAY,EAAE,CAAS;QACjC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,EAAE;YAC9C,MAAM,IAAI,qBAAQ,CAAC,YAAY,EAAE,gCAAgC,EAAE,IAAI,CAAC,CAAC;SAC1E;QACD,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAUD,SAAS,EAAT,UAAU,MAAiB,EAAE,IAAY,EAAE,GAAW;QACpD,IAAI,GAAG,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACtC;QACD,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,oBAAa,GAAG,YAAS,CAAC,CAAC;QACnE,MAAM,IAAI,CAAC,GAAG,GAAG,UAAG,MAAM,cAAI,GAAG,MAAG,CAAC,CAAC;QACtC,OAAO,IAAI,sBAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1E,CAAC;CAEF,CAAC"}