import {
  require_Retries,
  require_mathjax
} from "./chunk-EYAM6I3G.js";
import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/mathjax-full/js/util/AsyncLoad.js
var require_AsyncLoad = __commonJS({
  "node_modules/mathjax-full/js/util/AsyncLoad.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.asyncLoad = void 0;
    var mathjax_js_1 = require_mathjax();
    function asyncLoad(name) {
      if (!mathjax_js_1.mathjax.asyncLoad) {
        return Promise.reject("Can't load '".concat(name, "': No asyncLoad method specified"));
      }
      return new Promise(function(ok, fail) {
        var result = mathjax_js_1.mathjax.asyncLoad(name);
        if (result instanceof Promise) {
          result.then(function(value) {
            return ok(value);
          }).catch(function(err) {
            return fail(err);
          });
        } else {
          ok(result);
        }
      });
    }
    exports.asyncLoad = asyncLoad;
  }
});

// node_modules/mathjax-full/js/util/Entities.js
var require_Entities = __commonJS({
  "node_modules/mathjax-full/js/util/Entities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.numeric = exports.translate = exports.remove = exports.add = exports.entities = exports.options = void 0;
    var Retries_js_1 = require_Retries();
    var AsyncLoad_js_1 = require_AsyncLoad();
    exports.options = {
      loadMissingEntities: true
    };
    exports.entities = {
      ApplyFunction: "⁡",
      Backslash: "∖",
      Because: "∵",
      Breve: "˘",
      Cap: "⋒",
      CenterDot: "·",
      CircleDot: "⊙",
      CircleMinus: "⊖",
      CirclePlus: "⊕",
      CircleTimes: "⊗",
      Congruent: "≡",
      ContourIntegral: "∮",
      Coproduct: "∐",
      Cross: "⨯",
      Cup: "⋓",
      CupCap: "≍",
      Dagger: "‡",
      Del: "∇",
      Delta: "Δ",
      Diamond: "⋄",
      DifferentialD: "ⅆ",
      DotEqual: "≐",
      DoubleDot: "¨",
      DoubleRightTee: "⊨",
      DoubleVerticalBar: "∥",
      DownArrow: "↓",
      DownLeftVector: "↽",
      DownRightVector: "⇁",
      DownTee: "⊤",
      Downarrow: "⇓",
      Element: "∈",
      EqualTilde: "≂",
      Equilibrium: "⇌",
      Exists: "∃",
      ExponentialE: "ⅇ",
      FilledVerySmallSquare: "▪",
      ForAll: "∀",
      Gamma: "Γ",
      Gg: "⋙",
      GreaterEqual: "≥",
      GreaterEqualLess: "⋛",
      GreaterFullEqual: "≧",
      GreaterLess: "≷",
      GreaterSlantEqual: "⩾",
      GreaterTilde: "≳",
      Hacek: "ˇ",
      Hat: "^",
      HumpDownHump: "≎",
      HumpEqual: "≏",
      Im: "ℑ",
      ImaginaryI: "ⅈ",
      Integral: "∫",
      Intersection: "⋂",
      InvisibleComma: "⁣",
      InvisibleTimes: "⁢",
      Lambda: "Λ",
      Larr: "↞",
      LeftAngleBracket: "⟨",
      LeftArrow: "←",
      LeftArrowRightArrow: "⇆",
      LeftCeiling: "⌈",
      LeftDownVector: "⇃",
      LeftFloor: "⌊",
      LeftRightArrow: "↔",
      LeftTee: "⊣",
      LeftTriangle: "⊲",
      LeftTriangleEqual: "⊴",
      LeftUpVector: "↿",
      LeftVector: "↼",
      Leftarrow: "⇐",
      Leftrightarrow: "⇔",
      LessEqualGreater: "⋚",
      LessFullEqual: "≦",
      LessGreater: "≶",
      LessSlantEqual: "⩽",
      LessTilde: "≲",
      Ll: "⋘",
      Lleftarrow: "⇚",
      LongLeftArrow: "⟵",
      LongLeftRightArrow: "⟷",
      LongRightArrow: "⟶",
      Longleftarrow: "⟸",
      Longleftrightarrow: "⟺",
      Longrightarrow: "⟹",
      Lsh: "↰",
      MinusPlus: "∓",
      NestedGreaterGreater: "≫",
      NestedLessLess: "≪",
      NotDoubleVerticalBar: "∦",
      NotElement: "∉",
      NotEqual: "≠",
      NotExists: "∄",
      NotGreater: "≯",
      NotGreaterEqual: "≱",
      NotLeftTriangle: "⋪",
      NotLeftTriangleEqual: "⋬",
      NotLess: "≮",
      NotLessEqual: "≰",
      NotPrecedes: "⊀",
      NotPrecedesSlantEqual: "⋠",
      NotRightTriangle: "⋫",
      NotRightTriangleEqual: "⋭",
      NotSubsetEqual: "⊈",
      NotSucceeds: "⊁",
      NotSucceedsSlantEqual: "⋡",
      NotSupersetEqual: "⊉",
      NotTilde: "≁",
      NotVerticalBar: "∤",
      Omega: "Ω",
      OverBar: "‾",
      OverBrace: "⏞",
      PartialD: "∂",
      Phi: "Φ",
      Pi: "Π",
      PlusMinus: "±",
      Precedes: "≺",
      PrecedesEqual: "⪯",
      PrecedesSlantEqual: "≼",
      PrecedesTilde: "≾",
      Product: "∏",
      Proportional: "∝",
      Psi: "Ψ",
      Rarr: "↠",
      Re: "ℜ",
      ReverseEquilibrium: "⇋",
      RightAngleBracket: "⟩",
      RightArrow: "→",
      RightArrowLeftArrow: "⇄",
      RightCeiling: "⌉",
      RightDownVector: "⇂",
      RightFloor: "⌋",
      RightTee: "⊢",
      RightTeeArrow: "↦",
      RightTriangle: "⊳",
      RightTriangleEqual: "⊵",
      RightUpVector: "↾",
      RightVector: "⇀",
      Rightarrow: "⇒",
      Rrightarrow: "⇛",
      Rsh: "↱",
      Sigma: "Σ",
      SmallCircle: "∘",
      Sqrt: "√",
      Square: "□",
      SquareIntersection: "⊓",
      SquareSubset: "⊏",
      SquareSubsetEqual: "⊑",
      SquareSuperset: "⊐",
      SquareSupersetEqual: "⊒",
      SquareUnion: "⊔",
      Star: "⋆",
      Subset: "⋐",
      SubsetEqual: "⊆",
      Succeeds: "≻",
      SucceedsEqual: "⪰",
      SucceedsSlantEqual: "≽",
      SucceedsTilde: "≿",
      SuchThat: "∋",
      Sum: "∑",
      Superset: "⊃",
      SupersetEqual: "⊇",
      Supset: "⋑",
      Therefore: "∴",
      Theta: "Θ",
      Tilde: "∼",
      TildeEqual: "≃",
      TildeFullEqual: "≅",
      TildeTilde: "≈",
      UnderBar: "_",
      UnderBrace: "⏟",
      Union: "⋃",
      UnionPlus: "⊎",
      UpArrow: "↑",
      UpDownArrow: "↕",
      UpTee: "⊥",
      Uparrow: "⇑",
      Updownarrow: "⇕",
      Upsilon: "Υ",
      Vdash: "⊩",
      Vee: "⋁",
      VerticalBar: "∣",
      VerticalTilde: "≀",
      Vvdash: "⊪",
      Wedge: "⋀",
      Xi: "Ξ",
      amp: "&",
      acute: "´",
      aleph: "ℵ",
      alpha: "α",
      amalg: "⨿",
      and: "∧",
      ang: "∠",
      angmsd: "∡",
      angsph: "∢",
      ape: "≊",
      backprime: "‵",
      backsim: "∽",
      backsimeq: "⋍",
      beta: "β",
      beth: "ℶ",
      between: "≬",
      bigcirc: "◯",
      bigodot: "⨀",
      bigoplus: "⨁",
      bigotimes: "⨂",
      bigsqcup: "⨆",
      bigstar: "★",
      bigtriangledown: "▽",
      bigtriangleup: "△",
      biguplus: "⨄",
      blacklozenge: "⧫",
      blacktriangle: "▴",
      blacktriangledown: "▾",
      blacktriangleleft: "◂",
      bowtie: "⋈",
      boxdl: "┐",
      boxdr: "┌",
      boxminus: "⊟",
      boxplus: "⊞",
      boxtimes: "⊠",
      boxul: "┘",
      boxur: "└",
      bsol: "\\",
      bull: "•",
      cap: "∩",
      check: "✓",
      chi: "χ",
      circ: "ˆ",
      circeq: "≗",
      circlearrowleft: "↺",
      circlearrowright: "↻",
      circledR: "®",
      circledS: "Ⓢ",
      circledast: "⊛",
      circledcirc: "⊚",
      circleddash: "⊝",
      clubs: "♣",
      colon: ":",
      comp: "∁",
      ctdot: "⋯",
      cuepr: "⋞",
      cuesc: "⋟",
      cularr: "↶",
      cup: "∪",
      curarr: "↷",
      curlyvee: "⋎",
      curlywedge: "⋏",
      dagger: "†",
      daleth: "ℸ",
      ddarr: "⇊",
      deg: "°",
      delta: "δ",
      digamma: "ϝ",
      div: "÷",
      divideontimes: "⋇",
      dot: "˙",
      doteqdot: "≑",
      dotplus: "∔",
      dotsquare: "⊡",
      dtdot: "⋱",
      ecir: "≖",
      efDot: "≒",
      egs: "⪖",
      ell: "ℓ",
      els: "⪕",
      empty: "∅",
      epsi: "ε",
      epsiv: "ϵ",
      erDot: "≓",
      eta: "η",
      eth: "ð",
      flat: "♭",
      fork: "⋔",
      frown: "⌢",
      gEl: "⪌",
      gamma: "γ",
      gap: "⪆",
      gimel: "ℷ",
      gnE: "≩",
      gnap: "⪊",
      gne: "⪈",
      gnsim: "⋧",
      gt: ">",
      gtdot: "⋗",
      harrw: "↭",
      hbar: "ℏ",
      hellip: "…",
      hookleftarrow: "↩",
      hookrightarrow: "↪",
      imath: "ı",
      infin: "∞",
      intcal: "⊺",
      iota: "ι",
      jmath: "ȷ",
      kappa: "κ",
      kappav: "ϰ",
      lEg: "⪋",
      lambda: "λ",
      lap: "⪅",
      larrlp: "↫",
      larrtl: "↢",
      lbrace: "{",
      lbrack: "[",
      le: "≤",
      leftleftarrows: "⇇",
      leftthreetimes: "⋋",
      lessdot: "⋖",
      lmoust: "⎰",
      lnE: "≨",
      lnap: "⪉",
      lne: "⪇",
      lnsim: "⋦",
      longmapsto: "⟼",
      looparrowright: "↬",
      lowast: "∗",
      loz: "◊",
      lt: "<",
      ltimes: "⋉",
      ltri: "◃",
      macr: "¯",
      malt: "✠",
      mho: "℧",
      mu: "μ",
      multimap: "⊸",
      nLeftarrow: "⇍",
      nLeftrightarrow: "⇎",
      nRightarrow: "⇏",
      nVDash: "⊯",
      nVdash: "⊮",
      natur: "♮",
      nearr: "↗",
      nharr: "↮",
      nlarr: "↚",
      not: "¬",
      nrarr: "↛",
      nu: "ν",
      nvDash: "⊭",
      nvdash: "⊬",
      nwarr: "↖",
      omega: "ω",
      omicron: "ο",
      or: "∨",
      osol: "⊘",
      period: ".",
      phi: "φ",
      phiv: "ϕ",
      pi: "π",
      piv: "ϖ",
      prap: "⪷",
      precnapprox: "⪹",
      precneqq: "⪵",
      precnsim: "⋨",
      prime: "′",
      psi: "ψ",
      quot: '"',
      rarrtl: "↣",
      rbrace: "}",
      rbrack: "]",
      rho: "ρ",
      rhov: "ϱ",
      rightrightarrows: "⇉",
      rightthreetimes: "⋌",
      ring: "˚",
      rmoust: "⎱",
      rtimes: "⋊",
      rtri: "▹",
      scap: "⪸",
      scnE: "⪶",
      scnap: "⪺",
      scnsim: "⋩",
      sdot: "⋅",
      searr: "↘",
      sect: "§",
      sharp: "♯",
      sigma: "σ",
      sigmav: "ς",
      simne: "≆",
      smile: "⌣",
      spades: "♠",
      sub: "⊂",
      subE: "⫅",
      subnE: "⫋",
      subne: "⊊",
      supE: "⫆",
      supnE: "⫌",
      supne: "⊋",
      swarr: "↙",
      tau: "τ",
      theta: "θ",
      thetav: "ϑ",
      tilde: "˜",
      times: "×",
      triangle: "▵",
      triangleq: "≜",
      upsi: "υ",
      upuparrows: "⇈",
      veebar: "⊻",
      vellip: "⋮",
      weierp: "℘",
      xi: "ξ",
      yen: "¥",
      zeta: "ζ",
      zigrarr: "⇝",
      nbsp: " ",
      rsquo: "’",
      lsquo: "‘"
    };
    var loaded = {};
    function add(additions, file) {
      Object.assign(exports.entities, additions);
      loaded[file] = true;
    }
    exports.add = add;
    function remove(entity) {
      delete exports.entities[entity];
    }
    exports.remove = remove;
    function translate(text) {
      return text.replace(/&([a-z][a-z0-9]*|#(?:[0-9]+|x[0-9a-f]+));/ig, replace);
    }
    exports.translate = translate;
    function replace(match, entity) {
      if (entity.charAt(0) === "#") {
        return numeric(entity.slice(1));
      }
      if (exports.entities[entity]) {
        return exports.entities[entity];
      }
      if (exports.options["loadMissingEntities"]) {
        var file = entity.match(/^[a-zA-Z](fr|scr|opf)$/) ? RegExp.$1 : entity.charAt(0).toLowerCase();
        if (!loaded[file]) {
          loaded[file] = true;
          (0, Retries_js_1.retryAfter)((0, AsyncLoad_js_1.asyncLoad)("./util/entities/" + file + ".js"));
        }
      }
      return match;
    }
    function numeric(entity) {
      var n = entity.charAt(0) === "x" ? parseInt(entity.slice(1), 16) : parseInt(entity);
      return String.fromCodePoint(n);
    }
    exports.numeric = numeric;
  }
});

export {
  require_Entities
};
//# sourceMappingURL=chunk-ARGWDDH6.js.map
