!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.ContextMenu=e():t.ContextMenu=e()}(this,(function(){return function(t){var e={};function o(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}return o.m=t,o.c=e,o.d=function(t,e,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)o.d(n,r,function(e){return t[e]}.bind(null,r));return n},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=15)}([function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlAttrs=e.HtmlClasses=void 0;function n(t){return"CtxtMenu_"+t}function r(t){return n(t)}function i(t){return n(t)}e.HtmlClasses={ATTACHED:r("Attached"),CONTEXTMENU:r("ContextMenu"),MENU:r("Menu"),MENUARROW:r("MenuArrow"),MENUACTIVE:r("MenuActive"),MENUCHECK:r("MenuCheck"),MENUCLOSE:r("MenuClose"),MENUCOMBOBOX:r("MenuComboBox"),MENUDISABLED:r("MenuDisabled"),MENUFRAME:r("MenuFrame"),MENUITEM:r("MenuItem"),MENULABEL:r("MenuLabel"),MENURADIOCHECK:r("MenuRadioCheck"),MENUINPUTBOX:r("MenuInputBox"),MENURULE:r("MenuRule"),MENUSLIDER:r("MenuSlider"),MOUSEPOST:r("MousePost"),RTL:r("RTL"),INFO:r("Info"),INFOCLOSE:r("InfoClose"),INFOCONTENT:r("InfoContent"),INFOSIGNATURE:r("InfoSignature"),INFOTITLE:r("InfoTitle"),SLIDERVALUE:r("SliderValue"),SLIDERBAR:r("SliderBar"),SELECTION:r("Selection"),SELECTIONBOX:r("SelectionBox"),SELECTIONMENU:r("SelectionMenu"),SELECTIONDIVIDER:r("SelectionDivider"),SELECTIONITEM:r("SelectionItem")},e.HtmlAttrs={COUNTER:i("Counter"),KEYDOWNFUNC:i("keydownFunc"),CONTEXTMENUFUNC:i("contextmenuFunc"),OLDTAB:i("Oldtabindex"),TOUCHFUNC:i("TouchFunc")}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MenuUtil=void 0,function(t){t.close=function(t){var e=t.menu;e.baseMenu?e.baseMenu.unpost():e.unpost()},t.getActiveElement=function(t){var e=t.menu;return(e.baseMenu?e.baseMenu:e).store.active},t.error=function(t,e){console.error("ContextMenu Error: "+e)},t.counter=function(){return e++};var e=0}(e.MenuUtil||(e.MenuUtil={}))},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractItem=void 0;var s=o(9),a=o(1),u=o(0),c=function(t){function e(e,o,n,r){var i=t.call(this,e,o)||this;return i._content=n,i.disabled=!1,i.callbacks=[],i._id=r||n,i}return r(e,t),Object.defineProperty(e.prototype,"content",{get:function(){return this._content},set:function(t){this._content=t,this.generateHtml(),this.menu&&this.menu.generateHtml()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),e.prototype.press=function(){this.disabled||(this.executeAction(),this.executeCallbacks_())},e.prototype.executeAction=function(){},e.prototype.registerCallback=function(t){-1===this.callbacks.indexOf(t)&&this.callbacks.push(t)},e.prototype.unregisterCallback=function(t){var e=this.callbacks.indexOf(t);-1!==e&&this.callbacks.splice(e,1)},e.prototype.mousedown=function(t){this.press(),this.stop(t)},e.prototype.mouseover=function(t){this.focus(),this.stop(t)},e.prototype.mouseout=function(t){this.deactivate(),this.stop(t)},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this);var e=this.html;e.setAttribute("aria-disabled","false"),e.textContent=this.content},e.prototype.activate=function(){this.disabled||this.html.classList.add(u.HtmlClasses.MENUACTIVE)},e.prototype.deactivate=function(){this.html.classList.remove(u.HtmlClasses.MENUACTIVE)},e.prototype.focus=function(){this.menu.focused=this,t.prototype.focus.call(this),this.activate()},e.prototype.unfocus=function(){this.deactivate(),t.prototype.unfocus.call(this)},e.prototype.escape=function(t){a.MenuUtil.close(this)},e.prototype.up=function(t){this.menu.up(t)},e.prototype.down=function(t){this.menu.down(t)},e.prototype.left=function(t){this.menu.left(t)},e.prototype.right=function(t){this.menu.right(t)},e.prototype.space=function(t){this.press()},e.prototype.disable=function(){this.disabled=!0;var t=this.html;t.classList.add(u.HtmlClasses.MENUDISABLED),t.setAttribute("aria-disabled","true")},e.prototype.enable=function(){this.disabled=!1;var t=this.html;t.classList.remove(u.HtmlClasses.MENUDISABLED),t.removeAttribute("aria-disabled")},e.prototype.executeCallbacks_=function(){var t,e;try{for(var o=i(this.callbacks),n=o.next();!n.done;n=o.next()){var r=n.value;try{r(this)}catch(t){a.MenuUtil.error(t,"Callback for menu entry "+this.id+" failed.")}}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e}(s.AbstractEntry);e.AbstractItem=c},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractPostable=void 0;var i=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.posted=!1,e}return r(e,t),e.prototype.isPosted=function(){return this.posted},e.prototype.post=function(t,e){this.posted||(void 0!==t&&void 0!==e&&this.html.setAttribute("style","left: "+t+"px; top: "+e+"px;"),this.display(),this.posted=!0)},e.prototype.unpost=function(){if(this.posted){var t=this.html;t.parentNode&&t.parentNode.removeChild(t),this.posted=!1}},e}(o(8).MenuElement);e.AbstractPostable=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.KEY=void 0,function(t){t[t.RETURN=13]="RETURN",t[t.ESCAPE=27]="ESCAPE",t[t.SPACE=32]="SPACE",t[t.LEFT=37]="LEFT",t[t.UP=38]="UP",t[t.RIGHT=39]="RIGHT",t[t.DOWN=40]="DOWN"}(e.KEY||(e.KEY={}))},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractVariableItem=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this);var e=this.html;this.span||this.generateSpan(),e.appendChild(this.span),this.update()},e.prototype.register=function(){this.variable.register(this)},e.prototype.unregister=function(){this.variable.unregister(this)},e.prototype.update=function(){this.updateAria(),this.span&&this.updateSpan()},e}(o(2).AbstractItem);e.AbstractVariableItem=i},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractMenu=void 0;var s=o(3),a=o(2),u=o(0),c=o(10),p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.className=u.HtmlClasses.CONTEXTMENU,e.role="menu",e._items=[],e._baseMenu=null,e}return r(e,t),Object.defineProperty(e.prototype,"baseMenu",{get:function(){return this._baseMenu},set:function(t){this._baseMenu=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"items",{get:function(){return this._items},set:function(t){this._items=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"pool",{get:function(){return this.variablePool},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"focused",{get:function(){return this._focused},set:function(t){if(this._focused!==t){this._focused||this.unfocus();var e=this._focused;this._focused=t,e&&e.unfocus()}},enumerable:!1,configurable:!0}),e.prototype.up=function(t){var e=this.items.filter((function(t){return t instanceof a.AbstractItem&&!t.isHidden()}));if(0!==e.length)if(this.focused){var o=e.indexOf(this.focused);-1!==o&&e[o=o?--o:e.length-1].focus()}else e[e.length-1].focus()},e.prototype.down=function(t){var e=this.items.filter((function(t){return t instanceof a.AbstractItem&&!t.isHidden()}));if(0!==e.length)if(this.focused){var o=e.indexOf(this.focused);-1!==o&&e[o=++o===e.length?0:o].focus()}else e[0].focus()},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.generateMenu()},e.prototype.generateMenu=function(){var t,e,o=this.html;o.classList.add(u.HtmlClasses.MENU);try{for(var n=i(this.items),r=n.next();!r.done;r=n.next()){var s=r.value;if(s.isHidden()){var a=s.html;a.parentNode&&a.parentNode.removeChild(a)}else o.appendChild(s.html)}}catch(e){t={error:e}}finally{try{r&&!r.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}},e.prototype.post=function(e,o){this.variablePool.update(),t.prototype.post.call(this,e,o)},e.prototype.unpostSubmenus=function(){var t,e,o=this.items.filter((function(t){return t instanceof c.Submenu}));try{for(var n=i(o),r=n.next();!r.done;r=n.next()){var s=r.value;s.submenu.unpost(),s!==this.focused&&s.unfocus()}}catch(e){t={error:e}}finally{try{r&&!r.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}},e.prototype.unpost=function(){t.prototype.unpost.call(this),this.unpostSubmenus(),this.focused=null},e.prototype.find=function(t){var e,o;try{for(var n=i(this.items),r=n.next();!r.done;r=n.next()){var s=r.value;if("rule"!==s.type){if(s.id===t)return s;if("submenu"===s.type){var a=s.submenu.find(t);if(a)return a}}}}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(e)throw e.error}}return null},e}(s.AbstractPostable);e.AbstractMenu=p},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.ContextMenu=void 0;var i=o(6),s=o(0),a=o(18),u=o(19),c=function(t){function e(e){var o=t.call(this)||this;return o.factory=e,o.id="",o.moving=!1,o._store=new a.MenuStore(o),o.widgets=[],o.variablePool=new u.VariablePool,o}return r(e,t),e.fromJson=function(t,e){var o=e.pool,n=e.items,r=e.id,i=void 0===r?"":r,s=new this(t);s.id=i;var a=t.get("variable");o.forEach((function(e){return a(t,e,s.pool)}));var u=t.get("items")(t,n,s);return s.items=u,s},e.prototype.generateHtml=function(){this.isPosted()&&this.unpost(),t.prototype.generateHtml.call(this),this._frame=document.createElement("div"),this._frame.classList.add(s.HtmlClasses.MENUFRAME);var e="left: 0px; top: 0px; z-index: 200; width: 100%; height: 100%; border: 0px; padding: 0px; margin: 0px;";this._frame.setAttribute("style","position: absolute; "+e);var o=document.createElement("div");o.setAttribute("style","position: fixed; "+e),this._frame.appendChild(o),o.addEventListener("mousedown",function(t){this.unpost(),this.unpostWidgets(),this.stop(t)}.bind(this))},e.prototype.display=function(){document.body.appendChild(this.frame),this.frame.appendChild(this.html),this.focus()},e.prototype.escape=function(t){this.unpost(),this.unpostWidgets()},e.prototype.unpost=function(){if(t.prototype.unpost.call(this),!(this.widgets.length>0)){this.frame.parentNode.removeChild(this.frame);var e=this.store;this.moving||e.insertTaborder(),e.active.focus()}},e.prototype.left=function(t){this.move_(this.store.previous())},e.prototype.right=function(t){this.move_(this.store.next())},Object.defineProperty(e.prototype,"frame",{get:function(){return this._frame},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"store",{get:function(){return this._store},enumerable:!1,configurable:!0}),e.prototype.post=function(e,o){if(void 0!==o)return this.moving||this.store.removeTaborder(),void t.prototype.post.call(this,e,o);var n,r,i,s=e;if(s instanceof Event?(n=s.target,this.stop(s)):n=s,s instanceof MouseEvent&&(r=s.pageX,i=s.pageY,r||i||!s.clientX||(r=s.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,i=s.clientY+document.body.scrollTop+document.documentElement.scrollTop)),!r&&!i&&n){var a=window.pageXOffset||document.documentElement.scrollLeft,u=window.pageYOffset||document.documentElement.scrollTop,c=n.getBoundingClientRect();r=(c.right+c.left)/2+a,i=(c.bottom+c.top)/2+u}this.store.active=n,this.anchor=this.store.active;var p=this.html;r+p.offsetWidth>document.body.offsetWidth-5&&(r=document.body.offsetWidth-p.offsetWidth-5),this.post(r,i)},e.prototype.registerWidget=function(t){this.widgets.push(t)},e.prototype.unregisterWidget=function(t){var e=this.widgets.indexOf(t);e>-1&&this.widgets.splice(e,1),0===this.widgets.length&&this.unpost()},e.prototype.unpostWidgets=function(){this.widgets.forEach((function(t){return t.unpost()}))},e.prototype.toJson=function(){return{type:""}},e.prototype.move_=function(t){this.anchor&&t!==this.anchor&&(this.moving=!0,this.unpost(),this.post(t),this.moving=!1)},e}(i.AbstractMenu);e.ContextMenu=c},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.MenuElement=void 0;var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.addAttributes=function(t){for(var e in t)this.html.setAttribute(e,t[e])},Object.defineProperty(e.prototype,"html",{get:function(){return this._html||this.generateHtml(),this._html},set:function(t){this._html=t,this.addEvents(t)},enumerable:!1,configurable:!0}),e.prototype.generateHtml=function(){var t=document.createElement("div");t.classList.add(this.className),t.setAttribute("role",this.role),this.html=t},e.prototype.focus=function(){var t=this.html;t.setAttribute("tabindex","0"),t.focus()},e.prototype.unfocus=function(){var t=this.html;t.hasAttribute("tabindex")&&t.setAttribute("tabindex","-1");try{t.blur()}catch(t){}t.blur()},e}(o(16).AbstractNavigatable);e.MenuElement=i},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractEntry=void 0;var i=o(8),s=o(0),a=function(t){function e(e,o){var n=t.call(this)||this;return n._menu=e,n._type=o,n.className=s.HtmlClasses.MENUITEM,n.role="menuitem",n.hidden=!1,n}return r(e,t),Object.defineProperty(e.prototype,"menu",{get:function(){return this._menu},set:function(t){this._menu=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),e.prototype.hide=function(){this.hidden=!0,this.menu.generateMenu()},e.prototype.show=function(){this.hidden=!1,this.menu.generateMenu()},e.prototype.isHidden=function(){return this.hidden},e}(i.MenuElement);e.AbstractEntry=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Submenu=void 0;var i=o(2),s=o(0),a=function(t){function e(e,o,n){var r=t.call(this,e,"submenu",o,n)||this;return r._submenu=null,r}return r(e,t),e.fromJson=function(t,e,o){var n=e.content,r=e.menu,i=new this(o,n,e.id),s=t.get("subMenu")(t,r,i);return i.submenu=s,i},Object.defineProperty(e.prototype,"submenu",{get:function(){return this._submenu},set:function(t){this._submenu=t},enumerable:!1,configurable:!0}),e.prototype.mouseover=function(t){this.focus(),this.stop(t)},e.prototype.mouseout=function(t){this.stop(t)},e.prototype.unfocus=function(){if(this.submenu.isPosted()){if(this.menu.focused!==this)return t.prototype.unfocus.call(this),void this.menu.unpostSubmenus();this.html.setAttribute("tabindex","-1"),this.html.blur()}else t.prototype.unfocus.call(this)},e.prototype.focus=function(){t.prototype.focus.call(this),this.submenu.isPosted()||this.disabled||this.submenu.post()},e.prototype.executeAction=function(){this.submenu.isPosted()?this.submenu.unpost():this.submenu.post()},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this);var e=this.html;this.span=document.createElement("span"),this.span.textContent="\u25ba",this.span.classList.add(s.HtmlClasses.MENUARROW),e.appendChild(this.span),e.setAttribute("aria-haspopup","true")},e.prototype.left=function(e){this.submenu.isPosted()?this.submenu.unpost():t.prototype.left.call(this,e)},e.prototype.right=function(t){this.submenu.isPosted()?this.submenu.down(t):this.submenu.post()},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractItem);e.Submenu=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Info=void 0;var i=o(21),s=o(0),a=function(t){function e(e,o,n){var r=t.call(this)||this;return r.title=e,r.signature=n,r.className=s.HtmlClasses.INFO,r.role="dialog",r.contentDiv=r.generateContent(),r.close=r.generateClose(),r.content=o||function(){return""},r}return r(e,t),e.prototype.attachMenu=function(t){this.menu=t},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this);var e=this.html;e.appendChild(this.generateTitle()),e.appendChild(this.contentDiv),e.appendChild(this.generateSignature()),e.appendChild(this.close.html),e.setAttribute("tabindex","0")},e.prototype.post=function(){t.prototype.post.call(this);var e=document.documentElement,o=this.html,n=window.innerHeight||e.clientHeight||e.scrollHeight||0,r=Math.floor(-o.offsetWidth/2),i=Math.floor((n-o.offsetHeight)/3);o.setAttribute("style","margin-left: "+r+"px; top: "+i+"px;"),window.event instanceof MouseEvent&&o.classList.add(s.HtmlClasses.MOUSEPOST),o.focus()},e.prototype.display=function(){this.menu.registerWidget(this),this.contentDiv.innerHTML=this.content();var t=this.menu.html;t.parentNode&&t.parentNode.removeChild(t),this.menu.frame.appendChild(this.html)},e.prototype.click=function(t){},e.prototype.keydown=function(e){this.bubbleKey(),t.prototype.keydown.call(this,e)},e.prototype.escape=function(t){this.unpost()},e.prototype.unpost=function(){t.prototype.unpost.call(this),this.html.classList.remove(s.HtmlClasses.MOUSEPOST),this.menu.unregisterWidget(this)},e.prototype.generateClose=function(){var t=new i.CloseButton(this),e=t.html;return e.classList.add(s.HtmlClasses.INFOCLOSE),e.setAttribute("aria-label","Close Dialog Box"),t},e.prototype.generateTitle=function(){var t=document.createElement("span");return t.innerHTML=this.title,t.classList.add(s.HtmlClasses.INFOTITLE),t},e.prototype.generateContent=function(){var t=document.createElement("div");return t.classList.add(s.HtmlClasses.INFOCONTENT),t.setAttribute("tabindex","0"),t},e.prototype.generateSignature=function(){var t=document.createElement("span");return t.innerHTML=this.signature,t.classList.add(s.HtmlClasses.INFOSIGNATURE),t},e.prototype.toJson=function(){return{type:""}},e}(o(3).AbstractPostable);e.Info=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Variable=void 0;var n=o(1),r=function(){function t(t,e,o){this._name=t,this.getter=e,this.setter=o,this.items=[]}return t.fromJson=function(t,e,o){var n=new this(e.name,e.getter,e.setter);o.insert(n)},Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t.prototype.getValue=function(t){try{return this.getter(t)}catch(t){return n.MenuUtil.error(t,"Command of variable "+this.name+" failed."),null}},t.prototype.setValue=function(t,e){try{this.setter(t,e)}catch(t){n.MenuUtil.error(t,"Command of variable "+this.name+" failed.")}this.update()},t.prototype.register=function(t){-1===this.items.indexOf(t)&&this.items.push(t)},t.prototype.unregister=function(t){var e=this.items.indexOf(t);-1!==e&&this.items.splice(e,1)},t.prototype.update=function(){this.items.forEach((function(t){return t.update()}))},t.prototype.registerCallback=function(t){this.items.forEach((function(e){return e.registerCallback(t)}))},t.prototype.unregisterCallback=function(t){this.items.forEach((function(e){return e.unregisterCallback(t)}))},t.prototype.toJson=function(){return{type:"variable",name:this.name,getter:this.getter.toString(),setter:this.setter.toString()}},t}();e.Variable=r},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),i=this&&this.__read||function(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var n,r,i=o.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SelectionBox=e.SelectionMenu=void 0;var s=o(1),a=o(0),u=o(6),c=o(11),p=function(t){function e(e){var o=t.call(this)||this;return o.anchor=e,o.className=a.HtmlClasses.SELECTIONMENU,o.variablePool=o.anchor.menu.pool,o.baseMenu=o.anchor.menu,o}return r(e,t),e.fromJson=function(t,e,o){var n=e.title,r=e.values,i=e.variable,s=new this(o),a=t.get("label")(t,{content:n||"",id:n||"id"},s),u=t.get("rule")(t,{},s),c=r.map((function(e){return t.get("radio")(t,{content:e,variable:i,id:e},s)})),p=[a,u].concat(c);return s.items=p,s},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.items.forEach((function(t){return t.html.classList.add(a.HtmlClasses.SELECTIONITEM)}))},e.prototype.display=function(){},e.prototype.right=function(t){this.anchor.right(t)},e.prototype.left=function(t){this.anchor.left(t)},e}(u.AbstractMenu);e.SelectionMenu=p;var l=function(t){function e(e,o,n,r){void 0===n&&(n="none"),void 0===r&&(r="vertical");var i=t.call(this,e,null,o)||this;return i.style=n,i.grid=r,i._selections=[],i.prefix="ctxt-selection",i._balanced=!0,i}return r(e,t),e.fromJson=function(t,e,o){var n=e.title,r=e.signature,i=e.selections,s=new this(n,r,e.order,e.grid);s.attachMenu(o);var a=i.map((function(e){return t.get("selectionMenu")(t,e,s)}));return s.selections=a,s},e.prototype.attachMenu=function(t){this.menu=t},Object.defineProperty(e.prototype,"selections",{get:function(){return this._selections},set:function(t){var e=this;this._selections=[],t.forEach((function(t){return e.addSelection(t)}))},enumerable:!1,configurable:!0}),e.prototype.addSelection=function(t){t.anchor=this,this._selections.push(t)},e.prototype.rowDiv=function(t){var e=this,o=document.createElement("div");this.contentDiv.appendChild(o);var n=t.map((function(t){return o.appendChild(t.html),t.html.id||(t.html.id=e.prefix+s.MenuUtil.counter()),t.html.getBoundingClientRect()})),r=n.map((function(t){return t.width})),i=r.reduce((function(t,e){return t+e}),0),u=n.reduce((function(t,e){return Math.max(t,e.height)}),0);return o.classList.add(a.HtmlClasses.SELECTIONDIVIDER),o.setAttribute("style","height: "+u+"px;"),[o,i,u,r]},e.prototype.display=function(){if(t.prototype.display.call(this),this.order(),this.selections.length){for(var e=[],o=0,n=[],r=this.getChunkSize(this.selections.length),s=function(t){var s=a.selections.slice(t,t+r),u=i(a.rowDiv(s),4),c=u[0],p=u[1],l=u[2],h=u[3];e.push(c),o=Math.max(o,p),s.forEach((function(t){return t.html.style.height=l+"px"})),n=a.combineColumn(n,h)},a=this,u=0;u<this.selections.length;u+=r)s(u);this._balanced&&(this.balanceColumn(e,n),o=n.reduce((function(t,e){return t+e}),20)),e.forEach((function(t){return t.style.width=o+"px"}))}},e.prototype.getChunkSize=function(t){switch(this.grid){case"square":return Math.floor(Math.sqrt(t));case"horizontal":return Math.floor(t/e.chunkSize);case"vertical":default:return e.chunkSize}},e.prototype.balanceColumn=function(t,e){t.forEach((function(t){for(var o=Array.from(t.children),n=0,r=void 0;r=o[n];n++)r.style.width=e[n]+"px"}))},e.prototype.combineColumn=function(t,e){for(var o=[],n=0;t[n]||e[n];){if(!t[n]){o=o.concat(e.slice(n));break}if(!e[n]){o=o.concat(t.slice(n));break}o.push(Math.max(t[n],e[n])),n++}return o},e.prototype.left=function(t){var e=this;this.move(t,(function(t){return(0===t?e.selections.length:t)-1}))},e.prototype.right=function(t){var e=this;this.move(t,(function(t){return t===e.selections.length-1?0:t+1}))},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.SELECTION)},e.prototype.generateContent=function(){var e=t.prototype.generateContent.call(this);return e.classList.add(a.HtmlClasses.SELECTIONBOX),e.removeAttribute("tabindex"),e},e.prototype.findSelection=function(t){var e=t.target,o=null;if(e.id&&(o=this.selections.find((function(t){return t.html.id===e.id}))),!o){var n=e.parentElement.id;o=this.selections.find((function(t){return t.html.id===n}))}return o},e.prototype.move=function(t,e){var o=this.findSelection(t);o.focused&&o.focused.unfocus();var n=e(this.selections.indexOf(o));this.selections[n].focus()},e.prototype.order=function(){this.selections.sort(e.orderMethod.get(this.style))},e.prototype.toJson=function(){return{type:""}},e.chunkSize=4,e.orderMethod=new Map([["alphabetical",function(t,e){return t.items[0].content.localeCompare(e.items[0].content)}],["none",function(t,e){return 1}],["decreasing",function(t,e){var o=t.items.length,n=e.items.length;return o<n?1:n<o?-1:0}],["increasing",function(t,e){var o=t.items.length,n=e.items.length;return o<n?-1:n<o?1:0}]]),e}(c.Info);e.SelectionBox=l},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ParserFactory=void 0;var n=function(){function t(t){this._parser=new Map(t)}return t.prototype.get=function(t){return this._parser.get(t)},t.prototype.add=function(t,e){this._parser.set(t,e)},t}();e.ParserFactory=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ParserFactory=e.Variable=e.SelectionBox=e.version=e.Parser=e.Popup=e.Info=e.CssStyles=e.ContextMenu=void 0;var n=o(7),r=o(20),i=o(11),s=o(22),a=o(23),u=o(13),c=o(12),p=o(14);e.ContextMenu=n.ContextMenu,e.CssStyles=r.CssStyles,e.Info=i.Info,e.Popup=s.Popup,e.Parser=a.Parser,e.version="0.4.2",e.SelectionBox=u.SelectionBox,e.Variable=c.Variable,e.ParserFactory=p.ParserFactory},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractNavigatable=void 0;var n=o(4),r=o(17),i=function(){function t(){this.bubble=!1}return t.prototype.bubbleKey=function(){this.bubble=!0},t.prototype.keydown=function(t){switch(t.keyCode){case n.KEY.ESCAPE:this.escape(t);break;case n.KEY.RIGHT:this.right(t);break;case n.KEY.LEFT:this.left(t);break;case n.KEY.UP:this.up(t);break;case n.KEY.DOWN:this.down(t);break;case n.KEY.RETURN:case n.KEY.SPACE:this.space(t);break;default:return}this.bubble?this.bubble=!1:this.stop(t)},t.prototype.escape=function(t){},t.prototype.space=function(t){},t.prototype.left=function(t){},t.prototype.right=function(t){},t.prototype.up=function(t){},t.prototype.down=function(t){},t.prototype.stop=function(t){t&&(t.stopPropagation(),t.preventDefault(),t.cancelBubble=!0)},t.prototype.mousedown=function(t){return this.stop(t)},t.prototype.mouseup=function(t){return this.stop(t)},t.prototype.mouseover=function(t){return this.stop(t)},t.prototype.mouseout=function(t){return this.stop(t)},t.prototype.click=function(t){return this.stop(t)},t.prototype.addEvents=function(t){t.addEventListener(r.MOUSE.DOWN,this.mousedown.bind(this)),t.addEventListener(r.MOUSE.UP,this.mouseup.bind(this)),t.addEventListener(r.MOUSE.OVER,this.mouseover.bind(this)),t.addEventListener(r.MOUSE.OUT,this.mouseout.bind(this)),t.addEventListener(r.MOUSE.CLICK,this.click.bind(this)),t.addEventListener("keydown",this.keydown.bind(this)),t.addEventListener("dragstart",this.stop.bind(this)),t.addEventListener(r.MOUSE.SELECTSTART,this.stop.bind(this)),t.addEventListener("contextmenu",this.stop.bind(this)),t.addEventListener(r.MOUSE.DBLCLICK,this.stop.bind(this))},t}();e.AbstractNavigatable=i},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MOUSE=void 0,e.MOUSE={CLICK:"click",DBLCLICK:"dblclick",DOWN:"mousedown",UP:"mouseup",OVER:"mouseover",OUT:"mouseout",MOVE:"mousemove",SELECTEND:"selectend",SELECTSTART:"selectstart"}},function(t,e,o){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MenuStore=void 0;var r=o(1),i=o(0),s=o(4),a=function(){function t(t){this.menu=t,this.store=[],this._active=null,this.counter=0,this.attachedClass=i.HtmlClasses.ATTACHED+"_"+r.MenuUtil.counter(),this.taborder=!0,this.attrMap={}}return Object.defineProperty(t.prototype,"active",{get:function(){return this._active},set:function(t){do{if(-1!==this.store.indexOf(t)){this._active=t;break}t=t.parentNode}while(t)},enumerable:!1,configurable:!0}),t.prototype.next=function(){var t=this.store.length;if(0===t)return this.active=null,null;var e=this.store.indexOf(this.active);return e=-1===e?0:e<t-1?e+1:0,this.active=this.store[e],this.active},t.prototype.previous=function(){var t=this.store.length;if(0===t)return this.active=null,null;var e=t-1,o=this.store.indexOf(this.active);return o=-1===o||0===o?e:o-1,this.active=this.store[o],this.active},t.prototype.clear=function(){this.remove(this.store)},t.prototype.insert=function(t){var e,o,r=t instanceof HTMLElement?[t]:t;try{for(var i=n(r),s=i.next();!s.done;s=i.next()){var a=s.value;this.insertElement(a)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}this.sort()},t.prototype.remove=function(t){var e,o,r=t instanceof HTMLElement?[t]:t;try{for(var i=n(r),s=i.next();!s.done;s=i.next()){var a=s.value;this.removeElement(a)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}this.sort()},t.prototype.inTaborder=function(t){this.taborder&&!t&&this.removeTaborder(),!this.taborder&&t&&this.insertTaborder(),this.taborder=t},t.prototype.insertTaborder=function(){this.taborder&&this.insertTaborder_()},t.prototype.removeTaborder=function(){this.taborder&&this.removeTaborder_()},t.prototype.insertElement=function(t){t.classList.contains(this.attachedClass)||(t.classList.add(this.attachedClass),this.taborder&&this.addTabindex(t),this.addEvents(t))},t.prototype.removeElement=function(t){t.classList.contains(this.attachedClass)&&(t.classList.remove(this.attachedClass),this.taborder&&this.removeTabindex(t),this.removeEvents(t))},t.prototype.sort=function(){var t=document.getElementsByClassName(this.attachedClass);this.store=[].slice.call(t)},t.prototype.insertTaborder_=function(){this.store.forEach((function(t){return t.setAttribute("tabindex","0")}))},t.prototype.removeTaborder_=function(){this.store.forEach((function(t){return t.setAttribute("tabindex","-1")}))},t.prototype.addTabindex=function(t){t.hasAttribute("tabindex")&&t.setAttribute(i.HtmlAttrs.OLDTAB,t.getAttribute("tabindex")),t.setAttribute("tabindex","0")},t.prototype.removeTabindex=function(t){t.hasAttribute(i.HtmlAttrs.OLDTAB)?(t.setAttribute("tabindex",t.getAttribute(i.HtmlAttrs.OLDTAB)),t.removeAttribute(i.HtmlAttrs.OLDTAB)):t.removeAttribute("tabindex")},t.prototype.addEvents=function(t){t.hasAttribute(i.HtmlAttrs.COUNTER)||(this.addEvent(t,"contextmenu",this.menu.post.bind(this.menu)),this.addEvent(t,"keydown",this.keydown.bind(this)),t.setAttribute(i.HtmlAttrs.COUNTER,this.counter.toString()),this.counter++)},t.prototype.addEvent=function(t,e,o){var n=i.HtmlAttrs[e.toUpperCase()+"FUNC"];this.attrMap[n+this.counter]=o,t.addEventListener(e,o)},t.prototype.removeEvents=function(t){if(t.hasAttribute(i.HtmlAttrs.COUNTER)){var e=t.getAttribute(i.HtmlAttrs.COUNTER);this.removeEvent(t,"contextmenu",e),this.removeEvent(t,"keydown",e),t.removeAttribute(i.HtmlAttrs.COUNTER)}},t.prototype.removeEvent=function(t,e,o){var n=i.HtmlAttrs[e.toUpperCase()+"FUNC"],r=this.attrMap[n+o];t.removeEventListener(e,r)},t.prototype.keydown=function(t){t.keyCode===s.KEY.SPACE&&(this.menu.post(t),t.preventDefault(),t.stopImmediatePropagation())},t}();e.MenuStore=a},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VariablePool=void 0;var n=function(){function t(){this.pool={}}return t.prototype.insert=function(t){this.pool[t.name]=t},t.prototype.lookup=function(t){return this.pool[t]},t.prototype.remove=function(t){delete this.pool[t]},t.prototype.update=function(){for(var t in this.pool)this.pool[t].update()},t}();e.VariablePool=n},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CssStyles=void 0;var n=o(0);!function(t){function e(t){return"."+(n.HtmlClasses[t]||t)}var o={};o[e("INFOCLOSE")]="{  top:.2em; right:.2em;}",o[e("INFOCONTENT")]="{  overflow:auto; text-align:left; font-size:80%;  padding:.4em .6em; border:1px inset; margin:1em 0px;  max-height:20em; max-width:30em; background-color:#EEEEEE;  white-space:normal;}",o[e("INFO")+e("MOUSEPOST")]="{outline:none;}",o[e("INFO")]='{  position:fixed; left:50%; width:auto; text-align:center;  border:3px outset; padding:1em 2em; background-color:#DDDDDD;  color:black;  cursor:default; font-family:message-box; font-size:120%;  font-style:normal; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 15px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius:15px;               /* Safari and Chrome */  -moz-border-radius:15px;                  /* Firefox */  -khtml-border-radius:15px;                /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color="gray", Positive="true"); /* IE */}';var r={};r[e("MENU")]="{  position:absolute;  background-color:white;  color:black;  width:auto; padding:5px 0px;  border:1px solid #CCCCCC; margin:0; cursor:default;  font: menu; text-align:left; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 5px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius: 5px;             /* Safari and Chrome */  -moz-border-radius: 5px;                /* Firefox */  -khtml-border-radius: 5px;              /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */}",r[e("MENUITEM")]="{  padding: 1px 2em;  background:transparent;}",r[e("MENUARROW")]="{  position:absolute; right:.5em; padding-top:.25em; color:#666666;  font-family: null; font-size: .75em}",r[e("MENUACTIVE")+" "+e("MENUARROW")]="{color:white}",r[e("MENUARROW")+e("RTL")]="{left:.5em; right:auto}",r[e("MENUCHECK")]="{  position:absolute; left:.7em;  font-family: null}",r[e("MENUCHECK")+e("RTL")]="{ right:.7em; left:auto }",r[e("MENURADIOCHECK")]="{  position:absolute; left: .7em;}",r[e("MENURADIOCHECK")+e("RTL")]="{  right: .7em; left:auto}",r[e("MENUINPUTBOX")]="{  padding-left: 1em; right:.5em; color:#666666;  font-family: null;}",r[e("MENUINPUTBOX")+e("RTL")]="{  left: .1em;}",r[e("MENUCOMBOBOX")]="{  left:.1em; padding-bottom:.5em;}",r[e("MENUSLIDER")]="{  left: .1em;}",r[e("SLIDERVALUE")]="{  position:absolute; right:.1em; padding-top:.25em; color:#333333;  font-size: .75em}",r[e("SLIDERBAR")]="{  outline: none; background: #d3d3d3}",r[e("MENULABEL")]="{  padding: 1px 2em 3px 1.33em;  font-style:italic}",r[e("MENURULE")]="{  border-top: 1px solid #DDDDDD;  margin: 4px 3px;}",r[e("MENUDISABLED")]="{  color:GrayText}",r[e("MENUACTIVE")]="{  background-color: #606872;  color: white;}",r[e("MENUDISABLED")+":focus"]="{  background-color: #E8E8E8}",r[e("MENULABEL")+":focus"]="{  background-color: #E8E8E8}",r[e("CONTEXTMENU")+":focus"]="{  outline:none}",r[e("CONTEXTMENU")+" "+e("MENUITEM")+":focus"]="{  outline:none}",r[e("SELECTIONMENU")]="{  position:relative; float:left;  border-bottom: none; -webkit-box-shadow:none; -webkit-border-radius:0px; }",r[e("SELECTIONITEM")]="{  padding-right: 1em;}",r[e("SELECTION")]="{  right: 40%; width:50%; }",r[e("SELECTIONBOX")]="{  padding: 0em; max-height:20em; max-width: none;  background-color:#FFFFFF;}",r[e("SELECTIONDIVIDER")]="{  clear: both; border-top: 2px solid #000000;}",r[e("MENU")+" "+e("MENUCLOSE")]="{  top:-10px; left:-10px}";var i={};i[e("MENUCLOSE")]='{  position:absolute;  cursor:pointer;  display:inline-block;  border:2px solid #AAA;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  font-family: "Courier New", Courier;  font-size:24px;  color:#F0F0F0}',i[e("MENUCLOSE")+" span"]="{  display:block; background-color:#AAA; border:1.5px solid;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  line-height:0;  padding:8px 0 6px     /* may need to be browser-specific */}",i[e("MENUCLOSE")+":hover"]="{  color:white!important;  border:2px solid #CCC!important}",i[e("MENUCLOSE")+":hover span"]="{  background-color:#CCC!important}",i[e("MENUCLOSE")+":hover:focus"]="{  outline:none}";var s=!1,a=!1,u=!1;function c(t){u||(p(i,t),u=!0)}function p(t,e){var o=e||document,n=o.createElement("style");n.type="text/css";var r="";for(var i in t)r+=i,r+=" ",r+=t[i],r+="\n";n.innerHTML=r,o.head.appendChild(n)}t.addMenuStyles=function(t){a||(p(r,t),a=!0,c(t))},t.addInfoStyles=function(t){s||(p(o,t),s=!0,c(t))}}(e.CssStyles||(e.CssStyles={}))},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.CloseButton=void 0;var i=o(3),s=o(0),a=function(t){function e(e){var o=t.call(this)||this;return o.element=e,o.className=s.HtmlClasses.MENUCLOSE,o.role="button",o}return r(e,t),e.prototype.generateHtml=function(){var t=document.createElement("span");t.classList.add(this.className),t.setAttribute("role",this.role),t.setAttribute("tabindex","0");var e=document.createElement("span");e.textContent="\xd7",t.appendChild(e),this.html=t},e.prototype.display=function(){},e.prototype.unpost=function(){t.prototype.unpost.call(this),this.element.unpost()},e.prototype.keydown=function(e){this.bubbleKey(),t.prototype.keydown.call(this,e)},e.prototype.space=function(t){this.unpost(),this.stop(t)},e.prototype.mousedown=function(t){this.unpost(),this.stop(t)},e}(i.AbstractPostable);e.CloseButton=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Popup=void 0;var i=function(t){function e(e,o){var n=t.call(this)||this;return n.title=e,n.window=null,n.localSettings={left:Math.round((screen.width-400)/2),top:Math.round((screen.height-300)/3)},n.windowList=[],n.mobileFlag=!1,n.active=null,n.content=o||function(){return""},n}return r(e,t),e.fromJson=function(){},e.prototype.attachMenu=function(t){this.menu=t},e.prototype.post=function(){this.display()},e.prototype.display=function(){this.active=this.menu.store.active;var t=[];for(var o in e.popupSettings)t.push(o+"="+e.popupSettings[o]);for(var o in this.localSettings)t.push(o+"="+this.localSettings[o]);this.window=window.open("","_blank",t.join(",")),this.windowList.push(this.window);var n=this.window.document;this.mobileFlag?(n.open(),n.write('<html><head><meta name="viewport" content="width=device-width, initial-scale=1.0" /><title>'+this.title+'</title></head><body style="font-size:85%">'),n.write("<pre>"+this.generateContent()+"</pre>"),n.write('<hr><input type="button" value="Close" onclick="window.close()" />'),n.write("</body></html>"),n.close()):(n.open(),n.write("<html><head><title>"+this.title+'</title></head><body style="font-size:85%">'),n.write("<table><tr><td><pre>"+this.generateContent()+"</pre></td></tr></table>"),n.write("</body></html>"),n.close(),setTimeout(this.resize.bind(this),50))},e.prototype.unpost=function(){this.windowList.forEach((function(t){return t.close()})),this.window=null},e.prototype.generateContent=function(){return this.content(this.active)},e.prototype.resize=function(){var t=this.window.document.body.firstChild,e=this.window.outerHeight-this.window.innerHeight||30,o=this.window.outerWidth-this.window.innerWidth||30;o=Math.max(140,Math.min(Math.floor(.5*this.window.screen.width),t.offsetWidth+o+25)),e=Math.max(40,Math.min(Math.floor(.5*this.window.screen.height),t.offsetHeight+e+25)),this.window.resizeTo(o,e);var n=this.active.getBoundingClientRect();if(n){var r=Math.max(0,Math.min(n.right-Math.floor(o/2),this.window.screen.width-o-20)),i=Math.max(0,Math.min(n.bottom-Math.floor(e/2),this.window.screen.height-e-20));this.window.moveTo(r,i)}this.active=null},e.prototype.toJson=function(){return{type:""}},e.popupSettings={status:"no",toolbar:"no",locationbar:"no",menubar:"no",directories:"no",personalbar:"no",resizable:"yes",scrollbars:"yes",width:400,height:300},e}(o(3).AbstractPostable);e.Popup=i},function(t,e,o){"use strict";var n=this&&this.__rest||function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]])}return o},r=this&&this.__read||function(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var n,r,i=o.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}return s},i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(r(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.Parser=void 0;var a=o(24),u=o(7),c=o(12),p=o(25),l=o(26),h=o(27),f=o(28),d=o(10),m=o(29),y=o(30),b=o(31),v=o(13),E=o(14),_=function(){function t(t){var e=this;void 0===t&&(t=[]),this._initList=[["command",a.Command.fromJson.bind(a.Command)],["checkbox",p.Checkbox.fromJson.bind(p.Checkbox)],["combo",l.Combo.fromJson.bind(l.Combo)],["slider",y.Slider.fromJson.bind(y.Slider)],["label",h.Label.fromJson.bind(h.Label)],["radio",f.Radio.fromJson.bind(f.Radio)],["rule",m.Rule.fromJson.bind(m.Rule)],["submenu",d.Submenu.fromJson.bind(d.Submenu)],["contextMenu",u.ContextMenu.fromJson.bind(u.ContextMenu)],["subMenu",b.SubMenu.fromJson.bind(b.SubMenu)],["variable",c.Variable.fromJson.bind(c.Variable)],["items",this.items.bind(this)],["selectionMenu",v.SelectionMenu.fromJson.bind(v.SelectionMenu)],["selectionBox",v.SelectionBox.fromJson.bind(v.SelectionBox)]],this._factory=new E.ParserFactory(this._initList),t.forEach((function(t){var o=r(t,2),n=o[0],i=o[1];return e.factory.add(n,i)}))}return Object.defineProperty(t.prototype,"factory",{get:function(){return this._factory},enumerable:!1,configurable:!0}),t.prototype.items=function(t,e,o){var n,r,s=[];try{for(var a=i(e),u=a.next();!u.done;u=a.next()){var c=u.value,p=this.parse(c,o);p&&(o.items.push(p),c.disabled&&p.disable(),c.hidden&&s.push(p))}}catch(t){n={error:t}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return s.forEach((function(t){return t.hide()})),o.items},t.prototype.parse=function(t){for(var e=[],o=1;o<arguments.length;o++)e[o-1]=arguments[o];var r=t.type,i=n(t,["type"]),a=this.factory.get(r);return a?a.apply(void 0,s([this.factory,i],e)):null},t}();e.Parser=_},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Command=void 0;var i=o(2),s=o(1),a=function(t){function e(e,o,n,r){var i=t.call(this,e,"command",o,r)||this;return i.command=n,i}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.action,e.id)},e.prototype.executeAction=function(){try{this.command(s.MenuUtil.getActiveElement(this))}catch(t){s.MenuUtil.error(t,"Illegal command callback.")}s.MenuUtil.close(this)},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractItem);e.Command=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Checkbox=void 0;var i=o(5),s=o(1),a=o(0),u=function(t){function e(e,o,n,r){var i=t.call(this,e,"checkbox",o,r)||this;return i.role="menuitemcheckbox",i.variable=e.pool.lookup(n),i.register(),i}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.variable,e.id)},e.prototype.executeAction=function(){this.variable.setValue(!this.variable.getValue()),s.MenuUtil.close(this)},e.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(a.HtmlClasses.MENUCHECK)},e.prototype.updateAria=function(){this.html.setAttribute("aria-checked",this.variable.getValue()?"true":"false")},e.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()?"":"none"},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractVariableItem);e.Checkbox=u},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Combo=void 0;var i=o(5),s=o(1),a=o(0),u=o(4),c=function(t){function e(e,o,n,r){var i=t.call(this,e,"combobox",o,r)||this;return i.role="combobox",i.inputEvent=!1,i.variable=e.pool.lookup(n),i.register(),i}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.variable,e.id)},e.prototype.executeAction=function(){this.variable.setValue(this.input.value,s.MenuUtil.getActiveElement(this))},e.prototype.space=function(e){t.prototype.space.call(this,e),s.MenuUtil.close(this)},e.prototype.focus=function(){t.prototype.focus.call(this),this.input.focus()},e.prototype.unfocus=function(){t.prototype.unfocus.call(this),this.updateSpan()},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.MENUCOMBOBOX)},e.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.classList.add(a.HtmlClasses.MENUINPUTBOX),this.input=document.createElement("input"),this.input.addEventListener("keydown",this.inputKey.bind(this)),this.input.setAttribute("size","10em"),this.input.setAttribute("type","text"),this.input.setAttribute("tabindex","-1"),this.span.appendChild(this.input)},e.prototype.inputKey=function(t){this.bubbleKey(),this.inputEvent=!0},e.prototype.keydown=function(e){if(this.inputEvent&&e.keyCode!==u.KEY.ESCAPE&&e.keyCode!==u.KEY.RETURN)return this.inputEvent=!1,void e.stopPropagation();t.prototype.keydown.call(this,e),e.stopPropagation()},e.prototype.updateAria=function(){},e.prototype.updateSpan=function(){var t;try{t=this.variable.getValue(s.MenuUtil.getActiveElement(this))}catch(e){t=""}this.input.value=t},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractVariableItem);e.Combo=c},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Label=void 0;var i=o(2),s=o(0),a=function(t){function e(e,o,n){return t.call(this,e,"label",o,n)||this}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.id)},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.html.classList.add(s.HtmlClasses.MENULABEL)},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractItem);e.Label=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Radio=void 0;var i=o(5),s=o(1),a=o(0),u=function(t){function e(e,o,n,r){var i=t.call(this,e,"radio",o,r)||this;return i.role="menuitemradio",i.variable=e.pool.lookup(n),i.register(),i}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.variable,e.id)},e.prototype.executeAction=function(){this.variable.setValue(this.id),s.MenuUtil.close(this)},e.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(a.HtmlClasses.MENURADIOCHECK)},e.prototype.updateAria=function(){this.html.setAttribute("aria-checked",this.variable.getValue()===this.id?"true":"false")},e.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()===this.id?"":"none"},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractVariableItem);e.Radio=u},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Rule=void 0;var i=o(9),s=o(0),a=function(t){function e(e){var o=t.call(this,e,"rule")||this;return o.className=s.HtmlClasses.MENUITEM,o.role="separator",o}return r(e,t),e.fromJson=function(t,e,o){return new this(o)},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this);var e=this.html;e.classList.add(s.HtmlClasses.MENURULE),e.setAttribute("aria-orientation","vertical")},e.prototype.addEvents=function(t){},e.prototype.toJson=function(){return{type:"rule"}},e}(i.AbstractEntry);e.Rule=a},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.Slider=void 0;var i=o(5),s=o(1),a=o(0),u=o(4),c=function(t){function e(e,o,n,r){var i=t.call(this,e,"slider",o,r)||this;return i.role="slider",i.labelId="ctx_slideLabel"+s.MenuUtil.counter(),i.valueId="ctx_slideValue"+s.MenuUtil.counter(),i.inputEvent=!1,i.variable=e.pool.lookup(n),i.register(),i}return r(e,t),e.fromJson=function(t,e,o){return new this(o,e.content,e.variable,e.id)},e.prototype.executeAction=function(){this.variable.setValue(this.input.value,s.MenuUtil.getActiveElement(this)),this.update()},e.prototype.space=function(e){t.prototype.space.call(this,e),s.MenuUtil.close(this)},e.prototype.focus=function(){t.prototype.focus.call(this),this.input.focus()},e.prototype.unfocus=function(){t.prototype.unfocus.call(this),this.updateSpan()},e.prototype.generateHtml=function(){t.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.MENUSLIDER),this.valueSpan=document.createElement("span"),this.valueSpan.setAttribute("id",this.valueId),this.valueSpan.classList.add(a.HtmlClasses.SLIDERVALUE),this.html.appendChild(this.valueSpan)},e.prototype.generateSpan=function(){this.span=document.createElement("span"),this.labelSpan=document.createElement("span"),this.labelSpan.setAttribute("id",this.labelId),this.labelSpan.appendChild(this.html.childNodes[0]),this.html.appendChild(this.labelSpan),this.input=document.createElement("input"),this.input.setAttribute("type","range"),this.input.setAttribute("min","0"),this.input.setAttribute("max","100"),this.input.setAttribute("aria-valuemin","0"),this.input.setAttribute("aria-valuemax","100"),this.input.setAttribute("aria-labelledby",this.labelId),this.input.addEventListener("keydown",this.inputKey.bind(this)),this.input.addEventListener("input",this.executeAction.bind(this)),this.input.classList.add(a.HtmlClasses.SLIDERBAR),this.span.appendChild(this.input)},e.prototype.inputKey=function(t){this.inputEvent=!0},e.prototype.mousedown=function(t){t.stopPropagation()},e.prototype.mouseup=function(t){event.stopPropagation()},e.prototype.keydown=function(e){var o=e.keyCode;return o===u.KEY.UP||o===u.KEY.DOWN?(e.preventDefault(),void t.prototype.keydown.call(this,e)):this.inputEvent&&o!==u.KEY.ESCAPE&&o!==u.KEY.RETURN?(this.inputEvent=!1,void e.stopPropagation()):(t.prototype.keydown.call(this,e),void e.stopPropagation())},e.prototype.updateAria=function(){var t=this.variable.getValue();t&&this.input&&(this.input.setAttribute("aria-valuenow",t),this.input.setAttribute("aria-valuetext",t+"%"))},e.prototype.updateSpan=function(){var t;try{t=this.variable.getValue(s.MenuUtil.getActiveElement(this)),this.valueSpan.innerHTML=t+"%"}catch(e){t=""}this.input.value=t},e.prototype.toJson=function(){return{type:""}},e}(i.AbstractVariableItem);e.Slider=c},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.SubMenu=void 0;var i=function(t){function e(e){var o=t.call(this)||this;return o._anchor=e,o.variablePool=o.anchor.menu.pool,o.setBaseMenu(),o}return r(e,t),e.fromJson=function(t,e,o){var n=e.items,r=new this(o),i=t.get("items")(t,n,r);return r.items=i,r},Object.defineProperty(e.prototype,"anchor",{get:function(){return this._anchor},enumerable:!1,configurable:!0}),e.prototype.post=function(){if(this.anchor.menu.isPosted()){for(var e=this.anchor.html,o=this.html,n=this.baseMenu.frame,r=e.offsetWidth,i=r-2,s=0;e&&e!==n;)i+=e.offsetLeft,s+=e.offsetTop,e=e.parentNode;i+o.offsetWidth>document.body.offsetWidth-5&&(i=Math.max(5,i-r-o.offsetWidth+6)),t.prototype.post.call(this,i,s)}},e.prototype.display=function(){this.baseMenu.frame.appendChild(this.html)},e.prototype.setBaseMenu=function(){var t=this;do{t=t.anchor.menu}while(t instanceof e);this.baseMenu=t},e.prototype.left=function(t){this.focused=null,this.anchor.focus()},e.prototype.toJson=function(){return{type:""}},e}(o(6).AbstractMenu);e.SubMenu=i}])}));