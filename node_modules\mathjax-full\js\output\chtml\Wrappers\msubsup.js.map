{"version": 3, "file": "msubsup.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/msubsup.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,iDAAgD;AAChD,+DAAiE;AACjE,+DAAiE;AACjE,+DAAoE;AACpE,wEAAuF;AAYvF;IACA,6BAA0G;IAD1G;;IAQA,CAAC;IAFe,cAAI,GAAG,oBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAE9C,gBAAC;CAAA,AARD,CACA,IAAA,4BAAe,EAA2E,+BAAe,CAAC,GAOzG;AARY,8BAAS;AAmBtB;IACA,6BAA0G;IAD1G;;IAQA,CAAC;IAFe,cAAI,GAAG,oBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAE9C,gBAAC;CAAA,AARD,CACA,IAAA,4BAAe,EAA2E,+BAAe,CAAC,GAOzG;AARY,8BAAS;AAmBtB;IACA,gCAA6G;IAD7G;;IA6CA,CAAC;IApBQ,8BAAO,GAAd,UAAe,MAAS;QACtB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACvC,IAAA,KAAA,OAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAA,EAAhE,IAAI,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAkD,CAAC;QAClE,IAAA,KAAA,OAAY,IAAI,CAAC,MAAM,EAAE,IAAA,EAArB,CAAC,QAAA,EAAE,CAAC,QAAiB,CAAC;QAChC,IAAM,KAAK,GAAG,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACpB,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAM,CAAC;QAC3E,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAC,KAAK,EAAE,EAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,EAAC,CAAC,CAAC,CAAC;QACpF,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAChC,IAAI,EAAE,EAAE;YACN,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SAC9D;IACH,CAAC;IArCa,iBAAI,GAAG,uBAAU,CAAC,SAAS,CAAC,IAAI,CAAC;IAKjC,mBAAM,GAAc;QAChC,YAAY,EAAE;YACZ,OAAO,EAAE,cAAc;YACvB,eAAe,EAAE,OAAO;YACxB,cAAc,EAAE,QAAQ;SACzB;QACD,yBAAyB,EAAE;YACzB,OAAO,EAAE,OAAO;SACjB;KACF,CAAC;IAyBJ,mBAAC;CAAA,AA7CD,CACA,IAAA,+BAAkB,EAA2E,+BAAe,CAAC,GA4C5G;AA7CY,oCAAY"}