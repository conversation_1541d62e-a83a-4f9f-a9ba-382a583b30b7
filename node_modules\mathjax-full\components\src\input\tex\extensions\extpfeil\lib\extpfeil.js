import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/extpfeil/ExtpfeilConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/extpfeil', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      extpfeil: {
        ExtpfeilConfiguration: module1
      }
    }
  }
}});
