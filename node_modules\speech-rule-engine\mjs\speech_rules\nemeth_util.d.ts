import { AuditoryDescription } from '../audio/auditory_description.js';
import { Span } from '../audio/span.js';
export declare function openingFraction(node: Element): Span[];
export declare function closingFraction(node: Element): Span[];
export declare function overFraction(node: Element): Span[];
export declare function overBevelledFraction(node: Element): Span[];
export declare function hyperFractionBoundary(node: Element): Element[];
export declare function openingRadical(node: Element): Span[];
export declare function closingRadical(node: Element): Span[];
export declare function indexRadical(node: Element): Span[];
export declare function relationIterator(nodes: Element[], context: string): () => AuditoryDescription[];
export declare function implicitIterator(nodes: Element[], context: string): () => AuditoryDescription[];
export declare function contentIterator(nodes: Element[], context: string): () => AuditoryDescription[];
