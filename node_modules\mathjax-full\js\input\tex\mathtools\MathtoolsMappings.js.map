{"version": 3, "file": "MathtoolsMappings.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/mathtools/MathtoolsMappings.ts"], "names": [], "mappings": ";;;;;AAuBA,uEAA8C;AAC9C,gDAAyE;AACzE,sDAA+C;AAE/C,6DAAuD;AAmBvD,IAAI,yBAAU,CAAC,kBAAkB,EAAE;IAEjC,SAAS,EAAG,CAAC,aAAa,EAAE,6BAAW,CAAC,KAAK,CAAC,IAAI,CAAC;IACnD,UAAU,EAAE,CAAC,aAAa,EAAE,6BAAW,CAAC,KAAK,CAAC,KAAK,CAAC;IAEpD,eAAe,EAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,UAAU,EAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C,WAAW,EAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,eAAe,EAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,cAAc,EAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C,eAAe,EAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,OAAO,EAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,iBAAiB,EAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,gBAAgB,EAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,eAAe,EAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7C,cAAc,EAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;IAE9C,QAAQ,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC;IACjC,QAAQ,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC;IACjC,QAAQ,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC;IACjC,IAAI,EAAM,CAAC,OAAO,EAAE,GAAG,CAAC;IACxB,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;IACxB,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;IACxB,QAAQ,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;IAExB,OAAO,EAAE,SAAS;IAClB,WAAW,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC;IACnC,WAAW,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC;IACnC,WAAW,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC;IACnC,eAAe,EAAE,CAAC,OAAO,EAAE,qDAAqD,EAAE,CAAC,CAAC;IAEpF,QAAQ,EAAK,UAAU;IACvB,WAAW,EAAE,aAAa;IAE1B,WAAW,EAAG,kBAAkB;IAChC,YAAY,EAAE,kBAAkB;IAEhC,KAAK,EAAE,WAAW;IAElB,UAAU,EAAE,CAAC,OAAO,EAAE,gCAAgC,EAAE,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAM,EAAE,QAAQ;IAEhB,iBAAiB,EAAE,mBAAmB;IACtC,WAAW,EAAE,aAAa;IAC1B,gBAAgB,EAAE,kBAAkB;IACpC,iBAAiB,EAAE,iBAAiB;IACpC,iBAAiB,EAAE,iBAAiB;IAEpC,sBAAsB,EAAM,wBAAwB;IACpD,uBAAuB,EAAK,yBAAyB;IACrD,yBAAyB,EAAG,2BAA2B;IAKvD,uBAAuB,EAAK,wBAAwB;IACpD,wBAAwB,EAAI,yBAAyB;IACrD,0BAA0B,EAAE,2BAA2B;IAEvD,WAAW,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;IACxC,aAAa,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC;IACrC,WAAW,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAE9C,QAAQ,EAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;IACzC,QAAQ,EAAK,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC;IAC1C,OAAO,EAAM,CAAC,UAAU,EAAE,IAAI,CAAC;IAC/B,OAAO,EAAM,CAAC,UAAU,EAAE,KAAK,CAAC;IAChC,QAAQ,EAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;IACzC,QAAQ,EAAK,CAAC,UAAU,EAAE,KAAK,CAAC;IAChC,OAAO,EAAM,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;IACzC,OAAO,EAAM,CAAC,UAAU,EAAE,KAAK,CAAC;IAChC,WAAW,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;IACtC,WAAW,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;IACvC,QAAQ,EAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;IACnC,QAAQ,EAAK,CAAC,UAAU,EAAE,SAAS,CAAC;IACpC,QAAQ,EAAK,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC;IAEzC,QAAQ,EAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;IACzC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;IACzC,QAAQ,EAAI,CAAC,OAAO,EAAE,8DAA8D,CAAC;IAErF,SAAS,EAAG,CAAC,WAAW,EAAE,KAAK,CAAC;IAChC,UAAU,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC;IAE/B,UAAU,EAAE,YAAY;IAExB,SAAS,EAAE,WAAW;IAEtB,UAAU,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;IACjC,YAAY,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC;IAClC,UAAU,EAAE,YAAY;IAExB,YAAY,EAAE;QACZ,mBAAmB;QACnB,4GAA4G;QAC5G,CAAC,EAAE,AAAD,EAAG,GAAG,EAAE,AAAD,EAAG,GAAG;KAChB;IAED,YAAY,EAAE,YAAY;CAE3B,EAAE,sCAAgB,CAAC,CAAC;AAKrB,IAAI,6BAAc,CAAC,wBAAwB,EAAE,yBAAY,CAAC,WAAW,EAAE;IACrE,MAAM,EAAG,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC;IAC5D,MAAM,EAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;IACvD,OAAO,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC;IAC5D,SAAS,EAAG,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;IACzC,SAAS,EAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC;IACpC,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;IACzC,QAAQ,EAAI,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;IAEpC,SAAS,EAAG,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1C,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;IACxC,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;IACxC,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5C,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAClD,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAElD,cAAc,EAAG,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACpD,YAAY,EAAK,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACvD,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;IAClD,YAAY,EAAK,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACvD,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;IAClD,YAAY,EAAK,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;IAC3D,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IACtD,YAAY,EAAK,CAAC,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAC;IACjE,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAC5D,YAAY,EAAK,CAAC,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAC;IACjE,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAE5D,eAAe,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAE5E,SAAS,EAAE,aAAa;IAExB,WAAW,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC;IAElC,SAAS,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC;IACpE,SAAS,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC;CAErE,EAAE,sCAAgB,CAAC,CAAC;AAKrB,IAAI,2BAAY,CAAC,sBAAsB,EAAE,yBAAY,CAAC,SAAS,EAAE;IAC/D,UAAU,EAAE,GAAG;IACf,UAAU,EAAE,GAAG;CAChB,CAAC,CAAC;AAKH,IAAI,yBAAU,CAAC,sBAAsB,EAAE;IACrC,GAAG,EAAG,CAAC,aAAa,EAAE,IAAI,CAAC;CAC5B,EAAE,sCAAgB,CAAC,CAAC"}