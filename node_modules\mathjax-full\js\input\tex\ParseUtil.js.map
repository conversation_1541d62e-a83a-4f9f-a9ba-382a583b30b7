{"version": 3, "file": "ParseUtil.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/ParseUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4DAAgE;AAIhE,8DAAqC;AACrC,gEAAuC;AACvC,8DAAqC;AACrC,sDAAgD;AAIhD,IAAU,SAAS,CAmqBlB;AAnqBD,WAAU,SAAS;IAGjB,IAAM,SAAS,GAAG,GAAG,CAAC;IACtB,IAAM,SAAS,GAAG,EAAE,CAAC;IAErB,IAAM,UAAU,GAA8C;QAC5D,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,EAAD,CAAC;QACZ,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,GAAG,EAAP,CAAO;QAClB,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,EAAE,EAAN,CAAM;QACjB,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,GAAG,EAAP,CAAO;QAClB,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,SAAS,GAAG,SAAS,EAAzB,CAAyB;QACpC,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,SAAS,EAAb,CAAa;QACxB,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,SAAS,GAAG,IAAI,EAApB,CAAoB;QAC/B,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,SAAS,GAAG,IAAI,EAApB,CAAoB;QAC/B,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,EAAE,EAAN,CAAM;KAClB,CAAC;IACF,IAAM,GAAG,GAAG,mCAAmC,CAAC;IAChD,IAAM,IAAI,GAAG,8BAA8B,CAAC;IAC5C,IAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC;IACjE,IAAM,SAAS,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAW/D,SAAgB,UAAU,CACxB,GAAW,EAAE,IAAqB;QAArB,qBAAA,EAAA,YAAqB;QAChC,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACnD,OAAO,KAAK,CAAC,CAAC;YACZ,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC;IANe,oBAAU,aAMzB,CAAA;IAQD,SAAS,SAAS,CAAC,EAA+C;YAA/C,KAAA,aAA+C,EAA9C,KAAK,QAAA,EAAE,IAAI,QAAA,EAAE,MAAM,QAAA;QACrC,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SAC9B;QACD,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QACxD,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACzC,CAAC;IAQD,SAAgB,QAAQ,CAAC,GAAW;QAC9B,IAAA,KAAA,OAAgB,UAAU,CAAC,GAAG,CAAC,IAAA,EAA9B,KAAK,QAAA,EAAE,IAAI,QAAmB,CAAC;QACpC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC;QACjC,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IALe,kBAAQ,WAKvB,CAAA;IAQD,SAAgB,EAAE,CAAC,CAAS;QAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;IACnD,CAAC;IALe,YAAE,KAKjB,CAAA;IAQD,SAAgB,IAAI;QAAC,WAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,sBAAc;;QACjC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,EAAE,CAAC,CAAC,CAAC,EAAL,CAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAFe,cAAI,OAEnB,CAAA;IAWD,SAAgB,MAAM,CAAC,aAA2B,EAAE,IAAY,EAAE,GAAY,EACvD,KAAa,EAAE,GAAgB,EAAE,KAAkB;QAApC,oBAAA,EAAA,QAAgB;QAAE,sBAAA,EAAA,UAAkB;QAExE,IAAI,EAAE,GAAG,aAAa,CAAC,WAAW,CAAC;QACnC,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAClB,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,qBAAQ,CAAC,KAAK,EAAC,CAAC,CAAC;QAC3E,IAAI,EAAE,CAAC;QACP,IAAI,GAAG,EAAE;YACP,EAAE,GAAG,IAAI,sBAAS,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;SAClG;aAAM;YACL,IAAI,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAChB,EAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAQ,CAAC,IAAI,EAAC,EACvE,QAAQ,CAAC,CAAC;SAC1B;QACD,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACzC,IAAI,GAAG,EAAE;YACP,EAAE,GAAG,IAAI,sBAAS,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;SACnG;aAAM;YACL,IAAI,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACzC,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAChB,EAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAQ,CAAC,KAAK,EAAC,EACxE,SAAS,CAAC,CAAC;SAC3B;QACD,KAAK,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC/C,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IA3Be,gBAAM,SA2BrB,CAAA;IAWD,SAAgB,UAAU,CAAC,aAA2B,EAAE,IAAY,EACzC,GAAY,EAAE,KAAa;QAEpD,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAChD,MAAM,EAAE,EAAE,EAAE,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,qBAAQ,CAAC,GAAG,EAAC,CAAC,CAAC;QAClE,IAAI,IAAI,EAAE;YACR,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SACxE;QACD,IAAI,qBAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;YAChC,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1D;aAAM;YACL,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SACtC;QACD,IAAI,KAAK,EAAE;YACT,qBAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAjBe,oBAAU,aAiBzB,CAAA;IAYD,SAAgB,WAAW,CAAC,aAA2B,EAAE,KAAa,EAC1C,IAAY;QACtC,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE;YAClC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;SACtB;QACD,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;QAC7C,IAAI,CAAC,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;QAC5C,OAAO,IAAI,sBAAS,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;IAChF,CAAC;IARe,qBAAW,cAQ1B,CAAA;IAWD,SAAgB,YAAY,CAAC,aAA2B,EAAE,KAAgB;QACxE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,KAAK,IAAI,CAAC,CAAC,qBAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC;gBACjC,CAAC,CAAC,qBAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC;oBAClC,CAAC,qBAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC9B,qBAAQ,CAAC,WAAW,CAAC,qBAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC9E,IAAI,qBAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;oBAC7B,CAAC,qBAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,qBAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,qBAAQ,CAAC,GAAG,CAAC,EAAE;oBACvF,IAAI,EAAE,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBACxD,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;iBACnB;gBACD,MAAM;aACP;SACF;IACH,CAAC;IAfe,sBAAY,eAe3B,CAAA;IAWD,SAAgB,YAAY,CAAC,MAAiB,EAAE,IAAY,EAC/B,KAAuB,EAAE,IAAa;QACjE,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE;YAC7C,OAAO,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAC7E;QACD,IAAI,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QAChD,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,EAAC,WAAW,aAAA,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAI,GAAG,GAAc,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,EAAE;YACjD,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;gBACtB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrB,IAAI,CAAC,KAAK,GAAG,EAAE;oBACb,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE;wBAEjC,IAAI,GAAG,MAAM,CAAC,MAAM,CAClB,MAAM,EAAE,SAAS,EACjB,CAAC,CAAC,IAAI,sBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;wBAC3E,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACf,KAAK,GAAG,EAAE,CAAC;wBACX,CAAC,GAAG,CAAC,CAAC;qBACP;yBAAM,IAAI,KAAK,KAAK,EAAE,EAAE;wBAEvB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;4BAEb,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;yBAC3D;wBACD,KAAK,GAAG,GAAG,CAAC;wBACZ,CAAC,GAAG,CAAC,CAAC;qBACP;iBACF;qBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE,EAAE;oBAEpC,MAAM,EAAE,CAAC;iBACV;qBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;oBAEpB,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE;wBAEjC,IAAI,IAAI,GAAG,CAAC,IAAI,sBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAC7E,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;wBACrD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACf,KAAK,GAAG,EAAE,CAAC;wBACX,CAAC,GAAG,CAAC,CAAC;qBACP;yBAAM,IAAI,KAAK,KAAK,EAAE,EAAE;wBAEvB,IAAI,MAAM,EAAE;4BAEV,MAAM,EAAE,CAAC;yBACV;qBACF;iBACF;qBAAM,IAAI,CAAC,KAAK,IAAI,EAAE;oBAErB,IAAI,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;wBAE1D,IAAI,GAAG,GAAK,MAAc,CAAC,IAAI,CAAY,CAAC,MAAM,CAAC;wBACnD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;4BAEb,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;yBAC3D;wBACD,KAAK,GAAG,GAAG,CAAC;wBACZ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBACV,CAAC,IAAI,GAAG,CAAC;qBACV;yBAAM;wBAEL,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;wBACrB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE,EAAE;4BAE7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gCAEb,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;6BAC3D;4BACD,KAAK,GAAG,GAAG,CAAC;4BAAC,CAAC,GAAG,CAAC,CAAC;yBACpB;6BAAM,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE;4BAErD,IAAI,GAAG,MAAM,CAAC,MAAM,CAClB,MAAM,EAAE,SAAS,EACjB,CAAC,CAAC,IAAI,sBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;4BAC3E,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACf,KAAK,GAAG,EAAE,CAAC;4BACX,CAAC,GAAG,CAAC,CAAC;yBACP;6BAAM,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,EAAG;4BAE9C,CAAC,EAAE,CAAC;4BACJ,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;yBAC/C;qBACF;iBACF;aACF;YACD,IAAI,KAAK,KAAK,EAAE,EAAE;gBAEhB,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAAE,iCAAiC,CAAC,CAAC;aAC5E;SACF;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;YAEnB,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;SACpD;QACD,IAAI,KAAK,IAAI,IAAI,EAAE;YAEjB,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAC,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;SACzF;aAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YAEzB,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;SAC5C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAvGe,sBAAY,eAuG3B,CAAA;IAUD,SAAgB,YAAY,CAAC,MAAiB,EAAE,IAAY,EAAE,GAAY;QAExE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,sBAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,sBAAQ,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IALe,sBAAY,eAK3B,CAAA;IAWD,SAAgB,SAAS,CAAC,MAAiB,EAAE,IAAa,EAAE,MAAe,EAAE,GAAW,EAAE,KAAc;QAEtG,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,qBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAEvE,qBAAQ,CAAC,aAAa,CAAC,qBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC,CAAC;YACzE,IAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,MAAM,EAAE,CAAC,EAAC,CAAC,CAAC;YACxD,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;SAElD;QACD,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,IAAI,CAAC,CAAkB,CAAC;QACzE,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,KAAK,MAAM,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACvE,IAAI,IAAI,GAAY,GAAG,CAAC;QACxB,IAAI,KAAK,EAAE;YAET,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,EAAC,CAAC,CAAC;SAC3F;QACD,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAnBe,mBAAS,YAmBxB,CAAA;IAMD,SAAgB,kBAAkB,CAAC,IAAa;QAC9C,IAAM,MAAM,GAAG,CAAC,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,qBAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7E,IAAI,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;YAEnG,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,EAAC,aAAa,EAAE,KAAK,EAAC,CAAC,CAAC;SACtD;IACH,CAAC;IANe,4BAAkB,qBAMjC,CAAA;IAOD,SAAgB,UAAU,CAAC,IAAY;QACrC,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,IAAI,GAAG,CAAC;SACb;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IATe,oBAAU,aASzB,CAAA;IASD,SAAgB,aAAa,CAAC,KAAgB,EAAE,KAAa;QAE3D,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QAC1C,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,YAAY,CAAC;SACrC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE;YACxB,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC;SACtC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE;YACxB,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;SAC/B;aAAM,IAAI,KAAK,EAAE;YAChB,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;SAC9B;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAbe,uBAAa,gBAa5B,CAAA;IAUD,SAAgB,cAAc,CAAC,MAAiB,EAAE,IAAc,EACjC,GAAW;QACxC,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;aAC7B;iBACI,IAAI,CAAC,KAAK,GAAG,EAAE;gBAClB,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpB,IAAI,CAAC,KAAK,GAAG,EAAE;oBACb,IAAI,IAAI,CAAC,CAAC;iBACX;qBAAM;oBACL,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;wBACtD,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAClB,mCAAmC,CAAC,CAAC;qBAC1D;oBACD,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,EACxC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC/C,IAAI,GAAG,EAAE,CAAC;iBACX;aACF;iBAAM;gBACL,IAAI,IAAI,CAAC,CAAC;aACX;SACF;QACD,OAAO,OAAO,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IA5Be,wBAAc,iBA4B7B,CAAA;IAYD,SAAgB,OAAO,CAAC,MAAiB,EAAE,EAAU,EAAE,EAAU;QAC/D,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,EAAE;YACjE,EAAE,IAAI,GAAG,CAAC;SACX;QACD,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACrE,MAAM,IAAI,qBAAQ,CAAC,eAAe,EACd,mDAAmD;gBACnD,wBAAwB,CAAC,CAAC;SAC/C;QACD,OAAO,EAAE,GAAG,EAAE,CAAC;IACjB,CAAC;IAVe,iBAAO,UAUtB,CAAA;IAOD,SAAgB,cAAc,CAAC,MAAiB,EAAE,OAAuB;QAAvB,wBAAA,EAAA,cAAuB;QACvE,IAAI,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACpE,OAAO;SACR;QACD,IAAI,OAAO,EAAE;YACX,MAAM,IAAI,qBAAQ,CAAC,cAAc,EACd,qDAAqD;gBACrD,iCAAiC,CAAC,CAAC;SACvD;aAAM;YACL,MAAM,IAAI,qBAAQ,CAAC,cAAc,EACd,+CAA+C;gBAC/C,yCAAyC,CAAC,CAAC;SAC/D;IACH,CAAC;IAbe,wBAAc,iBAa7B,CAAA;IAMD,SAAgB,WAAW,CAAC,MAAiB;QAC3C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE;YAE9B,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EAAE,0CAA0C,CAAC,CAAC;SACtF;QACD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;IACpC,CAAC;IANe,qBAAW,cAM1B,CAAA;IASD,SAAgB,QAAQ,CAAC,IAAa,EAAE,MAAiB;QACvD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAa,CAAC;QACpC,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,UAAC,CAAU;;YACvB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3B,IAAM,KAAK,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;gBACrE,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;oBAArB,IAAM,IAAI,kBAAA;oBACb,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;iBAClC;;;;;;;;;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAXe,kBAAQ,WAWvB,CAAA;IASD,SAAgB,kBAAkB,CAAC,OAAkB,EAAE,KAAa,EAAE,KAAa;QAEjF,OAAO,KAAK,CAAC;IACf,CAAC;IAHe,4BAAkB,qBAGjC,CAAA;IAQD,SAAgB,UAAU,CAAC,MAAiB;QAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,WAAW,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC;IAHe,oBAAU,aAGzB,CAAA;IAaD,SAAgB,aAAa,CAAC,MAAc,EACd,OAAuC,EACvC,KAAsB;;QADtB,wBAAA,EAAA,cAAuC;QACvC,sBAAA,EAAA,aAAsB;QAClD,IAAI,GAAG,GAAY,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,OAAO,EAAE;;gBACX,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;oBAA7B,IAAI,GAAG,WAAA;oBACV,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;wBAChC,IAAI,KAAK,EAAE;4BACT,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,oBAAoB,EAAE,GAAG,CAAC,CAAC;yBAChE;wBACD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;qBACjB;iBACF;;;;;;;;;SACF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAfe,uBAAa,gBAe5B,CAAA;IASD,SAAS,UAAU,CAAC,IAAY;;QAC9B,IAAI,OAAO,GAAY,EAAE,CAAC;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAClB,OAAO,IAAI,EAAE;YACX,KAAA,OAAmB,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAA,EAA7C,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,IAAI,QAAA,CAAgC;YAC/C,IAAI,GAAG,KAAK,GAAG,EAAE;gBACf,KAAA,OAAmB,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAA,EAAxC,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,IAAI,QAAA,CAA2B;gBAC1C,GAAG,GAAG,CAAC,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC;oBACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;aACpB;iBAAM,IAAI,GAAG,EAAE;gBACd,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;aACrB;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IASD,SAAS,YAAY,CAAC,IAAY,EAAE,KAAa;QAC/C,OAAO,KAAK,GAAG,CAAC,EAAE;YAChB,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,EAAE,CAAC;SACT;QACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;IAWD,SAAS,SAAS,CAAC,IAAY,EAAE,GAAa;QAC5C,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,OAAO,KAAK,GAAG,MAAM,EAAE;YACrB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACtB,QAAQ,CAAC,EAAE;gBACX,KAAK,GAAG;oBACN,MAAM;gBACR,KAAK,GAAG;oBACN,IAAI,UAAU,EAAE;wBACd,KAAK,EAAE,CAAC;qBACT;yBAAM;wBACL,SAAS,GAAG,KAAK,CAAC;wBAClB,IAAI,KAAK,GAAG,MAAM,EAAE;4BAClB,KAAK,GAAG,MAAM,CAAC;yBAChB;qBACF;oBACD,MAAM,EAAE,CAAC;oBACT,MAAM;gBACR,KAAK,GAAG;oBACN,IAAI,MAAM,EAAE;wBACV,MAAM,EAAE,CAAC;qBACV;oBACD,IAAI,UAAU,IAAI,SAAS,EAAE;wBAC3B,KAAK,EAAE,CAAC;wBACR,SAAS,GAAG,IAAI,CAAC;qBAClB;oBACD,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACR;oBACE,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;wBACpC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gCAEpB,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;qBAC3D;oBACD,UAAU,GAAG,KAAK,CAAC;oBACnB,SAAS,GAAG,KAAK,CAAC;aACnB;YACD,KAAK,IAAI,CAAC,CAAC;SACZ;QACD,IAAI,MAAM,EAAE;YACV,MAAM,IAAI,qBAAQ,CAAC,uBAAuB,EACvB,yCAAyC,CAAC,CAAC;SAC/D;QACD,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAClF,CAAC;AAEH,CAAC,EAnqBS,SAAS,KAAT,SAAS,QAmqBlB;AAED,kBAAe,SAAS,CAAC"}