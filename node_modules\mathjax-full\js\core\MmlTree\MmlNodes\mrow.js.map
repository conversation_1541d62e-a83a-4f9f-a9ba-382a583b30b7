{"version": 3, "file": "mrow.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mrow.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAiE;AAOjE;IAA6B,2BAAe;IAA5C;QAAA,qEA0JC;QA9IW,WAAK,GAAW,IAAI,CAAC;;IA8IjC,CAAC;IAzIC,sBAAW,yBAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAOD,sBAAW,gCAAW;aAAtB;;;gBACE,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;wBACtB,OAAO,KAAK,CAAC;qBACd;iBACF;;;;;;;;;YACD,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAQD,sBAAW,kCAAa;aAAxB;;YACE,IAAI,WAAW,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,CAAC;;gBACV,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,KAAK,EAAE;wBACT,IAAI,KAAK,CAAC,aAAa,EAAE;4BACvB,IAAI,WAAW,EAAE;gCACf,OAAO,KAAK,CAAC;6BACd;4BACD,WAAW,GAAG,IAAI,CAAC;4BACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;yBAChB;6BAAM,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;4BAC7B,OAAO,KAAK,CAAC;yBACd;qBACF;oBACD,CAAC,EAAE,CAAC;iBACL;;;;;;;;;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;;;OAAA;IAKM,sBAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YAC7C,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAKM,wBAAM,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YAC7C,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;IAC9C,CAAC;IAKM,gCAAc,GAArB;;QACE,IAAI,CAAC,GAAG,CAAC,CAAC;;YACV,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;oBAC/B,CAAC,EAAE,CAAC;iBACL;aACF;;;;;;;;;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAKM,+BAAa,GAApB;;;YACE,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;oBAC/B,OAAO,KAAK,CAAC;iBACd;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,8BAAY,GAAnB;QACE,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC/B,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;YACf,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBAC/B,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,6BAAW,GAAlB,UAAmB,IAAa;;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;YAQzE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACxB,IAAI,GAAG,IAAI,CAAC;;gBACZ,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAChC;;;;;;;;;YACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,QAAQ,GAAG,qBAAQ,CAAC,KAAK,CAAC;aAChC;SACF;aAAM;;gBAIL,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAChC;;;;;;;;;YACD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAnJa,gBAAQ,gBACjB,4BAAe,CAAC,QAAQ,EAC3B;IAmJJ,cAAC;CAAA,AA1JD,CAA6B,4BAAe,GA0J3C;AA1JY,0BAAO;AAkKpB;IAAqC,mCAAO;IAA5C;;IAmCA,CAAC;IAzBC,sBAAW,iCAAI;aAAf;YACE,OAAO,cAAc,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,uCAAU;aAArB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKD,sBAAW,sCAAS;aAApB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,kCAAQ,GAAf;QACE,OAAO,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/C,CAAC;IA5Ba,wBAAQ,GAAiB,OAAO,CAAC,QAAQ,CAAC;IA8B1D,sBAAC;CAAA,AAnCD,CAAqC,OAAO,GAmC3C;AAnCY,0CAAe"}