{"version": 3, "file": "NewcommandItems.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/newcommand/NewcommandItems.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAyBA,+DAAsC;AACtC,gDAA+D;AAQ/D;IAAkC,gCAAQ;IAA1C;;IAuCA,CAAC;IAlCC,sBAAW,8BAAI;aAAf;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAMD,sBAAI,gCAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAMM,gCAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAEtB,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,EAAE;gBAErC,MAAM,IAAI,qBAAQ,CAAC,WAAW,EAAE,kCAAkC,EAC9C,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;aACrD;YACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAEvB,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC1E;QAED,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,mBAAC;AAAD,CAAC,AAvCD,CAAkC,uBAAQ,GAuCzC;AAvCY,oCAAY"}