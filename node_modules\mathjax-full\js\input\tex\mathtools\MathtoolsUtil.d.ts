import { EqnArrayItem } from '../base/BaseItems.js';
import TexParser from '../TexParser.js';
import ParseOptions from '../ParseOptions.js';
import { MmlNode } from '../../../core/MmlTree/MmlNode.js';
export declare const MathtoolsUtil: {
    setDisplayLevel(mml: MmlNode, style: string): void;
    checkAlignment(parser: TexParser, name: string): EqnArrayItem;
    addPairedDelims(config: ParseOptions, cs: string, args: string[]): void;
    spreadLines(mtable: MmlNode, spread: string): void;
    plusOrMinus(name: string, n: string): string;
    getScript(parser: TexParser, name: string, pos: string): MmlNode;
};
