{"version": 3, "file": "UpgreekConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/upgreek/UpgreekConfiguration.ts"], "names": [], "mappings": ";;;AAwBA,wDAAkD;AAElD,gDAA6C;AAC7C,sDAA+C;AAS/C,SAAS,iBAAiB,CAAC,MAAiB,EAAE,KAAa;IACzD,IAAM,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;IACnC,GAAG,CAAC,WAAW,GAAG,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC;IAC7C,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC;AAKD,IAAI,2BAAY,CAAC,SAAS,EAAE,iBAAiB,EAAE;IAC7C,OAAO,EAAS,QAAQ;IACxB,MAAM,EAAU,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,SAAS,EAAO,QAAQ;IACxB,MAAM,EAAU,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,MAAM,EAAU,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,QAAQ,EAAQ,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,SAAS,EAAO,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,SAAS,EAAO,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,YAAY,EAAI,QAAQ;IACxB,UAAU,EAAM,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,QAAQ,EAAQ,QAAQ;IACxB,UAAU,EAAM,QAAQ;IACxB,QAAQ,EAAQ,QAAQ;IAExB,OAAO,EAAS,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,QAAQ,EAAQ,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,IAAI,EAAY,QAAQ;IACxB,OAAO,EAAS,QAAQ;IACxB,SAAS,EAAO,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,KAAK,EAAW,QAAQ;IACxB,OAAO,EAAS,QAAQ;CACzB,CAAC,CAAC;AAGU,QAAA,oBAAoB,GAAG,gCAAa,CAAC,MAAM,CACtD,SAAS,EAAE;IACT,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAC;CAC9B,CACF,CAAC"}