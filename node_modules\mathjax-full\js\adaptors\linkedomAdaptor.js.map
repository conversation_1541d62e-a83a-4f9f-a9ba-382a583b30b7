{"version": 3, "file": "linkedomAdaptor.js", "sourceRoot": "", "sources": ["../../ts/adaptors/linkedomAdaptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,mDAA6C;AAC7C,+CAAsD;AAWtD;IAAqC,mCAA2E;IAAhH;;IAuBA,CAAC;IAlBQ,+BAAK,GAAZ,UAAa,IAAY,EAAE,MAA4B;QAA5B,uBAAA,EAAA,oBAA4B;QAIrD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAAE,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC;QACpE,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAQM,sCAAY,GAAnB,UAAoB,IAAiB;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEH,sBAAC;AAAD,CAAC,AAvBD,CAAqC,IAAA,wBAAS,EAAsD,4BAAW,CAAC,GAuB/G;AAvBY,0CAAe;AA+B5B,SAAgB,eAAe,CAAC,SAAc,EAAE,OAA0B;IAA1B,wBAAA,EAAA,cAA0B;IACxE,IAAM,MAAM,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC;IAC1C,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc;QAAG;QAAO,CAAC;QAAD,qBAAC;IAAD,CAAC,AAAR,GAAQ,CAAC;IACvD,OAAO,IAAI,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AAJD,0CAIC"}