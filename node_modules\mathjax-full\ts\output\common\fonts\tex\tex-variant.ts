/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import {CharMap, CharOptions} from '../../FontData.js';

export const texVariant: CharMap<CharOptions> = {
    0x2C6: [.845, -0.561, 2.333, {ic: .013}],
    0x2DC: [.899, -0.628, 2.333],
    0x302: [.845, -0.561, 0, {ic: .013}],
    0x303: [.899, -0.628, 0],
    0x3F0: [.434, .006, .667, {ic: .067}],
    0x210F: [.695, .013, .54, {ic: .022}],
    0x2190: [.437, -0.064, .5],
    0x2192: [.437, -0.064, .5],
    0x21CC: [.514, .014, 1],
    0x2204: [.86, .166, .556],
    0x2205: [.587, 0, .778],
    0x2212: [.27, -0.23, .5],
    0x2216: [.43, .023, .778],
    0x221D: [.472, -0.028, .778],
    0x2223: [.43, .023, .222],
    0x2224: [.43, .023, .222, {ic: .018}],
    0x2225: [.431, .023, .389],
    0x2226: [.431, .024, .389, {ic: .018}],
    0x223C: [.365, -0.132, .778],
    0x2248: [.481, -0.05, .778],
    0x2268: [.752, .284, .778],
    0x2269: [.752, .284, .778],
    0x2270: [.919, .421, .778],
    0x2271: [.919, .421, .778],
    0x2288: [.828, .33, .778],
    0x2289: [.828, .33, .778],
    0x228A: [.634, .255, .778],
    0x228B: [.634, .254, .778],
    0x22A8: [.694, 0, .611],
    0x22C5: [.189, 0, .278],
    0x2322: [.378, -0.122, .778],
    0x2323: [.378, -0.143, .778],
    0x25B3: [.575, .02, .722],
    0x25BD: [.576, .019, .722],
    0x2A87: [.801, .303, .778],
    0x2A88: [.801, .303, .778],
    0x2ACB: [.752, .332, .778],
    0x2ACC: [.752, .333, .778],
};
