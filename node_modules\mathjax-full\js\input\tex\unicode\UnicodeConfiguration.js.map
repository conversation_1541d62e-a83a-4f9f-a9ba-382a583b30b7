{"version": 3, "file": "UnicodeConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/unicode/UnicodeConfiguration.ts"], "names": [], "mappings": ";;;;;;AAwBA,wDAAkD;AAGlD,+DAAsC;AACtC,gDAA2C;AAE3C,iEAAwC;AACxC,+DAAsC;AACtC,yDAAkD;AAGvC,QAAA,cAAc,GAAgC,EAAE,CAAC;AAE5D,IAAI,YAAY,GAAsD,EAAE,CAAC;AAOzE,sBAAc,CAAC,OAAO,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC/D,IAAI,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,EAAE,EAAE;QACN,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YACpB,KAAK,CAAC,2CAA2C,CAAC,EAAE;YACtD,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,GAAG,EAAE,CAAC;SACX;KACF;IACD,IAAI,CAAC,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC3E,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE;QACxC,MAAM,IAAI,qBAAQ,CAAC,YAAY,EAAE,wCAAwC,CAAC,CAAC;KAC5E;IACD,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;KACvC;SAAM,IAAI,CAAC,IAAI,EAAE;QAChB,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3B;IACD,IAAI,OAAO,EAAE;QACX,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/D,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;KAChE;IACD,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAc,CAAC;IAC9C,IAAI,GAAG,GAAY,EAAE,CAAC;IACtB,IAAI,IAAI,EAAE;QACR,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,OAAO,EAAE;YACX,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACzB,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC;aACzB;YACD,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;gBACnC,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC;aAC1B;SACF;KACF;SAAM,IAAI,OAAO,EAAE;QAClB,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC;KAC3B;IACD,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,IAAA,qBAAO,EAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAGF,IAAI,yBAAU,CAAC,SAAS,EAAE,EAAC,OAAO,EAAE,SAAS,EAAC,EAAE,sBAAc,CAAC,CAAC;AAGnD,QAAA,oBAAoB,GAAG,gCAAa,CAAC,MAAM,CACtD,SAAS,EAAE,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAC,EAAC,CAC3C,CAAC"}