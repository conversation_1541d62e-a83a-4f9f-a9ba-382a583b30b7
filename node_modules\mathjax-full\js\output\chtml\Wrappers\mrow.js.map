{"version": 3, "file": "mrow.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mrow.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAA0E;AAC1E,yDAA8D;AAC9D,yDAAsE;AACtE,kEAAgF;AAWhF;IACA,6BAA8D;IAD9D;;IAgCA,CAAC;IArBQ,2BAAO,GAAd,UAAe,MAAS;;QACtB,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,IAAI,WAAW,GAAG,KAAK,CAAC;;YACxB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACrB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;oBACpB,WAAW,GAAG,IAAI,CAAC;iBACpB;aACF;;;;;;;;;QAED,IAAI,WAAW,EAAE;YACR,IAAA,CAAC,GAAI,IAAI,CAAC,OAAO,EAAE,EAAlB,CAAmB;YAC3B,IAAI,CAAC,EAAE;gBACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,GAAG,CAAC,EAAE;oBACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;iBACzD;aACF;SACF;IACH,CAAC;IAxBa,cAAI,GAAG,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IA0B9C,gBAAC;CAAA,AAhCD,CACA,IAAA,yBAAe,EAAkC,yBAAY,CAAC,GA+B7D;AAhCY,8BAAS;AA2CtB;IACA,qCAAyE;IADzE;;IAQA,CAAC;IAFe,sBAAI,GAAG,yBAAe,CAAC,SAAS,CAAC,IAAI,CAAC;IAEtD,wBAAC;CAAA,AARD,CACA,IAAA,iCAAuB,EAAwC,SAAS,CAAC,GAOxE;AARY,8CAAiB"}