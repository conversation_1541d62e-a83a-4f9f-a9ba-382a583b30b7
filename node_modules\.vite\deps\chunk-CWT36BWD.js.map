{"version": 3, "sources": ["../../mathjax-full/ts/util/PrioritizedList.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a list sorted by a numeric priority\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n/*****************************************************************/\n/**\n *  The PrioritizedListItem<DataClass> interface\n *\n * @template DataClass   The class of data stored in the item\n */\n\nexport interface PrioritizedListItem<DataClass> {\n\n  /**\n   * The priority of this item\n   */\n  priority: number;\n\n  /**\n   * The data for the list item\n   */\n  item: DataClass;\n}\n\n/*****************************************************************/\n/**\n *  Implements the PrioritizedList<DataClass> class\n *\n * @template DataClass   The class of data stored in the list\n */\n\nexport class PrioritizedList<DataClass> {\n\n  /**\n   * The default priority for items added to the list\n   */\n  public static DEFAULTPRIORITY: number = 5;\n\n  /**\n   * The list of items, sorted by priority (smallest number first)\n   */\n  protected items: PrioritizedListItem<DataClass>[] = [];\n\n  /**\n   * @constructor\n   */\n  constructor() {\n    this.items = [];\n  }\n\n  /**\n   * Make the list iterable, and return the data for the items in the list\n   *\n   * @return {{next: Function}}  The object containing the iterator's next() function\n   */\n  public [Symbol.iterator](): Iterator<PrioritizedListItem<DataClass>> {\n    let i = 0;\n    let items = this.items;\n    return {\n      /* tslint:disable-next-line:jsdoc-require */\n      next(): IteratorResult<PrioritizedListItem<DataClass>> {\n        return {value: items[i++], done: (i > items.length)};\n      }\n    };\n  }\n\n  /**\n   * Add an item to the list\n   *\n   * @param {DataClass} item   The data for the item to be added\n   * @param {number} priority  The priority for the item\n   * @return {DataClass}       The data itself\n   */\n  public add(item: DataClass, priority: number = PrioritizedList.DEFAULTPRIORITY): DataClass {\n    let i = this.items.length;\n    do {\n      i--;\n    } while (i >= 0 && priority < this.items[i].priority);\n    this.items.splice(i + 1, 0, {item: item, priority: priority});\n    return item;\n  }\n\n  /**\n   * Remove an item from the list\n   *\n   * @param {DataClass} item   The data for the item to be removed\n   */\n  public remove(item: DataClass) {\n    let i = this.items.length;\n    do {\n      i--;\n    } while (i >= 0 && this.items[i].item !== item);\n    if (i >= 0) {\n      this.items.splice(i, 1);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAkDA,QAAA,kBAAA,WAAA;AAeE,eAAAA,mBAAA;AALU,aAAA,QAA0C,CAAA;AAMlD,aAAK,QAAQ,CAAA;MACf;AAOO,MAAAA,iBAAA,UAAC,OAAO,QAAQ,IAAvB,WAAA;AACE,YAAI,IAAI;AACR,YAAI,QAAQ,KAAK;AACjB,eAAO;UAEL,MAAA,WAAA;AACE,mBAAO,EAAC,OAAO,MAAM,GAAG,GAAG,MAAO,IAAI,MAAM,OAAO;UACrD;;MAEJ;AASO,MAAAA,iBAAA,UAAA,MAAP,SAAW,MAAiB,UAAkD;AAAlD,YAAA,aAAA,QAAA;AAAA,qBAAmBA,iBAAgB;QAAe;AAC5E,YAAI,IAAI,KAAK,MAAM;AACnB,WAAG;AACD;iBACO,KAAK,KAAK,WAAW,KAAK,MAAM,CAAC,EAAE;AAC5C,aAAK,MAAM,OAAO,IAAI,GAAG,GAAG,EAAC,MAAY,SAAkB,CAAC;AAC5D,eAAO;MACT;AAOO,MAAAA,iBAAA,UAAA,SAAP,SAAc,MAAe;AAC3B,YAAI,IAAI,KAAK,MAAM;AACnB,WAAG;AACD;iBACO,KAAK,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS;AAC1C,YAAI,KAAK,GAAG;AACV,eAAK,MAAM,OAAO,GAAG,CAAC;;MAE1B;AA3Dc,MAAAA,iBAAA,kBAA0B;AA4D1C,aAAAA;MAjEA;AAAa,YAAA,kBAAA;;;", "names": ["PrioritizedList"]}