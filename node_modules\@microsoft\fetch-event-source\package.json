{"name": "@microsoft/fetch-event-source", "version": "2.0.1", "description": "A better API for making Event Source requests, with all the features of fetch()", "homepage": "https://github.com/Azure/fetch-event-source#readme", "repository": "github:Azure/fetch-event-source", "bugs": {"url": "https://github.com/Azure/fetch-event-source/issues"}, "author": "Microsoft", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/cjs/index.d.ts", "sideEffects": false, "scripts": {"clean": "rimraf ./lib ./coverage", "prebuild": "npm run clean", "build": "tsc && tsc -p tsconfig.esm.json", "test": "nyc jasmine --config=jasmine.json", "prepublishOnly": "npm run build && npm run test"}, "devDependencies": {"@types/jasmine": "^3.6.9", "jasmine": "^3.7.0", "nyc": "^15.1.0", "rimraf": "^3.0.2", "source-map-support": "^0.5.19", "typescript": "^4.2.4"}}