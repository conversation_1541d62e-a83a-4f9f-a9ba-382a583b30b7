export declare const BIGDIMEN = 1000000;
export declare const UNITS: {
    [unit: string]: number;
};
export declare const RELUNITS: {
    [unit: string]: number;
};
export declare const MATHSPACE: {
    [name: string]: number;
};
export declare function length2em(length: string | number, size?: number, scale?: number, em?: number): number;
export declare function percent(m: number): string;
export declare function em(m: number): string;
export declare function emRounded(m: number, em?: number): string;
export declare function px(m: number, M?: number, em?: number): string;
