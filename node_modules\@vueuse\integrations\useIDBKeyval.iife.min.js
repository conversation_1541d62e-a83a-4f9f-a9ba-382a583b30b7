(function(i,f,s,a){"use strict";function w(u,l,h={}){const{flush:d="pre",deep:p=!0,shallow:V=!1,onError:c=e=>{console.error(e)},writeDefaults:D=!0}=h,o=a.shallowRef(!1),t=(V?a.shallowRef:a.ref)(l),n=a.toValue(l);async function v(){try{const e=await s.get(u);e===void 0?n!=null&&D&&await s.set(u,n):t.value=e}catch(e){c(e)}o.value=!0}v();async function r(){try{t.value==null?await s.del(u):await s.update(u,()=>a.toRaw(t.value))}catch(e){c(e)}}const{pause:I,resume:R}=f.watchPausable(t,()=>r(),{flush:d,deep:p});async function U(e){I(),t.value=e,await r(),R()}return{set:U,isFinished:o,data:t}}i.useIDBKeyval=w})(this.VueUse=this.VueUse||{},VueUse,idbKeyval,Vue);
