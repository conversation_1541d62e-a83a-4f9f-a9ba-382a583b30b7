import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/noundefined/NoUndefinedConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/noundefined', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      noundefined: {
        NoUndefinedConfiguration: module1
      }
    }
  }
}});
