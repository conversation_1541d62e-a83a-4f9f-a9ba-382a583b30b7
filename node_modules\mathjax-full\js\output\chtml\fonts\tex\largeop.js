"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.largeop = void 0;
var FontData_js_1 = require("../../FontData.js");
var largeop_js_1 = require("../../../common/fonts/tex/largeop.js");
exports.largeop = (0, FontData_js_1.AddCSS)(largeop_js_1.largeop, {
    0x2016: { f: 'S1' },
    0x2044: { c: '/' },
    0x2191: { f: 'S1' },
    0x2193: { f: 'S1' },
    0x21D1: { f: 'S1' },
    0x21D3: { f: 'S1' },
    0x2223: { f: 'S1' },
    0x2225: { f: 'S1' },
    0x2329: { c: '\\27E8' },
    0x232A: { c: '\\27E9' },
    0x23D0: { f: 'S1' },
    0x2758: { c: '\\2223', f: 'S1' },
    0x2A0C: { c: '\\222C\\222C' },
    0x3008: { c: '\\27E8' },
    0x3009: { c: '\\27E9' },
});
//# sourceMappingURL=largeop.js.map