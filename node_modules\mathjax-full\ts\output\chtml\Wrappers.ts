/*************************************************************
 *
 *  Copyright (c) 2017-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  An object listing all the CHTMLWrapper classes
 *
 * <AUTHOR> (<PERSON><PERSON>)
 */

import {WrapperConstructor} from '../common/Wrapper.js';
import {CHTMLWrapper} from './Wrapper.js';
import {CHTMLmath} from './Wrappers/math.js';
import {CHTMLmi} from './Wrappers/mi.js';
import {CHTMLmo} from './Wrappers/mo.js';
import {CHTMLmn} from './Wrappers/mn.js';
import {CHTMLms} from './Wrappers/ms.js';
import {CHTMLmtext} from './Wrappers/mtext.js';
import {CHTMLmspace} from './Wrappers/mspace.js';
import {CHTMLmpadded} from './Wrappers/mpadded.js';
import {CHTMLmenclose} from './Wrappers/menclose.js';
import {CHTMLmrow, CHTMLinferredMrow} from './Wrappers/mrow.js';
import {CHTMLmfenced} from './Wrappers/mfenced.js';
import {CHTMLmfrac} from './Wrappers/mfrac.js';
import {CHTMLmsqrt} from './Wrappers/msqrt.js';
import {CHTMLmroot} from './Wrappers/mroot.js';
import {CHTMLmsub, CHTMLmsup, CHTMLmsubsup} from './Wrappers/msubsup.js';
import {CHTMLmover, CHTMLmunder, CHTMLmunderover} from './Wrappers/munderover.js';
import {CHTMLmmultiscripts} from './Wrappers/mmultiscripts.js';
import {CHTMLmtable} from './Wrappers/mtable.js';
import {CHTMLmtr, CHTMLmlabeledtr} from './Wrappers/mtr.js';
import {CHTMLmtd} from './Wrappers/mtd.js';
import {CHTMLmaction} from './Wrappers/maction.js';
import {CHTMLmglyph} from './Wrappers/mglyph.js';
import {CHTMLsemantics, CHTMLannotation, CHTMLannotationXML, CHTMLxml} from './Wrappers/semantics.js';
import {CHTMLTeXAtom} from './Wrappers/TeXAtom.js';
import {CHTMLTextNode} from './Wrappers/TextNode.js';

export const CHTMLWrappers: {[kind: string]: WrapperConstructor}  = {
  [CHTMLmath.kind]: CHTMLmath,
  [CHTMLmrow.kind]: CHTMLmrow,
  [CHTMLinferredMrow.kind]: CHTMLinferredMrow,
  [CHTMLmi.kind]: CHTMLmi,
  [CHTMLmo.kind]: CHTMLmo,
  [CHTMLmn.kind]: CHTMLmn,
  [CHTMLms.kind]: CHTMLms,
  [CHTMLmtext.kind]: CHTMLmtext,
  [CHTMLmspace.kind]: CHTMLmspace,
  [CHTMLmpadded.kind]: CHTMLmpadded,
  [CHTMLmenclose.kind]: CHTMLmenclose,
  [CHTMLmfrac.kind]: CHTMLmfrac,
  [CHTMLmsqrt.kind]: CHTMLmsqrt,
  [CHTMLmroot.kind]: CHTMLmroot,
  [CHTMLmsub.kind]: CHTMLmsub,
  [CHTMLmsup.kind]: CHTMLmsup,
  [CHTMLmsubsup.kind]: CHTMLmsubsup,
  [CHTMLmunder.kind]: CHTMLmunder,
  [CHTMLmover.kind]: CHTMLmover,
  [CHTMLmunderover.kind]: CHTMLmunderover,
  [CHTMLmmultiscripts.kind]: CHTMLmmultiscripts,
  [CHTMLmfenced.kind]: CHTMLmfenced,
  [CHTMLmtable.kind]: CHTMLmtable,
  [CHTMLmtr.kind]: CHTMLmtr,
  [CHTMLmlabeledtr.kind]: CHTMLmlabeledtr,
  [CHTMLmtd.kind]: CHTMLmtd,
  [CHTMLmaction.kind]: CHTMLmaction,
  [CHTMLmglyph.kind]: CHTMLmglyph,
  [CHTMLsemantics.kind]: CHTMLsemantics,
  [CHTMLannotation.kind]: CHTMLannotation,
  [CHTMLannotationXML.kind]: CHTMLannotationXML,
  [CHTMLxml.kind]: CHTMLxml,
  [CHTMLTeXAtom.kind]: CHTMLTeXAtom,
  [CHTMLTextNode.kind]: CHTMLTextNode,
  [CHTMLWrapper.kind]: CHTMLWrapper
};
