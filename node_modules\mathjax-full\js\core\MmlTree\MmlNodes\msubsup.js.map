{"version": 3, "file": "msubsup.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/msubsup.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAiE;AAOjE;IAAgC,8BAAmB;IAAnD;;IA8DA,CAAC;IAhDC,sBAAW,4BAAI;aAAf;YACE,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAMD,sBAAW,6BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,4BAAI;aAAf;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,2BAAG;aAAd;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,2BAAG;aAAd;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAOS,gDAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACnE,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvF,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACb,OAAO;SACR;QACD,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IACzF,CAAC;IAvDa,mBAAQ,yBACjB,gCAAmB,CAAC,QAAQ,KAC/B,cAAc,EAAE,EAAE,EAClB,gBAAgB,EAAE,EAAE,IACpB;IAqDJ,iBAAC;CAAA,AA9DD,CAAgC,gCAAmB,GA8DlD;AA9DY,gCAAU;AAqEvB;IAA6B,2BAAU;IAAvC;;IAwBA,CAAC;IAZC,sBAAW,yBAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAW,0BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAjBa,gBAAQ,gBACjB,UAAU,CAAC,QAAQ,EACtB;IAiBJ,cAAC;CAAA,AAxBD,CAA6B,UAAU,GAwBtC;AAxBY,0BAAO;AA+BpB;IAA6B,2BAAU;IAAvC;;IAwCA,CAAC;IA5BC,sBAAW,yBAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,0BAAK;aAAT;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAI,wBAAG;aAAP;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAI,wBAAG;aAAP;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAjCa,gBAAQ,gBACjB,UAAU,CAAC,QAAQ,EACtB;IAiCJ,cAAC;CAAA,AAxCD,CAA6B,UAAU,GAwCtC;AAxCY,0BAAO"}