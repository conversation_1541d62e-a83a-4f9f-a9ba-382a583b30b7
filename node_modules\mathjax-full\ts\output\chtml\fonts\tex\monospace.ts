/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
import {CHTMLCharMap, AddCSS} from '../../FontData.js';
import {monospace as font} from '../../../common/fonts/tex/monospace.js';

export const monospace: CHTMLCharMap = AddCSS(font, {
    0x2B9: {c: '\\2032'},
    0x391: {c: 'A'},
    0x392: {c: 'B'},
    0x395: {c: 'E'},
    0x396: {c: 'Z'},
    0x397: {c: 'H'},
    0x399: {c: 'I'},
    0x39A: {c: 'K'},
    0x39C: {c: 'M'},
    0x39D: {c: 'N'},
    0x39F: {c: 'O'},
    0x3A1: {c: 'P'},
    0x3A4: {c: 'T'},
    0x3A7: {c: 'X'},
    0x2017: {c: '_'},
    0x2033: {c: '\\2032\\2032'},
    0x2034: {c: '\\2032\\2032\\2032'},
    0x2044: {c: '/'},
    0x2057: {c: '\\2032\\2032\\2032\\2032'},
    0x2206: {c: '\\394'},
});
