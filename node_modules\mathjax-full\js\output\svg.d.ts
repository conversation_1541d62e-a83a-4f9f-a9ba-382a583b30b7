import { CommonOutputJax, UnknownBBox } from './common/OutputJax.js';
import { OptionList } from '../util/Options.js';
import { MathDocument } from '../core/MathDocument.js';
import { MathItem } from '../core/MathItem.js';
import { MmlNode } from '../core/MmlTree/MmlNode.js';
import { SVGWrapper } from './svg/Wrapper.js';
import { SVGWrapperFactory } from './svg/WrapperFactory.js';
import { SVGFontData } from './svg/FontData.js';
import { StyleList as CssStyleList } from '../util/StyleList.js';
import { FontCache } from './svg/FontCache.js';
export declare const SVGNS = "http://www.w3.org/2000/svg";
export declare const XLINKNS = "http://www.w3.org/1999/xlink";
export declare class SVG<N, T, D> extends CommonOutputJax<N, T, D, SVGWrapper<N, T, D>, SVGWrapperFactory<N, T, D>, SVGFontData, typeof SVGFontData> {
    static NAME: string;
    static OPTIONS: OptionList;
    static commonStyles: CssStyleList;
    static FONTCACHEID: string;
    static STYLESHEETID: string;
    factory: SVGWrapperFactory<N, T, D>;
    fontCache: FontCache<N, T, D>;
    minwidth: number;
    shift: number;
    container: N;
    svgStyles: N;
    constructor(options?: OptionList);
    initialize(): void;
    clearFontCache(): void;
    reset(): void;
    protected setScale(node: N): void;
    escaped(math: MathItem<N, T, D>, html: MathDocument<N, T, D>): N;
    styleSheet(html: MathDocument<N, T, D>): N;
    pageElements(html: MathDocument<N, T, D>): N;
    protected findCache(html: MathDocument<N, T, D>): boolean;
    protected processMath(math: MmlNode, parent: N): void;
    protected createRoot(wrapper: SVGWrapper<N, T, D>): [N, N];
    protected typesetSVG(wrapper: SVGWrapper<N, T, D>, svg: N, g: N): void;
    protected setIndent(svg: N, align: string, shift: number): void;
    ex(m: number): string;
    svg(kind: string, properties?: OptionList, children?: (N | T)[]): N;
    unknownText(text: string, variant: string): N;
    measureTextNode(text: N): UnknownBBox;
}
