"use strict";
Object.defineProperty(exports, '__esModule', {value: true});
exports.balanceRules = MathJax._.input.tex.bussproofs.BussproofsUtil.balanceRules;
exports.setProperty = MathJax._.input.tex.bussproofs.BussproofsUtil.setProperty;
exports.getProperty = MathJax._.input.tex.bussproofs.BussproofsUtil.getProperty;
exports.removeProperty = MathJax._.input.tex.bussproofs.BussproofsUtil.removeProperty;
exports.makeBsprAttributes = MathJax._.input.tex.bussproofs.BussproofsUtil.makeBsprAttributes;
exports.saveDocument = MathJax._.input.tex.bussproofs.BussproofsUtil.saveDocument;
exports.clearDocument = MathJax._.input.tex.bussproofs.BussproofsUtil.clearDocument;
