// Generated by dts-bundle-generator v9.5.1

export type MarkedToken = (Tokens.Blockquote | Tokens.Br | Tokens.Code | Tokens.Codespan | Tokens.Def | Tokens.Del | Tokens.Em | Tokens.Escape | Tokens.Heading | Tokens.Hr | Tokens.HTML | Tokens.Image | Tokens.Link | Tokens.List | Tokens.ListItem | Tokens.Paragraph | Tokens.Space | Tokens.Strong | Tokens.Table | Tokens.Tag | Tokens.Text);
export type Token = (MarkedToken | Tokens.Generic);
export declare namespace Tokens {
	interface Blockquote {
		type: "blockquote";
		raw: string;
		text: string;
		tokens: Token[];
	}
	interface Br {
		type: "br";
		raw: string;
	}
	interface Checkbox {
		checked: boolean;
	}
	interface Code {
		type: "code";
		raw: string;
		codeBlockStyle?: "indented";
		lang?: string;
		text: string;
		escaped?: boolean;
	}
	interface Codespan {
		type: "codespan";
		raw: string;
		text: string;
	}
	interface Def {
		type: "def";
		raw: string;
		tag: string;
		href: string;
		title: string;
	}
	interface Del {
		type: "del";
		raw: string;
		text: string;
		tokens: Token[];
	}
	interface Em {
		type: "em";
		raw: string;
		text: string;
		tokens: Token[];
	}
	interface Escape {
		type: "escape";
		raw: string;
		text: string;
	}
	interface Generic {
		[index: string]: any;
		type: string;
		raw: string;
		tokens?: Token[];
	}
	interface Heading {
		type: "heading";
		raw: string;
		depth: number;
		text: string;
		tokens: Token[];
	}
	interface Hr {
		type: "hr";
		raw: string;
	}
	interface HTML {
		type: "html";
		raw: string;
		pre: boolean;
		text: string;
		block: boolean;
	}
	interface Image {
		type: "image";
		raw: string;
		href: string;
		title: string | null;
		text: string;
		tokens: Token[];
	}
	interface Link {
		type: "link";
		raw: string;
		href: string;
		title?: string | null;
		text: string;
		tokens: Token[];
	}
	interface List {
		type: "list";
		raw: string;
		ordered: boolean;
		start: number | "";
		loose: boolean;
		items: ListItem[];
	}
	interface ListItem {
		type: "list_item";
		raw: string;
		task: boolean;
		checked?: boolean;
		loose: boolean;
		text: string;
		tokens: Token[];
	}
	interface Paragraph {
		type: "paragraph";
		raw: string;
		pre?: boolean;
		text: string;
		tokens: Token[];
	}
	interface Space {
		type: "space";
		raw: string;
	}
	interface Strong {
		type: "strong";
		raw: string;
		text: string;
		tokens: Token[];
	}
	interface Table {
		type: "table";
		raw: string;
		align: Array<"center" | "left" | "right" | null>;
		header: TableCell[];
		rows: TableCell[][];
	}
	interface TableCell {
		text: string;
		tokens: Token[];
		header: boolean;
		align: "center" | "left" | "right" | null;
	}
	interface TableRow<P = string> {
		text: P;
	}
	interface Tag {
		type: "html";
		raw: string;
		inLink: boolean;
		inRawBlock: boolean;
		text: string;
		block: boolean;
	}
	interface Text {
		type: "text";
		raw: string;
		text: string;
		tokens?: Token[];
		escaped?: boolean;
	}
}
export type Links = Record<string, Pick<Tokens.Link | Tokens.Image, "href" | "title">>;
export type TokensList = Token[] & {
	links: Links;
};
/**
 * Renderer
 */
declare class _Renderer<ParserOutput = string, RendererOutput = string> {
	options: MarkedOptions<ParserOutput, RendererOutput>;
	parser: _Parser<ParserOutput, RendererOutput>;
	constructor(options?: MarkedOptions<ParserOutput, RendererOutput>);
	space(token: Tokens.Space): RendererOutput;
	code({ text, lang, escaped }: Tokens.Code): RendererOutput;
	blockquote({ tokens }: Tokens.Blockquote): RendererOutput;
	html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput;
	heading({ tokens, depth }: Tokens.Heading): RendererOutput;
	hr(token: Tokens.Hr): RendererOutput;
	list(token: Tokens.List): RendererOutput;
	listitem(item: Tokens.ListItem): RendererOutput;
	checkbox({ checked }: Tokens.Checkbox): RendererOutput;
	paragraph({ tokens }: Tokens.Paragraph): RendererOutput;
	table(token: Tokens.Table): RendererOutput;
	tablerow({ text }: Tokens.TableRow<ParserOutput>): RendererOutput;
	tablecell(token: Tokens.TableCell): RendererOutput;
	/**
	 * span level renderer
	 */
	strong({ tokens }: Tokens.Strong): RendererOutput;
	em({ tokens }: Tokens.Em): RendererOutput;
	codespan({ text }: Tokens.Codespan): RendererOutput;
	br(token: Tokens.Br): RendererOutput;
	del({ tokens }: Tokens.Del): RendererOutput;
	link({ href, title, tokens }: Tokens.Link): RendererOutput;
	image({ href, title, text, tokens }: Tokens.Image): RendererOutput;
	text(token: Tokens.Text | Tokens.Escape): RendererOutput;
}
/**
 * TextRenderer
 * returns only the textual part of the token
 */
declare class _TextRenderer<RendererOutput = string> {
	strong({ text }: Tokens.Strong): RendererOutput;
	em({ text }: Tokens.Em): RendererOutput;
	codespan({ text }: Tokens.Codespan): RendererOutput;
	del({ text }: Tokens.Del): RendererOutput;
	html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput;
	text({ text }: Tokens.Text | Tokens.Escape | Tokens.Tag): RendererOutput;
	link({ text }: Tokens.Link): RendererOutput;
	image({ text }: Tokens.Image): RendererOutput;
	br(): RendererOutput;
}
/**
 * Parsing & Compiling
 */
declare class _Parser<ParserOutput = string, RendererOutput = string> {
	options: MarkedOptions<ParserOutput, RendererOutput>;
	renderer: _Renderer<ParserOutput, RendererOutput>;
	textRenderer: _TextRenderer<RendererOutput>;
	constructor(options?: MarkedOptions<ParserOutput, RendererOutput>);
	/**
	 * Static Parse Method
	 */
	static parse<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>): ParserOutput;
	/**
	 * Static Parse Inline Method
	 */
	static parseInline<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>): ParserOutput;
	/**
	 * Parse Loop
	 */
	parse(tokens: Token[], top?: boolean): ParserOutput;
	/**
	 * Parse Inline Tokens
	 */
	parseInline(tokens: Token[], renderer?: _Renderer<ParserOutput, RendererOutput> | _TextRenderer<RendererOutput>): ParserOutput;
}
declare const other: {
	codeRemoveIndent: RegExp;
	outputLinkReplace: RegExp;
	indentCodeCompensation: RegExp;
	beginningSpace: RegExp;
	endingHash: RegExp;
	startingSpaceChar: RegExp;
	endingSpaceChar: RegExp;
	nonSpaceChar: RegExp;
	newLineCharGlobal: RegExp;
	tabCharGlobal: RegExp;
	multipleSpaceGlobal: RegExp;
	blankLine: RegExp;
	doubleBlankLine: RegExp;
	blockquoteStart: RegExp;
	blockquoteSetextReplace: RegExp;
	blockquoteSetextReplace2: RegExp;
	listReplaceTabs: RegExp;
	listReplaceNesting: RegExp;
	listIsTask: RegExp;
	listReplaceTask: RegExp;
	anyLine: RegExp;
	hrefBrackets: RegExp;
	tableDelimiter: RegExp;
	tableAlignChars: RegExp;
	tableRowBlankLine: RegExp;
	tableAlignRight: RegExp;
	tableAlignCenter: RegExp;
	tableAlignLeft: RegExp;
	startATag: RegExp;
	endATag: RegExp;
	startPreScriptTag: RegExp;
	endPreScriptTag: RegExp;
	startAngleBracket: RegExp;
	endAngleBracket: RegExp;
	pedanticHrefTitle: RegExp;
	unicodeAlphaNumeric: RegExp;
	escapeTest: RegExp;
	escapeReplace: RegExp;
	escapeTestNoEncode: RegExp;
	escapeReplaceNoEncode: RegExp;
	unescapeTest: RegExp;
	caret: RegExp;
	percentDecode: RegExp;
	findPipe: RegExp;
	splitPipe: RegExp;
	slashPipe: RegExp;
	carriageReturn: RegExp;
	spaceLine: RegExp;
	notSpaceStart: RegExp;
	endingNewline: RegExp;
	listItemRegex: (bull: string) => RegExp;
	nextBulletRegex: (indent: number) => RegExp;
	hrRegex: (indent: number) => RegExp;
	fencesBeginRegex: (indent: number) => RegExp;
	headingBeginRegex: (indent: number) => RegExp;
	htmlBeginRegex: (indent: number) => RegExp;
};
declare const blockNormal: {
	blockquote: RegExp;
	code: RegExp;
	def: RegExp;
	fences: RegExp;
	heading: RegExp;
	hr: RegExp;
	html: RegExp;
	lheading: RegExp;
	list: RegExp;
	newline: RegExp;
	paragraph: RegExp;
	table: RegExp;
	text: RegExp;
};
export type BlockKeys = keyof typeof blockNormal;
declare const inlineNormal: {
	_backpedal: RegExp;
	anyPunctuation: RegExp;
	autolink: RegExp;
	blockSkip: RegExp;
	br: RegExp;
	code: RegExp;
	del: RegExp;
	emStrongLDelim: RegExp;
	emStrongRDelimAst: RegExp;
	emStrongRDelimUnd: RegExp;
	escape: RegExp;
	link: RegExp;
	nolink: RegExp;
	punctuation: RegExp;
	reflink: RegExp;
	reflinkSearch: RegExp;
	tag: RegExp;
	text: RegExp;
	url: RegExp;
};
export type InlineKeys = keyof typeof inlineNormal;
export interface Rules {
	other: typeof other;
	block: Record<BlockKeys, RegExp>;
	inline: Record<InlineKeys, RegExp>;
}
/**
 * Tokenizer
 */
declare class _Tokenizer<ParserOutput = string, RendererOutput = string> {
	options: MarkedOptions<ParserOutput, RendererOutput>;
	rules: Rules;
	lexer: _Lexer<ParserOutput, RendererOutput>;
	constructor(options?: MarkedOptions<ParserOutput, RendererOutput>);
	space(src: string): Tokens.Space | undefined;
	code(src: string): Tokens.Code | undefined;
	fences(src: string): Tokens.Code | undefined;
	heading(src: string): Tokens.Heading | undefined;
	hr(src: string): Tokens.Hr | undefined;
	blockquote(src: string): Tokens.Blockquote | undefined;
	list(src: string): Tokens.List | undefined;
	html(src: string): Tokens.HTML | undefined;
	def(src: string): Tokens.Def | undefined;
	table(src: string): Tokens.Table | undefined;
	lheading(src: string): Tokens.Heading | undefined;
	paragraph(src: string): Tokens.Paragraph | undefined;
	text(src: string): Tokens.Text | undefined;
	escape(src: string): Tokens.Escape | undefined;
	tag(src: string): Tokens.Tag | undefined;
	link(src: string): Tokens.Link | Tokens.Image | undefined;
	reflink(src: string, links: Links): Tokens.Link | Tokens.Image | Tokens.Text | undefined;
	emStrong(src: string, maskedSrc: string, prevChar?: string): Tokens.Em | Tokens.Strong | undefined;
	codespan(src: string): Tokens.Codespan | undefined;
	br(src: string): Tokens.Br | undefined;
	del(src: string): Tokens.Del | undefined;
	autolink(src: string): Tokens.Link | undefined;
	url(src: string): Tokens.Link | undefined;
	inlineText(src: string): Tokens.Text | undefined;
}
declare class _Hooks<ParserOutput = string, RendererOutput = string> {
	options: MarkedOptions<ParserOutput, RendererOutput>;
	block?: boolean;
	constructor(options?: MarkedOptions<ParserOutput, RendererOutput>);
	static passThroughHooks: Set<string>;
	/**
	 * Process markdown before marked
	 */
	preprocess(markdown: string): string;
	/**
	 * Process HTML after marked is finished
	 */
	postprocess(html: ParserOutput): ParserOutput;
	/**
	 * Process all tokens before walk tokens
	 */
	processAllTokens(tokens: Token[] | TokensList): Token[] | TokensList;
	/**
	 * Provide function to tokenize markdown
	 */
	provideLexer(): typeof _Lexer.lexInline;
	/**
	 * Provide function to parse tokens
	 */
	provideParser(): (tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput> | undefined) => ParserOutput;
}
export interface TokenizerThis {
	lexer: _Lexer;
}
export type TokenizerExtensionFunction = (this: TokenizerThis, src: string, tokens: Token[] | TokensList) => Tokens.Generic | undefined;
export type TokenizerStartFunction = (this: TokenizerThis, src: string) => number | void;
export interface TokenizerExtension {
	name: string;
	level: "block" | "inline";
	start?: TokenizerStartFunction;
	tokenizer: TokenizerExtensionFunction;
	childTokens?: string[];
}
export interface RendererThis<ParserOutput = string, RendererOutput = string> {
	parser: _Parser<ParserOutput, RendererOutput>;
}
export type RendererExtensionFunction<ParserOutput = string, RendererOutput = string> = (this: RendererThis<ParserOutput, RendererOutput>, token: Tokens.Generic) => RendererOutput | false | undefined;
export interface RendererExtension<ParserOutput = string, RendererOutput = string> {
	name: string;
	renderer: RendererExtensionFunction<ParserOutput, RendererOutput>;
}
export type TokenizerAndRendererExtension<ParserOutput = string, RendererOutput = string> = TokenizerExtension | RendererExtension<ParserOutput, RendererOutput> | (TokenizerExtension & RendererExtension<ParserOutput, RendererOutput>);
export type HooksApi<ParserOutput = string, RendererOutput = string> = Omit<_Hooks<ParserOutput, RendererOutput>, "constructor" | "options" | "block">;
export type HooksObject<ParserOutput = string, RendererOutput = string> = {
	[K in keyof HooksApi<ParserOutput, RendererOutput>]?: (this: _Hooks<ParserOutput, RendererOutput>, ...args: Parameters<HooksApi<ParserOutput, RendererOutput>[K]>) => ReturnType<HooksApi<ParserOutput, RendererOutput>[K]> | Promise<ReturnType<HooksApi<ParserOutput, RendererOutput>[K]>>;
};
export type RendererApi<ParserOutput = string, RendererOutput = string> = Omit<_Renderer<ParserOutput, RendererOutput>, "constructor" | "options" | "parser">;
export type RendererObject<ParserOutput = string, RendererOutput = string> = {
	[K in keyof RendererApi<ParserOutput, RendererOutput>]?: (this: _Renderer<ParserOutput, RendererOutput>, ...args: Parameters<RendererApi<ParserOutput, RendererOutput>[K]>) => ReturnType<RendererApi<ParserOutput, RendererOutput>[K]> | false;
};
export type TokenizerApi<ParserOutput = string, RendererOutput = string> = Omit<_Tokenizer<ParserOutput, RendererOutput>, "constructor" | "options" | "rules" | "lexer">;
export type TokenizerObject<ParserOutput = string, RendererOutput = string> = {
	[K in keyof TokenizerApi<ParserOutput, RendererOutput>]?: (this: _Tokenizer<ParserOutput, RendererOutput>, ...args: Parameters<TokenizerApi<ParserOutput, RendererOutput>[K]>) => ReturnType<TokenizerApi<ParserOutput, RendererOutput>[K]> | false;
};
export interface MarkedExtension<ParserOutput = string, RendererOutput = string> {
	/**
	 * True will tell marked to await any walkTokens functions before parsing the tokens and returning an HTML string.
	 */
	async?: boolean;
	/**
	 * Enable GFM line breaks. This option requires the gfm option to be true.
	 */
	breaks?: boolean;
	/**
	 * Add tokenizers and renderers to marked
	 */
	extensions?: TokenizerAndRendererExtension<ParserOutput, RendererOutput>[] | null;
	/**
	 * Enable GitHub flavored markdown.
	 */
	gfm?: boolean;
	/**
	 * Hooks are methods that hook into some part of marked.
	 * preprocess is called to process markdown before sending it to marked.
	 * processAllTokens is called with the TokensList before walkTokens.
	 * postprocess is called to process html after marked has finished parsing.
	 * provideLexer is called to provide a function to tokenize markdown.
	 * provideParser is called to provide a function to parse tokens.
	 */
	hooks?: HooksObject<ParserOutput, RendererOutput> | null;
	/**
	 * Conform to obscure parts of markdown.pl as much as possible. Don't fix any of the original markdown bugs or poor behavior.
	 */
	pedantic?: boolean;
	/**
	 * Type: object Default: new Renderer()
	 *
	 * An object containing functions to render tokens to HTML.
	 */
	renderer?: RendererObject<ParserOutput, RendererOutput> | null;
	/**
	 * Shows an HTML error message when rendering fails.
	 */
	silent?: boolean;
	/**
	 * The tokenizer defines how to turn markdown text into tokens.
	 */
	tokenizer?: TokenizerObject | null;
	/**
	 * The walkTokens function gets called with every token.
	 * Child tokens are called before moving on to sibling tokens.
	 * Each token is passed by reference so updates are persisted when passed to the parser.
	 * The return value of the function is ignored.
	 */
	walkTokens?: ((token: Token) => void | Promise<void>) | null;
}
export interface MarkedOptions<ParserOutput = string, RendererOutput = string> extends Omit<MarkedExtension<ParserOutput, RendererOutput>, "hooks" | "renderer" | "tokenizer" | "extensions" | "walkTokens"> {
	/**
	 * Hooks are methods that hook into some part of marked.
	 */
	hooks?: _Hooks<ParserOutput, RendererOutput> | null;
	/**
	 * Type: object Default: new Renderer()
	 *
	 * An object containing functions to render tokens to HTML.
	 */
	renderer?: _Renderer<ParserOutput, RendererOutput> | null;
	/**
	 * The tokenizer defines how to turn markdown text into tokens.
	 */
	tokenizer?: _Tokenizer<ParserOutput, RendererOutput> | null;
	/**
	 * Custom extensions
	 */
	extensions?: null | {
		renderers: {
			[name: string]: RendererExtensionFunction<ParserOutput, RendererOutput>;
		};
		childTokens: {
			[name: string]: string[];
		};
		inline?: TokenizerExtensionFunction[];
		block?: TokenizerExtensionFunction[];
		startInline?: TokenizerStartFunction[];
		startBlock?: TokenizerStartFunction[];
	};
	/**
	 * walkTokens function returns array of values for Promise.all
	 */
	walkTokens?: null | ((token: Token) => void | Promise<void> | (void | Promise<void>)[]);
}
/**
 * Block Lexer
 */
declare class _Lexer<ParserOutput = string, RendererOutput = string> {
	tokens: TokensList;
	options: MarkedOptions<ParserOutput, RendererOutput>;
	state: {
		inLink: boolean;
		inRawBlock: boolean;
		top: boolean;
	};
	private tokenizer;
	private inlineQueue;
	constructor(options?: MarkedOptions<ParserOutput, RendererOutput>);
	/**
	 * Expose Rules
	 */
	static get rules(): {
		block: {
			normal: {
				blockquote: RegExp;
				code: RegExp;
				def: RegExp;
				fences: RegExp;
				heading: RegExp;
				hr: RegExp;
				html: RegExp;
				lheading: RegExp;
				list: RegExp;
				newline: RegExp;
				paragraph: RegExp;
				table: RegExp;
				text: RegExp;
			};
			gfm: Record<"code" | "blockquote" | "hr" | "html" | "table" | "text" | "def" | "heading" | "list" | "paragraph" | "fences" | "lheading" | "newline", RegExp>;
			pedantic: Record<"code" | "blockquote" | "hr" | "html" | "table" | "text" | "def" | "heading" | "list" | "paragraph" | "fences" | "lheading" | "newline", RegExp>;
		};
		inline: {
			normal: {
				_backpedal: RegExp;
				anyPunctuation: RegExp;
				autolink: RegExp;
				blockSkip: RegExp;
				br: RegExp;
				code: RegExp;
				del: RegExp;
				emStrongLDelim: RegExp;
				emStrongRDelimAst: RegExp;
				emStrongRDelimUnd: RegExp;
				escape: RegExp;
				link: RegExp;
				nolink: RegExp;
				punctuation: RegExp;
				reflink: RegExp;
				reflinkSearch: RegExp;
				tag: RegExp;
				text: RegExp;
				url: RegExp;
			};
			gfm: Record<"link" | "code" | "url" | "br" | "del" | "text" | "escape" | "tag" | "reflink" | "nolink" | "_backpedal" | "anyPunctuation" | "autolink" | "blockSkip" | "emStrongLDelim" | "emStrongRDelimAst" | "emStrongRDelimUnd" | "punctuation" | "reflinkSearch", RegExp>;
			breaks: Record<"link" | "code" | "url" | "br" | "del" | "text" | "escape" | "tag" | "reflink" | "nolink" | "_backpedal" | "anyPunctuation" | "autolink" | "blockSkip" | "emStrongLDelim" | "emStrongRDelimAst" | "emStrongRDelimUnd" | "punctuation" | "reflinkSearch", RegExp>;
			pedantic: Record<"link" | "code" | "url" | "br" | "del" | "text" | "escape" | "tag" | "reflink" | "nolink" | "_backpedal" | "anyPunctuation" | "autolink" | "blockSkip" | "emStrongLDelim" | "emStrongRDelimAst" | "emStrongRDelimUnd" | "punctuation" | "reflinkSearch", RegExp>;
		};
	};
	/**
	 * Static Lex Method
	 */
	static lex<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>): TokensList;
	/**
	 * Static Lex Inline Method
	 */
	static lexInline<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>): Token[];
	/**
	 * Preprocessing
	 */
	lex(src: string): TokensList;
	/**
	 * Lexing
	 */
	blockTokens(src: string, tokens?: Token[], lastParagraphClipped?: boolean): Token[];
	blockTokens(src: string, tokens?: TokensList, lastParagraphClipped?: boolean): TokensList;
	inline(src: string, tokens?: Token[]): Token[];
	/**
	 * Lexing/Compiling
	 */
	inlineTokens(src: string, tokens?: Token[]): Token[];
}
/**
 * Gets the original marked default options.
 */
declare function _getDefaults<ParserOutput = string, RendererOutput = string>(): MarkedOptions<ParserOutput, RendererOutput>;
declare let _defaults: MarkedOptions<any, any>;
export type MaybePromise = void | Promise<void>;
export declare class Marked<ParserOutput = string, RendererOutput = string> {
	defaults: MarkedOptions<ParserOutput, RendererOutput>;
	options: (opt: MarkedOptions<ParserOutput, RendererOutput>) => this;
	parse: {
		(src: string, options: MarkedOptions<ParserOutput, RendererOutput> & {
			async: true;
		}): Promise<ParserOutput>;
		(src: string, options: MarkedOptions<ParserOutput, RendererOutput> & {
			async: false;
		}): ParserOutput;
		(src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): ParserOutput | Promise<ParserOutput>;
	};
	parseInline: {
		(src: string, options: MarkedOptions<ParserOutput, RendererOutput> & {
			async: true;
		}): Promise<ParserOutput>;
		(src: string, options: MarkedOptions<ParserOutput, RendererOutput> & {
			async: false;
		}): ParserOutput;
		(src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): ParserOutput | Promise<ParserOutput>;
	};
	Parser: {
		new (options?: MarkedOptions<ParserOutput, RendererOutput> | undefined): _Parser<ParserOutput, RendererOutput>;
		parse<ParserOutput_1 = string, RendererOutput_1 = string>(tokens: Token[], options?: MarkedOptions<ParserOutput_1, RendererOutput_1>): ParserOutput_1;
		parseInline<ParserOutput_1 = string, RendererOutput_1 = string>(tokens: Token[], options?: MarkedOptions<ParserOutput_1, RendererOutput_1>): ParserOutput_1;
	};
	Renderer: {
		new (options?: MarkedOptions<ParserOutput, RendererOutput> | undefined): _Renderer<ParserOutput, RendererOutput>;
	};
	TextRenderer: {
		new (): _TextRenderer<RendererOutput>;
	};
	Lexer: typeof _Lexer;
	Tokenizer: {
		new (options?: MarkedOptions<ParserOutput, RendererOutput> | undefined): _Tokenizer<ParserOutput, RendererOutput>;
	};
	Hooks: {
		new (options?: MarkedOptions<ParserOutput, RendererOutput> | undefined): _Hooks<ParserOutput, RendererOutput>;
		passThroughHooks: Set<string>;
	};
	constructor(...args: MarkedExtension<ParserOutput, RendererOutput>[]);
	/**
	 * Run callback for every token
	 */
	walkTokens(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]): MaybePromise[];
	use(...args: MarkedExtension<ParserOutput, RendererOutput>[]): this;
	setOptions(opt: MarkedOptions<ParserOutput, RendererOutput>): this;
	lexer(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>): TokensList;
	parser(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>): ParserOutput;
	private parseMarkdown;
	private onError;
}
/**
 * Compiles markdown to HTML asynchronously.
 *
 * @param src String of markdown source to be compiled
 * @param options Hash of options, having async: true
 * @return Promise of string of compiled HTML
 */
export declare function marked(src: string, options: MarkedOptions & {
	async: true;
}): Promise<string>;
/**
 * Compiles markdown to HTML.
 *
 * @param src String of markdown source to be compiled
 * @param options Optional hash of options
 * @return String of compiled HTML. Will be a Promise of string if async is set to true by any extensions.
 */
export declare function marked(src: string, options: MarkedOptions & {
	async: false;
}): string;
export declare function marked(src: string, options: MarkedOptions & {
	async: true;
}): Promise<string>;
export declare function marked(src: string, options?: MarkedOptions | null): string | Promise<string>;
export declare namespace marked {
	var options: (options: MarkedOptions) => typeof marked;
	var setOptions: (options: MarkedOptions) => typeof marked;
	var getDefaults: typeof _getDefaults;
	var defaults: MarkedOptions<any, any>;
	var use: (...args: MarkedExtension[]) => typeof marked;
	var walkTokens: (tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) => MaybePromise[];
	var parseInline: {
		(src: string, options: MarkedOptions<string, string> & {
			async: true;
		}): Promise<string>;
		(src: string, options: MarkedOptions<string, string> & {
			async: false;
		}): string;
		(src: string, options?: MarkedOptions<string, string> | null | undefined): string | Promise<string>;
	};
	var Parser: typeof _Parser;
	var parser: typeof _Parser.parse;
	var Renderer: typeof _Renderer;
	var TextRenderer: typeof _TextRenderer;
	var Lexer: typeof _Lexer;
	var lexer: typeof _Lexer.lex;
	var Tokenizer: typeof _Tokenizer;
	var Hooks: typeof _Hooks;
	var parse: typeof marked;
}
export declare const options: (options: MarkedOptions) => typeof marked;
export declare const setOptions: (options: MarkedOptions) => typeof marked;
export declare const use: (...args: MarkedExtension[]) => typeof marked;
export declare const walkTokens: (tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) => MaybePromise[];
export declare const parseInline: {
	(src: string, options: MarkedOptions<string, string> & {
		async: true;
	}): Promise<string>;
	(src: string, options: MarkedOptions<string, string> & {
		async: false;
	}): string;
	(src: string, options?: MarkedOptions<string, string> | null | undefined): string | Promise<string>;
};
export declare const parse: typeof marked;
export declare const parser: typeof _Parser.parse;
export declare const lexer: typeof _Lexer.lex;

export {
	_Hooks as Hooks,
	_Lexer as Lexer,
	_Parser as Parser,
	_Renderer as Renderer,
	_TextRenderer as TextRenderer,
	_Tokenizer as Tokenizer,
	_defaults as defaults,
	_getDefaults as getDefaults,
};

export {};
