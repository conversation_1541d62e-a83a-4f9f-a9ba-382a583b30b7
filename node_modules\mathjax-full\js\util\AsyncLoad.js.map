{"version": 3, "file": "AsyncLoad.js", "sourceRoot": "", "sources": ["../../ts/util/AsyncLoad.ts"], "names": [], "mappings": ";;;AAuBA,4CAAsC;AAQtC,SAAgB,SAAS,CAAC,IAAY;IACpC,IAAI,CAAC,oBAAO,CAAC,SAAS,EAAE;QACtB,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAe,IAAI,qCAAkC,CAAC,CAAC;KAC9E;IACD,OAAO,IAAI,OAAO,CAAC,UAAC,EAAE,EAAE,IAAI;QAC1B,IAAM,MAAM,GAAG,oBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,MAAM,YAAY,OAAO,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,UAAC,KAAU,IAAK,OAAA,EAAE,CAAC,KAAK,CAAC,EAAT,CAAS,CAAC,CAAC,KAAK,CAAC,UAAC,GAAU,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,EAAT,CAAS,CAAC,CAAC;SACzE;aAAM;YACL,EAAE,CAAC,MAAM,CAAC,CAAC;SACZ;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAZD,8BAYC"}