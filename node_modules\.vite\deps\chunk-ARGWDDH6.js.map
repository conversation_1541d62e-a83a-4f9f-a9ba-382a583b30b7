{"version": 3, "sources": ["../../mathjax-full/ts/util/AsyncLoad.ts", "../../mathjax-full/ts/util/Entities.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements asynchronous loading of files\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {mathjax} from '../mathjax.js';\n\n/**\n * Load a file asynchronously using the mathjax.asynchLoad method, if there is one\n *\n * @param {string} name  The name of the file to load\n * @return {Promise}     The promise that is satisfied when the file is loaded\n */\nexport function asyncLoad(name: string): Promise<void> {\n  if (!mathjax.asyncLoad) {\n    return Promise.reject(`Can't load '${name}': No asyncLoad method specified`);\n  }\n  return new Promise((ok, fail) => {\n    const result = mathjax.asyncLoad(name);\n    if (result instanceof Promise) {\n      result.then((value: any) => ok(value)).catch((err: Error) => fail(err));\n    } else {\n      ok(result);\n    }\n  });\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Converts named entities to unicode characters\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {retryAfter} from './Retries.js';\nimport {asyncLoad} from './AsyncLoad.js';\nimport {OptionList} from './Options.js';\n\n/**\n * The type for lists of entities\n */\nexport type EntityList = {[name: string]: string};\n\n\n/**\n *  Options controlling the process of conversion\n */\nexport const options: OptionList = {\n  loadMissingEntities: true           // True means load entity files dynamically if needed\n};\n\n/**\n *  The entity name-to-value translation table\n *  (basic math entities -- others are loaded from external files)\n */\nexport const entities: EntityList = {\n  ApplyFunction: '\\u2061',\n  Backslash: '\\u2216',\n  Because: '\\u2235',\n  Breve: '\\u02D8',\n  Cap: '\\u22D2',\n  CenterDot: '\\u00B7',\n  CircleDot: '\\u2299',\n  CircleMinus: '\\u2296',\n  CirclePlus: '\\u2295',\n  CircleTimes: '\\u2297',\n  Congruent: '\\u2261',\n  ContourIntegral: '\\u222E',\n  Coproduct: '\\u2210',\n  Cross: '\\u2A2F',\n  Cup: '\\u22D3',\n  CupCap: '\\u224D',\n  Dagger: '\\u2021',\n  Del: '\\u2207',\n  Delta: '\\u0394',\n  Diamond: '\\u22C4',\n  DifferentialD: '\\u2146',\n  DotEqual: '\\u2250',\n  DoubleDot: '\\u00A8',\n  DoubleRightTee: '\\u22A8',\n  DoubleVerticalBar: '\\u2225',\n  DownArrow: '\\u2193',\n  DownLeftVector: '\\u21BD',\n  DownRightVector: '\\u21C1',\n  DownTee: '\\u22A4',\n  Downarrow: '\\u21D3',\n  Element: '\\u2208',\n  EqualTilde: '\\u2242',\n  Equilibrium: '\\u21CC',\n  Exists: '\\u2203',\n  ExponentialE: '\\u2147',\n  FilledVerySmallSquare: '\\u25AA',\n  ForAll: '\\u2200',\n  Gamma: '\\u0393',\n  Gg: '\\u22D9',\n  GreaterEqual: '\\u2265',\n  GreaterEqualLess: '\\u22DB',\n  GreaterFullEqual: '\\u2267',\n  GreaterLess: '\\u2277',\n  GreaterSlantEqual: '\\u2A7E',\n  GreaterTilde: '\\u2273',\n  Hacek: '\\u02C7',\n  Hat: '\\u005E',\n  HumpDownHump: '\\u224E',\n  HumpEqual: '\\u224F',\n  Im: '\\u2111',\n  ImaginaryI: '\\u2148',\n  Integral: '\\u222B',\n  Intersection: '\\u22C2',\n  InvisibleComma: '\\u2063',\n  InvisibleTimes: '\\u2062',\n  Lambda: '\\u039B',\n  Larr: '\\u219E',\n  LeftAngleBracket: '\\u27E8',\n  LeftArrow: '\\u2190',\n  LeftArrowRightArrow: '\\u21C6',\n  LeftCeiling: '\\u2308',\n  LeftDownVector: '\\u21C3',\n  LeftFloor: '\\u230A',\n  LeftRightArrow: '\\u2194',\n  LeftTee: '\\u22A3',\n  LeftTriangle: '\\u22B2',\n  LeftTriangleEqual: '\\u22B4',\n  LeftUpVector: '\\u21BF',\n  LeftVector: '\\u21BC',\n  Leftarrow: '\\u21D0',\n  Leftrightarrow: '\\u21D4',\n  LessEqualGreater: '\\u22DA',\n  LessFullEqual: '\\u2266',\n  LessGreater: '\\u2276',\n  LessSlantEqual: '\\u2A7D',\n  LessTilde: '\\u2272',\n  Ll: '\\u22D8',\n  Lleftarrow: '\\u21DA',\n  LongLeftArrow: '\\u27F5',\n  LongLeftRightArrow: '\\u27F7',\n  LongRightArrow: '\\u27F6',\n  Longleftarrow: '\\u27F8',\n  Longleftrightarrow: '\\u27FA',\n  Longrightarrow: '\\u27F9',\n  Lsh: '\\u21B0',\n  MinusPlus: '\\u2213',\n  NestedGreaterGreater: '\\u226B',\n  NestedLessLess: '\\u226A',\n  NotDoubleVerticalBar: '\\u2226',\n  NotElement: '\\u2209',\n  NotEqual: '\\u2260',\n  NotExists: '\\u2204',\n  NotGreater: '\\u226F',\n  NotGreaterEqual: '\\u2271',\n  NotLeftTriangle: '\\u22EA',\n  NotLeftTriangleEqual: '\\u22EC',\n  NotLess: '\\u226E',\n  NotLessEqual: '\\u2270',\n  NotPrecedes: '\\u2280',\n  NotPrecedesSlantEqual: '\\u22E0',\n  NotRightTriangle: '\\u22EB',\n  NotRightTriangleEqual: '\\u22ED',\n  NotSubsetEqual: '\\u2288',\n  NotSucceeds: '\\u2281',\n  NotSucceedsSlantEqual: '\\u22E1',\n  NotSupersetEqual: '\\u2289',\n  NotTilde: '\\u2241',\n  NotVerticalBar: '\\u2224',\n  Omega: '\\u03A9',\n  OverBar: '\\u203E',\n  OverBrace: '\\u23DE',\n  PartialD: '\\u2202',\n  Phi: '\\u03A6',\n  Pi: '\\u03A0',\n  PlusMinus: '\\u00B1',\n  Precedes: '\\u227A',\n  PrecedesEqual: '\\u2AAF',\n  PrecedesSlantEqual: '\\u227C',\n  PrecedesTilde: '\\u227E',\n  Product: '\\u220F',\n  Proportional: '\\u221D',\n  Psi: '\\u03A8',\n  Rarr: '\\u21A0',\n  Re: '\\u211C',\n  ReverseEquilibrium: '\\u21CB',\n  RightAngleBracket: '\\u27E9',\n  RightArrow: '\\u2192',\n  RightArrowLeftArrow: '\\u21C4',\n  RightCeiling: '\\u2309',\n  RightDownVector: '\\u21C2',\n  RightFloor: '\\u230B',\n  RightTee: '\\u22A2',\n  RightTeeArrow: '\\u21A6',\n  RightTriangle: '\\u22B3',\n  RightTriangleEqual: '\\u22B5',\n  RightUpVector: '\\u21BE',\n  RightVector: '\\u21C0',\n  Rightarrow: '\\u21D2',\n  Rrightarrow: '\\u21DB',\n  Rsh: '\\u21B1',\n  Sigma: '\\u03A3',\n  SmallCircle: '\\u2218',\n  Sqrt: '\\u221A',\n  Square: '\\u25A1',\n  SquareIntersection: '\\u2293',\n  SquareSubset: '\\u228F',\n  SquareSubsetEqual: '\\u2291',\n  SquareSuperset: '\\u2290',\n  SquareSupersetEqual: '\\u2292',\n  SquareUnion: '\\u2294',\n  Star: '\\u22C6',\n  Subset: '\\u22D0',\n  SubsetEqual: '\\u2286',\n  Succeeds: '\\u227B',\n  SucceedsEqual: '\\u2AB0',\n  SucceedsSlantEqual: '\\u227D',\n  SucceedsTilde: '\\u227F',\n  SuchThat: '\\u220B',\n  Sum: '\\u2211',\n  Superset: '\\u2283',\n  SupersetEqual: '\\u2287',\n  Supset: '\\u22D1',\n  Therefore: '\\u2234',\n  Theta: '\\u0398',\n  Tilde: '\\u223C',\n  TildeEqual: '\\u2243',\n  TildeFullEqual: '\\u2245',\n  TildeTilde: '\\u2248',\n  UnderBar: '\\u005F',\n  UnderBrace: '\\u23DF',\n  Union: '\\u22C3',\n  UnionPlus: '\\u228E',\n  UpArrow: '\\u2191',\n  UpDownArrow: '\\u2195',\n  UpTee: '\\u22A5',\n  Uparrow: '\\u21D1',\n  Updownarrow: '\\u21D5',\n  Upsilon: '\\u03A5',\n  Vdash: '\\u22A9',\n  Vee: '\\u22C1',\n  VerticalBar: '\\u2223',\n  VerticalTilde: '\\u2240',\n  Vvdash: '\\u22AA',\n  Wedge: '\\u22C0',\n  Xi: '\\u039E',\n  amp: '\\u0026',\n  acute: '\\u00B4',\n  aleph: '\\u2135',\n  alpha: '\\u03B1',\n  amalg: '\\u2A3F',\n  and: '\\u2227',\n  ang: '\\u2220',\n  angmsd: '\\u2221',\n  angsph: '\\u2222',\n  ape: '\\u224A',\n  backprime: '\\u2035',\n  backsim: '\\u223D',\n  backsimeq: '\\u22CD',\n  beta: '\\u03B2',\n  beth: '\\u2136',\n  between: '\\u226C',\n  bigcirc: '\\u25EF',\n  bigodot: '\\u2A00',\n  bigoplus: '\\u2A01',\n  bigotimes: '\\u2A02',\n  bigsqcup: '\\u2A06',\n  bigstar: '\\u2605',\n  bigtriangledown: '\\u25BD',\n  bigtriangleup: '\\u25B3',\n  biguplus: '\\u2A04',\n  blacklozenge: '\\u29EB',\n  blacktriangle: '\\u25B4',\n  blacktriangledown: '\\u25BE',\n  blacktriangleleft: '\\u25C2',\n  bowtie: '\\u22C8',\n  boxdl: '\\u2510',\n  boxdr: '\\u250C',\n  boxminus: '\\u229F',\n  boxplus: '\\u229E',\n  boxtimes: '\\u22A0',\n  boxul: '\\u2518',\n  boxur: '\\u2514',\n  bsol: '\\u005C',\n  bull: '\\u2022',\n  cap: '\\u2229',\n  check: '\\u2713',\n  chi: '\\u03C7',\n  circ: '\\u02C6',\n  circeq: '\\u2257',\n  circlearrowleft: '\\u21BA',\n  circlearrowright: '\\u21BB',\n  circledR: '\\u00AE',\n  circledS: '\\u24C8',\n  circledast: '\\u229B',\n  circledcirc: '\\u229A',\n  circleddash: '\\u229D',\n  clubs: '\\u2663',\n  colon: '\\u003A',\n  comp: '\\u2201',\n  ctdot: '\\u22EF',\n  cuepr: '\\u22DE',\n  cuesc: '\\u22DF',\n  cularr: '\\u21B6',\n  cup: '\\u222A',\n  curarr: '\\u21B7',\n  curlyvee: '\\u22CE',\n  curlywedge: '\\u22CF',\n  dagger: '\\u2020',\n  daleth: '\\u2138',\n  ddarr: '\\u21CA',\n  deg: '\\u00B0',\n  delta: '\\u03B4',\n  digamma: '\\u03DD',\n  div: '\\u00F7',\n  divideontimes: '\\u22C7',\n  dot: '\\u02D9',\n  doteqdot: '\\u2251',\n  dotplus: '\\u2214',\n  dotsquare: '\\u22A1',\n  dtdot: '\\u22F1',\n  ecir: '\\u2256',\n  efDot: '\\u2252',\n  egs: '\\u2A96',\n  ell: '\\u2113',\n  els: '\\u2A95',\n  empty: '\\u2205',\n  epsi: '\\u03B5',\n  epsiv: '\\u03F5',\n  erDot: '\\u2253',\n  eta: '\\u03B7',\n  eth: '\\u00F0',\n  flat: '\\u266D',\n  fork: '\\u22D4',\n  frown: '\\u2322',\n  gEl: '\\u2A8C',\n  gamma: '\\u03B3',\n  gap: '\\u2A86',\n  gimel: '\\u2137',\n  gnE: '\\u2269',\n  gnap: '\\u2A8A',\n  gne: '\\u2A88',\n  gnsim: '\\u22E7',\n  gt: '\\u003E',\n  gtdot: '\\u22D7',\n  harrw: '\\u21AD',\n  hbar: '\\u210F',\n  hellip: '\\u2026',\n  hookleftarrow: '\\u21A9',\n  hookrightarrow: '\\u21AA',\n  imath: '\\u0131',\n  infin: '\\u221E',\n  intcal: '\\u22BA',\n  iota: '\\u03B9',\n  jmath: '\\u0237',\n  kappa: '\\u03BA',\n  kappav: '\\u03F0',\n  lEg: '\\u2A8B',\n  lambda: '\\u03BB',\n  lap: '\\u2A85',\n  larrlp: '\\u21AB',\n  larrtl: '\\u21A2',\n  lbrace: '\\u007B',\n  lbrack: '\\u005B',\n  le: '\\u2264',\n  leftleftarrows: '\\u21C7',\n  leftthreetimes: '\\u22CB',\n  lessdot: '\\u22D6',\n  lmoust: '\\u23B0',\n  lnE: '\\u2268',\n  lnap: '\\u2A89',\n  lne: '\\u2A87',\n  lnsim: '\\u22E6',\n  longmapsto: '\\u27FC',\n  looparrowright: '\\u21AC',\n  lowast: '\\u2217',\n  loz: '\\u25CA',\n  lt: '\\u003C',\n  ltimes: '\\u22C9',\n  ltri: '\\u25C3',\n  macr: '\\u00AF',\n  malt: '\\u2720',\n  mho: '\\u2127',\n  mu: '\\u03BC',\n  multimap: '\\u22B8',\n  nLeftarrow: '\\u21CD',\n  nLeftrightarrow: '\\u21CE',\n  nRightarrow: '\\u21CF',\n  nVDash: '\\u22AF',\n  nVdash: '\\u22AE',\n  natur: '\\u266E',\n  nearr: '\\u2197',\n  nharr: '\\u21AE',\n  nlarr: '\\u219A',\n  not: '\\u00AC',\n  nrarr: '\\u219B',\n  nu: '\\u03BD',\n  nvDash: '\\u22AD',\n  nvdash: '\\u22AC',\n  nwarr: '\\u2196',\n  omega: '\\u03C9',\n  omicron: '\\u03BF',\n  or: '\\u2228',\n  osol: '\\u2298',\n  period: '\\u002E',\n  phi: '\\u03C6',\n  phiv: '\\u03D5',\n  pi: '\\u03C0',\n  piv: '\\u03D6',\n  prap: '\\u2AB7',\n  precnapprox: '\\u2AB9',\n  precneqq: '\\u2AB5',\n  precnsim: '\\u22E8',\n  prime: '\\u2032',\n  psi: '\\u03C8',\n  quot: '\\u0022',\n  rarrtl: '\\u21A3',\n  rbrace: '\\u007D',\n  rbrack: '\\u005D',\n  rho: '\\u03C1',\n  rhov: '\\u03F1',\n  rightrightarrows: '\\u21C9',\n  rightthreetimes: '\\u22CC',\n  ring: '\\u02DA',\n  rmoust: '\\u23B1',\n  rtimes: '\\u22CA',\n  rtri: '\\u25B9',\n  scap: '\\u2AB8',\n  scnE: '\\u2AB6',\n  scnap: '\\u2ABA',\n  scnsim: '\\u22E9',\n  sdot: '\\u22C5',\n  searr: '\\u2198',\n  sect: '\\u00A7',\n  sharp: '\\u266F',\n  sigma: '\\u03C3',\n  sigmav: '\\u03C2',\n  simne: '\\u2246',\n  smile: '\\u2323',\n  spades: '\\u2660',\n  sub: '\\u2282',\n  subE: '\\u2AC5',\n  subnE: '\\u2ACB',\n  subne: '\\u228A',\n  supE: '\\u2AC6',\n  supnE: '\\u2ACC',\n  supne: '\\u228B',\n  swarr: '\\u2199',\n  tau: '\\u03C4',\n  theta: '\\u03B8',\n  thetav: '\\u03D1',\n  tilde: '\\u02DC',\n  times: '\\u00D7',\n  triangle: '\\u25B5',\n  triangleq: '\\u225C',\n  upsi: '\\u03C5',\n  upuparrows: '\\u21C8',\n  veebar: '\\u22BB',\n  vellip: '\\u22EE',\n  weierp: '\\u2118',\n  xi: '\\u03BE',\n  yen: '\\u00A5',\n  zeta: '\\u03B6',\n  zigrarr: '\\u21DD',\n\n  //\n  //  Needed by TeX input jax\n  nbsp: '\\u00A0',\n  rsquo: '\\u2019',\n  lsquo: '\\u2018'\n};\n\n/**\n * The files that have been loaded\n */\nconst loaded: {[name: string]: boolean} = {};\n\n/**\n * Used by entity files to add more entities to the table\n *\n * @param {EntityList} additions The entities to add\n * @param {string} file          The name of the file that they came from\n */\nexport function add(additions: EntityList, file: string) {\n  Object.assign(entities, additions);\n  loaded[file] = true;\n}\n\n/**\n * Used to remove an entity from the list, if needed\n *\n * @param {string} entity  The name of the entity to remove\n */\nexport function remove(entity: string) {\n  delete entities[entity];\n}\n\n/**\n * @param {string} text  The text whose entities are to be replaced\n * @return {string}      The text with entries replaced\n */\nexport function translate(text: string): string {\n  return text.replace(/&([a-z][a-z0-9]*|#(?:[0-9]+|x[0-9a-f]+));/ig, replace);\n}\n\n/**\n * Returns the unicode character for an entity, if found\n * If not, loads an entity file to see if it is there (and retries after loading)\n * Otherwire, returns the original entity string\n *\n * @param {string} match   The complete entity being replaced\n * @param {string} entity  The name of the entity to be replaced\n * @return {string}        The unicode character for the entity, or the entity name (if none found)\n */\nfunction replace(match: string, entity: string): string {\n  if (entity.charAt(0) === '#') {\n    return numeric(entity.slice(1));\n  }\n  if (entities[entity]) {\n    return entities[entity];\n  }\n  if (options['loadMissingEntities']) {\n    let file = (entity.match(/^[a-zA-Z](fr|scr|opf)$/) ? RegExp.$1 : entity.charAt(0).toLowerCase());\n    if (!loaded[file]) {\n      loaded[file] = true;\n      retryAfter(asyncLoad('./util/entities/' + file + '.js'));\n    }\n  }\n  return match;\n}\n\n/**\n * @param {string} entity  The character code point as a string\n * @return {string}        The character(s) with the given code point\n */\nexport function numeric(entity: string): string {\n  let n = (entity.charAt(0) === 'x' ?\n           parseInt(entity.slice(1), 16) :\n           parseInt(entity));\n  return String.fromCodePoint(n);\n}\n"], "mappings": ";;;;;;;;;;;;;;AAuBA,QAAA,eAAA;AAQA,aAAgB,UAAU,MAAY;AACpC,UAAI,CAAC,aAAA,QAAQ,WAAW;AACtB,eAAO,QAAQ,OAAO,eAAA,OAAe,MAAI,kCAAA,CAAkC;;AAE7E,aAAO,IAAI,QAAQ,SAAC,IAAI,MAAI;AAC1B,YAAM,SAAS,aAAA,QAAQ,UAAU,IAAI;AACrC,YAAI,kBAAkB,SAAS;AAC7B,iBAAO,KAAK,SAAC,OAAU;AAAK,mBAAA,GAAG,KAAK;UAAR,CAAS,EAAE,MAAM,SAAC,KAAU;AAAK,mBAAA,KAAK,GAAG;UAAR,CAAS;eACjE;AACL,aAAG,MAAM;;MAEb,CAAC;IACH;AAZA,YAAA,YAAA;;;;;;;;;;ACRA,QAAA,eAAA;AACA,QAAA,iBAAA;AAYa,YAAA,UAAsB;MACjC,qBAAqB;;AAOV,YAAA,WAAuB;MAClC,eAAe;MACf,WAAW;MACX,SAAS;MACT,OAAO;MACP,KAAK;MACL,WAAW;MACX,WAAW;MACX,aAAa;MACb,YAAY;MACZ,aAAa;MACb,WAAW;MACX,iBAAiB;MACjB,WAAW;MACX,OAAO;MACP,KAAK;MACL,QAAQ;MACR,QAAQ;MACR,KAAK;MACL,OAAO;MACP,SAAS;MACT,eAAe;MACf,UAAU;MACV,WAAW;MACX,gBAAgB;MAChB,mBAAmB;MACnB,WAAW;MACX,gBAAgB;MAChB,iBAAiB;MACjB,SAAS;MACT,WAAW;MACX,SAAS;MACT,YAAY;MACZ,aAAa;MACb,QAAQ;MACR,cAAc;MACd,uBAAuB;MACvB,QAAQ;MACR,OAAO;MACP,IAAI;MACJ,cAAc;MACd,kBAAkB;MAClB,kBAAkB;MAClB,aAAa;MACb,mBAAmB;MACnB,cAAc;MACd,OAAO;MACP,KAAK;MACL,cAAc;MACd,WAAW;MACX,IAAI;MACJ,YAAY;MACZ,UAAU;MACV,cAAc;MACd,gBAAgB;MAChB,gBAAgB;MAChB,QAAQ;MACR,MAAM;MACN,kBAAkB;MAClB,WAAW;MACX,qBAAqB;MACrB,aAAa;MACb,gBAAgB;MAChB,WAAW;MACX,gBAAgB;MAChB,SAAS;MACT,cAAc;MACd,mBAAmB;MACnB,cAAc;MACd,YAAY;MACZ,WAAW;MACX,gBAAgB;MAChB,kBAAkB;MAClB,eAAe;MACf,aAAa;MACb,gBAAgB;MAChB,WAAW;MACX,IAAI;MACJ,YAAY;MACZ,eAAe;MACf,oBAAoB;MACpB,gBAAgB;MAChB,eAAe;MACf,oBAAoB;MACpB,gBAAgB;MAChB,KAAK;MACL,WAAW;MACX,sBAAsB;MACtB,gBAAgB;MAChB,sBAAsB;MACtB,YAAY;MACZ,UAAU;MACV,WAAW;MACX,YAAY;MACZ,iBAAiB;MACjB,iBAAiB;MACjB,sBAAsB;MACtB,SAAS;MACT,cAAc;MACd,aAAa;MACb,uBAAuB;MACvB,kBAAkB;MAClB,uBAAuB;MACvB,gBAAgB;MAChB,aAAa;MACb,uBAAuB;MACvB,kBAAkB;MAClB,UAAU;MACV,gBAAgB;MAChB,OAAO;MACP,SAAS;MACT,WAAW;MACX,UAAU;MACV,KAAK;MACL,IAAI;MACJ,WAAW;MACX,UAAU;MACV,eAAe;MACf,oBAAoB;MACpB,eAAe;MACf,SAAS;MACT,cAAc;MACd,KAAK;MACL,MAAM;MACN,IAAI;MACJ,oBAAoB;MACpB,mBAAmB;MACnB,YAAY;MACZ,qBAAqB;MACrB,cAAc;MACd,iBAAiB;MACjB,YAAY;MACZ,UAAU;MACV,eAAe;MACf,eAAe;MACf,oBAAoB;MACpB,eAAe;MACf,aAAa;MACb,YAAY;MACZ,aAAa;MACb,KAAK;MACL,OAAO;MACP,aAAa;MACb,MAAM;MACN,QAAQ;MACR,oBAAoB;MACpB,cAAc;MACd,mBAAmB;MACnB,gBAAgB;MAChB,qBAAqB;MACrB,aAAa;MACb,MAAM;MACN,QAAQ;MACR,aAAa;MACb,UAAU;MACV,eAAe;MACf,oBAAoB;MACpB,eAAe;MACf,UAAU;MACV,KAAK;MACL,UAAU;MACV,eAAe;MACf,QAAQ;MACR,WAAW;MACX,OAAO;MACP,OAAO;MACP,YAAY;MACZ,gBAAgB;MAChB,YAAY;MACZ,UAAU;MACV,YAAY;MACZ,OAAO;MACP,WAAW;MACX,SAAS;MACT,aAAa;MACb,OAAO;MACP,SAAS;MACT,aAAa;MACb,SAAS;MACT,OAAO;MACP,KAAK;MACL,aAAa;MACb,eAAe;MACf,QAAQ;MACR,OAAO;MACP,IAAI;MACJ,KAAK;MACL,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,KAAK;MACL,KAAK;MACL,QAAQ;MACR,QAAQ;MACR,KAAK;MACL,WAAW;MACX,SAAS;MACT,WAAW;MACX,MAAM;MACN,MAAM;MACN,SAAS;MACT,SAAS;MACT,SAAS;MACT,UAAU;MACV,WAAW;MACX,UAAU;MACV,SAAS;MACT,iBAAiB;MACjB,eAAe;MACf,UAAU;MACV,cAAc;MACd,eAAe;MACf,mBAAmB;MACnB,mBAAmB;MACnB,QAAQ;MACR,OAAO;MACP,OAAO;MACP,UAAU;MACV,SAAS;MACT,UAAU;MACV,OAAO;MACP,OAAO;MACP,MAAM;MACN,MAAM;MACN,KAAK;MACL,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;MACR,iBAAiB;MACjB,kBAAkB;MAClB,UAAU;MACV,UAAU;MACV,YAAY;MACZ,aAAa;MACb,aAAa;MACb,OAAO;MACP,OAAO;MACP,MAAM;MACN,OAAO;MACP,OAAO;MACP,OAAO;MACP,QAAQ;MACR,KAAK;MACL,QAAQ;MACR,UAAU;MACV,YAAY;MACZ,QAAQ;MACR,QAAQ;MACR,OAAO;MACP,KAAK;MACL,OAAO;MACP,SAAS;MACT,KAAK;MACL,eAAe;MACf,KAAK;MACL,UAAU;MACV,SAAS;MACT,WAAW;MACX,OAAO;MACP,MAAM;MACN,OAAO;MACP,KAAK;MACL,KAAK;MACL,KAAK;MACL,OAAO;MACP,MAAM;MACN,OAAO;MACP,OAAO;MACP,KAAK;MACL,KAAK;MACL,MAAM;MACN,MAAM;MACN,OAAO;MACP,KAAK;MACL,OAAO;MACP,KAAK;MACL,OAAO;MACP,KAAK;MACL,MAAM;MACN,KAAK;MACL,OAAO;MACP,IAAI;MACJ,OAAO;MACP,OAAO;MACP,MAAM;MACN,QAAQ;MACR,eAAe;MACf,gBAAgB;MAChB,OAAO;MACP,OAAO;MACP,QAAQ;MACR,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,KAAK;MACL,QAAQ;MACR,KAAK;MACL,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,IAAI;MACJ,gBAAgB;MAChB,gBAAgB;MAChB,SAAS;MACT,QAAQ;MACR,KAAK;MACL,MAAM;MACN,KAAK;MACL,OAAO;MACP,YAAY;MACZ,gBAAgB;MAChB,QAAQ;MACR,KAAK;MACL,IAAI;MACJ,QAAQ;MACR,MAAM;MACN,MAAM;MACN,MAAM;MACN,KAAK;MACL,IAAI;MACJ,UAAU;MACV,YAAY;MACZ,iBAAiB;MACjB,aAAa;MACb,QAAQ;MACR,QAAQ;MACR,OAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,KAAK;MACL,OAAO;MACP,IAAI;MACJ,QAAQ;MACR,QAAQ;MACR,OAAO;MACP,OAAO;MACP,SAAS;MACT,IAAI;MACJ,MAAM;MACN,QAAQ;MACR,KAAK;MACL,MAAM;MACN,IAAI;MACJ,KAAK;MACL,MAAM;MACN,aAAa;MACb,UAAU;MACV,UAAU;MACV,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,KAAK;MACL,MAAM;MACN,kBAAkB;MAClB,iBAAiB;MACjB,MAAM;MACN,QAAQ;MACR,QAAQ;MACR,MAAM;MACN,MAAM;MACN,MAAM;MACN,OAAO;MACP,QAAQ;MACR,MAAM;MACN,OAAO;MACP,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,OAAO;MACP,OAAO;MACP,QAAQ;MACR,KAAK;MACL,MAAM;MACN,OAAO;MACP,OAAO;MACP,MAAM;MACN,OAAO;MACP,OAAO;MACP,OAAO;MACP,KAAK;MACL,OAAO;MACP,QAAQ;MACR,OAAO;MACP,OAAO;MACP,UAAU;MACV,WAAW;MACX,MAAM;MACN,YAAY;MACZ,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,IAAI;MACJ,KAAK;MACL,MAAM;MACN,SAAS;MAIT,MAAM;MACN,OAAO;MACP,OAAO;;AAMT,QAAM,SAAoC,CAAA;AAQ1C,aAAgB,IAAI,WAAuB,MAAY;AACrD,aAAO,OAAO,QAAA,UAAU,SAAS;AACjC,aAAO,IAAI,IAAI;IACjB;AAHA,YAAA,MAAA;AAUA,aAAgB,OAAO,QAAc;AACnC,aAAO,QAAA,SAAS,MAAM;IACxB;AAFA,YAAA,SAAA;AAQA,aAAgB,UAAU,MAAY;AACpC,aAAO,KAAK,QAAQ,+CAA+C,OAAO;IAC5E;AAFA,YAAA,YAAA;AAaA,aAAS,QAAQ,OAAe,QAAc;AAC5C,UAAI,OAAO,OAAO,CAAC,MAAM,KAAK;AAC5B,eAAO,QAAQ,OAAO,MAAM,CAAC,CAAC;;AAEhC,UAAI,QAAA,SAAS,MAAM,GAAG;AACpB,eAAO,QAAA,SAAS,MAAM;;AAExB,UAAI,QAAA,QAAQ,qBAAqB,GAAG;AAClC,YAAI,OAAQ,OAAO,MAAM,wBAAwB,IAAI,OAAO,KAAK,OAAO,OAAO,CAAC,EAAE,YAAW;AAC7F,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI;AACf,WAAA,GAAA,aAAA,aAAW,GAAA,eAAA,WAAU,qBAAqB,OAAO,KAAK,CAAC;;;AAG3D,aAAO;IACT;AAMA,aAAgB,QAAQ,QAAc;AACpC,UAAI,IAAK,OAAO,OAAO,CAAC,MAAM,MACrB,SAAS,OAAO,MAAM,CAAC,GAAG,EAAE,IAC5B,SAAS,MAAM;AACxB,aAAO,OAAO,cAAc,CAAC;IAC/B;AALA,YAAA,UAAA;;;", "names": []}