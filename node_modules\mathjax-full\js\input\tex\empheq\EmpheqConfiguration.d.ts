import { Configuration } from '../Configuration.js';
import TexParser from '../TexParser.js';
import { BeginItem } from '../base/BaseItems.js';
import { StackItem } from '../StackItem.js';
export declare class EmpheqBeginItem extends BeginItem {
    get kind(): string;
    checkItem(item: StackItem): import("../StackItem.js").CheckType;
}
export declare const EmpheqMethods: {
    Empheq(parser: TexParser, begin: EmpheqBeginItem): void;
    EmpheqMO(parser: TexParser, _name: string, c: string): void;
    EmpheqDelim(parser: TexParser, name: string): void;
};
export declare const EmpheqConfiguration: Configuration;
