{"version": 3, "file": "mi.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAA6F;AAO7F;IAA2B,yBAAoB;IAA/C;QAAA,qEAgEC;QA1CW,cAAQ,GAAG,qBAAQ,CAAC,GAAG,CAAC;;IA0CpC,CAAC;IArCC,sBAAW,uBAAI;aAAf;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAQM,sCAAsB,GAA7B,UAA8B,UAA8B,EAC9B,OAAwB,EAAE,KAAiB,EAAE,KAAsB;QADnE,2BAAA,EAAA,eAA8B;QAC9B,wBAAA,EAAA,eAAwB;QAAE,sBAAA,EAAA,SAAiB;QAAE,sBAAA,EAAA,aAAsB;QAC/F,iBAAM,sBAAsB,YAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAChE,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAChE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;SACvD;IACH,CAAC;IAOM,2BAAW,GAAlB,UAAmB,IAAqB;QACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,QAAQ;YAC/C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,SAAS;YACxC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YAC9C,IAAI,CAAC,QAAQ,GAAG,qBAAQ,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAClC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAzDa,cAAQ,gBACjB,iCAAoB,CAAC,QAAQ,EAChC;IAKY,kBAAY,GAAW,mBAAmB,CAAC;IAI3C,qBAAe,GAC3B,4EAA4E,CAAC;IA+CjF,YAAC;CAAA,AAhED,CAA2B,iCAAoB,GAgE9C;AAhEY,sBAAK"}