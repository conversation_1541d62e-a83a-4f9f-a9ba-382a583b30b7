{"version": 3, "file": "KeyExplorer.js", "sourceRoot": "", "sources": ["../../../ts/a11y/explorer/KeyExplorer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,6CAAyD;AACzD,qDAA4B;AAqC5B;IAAqD,uCAAmB;IAAxE;QAAA,qEA0GC;QArGQ,cAAQ,GAAY,KAAK,CAAC;QAQzB,oBAAc,GAAY,KAAK,CAAC;QAK9B,YAAM,GACd,iBAAM,MAAM,YAAE,CAAC,MAAM,CACnB,CAAC,CAAC,SAAS,EAAE,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;YACpC,CAAC,SAAS,EAAE,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;YACpC,CAAC,UAAU,EAAE,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAMtC,cAAQ,GAAW,IAAI,CAAC;;IA8ElC,CAAC;IApEQ,qCAAO,GAAd,UAAe,MAAkB;IACjC,CAAC;IAKM,sCAAQ,GAAf,UAAgB,MAAkB;QAChC,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAKM,oCAAM,GAAb,UAAc,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK;YAAE,OAAO;QACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACtB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;SAC3C;QACD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAsB,CAAC,CAAC;IACrD,CAAC;IAKM,oCAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAChD,CAAC;IAKM,uCAAS,GAAhB;QACE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,iBAAM,SAAS,WAAE,CAAC;YAClB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC;IAKM,oCAAM,GAAb;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAKM,kCAAI,GAAX;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;SAC1B;QACD,iBAAM,IAAI,WAAE,CAAC;IACf,CAAC;IAEH,0BAAC;AAAD,CAAC,AA1GD,CAAqD,8BAAgB,GA0GpE;AA1GqB,kDAAmB;AAkHzC;IAAoC,kCAA2B;IA8B7D,wBAAmB,QAAsB,EACnB,MAAsB,EACtB,IAAiB,EACnB,GAAW;QAH/B,YAIE,kBAAM,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,SAE9B;QANkB,cAAQ,GAAR,QAAQ,CAAc;QACnB,YAAM,GAAN,MAAM,CAAgB;QACtB,UAAI,GAAJ,IAAI,CAAa;QACnB,SAAG,GAAH,GAAG,CAAQ;QAnBxB,gBAAU,GAAW,WAAW,CAAC;QAEhC,UAAI,GAAY,KAAK,CAAC;QAQtB,eAAS,GAAY,KAAK,CAAC;QAWjC,KAAI,CAAC,UAAU,EAAE,CAAC;;IACpB,CAAC;IAMM,8BAAK,GAAZ;QAAA,iBA8BC;QA7BC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAC3B,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;;;oBAC/D,WAAO,gBAAG,CAAC,QAAQ,EAAE;6BAClB,IAAI,CAAC,cAAM,OAAA,gBAAG,CAAC,WAAW,CAAC,EAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC,CAAC,EAAzC,CAAyC,CAAC;6BACrD,IAAI,CAAC;4BAGJ,KAAI,CAAC,MAAM,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;4BACzB,KAAI,CAAC,KAAK,EAAE,CAAC;wBACf,CAAC,CAAC,EAAC;;iBACN,CAAC;iBACC,KAAK,CAAC,UAAC,KAAY,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAA1B,CAA0B,CAAC,CAAC;YACvD,OAAO;SACR;QACD,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,GAAG,gBAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,gBAAG,CAAC,SAAS,CACzB,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACxE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC/C,cAAc,CAAC,aAAa,CAAC,IAAI,CAC/B,cAAM,OAAA,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,CAAC,WAAW,CAAC,EAA7C,CAA6C,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAMM,+BAAM,GAAb,UAAc,KAAsB;QAApC,iBAiBC;QAjBa,sBAAA,EAAA,aAAsB;QAClC,iBAAM,MAAM,YAAC,KAAK,CAAC,CAAC;QACpB,IAAI,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;QAGhD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAChD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;gBACpC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC;SACxC;QACD,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;;;gBAC/D,WAAO,gBAAG,CAAC,QAAQ,EAAE;yBAClB,IAAI,CAAC,cAAM,OAAA,gBAAG,CAAC,WAAW,CAAC,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC,CAAC,EADzC,CACyC,CAAC;yBACrD,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAxC,CAAwC,CAAC,EAAC;;aACzD,CAAC,CAAC;IACL,CAAC;IAOM,+BAAM,GAAb,UAAc,MAAkB;QAAhC,iBASC;QARC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;YAChC,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,KAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC5C,KAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,KAAI,CAAC,SAAS,IAAI,KAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,EAAE;gBACjE,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,CAAC,WAAW,CAAC,CAAC;aAC/C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAMM,gCAAO,GAAd,UAAe,KAAoB;QACjC,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QACtC,IAAI,IAAI,KAAK,EAAE,EAAE;YACf,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO;SACR;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAAE,OAAO;YACnC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO;SACR;QACD,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,KAAK,EAAE,EAAE;YAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB;IACH,CAAC;IAMS,oCAAW,GAArB,UAAsB,IAAY;;QAChC,IAAI,IAAI,KAAK,EAAE,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QACD,IAAI,IAAI,GAAG,MAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,0CAAG,CAAC,CAAC,CAAC;QAClD,IAAI,KAAK,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CACd,YAAY,CAAC,uBAAuB,CAAC,0CACrC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC1B,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,6BAAI,GAAX,UAAY,GAAW;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAKO,mCAAU,GAAlB;QACE,IAAI,CAAC,eAAe,GAAG,gBAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,KAAK,GAAG,gBAAG,CAAC,SAAS,CACvB,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACxE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAOO,mCAAU,GAAlB;QACE,IAAI,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;QAChD,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;QAC3C,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAC/B,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;gBACnC,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;gBACpC,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC,EAAE;YACvC,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACnC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;YACjC,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC7B;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IA9Lc,4BAAa,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAgMnD,qBAAC;CAAA,AAlMD,CAAoC,mBAAmB,GAkMtD;AAlMY,wCAAc;AA0M3B;IAA+B,6BAAgC;IAM7D,mBAAmB,QAAsB,EACnB,MAA2B,EAC3B,IAAiB,EACnB,GAAW;QAH/B,YAIE,kBAAM,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,SAI9B;QARkB,cAAQ,GAAR,QAAQ,CAAc;QACnB,YAAM,GAAN,MAAM,CAAqB;QAC3B,UAAI,GAAJ,IAAI,CAAa;QACnB,SAAG,GAAH,GAAG,CAAQ;QAE7B,KAAI,CAAC,MAAM,GAAG,gBAAG,CAAC,SAAS,CACzB,OAAO,EAAE,KAAI,CAAC,IAAI,EAAE,gBAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,EACnD,KAAI,CAAC,WAAW,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC;;IAChC,CAAC;IAKM,0BAAM,GAAb,UAAc,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QAClC,iBAAM,MAAM,YAAC,KAAK,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAMM,yBAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAMO,6BAAS,GAAjB;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAgB,CAAC;QAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAMM,wBAAI,GAAX,UAAY,GAAW;QACrB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IAMM,2BAAO,GAAd,UAAe,KAAoB;QACjC,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QACtC,IAAI,IAAI,KAAK,EAAE,EAAE;YACf,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO;SACR;QACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO;SACR;QACD,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,KAAK,EAAE,EAAE;YAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB;IACH,CAAC;IAEH,gBAAC;AAAD,CAAC,AA/ED,CAA+B,mBAAmB,GA+EjD;AA/EY,8BAAS"}