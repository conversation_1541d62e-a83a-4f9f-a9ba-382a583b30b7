{"version": 3, "file": "EmpheqUtil.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/empheq/EmpheqUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,iEAAwC;AACxC,iEAAwC;AAQ3B,QAAA,UAAU,GAAG;IAUxB,WAAW,EAAX,UAAY,MAAiB,EAAE,GAAW,EAAE,IAAc,EAAE,IAAW;QACrE,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAC,CAAC,CAAC;QAC9F,MAAM,CAAC,IAAI,CAAC,IAAI,8BAAC,MAAM,EAAE,IAAI,UAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAE,CAAC;IACpD,CAAC;IASD,YAAY,EAAZ,UAAa,IAAY,EAAE,OAAuC;QAAvC,wBAAA,EAAA,cAAuC;QAChE,OAAO,sBAAS,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAQD,WAAW,EAAX,UAAY,KAAgB;;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC;;YACV,KAAkB,IAAA,KAAA,SAAA,KAAK,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,GAAG,WAAA;gBACZ,IAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,GAAG,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC;aAClB;;;;;;;;;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAaD,SAAS,EAAT,UAAU,GAAW,EAAE,KAAgB,EAAE,MAAiB,EAAE,GAAW;;QACrE,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,UAAU,EAAC,CAAC,CAAC;QACjG,IAAM,MAAM,GAAG,IAAI,sBAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;QAC1E,IAAM,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE;YACzC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,UAAkB,CAAC,GAAG,GAAG,GAAG,CAAC;YACvD,MAAM,CAAC,aAAa,CAAC,IAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC1D;;YACD,KAAoB,IAAA,KAAA,SAAA,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBAA1D,IAAM,KAAK,WAAA;gBACd,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC5B;;;;;;;;;QACD,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;YACpD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,EAAC,KAAK,EAAE,CAAC,EAAC,CAAC;SACtD,CAAC,CAAC,CAAC;QACJ,OAAO,OAAO,CAAC;IACjB,CAAC;IAUD,WAAW,EAAX,UAAY,QAAmB,EAAE,MAAiB;QAChD,IAAM,KAAK,GAAG,sBAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACnD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC5C,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAAG,EAAC,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;IAC3G,CAAC;IAYD,WAAW,EAAX,UAAY,GAAW,EAAE,GAAW,EAAE,KAAgB,EAAE,MAAiB,EAAE,GAAW;QACpF,GAAG,CAAC,WAAW,CACb,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,sBAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC;YACnE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC;SAChC,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC,CAC7C,CAAC;IACJ,CAAC;IAWD,IAAI,EAAJ,UAAK,KAAgB,EAAE,QAAmB,EAAE,IAAY,EAAE,MAAiB,EAAE,GAAgB;;QAAhB,oBAAA,EAAA,QAAgB;QAC3F,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5F,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC9F,IAAI,GAAG,CAAC;;YACR,KAAkB,IAAA,KAAA,SAAA,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAAlD,IAAM,GAAG,WAAA;gBACZ,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACnC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC5B,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;gBACjB,IAAI,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;oBAC5B,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACtC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;iBACzB;aACF;;;;;;;;;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAWD,KAAK,EAAL,UAAM,KAAgB,EAAE,QAAmB,EAAE,KAAa,EAAE,MAAiB,EAAE,GAAgB;QAAhB,oBAAA,EAAA,QAAgB;QAC7F,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;SACjD;QACD,IAAM,CAAC,GAAG,kBAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACxC,IAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;YAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QAChF,IAAM,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAW,CAAC;QACpE,kBAAU,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1D,KAAK,CAAC,UAAU,CAAC,GAAG,CAClB,aAAa,EACb,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CACjG,CAAC;QACF,KAAK,CAAC,UAAU,CAAC,GAAG,CAClB,eAAe,EACf,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CACtG,CAAC;IACJ,CAAC;IAKD,WAAW,EAAX,UAAY,MAAuB,EAAE,MAAiB;QACpD,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACxC,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,IAAI,IAAI,KAAK,EAAE;YACjB,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;YAC1B,IAAM,QAAQ,GAAG,sBAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,IAAI;gBAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,KAAK;gBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACvD;IACH,CAAC;IAKD,QAAQ,EAAE;QACR,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;KACf;IAQD,QAAQ,EAAR,UAAS,GAAW;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC;IACvE,CAAC;CAEF,CAAC"}