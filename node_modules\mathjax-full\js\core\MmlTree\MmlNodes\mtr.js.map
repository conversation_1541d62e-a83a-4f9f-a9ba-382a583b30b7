{"version": 3, "file": "mtr.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mtr.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAsE;AACtE,kDAAyC;AACzC,qDAA8C;AAO9C;IAA4B,0BAAe;IAA3C;;IAsFA,CAAC;IAvEC,sBAAW,wBAAI;aAAf;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAMD,sBAAW,sCAAkB;aAA7B;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAOS,4CAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;;;YAC9G,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;yBAC/C,WAAW,CAAC,KAAK,CAAC,CAAC;iBACzB;aACF;;;;;;;;;QACD,IAAM,MAAM,GAAG,IAAA,iBAAK,EAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAW,CAAC,CAAC;QACnE,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;YACpB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC,CAAC;SAC9D;QACD,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE;YACnD,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC;YACzC,WAAW,EAAE,QAAQ;SACtB,CAAC,CAAC;;YACH,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACxE,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACjE;;;;;;;;;IACH,CAAC;IAOS,+BAAc,GAAxB,UAAyB,OAAqB;;QAC5C,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,mCAAmC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC5E,OAAO;SACR;;YACD,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACxB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAY,CAAC;oBAC1E,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBACvB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;wBAC1B,KAAK,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,GAAG,cAAc,EAAE,OAAO,CAAC,CAAC;qBACpE;iBACF;aACF;;;;;;;;;QACD,iBAAM,cAAc,YAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAKM,4BAAW,GAAlB,UAAmB,IAAa;;QAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;;YACxB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aACzB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IA/Ea,eAAQ,yBACjB,4BAAe,CAAC,QAAQ,KAC3B,QAAQ,EAAE,uBAAO,EACjB,WAAW,EAAE,uBAAO,EACpB,UAAU,EAAE,uBAAO,IACnB;IA4EJ,aAAC;CAAA,AAtFD,CAA4B,4BAAe,GAsF1C;AAtFY,wBAAM;AA6FnB;IAAmC,iCAAM;IAAzC;;IAiBA,CAAC;IAZC,sBAAW,+BAAI;aAAf;YACE,OAAO,YAAY,CAAC;QACtB,CAAC;;;OAAA;IAMD,sBAAI,gCAAK;aAAT;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAEH,oBAAC;AAAD,CAAC,AAjBD,CAAmC,MAAM,GAiBxC;AAjBY,sCAAa"}