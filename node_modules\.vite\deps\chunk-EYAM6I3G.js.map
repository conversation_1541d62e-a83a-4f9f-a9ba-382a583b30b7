{"version": 3, "sources": ["../../mathjax-full/ts/components/version.ts", "../../mathjax-full/ts/core/HandlerList.ts", "../../mathjax-full/ts/util/Retries.ts", "../../mathjax-full/ts/mathjax.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  The version of MathJax (used to tell what version a component\n *                was compiled against).\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nexport const VERSION = '3.2.2';\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for HandlerList objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PrioritizedList} from '../util/PrioritizedList.js';\nimport {OptionList} from '../util/Options.js';\nimport {Handler} from './Handler.js';\nimport {MathDocument} from './MathDocument.js';\n\n/*****************************************************************/\n/**\n *  The HandlerList class (extends PrioritizedList of Handlers)\n *\n *  This list is used to find the handler for a given document\n *  by asking each handler to test if it can handle the document,\n *  and when one can, it is asked to create its associated MathDocument.\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HandlerList<N, T, D> extends PrioritizedList<Handler<N, T, D>>  {\n\n  /**\n   * @param {Handler} handler  The handler to register\n   * @return {Handler}  The list item created for the handler\n   */\n  public register(handler: Handler<N, T, D>): Handler<N, T, D> {\n    return this.add(handler, handler.priority);\n  }\n\n  /**\n   * @param {Handler} Handler  The handler to remove from the list\n   */\n  public unregister(handler: Handler<N, T, D>) {\n    this.remove(handler);\n  }\n\n  /**\n   * @param {any} document  The document (string, window, DOM element, etc) to be handled\n   * @return {Handler}      The handler from the list that can process the given document\n   */\n  public handlesDocument(document: any): Handler<N, T, D> {\n    for (const item of this) {\n      let handler = item.item;\n      if (handler.handlesDocument(document)) {\n        return handler;\n      }\n    }\n    throw new Error(`Can't find handler for document`);\n  }\n\n  /**\n   * @param {any} document        The document to be processed\n   * @param {OptionList} options  The options for the handler\n   * @return {MathDocument}       The MathDocument created by the handler for this document\n   */\n  public document(document: any, options: OptionList = null): MathDocument<N, T, D> {\n    return this.handlesDocument(document).create(document, options);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements methods for handling asynchronous actions\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n\n/*****************************************************************/\n/*\n *  The legacy MathJax object  (FIXME: remove this after all v2 code is gone)\n */\n\ndeclare var MathJax: {Callback: {After: Function}};\n\n\n/*****************************************************************/\n/**\n *  Allow us to pass a promise as part of an Error object\n */\n\nexport interface RetryError extends Error {\n  retry: Promise<any>;\n}\n\n/*****************************************************************/\n/**\n * A wrapper for actions that may be asynchronous.  This will\n *   rerun the action after the asychronous action completes.\n *   Usually, this is for dynamic loading of files.  Legacy\n *   MathJax does that a lot, so we still need it for now, but\n *   may be able to go without it in the future.\n *\n *   Example:\n *\n *     HandleRetriesFor(() => {\n *\n *         html.findMath()\n *             .compile()\n *             .getMetrics()\n *             .typeset()\n *             .updateDocument();\n *\n *     }).catch(err => {\n *       console.log(err.message);\n *     });\n *\n * @param {Function} code  The code to run that might cause retries\n * @return {Promise}       A promise that is satisfied when the code\n *                         runs completely, and fails if the code\n *                         generates an error (that is not a retry).\n */\n\nexport function handleRetriesFor(code: Function): Promise<any> {\n  return new Promise(function run(ok: Function, fail: Function) {\n    try {\n      ok(code());\n    } catch (err) {\n      if (err.retry && err.retry instanceof Promise) {\n        err.retry.then(() => run(ok, fail))\n                 .catch((perr: Error) => fail(perr));\n      } else if (err.restart && err.restart.isCallback) {\n        // FIXME: Remove this branch when all legacy code is gone\n        MathJax.Callback.After(() => run(ok, fail), err.restart);\n      } else {\n        fail(err);\n      }\n    }\n  });\n}\n\n/*****************************************************************/\n/**\n * Tells HandleRetriesFor() to wait for this promise to be fulfilled\n *   before rerunning the code.  Causes an error to be thrown, so\n *   calling this terminates the code at that point.\n *\n * @param {Promise} promise  The promise that must be satisfied before\n *                            actions will continue\n */\n\nexport function retryAfter(promise: Promise<any>) {\n  let err = new Error('MathJax retry') as RetryError;\n  err.retry = promise;\n  throw err;\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  The main MathJax global object\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {VERSION} from './components/version.js';\nimport {HandlerList} from './core/HandlerList.js';\nimport {handleRetriesFor, retryAfter} from './util/Retries.js';\nimport {OptionList} from './util/Options.js';\nimport {MathDocument} from './core/MathDocument.js';\n\n/*****************************************************************/\n/**\n * The main MathJax global object\n */\nexport const mathjax = {\n  /**\n   *  The MathJax version number\n   */\n  version: VERSION,\n\n  /**\n   *  The list of registers document handlers\n   */\n  handlers: new HandlerList<any, any, any>(),\n\n  /**\n   * Creates a MathDocument using a registered handler that knows how to handl it\n   *\n   * @param {any} document        The document to handle\n   * @param {OptionList} options   The options to use for the document (e.g., input and output jax)\n   * @return {MathDocument}       The MathDocument to handle the document\n   */\n  document: function (document: any, options: OptionList): MathDocument<any, any, any> {\n    return mathjax.handlers.document(document, options);\n  },\n\n  /**\n   * The functions for handling retries if a file must be loaded dynamically\n   */\n  handleRetriesFor: handleRetriesFor,\n  retryAfter: retryAfter,\n\n  /**\n   * A function for loading external files (can be changed for node/browser use)\n   */\n  asyncLoad: null as ((file: string) => any),\n\n};\n"], "mappings": ";;;;;;;;;;;;;AAwBa,YAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDvB,QAAA,uBAAA;AAiBA,QAAA,cAAA,SAAA,QAAA;AAA0C,gBAAAA,cAAA,MAAA;AAA1C,eAAAA,eAAA;;MAwCA;AAlCS,MAAAA,aAAA,UAAA,WAAP,SAAgB,SAAyB;AACvC,eAAO,KAAK,IAAI,SAAS,QAAQ,QAAQ;MAC3C;AAKO,MAAAA,aAAA,UAAA,aAAP,SAAkB,SAAyB;AACzC,aAAK,OAAO,OAAO;MACrB;AAMO,MAAAA,aAAA,UAAA,kBAAP,SAAuB,UAAa;;;AAClC,mBAAmB,KAAA,SAAA,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApB,gBAAM,OAAI,GAAA;AACb,gBAAI,UAAU,KAAK;AACnB,gBAAI,QAAQ,gBAAgB,QAAQ,GAAG;AACrC,qBAAO;;;;;;;;;;;;AAGX,cAAM,IAAI,MAAM,iCAAiC;MACnD;AAOO,MAAAA,aAAA,UAAA,WAAP,SAAgB,UAAe,SAA0B;AAA1B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAA0B;AACvD,eAAO,KAAK,gBAAgB,QAAQ,EAAE,OAAO,UAAU,OAAO;MAChE;AAEF,aAAAA;IAAA,EAxC0C,qBAAA,eAAe;AAA5C,YAAA,cAAA;;;;;;;;;;AC6Bb,aAAgB,iBAAiB,MAAc;AAC7C,aAAO,IAAI,QAAQ,SAAS,IAAI,IAAc,MAAc;AAC1D,YAAI;AACF,aAAG,KAAI,CAAE;iBACF,KAAK;AACZ,cAAI,IAAI,SAAS,IAAI,iBAAiB,SAAS;AAC7C,gBAAI,MAAM,KAAK,WAAA;AAAM,qBAAA,IAAI,IAAI,IAAI;YAAZ,CAAa,EACxB,MAAM,SAAC,MAAW;AAAK,qBAAA,KAAK,IAAI;YAAT,CAAU;qBAClC,IAAI,WAAW,IAAI,QAAQ,YAAY;AAEhD,oBAAQ,SAAS,MAAM,WAAA;AAAM,qBAAA,IAAI,IAAI,IAAI;YAAZ,GAAe,IAAI,OAAO;iBAClD;AACL,iBAAK,GAAG;;;MAGd,CAAC;IACH;AAhBA,YAAA,mBAAA;AA4BA,aAAgB,WAAW,SAAqB;AAC9C,UAAI,MAAM,IAAI,MAAM,eAAe;AACnC,UAAI,QAAQ;AACZ,YAAM;IACR;AAJA,YAAA,aAAA;;;;;;;;;AC1EA,QAAA,eAAA;AACA,QAAA,mBAAA;AACA,QAAA,eAAA;AAQa,YAAA,UAAU;MAIrB,SAAS,aAAA;MAKT,UAAU,IAAI,iBAAA,YAAW;MASzB,UAAU,SAAU,UAAe,SAAmB;AACpD,eAAO,QAAA,QAAQ,SAAS,SAAS,UAAU,OAAO;MACpD;MAKA,kBAAkB,aAAA;MAClB,YAAY,aAAA;MAKZ,WAAW;;;;", "names": ["HandlerList"]}