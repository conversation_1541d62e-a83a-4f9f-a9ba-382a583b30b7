{"version": 3, "file": "MathItem.js", "sourceRoot": "", "sources": ["../../ts/core/MathItem.ts"], "names": [], "mappings": ";;;AA0OA,SAAgB,SAAS,CAAO,IAAY,EAAE,IAAY,EAAE,KAAa,EAAE,CAAS,EACpD,KAAa,EAAE,GAAW,EAAE,OAAuB;IAAvB,wBAAA,EAAA,cAAuB;IACjF,IAAI,IAAI,GAAoB,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;QACpC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAC,CAAC,EAAE,KAAK,EAAC,EAAE,GAAG,EAAE,EAAC,CAAC,EAAE,GAAG,EAAC,EAAE,OAAO,EAAE,OAAO,EAAC,CAAC;IACvF,OAAO,IAAI,CAAC;AACd,CAAC;AALD,8BAKC;AAUD;IAuEE,0BAAa,IAAY,EAAE,GAAsB,EAAE,OAAuB,EAC7D,KAA+C,EAC/C,GAA6C;QAFP,wBAAA,EAAA,cAAuB;QAC7D,sBAAA,EAAA,UAAyB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC;QAC/C,oBAAA,EAAA,QAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC;QA3CnD,SAAI,GAAY,IAAI,CAAC;QAIrB,gBAAW,GAAM,IAAI,CAAC;QAKtB,YAAO,GAAY,EAAa,CAAC;QAKjC,cAAS,GAAe,EAAE,CAAC;QAK3B,eAAU,GAAe,EAAE,CAAC;QAKzB,WAAM,GAAW,aAAK,CAAC,WAAW,CAAC;QAoB3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,EAAa,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAzBD,sBAAW,uCAAS;aAApB;YACE,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;QAC/B,CAAC;;;OAAA;IA4BM,iCAAM,GAAb,UAAc,QAA+B;QAC3C,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC;IAKM,mCAAQ,GAAf,UAAgB,QAA+B,EAAE,KAA8B;QAA9B,sBAAA,EAAA,QAAgB,aAAK,CAAC,QAAQ;QAC7E,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SACvB;QACD,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAKM,kCAAO,GAAd,UAAe,QAA+B,EAAE,GAAwB;QAAxB,oBAAA,EAAA,MAAc,aAAK,CAAC,IAAI;QACtE,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IAKM,kCAAO,GAAd,UAAe,QAA+B;QAC5C,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,aAAK,CAAC,QAAQ,EAAE;YACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAC;SAC5B;IACH,CAAC;IAKM,kCAAO,GAAd,UAAe,QAA+B;QAC5C,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,aAAK,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9F,IAAI,CAAC,KAAK,CAAC,aAAK,CAAC,OAAO,CAAC,CAAC;SAC3B;IACH,CAAC;IAKM,yCAAc,GAArB,UAAsB,SAAgC,IAAG,CAAC;IAKnD,6CAAkB,GAAzB,UAA0B,QAAyB;QAAzB,yBAAA,EAAA,gBAAyB;IAAG,CAAC;IAKhD,qCAAU,GAAjB,UAAkB,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,MAAc,EAAE,KAAa;QACrF,IAAI,CAAC,OAAO,GAAG;YACb,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YACd,cAAc,EAAE,MAAM;YACtB,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,KAAK;SACb,CAAC;IACJ,CAAC;IAKM,gCAAK,GAAZ,UAAa,KAAoB,EAAE,OAAwB;QAA9C,sBAAA,EAAA,YAAoB;QAAE,wBAAA,EAAA,eAAwB;QACzD,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,IAAI,KAAK,GAAG,aAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,aAAK,CAAC,QAAQ,EAAE;gBAC3D,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;aAClC;YACD,IAAI,KAAK,GAAG,aAAK,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,aAAK,CAAC,OAAO,EAAE;gBACzD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;aACtB;YACD,IAAI,KAAK,GAAG,aAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,aAAK,CAAC,QAAQ,EAAE;gBAC3D,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;aACrB;YACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACrB;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAKM,gCAAK,GAAZ,UAAa,OAAwB;QAAxB,wBAAA,EAAA,eAAwB;QACnC,IAAI,CAAC,KAAK,CAAC,aAAK,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC;IAEH,uBAAC;AAAD,CAAC,AAlLD,IAkLC;AAlLqB,4CAAgB;AAyLzB,QAAA,KAAK,GAA8B;IAC9C,WAAW,EAAE,CAAC;IACd,QAAQ,EAAE,EAAE;IACZ,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,IAAI,EAAE,KAAK;CACZ,CAAC;AAQF,SAAgB,QAAQ,CAAC,IAAY,EAAE,KAAa;IAClD,IAAI,IAAI,IAAI,aAAK,EAAE;QACjB,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,iBAAiB,CAAC,CAAC;KAClD;IACD,aAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACtB,CAAC;AALD,4BAKC"}