{"version": 3, "file": "complexity.js", "sourceRoot": "", "sources": ["../../ts/a11y/complexity.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,mDAAoD;AAGpD,2DAA2F;AAC3F,sDAA0D;AAC1D,iDAAiF;AAoBjF,IAAA,sBAAQ,EAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AA+B3B,SAAgB,uBAAuB,CACrB,YAAe,EAAE,iBAA0C;IAE3E;QAAqB,2BAAY;QAA1B;;QAeP,CAAC;QATQ,4BAAU,GAAjB,UAAkB,QAAyC,EAAE,KAAsB;YAAtB,sBAAA,EAAA,aAAsB;YACjF,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,UAAU;gBAAE,OAAO;YAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,EAAE;gBACnE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC5B,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;QAEH,cAAC;IAAD,CAAC,AAfM,CAAc,YAAY,GAe/B;AAEJ,CAAC;AApBD,0DAoBC;AA+BD,SAAgB,2BAA2B,CAC1B,YAAe;;IAE9B;YAAqB,2BAAY;YA2B/B;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAYd;gBAXC,IAAM,WAAW,GAAI,KAAI,CAAC,WAAmC,CAAC,WAAW,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;oBAClC,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;iBACpC;gBACD,IAAM,cAAc,GAAG,IAAA,kCAAqB,EAAC,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACnG,KAAI,CAAC,iBAAiB,GAAG,IAAI,KAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBAC7F,IAAM,iBAAiB,GAAG,CAAC,UAAC,IAAa,IAAK,OAAA,KAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAtC,CAAsC,CAAC,CAAC;gBACtF,KAAI,CAAC,OAAO,CAAC,QAAQ;oBACnB,uBAAuB,CACrB,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CACzC,CAAC;;YACN,CAAC;YAKM,4BAAU,GAAjB;;gBACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;oBACvC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;;4BACjC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;gCAAzB,IAAM,IAAI,WAAA;gCACZ,IAAoC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;6BACxD;;;;;;;;;qBACF;oBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;iBAClC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAKM,uBAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;gBAAxB,wBAAA,EAAA,eAAwB;gBAClD,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,GAAG,mBAAK,CAAC,UAAU,EAAE;oBAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;iBACpC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAEH,cAAC;QAAD,CAAC,AApEM,CAAc,YAAY;QAKjB,UAAO,kCAChB,YAAY,CAAC,OAAO,GACpB,8BAAiB,CAAC,OAAO,KAC5B,gBAAgB,EAAE,IAAI,EACtB,iBAAiB,EAAE,8BAAiB,EACpC,aAAa,EAAE,IAAA,uBAAU,wBACpB,YAAY,CAAC,OAAO,CAAC,aAAa,KACrC,UAAU,EAAE,CAAC,mBAAK,CAAC,UAAU,CAAC,IAC9B,GACF;WAsDF;AAEJ,CAAC;AAzED,kEAyEC;AAeD,SAAgB,iBAAiB,CAC/B,OAAyB,EACzB,MAA8B;IAA9B,uBAAA,EAAA,aAA8B;IAE9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM,EAAE;QACrD,OAAO,GAAG,IAAA,kCAAa,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAC1C;IACD,OAAO,CAAC,aAAa,GAAG,2BAA2B,CAA2B,OAAO,CAAC,aAAoB,CAAC,CAAC;IAC5G,OAAO,OAAO,CAAC;AACjB,CAAC;AATD,8CASC"}