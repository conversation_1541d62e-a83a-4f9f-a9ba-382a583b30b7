{"version": 3, "file": "mfenced.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mfenced.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAA0F;AAO1F;IAAgC,8BAAe;IAA/C;QAAA,qEAqIC;QAtHW,cAAQ,GAAG,qBAAQ,CAAC,KAAK,CAAC;QAK7B,gBAAU,GAAc,EAAE,CAAC;QAI3B,UAAI,GAAY,IAAI,CAAC;QAIrB,WAAK,GAAY,IAAI,CAAC;;IAyG/B,CAAC;IApGC,sBAAW,4BAAI;aAAf;YACE,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAQM,gCAAW,GAAlB,UAAmB,IAAa;QAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACpC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC7C;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC1B,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aACjD;YACD,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACtB,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAC7C;SACF;QACD,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAQS,gDAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;;QAC9G,IAAI,CAAC,YAAY,EAAE,CAAC;;YACpB,KAAoB,IAAA,KAAA,SAAA,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAhE,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,EAAE;oBACT,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBACjE;aACF;;;;;;;;;QACD,iBAAM,2BAA2B,YAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAKS,iCAAY,GAAtB;;QACM,IAAA,KAA4B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CACpC,EAD5C,IAAI,UAAA,EAAE,KAAK,WAAA,EAAE,UAAU,gBACqB,CAAC;QAClD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACtC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACxC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAIlD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC,EAAE,qBAAQ,CAAC,IAAI,CAAC,CAAC;SAC/E;QAID,IAAI,UAAU,EAAE;YACd,OAAO,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrD,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACxD;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;;gBACV,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,KAAK,WAAA;oBACd,IAAI,KAAK,EAAE;wBACT,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;qBAC7D;iBACF;;;;;;;;;SACF;QAID,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAC,EAAE,qBAAQ,CAAC,KAAK,CAAC,CAAC;SACnF;IACH,CAAC;IAQS,6BAAQ,GAAlB,UAAmB,CAAS,EAAE,UAA6B,EAAE,QAAuB;QAAtD,2BAAA,EAAA,eAA6B;QAAE,yBAAA,EAAA,eAAuB;QAClF,IAAI,IAAI,GAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IA9Ha,mBAAQ,yBACjB,4BAAe,CAAC,QAAQ,KAC3B,IAAI,EAAE,GAAG,EACT,KAAK,EAAE,GAAG,EACV,UAAU,EAAE,GAAG,IACf;IA2HJ,iBAAC;CAAA,AArID,CAAgC,4BAAe,GAqI9C;AArIY,gCAAU"}