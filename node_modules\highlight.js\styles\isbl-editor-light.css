pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

ISBL Editor style light color schemec (c) <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

*/
.hljs {
  background: white;
  color: black
}
/* Base color: saturation 0; */
.hljs-subst {
  color: black
}
.hljs-comment {
  color: #555555;
  font-style: italic
}
.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta .hljs-keyword,
.hljs-doctag,
.hljs-name {
  color: #000000;
  font-weight: bold
}
/* User color: hue: 0 */
.hljs-string {
  color: #000080
}
.hljs-type,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #000000
}
.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #5e1700
}
/* Language color: hue: 90; */
.hljs-built_in,
.hljs-literal {
  color: #000080;
  font-weight: bold
}
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #397300
}
.hljs-class {
  color: #6f1C00;
  font-weight: bold
}
.hljs-title,
.hljs-section {
  color: #fb2c00
}
.hljs-title>.hljs-built_in {
  color: #008080;
  font-weight: normal
}
/* Meta color: hue: 200 */
.hljs-meta {
  color: #1f7199
}
.hljs-meta .hljs-string {
  color: #4d99bf
}
/* Misc effects */
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}