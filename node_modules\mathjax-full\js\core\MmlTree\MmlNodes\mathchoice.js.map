{"version": 3, "file": "mathchoice.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mathchoice.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAiE;AAUjE;IAAgC,8BAAmB;IAAnD;;IA6CA,CAAC;IAjCC,sBAAW,4BAAI;aAAf;YACE,OAAO,YAAY,CAAC;QACtB,CAAC;;;OAAA;IAMD,sBAAW,6BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAW,iCAAS;aAApB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAQM,2CAAsB,GAA7B,UAA8B,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QACtG,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAtCa,mBAAQ,gBACjB,gCAAmB,CAAC,QAAQ,EAC/B;IAsCJ,iBAAC;CAAA,AA7CD,CAAgC,gCAAmB,GA6ClD;AA7CY,gCAAU"}