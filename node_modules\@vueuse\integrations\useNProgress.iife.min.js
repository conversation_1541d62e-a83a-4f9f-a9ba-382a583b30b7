(function(a,l,e,c){"use strict";function f(s=null,i){const t=l.toRef(s),n=c.computed({set:u=>u?e.start():e.done(),get:()=>typeof t.value=="number"&&t.value<1});i&&e.configure(i);const o=e.set;return e.set=u=>(t.value=u,o.call(e,u)),c.watchEffect(()=>{typeof t.value=="number"&&l.isClient&&o.call(e,t.value)}),l.tryOnScopeDispose(e.remove),{isLoading:n,progress:t,start:e.start,done:e.done,remove:()=>{t.value=null,e.remove()}}}a.useNProgress=f})(this.VueUse=this.VueUse||{},VueUse,nprogress,Vue);
