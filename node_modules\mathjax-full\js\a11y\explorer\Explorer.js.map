{"version": 3, "file": "Explorer.js", "sourceRoot": "", "sources": ["../../../ts/a11y/explorer/Explorer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,qDAA4B;AAyE5B;IAqEE,0BACS,QAAsB,EACnB,MAAiB,EACjB,IAAiB;QAAE,eAAe;aAAf,UAAe,EAAf,qBAAe,EAAf,IAAe;YAAf,8BAAe;;QAFrC,aAAQ,GAAR,QAAQ,CAAc;QACnB,WAAM,GAAN,MAAM,CAAW;QACjB,SAAI,GAAJ,IAAI,CAAa;QAnEtB,cAAS,GAAY,IAAI,CAAC;QAMvB,WAAM,GAAmC,EAAE,CAAC;QAM5C,gBAAW,GAAoB,IAAI,CAAC,cAAc,EAAE,CAAC;QAMvD,YAAO,GAAY,KAAK,CAAC;IAmDjC,CAAC;IA7CgB,0BAAS,GAA1B,UAA2B,KAAY;QACrC,IAAI,KAAK,CAAC,cAAc,EAAE;YACxB,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;aAAM;YACL,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;SAC3B;QACD,IAAI,KAAK,CAAC,wBAAwB,EAAE;YAClC,KAAK,CAAC,wBAAwB,EAAE,CAAC;SAClC;aAAM,IAAI,KAAK,CAAC,eAAe,EAAE;YAChC,KAAK,CAAC,eAAe,EAAE,CAAC;SACzB;QACD,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;IAC5B,CAAC;IAYa,uBAAM,GAApB,UACE,QAAsB,EACtB,MAAiB,EACjB,IAAiB;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAEjC,IAAI,QAAQ,QAAO,IAAI,YAAJ,IAAI,yBAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,UAAK,IAAI,aAAC,CAAC;QACzD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAqBS,iCAAM,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAMD,sBAAW,oCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;aAKD,UAAkB,IAAa;YAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;;;OAPA;IAYM,iCAAM,GAAb;QACE,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAKM,iCAAM,GAAb;QACE,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAKM,gCAAK,GAAZ;QACE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAKM,+BAAI,GAAX;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACrB;IACH,CAAC;IAKM,oCAAS,GAAhB;;;YACE,KAAoC,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAAxC,IAAA,KAAA,mBAAsB,EAArB,SAAS,QAAA,EAAE,SAAS,QAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aAClD;;;;;;;;;IACH,CAAC;IAKM,uCAAY,GAAnB;;;YACE,KAAoC,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAAxC,IAAA,KAAA,mBAAsB,EAArB,SAAS,QAAA,EAAE,SAAS,QAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACrD;;;;;;;;;IACH,CAAC;IAMM,iCAAM,GAAb,UAAc,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;IAAS,CAAC;IAMpC,yCAAc,GAAxB;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACtC,IAAI,UAAU,GAAG,EAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;YACzC,KAAK,EAAE,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAC,CAAC;QACvD,IAAI,UAAU,GAAG,EAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;YACzC,KAAK,EAAE,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAC,CAAC;QACvD,OAAO,gBAAG,CAAC,cAAc,CACvB,UAAU,EAAE,UAAU,EACtB,EAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;IAC7D,CAAC;IAMS,oCAAS,GAAnB,UAAoB,KAAY;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACnC;IACH,CAAC;IAEH,uBAAC;AAAD,CAAC,AAtLD,IAsLC;AAtLY,4CAAgB"}