{"version": 3, "file": "mfrac.js", "sourceRoot": "", "sources": ["../../../../ts/output/svg/Wrappers/mfrac.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAAyD;AACzD,2DAAgE;AAChE,oEAAiE;AAWjE;IAAuC,4BAA2D;IAAlG;;IAiHA,CAAC;IAhGQ,wBAAK,GAAZ,UAAa,MAAS;QACpB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACvB,IAAA,KAA4B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,EAApF,aAAa,mBAAA,EAAE,QAAQ,cAA6D,CAAC;QAC5F,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC5B;aAAM;YACL,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;YAC7D,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aACxB;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aACvC;SACF;IACH,CAAC;IAQS,+BAAY,GAAtB,UAAuB,OAAgB,EAAE,CAAS;QAChD,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACnB,IAAA,KAAyB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAA9E,QAAQ,cAAA,EAAE,UAAU,gBAA0D,CAAC;QAChF,IAAA,KAAA,OAAa,IAAI,CAAC,UAAU,IAAA,EAA3B,GAAG,QAAA,EAAE,GAAG,QAAmB,CAAC;QACnC,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;QAChC,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;QAEhC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAM,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC;QAC1B,IAAM,CAAC,GAAG,EAAE,CAAC;QACb,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC/E,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EACxC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,QAAkB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;QACjE,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,UAAoB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;QAC7D,IAAA,KAAY,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAlC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAA2B,CAAC;QAE1C,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACf,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACf,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACxC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC7C,CAAC,CAAC,CAAC;IACN,CAAC;IAOS,2BAAQ,GAAlB,UAAmB,OAAgB;QACjC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACnB,IAAA,KAAyB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAA9E,QAAQ,cAAA,EAAE,UAAU,gBAA0D,CAAC;QAChF,IAAA,KAAA,OAAa,IAAI,CAAC,UAAU,IAAA,EAA3B,GAAG,QAAA,EAAE,GAAG,QAAmB,CAAC;QACnC,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;QAChC,IAAM,IAAI,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;QAEhC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC/E,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EACxC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,QAAkB,CAAC,GAAG,GAAG,CAAC;QAC7D,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,UAAoB,CAAC,GAAG,GAAG,CAAC;QACzD,IAAA,KAAS,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAA5B,CAAC,OAAA,EAAE,CAAC,OAAwB,CAAC;QAEpC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACf,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACjB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACf,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAOS,+BAAY,GAAtB,UAAuB,OAAgB;QACrC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACnB,IAAA,KAAA,OAAa,IAAI,CAAC,UAAU,IAAA,EAA3B,GAAG,QAAA,EAAE,GAAG,QAAmB,CAAC;QAC7B,IAAA,KAA4B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAArD,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAA8B,CAAC;QAC7D,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAEnD,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEf,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;IAC/E,CAAC;IA1Ga,aAAI,GAAG,mBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;IA4G/C,eAAC;CAAA,AAjHD,CAAuC,IAAA,2BAAgB,EAAgC,uBAAU,CAAC,GAiHjG;AAjHY,4BAAQ"}