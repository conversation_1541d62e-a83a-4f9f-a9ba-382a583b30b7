{"version": 3, "file": "CancelConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/cancel/CancelConfiguration.ts"], "names": [], "mappings": ";;;;;;AAwBA,wDAAkD;AAElD,sDAA+C;AAC/C,gDAA2C;AAE3C,iEAAwC;AACxC,8EAAmE;AAIxD,QAAA,aAAa,GAAgC,EAAE,CAAC;AAS3D,qBAAa,CAAC,MAAM,GAAG,UAAS,MAAiB,EAAE,IAAY,EAAE,QAAgB;IAC/E,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,sBAAS,CAAC,aAAa,CAAC,IAAI,EAAE,yCAAe,CAAC,CAAC;IAC3D,GAAG,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;IAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AASF,qBAAa,CAAC,QAAQ,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC/D,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1C,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,sBAAS,CAAC,aAAa,CAAC,IAAI,EAAE,yCAAe,CAAC,CAAC;IAC3D,GAAG,CAAE,UAAU,CAAC,GAAG,CAAC,6BAAW,CAAC,QAAQ,CAAC,gBAAgB;QACrC,6BAAW,CAAC,QAAQ,CAAC,eAAe;QACpC,6BAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAC1B,EAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAC,CAAC,CAAC;IAC1E,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EACd,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC;AAGF,IAAI,yBAAU,CAAC,QAAQ,EAAE;IACvB,MAAM,EAAI,CAAC,QAAQ,EAAE,6BAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC3D,OAAO,EAAG,CAAC,QAAQ,EAAE,6BAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC;IAC7D,OAAO,EAAG,CAAC,QAAQ,EAAE,6BAAW,CAAC,QAAQ,CAAC,gBAAgB,GAAG,GAAG;YACrD,6BAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC;IACnD,QAAQ,EAAE,UAAU;CACrB,EAAE,qBAAa,CAAC,CAAC;AAGL,QAAA,mBAAmB,GAAG,gCAAa,CAAC,MAAM,CACrD,QAAQ,EAAE,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAC,EAAC,CACzC,CAAC"}