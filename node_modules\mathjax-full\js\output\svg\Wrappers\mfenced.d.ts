import { <PERSON><PERSON><PERSON>rapper, SVGConstructor } from '../Wrapper.js';
import { SVGinferredMrow } from './mrow.js';
declare const SVGmfenced_base: import("../../common/Wrappers/mfenced.js").MfencedConstructor & SVGConstructor<any, any, any>;
export declare class SVGmfenced<N, T, D> extends SVGmfenced_base {
    static kind: string;
    mrow: SVGinferredMrow<N, T, D>;
    toSVG(parent: N): void;
    protected setChildrenParent(parent: SVGWrapper<N, T, D>): void;
}
export {};
