import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/cases/CasesConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/cases', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      cases: {
        CasesConfiguration: module1
      }
    }
  }
}});
