/*!
  Theme: darkmoss
  Author: <PERSON> (https://github.com/a<PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#c7c7a5;background:#171e1f}.hljs ::selection,.hljs::selection{background-color:#373c3d;color:#c7c7a5}.hljs-comment{color:#555e5f}.hljs-tag{color:#818f80}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#c7c7a5}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ff4658}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#e6db74}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#fdb11f}.hljs-strong{font-weight:700;color:#fdb11f}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#499180}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#66d9ef}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#498091}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#9bc0c8}.hljs-emphasis{color:#9bc0c8;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#d27b53}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}