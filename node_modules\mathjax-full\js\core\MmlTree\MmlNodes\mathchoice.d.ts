import { PropertyList } from '../../Tree/Node.js';
import { AbstractMmlBaseNode, AttributeList } from '../MmlNode.js';
export declare class MathChoice extends AbstractMmlBaseNode {
    static defaults: PropertyList;
    get kind(): string;
    get arity(): number;
    get notParent(): boolean;
    setInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean): void;
}
