{"version": 3, "file": "mstyle.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mstyle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAmE;AACnE,kDAAyC;AAOzC;IAA+B,6BAAqB;IAApD;;IA4DA,CAAC;IAxCC,sBAAW,2BAAI;aAAf;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAKD,sBAAW,gCAAS;aAApB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1E,CAAC;;;OAAA;IAOS,+CAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,WAAW,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBACjC,KAAK,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;aAChC;iBAAM;gBACL,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;aAC/B;YACD,KAAK,GAAG,KAAK,CAAC;SACf;QACD,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAY,CAAC;QAC1E,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,OAAO,GAAG,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC;YAClC,KAAK,GAAG,KAAK,CAAC;SACf;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAY,CAAC;QACvE,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,KAAK,GAAG,OAAO,CAAC;SACjB;QACD,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACzF,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/E,CAAC;IArDa,kBAAQ,yBACjB,kCAAqB,CAAC,QAAQ,KACjC,WAAW,EAAE,uBAAO,EACpB,YAAY,EAAE,uBAAO,EACrB,oBAAoB,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EACtC,aAAa,EAAE,KAAK,EACpB,cAAc,EAAE,uBAAO,EACvB,SAAS,EAAE,uBAAO,EAClB,GAAG,EAAE,uBAAO,EACZ,mBAAmB,EAAE,QAAQ,IAC7B;IA6CJ,gBAAC;CAAA,AA5DD,CAA+B,kCAAqB,GA4DnD;AA5DY,8BAAS"}