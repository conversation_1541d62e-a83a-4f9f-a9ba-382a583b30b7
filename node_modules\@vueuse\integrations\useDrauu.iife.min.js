(function(E,o,k,H,a){"use strict";function V(p,u){const n=a.ref();let r=[];const s=o.createEventHook(),c=o.createEventHook(),d=o.createEventHook(),v=o.createEventHook(),i=o.createEventHook(),f=a.shallowRef(!1),h=a.shallowRef(!1),w=a.shallowRef(!1),S=a.shallowRef(!1),l=a.ref({color:"black",size:3,arrowEnd:!1,cornerRadius:0,dasharray:void 0,fill:"transparent",mode:"draw",...u?.brush});a.watch(l,()=>{const e=n.value;e&&(e.brush=l.value,e.mode=l.value.mode)},{deep:!0});const R=()=>{var e;return(e=n.value)==null?void 0:e.undo()},_=()=>{var e;return(e=n.value)==null?void 0:e.redo()},C=()=>{var e;return(e=n.value)==null?void 0:e.clear()},U=()=>{var e;return(e=n.value)==null?void 0:e.cancel()},b=e=>{var t;return(t=n.value)==null?void 0:t.load(e)},D=()=>{var e;return(e=n.value)==null?void 0:e.dump()},m=()=>{var e;r.forEach(t=>t()),(e=n.value)==null||e.unmount()},g=()=>{n.value&&(f.value=n.value.canUndo(),h.value=n.value.canRedo(),w.value=n.value.altPressed,S.value=n.value.shiftPressed)};return a.watch(()=>o.unrefElement(p),e=>{!e||typeof SVGSVGElement>"u"||!(e instanceof SVGSVGElement)||(n.value&&m(),n.value=H.createDrauu({el:e,...u}),g(),r=[n.value.on("canceled",()=>c.trigger()),n.value.on("committed",t=>d.trigger(t)),n.value.on("start",()=>v.trigger()),n.value.on("end",()=>i.trigger()),n.value.on("changed",()=>{g(),s.trigger()})])},{flush:"post"}),k.tryOnScopeDispose(()=>m()),{drauuInstance:n,load:b,dump:D,clear:C,cancel:U,undo:R,redo:_,canUndo:f,canRedo:h,brush:l,onChanged:s.on,onCommitted:d.on,onStart:v.on,onEnd:i.on,onCanceled:c.on}}E.useDrauu=V})(this.VueUse=this.VueUse||{},VueUse,VueUse,Drauu,Vue);
