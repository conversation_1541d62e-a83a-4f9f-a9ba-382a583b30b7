(function(o,f,r){"use strict";function i(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(n){if(n!=="default"){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}}),t.default=e,Object.freeze(t)}var l=i(f);const u=Object.entries(l).filter(([e,t])=>typeof t=="function"&&e.endsWith("Case")).reduce((e,[t,n])=>(e[t]=n,e),{});function h(e,t,n){const a=r.computed(()=>{const c=r.toValue(t);if(!u[c])throw new Error(`Invalid change case type "${c}"`);return c});if(typeof e=="function")return r.computed(()=>u[a.value](r.toValue(e),r.toValue(n)));const s=r.ref(e);return r.computed({get(){return u[a.value](s.value,r.toValue(n))},set(c){s.value=c}})}o.useChangeCase=h})(this.VueUse=this.VueUse||{},changeCase,Vue);
