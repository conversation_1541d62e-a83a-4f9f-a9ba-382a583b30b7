{"version": 3, "file": "MathtoolsConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/mathtools/MathtoolsConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,wDAAkD;AAClD,gDAA2C;AAC3C,+DAAsC;AACtC,uDAAoD;AAKpD,kCAAgC;AAChC,uDAAiD;AACjD,uDAAsD;AACtD,yDAAkD;AAKrC,QAAA,YAAY,GAAG,yBAAyB,CAAC;AAMtD,SAAS,aAAa,CAAC,MAA2B;IAChD,IAAI,yBAAU,CAAC,oBAAY,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACrC,MAAM,CAAC,MAAM,CAAC,gCAAa,CAAC,KAAK,CAAC,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,oBAAY,CAAC,EAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;AACvF,CAAC;AAOD,SAAS,eAAe,CAAC,MAA2B,EAAE,GAAuB;;IAC3E,IAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC;IAChC,IAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC;;QAC/D,KAAiB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA,gBAAA,4BAAE;YAAvC,IAAM,EAAE,WAAA;YACX,gCAAa,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;SAC7D;;;;;;;;;IACD,IAAA,qCAAkB,EAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAMD,SAAgB,aAAa,CAAC,EAA4B;;QAA3B,IAAI,UAAA;;QACjC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA,gBAAA,4BAAE;YAA7C,IAAM,IAAI,WAAA;YACb,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;gBAAE,SAAS;YAChD,IAAM,UAAU,GAAG,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,CAAC;;gBACV,KAAgB,IAAA,oBAAA,SAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAA,gBAAA,4BAAE;oBAAnB,IAAM,CAAC,WAAA;oBACV,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;wBAClB,qBAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;wBACpE,CAAC,EAAE,CAAC;qBACL;iBACF;;;;;;;;;;gBACD,KAAgB,IAAA,oBAAA,SAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAA,gBAAA,4BAAE;oBAAnB,IAAM,CAAC,WAAA;oBACV,IAAI,qBAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,qBAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC9F,qBAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;qBACrE;iBACF;;;;;;;;;YACD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACX,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;SACF;;;;;;;;;AACH,CAAC;AApBD,sCAoBC;AAKY,QAAA,sBAAsB,GAAG,gCAAa,CAAC,MAAM,CACxD,WAAW,EAAE;IACX,OAAO,EAAE;QACP,KAAK,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;QACnD,WAAW,EAAE,CAAC,wBAAwB,CAAC;QACvC,SAAS,EAAE,CAAC,sBAAsB,CAAC;QACnC,SAAS,EAAE,CAAC,sBAAsB,CAAC;KACpC;IACD,KAAK;QACH,GAAC,iCAAa,CAAC,SAAS,CAAC,IAAI,IAAG,iCAAa;WAC9C;IACD,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE,eAAe;IACvB,cAAc,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,OAAO,EAAE;QACP,SAAS,EAAE;YACT,aAAa,EAAE,KAAK;YACpB,eAAe,EAAE,GAAG;YACpB,qBAAqB,EAAE,EAAE;YACzB,kBAAkB,EAAE,EAAE;YACtB,mBAAmB,EAAE,GAAG;YACxB,uBAAuB,EAAE,MAAM;YAC/B,uBAAuB,EAAE,MAAM;YAC/B,aAAa,EAAE,KAAK;YACpB,oBAAoB,EAAE,OAAO;YAC7B,cAAc,EAAE,QAAQ;YACxB,cAAc,EAAE,QAAQ;YACxB,aAAa,EAAE,KAAK;YAEpB,sBAAsB,EAAE,EAAE;YAC1B,sBAAsB,EAAE,EAAE;YAC1B,sBAAsB,EAAE,EAAE;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,gBAAgB,EAAE,IAAA,uBAAU,EAAC,EAAE,CAAC;YAEhC,QAAQ,EAAE,IAAA,uBAAU,EAAC,EAAE,CAAC;SAExB;KACH;CACF,CACF,CAAC"}