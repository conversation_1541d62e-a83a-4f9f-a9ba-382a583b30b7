{"version": 3, "file": "Tags.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/Tags.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,gEAAuC;AAWvC;IAOE,eAAmB,GAAmB,EAAS,EAAe;QAA3C,oBAAA,EAAA,WAAmB;QAAS,mBAAA,EAAA,OAAe;QAA3C,QAAG,GAAH,GAAG,CAAgB;QAAS,OAAE,GAAF,EAAE,CAAa;IAAG,CAAC;IACpE,YAAC;AAAD,CAAC,AARD,IAQC;AARY,sBAAK;AAclB;IAgBE,iBAAqB,GAAgB,EAChB,QAAyB,EACzB,WAA4B,EAC9B,GAAkB,EAClB,KAAkB,EAClB,SAAsB,EACtB,KAAsB,EACtB,OAAoB;QAPlB,oBAAA,EAAA,QAAgB;QAChB,yBAAA,EAAA,gBAAyB;QACzB,4BAAA,EAAA,mBAA4B;QAC9B,oBAAA,EAAA,UAAkB;QAClB,sBAAA,EAAA,UAAkB;QAClB,0BAAA,EAAA,cAAsB;QACtB,sBAAA,EAAA,aAAsB;QACtB,wBAAA,EAAA,YAAoB;QAPlB,QAAG,GAAH,GAAG,CAAa;QAChB,aAAQ,GAAR,QAAQ,CAAiB;QACzB,gBAAW,GAAX,WAAW,CAAiB;QAC9B,QAAG,GAAH,GAAG,CAAe;QAClB,UAAK,GAAL,KAAK,CAAa;QAClB,cAAS,GAAT,SAAS,CAAa;QACtB,UAAK,GAAL,KAAK,CAAiB;QACtB,YAAO,GAAP,OAAO,CAAa;IAAG,CAAC;IAE7C,cAAC;AAAD,CAAC,AAzBD,IAyBC;AAzBY,0BAAO;AA6LpB;IAAA;QAMY,YAAO,GAAW,CAAC,CAAC;QAMpB,eAAU,GAAW,CAAC,CAAC;QAK1B,kBAAa,GAAiB,IAAI,CAAC;QAKnC,QAAG,GAA6B,EAAE,CAAC;QAKnC,WAAM,GAA6B,EAAE,CAAC;QAKtC,WAAM,GAA2B,EAAE,CAAC;QAKpC,cAAS,GAA2B,EAAE,CAAC;QAKvC,SAAI,GAAY,KAAK,CAAC;QAKtB,cAAS,GAAY,KAAK,CAAC;QAK3B,eAAU,GAAY,IAAI,OAAO,EAAE,CAAC;QAQjC,YAAO,GAAc,EAAE,CAAC;QAE1B,UAAK,GAAc,EAAE,CAAC;QA2MvB,UAAK,GAAG,UAAS,IAAa,EAAE,GAAY;YACjD,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;YACxC,IAAI,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5C,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YACvD,IAAI,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE;gBAC7C,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC;gBAC3C,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC;gBACxD,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;IA2BJ,CAAC;IA3OQ,4BAAK,GAAZ,UAAa,GAAW,EAAE,QAAiB,EAAE,WAAoB;QAC/D,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED,sBAAW,6BAAG;aAAd;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC7B,CAAC;;;OAAA;IAMM,0BAAG,GAAV;QACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IACrC,CAAC;IAMM,0BAAG,GAAV,UAAW,GAAW,EAAE,QAAiB;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;IAChC,CAAC;IAMM,4BAAK,GAAZ;QACE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,sBAAc,+BAAK;aAAnB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAC/B,CAAC;;;OAAA;IAED,sBAAW,+BAAK;aAIhB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACjC,CAAC;aAND,UAAiB,KAAa;YAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;QAClC,CAAC;;;OAAA;IASM,gCAAS,GAAhB,UAAiB,EAAU,EAAE,IAAY;QACvC,OAAO,IAAI,GAAG,GAAG,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;IAKM,gCAAS,GAAhB,UAAiB,GAAW;QAC1B,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACzB,CAAC;IAOS,+BAAQ,GAAlB,UAAmB,EAAU;QAC3B,OAAO,UAAU,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAOS,mCAAY,GAAtB,UAAuB,CAAS;QAC9B,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;IACtB,CAAC;IAMM,8BAAO,GAAd;QACE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,IAAI,EAAE;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;SAClD;IACH,CAAC;IAMM,+BAAQ,GAAf;QACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;IAC7B,CAAC;IAMM,6BAAM,GAAb,UAAc,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QAClC,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;SACvB;QACD,IAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QAC3B,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,IAAI,EAAE,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;YACD,IAAI,EAAE,CAAC,GAAG,EAAE;gBACV,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;aACvB;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAMM,+BAAQ,GAAf;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAKM,4BAAK,GAAZ,UAAa,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAKM,oCAAa,GAApB,UAAqB,IAA6B;QAChD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;QAC3C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;SAClC;IACH,CAAC;IAKM,qCAAc,GAArB,UAAsB,IAA6B;QACjD,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;gBACzB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;gBACnB,OAAO,EAAE,IAAI,CAAC,UAAU;aACzB,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;SAChC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAKM,+BAAQ,GAAf,UAAgB,IAAa,EAAE,GAAY;QACzC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG;YACnC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,IAAI,EAAE;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IAqBO,6BAAM,GAAd;QACE,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CACnC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;YACzC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACjE,CAAC;IAMO,8BAAO,GAAf;QACE,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACjF;QACD,IAAI,GAAG,GAAG,IAAI,sBAAS,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,EAAE,EAAE,EAC/C,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EACpB,EAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAC,CAAC,CAAC;IAC5E,CAAC;IAEH,mBAAC;AAAD,CAAC,AA9SD,IA8SC;AA9SY,oCAAY;AAsTzB;IAA4B,0BAAY;IAAxC;;IAcA,CAAC;IATQ,wBAAO,GAAd,cAAkB,CAAC;IAKZ,uBAAM,GAAb;QACE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAM,MAAM,WAAE,CAAC;IACtD,CAAC;IAEH,aAAC;AAAD,CAAC,AAdD,CAA4B,YAAY,GAcvC;AAdY,wBAAM;AAuBnB;IAA6B,2BAAY;IAAzC;;IAcA,CAAC;IATQ,0BAAQ,GAAf,UAAgB,IAAa,EAAE,GAAY;QACzC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CACnC,UAAS,CAAU,IAAI,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9C,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEH,cAAC;AAAD,CAAC,AAdD,CAA6B,YAAY,GAcxC;AAdY,0BAAO;AA0BpB,IAAiB,WAAW,CAkF3B;AAlFD,WAAiB,WAAW;IAE1B,IAAI,WAAW,GAAG,IAAI,GAAG,CAAoB;QAC3C,CAAC,MAAM,EAAE,MAAM,CAAC;QAChB,CAAC,KAAK,EAAE,OAAO,CAAC;KACjB,CAAC,CAAC;IAEH,IAAI,WAAW,GAAG,MAAM,CAAC;IAMd,mBAAO,GAAe;QAE/B,IAAI,EAAE,WAAW;QAGjB,OAAO,EAAE,OAAO;QAEhB,SAAS,EAAE,OAAO;QAKlB,WAAW,EAAE,IAAI;QAEjB,qBAAqB,EAAE,KAAK;KAC7B,CAAC;IAQS,eAAG,GAAG,UAAS,IAAY,EAAE,MAAiB;QACvD,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC;IAOS,mBAAO,GAAG,UAAS,IAAiC;;;YAC7D,KAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,GAAG,WAAA;gBACZ,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aACjC;;;;;;;;;IACH,CAAC,CAAC;IAQS,kBAAM,GAAG,UAAS,IAAY;QACvC,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnE,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACrC;QACD,OAAO,IAAI,MAAM,EAAE,CAAC;IACtB,CAAC,CAAC;IAOS,sBAAU,GAAG,UAAS,IAAY;QAC3C,WAAW,GAAG,IAAI,CAAC;IACrB,CAAC,CAAC;IAMS,sBAAU,GAAG;QACtB,OAAO,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC,CAAC;AAEJ,CAAC,EAlFgB,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAkF3B"}