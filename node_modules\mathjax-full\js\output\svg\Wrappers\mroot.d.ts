import { SVGWrapper, Constructor } from '../Wrapper.js';
import { SVGmsqrt } from './msqrt.js';
import { BBox } from '../../../util/BBox.js';
declare const SVGmroot_base: import("../../common/Wrappers/mroot.js").MrootConstructor & Constructor<SVGmsqrt<any, any, any>>;
export declare class SVGmroot<N, T, D> extends SVGmroot_base {
    static kind: string;
    protected addRoot(ROOT: N, root: SVGWrapper<N, T, D>, sbox: BBox, H: number): void;
}
export {};
