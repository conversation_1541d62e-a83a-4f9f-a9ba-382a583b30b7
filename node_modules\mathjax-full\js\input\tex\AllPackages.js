"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllPackages = void 0;
require("./base/BaseConfiguration.js");
require("./action/ActionConfiguration.js");
require("./ams/AmsConfiguration.js");
require("./amscd/AmsCdConfiguration.js");
require("./bbox/BboxConfiguration.js");
require("./boldsymbol/BoldsymbolConfiguration.js");
require("./braket/BraketConfiguration.js");
require("./bussproofs/BussproofsConfiguration.js");
require("./cancel/CancelConfiguration.js");
require("./cases/CasesConfiguration.js");
require("./centernot/CenternotConfiguration.js");
require("./color/ColorConfiguration.js");
require("./colorv2/ColorV2Configuration.js");
require("./colortbl/ColortblConfiguration.js");
require("./configmacros/ConfigMacrosConfiguration.js");
require("./empheq/EmpheqConfiguration.js");
require("./enclose/EncloseConfiguration.js");
require("./extpfeil/ExtpfeilConfiguration.js");
require("./gensymb/GensymbConfiguration.js");
require("./html/HtmlConfiguration.js");
require("./mathtools/MathtoolsConfiguration.js");
require("./mhchem/MhchemConfiguration.js");
require("./newcommand/NewcommandConfiguration.js");
require("./noerrors/NoErrorsConfiguration.js");
require("./noundefined/NoUndefinedConfiguration.js");
require("./physics/PhysicsConfiguration.js");
require("./setoptions/SetOptionsConfiguration.js");
require("./tagformat/TagFormatConfiguration.js");
require("./textcomp/TextcompConfiguration.js");
require("./textmacros/TextMacrosConfiguration.js");
require("./upgreek/UpgreekConfiguration.js");
require("./unicode/UnicodeConfiguration.js");
require("./verb/VerbConfiguration.js");
if (typeof MathJax !== 'undefined' && MathJax.loader) {
    MathJax.loader.preLoad('[tex]/action', '[tex]/ams', '[tex]/amscd', '[tex]/bbox', '[tex]/boldsymbol', '[tex]/braket', '[tex]/bussproofs', '[tex]/cancel', '[tex]/cases', '[tex]/centernot', '[tex]/color', '[tex]/colorv2', '[tex]/colortbl', '[tex]/empheq', '[tex]/enclose', '[tex]/extpfeil', '[tex]/gensymb', '[tex]/html', '[tex]/mathtools', '[tex]/mhchem', '[tex]/newcommand', '[tex]/noerrors', '[tex]/noundefined', '[tex]/physics', '[tex]/upgreek', '[tex]/unicode', '[tex]/verb', '[tex]/configmacros', '[tex]/tagformat', '[tex]/textcomp', '[tex]/textmacros', '[tex]/setoptions');
}
exports.AllPackages = [
    'base',
    'action',
    'ams',
    'amscd',
    'bbox',
    'boldsymbol',
    'braket',
    'bussproofs',
    'cancel',
    'cases',
    'centernot',
    'color',
    'colortbl',
    'empheq',
    'enclose',
    'extpfeil',
    'gensymb',
    'html',
    'mathtools',
    'mhchem',
    'newcommand',
    'noerrors',
    'noundefined',
    'upgreek',
    'unicode',
    'verb',
    'configmacros',
    'tagformat',
    'textcomp',
    'textmacros'
];
//# sourceMappingURL=AllPackages.js.map