import {
  require_Styles
} from "./chunk-Z7WOSC22.js";
import {
  require_Entities
} from "./chunk-ARGWDDH6.js";
import {
  require_Options
} from "./chunk-FQVVVHII.js";
import "./chunk-EYAM6I3G.js";
import "./chunk-CWT36BWD.js";
import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/mathjax-full/js/core/DOMAdaptor.js
var require_DOMAdaptor = __commonJS({
  "node_modules/mathjax-full/js/core/DOMAdaptor.js"(exports) {
    "use strict";
    var __values = exports && exports.__values || function(o) {
      var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && typeof o.length === "number") return {
        next: function() {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.AbstractDOMAdaptor = void 0;
    var AbstractDOMAdaptor = function() {
      function AbstractDOMAdaptor2(document) {
        if (document === void 0) {
          document = null;
        }
        this.document = document;
      }
      AbstractDOMAdaptor2.prototype.node = function(kind, def, children, ns) {
        var e_1, _a;
        if (def === void 0) {
          def = {};
        }
        if (children === void 0) {
          children = [];
        }
        var node = this.create(kind, ns);
        this.setAttributes(node, def);
        try {
          for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {
            var child = children_1_1.value;
            this.append(node, child);
          }
        } catch (e_1_1) {
          e_1 = { error: e_1_1 };
        } finally {
          try {
            if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);
          } finally {
            if (e_1) throw e_1.error;
          }
        }
        return node;
      };
      AbstractDOMAdaptor2.prototype.setAttributes = function(node, def) {
        var e_2, _a, e_3, _b, e_4, _c;
        if (def.style && typeof def.style !== "string") {
          try {
            for (var _d = __values(Object.keys(def.style)), _e = _d.next(); !_e.done; _e = _d.next()) {
              var key = _e.value;
              this.setStyle(node, key.replace(/-([a-z])/g, function(_m, c) {
                return c.toUpperCase();
              }), def.style[key]);
            }
          } catch (e_2_1) {
            e_2 = { error: e_2_1 };
          } finally {
            try {
              if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            } finally {
              if (e_2) throw e_2.error;
            }
          }
        }
        if (def.properties) {
          try {
            for (var _f = __values(Object.keys(def.properties)), _g = _f.next(); !_g.done; _g = _f.next()) {
              var key = _g.value;
              node[key] = def.properties[key];
            }
          } catch (e_3_1) {
            e_3 = { error: e_3_1 };
          } finally {
            try {
              if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            } finally {
              if (e_3) throw e_3.error;
            }
          }
        }
        try {
          for (var _h = __values(Object.keys(def)), _j = _h.next(); !_j.done; _j = _h.next()) {
            var key = _j.value;
            if ((key !== "style" || typeof def.style === "string") && key !== "properties") {
              this.setAttribute(node, key, def[key]);
            }
          }
        } catch (e_4_1) {
          e_4 = { error: e_4_1 };
        } finally {
          try {
            if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
          } finally {
            if (e_4) throw e_4.error;
          }
        }
      };
      AbstractDOMAdaptor2.prototype.replace = function(nnode, onode) {
        this.insert(nnode, onode);
        this.remove(onode);
        return onode;
      };
      AbstractDOMAdaptor2.prototype.childNode = function(node, i) {
        return this.childNodes(node)[i];
      };
      AbstractDOMAdaptor2.prototype.allClasses = function(node) {
        var classes = this.getAttribute(node, "class");
        return !classes ? [] : classes.replace(/  +/g, " ").replace(/^ /, "").replace(/ $/, "").split(/ /);
      };
      return AbstractDOMAdaptor2;
    }();
    exports.AbstractDOMAdaptor = AbstractDOMAdaptor;
  }
});

// node_modules/mathjax-full/js/adaptors/NodeMixin.js
var require_NodeMixin = __commonJS({
  "node_modules/mathjax-full/js/adaptors/NodeMixin.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.NodeMixin = exports.NodeMixinOptions = void 0;
    var Options_js_1 = require_Options();
    exports.NodeMixinOptions = {
      badCSS: true,
      badSizes: true
    };
    function NodeMixin(Base, options) {
      var _a;
      if (options === void 0) {
        options = {};
      }
      options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, exports.NodeMixinOptions), options);
      return _a = function(_super) {
        __extends(NodeAdaptor, _super);
        function NodeAdaptor() {
          var args = [];
          for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
          }
          var _this = _super.call(this, args[0]) || this;
          var CLASS = _this.constructor;
          _this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), args[1]);
          return _this;
        }
        NodeAdaptor.prototype.fontSize = function(node) {
          return options.badCSS ? this.options.fontSize : _super.prototype.fontSize.call(this, node);
        };
        NodeAdaptor.prototype.fontFamily = function(node) {
          return options.badCSS ? this.options.fontFamily : _super.prototype.fontFamily.call(this, node);
        };
        NodeAdaptor.prototype.nodeSize = function(node, em, local) {
          if (em === void 0) {
            em = 1;
          }
          if (local === void 0) {
            local = null;
          }
          if (!options.badSizes) {
            return _super.prototype.nodeSize.call(this, node, em, local);
          }
          var text = this.textContent(node);
          var non = Array.from(text.replace(NodeAdaptor.cjkPattern, "")).length;
          var CJK = Array.from(text).length - non;
          return [
            CJK * this.options.cjkCharWidth + non * this.options.unknownCharWidth,
            this.options.unknownCharHeight
          ];
        };
        NodeAdaptor.prototype.nodeBBox = function(node) {
          return options.badSizes ? { left: 0, right: 0, top: 0, bottom: 0 } : _super.prototype.nodeBBox.call(this, node);
        };
        return NodeAdaptor;
      }(Base), _a.OPTIONS = __assign(__assign({}, options.badCSS ? {
        fontSize: 16,
        fontFamily: "Times"
      } : {}), options.badSizes ? {
        cjkCharWidth: 1,
        unknownCharWidth: 0.6,
        unknownCharHeight: 0.8
      } : {}), _a.cjkPattern = new RegExp([
        "[",
        "ᄀ-ᅟ",
        "〈〉",
        "⺀-〾",
        "぀-㉇",
        "㉐-䶿",
        "一-꓆",
        "ꥠ-ꥼ",
        "가-힣",
        "豈-﫿",
        "︐-︙",
        "︰-﹫",
        "！-｠￠-￦",
        "𛀀-𛀁",
        "🈀-🉑",
        "𠀀-𿿽",
        "]"
      ].join(""), "gu"), _a;
    }
    exports.NodeMixin = NodeMixin;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/Element.js
var require_Element = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/Element.js"(exports) {
    "use strict";
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    var __read = exports && exports.__read || function(o, n) {
      var m = typeof Symbol === "function" && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = { error };
      } finally {
        try {
          if (r && !r.done && (m = i["return"])) m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    var __spreadArray = exports && exports.__spreadArray || function(to, from, pack) {
      if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i);
          ar[i] = from[i];
        }
      }
      return to.concat(ar || Array.prototype.slice.call(from));
    };
    var __values = exports && exports.__values || function(o) {
      var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && typeof o.length === "number") return {
        next: function() {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteElement = void 0;
    var LiteElement = /* @__PURE__ */ function() {
      function LiteElement2(kind, attributes, children) {
        var e_1, _a;
        if (attributes === void 0) {
          attributes = {};
        }
        if (children === void 0) {
          children = [];
        }
        this.kind = kind;
        this.attributes = __assign({}, attributes);
        this.children = __spreadArray([], __read(children), false);
        try {
          for (var _b = __values(this.children), _c = _b.next(); !_c.done; _c = _b.next()) {
            var child = _c.value;
            child.parent = this;
          }
        } catch (e_1_1) {
          e_1 = { error: e_1_1 };
        } finally {
          try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
          } finally {
            if (e_1) throw e_1.error;
          }
        }
        this.styles = null;
      }
      return LiteElement2;
    }();
    exports.LiteElement = LiteElement;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/Document.js
var require_Document = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/Document.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteDocument = void 0;
    var Element_js_1 = require_Element();
    var LiteDocument = function() {
      function LiteDocument2() {
        this.root = new Element_js_1.LiteElement("html", {}, [
          this.head = new Element_js_1.LiteElement("head"),
          this.body = new Element_js_1.LiteElement("body")
        ]);
        this.type = "";
      }
      Object.defineProperty(LiteDocument2.prototype, "kind", {
        get: function() {
          return "#document";
        },
        enumerable: false,
        configurable: true
      });
      return LiteDocument2;
    }();
    exports.LiteDocument = LiteDocument;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/Text.js
var require_Text = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/Text.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteComment = exports.LiteText = void 0;
    var LiteText = function() {
      function LiteText2(text) {
        if (text === void 0) {
          text = "";
        }
        this.value = text;
      }
      Object.defineProperty(LiteText2.prototype, "kind", {
        get: function() {
          return "#text";
        },
        enumerable: false,
        configurable: true
      });
      return LiteText2;
    }();
    exports.LiteText = LiteText;
    var LiteComment = function(_super) {
      __extends(LiteComment2, _super);
      function LiteComment2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      Object.defineProperty(LiteComment2.prototype, "kind", {
        get: function() {
          return "#comment";
        },
        enumerable: false,
        configurable: true
      });
      return LiteComment2;
    }(LiteText);
    exports.LiteComment = LiteComment;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/List.js
var require_List = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/List.js"(exports) {
    "use strict";
    var __read = exports && exports.__read || function(o, n) {
      var m = typeof Symbol === "function" && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = { error };
      } finally {
        try {
          if (r && !r.done && (m = i["return"])) m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    var __spreadArray = exports && exports.__spreadArray || function(to, from, pack) {
      if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i);
          ar[i] = from[i];
        }
      }
      return to.concat(ar || Array.prototype.slice.call(from));
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteList = void 0;
    var LiteList = function() {
      function LiteList2(children) {
        this.nodes = [];
        this.nodes = __spreadArray([], __read(children), false);
      }
      LiteList2.prototype.append = function(node) {
        this.nodes.push(node);
      };
      LiteList2.prototype[Symbol.iterator] = function() {
        var i = 0;
        return {
          next: function() {
            return i === this.nodes.length ? { value: null, done: true } : { value: this.nodes[i++], done: false };
          }
        };
      };
      return LiteList2;
    }();
    exports.LiteList = LiteList;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/Parser.js
var require_Parser = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/Parser.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __read = exports && exports.__read || function(o, n) {
      var m = typeof Symbol === "function" && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = { error };
      } finally {
        try {
          if (r && !r.done && (m = i["return"])) m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    var __values = exports && exports.__values || function(o) {
      var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && typeof o.length === "number") return {
        next: function() {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteParser = exports.PATTERNS = void 0;
    var Entities = __importStar(require_Entities());
    var Element_js_1 = require_Element();
    var Text_js_1 = require_Text();
    var PATTERNS;
    (function(PATTERNS2) {
      PATTERNS2.TAGNAME = "[a-z][^\\s\\n>]*";
      PATTERNS2.ATTNAME = "[a-z][^\\s\\n>=]*";
      PATTERNS2.VALUE = `(?:'[^']*'|"[^"]*"|[^\\s\\n]+)`;
      PATTERNS2.VALUESPLIT = `(?:'([^']*)'|"([^"]*)"|([^\\s\\n]+))`;
      PATTERNS2.SPACE = "(?:\\s|\\n)+";
      PATTERNS2.OPTIONALSPACE = "(?:\\s|\\n)*";
      PATTERNS2.ATTRIBUTE = PATTERNS2.ATTNAME + "(?:" + PATTERNS2.OPTIONALSPACE + "=" + PATTERNS2.OPTIONALSPACE + PATTERNS2.VALUE + ")?";
      PATTERNS2.ATTRIBUTESPLIT = "(" + PATTERNS2.ATTNAME + ")(?:" + PATTERNS2.OPTIONALSPACE + "=" + PATTERNS2.OPTIONALSPACE + PATTERNS2.VALUESPLIT + ")?";
      PATTERNS2.TAG = "(<(?:" + PATTERNS2.TAGNAME + "(?:" + PATTERNS2.SPACE + PATTERNS2.ATTRIBUTE + ")*" + PATTERNS2.OPTIONALSPACE + "/?|/" + PATTERNS2.TAGNAME + "|!--[^]*?--|![^]*?)(?:>|$))";
      PATTERNS2.tag = new RegExp(PATTERNS2.TAG, "i");
      PATTERNS2.attr = new RegExp(PATTERNS2.ATTRIBUTE, "i");
      PATTERNS2.attrsplit = new RegExp(PATTERNS2.ATTRIBUTESPLIT, "i");
    })(PATTERNS = exports.PATTERNS || (exports.PATTERNS = {}));
    var LiteParser = function() {
      function LiteParser2() {
      }
      LiteParser2.prototype.parseFromString = function(text, _format, adaptor) {
        if (_format === void 0) {
          _format = "text/html";
        }
        if (adaptor === void 0) {
          adaptor = null;
        }
        var root = adaptor.createDocument();
        var node = adaptor.body(root);
        var parts = text.replace(/<\?.*?\?>/g, "").split(PATTERNS.tag);
        while (parts.length) {
          var text_1 = parts.shift();
          var tag = parts.shift();
          if (text_1) {
            this.addText(adaptor, node, text_1);
          }
          if (tag && tag.charAt(tag.length - 1) === ">") {
            if (tag.charAt(1) === "!") {
              this.addComment(adaptor, node, tag);
            } else if (tag.charAt(1) === "/") {
              node = this.closeTag(adaptor, node, tag);
            } else {
              node = this.openTag(adaptor, node, tag, parts);
            }
          }
        }
        this.checkDocument(adaptor, root);
        return root;
      };
      LiteParser2.prototype.addText = function(adaptor, node, text) {
        text = Entities.translate(text);
        return adaptor.append(node, adaptor.text(text));
      };
      LiteParser2.prototype.addComment = function(adaptor, node, comment) {
        return adaptor.append(node, new Text_js_1.LiteComment(comment));
      };
      LiteParser2.prototype.closeTag = function(adaptor, node, tag) {
        var kind = tag.slice(2, tag.length - 1).toLowerCase();
        while (adaptor.parent(node) && adaptor.kind(node) !== kind) {
          node = adaptor.parent(node);
        }
        return adaptor.parent(node);
      };
      LiteParser2.prototype.openTag = function(adaptor, node, tag, parts) {
        var PCDATA = this.constructor.PCDATA;
        var SELF_CLOSING = this.constructor.SELF_CLOSING;
        var kind = tag.match(/<(.*?)[\s\n>\/]/)[1].toLowerCase();
        var child = adaptor.node(kind);
        var attributes = tag.replace(/^<.*?[\s\n>]/, "").split(PATTERNS.attrsplit);
        if (attributes.pop().match(/>$/) || attributes.length < 5) {
          this.addAttributes(adaptor, child, attributes);
          adaptor.append(node, child);
          if (!SELF_CLOSING[kind] && !tag.match(/\/>$/)) {
            if (PCDATA[kind]) {
              this.handlePCDATA(adaptor, child, kind, parts);
            } else {
              node = child;
            }
          }
        }
        return node;
      };
      LiteParser2.prototype.addAttributes = function(adaptor, node, attributes) {
        var CDATA_ATTR = this.constructor.CDATA_ATTR;
        while (attributes.length) {
          var _a = __read(attributes.splice(0, 5), 5), name_1 = _a[1], v1 = _a[2], v2 = _a[3], v3 = _a[4];
          var value = v1 || v2 || v3 || "";
          if (!CDATA_ATTR[name_1]) {
            value = Entities.translate(value);
          }
          adaptor.setAttribute(node, name_1, value);
        }
      };
      LiteParser2.prototype.handlePCDATA = function(adaptor, node, kind, parts) {
        var pcdata = [];
        var etag = "</" + kind + ">";
        var ptag = "";
        while (parts.length && ptag !== etag) {
          pcdata.push(ptag);
          pcdata.push(parts.shift());
          ptag = parts.shift();
        }
        adaptor.append(node, adaptor.text(pcdata.join("")));
      };
      LiteParser2.prototype.checkDocument = function(adaptor, root) {
        var e_1, _a, e_2, _b;
        var node = this.getOnlyChild(adaptor, adaptor.body(root));
        if (!node)
          return;
        try {
          for (var _c = __values(adaptor.childNodes(adaptor.body(root))), _d = _c.next(); !_d.done; _d = _c.next()) {
            var child = _d.value;
            if (child === node) {
              break;
            }
            if (child instanceof Text_js_1.LiteComment && child.value.match(/^<!DOCTYPE/)) {
              root.type = child.value;
            }
          }
        } catch (e_1_1) {
          e_1 = { error: e_1_1 };
        } finally {
          try {
            if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
          } finally {
            if (e_1) throw e_1.error;
          }
        }
        switch (adaptor.kind(node)) {
          case "html":
            try {
              for (var _e = __values(node.children), _f = _e.next(); !_f.done; _f = _e.next()) {
                var child = _f.value;
                switch (adaptor.kind(child)) {
                  case "head":
                    root.head = child;
                    break;
                  case "body":
                    root.body = child;
                    break;
                }
              }
            } catch (e_2_1) {
              e_2 = { error: e_2_1 };
            } finally {
              try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
              } finally {
                if (e_2) throw e_2.error;
              }
            }
            root.root = node;
            adaptor.remove(node);
            if (adaptor.parent(root.body) !== node) {
              adaptor.append(node, root.body);
            }
            if (adaptor.parent(root.head) !== node) {
              adaptor.insert(root.head, root.body);
            }
            break;
          case "head":
            root.head = adaptor.replace(node, root.head);
            break;
          case "body":
            root.body = adaptor.replace(node, root.body);
            break;
        }
      };
      LiteParser2.prototype.getOnlyChild = function(adaptor, body) {
        var e_3, _a;
        var node = null;
        try {
          for (var _b = __values(adaptor.childNodes(body)), _c = _b.next(); !_c.done; _c = _b.next()) {
            var child = _c.value;
            if (child instanceof Element_js_1.LiteElement) {
              if (node)
                return null;
              node = child;
            }
          }
        } catch (e_3_1) {
          e_3 = { error: e_3_1 };
        } finally {
          try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
          } finally {
            if (e_3) throw e_3.error;
          }
        }
        return node;
      };
      LiteParser2.prototype.serialize = function(adaptor, node, xml) {
        var _this = this;
        if (xml === void 0) {
          xml = false;
        }
        var SELF_CLOSING = this.constructor.SELF_CLOSING;
        var CDATA = this.constructor.CDATA_ATTR;
        var tag = adaptor.kind(node);
        var attributes = adaptor.allAttributes(node).map(function(x) {
          return x.name + '="' + (CDATA[x.name] ? x.value : _this.protectAttribute(x.value)) + '"';
        }).join(" ");
        var content = this.serializeInner(adaptor, node, xml);
        var html = "<" + tag + (attributes ? " " + attributes : "") + ((!xml || content) && !SELF_CLOSING[tag] ? ">".concat(content, "</").concat(tag, ">") : xml ? "/>" : ">");
        return html;
      };
      LiteParser2.prototype.serializeInner = function(adaptor, node, xml) {
        var _this = this;
        if (xml === void 0) {
          xml = false;
        }
        var PCDATA = this.constructor.PCDATA;
        if (PCDATA.hasOwnProperty(node.kind)) {
          return adaptor.childNodes(node).map(function(x) {
            return adaptor.value(x);
          }).join("");
        }
        return adaptor.childNodes(node).map(function(x) {
          var kind = adaptor.kind(x);
          return kind === "#text" ? _this.protectHTML(adaptor.value(x)) : kind === "#comment" ? x.value : _this.serialize(adaptor, x, xml);
        }).join("");
      };
      LiteParser2.prototype.protectAttribute = function(text) {
        if (typeof text !== "string") {
          text = String(text);
        }
        return text.replace(/"/g, "&quot;");
      };
      LiteParser2.prototype.protectHTML = function(text) {
        return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
      };
      LiteParser2.SELF_CLOSING = {
        area: true,
        base: true,
        br: true,
        col: true,
        command: true,
        embed: true,
        hr: true,
        img: true,
        input: true,
        keygen: true,
        link: true,
        menuitem: true,
        meta: true,
        param: true,
        source: true,
        track: true,
        wbr: true
      };
      LiteParser2.PCDATA = {
        option: true,
        textarea: true,
        fieldset: true,
        title: true,
        style: true,
        script: true
      };
      LiteParser2.CDATA_ATTR = {
        style: true,
        datafld: true,
        datasrc: true,
        href: true,
        src: true,
        longdesc: true,
        usemap: true,
        cite: true,
        datetime: true,
        action: true,
        axis: true,
        profile: true,
        content: true,
        scheme: true
      };
      return LiteParser2;
    }();
    exports.LiteParser = LiteParser;
  }
});

// node_modules/mathjax-full/js/adaptors/lite/Window.js
var require_Window = __commonJS({
  "node_modules/mathjax-full/js/adaptors/lite/Window.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LiteWindow = void 0;
    var Element_js_1 = require_Element();
    var Document_js_1 = require_Document();
    var List_js_1 = require_List();
    var Parser_js_1 = require_Parser();
    var LiteWindow = /* @__PURE__ */ function() {
      function LiteWindow2() {
        this.DOMParser = Parser_js_1.LiteParser;
        this.NodeList = List_js_1.LiteList;
        this.HTMLCollection = List_js_1.LiteList;
        this.HTMLElement = Element_js_1.LiteElement;
        this.DocumentFragment = List_js_1.LiteList;
        this.Document = Document_js_1.LiteDocument;
        this.document = new Document_js_1.LiteDocument();
      }
      return LiteWindow2;
    }();
    exports.LiteWindow = LiteWindow;
  }
});

// node_modules/mathjax-full/js/adaptors/liteAdaptor.js
var require_liteAdaptor = __commonJS({
  "node_modules/mathjax-full/js/adaptors/liteAdaptor.js"(exports) {
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    var __values = exports && exports.__values || function(o) {
      var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && typeof o.length === "number") return {
        next: function() {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    var __read = exports && exports.__read || function(o, n) {
      var m = typeof Symbol === "function" && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = { error };
      } finally {
        try {
          if (r && !r.done && (m = i["return"])) m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    var __spreadArray = exports && exports.__spreadArray || function(to, from, pack) {
      if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i);
          ar[i] = from[i];
        }
      }
      return to.concat(ar || Array.prototype.slice.call(from));
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.liteAdaptor = exports.LiteAdaptor = exports.LiteBase = void 0;
    var DOMAdaptor_js_1 = require_DOMAdaptor();
    var NodeMixin_js_1 = require_NodeMixin();
    var Document_js_1 = require_Document();
    var Element_js_1 = require_Element();
    var Text_js_1 = require_Text();
    var Window_js_1 = require_Window();
    var Parser_js_1 = require_Parser();
    var Styles_js_1 = require_Styles();
    var LiteBase = function(_super) {
      __extends(LiteBase2, _super);
      function LiteBase2() {
        var _this = _super.call(this) || this;
        _this.parser = new Parser_js_1.LiteParser();
        _this.window = new Window_js_1.LiteWindow();
        return _this;
      }
      LiteBase2.prototype.parse = function(text, format) {
        return this.parser.parseFromString(text, format, this);
      };
      LiteBase2.prototype.create = function(kind, _ns) {
        if (_ns === void 0) {
          _ns = null;
        }
        return new Element_js_1.LiteElement(kind);
      };
      LiteBase2.prototype.text = function(text) {
        return new Text_js_1.LiteText(text);
      };
      LiteBase2.prototype.comment = function(text) {
        return new Text_js_1.LiteComment(text);
      };
      LiteBase2.prototype.createDocument = function() {
        return new Document_js_1.LiteDocument();
      };
      LiteBase2.prototype.head = function(doc) {
        return doc.head;
      };
      LiteBase2.prototype.body = function(doc) {
        return doc.body;
      };
      LiteBase2.prototype.root = function(doc) {
        return doc.root;
      };
      LiteBase2.prototype.doctype = function(doc) {
        return doc.type;
      };
      LiteBase2.prototype.tags = function(node, name, ns) {
        if (ns === void 0) {
          ns = null;
        }
        var stack = [];
        var tags = [];
        if (ns) {
          return tags;
        }
        var n = node;
        while (n) {
          var kind = n.kind;
          if (kind !== "#text" && kind !== "#comment") {
            n = n;
            if (kind === name) {
              tags.push(n);
            }
            if (n.children.length) {
              stack = n.children.concat(stack);
            }
          }
          n = stack.shift();
        }
        return tags;
      };
      LiteBase2.prototype.elementById = function(node, id) {
        var stack = [];
        var n = node;
        while (n) {
          if (n.kind !== "#text" && n.kind !== "#comment") {
            n = n;
            if (n.attributes["id"] === id) {
              return n;
            }
            if (n.children.length) {
              stack = n.children.concat(stack);
            }
          }
          n = stack.shift();
        }
        return null;
      };
      LiteBase2.prototype.elementsByClass = function(node, name) {
        var stack = [];
        var tags = [];
        var n = node;
        while (n) {
          if (n.kind !== "#text" && n.kind !== "#comment") {
            n = n;
            var classes = (n.attributes["class"] || "").trim().split(/ +/);
            if (classes.includes(name)) {
              tags.push(n);
            }
            if (n.children.length) {
              stack = n.children.concat(stack);
            }
          }
          n = stack.shift();
        }
        return tags;
      };
      LiteBase2.prototype.getElements = function(nodes, document) {
        var e_1, _a;
        var containers = [];
        var body = this.body(document);
        try {
          for (var nodes_1 = __values(nodes), nodes_1_1 = nodes_1.next(); !nodes_1_1.done; nodes_1_1 = nodes_1.next()) {
            var node = nodes_1_1.value;
            if (typeof node === "string") {
              if (node.charAt(0) === "#") {
                var n = this.elementById(body, node.slice(1));
                if (n) {
                  containers.push(n);
                }
              } else if (node.charAt(0) === ".") {
                containers = containers.concat(this.elementsByClass(body, node.slice(1)));
              } else if (node.match(/^[-a-z][-a-z0-9]*$/i)) {
                containers = containers.concat(this.tags(body, node));
              }
            } else if (Array.isArray(node)) {
              containers = containers.concat(node);
            } else if (node instanceof this.window.NodeList || node instanceof this.window.HTMLCollection) {
              containers = containers.concat(node.nodes);
            } else {
              containers.push(node);
            }
          }
        } catch (e_1_1) {
          e_1 = { error: e_1_1 };
        } finally {
          try {
            if (nodes_1_1 && !nodes_1_1.done && (_a = nodes_1.return)) _a.call(nodes_1);
          } finally {
            if (e_1) throw e_1.error;
          }
        }
        return containers;
      };
      LiteBase2.prototype.contains = function(container, node) {
        while (node && node !== container) {
          node = this.parent(node);
        }
        return !!node;
      };
      LiteBase2.prototype.parent = function(node) {
        return node.parent;
      };
      LiteBase2.prototype.childIndex = function(node) {
        return node.parent ? node.parent.children.findIndex(function(n) {
          return n === node;
        }) : -1;
      };
      LiteBase2.prototype.append = function(node, child) {
        if (child.parent) {
          this.remove(child);
        }
        node.children.push(child);
        child.parent = node;
        return child;
      };
      LiteBase2.prototype.insert = function(nchild, ochild) {
        if (nchild.parent) {
          this.remove(nchild);
        }
        if (ochild && ochild.parent) {
          var i = this.childIndex(ochild);
          ochild.parent.children.splice(i, 0, nchild);
          nchild.parent = ochild.parent;
        }
      };
      LiteBase2.prototype.remove = function(child) {
        var i = this.childIndex(child);
        if (i >= 0) {
          child.parent.children.splice(i, 1);
        }
        child.parent = null;
        return child;
      };
      LiteBase2.prototype.replace = function(nnode, onode) {
        var i = this.childIndex(onode);
        if (i >= 0) {
          onode.parent.children[i] = nnode;
          nnode.parent = onode.parent;
          onode.parent = null;
        }
        return onode;
      };
      LiteBase2.prototype.clone = function(node) {
        var _this = this;
        var nnode = new Element_js_1.LiteElement(node.kind);
        nnode.attributes = __assign({}, node.attributes);
        nnode.children = node.children.map(function(n) {
          if (n.kind === "#text") {
            return new Text_js_1.LiteText(n.value);
          } else if (n.kind === "#comment") {
            return new Text_js_1.LiteComment(n.value);
          } else {
            var m = _this.clone(n);
            m.parent = nnode;
            return m;
          }
        });
        return nnode;
      };
      LiteBase2.prototype.split = function(node, n) {
        var text = new Text_js_1.LiteText(node.value.slice(n));
        node.value = node.value.slice(0, n);
        node.parent.children.splice(this.childIndex(node) + 1, 0, text);
        text.parent = node.parent;
        return text;
      };
      LiteBase2.prototype.next = function(node) {
        var parent = node.parent;
        if (!parent)
          return null;
        var i = this.childIndex(node) + 1;
        return i >= 0 && i < parent.children.length ? parent.children[i] : null;
      };
      LiteBase2.prototype.previous = function(node) {
        var parent = node.parent;
        if (!parent)
          return null;
        var i = this.childIndex(node) - 1;
        return i >= 0 ? parent.children[i] : null;
      };
      LiteBase2.prototype.firstChild = function(node) {
        return node.children[0];
      };
      LiteBase2.prototype.lastChild = function(node) {
        return node.children[node.children.length - 1];
      };
      LiteBase2.prototype.childNodes = function(node) {
        return __spreadArray([], __read(node.children), false);
      };
      LiteBase2.prototype.childNode = function(node, i) {
        return node.children[i];
      };
      LiteBase2.prototype.kind = function(node) {
        return node.kind;
      };
      LiteBase2.prototype.value = function(node) {
        return node.kind === "#text" ? node.value : node.kind === "#comment" ? node.value.replace(/^<!(--)?((?:.|\n)*)\1>$/, "$2") : "";
      };
      LiteBase2.prototype.textContent = function(node) {
        var _this = this;
        return node.children.reduce(function(s, n) {
          return s + (n.kind === "#text" ? n.value : n.kind === "#comment" ? "" : _this.textContent(n));
        }, "");
      };
      LiteBase2.prototype.innerHTML = function(node) {
        return this.parser.serializeInner(this, node);
      };
      LiteBase2.prototype.outerHTML = function(node) {
        return this.parser.serialize(this, node);
      };
      LiteBase2.prototype.serializeXML = function(node) {
        return this.parser.serialize(this, node, true);
      };
      LiteBase2.prototype.setAttribute = function(node, name, value, ns) {
        if (ns === void 0) {
          ns = null;
        }
        if (typeof value !== "string") {
          value = String(value);
        }
        if (ns) {
          name = ns.replace(/.*\//, "") + ":" + name.replace(/^.*:/, "");
        }
        node.attributes[name] = value;
        if (name === "style") {
          node.styles = null;
        }
      };
      LiteBase2.prototype.getAttribute = function(node, name) {
        return node.attributes[name];
      };
      LiteBase2.prototype.removeAttribute = function(node, name) {
        delete node.attributes[name];
      };
      LiteBase2.prototype.hasAttribute = function(node, name) {
        return node.attributes.hasOwnProperty(name);
      };
      LiteBase2.prototype.allAttributes = function(node) {
        var e_2, _a;
        var attributes = node.attributes;
        var list = [];
        try {
          for (var _b = __values(Object.keys(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {
            var name_1 = _c.value;
            list.push({ name: name_1, value: attributes[name_1] });
          }
        } catch (e_2_1) {
          e_2 = { error: e_2_1 };
        } finally {
          try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
          } finally {
            if (e_2) throw e_2.error;
          }
        }
        return list;
      };
      LiteBase2.prototype.addClass = function(node, name) {
        var classes = (node.attributes["class"] || "").split(/ /);
        if (!classes.find(function(n) {
          return n === name;
        })) {
          classes.push(name);
          node.attributes["class"] = classes.join(" ");
        }
      };
      LiteBase2.prototype.removeClass = function(node, name) {
        var classes = (node.attributes["class"] || "").split(/ /);
        var i = classes.findIndex(function(n) {
          return n === name;
        });
        if (i >= 0) {
          classes.splice(i, 1);
          node.attributes["class"] = classes.join(" ");
        }
      };
      LiteBase2.prototype.hasClass = function(node, name) {
        var classes = (node.attributes["class"] || "").split(/ /);
        return !!classes.find(function(n) {
          return n === name;
        });
      };
      LiteBase2.prototype.setStyle = function(node, name, value) {
        if (!node.styles) {
          node.styles = new Styles_js_1.Styles(this.getAttribute(node, "style"));
        }
        node.styles.set(name, value);
        node.attributes["style"] = node.styles.cssText;
      };
      LiteBase2.prototype.getStyle = function(node, name) {
        if (!node.styles) {
          var style = this.getAttribute(node, "style");
          if (!style) {
            return "";
          }
          node.styles = new Styles_js_1.Styles(style);
        }
        return node.styles.get(name);
      };
      LiteBase2.prototype.allStyles = function(node) {
        return this.getAttribute(node, "style");
      };
      LiteBase2.prototype.insertRules = function(node, rules) {
        node.children = [this.text(rules.join("\n\n") + "\n\n" + this.textContent(node))];
      };
      LiteBase2.prototype.fontSize = function(_node) {
        return 0;
      };
      LiteBase2.prototype.fontFamily = function(_node) {
        return "";
      };
      LiteBase2.prototype.nodeSize = function(_node, _em, _local) {
        if (_em === void 0) {
          _em = 1;
        }
        if (_local === void 0) {
          _local = null;
        }
        return [0, 0];
      };
      LiteBase2.prototype.nodeBBox = function(_node) {
        return { left: 0, right: 0, top: 0, bottom: 0 };
      };
      return LiteBase2;
    }(DOMAdaptor_js_1.AbstractDOMAdaptor);
    exports.LiteBase = LiteBase;
    var LiteAdaptor = function(_super) {
      __extends(LiteAdaptor2, _super);
      function LiteAdaptor2() {
        return _super !== null && _super.apply(this, arguments) || this;
      }
      return LiteAdaptor2;
    }((0, NodeMixin_js_1.NodeMixin)(LiteBase));
    exports.LiteAdaptor = LiteAdaptor;
    function liteAdaptor(options) {
      if (options === void 0) {
        options = null;
      }
      return new LiteAdaptor(null, options);
    }
    exports.liteAdaptor = liteAdaptor;
  }
});
export default require_liteAdaptor();
//# sourceMappingURL=mathjax-full_js_adaptors_liteAdaptor.js.map
