"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DummyWalker = void 0;
const abstract_walker_js_1 = require("./abstract_walker.js");
class DummyWalker extends abstract_walker_js_1.AbstractWalker {
    up() {
        return null;
    }
    down() {
        return null;
    }
    left() {
        return null;
    }
    right() {
        return null;
    }
    repeat() {
        return null;
    }
    depth() {
        return null;
    }
    home() {
        return null;
    }
    getDepth() {
        return 0;
    }
    initLevels() {
        return null;
    }
    combineContentChildren(_type, _role, _content, _children) {
        return [];
    }
    findFocusOnLevel(_id) {
        return null;
    }
}
exports.DummyWalker = DummyWalker;
