{"version": 3, "file": "FindTeX.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/FindTeX.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,sDAAwD;AAExD,kDAA8D;AAC9D,sDAA4D;AAoB5D;IAAsC,2BAAyB;IAmD7D,iBAAY,OAAmB;QAA/B,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,WAAW,EAAE,CAAC;;IACrB,CAAC;IAMS,6BAAW,GAArB;QAAA,iBA4BC;QA3BC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,MAAM,GAAa,EAAE,EAAE,KAAK,GAAa,EAAE,EAAE,QAAQ,GAAa,EAAE,CAAC;QACzE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAC,MAAc,IAAK,OAAA,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAtC,CAAsC,CAAC,CAAC;QAC1F,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAC,MAAc,IAAK,OAAA,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAArC,CAAqC,CAAC,CAAC;QAC1F,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,qBAAqB,CAAC,EAAE;YAClC,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACzC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YACb,CAAC,EAAE,CAAC;SACL;QACD,IAAI,OAAO,CAAC,gBAAgB,CAAC,EAAE;YAC7B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChC;QACD,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE;YAC1B,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;SAClD;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;SACd;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IASS,4BAAU,GAApB,UAAqB,MAAgB,EAAE,MAAc,EAAE,OAAgB;QACjE,IAAA,KAAA,OAAgB,MAAM,IAAA,EAArB,IAAI,QAAA,EAAE,KAAK,QAAU,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5D,CAAC;IASS,4BAAU,GAApB,UAAqB,GAAW,EAAE,IAAa;QAC7C,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,IAAI,IAAA,wBAAY,EAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B,EAAE,GAAG,CAAC,CAAC;IACnF,CAAC;IAaS,yBAAO,GAAjB,UAAkB,IAAY,EAAE,CAAS,EAAE,KAAsB,EAAE,GAAY;QACzE,IAAA,KAAA,OAA4B,GAAG,IAAA,EAA9B,KAAK,QAAA,EAAE,OAAO,QAAA,EAAE,OAAO,QAAO,CAAC;QACpC,IAAI,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC1D,IAAI,KAAsB,EAAE,MAAM,GAAW,CAAC,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE;gBACpD,OAAO,IAAA,uBAAS,EAAO,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACnD,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAChF;iBAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC3B,MAAM,EAAE,CAAC;aACV;iBAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,EAAE;gBACrC,MAAM,EAAE,CAAC;aACV;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAUS,kCAAgB,GAA1B,UAA2B,IAAuB,EAAE,CAAS,EAAE,IAAY;QACzE,IAAI,KAAK,EAAE,KAAK,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QACzB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACtC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;gBAC7C,IAAI,GAAG,GAAG,iBAAiB,GAAG,IAAA,wBAAY,EAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;gBACrE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtG,IAAI,KAAK,EAAE;oBACT,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;oBACnD,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;iBAC/B;aACF;iBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;gBACpD,IAAI,MAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;gBAC/C,IAAI,MAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,KAAK,GAAG,IAAA,uBAAS,EAAO,EAAE,EAAE,MAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;iBACtE;qBAAM;oBACL,KAAK,GAAG,IAAA,uBAAS,EAAO,EAAE,EAAE,MAAI,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;iBACnE;aACF;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aACpC;SACF;IACH,CAAC;IAOM,0BAAQ,GAAf,UAAgB,OAAiB;QAC/B,IAAI,IAAI,GAAsB,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9C,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5C;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IA3La,eAAO,GAAe;QAClC,UAAU,EAAE;YAEV,CAAC,KAAK,EAAE,KAAK,CAAC;SACf;QAED,WAAW,EAAE;YACX,CAAC,IAAI,EAAE,IAAI,CAAC;YACZ,CAAC,KAAK,EAAE,KAAK,CAAC;SACf;QAED,cAAc,EAAE,IAAI;QAEpB,mBAAmB,EAAE,IAAI;QAEzB,WAAW,EAAE,IAAI;KAClB,CAAC;IA6KJ,cAAC;CAAA,AAlMD,CAAsC,8BAAgB,GAkMrD;AAlMY,0BAAO"}