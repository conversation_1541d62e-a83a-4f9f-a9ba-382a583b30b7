{"version": 3, "file": "menclose.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/menclose.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,uDAA2C;AAI3C,qDAA8C;AAgK9C,SAAgB,mBAAmB,CAKjC,IAAO;IAEP;QAAqB,2BAAI;QAuCvB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAMd;YAzCM,eAAS,GAAwB,EAAE,CAAC;YAKpC,iBAAW,GAA4B,IAAI,CAAC;YAK5C,WAAK,GAAM,IAAI,CAAC;YAKhB,aAAO,GAAW,QAAQ,CAAC,OAAO,CAAC;YAInC,eAAS,GAAW,QAAQ,CAAC,SAAS,CAAC;YAIvC,eAAS,GAAG,EAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAC,CAAC;YAK3E,UAAI,GAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAQ/C,KAAI,CAAC,aAAa,EAAE,CAAC;YACrB,KAAI,CAAC,YAAY,EAAE,CAAC;YACpB,KAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,KAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,gBAAgB,EAAE,CAAC;;QACtC,CAAC;QAKM,+BAAa,GAApB;YACE,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,IAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC/C,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC1D;YACD,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACnD,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;aAChE;YACD,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAW,CAAC;YAC7D,IAAI,SAAS,KAAK,SAAS,EAAE;gBACvB,IAAA,KAAA,OAAa,IAAA,iBAAK,EAAC,SAAS,CAAC,IAAA,EAA5B,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAAoB,CAAC;gBAClC,IAAI,CAAC,SAAS,GAAG;oBACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACxC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;iBAC7C,CAAC;aACH;QACH,CAAC;QAMM,8BAAY,GAAnB;;YACE,IAAM,SAAS,GAAI,IAAI,CAAC,WAAyC,CAAC,SAAS,CAAC;;gBAC5E,KAAmB,IAAA,KAAA,SAAA,IAAA,iBAAK,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC,CAAA,gBAAA,4BAAE;oBAArE,IAAM,MAAI,WAAA;oBACb,IAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC;oBACrC,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,GAAG,QAAQ,CAAC;wBAChC,IAAI,QAAQ,CAAC,WAAW,EAAE;4BACxB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;yBACtC;qBACF;iBACF;;;;;;;;;QACH,CAAC;QAKM,0CAAwB,GAA/B;;;gBACE,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;oBAA3C,IAAM,MAAI,WAAA;oBACb,IAAI,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,EAAE;wBACxB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC;;4BACjD,KAAuB,IAAA,oBAAA,SAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAAA,gBAAA,4BAAE;gCAArC,IAAM,QAAQ,WAAA;gCACjB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;6BACjC;;;;;;;;;qBACF;iBACF;;;;;;;;;QACH,CAAC;QAKM,qCAAmB,GAA1B;;;gBACE,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;oBAA3C,IAAM,MAAI,WAAA;oBACb,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,CAAC,IAAI,CAAC;oBACvC,IAAI,IAAI,IAAI,CAAC,IAAW,CAAC,CAAC;iBAC3B;;;;;;;;;QACH,CAAC;QAOM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YAInD,IAAA,KAAA,OAAe,IAAI,CAAC,IAAI,IAAA,EAAvB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAa,CAAC;YAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YACZ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YACZ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YACZ,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAKM,kCAAgB,GAAvB;;YACE,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAyB,CAAC;;gBAChD,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;oBAA3C,IAAM,MAAI,WAAA;oBACb,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,CAAC,IAAI,CAAC,IAAW,CAAC,CAAC,CAAC;iBACpE;;;;;;;;;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAKM,4BAAU,GAAjB;;YAAA,iBASC;YARC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAyB,CAAC;;gBACjD,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;oBAA3C,IAAM,MAAI,WAAA;oBACb,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAI,CAAC,CAAC,MAAM,CAAC;oBAC3C,IAAI,MAAM,EAAE;wBACV,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC;qBAClD;iBACF;;;;;;;;;YACD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAvB,CAAuB,CAAyB,CAAC;QAChF,CAAC;QAQM,iCAAe,GAAtB,UAAuB,CAAuB,EAAE,CAAuB;YACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;oBACf,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBACb;aACF;QACH,CAAC;QAUM,2BAAS,GAAhB,UAAiB,SAAiB;YAC5B,IAAA,KAAA,OAAe,IAAI,CAAC,IAAI,IAAA,EAAvB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAa,CAAC;YAC7B,IAAM,CAAC,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;QAOM,2BAAS,GAAhB,UAAiB,CAAS,EAAE,CAAS;YACnC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;QAYM,uBAAK,GAAZ,UAAa,EAAU,EAAE,EAAU,EAAE,OAAgB,EAAE,OAAoB,EAAE,KAAiB;YAAvC,wBAAA,EAAA,YAAoB;YAAE,sBAAA,EAAA,SAAiB;YAC5F,OAAO,IAAS,CAAC;QACnB,CAAC;QAQM,2BAAS,GAAhB;YACQ,IAAA,KAAA,OAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAA,EAAtC,CAAC,QAAA,EAAE,CAAC,QAAkC,CAAC;YAC9C,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,IAAA,KAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAvC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAgC,CAAC;YAC/C,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACjC,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3B,IAAA,KAAA,OAAS,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAA,EAA5C,CAAC,QAAA,EAAE,CAAC,QAAwC,CAAC;YACpD,OAAO,EAAC,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAC,CAAC;QACtB,CAAC;QAOM,yBAAO,GAAd;YACQ,IAAA,KAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAvC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAgC,CAAC;YACzC,IAAA,KAAA,OAAe,IAAI,CAAC,IAAI,IAAA,EAAvB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAa,CAAC;YAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QAaM,6BAAW,GAAlB,UAAmB,KAAQ;YACzB,IAAM,UAAU,GAAI,IAAI,CAAC,IAAwB,CAAC,OAAO,CAAC;YAC1D,IAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACvC,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;YAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAM,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAOM,0BAAQ,GAAf;YACE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAClC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;QAEH,cAAC;IAAD,CAAC,AA5QM,CAAc,IAAI,GA4QvB;AACJ,CAAC;AApRD,kDAoRC"}