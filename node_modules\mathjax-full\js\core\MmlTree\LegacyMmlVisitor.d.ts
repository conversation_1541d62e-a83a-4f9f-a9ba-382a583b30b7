import { MmlVisitor } from './MmlVisitor.js';
import { MmlNode, TextNode, XMLNode } from './MmlNode.js';
export declare class LegacyMmlVisitor extends MmlVisitor {
    visitTree(node: MmlNode): any;
    visitTextNode(node: TextNode, parent: any): void;
    visitXMLNode(node: XMLNode, parent: any): void;
    visitInferredMrowNode(node: MmlNode, parent: any): void;
    visitDefault(node: MmlNode, parent: any): void;
    addAttributes(node: MmlNode, mml: any): void;
    addProperties(node: MmlNode, mml: any): void;
}
