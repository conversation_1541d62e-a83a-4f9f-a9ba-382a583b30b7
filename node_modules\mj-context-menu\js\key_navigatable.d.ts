export declare enum KEY {
    RETURN = 13,
    ESCAPE = 27,
    SPACE = 32,
    LEFT = 37,
    UP = 38,
    RIGHT = 39,
    DOWN = 40
}
export interface KeyNavigatable {
    keydown(event: KeyboardEvent): void;
    escape(event: KeyboardEvent): void;
    space(event: KeyboardEvent): void;
    left(event: KeyboardEvent): void;
    right(event: KeyboardEvent): void;
    up(event: KeyboardEvent): void;
    down(event: KeyboardEvent): void;
}
