import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/colorv2/ColorV2Configuration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/colorv2', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      colorv2: {
        ColorV2Configuration: module1
      }
    }
  }
}});
