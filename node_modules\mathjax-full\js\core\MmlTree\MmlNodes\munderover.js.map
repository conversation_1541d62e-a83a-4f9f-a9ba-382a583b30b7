{"version": 3, "file": "munderover.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/munderover.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAAiE;AAOjE;IAAmC,iCAAmB;IAAtD;;IA4HA,CAAC;IAxGC,sBAAW,+BAAI;aAAf;YACE,OAAO,YAAY,CAAC;QACtB,CAAC;;;OAAA;IAMD,sBAAW,gCAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,+BAAI;aAAf;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,gCAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAKD,sBAAW,+BAAI;aAAf;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAW,6CAAkB;aAA7B;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAUS,mDAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzF,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;QAC9E,IAAI,OAAO,GAAI,IAAI,CAAC,WAAoC,CAAC,OAAO,CAAC;QACjE,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAC7C,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YACb,OAAO;SACR;QACD,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAC7C,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAQS,sCAAc,GAAxB,UAAyB,MAAc,EAAE,KAAc,EAAE,KAAa;QACpE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACzC,KAAK,EAAE,CAAC;SACT;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAcS,0CAAkB,GAA5B,UAA6B,CAAS,EAAE,MAAc,EAAE,OAAgB,EAAE,KAAa,EAC1D,KAAc,EAAE,KAAc;QACzD,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;YACrE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAChD,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5F;SACF;IACH,CAAC;IArHa,sBAAQ,yBACjB,gCAAmB,CAAC,QAAQ,KAC/B,MAAM,EAAE,KAAK,EACb,WAAW,EAAE,KAAK,EAClB,KAAK,EAAE,QAAQ,IACf;IAKe,qBAAO,GAAG,CAAC,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;IA6G3D,oBAAC;CAAA,AA5HD,CAAmC,gCAAmB,GA4HrD;AA5HY,sCAAa;AAmI1B;IAA+B,6BAAa;IAA5C;;IAwBA,CAAC;IAZC,sBAAW,2BAAI;aAAf;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAMD,sBAAW,4BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAjBa,kBAAQ,gBACf,aAAa,CAAC,QAAQ,EAC3B;IAiBJ,gBAAC;CAAA,AAxBD,CAA+B,aAAa,GAwB3C;AAxBY,8BAAS;AA+BtB;IAA8B,4BAAa;IAA3C;;IA4CA,CAAC;IA5BC,sBAAW,0BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,2BAAK;aAAT;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAW,0BAAI;aAAf;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAMD,sBAAW,2BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IArCa,iBAAQ,gBACf,aAAa,CAAC,QAAQ,EAC3B;IAIe,gBAAO,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IAiC3D,eAAC;CAAA,AA5CD,CAA8B,aAAa,GA4C1C;AA5CY,4BAAQ"}