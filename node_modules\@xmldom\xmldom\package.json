{"name": "@xmldom/xmldom", "version": "0.9.8", "description": "A pure JavaScript W3C standard-based (XML DOM Level 2 Core) DOMParser and XMLSerializer module.", "keywords": ["w3c", "dom", "xml", "parser", "javascript", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XMLSerializer", "ponyfill"], "homepage": "https://github.com/xmldom/xmldom", "repository": {"type": "git", "url": "git://github.com/xmldom/xmldom.git"}, "main": "lib/index.js", "types": "index.d.ts", "files": ["CHANGELOG.md", "LICENSE", "readme.md", "SECURITY.md", "index.d.ts", "lib"], "scripts": {"lint": "eslint examples lib test", "format": "prettier --write examples lib test index.d.ts", "changelog": "auto-changelog --unreleased-only", "start": "nodemon --watch package.json --watch lib --watch test --exec 'npm --silent run test && npm --silent run lint'", "test": "jest", "fuzz": "jest --config=./jest.fuzz.config.js", "test:types": "cd examples/typescript-node-es6 && ./pretest.sh 3 && ./pretest.sh 4 && ./pretest.sh 5 && node dist/index.js", "testrelease": "npm test && eslint lib", "version": "./changelog-has-version.sh", "release": "np --no-yarn --test-script testrelease"}, "engines": {"node": ">=14.6"}, "devDependencies": {"@homer0/prettier-plugin-jsdoc": "9.1.0", "auto-changelog": "2.5.0", "eslint": "8.57.1", "eslint-config-prettier": "10.0.1", "eslint-plugin-anti-trojan-source": "1.1.1", "eslint-plugin-es5": "1.5.0", "eslint-plugin-n": "17.15.1", "eslint-plugin-prettier": "5.2.3", "get-stream": "6.0.1", "jest": "29.7.0", "nodemon": "3.1.9", "np": "8.0.4", "prettier": "3.5.2", "xmltest": "2.0.3", "yauzl": "3.2.0"}, "bugs": {"url": "https://github.com/xmldom/xmldom/issues"}, "license": "MIT", "auto-changelog": {"prepend": true, "remote": "origin", "tagPrefix": "", "template": "./auto-changelog.hbs"}, "packageManager": "npm@11.1.0+sha512.acf301ad9b9ddba948fcb72341e2f0fcae477f56a95cc2a092934d133a7461062633cefbf93d5934a3dc0768674e2edee9f04dcfcc4bb4c327ff0e3a7d552a1b"}