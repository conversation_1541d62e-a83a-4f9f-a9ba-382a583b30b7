{"version": 3, "file": "LinkedList.js", "sourceRoot": "", "sources": ["../../ts/util/LinkedList.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4Ba,QAAA,GAAG,GAAG,MAAM,EAAE,CAAC;AAkB5B;IAmBE,kBAAY,IAAgB;QAAhB,qBAAA,EAAA,WAAgB;QAVrB,SAAI,GAAwB,IAAI,CAAC;QAIjC,SAAI,GAAwB,IAAI,CAAC;QAOtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IACH,eAAC;AAAD,CAAC,AAtBD,IAsBC;AAtBY,4BAAQ;AA+BrB;IAiBE;QAAY,cAAoB;aAApB,UAAoB,EAApB,qBAAoB,EAApB,IAAoB;YAApB,yBAAoB;;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAY,WAAG,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC,IAAI,OAAT,IAAI,2BAAS,IAAI,WAAE;IACrB,CAAC;IASM,6BAAQ,GAAf,UAAgB,CAAY,EAAE,CAAY;QACxC,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAQM,yBAAI,GAAX;;QAAY,cAAoB;aAApB,UAAoB,EAApB,qBAAoB,EAApB,IAAoB;YAApB,yBAAoB;;;YAC9B,KAAmB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAApB,IAAM,IAAI,iBAAA;gBACb,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAY,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aACvB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,wBAAG,GAAV;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAG,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC,IAAiB,CAAC;IAChC,CAAC;IAQM,4BAAO,GAAd;;QAAe,cAAoB;aAApB,UAAoB,EAApB,qBAAoB,EAApB,IAAoB;YAApB,yBAAoB;;;YACjC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAY,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aACvB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,0BAAK,GAAZ;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAG,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC,IAAiB,CAAC;IAChC,CAAC;IAOM,2BAAM,GAAb;;QAAc,eAAqB;aAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;YAArB,0BAAqB;;QACjC,IAAM,GAAG,GAAG,IAAI,GAAG,EAAsB,CAAC;;YAC1C,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,GAAG,CAAC,GAAG,CAAC,MAAI,EAAE,IAAI,CAAC,CAAC;aACrB;;;;;;;;;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,WAAG,EAAE;YACxB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAiB,CAAC,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAC9B;YACD,IAAI,GAAG,IAAI,CAAC;SACb;IACH,CAAC;IAOM,0BAAK,GAAZ;QACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAOO,qBAAC,MAAM,CAAC,QAAQ,CAAC,GAAzB;;;;;oBACM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;;yBAEtB,CAAA,OAAO,CAAC,IAAI,KAAK,WAAG,CAAA;oBACzB,WAAM,OAAO,CAAC,IAAiB,EAAA;;oBAA/B,SAA+B,CAAC;oBAChC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;;;;;KAE1B;IAOO,6BAAQ,GAAhB;;;;;oBACM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;;yBAEtB,CAAA,OAAO,CAAC,IAAI,KAAK,WAAG,CAAA;oBACzB,WAAM,OAAO,CAAC,IAAiB,EAAA;;oBAA/B,SAA+B,CAAC;oBAChC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;;;;;KAE1B;IASM,2BAAM,GAAb,UAAc,IAAe,EAAE,QAAkC;QAAlC,yBAAA,EAAA,eAAkC;QAC/D,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QACD,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAY,IAAI,CAAC,CAAC;QACzC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,OAAO,GAAG,CAAC,IAAI,KAAK,WAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAiB,EAAE,IAAI,CAAC,IAAiB,CAAC,EAAE;YAClF,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAQM,yBAAI,GAAX,UAAY,QAAkC;;QAAlC,yBAAA,EAAA,eAAkC;QAC5C,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QAID,IAAI,KAAK,GAA4B,EAAE,CAAC;;YACxC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAA,gBAAA,4BAAE;gBAApB,IAAM,IAAI,WAAA;gBACb,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAY,IAAiB,CAAC,CAAC,CAAC;aAC1D;;;;;;;;;QAID,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAI5C,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YACvB,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAChB;QAID,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SAC3B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IASM,0BAAK,GAAZ,UAAa,IAA2B,EAAE,QAAkC;;QAAlC,yBAAA,EAAA,eAAkC;QAC1E,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QAID,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAI1B,OAAO,IAAI,CAAC,IAAI,KAAK,WAAG,IAAI,IAAI,CAAC,IAAI,KAAK,WAAG,EAAE;YAW7C,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAiB,EAAE,IAAI,CAAC,IAAiB,CAAC,EAAE;gBAC5D,KAAA,OAAmC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAA,EAA9C,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,CAAiB;gBAChD,KAAA,OAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,EAA9C,IAAI,CAAC,IAAI,QAAA,EAAE,IAAI,CAAC,IAAI,QAAA,CAA2B;gBAChD,KAAA,OAA6C,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,EAAlE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,CAA2B;gBACpE,KAAA,OAAmC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,EAAlE,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,QAAA,CAAqC;gBACpE,KAAA,OAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAA,EAA/B,IAAI,QAAA,EAAE,IAAI,QAAA,CAAsB;aAClC;iBAAM;gBACL,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;aAClB;SACF;QAKD,IAAI,IAAI,CAAC,IAAI,KAAK,WAAG,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SAC7C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACH,iBAAC;AAAD,CAAC,AAnRD,IAmRC;AAnRY,gCAAU"}