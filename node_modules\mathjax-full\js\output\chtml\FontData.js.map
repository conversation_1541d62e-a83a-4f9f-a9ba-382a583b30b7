{"version": 3, "file": "FontData.js", "sourceRoot": "", "sources": ["../../../ts/output/chtml/FontData.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,qDAAsH;AACtH,uCAAiC;AAGjC,oDAAyC;AAEzC,wDAAsC;AAqCtC;IAAmC,iCAAgE;IAAnG;QAAA,qEAmYC;QAlVQ,eAAS,GAA4B,IAAI,gBAAK,EAAoB,CAAC;QAKnE,gBAAU,GAAkB,IAAI,gBAAK,EAAU,CAAC;;IA6UzD,CAAC;IAtUe,yBAAW,GAAzB,UAA0B,IAAkB,EAAE,CAAS;QACrD,OAAO,OAAM,WAAW,YAAC,IAAI,EAAE,CAAC,CAAqB,CAAC;IACxD,CAAC;IAOM,mCAAW,GAAlB,UAAmB,KAAc;QAC/B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;IACnC,CAAC;IAKM,kCAAU,GAAjB;QACE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC;IAKM,qCAAa,GAApB,UAAqB,IAAY,EAAE,OAAsB,EAAE,IAAmB;QAA3C,wBAAA,EAAA,cAAsB;QAAE,qBAAA,EAAA,WAAmB;QAC5E,iBAAM,aAAa,YAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,KAAK,GAAI,IAAI,CAAC,WAAkC,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAKM,mCAAW,GAAlB,UAAmB,IAAY,EAAE,KAAmB;;QAClD,iBAAM,WAAW,YAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;;YACzC,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,CAAC,WAAA;gBACV,IAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,OAAO,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC3B,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC;iBACpB;aACF;;;;;;;;;IACH,CAAC;IAOD,sBAAI,iCAAM;aAAV;YACE,IAAM,KAAK,GAAG,IAAI,CAAC,WAAmC,CAAC;YAIvD,IAAM,MAAM,gBAAkB,KAAK,CAAC,aAAa,CAAC,CAAC;YAInD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAInE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aAC3B;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;aACxB;YAID,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAQM,oCAAY,GAAnB,UAAoB,MAAiB;;;YACnC,KAAgB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAArC,IAAM,CAAC,WAAA;gBACV,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACxD;;;;;;;;;;YACD,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAtC,IAAA,KAAA,mBAAS,EAAR,MAAI,QAAA,EAAE,CAAC,QAAA;gBACjB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACjE;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAKS,iCAAS,GAAnB,UAAoB,MAAiB;;;YAInC,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,CAAC,WAAA;gBACV,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACxD;;;;;;;;;;YAID,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,MAAI,WAAA;gBACb,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAI,CAAC,CAAC;gBACnC,IAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;;oBAC/B,KAAgB,IAAA,oBAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA,CAAA,gBAAA,4BAAE;wBAAvC,IAAM,CAAC,WAAA;wBACV,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBACtB,IAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC9B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG;4BAAE,SAAS;wBAClC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;4BAClB,IAAsB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;yBACjC;wBACD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;qBAC9C;;;;;;;;;aACF;;;;;;;;;IACH,CAAC;IAOS,mCAAW,GAArB,UAAsB,MAAiB,EAAE,KAAgB,EAAE,GAAW;;;YACpE,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,gBAAA,4BAAE;gBAAlC,IAAM,MAAI,WAAA;gBACb,IAAM,IAAI,gBAAO,KAAK,CAAC,MAAI,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,GAAG,GAAI,IAAI,CAAC,GAAc,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAI,CAAC,GAAG,IAAI,CAAC;aACrB;;;;;;;;;IACH,CAAC;IASS,0CAAkB,GAA5B,UAA6B,MAAiB,EAAE,CAAS,EAAE,IAAwB;QACjF,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;YAC1B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,sBAAsB,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG;gBAChD,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;aAClC,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAC1B,IAAI,IAAI,CAAC,GAAG,MAAuB,EAAE;YACnC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3C;IACH,CAAC;IASS,2CAAmB,GAA7B,UAA8B,MAAiB,EAAE,CAAS,EAAE,IAAwB;QAClF,IAAM,GAAG,GAAG,IAAI,CAAC,GAAoB,CAAC;QAChC,IAAA,KAAA,OAAuB,IAAI,CAAC,OAAO,IAAA,EAAlC,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAgB,CAAC;QAC1C,IAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnD,IAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,IAAI,GAAG,EAAE;YACP,IAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC9D,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;YACnB,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG;gBAC5C,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC9B,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aAClC,CAAC;SACH;QACD,IAAI,EAAE,EAAE;YACN,GAAG,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;SAC9C;QACD,IAAI,EAAE,EAAE;YACN,GAAG,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,EAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAC,CAAC;SAC5E;QACD,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;YAC3B,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;SACnD;IACH,CAAC;IAUS,yCAAiB,GAA3B,UAA4B,MAAiB,EAAE,CAAS,EAAE,IAAY,EAAE,CAAS,EAAE,GAAkB;QACnG,IAAI,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC;QACjB,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtC,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,IAAM,GAAG,GAAc,EAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAC,CAAC;QACtD,IAAI,IAAI,KAAK,KAAK,EAAE;YAClB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SACtC;aAAM;YACL,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,EAAE;gBACN,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aACpC;SACF;QACD,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC;QACvE,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IASS,2CAAmB,GAA7B,UAA8B,MAAiB,EAAE,CAAS,EAAE,IAAwB;QAC5E,IAAA,KAAA,OAAuB,IAAI,CAAC,OAAO,IAAA,EAAlC,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAA,EAAE,GAAG,QAAgB,CAAC;QAC1C,IAAM,GAAG,GAAG,IAAI,CAAC,GAAoB,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnD,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACnD,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;SAC9D;IACH,CAAC;IASS,yCAAiB,GAA3B,UAA4B,MAAiB,EAAE,CAAS,EAAE,IAAY,EAAE,CAAS,EAAE,GAAkB;QACnG,IAAI,CAAC,CAAC;YAAE,OAAO;QACf,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtC,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAqB,CAAC;QAC5C,IAAM,GAAG,GAAc,EAAC,OAAO,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;QACvG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAoB,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,CAAC,gBAAgB,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC;IACzE,CAAC;IAUS,qCAAa,GAAvB,UAAwB,MAAiB,EAAE,OAAe,EAAE,CAAS,EAAE,IAAmB;QACxF,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAqB,CAAC;QAC5C,IAAM,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/D,IAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnF,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG;YAC9B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/C,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC3E,CAAC;IACJ,CAAC;IAQS,wCAAgB,GAA1B,UAA2B,CAAS;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC;IAMM,0BAAE,GAAT,UAAU,CAAS;QACjB,OAAO,IAAA,eAAE,EAAC,CAAC,CAAC,CAAC;IACf,CAAC;IAMM,2BAAG,GAAV,UAAW,CAAS;QAClB,OAAO,IAAA,eAAE,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAQM,+BAAO,GAAd,UAAe,EAAwB,EAAE,EAAc,EAAE,EAAc;YAAxD,KAAA,aAAwB,EAAvB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA;QAAkB,mBAAA,EAAA,MAAc;QAAE,mBAAA,EAAA,MAAc;QACrE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAQM,mCAAW,GAAlB,UAAmB,CAAS;QAC1B,OAAO,GAAG,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YAClE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,CAAC;IACpF,CAAC;IAOM,oCAAY,GAAnB,UAAoB,CAAS;QAC3B,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IA7Xa,qBAAO,yBAChB,sBAAQ,CAAC,OAAO,KACnB,OAAO,EAAE,mCAAmC,IAC5C;IAKY,iBAAG,GAAG,OAAO,CAAC;IAKX,mCAAqB,GAAc,EAAE,CAAC;IAKtC,mCAAqB,GAAc,EAAE,CAAC;IAKtC,2BAAa,GAAG;QAC/B,eAAe,EAAE;YACf,OAAO,EAAE,OAAO;YAChB,KAAK,EAAE,CAAC;SACT;KACF,CAAC;IAKe,0BAAY,GAAG;QAC9B,oBAAoB,EAAE;YACpB,aAAa,EAAE,SAAS;YACxB,GAAG,EAAE,iDAAiD;SACvD;KACF,CAAC;IAyVJ,oBAAC;CAAA,AAnYD,CAAmC,sBAAQ,GAmY1C;AAnYY,sCAAa;AAuZ1B,SAAgB,MAAM,CAAC,IAAkB,EAAE,OAAuB;;;QAChE,KAAgB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,gBAAA,4BAAE;YAAjC,IAAM,CAAC,WAAA;YACV,IAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,sBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1D;;;;;;;;;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAND,wBAMC"}