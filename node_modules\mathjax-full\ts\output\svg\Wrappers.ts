/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  An object listing all the SVGWrapper classes
 *
 * <AUTHOR> (<PERSON><PERSON>)
 */

import {WrapperConstructor} from '../common/Wrapper.js';
import {SVGWrapper} from './Wrapper.js';
import {SVGmath} from './Wrappers/math.js';
import {SVGmrow, SVGinferredMrow} from './Wrappers/mrow.js';
import {SVGmi} from './Wrappers/mi.js';
import {SVGmo} from './Wrappers/mo.js';
import {SVGmn} from './Wrappers/mn.js';
import {SVGms} from './Wrappers/ms.js';
import {SVGmtext} from './Wrappers/mtext.js';
import {SVGmerror} from './Wrappers/merror.js';
import {SVGmspace} from './Wrappers/mspace.js';
import {SVGmpadded} from './Wrappers/mpadded.js';
import {SVGmphantom} from './Wrappers/mphantom.js';
import {SVGmfrac} from './Wrappers/mfrac.js';
import {SVGmsqrt} from './Wrappers/msqrt.js';
import {SVGmroot} from './Wrappers/mroot.js';
import {SVGmfenced} from './Wrappers/mfenced.js';
import {SVGmsub, SVGmsup, SVGmsubsup} from './Wrappers/msubsup.js';
import {SVGmover, SVGmunder, SVGmunderover} from './Wrappers/munderover.js';
import {SVGmmultiscripts} from './Wrappers/mmultiscripts.js';

import {SVGmtable} from './Wrappers/mtable.js';
import {SVGmtr, SVGmlabeledtr} from './Wrappers/mtr.js';
import {SVGmtd} from './Wrappers/mtd.js';

import {SVGmaction} from './Wrappers/maction.js';
import {SVGmenclose} from './Wrappers/menclose.js';
import {SVGsemantics, SVGannotation, SVGannotationXML, SVGxml} from './Wrappers/semantics.js';
import {SVGmglyph} from './Wrappers/mglyph.js';
import {SVGTeXAtom} from './Wrappers/TeXAtom.js';
import {SVGTextNode} from './Wrappers/TextNode.js';

export const SVGWrappers: {[kind: string]: WrapperConstructor}  = {
  [SVGmath.kind]: SVGmath,
  [SVGmrow.kind]: SVGmrow,
  [SVGinferredMrow.kind]: SVGinferredMrow,
  [SVGmi.kind]: SVGmi,
  [SVGmo.kind]: SVGmo,
  [SVGmn.kind]: SVGmn,
  [SVGms.kind]: SVGms,
  [SVGmtext.kind]: SVGmtext,
  [SVGmerror.kind]: SVGmerror,
  [SVGmspace.kind]: SVGmspace,
  [SVGmpadded.kind]: SVGmpadded,
  [SVGmphantom.kind]: SVGmphantom,
  [SVGmfrac.kind]: SVGmfrac,
  [SVGmsqrt.kind]: SVGmsqrt,
  [SVGmroot.kind]: SVGmroot,
  [SVGmfenced.kind]: SVGmfenced,
  [SVGmsub.kind]: SVGmsub,
  [SVGmsup.kind]: SVGmsup,
  [SVGmsubsup.kind]: SVGmsubsup,
  [SVGmunder.kind]: SVGmunder,
  [SVGmover.kind]: SVGmover,
  [SVGmunderover.kind]: SVGmunderover,
  [SVGmmultiscripts.kind]: SVGmmultiscripts,
  [SVGmtable.kind]: SVGmtable,
  [SVGmtr.kind]: SVGmtr,
  [SVGmlabeledtr.kind]: SVGmlabeledtr,
  [SVGmtd.kind]: SVGmtd,
  [SVGmaction.kind]: SVGmaction,
  [SVGmenclose.kind]: SVGmenclose,
  [SVGsemantics.kind]: SVGsemantics,
  [SVGannotation.kind]: SVGannotation,
  [SVGannotationXML.kind]: SVGannotationXML,
  [SVGxml.kind]: SVGxml,
  [SVGmglyph.kind]: SVGmglyph,
  [SVGTeXAtom.kind]: SVGTeXAtom,
  [SVGTextNode.kind]: SVGTextNode,
  [SVGWrapper.kind]: SVGWrapper
};
