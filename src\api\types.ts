// 消息类型
export type MessageType = 'user' | 'assistant'

// 媒体类型
export type MediaType = 'text' | 'image' | 'video' | null

// 消息接口
export interface Message {
  id: number
  type: MessageType
  content: string
  timestamp: Date
  mediaType?: MediaType
  mediaUrl?: string
}

// 发送消息请求参数
export interface SendMessageRequest {
  message: string
  session_id: string
  image_0?: File | null
}

// 发送消息响应
export interface SendMessageResponse {
  id: number
  type: 'assistant'
  content: string
  timestamp: string
  mediaType: MediaType
  mediaUrl?: string
}

// 上传文件响应
export interface UploadFileResponse {
  url: string
  filename: string
  fileType: string
  fileSize: number
} 