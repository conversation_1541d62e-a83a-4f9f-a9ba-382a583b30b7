import { SemanticRole, SemanticType } from './semantic_meaning.js';
import { SemanticNode } from './semantic_node.js';
export declare function isType(node: SemanticNode, attr: SemanticType): boolean;
export declare function isRole(node: SemanticNode, attr: SemanticRole): boolean;
export declare function isAccent(node: SemanticNode): boolean;
export declare function isSimpleFunctionScope(node: SemanticNode): boolean;
export declare function isPrefixFunctionBoundary(node: SemanticNode): boolean;
export declare function isBigOpBoundary(node: SemanticNode): boolean;
export declare function isIntegralDxBoundary(firstNode: SemanticNode, secondNode: SemanticNode): boolean;
export declare function isIntegralDxBoundarySingle(node: SemanticNode): boolean;
export declare function isGeneralFunctionBoundary(node: SemanticNode): boolean;
export declare function isEmbellished(node: SemanticNode): SemanticType | null;
export declare function isOperator(node: SemanticNode): boolean;
export declare function isRelation(node: SemanticNode): boolean;
export declare function isPunctuation(node: SemanticNode): boolean;
export declare function isFence(node: SemanticNode): boolean;
export declare function isElligibleEmbellishedFence(node: SemanticNode): boolean;
export declare function isTableOrMultiline(node: SemanticNode): boolean;
export declare function tableIsMatrixOrVector(node: SemanticNode): boolean;
export declare function isFencedElement(node: SemanticNode): boolean;
export declare function tableIsCases(_table: SemanticNode, prevNodes: SemanticNode[]): boolean;
export declare function tableIsMultiline(table: SemanticNode): boolean;
export declare function lineIsLabelled(line: SemanticNode): boolean;
export declare function isBinomial(table: SemanticNode): boolean;
export declare function isLimitBase(node: SemanticNode): boolean;
export declare function isSimpleFunctionHead(node: SemanticNode): boolean;
export declare function singlePunctAtPosition(nodes: SemanticNode[], puncts: SemanticNode[], position: number): boolean;
export declare function isSimpleFunction(node: SemanticNode): boolean;
export declare function isSetNode(node: SemanticNode): boolean;
export declare function isSingletonSetContent(node: SemanticNode): boolean;
export declare function isUnitCounter(node: SemanticNode): boolean;
export declare function isPureUnit(node: SemanticNode): boolean;
export declare function isUnitProduct(node: SemanticNode): boolean;
export declare function isImplicit(node: SemanticNode): boolean;
export declare function isImplicitOp(node: SemanticNode): boolean;
export declare function isNeutralFence(fence: SemanticNode): boolean;
export declare function compareNeutralFences(fence1: SemanticNode, fence2: SemanticNode): boolean;
export declare function elligibleLeftNeutral(fence: SemanticNode): boolean;
export declare function elligibleRightNeutral(fence: SemanticNode): boolean;
export declare function isMembership(element: SemanticNode): boolean;
