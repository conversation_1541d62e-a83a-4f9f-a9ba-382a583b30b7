/*************************************************************
 *
 *  Copyright (c) 2017-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import * as Entities from '../Entities.js';

Entities.add({
    QUOT: '\u0022',
    qint: '\u2A0C',
    qprime: '\u2057',
    quaternions: '\u210D',
    quatint: '\u2A16',
    quest: '\u003F',
    questeq: '\u225F'
}, 'q');
