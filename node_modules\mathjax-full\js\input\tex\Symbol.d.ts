import { Args, Attributes, ParseMethod } from './Types.js';
export declare class Symbol {
    private _symbol;
    private _char;
    private _attributes;
    constructor(_symbol: string, _char: string, _attributes: Attributes);
    get symbol(): string;
    get char(): string;
    get attributes(): Attributes;
}
export declare class Macro {
    private _symbol;
    private _func;
    private _args;
    constructor(_symbol: string, _func: ParseMethod, _args?: Args[]);
    get symbol(): string;
    get func(): ParseMethod;
    get args(): Args[];
}
