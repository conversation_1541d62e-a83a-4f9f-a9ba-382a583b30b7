"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.jsdomAdaptor = exports.JsdomAdaptor = void 0;
var HTMLAdaptor_js_1 = require("./HTMLAdaptor.js");
var NodeMixin_js_1 = require("./NodeMixin.js");
var JsdomAdaptor = (function (_super) {
    __extends(JsdomAdaptor, _super);
    function JsdomAdaptor() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return JsdomAdaptor;
}((0, NodeMixin_js_1.NodeMixin)(HTMLAdaptor_js_1.HTMLAdaptor)));
exports.JsdomAdaptor = JsdomAdaptor;
function jsdomAdaptor(JSDOM, options) {
    if (options === void 0) { options = null; }
    return new JsdomAdaptor(new JSDOM().window, options);
}
exports.jsdomAdaptor = jsdomAdaptor;
//# sourceMappingURL=jsdomAdaptor.js.map