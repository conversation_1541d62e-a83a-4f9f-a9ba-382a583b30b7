/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
import {CHTMLCharMap, AddCSS} from '../../FontData.js';
import {sansSerifBold as font} from '../../../common/fonts/tex/sans-serif-bold.js';

export const sansSerifBold: CHTMLCharMap = AddCSS(font, {
    0x2015: {c: '\\2014'},
    0x2017: {c: '_'},
    0x2044: {c: '/'},
    0x2206: {c: '\\394'},
});
