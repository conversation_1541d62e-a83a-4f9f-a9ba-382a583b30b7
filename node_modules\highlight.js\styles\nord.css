pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
 * Copyright (c) 2017-present Arctic Ice Studio <<EMAIL>>
 * Copyright (c) 2017-present <PERSON> <<EMAIL>>
 *
 * Project:    Nord highlight.js
 * Version:    0.1.0
 * Repository: https://github.com/arcticicestudio/nord-highlightjs
 * License:    MIT
 * References:
 *   https://github.com/arcticicestudio/nord
 */
/*

Polar Night

#2E3440
#3B4252
#434C5E
#4C566A

Snow Storm

#D8DEE9
#E5E9F0
#ECEFF4

Frost

#8FBCBB
#88C0D0
#81A1C1
#5E81AC

Aurora

#BF616A
#D08770
#EBCB8B
#A3BE8C
#B48EAD

*/
.hljs {
  background: #2E3440
}
.hljs,
.hljs-subst {
  color: #D8DEE9
}
.hljs-selector-tag {
  color: #81A1C1
}
.hljs-selector-id {
  color: #8FBCBB;
  font-weight: bold
}
.hljs-selector-class {
  color: #8FBCBB
}
.hljs-selector-attr {
  color: #8FBCBB
}
.hljs-property {
  color: #88C0D0
}
.hljs-selector-pseudo {
  color: #88C0D0
}
.hljs-addition {
  background-color: rgba(163, 190, 140, 0.5)
}
.hljs-deletion {
  background-color: rgba(191, 97, 106, 0.5)
}
.hljs-built_in,
.hljs-type {
  color: #8FBCBB
}
.hljs-class {
  color: #8FBCBB
}
.hljs-function {
  color: #88C0D0
}
.hljs-title.hljs-function,
.hljs-function > .hljs-title {
  color: #88C0D0
}
.hljs-keyword,
.hljs-literal,
.hljs-symbol {
  color: #81A1C1
}
.hljs-number {
  color: #B48EAD
}
.hljs-regexp {
  color: #EBCB8B
}
.hljs-string {
  color: #A3BE8C
}
.hljs-title {
  color: #8FBCBB
}
.hljs-params {
  color: #D8DEE9
}
.hljs-bullet {
  color: #81A1C1
}
.hljs-code {
  color: #8FBCBB
}
.hljs-emphasis {
  font-style: italic
}
.hljs-formula {
  color: #8FBCBB
}
.hljs-strong {
  font-weight: bold
}
.hljs-link:hover {
  text-decoration: underline
}
.hljs-quote {
  color: #4C566A
}
.hljs-comment {
  color: #4C566A
}
.hljs-doctag {
  color: #8FBCBB
}
.hljs-meta,
.hljs-meta .hljs-keyword {
  color: #5E81AC
}
.hljs-meta .hljs-string {
  color: #A3BE8C
}
.hljs-attr {
  color: #8FBCBB
}
.hljs-attribute {
  color: #D8DEE9
}
.hljs-name {
  color: #81A1C1
}
.hljs-section {
  color: #88C0D0
}
.hljs-tag {
  color: #81A1C1
}
.hljs-variable {
  color: #D8DEE9
}
.hljs-template-variable {
  color: #D8DEE9
}
.hljs-template-tag {
  color: #5E81AC
}
/* per language customizations */
.language-abnf .hljs-attribute {
  color: #88C0D0
}
.language-abnf .hljs-symbol {
  color: #EBCB8B
}
.language-apache .hljs-attribute {
  color: #88C0D0
}
.language-apache .hljs-section {
  color: #81A1C1
}
.language-arduino .hljs-built_in {
  color: #88C0D0
}
.language-aspectj .hljs-meta {
  color: #D08770
}
.language-aspectj > .hljs-title {
  color: #88C0D0
}
.language-bnf .hljs-attribute {
  color: #8FBCBB
}
.language-clojure .hljs-name {
  color: #88C0D0
}
.language-clojure .hljs-symbol {
  color: #EBCB8B
}
.language-coq .hljs-built_in {
  color: #88C0D0
}
.language-cpp .hljs-meta .hljs-string {
  color: #8FBCBB
}
.language-css .hljs-built_in {
  color: #88C0D0
}
.language-css .hljs-keyword {
  color: #D08770
}
.language-diff .hljs-meta {
  color: #8FBCBB
}
.language-ebnf .hljs-attribute {
  color: #8FBCBB
}
.language-glsl .hljs-built_in {
  color: #88C0D0
}
.language-groovy .hljs-meta:not(:first-child) {
  color: #D08770
}
.language-haxe .hljs-meta {
  color: #D08770
}
.language-java .hljs-meta {
  color: #D08770
}
.language-ldif .hljs-attribute {
  color: #8FBCBB
}
.language-lisp .hljs-name {
  color: #88C0D0
}
.language-lua .hljs-built_in {
  color: #88C0D0
}
.language-moonscript .hljs-built_in {
  color: #88C0D0
}
.language-nginx .hljs-attribute {
  color: #88C0D0
}
.language-nginx .hljs-section {
  color: #5E81AC
}
.language-pf .hljs-built_in {
  color: #88C0D0
}
.language-processing .hljs-built_in {
  color: #88C0D0
}
.language-scss .hljs-keyword {
  color: #81A1C1
}
.language-stylus .hljs-keyword {
  color: #81A1C1
}
.language-swift .hljs-meta {
  color: #D08770
}
.language-vim .hljs-built_in {
  color: #88C0D0;
  font-style: italic
}
.language-yaml .hljs-meta {
  color: #D08770
}