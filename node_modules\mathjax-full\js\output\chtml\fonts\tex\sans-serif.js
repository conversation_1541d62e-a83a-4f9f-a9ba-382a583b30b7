"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sansSerif = void 0;
var FontData_js_1 = require("../../FontData.js");
var sans_serif_js_1 = require("../../../common/fonts/tex/sans-serif.js");
exports.sansSerif = (0, FontData_js_1.AddCSS)(sans_serif_js_1.sansSerif, {
    0x391: { c: 'A' },
    0x392: { c: 'B' },
    0x395: { c: 'E' },
    0x396: { c: 'Z' },
    0x397: { c: 'H' },
    0x399: { c: 'I' },
    0x39A: { c: 'K' },
    0x39C: { c: 'M' },
    0x39D: { c: 'N' },
    0x39F: { c: 'O' },
    0x3A1: { c: 'P' },
    0x3A4: { c: 'T' },
    0x3A7: { c: 'X' },
    0x2015: { c: '\\2014' },
    0x2017: { c: '_' },
    0x2044: { c: '/' },
    0x2206: { c: '\\394' },
});
//# sourceMappingURL=sans-serif.js.map