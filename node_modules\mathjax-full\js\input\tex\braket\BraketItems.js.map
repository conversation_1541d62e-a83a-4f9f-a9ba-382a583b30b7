{"version": 3, "file": "BraketItems.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/braket/BraketItems.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAyBA,gDAA+D;AAC/D,+DAA0D;AAC1D,iEAAwC;AAQxC;IAAgC,8BAAQ;IAAxC;;IAqDA,CAAC;IAhDC,sBAAI,4BAAI;aAAR;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAKD,sBAAI,8BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,8BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACxB,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;gBAC9B,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;aAC/B;YACD,OAAO,uBAAQ,CAAC,IAAI,CAAC;SACtB;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAMM,0BAAK,GAAZ;QACE,IAAI,KAAK,GAAG,iBAAM,KAAK,WAAE,CAAC;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAW,CAAC;QAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,CAAC;QAChD,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YAChC,OAAO,sBAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACzE;QACD,IAAI,KAAK,GAAG,EAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,qBAAQ,CAAC,IAAI,EAAC,CAAC;QACrF,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACvD,KAAK,CAAC,QAAQ,GAAG,qBAAQ,CAAC,KAAK,CAAC;QAChC,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,EAC9C,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,qBAAQ,CAAC,KAAK,EAAC,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC;IAEH,iBAAC;AAAD,CAAC,AArDD,CAAgC,uBAAQ,GAqDvC;AArDY,gCAAU"}