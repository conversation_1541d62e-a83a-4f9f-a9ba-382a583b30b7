{"version": 3, "file": "TextNode.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/TextNode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,+DAA0D;AAC1D,4CAA6D;AAC7D,iEAAsE;AAYtE;IACA,iCAAkE;IADlE;;IAmDA,CAAC;IAtBQ,+BAAO,GAAd,UAAe,MAAS;;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpC,IAAM,IAAI,GAAI,IAAI,CAAC,IAAiB,CAAC,OAAO,EAAE,CAAC;QAC/C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAC9B,IAAI,OAAO,KAAK,eAAe,EAAE;YAC/B,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/E;aAAM;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;gBAC/C,KAAgB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;oBAAlB,IAAM,CAAC,kBAAA;oBACV,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC9C,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACd,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;wBACxD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC,CAAC,CAAC,CAAC;oBAChE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAC7B,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;iBACxD;;;;;;;;;SACF;IACH,CAAC;IA3Ca,kBAAI,GAAG,qBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;IAK/B,uBAAS,GAAG,KAAK,CAAC;IAKlB,oBAAM,GAAc;QAChC,OAAO,EAAE;YACP,OAAO,EAAE,cAAc;SACxB;QACD,WAAW,EAAE;YACX,OAAO,EAAE,cAAc;YACvB,OAAO,EAAE,gBAAgB;SAC1B;KACF,CAAC;IA2BJ,oBAAC;CAAA,AAnDD,CACA,IAAA,iCAAmB,EAAkC,yBAAY,CAAC,GAkDjE;AAnDY,sCAAa"}