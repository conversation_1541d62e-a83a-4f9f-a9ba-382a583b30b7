{"version": 3, "file": "mpadded.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mpadded.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,SAAgB,kBAAkB,CAA+B,IAAO;IAEtE;QAAqB,2BAAI;QAAlB;;QA2EP,CAAC;QAlEQ,2BAAS,GAAhB;YACE,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC7F,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACrC,IAAA,CAAC,GAAU,IAAI,EAAd,EAAE,CAAC,GAAO,IAAI,EAAX,EAAE,CAAC,GAAI,IAAI,EAAR,CAAS;YACrB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,CAAC,KAAK,KAAK,EAAE;gBAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACtE,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE;gBAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACvE,IAAI,MAAM,CAAC,KAAK,KAAK,EAAE;gBAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACtE,IAAI,MAAM,CAAC,OAAO,KAAK,EAAE;gBAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAChE,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE;gBAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC/D,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAW,CAAC;YAC/D,IAAI,KAAK,EAAE;gBACT,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAClD,CAAC;QAYM,uBAAK,GAAZ,UAAa,MAAgB,EAAE,IAAU,EAAE,CAAc,EAAE,CAAgB;YAAhC,kBAAA,EAAA,MAAc;YAAE,kBAAA,EAAA,QAAgB;YACzE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YACxB,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACjD,IAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAiB,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAW,CAAC;YAC3D,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAChD,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC9B,KAAK,IAAI,IAAI,CAAC;aACf;YACD,IAAI,CAAC,IAAI,IAAI,EAAE;gBACb,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5B;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YACjD,IAAA,KAAA,OAAwB,IAAI,CAAC,SAAS,EAAE,IAAA,EAAvC,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAoB,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QAKM,8BAAY,GAAnB,UAAoB,EAAU;YAC5B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC1B,CAAC;QAKM,+BAAa,GAApB,UAAqB,EAAU;YAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAW,IAAI,MAAM,CAAC;QACpE,CAAC;QACH,cAAC;IAAD,CAAC,AA3EM,CAAc,IAAI,GA2EvB;AAEJ,CAAC;AA/ED,gDA+EC"}