{"version": 3, "file": "mfrac.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mfrac.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAA6D;AAC7D,2DAAgE;AAChE,oEAAiE;AAajE;IAAyC,8BAA+D;IAAxG;;IA4OA,CAAC;IAnIQ,4BAAO,GAAd,UAAe,MAAS;QACtB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzB,IAAA,KAA4B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,EAApF,aAAa,mBAAA,EAAE,QAAQ,cAA6D,CAAC;QAC5F,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC5B;aAAM;YACL,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;YAC7D,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aACxB;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aACvC;SACF;IACH,CAAC;IAQS,iCAAY,GAAtB,UAAuB,OAAgB,EAAE,CAAS;QAC1C,IAAA,KAAyB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAA9E,QAAQ,cAAA,EAAE,UAAU,gBAA0D,CAAC;QACtF,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAIvD,IAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,GAAG,EAAC,CAAC,CAAC,CAAC,EAAE,CAAe,CAAC;QACxD,IAAM,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC,uBAAK,IAAI,KAAE,MAAM,EAAE,MAAM,IAAE,CAAC,cAAK,IAAI,CAAC,CAAe,CAAC;QACjF,IAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,CAAe,CAAC;QAC7E,IAAM,KAAK,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC,CAAC,EAAE,CAAe,CAAC;QACjF,IAAM,MAAM,gBAAO,IAAI,CAAC,EAAE,MAAM,gBAAO,IAAI,CAAC,CAAC;QAI7C,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,EAAE;YACb,IAAM,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC;YAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,IAAA,KAAY,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAlC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAA2B,CAAC;YAC1C,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YACtD,IAAI,CAAC,KAAK,GAAG,EAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,GAAG,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC;YACpE,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,GAAG,EAAC,MAAM,EAAE,EAAE,EAAE,gBAAgB,EAAE,GAAG,GAAG,EAAE,EAAC,CAAC;YACxD,MAAM,CAAC,KAAK,GAAG,EAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC;YACjD,KAAK,CAAC,KAAK,GAAI,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC;SACnD;QAID,IAAI,GAAG,EAAE,GAAG,CAAC;QACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;YAC3D,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE;wBACvB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;qBACrE,CAAC;iBACH,CAAC;aACH,CAAC;SACH,CAAC,CAAC,CAAC;QACJ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAOS,6BAAQ,GAAlB,UAAmB,OAAgB;QAC3B,IAAA,KAAyB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,EAA9E,QAAQ,cAAA,EAAE,UAAU,gBAA0D,CAAC;QACtF,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAIvD,IAAM,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC,EAAC,IAAI,EAAE,IAAI,EAAC,CAAe,CAAC;QAC9E,IAAM,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC,uBAAK,IAAI,KAAE,MAAM,EAAE,IAAI,IAAE,CAAC,cAAK,IAAI,CAAC,CAAe,CAAC;QAC/E,IAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,CAAe,CAAC;QAC7E,IAAM,KAAK,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC,CAAC,EAAE,CAAe,CAAC;QAI3E,IAAA,KAAS,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAA5B,CAAC,OAAA,EAAE,CAAC,OAAwB,CAAC;QACpC,KAAK,CAAC,KAAK,GAAG,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC;QAC7C,KAAK,CAAC,KAAK,GAAG,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;QAI9C,IAAI,GAAG,EAAE,GAAG,CAAC;QACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;YAC3D,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;YACjC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;SAClC,CAAC,CAAC,CAAC;QACJ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAOS,iCAAY,GAAtB,UAAuB,OAAgB;QACrC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAI7B,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QACrD,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAI1B,IAAA,KAA4B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAArD,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAA8B,CAAC;QAC7D,IAAI,CAAC,EAAE;YACL,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,EAAE;YACL,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SACjE;QACD,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC/B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;QACrD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IArOa,eAAI,GAAG,mBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;IAK/B,iBAAM,GAAc;QAChC,UAAU,EAAE;YACV,OAAO,EAAE,cAAc;YACvB,gBAAgB,EAAE,QAAQ;YAC1B,OAAO,EAAE,SAAS;SACnB;QACD,oBAAoB,EAAE;YACpB,gBAAgB,EAAE,OAAO;SAC1B;QACD,kBAAkB,EAAE;YAClB,OAAO,EAAE,QAAQ;SAClB;QACD,gBAAgB,EAAE;YAChB,OAAO,EAAE,SAAS;SACnB;QACD,wBAAwB,EAAE;YACxB,OAAO,EAAE,GAAG;SACb;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE,MAAM;SACd;QACD,gBAAgB,EAAE;YAChB,WAAW,EAAE,OAAO;SACrB;QACD,UAAU,EAAE;YACV,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;SAClB;QACD,SAAS,EAAE;YACT,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,QAAQ;SACvB;QACD,SAAS,EAAE;YACT,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,QAAQ;SACvB;QACD,+BAA+B,EAAE;YAC/B,OAAO,EAAE,cAAc;SACxB;QACD,+BAA+B,EAAE;YAC/B,OAAO,EAAE,cAAc;SACxB;QAED,gDAAgD,EAAE;YAChD,YAAY,EAAE,OAAO;SACtB;QACD,8CAA8C,EAAE;YAC9C,YAAY,EAAE,MAAM;SACrB;QAED,YAAY,EAAE;YACZ,OAAO,EAAE,cAAc;YACvB,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,CAAC;YACR,gBAAgB,EAAE,SAAS;SAC5B;QACD,sBAAsB,EAAE;YACtB,MAAM,EAAE,QAAQ;YAChB,gBAAgB,EAAE,SAAS;SAC5B;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,cAAc;YACvB,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,CAAC;SACT;QACD,sBAAsB,EAAE;YACtB,MAAM,EAAE,QAAQ;SACjB;QAED,UAAU,EAAE;YACV,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,YAAY;YAC1B,YAAY,EAAE,KAAK;YACnB,MAAM,EAAE,OAAO;YACf,YAAY,EAAE,aAAa;YAC3B,MAAM,EAAE,aAAa;YACrB,QAAQ,EAAE,QAAQ;SACnB;QACD,oBAAoB,EAAE;YACpB,MAAM,EAAE,aAAa;SACtB;KAEF,CAAC;IA+IJ,iBAAC;CAAA,AA5OD,CAAyC,IAAA,2BAAgB,EAAkC,yBAAY,CAAC,GA4OvG;AA5OY,gCAAU"}