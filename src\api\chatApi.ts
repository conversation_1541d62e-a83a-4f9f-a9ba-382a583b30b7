import axios from './axios'
import { sessionManager } from './sessionManager'
import { handleStreamResponse } from './streamUtils'
import { SendMessageRequest, SendMessageResponse, UploadFileResponse } from './types'

/**
 * 处理可能包含特殊字符的内容
 * @param content 原始内容
 * @returns 处理后的内容
 */
function processSpecialContent(content: string): string {
  // 处理转义字符
  let processed = content
    .replace(/\\n/g, '\n')  // 换行符
    .replace(/\\t/g, '\t')  // 制表符
    .replace(/\\r/g, '\r')  // 回车符
    .replace(/\\"/g, '"')   // 双引号
    .replace(/\\'/g, "'")   // 单引号
    .replace(/\\\\/g, '\\') // 反斜杠
  
  // 处理Unicode转义序列，如 \u0000 到 \uffff
  processed = processed.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) => {
    return String.fromCharCode(parseInt(hex, 16))
  })
  
  return processed
}

/**
 * 聊天API服务
 */
export const chatApi = {
  /**
   * 发送消息
   * @param data 消息内容
   * @param onChunk 每收到一块数据时的回调函数
   * @returns 
   */
  sendMessage: async (
    data: SendMessageRequest, 
    onChunk?: (chunk: string) => void
  ): Promise<SendMessageResponse> => {
    try {
      // 创建FormData对象
      const formData = new FormData()
      formData.append('message', data.message)
      formData.append('session_id', data.session_id)
      
      // 如果有图片文件，添加到请求中
      if (data.image_0) {
        formData.append('image_0', data.image_0)
      }
      
      // 如果提供了onChunk回调，使用流式处理
      if (onChunk) {
        // 使用fetch API进行流式请求
        const response = await fetch('/api/chat', {
          method: 'POST',
          body: formData,
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        // 创建一个Promise，在流处理完成后解析
        return new Promise((resolve) => {
          // 存储完整的响应内容
          let fullContent = ''
          
          // 处理流式响应
          handleStreamResponse(
            response,
            (chunk) => {
              // 处理特殊字符和数学公式
              // const processedChunk = processSpecialContent(chunk)
              // fullContent += processedChunk
              // onChunk(processedChunk)
              const processedChunk = chunk
              fullContent += processedChunk
              onChunk(processedChunk)
            
            },
            () => {
              // 流处理完成，返回完整响应
              resolve({
                id: Date.now(),
                type: 'assistant',
                content: fullContent,
                timestamp: new Date().toISOString(),
                mediaType: 'text'
              })
            }
          ).catch(error => {
            console.error('流处理错误:', error)
            // 出错时也要解析Promise，避免卡住UI
            resolve({
              id: Date.now(),
              type: 'assistant',
              content: fullContent || '抱歉，处理响应时出错。',
              timestamp: new Date().toISOString(),
              mediaType: 'text'
            })
          })
        })
      } else {
        // 不需要流式处理，使用普通axios请求
        // 发送请求
        const response = await axios.post('/api/chat', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        // 处理响应数据
        let content = typeof response === 'string' ? response : JSON.stringify(response)
        // content = processSpecialContent(content)
        
        return {
          id: Date.now(),
          type: 'assistant',
          content: content,
          timestamp: new Date().toISOString(),
          mediaType: 'text'
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      // 返回模拟数据，实际项目中应该抛出错误
      return {
        id: Date.now(),
        type: 'assistant',
        content: '抱歉，消息发送失败，请稍后再试。',
        timestamp: new Date().toISOString(),
        mediaType: 'text'
      }
    }
  },

  /**
   * 上传文件
   * @param file 文件对象
   * @returns 上传结果
   */
  uploadFile: async (file: File): Promise<UploadFileResponse> => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      return await axios.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    } catch (error) {
      console.error('文件上传失败:', error)
      // 返回模拟数据，实际项目中应该抛出错误
      return {
        url: URL.createObjectURL(file), // 本地预览URL
        filename: file.name,
        fileType: file.type,
        fileSize: file.size
      }
    }
  }
} 