{"version": 3, "file": "ColortblConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/colortbl/ColortblConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAwBA,qDAA+C;AAC/C,wDAA6F;AAC7F,gDAA2C;AAE3C,+DAAsC;AAiBtC;IAAoC,kCAAS;IAA7C;QAAA,qEAyDC;QArDQ,WAAK,GAAc;YACxB,IAAI,EAAE,EAAE;YACR,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,EAAE;SACR,CAAC;QAKK,cAAQ,GAAY,KAAK,CAAC;;IA4CnC,CAAC;IAvCQ,iCAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvF,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAKM,+BAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;IACtB,CAAC;IAKM,kCAAS,GAAhB;QAME,IAAM,GAAG,GAAG,iBAAM,SAAS,WAAE,CAAC;QAC9B,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAY,CAAC;QACtE,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;YAC5B,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;YAC7D,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACnC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEH,qBAAC;AAAD,CAAC,AAzDD,CAAoC,wBAAS,GAyD5C;AAzDY,wCAAc;AA8D3B,IAAI,yBAAU,CAAC,UAAU,EAAE;IACzB,SAAS,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;IACjC,QAAQ,EAAG,CAAC,YAAY,EAAE,KAAK,CAAC;IAChC,WAAW,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;CACnC,EAAE;IAQD,UAAU,EAAV,UAAW,MAAiB,EAAE,IAAY,EAAE,IAAqB;QAC/D,IAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;QACnE,IAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3C,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QAI/D,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAoB,CAAC;QACjD,IAAI,CAAC,CAAC,GAAG,YAAY,cAAc,CAAC,EAAE;YACpC,MAAM,IAAI,qBAAQ,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;SACxF;QAID,IAAI,IAAI,KAAK,KAAK,EAAE;YAClB,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;gBACpB,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAAE,2BAA2B,EAAE,IAAI,CAAC,CAAC;aAC5E;YACD,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;YAItC,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;gBAChC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;aAC9B;SACF;aAAM;YACL,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACxB,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACpD,MAAM,IAAI,qBAAQ,CAAC,kBAAkB,EAAE,sCAAsC,EAAE,IAAI,CAAC,CAAC;aACtF;SACF;IACH,CAAC;CACF,CAAC,CAAC;AAQH,IAAM,MAAM,GAAG,UAAU,MAA2B,EAAE,GAAuB;IAI3E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QAC9C,uCAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACvD;AACH,CAAC,CAAC;AAKW,QAAA,qBAAqB,GAAG,gCAAa,CAAC,MAAM,CAAC,UAAU,EAAE;IACpE,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,UAAU,CAAC,EAAC;IAC9B,KAAK,EAAE,EAAC,OAAO,EAAE,cAAc,EAAC;IAChC,QAAQ,EAAE,EAAE;IACZ,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC;CACrB,CAAC,CAAC"}