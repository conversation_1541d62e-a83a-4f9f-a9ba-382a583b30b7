{"version": 3, "sources": ["../../mathjax-full/ts/util/LinkedList.ts", "../../mathjax-full/ts/core/MathList.ts", "../../mathjax-full/ts/core/Tree/NodeFactory.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mstyle.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/maligngroup.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/malignmark.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mathchoice.ts", "../../mathjax-full/ts/core/MmlTree/MML.ts", "../../mathjax-full/ts/core/MmlTree/MmlFactory.ts", "../../mathjax-full/ts/util/BitField.ts", "../../mathjax-full/ts/core/MathDocument.ts", "../../mathjax-full/ts/core/Handler.ts", "../../mathjax-full/ts/handlers/html/HTMLMathItem.ts", "../../mathjax-full/ts/handlers/html/HTMLMathList.ts", "../../mathjax-full/ts/handlers/html/HTMLDomStrings.ts", "../../mathjax-full/ts/handlers/html/HTMLDocument.ts", "../../mathjax-full/ts/handlers/html/HTMLHandler.ts", "../../mathjax-full/ts/handlers/html.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implement a generic LinkedList object.\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n/*****************************************************************/\n/**\n *  A symbol used to mark the special node used to indicate\n *  the start and end of the list.\n */\nexport const END = Symbol();\n\n/**\n * Shorthand type for the functions used to sort the data items\n *\n * @template DataClass   The type of data stored in the list\n */\nexport type SortFn<DataClass> = (a: DataClass, b: DataClass) => boolean;\n\n/*****************************************************************/\n/**\n *  The ListItem interface (for a specific type of data item)\n *\n *  These are the items in the doubly-linked list.\n *\n * @template DataClass   The type of data stored in the list\n */\n\nexport class ListItem<DataClass> {\n  /**\n   * The data for the list item\n   */\n  public data: DataClass | symbol;\n\n  /**\n   * Pointers to the next item in the list\n   */\n  public next: ListItem<DataClass> = null;\n  /**\n   * Pointers to the previous item in the list\n   */\n  public prev: ListItem<DataClass> = null;\n\n  /**\n   * @param {any} data  The data to be stored in the list item\n   * @constructor\n   */\n  constructor(data: any = null) {\n    this.data = data;\n  }\n}\n\n/*****************************************************************/\n/**\n *  Implements the generic LinkedList class\n *\n * @template DataClass   The type of data stored in the list\n */\n\nexport class LinkedList<DataClass> {\n  /**\n   * The linked list\n   */\n  protected list: ListItem<DataClass>;\n\n  /**\n   *  This.list is a special ListItem whose next property\n   *    points to the head of the list and whose prev\n   *    property points to the tail.  This lets us relink\n   *    the head and tail items in the same way as any other\n   *    item in the list, without having to handle special\n   *    cases.\n   *\n   * @param {DataClass[]} args  The data items that form the initial list\n   * @constructor\n   */\n  constructor(...args: DataClass[]) {\n    this.list = new ListItem<DataClass>(END);\n    this.list.next = this.list.prev = this.list;\n    this.push(...args);\n  }\n\n  /**\n   *  Used for sorting and merging lists (Overridden by subclasses)\n   *\n   * @param {DataClass} a   The first item to compare\n   * @param {DataClass} b   The second item to compare\n   * @return {boolean}      True if a is before b, false otherwise\n   */\n  public isBefore(a: DataClass, b: DataClass): boolean {\n    return a < b;\n  }\n\n  /**\n   * Push items on the end of the list\n   *\n   * @param {DataClass[]} args   The list of data items to be pushed\n   * @return {LinkedList}        The LinkedList object (for chaining)\n   */\n  public push(...args: DataClass[]): LinkedList<DataClass> {\n    for (const data of args) {\n      let item = new ListItem<DataClass>(data);\n      item.next = this.list;\n      item.prev = this.list.prev;\n      this.list.prev = item;\n      item.prev.next = item;\n    }\n    return this;\n  }\n\n  /**\n   * Pop the end item off the list and return its data\n   *\n   * @return {DataClass}  The data from the last item in the list\n   */\n  public pop(): DataClass {\n    let item = this.list.prev;\n    if (item.data === END) {\n      return null;\n    }\n    this.list.prev = item.prev;\n    item.prev.next = this.list;\n    item.next = item.prev = null;\n    return item.data as DataClass;\n  }\n\n  /**\n   * Push items at the head of the list\n   *\n   * @param {DataClass[]} args   The list of data items to inserted\n   * @return {LinkedList}        The LinkedList object (for chaining)\n   */\n  public unshift(...args: DataClass[]): LinkedList<DataClass> {\n    for (const data of args.slice(0).reverse()) {\n      let item = new ListItem<DataClass>(data);\n      item.next = this.list.next;\n      item.prev = this.list;\n      this.list.next = item;\n      item.next.prev = item;\n    }\n    return this;\n  }\n\n  /**\n   * Remove an item from the head of the list and return its data\n   *\n   * @return {DataClass}  The data from the first item in the list\n   */\n  public shift(): DataClass {\n    let item = this.list.next;\n    if (item.data === END) {\n      return null;\n    }\n    this.list.next = item.next;\n    item.next.prev = this.list;\n    item.next = item.prev = null;\n    return item.data as DataClass;\n  }\n\n  /**\n   * Remove items from the list\n   *\n   * @param {DataClass[]} items   The items to remove\n   */\n  public remove(...items: DataClass[]) {\n    const map = new Map<DataClass, boolean>();\n    for (const item of items) {\n      map.set(item, true);\n    }\n    let item = this.list.next;\n    while (item.data !== END) {\n      const next = item.next;\n      if (map.has(item.data as DataClass)) {\n        item.prev.next = item.next;\n        item.next.prev = item.prev;\n        item.next = item.prev = null;\n      }\n      item = next;\n    }\n  }\n\n  /**\n   * Empty the list\n   *\n   * @return {LinkedList}  The LinkedList object (for chaining)\n   */\n  public clear(): LinkedList<DataClass> {\n    this.list.next.prev = this.list.prev.next = null;\n    this.list.next = this.list.prev = this.list;\n    return this;\n  }\n\n  /**\n   * An iterator for the list in forward order\n   *\n   * @yield {DataClass} The next item in the iteration sequence\n   */\n  public *[Symbol.iterator](): IterableIterator<DataClass> {\n    let current = this.list.next;\n\n    while (current.data !== END) {\n      yield current.data as DataClass;\n      current = current.next;\n    }\n  }\n\n  /**\n   * An iterator for the list in reverse order\n   *\n   * @yield {DataClass} The previous item in the iteration sequence\n   */\n  public *reversed(): IterableIterator<DataClass> {\n    let current = this.list.prev;\n\n    while (current.data !== END) {\n      yield current.data as DataClass;\n      current = current.prev;\n    }\n  }\n\n  /**\n   * Insert a new item into a sorted list in the correct locations\n   *\n   * @param {DataClass} data   The data item to add\n   * @param {SortFn} isBefore   The function used to order the data\n   * @param {LinkedList}        The LinkedList object (for chaining)\n   */\n  public insert(data: DataClass, isBefore: SortFn<DataClass> = null) {\n    if (isBefore === null) {\n      isBefore = this.isBefore.bind(this);\n    }\n    let item = new ListItem<DataClass>(data);\n    let cur = this.list.next;\n    while (cur.data !== END && isBefore(cur.data as DataClass, item.data as DataClass)) {\n      cur = cur.next;\n    }\n    item.prev = cur.prev;\n    item.next = cur;\n    cur.prev.next = cur.prev = item;\n    return this;\n  }\n\n  /**\n   * Sort the list using an optional sort function\n   *\n   * @param {SortFn} isBefore  The function used to order the data\n   * @return {LinkedList}      The LinkedList object (for chaining)\n   */\n  public sort(isBefore: SortFn<DataClass> = null): LinkedList<DataClass> {\n    if (isBefore === null) {\n      isBefore = this.isBefore.bind(this);\n    }\n    //\n    //  Make an array of singleton lists\n    //\n    let lists: LinkedList<DataClass>[] = [];\n    for (const item of this) {\n      lists.push(new LinkedList<DataClass>(item as DataClass));\n    }\n    //\n    //  Clear current list\n    //\n    this.list.next = this.list.prev = this.list;\n    //\n    //  Merge pairs of lists until there is only one left\n    //\n    while (lists.length > 1) {\n      let l1 = lists.shift();\n      let l2 = lists.shift();\n      l1.merge(l2, isBefore);\n      lists.push(l1);\n    }\n    //\n    //  Use the final list as our list\n    //\n    if (lists.length) {\n      this.list = lists[0].list;\n    }\n    return this;\n  }\n\n  /**\n   * Merge a sorted list with another sorted list\n   *\n   * @param {LinkedList} list  The list to merge into this instance's list\n   * @param {SortFn} isBefore  The function used to order the data\n   * @return {LinkedList}      The LinkedList instance (for chaining)\n   */\n  public merge(list: LinkedList<DataClass>, isBefore: SortFn<DataClass> = null): LinkedList<DataClass> {\n    if (isBefore === null) {\n      isBefore = this.isBefore.bind(this);\n    }\n    //\n    //  Get the head of each list\n    //\n    let lcur = this.list.next;\n    let mcur = list.list.next;\n    //\n    //  While there is more in both lists\n    //\n    while (lcur.data !== END && mcur.data !== END) {\n      //\n      //  If the merge item is before the list item\n      //    (we have found where the head of the merge list belongs)\n      //    Link the merge list into the main list at this point\n      //      and make the merge list be the remainder of the original list.\n      //    The merge continues by looking for where the rest of the original\n      //      list fits into the newly formed main list (the old merge list).\n      //  Otherwise\n      //    Go on to the next item in the main list\n      //\n      if (isBefore(mcur.data as DataClass, lcur.data as DataClass)) {\n        [mcur.prev.next, lcur.prev.next] = [lcur, mcur];\n        [mcur.prev, lcur.prev] = [lcur.prev, mcur.prev];\n        [this.list.prev.next, list.list.prev.next] = [list.list, this.list];\n        [this.list.prev, list.list.prev] = [list.list.prev, this.list.prev];\n        [lcur, mcur] = [mcur.next, lcur];\n      } else {\n        lcur = lcur.next;\n      }\n    }\n    //\n    //  If there is more to be merged (i.e., we came to the end of the main list),\n    //  then link that at the end of the main list.\n    //\n    if (mcur.data !== END) {\n      this.list.prev.next = list.list.next;\n      list.list.next.prev = this.list.prev;\n      list.list.prev.next = this.list;\n      this.list.prev = list.list.prev;\n      list.list.next = list.list.prev = list.list;\n    }\n    return this;\n  }\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for MathList objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {LinkedList} from '../util/LinkedList.js';\nimport {MathItem} from './MathItem.js';\n\n/*****************************************************************/\n/**\n *  The MathList interface (extends LinkedList<MathItem>)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface MathList<N, T, D> extends LinkedList<MathItem<N, T, D>> {\n  /**\n   * Test if one math item is before the other in the document (a < b)\n   *\n   * @param {MathItem} a   The first MathItem\n   * @param {MathItem} b   The second MathItem\n   */\n  isBefore(a: MathItem<N, T, D>, b: MathItem<N, T, D>): boolean;\n}\n\n/*****************************************************************/\n/**\n *  The MathList abstract class (extends LinkedList<MathItem>)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractMathList<N, T, D> extends\nLinkedList<MathItem<N, T, D>> implements MathList<N, T, D> {\n\n  /**\n   * @override\n   */\n  public isBefore(a: MathItem<N, T, D>, b: MathItem<N, T, D>) {\n    return (a.start.i < b.start.i || (a.start.i === b.start.i && a.start.n < b.start.n));\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  The generic NodeFactory class for creating Node objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {Node, PropertyList} from './Node.js';\nimport {Factory, FactoryNodeClass, AbstractFactory} from './Factory.js';\n\n/*****************************************************************/\n/**\n * The NodeFactory interface\n *\n * @template N  The node type created by the factory\n * @template C  The class of the node being constructed (for access to static properties)\n */\nexport interface NodeFactory<N extends Node, C extends FactoryNodeClass<N>> extends Factory<N, C> {\n  /**\n   * @param {string} kind  The kind of node to create\n   * @param {PropertyList} properties  The list of initial properties for the node (if any)\n   * @param {N[]} children  The array of initial child nodes (if any)\n   * @return {N}  The newly created node of the given kind\n   */\n  create(kind: string, properties?: PropertyList, children?: N[]): N;\n}\n\n/*****************************************************************/\n/**\n * The generic NodeFactory class\n *\n * @template N  The node type created by the factory\n * @template C  The class of the node being constructed (for access to static properties)\n */\nexport abstract class AbstractNodeFactory<N extends Node, C extends FactoryNodeClass<N>> extends AbstractFactory<N, C> {\n  /**\n   * @override\n   */\n  public create(kind: string, properties: PropertyList = {}, children: N[] = []) {\n    return this.node[kind](properties, children);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMstyle node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlLayoutNode, AttributeList} from '../MmlNode.js';\nimport {INHERIT} from '../Attributes.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMstyle node class (subclass of AbstractMmlLayoutNode)\n */\n\nexport class MmlMstyle extends AbstractMmlLayoutNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlLayoutNode.defaults,\n    scriptlevel: INHERIT,\n    displaystyle: INHERIT,\n    scriptsizemultiplier: 1 / Math.sqrt(2),\n    scriptminsize: '8px',  // should be 8pt, but that is too large\n    mathbackground: INHERIT,\n    mathcolor: INHERIT,\n    dir: INHERIT,\n    infixlinebreakstyle: 'before'\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mstyle';\n  }\n\n  /**\n   * @override\n   */\n  public get notParent() {\n    return this.childNodes[0] && this.childNodes[0].childNodes.length === 1;\n  }\n\n  /**\n   * Handle scriptlevel changes, and add mstyle attributes to the ones being inherited.\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    let scriptlevel = this.attributes.getExplicit('scriptlevel');\n    if (scriptlevel != null) {\n      scriptlevel = scriptlevel.toString();\n      if (scriptlevel.match(/^\\s*[-+]/)) {\n        level += parseInt(scriptlevel);\n      } else {\n        level = parseInt(scriptlevel);\n      }\n      prime = false;  // style change resets tex prime style\n    }\n    let displaystyle = this.attributes.getExplicit('displaystyle') as boolean;\n    if (displaystyle != null) {\n      display = (displaystyle === true);\n      prime = false;  // style change resets tex prime style\n    }\n    const cramped = this.attributes.getExplicit('data-cramped') as boolean;  // manual control of tex prime style\n    if (cramped != null) {\n      prime = cramped;\n    }\n    attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n    this.childNodes[0].setInheritedAttributes(attributes, display, level, prime);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMaligngroup node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlLayoutNode, AttributeList} from '../MmlNode.js';\nimport {INHERIT} from '../Attributes.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMaligngroup node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMaligngroup extends AbstractMmlLayoutNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlLayoutNode.defaults,\n    groupalign: INHERIT\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'maligngroup';\n  }\n\n  /**\n   * <maligngroup> is space-like\n   * @override\n   */\n  public get isSpacelike() {\n    return true;\n  }\n\n  /**\n   * Children can inherit from <maligngroup>\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n    super.setChildInheritedAttributes(attributes, display, level, prime);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMalignmark node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlNode} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMalignmark node class (subclass of AbstractMmlNode)\n */\n\nexport class MmlMalignmark extends AbstractMmlNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlNode.defaults,\n    edge: 'left'\n  };\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'malignmark';\n  }\n\n  /**\n   * No children allowed\n   * @override\n   */\n  public get arity() {\n    return 0;\n  }\n\n  /**\n   * <malignmark> is space-like\n   * @override\n   */\n  public get isSpacelike() {\n    return true;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MathChoice node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlBaseNode, AttributeList} from '../MmlNode.js';\n\n/*****************************************************************/\n/**\n *  Implements the MathChoice node class (subclass of AbstractMmlBaseNode)\n *\n *  This is used by TeX's \\mathchoice macro, but removes itself\n *  during the setInheritedAttributes process\n */\n\nexport class MathChoice extends AbstractMmlBaseNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlBaseNode.defaults\n  };\n\n  /**\n   *  @override\n   */\n  public get kind() {\n    return 'MathChoice';\n  }\n\n  /**\n   * 4 children (display, text, script, and scriptscript styles)\n   * @override\n   */\n  public get arity() {\n    return 4;\n  }\n\n  /**\n   * This element is not considered a MathML container\n   * @override\n   */\n  public get notParent() {\n    return true;\n  }\n\n  /**\n   * Replace the MathChoice node with the selected on based on the displaystyle and scriptlevel settings\n   * (so the MathChoice never ends up in a finished MmlNode tree)\n   *\n   * @override\n   */\n  public setInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    const selection = (display ? 0 : Math.max(0, Math.min(level, 2)) + 1);\n    const child = this.childNodes[selection] || this.factory.create('mrow');\n    this.parent.replaceChild(child, this);\n    child.setInheritedAttributes(attributes, display, level, prime);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  An object listing all the MathML node types\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {MmlNodeClass, TextNode, XMLNode} from './MmlNode.js';\n\nimport {MmlMath}      from './MmlNodes/math.js';\n\nimport {MmlMi}        from './MmlNodes/mi.js';\nimport {MmlMn}        from './MmlNodes/mn.js';\nimport {MmlMo}        from './MmlNodes/mo.js';\nimport {MmlMtext}     from './MmlNodes/mtext.js';\nimport {MmlMspace}    from './MmlNodes/mspace.js';\nimport {MmlMs}        from './MmlNodes/ms.js';\n\nimport {MmlMrow, MmlInferredMrow} from './MmlNodes/mrow.js';\nimport {MmlMfrac}     from './MmlNodes/mfrac.js';\nimport {MmlMsqrt}     from './MmlNodes/msqrt.js';\nimport {MmlMroot}     from './MmlNodes/mroot.js';\nimport {MmlMstyle}    from './MmlNodes/mstyle.js';\nimport {MmlMerror}    from './MmlNodes/merror.js';\nimport {MmlMpadded}   from './MmlNodes/mpadded.js';\nimport {MmlMphantom}  from './MmlNodes/mphantom.js';\nimport {MmlMfenced}   from './MmlNodes/mfenced.js';\nimport {MmlMenclose}  from './MmlNodes/menclose.js';\n\nimport {MmlMaction}   from './MmlNodes/maction.js';\n\nimport {MmlMsubsup, MmlMsub, MmlMsup}       from './MmlNodes/msubsup.js';\nimport {MmlMunderover, MmlMunder, MmlMover} from './MmlNodes/munderover.js';\nimport {MmlMmultiscripts, MmlMprescripts, MmlNone} from './MmlNodes/mmultiscripts.js';\n\nimport {MmlMtable}      from './MmlNodes/mtable.js';\nimport {MmlMtr, MmlMlabeledtr} from './MmlNodes/mtr.js';\nimport {MmlMtd}         from './MmlNodes/mtd.js';\nimport {MmlMaligngroup} from './MmlNodes/maligngroup.js';\nimport {MmlMalignmark}  from './MmlNodes/malignmark.js';\n\nimport {MmlMglyph}      from './MmlNodes/mglyph.js';\n\nimport {MmlSemantics, MmlAnnotation, MmlAnnotationXML} from './MmlNodes/semantics.js';\n\nimport {TeXAtom} from './MmlNodes/TeXAtom.js';\nimport {MathChoice} from './MmlNodes/mathchoice.js';\n\n/************************************************************************/\n/**\n *  This object collects all the MathML node types together so that\n *  they can be used to seed an MmlNodeFactory.  One could copy this\n *  object to override existing classes with subclasses, or to add new\n *  classes as necessary.\n */\n\nexport let MML: {[kind: string]: MmlNodeClass} = {\n  [MmlMath.prototype.kind]: MmlMath,\n\n  [MmlMi.prototype.kind]: MmlMi,\n  [MmlMn.prototype.kind]: MmlMn,\n  [MmlMo.prototype.kind]: MmlMo,\n  [MmlMtext.prototype.kind]: MmlMtext,\n  [MmlMspace.prototype.kind]: MmlMspace,\n  [MmlMs.prototype.kind]: MmlMs,\n\n  [MmlMrow.prototype.kind]: MmlMrow,\n  [MmlInferredMrow.prototype.kind]: MmlInferredMrow,\n  [MmlMfrac.prototype.kind]: MmlMfrac,\n  [MmlMsqrt.prototype.kind]: MmlMsqrt,\n  [MmlMroot.prototype.kind]: MmlMroot,\n  [MmlMstyle.prototype.kind]: MmlMstyle,\n  [MmlMerror.prototype.kind]: MmlMerror,\n  [MmlMpadded.prototype.kind]: MmlMpadded,\n  [MmlMphantom.prototype.kind]: MmlMphantom,\n  [MmlMfenced.prototype.kind]: MmlMfenced,\n  [MmlMenclose.prototype.kind]: MmlMenclose,\n\n  [MmlMaction.prototype.kind]: MmlMaction,\n\n  [MmlMsub.prototype.kind]: MmlMsub,\n  [MmlMsup.prototype.kind]: MmlMsup,\n  [MmlMsubsup.prototype.kind]: MmlMsubsup,\n  [MmlMunder.prototype.kind]: MmlMunder,\n  [MmlMover.prototype.kind]: MmlMover,\n  [MmlMunderover.prototype.kind]: MmlMunderover,\n  [MmlMmultiscripts.prototype.kind]: MmlMmultiscripts,\n  [MmlMprescripts.prototype.kind]: MmlMprescripts,\n  [MmlNone.prototype.kind]: MmlNone,\n\n  [MmlMtable.prototype.kind]: MmlMtable,\n  [MmlMlabeledtr.prototype.kind]: MmlMlabeledtr,\n  [MmlMtr.prototype.kind]: MmlMtr,\n  [MmlMtd.prototype.kind]: MmlMtd,\n  [MmlMaligngroup.prototype.kind]: MmlMaligngroup,\n  [MmlMalignmark.prototype.kind]: MmlMalignmark,\n\n  [MmlMglyph.prototype.kind]: MmlMglyph,\n\n  [MmlSemantics.prototype.kind]: MmlSemantics,\n  [MmlAnnotation.prototype.kind]: MmlAnnotation,\n  [MmlAnnotationXML.prototype.kind]: MmlAnnotationXML,\n\n  [TeXAtom.prototype.kind]: TeXAtom,\n  [MathChoice.prototype.kind]: MathChoice,\n\n  [TextNode.prototype.kind]: TextNode,\n  [XMLNode.prototype.kind]: XMLNode\n};\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlFactory to create Mml Nodes\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractNodeFactory} from '../Tree/NodeFactory.js';\nimport {MmlNode, MmlNodeClass} from './MmlNode.js';\nimport {MML} from './MML.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlFactory (subclass of NodeFactory)\n */\n\nexport class MmlFactory extends AbstractNodeFactory<MmlNode, MmlNodeClass> {\n\n  /**\n   * The default node-creation functions\n   */\n  public static defaultNodes = MML;\n\n  /**\n   * @return {Object}  The list of node-creation functions (similar to the\n   *                   MML object from MathJax v2).\n   */\n  get MML(): Object {\n    return this.node;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements bit-fields with extendable field names\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nexport class BitField {\n\n  /**\n   * The largest bit available\n   */\n  protected static MAXBIT = 1 << 31;\n\n  /**\n   * The next bit to be allocated\n   */\n  protected static next: number = 1;\n\n  /**\n   * The map of names to bit positions\n   */\n  protected static names: Map<string, number> = new Map();\n\n  /**\n   * The bits that are set\n   */\n  protected bits: number = 0;\n\n  /**\n   * @param {string} names    The names of the bit positions to reserve\n   */\n  public static allocate(...names: string[]) {\n    for (const name of names) {\n      if (this.has(name)) {\n        throw new Error('Bit already allocated for ' + name);\n      }\n      if (this.next === BitField.MAXBIT) {\n        throw new Error('Maximum number of bits already allocated');\n      }\n      this.names.set(name, this.next);\n      this.next <<= 1;\n    }\n  }\n\n  /**\n   * @param {string} name   The name of the bit to check for being defined\n   * @return {boolean}      True if the named bit is already allocated\n   */\n  public static has(name: string): boolean {\n    return this.names.has(name);\n  }\n\n  /**\n   * @param {string} name    The name of the bit position to set\n   */\n  public set(name: string) {\n    this.bits |= this.getBit(name);\n  }\n\n  /**\n   * @param {string} name    The name of the bit position to clear\n   */\n  public clear(name: string) {\n    this.bits &= ~this.getBit(name);\n  }\n\n  /**\n   * @param {string} name   The name of the bit to check if set\n   * @return {boolean}      True if the named bit is set\n   */\n  public isSet(name: string): boolean {\n    return !!(this.bits & this.getBit(name));\n  }\n\n  /**\n   * Clear all bits\n   */\n  public reset() {\n    this.bits = 0;\n  }\n\n  /**\n   * @param {string} name   The name whose bit position is needed (error if not defined)\n   * @return {number}       The position of the named bit\n   */\n  protected getBit(name: string): number {\n    const bit = (this.constructor as typeof BitField).names.get(name);\n    if (!bit) {\n      throw new Error('Unknown bit-field name: ' + name);\n    }\n    return bit;\n  }\n\n}\n\n/**\n * @param {string[]} names    The name of the positions to allocate initially\n * @return {typeof AbstractBitField}  The bit-field class with names allocated\n */\nexport function BitFieldClass(...names: string[]): typeof BitField {\n  const Bits = class extends BitField {};\n  Bits.allocate(...names);\n  return Bits;\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for MathDocument objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {userOptions, defaultOptions, OptionList, expandable} from '../util/Options.js';\nimport {InputJax, AbstractInputJax} from './InputJax.js';\nimport {OutputJax, AbstractOutputJax} from './OutputJax.js';\nimport {MathList, AbstractMathList} from './MathList.js';\nimport {MathItem, AbstractMathItem, STATE} from './MathItem.js';\nimport {MmlNode, TextNode} from './MmlTree/MmlNode.js';\nimport {MmlFactory} from '../core/MmlTree/MmlFactory.js';\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\nimport {BitField, BitFieldClass} from '../util/BitField.js';\n\nimport {PrioritizedList} from '../util/PrioritizedList.js';\n\n/*****************************************************************/\n\n/**\n * A function to call while rendering a document (usually calls a MathDocument method)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport type RenderDoc<N, T, D> = (document: MathDocument<N, T, D>) => boolean;\n\n/**\n * A function to call while rendering a MathItem (usually calls one of its methods)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport type RenderMath<N, T, D> = (math: MathItem<N, T, D>, document: MathDocument<N, T, D>) => boolean;\n\n/**\n * The data for an action to perform during rendering or conversion\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport type RenderData<N, T, D> = {\n  id: string,                           //  The name for the action\n  renderDoc: RenderDoc<N, T, D>,        //  The action to take during a render() call\n  renderMath: RenderMath<N, T, D>,      //  The action to take during a rerender() or convert() call\n  convert: boolean                      //  Whether the action is to be used during convert()\n};\n\n/**\n * The data used to define a render action in configurations and options objects\n *   (the key is used as the id, the number in the data below is the priority, and\n *    the remainind data is as described below; if no boolean is given, convert = true\n *    by default)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport type RenderAction<N, T, D> =\n  [number] |                                                     // id (i.e., key) is method name to use\n  [number, string] |                                             // string is method to call\n  [number, string, string] |                                     // the strings are methods names for doc and math\n  [number, RenderDoc<N, T, D>, RenderMath<N, T, D>] |            // explicit functions for doc and math\n  [number, boolean] |                                            // same as first above, with boolean for convert\n  [number, string, boolean] |                                    // same as second above, with boolean for convert\n  [number, string, string, boolean] |                            // same as third above, with boolean for convert\n  [number, RenderDoc<N, T, D>, RenderMath<N, T, D>, boolean];    // same as forth above, with boolean for convert\n\n/**\n * An object representing a collection of rendering actions (id's tied to priority-and-method data)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport type RenderActions<N, T, D> = {[id: string]: RenderAction<N, T, D>};\n\n/**\n * Implements a prioritized list of render actions.  Extensions can add actions to the list\n *   to make it easy to extend the normal typesetting and conversion operations.\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class RenderList<N, T, D> extends PrioritizedList<RenderData<N, T, D>> {\n\n  /**\n   * Creates a new RenderList from an initial list of rendering actions\n   *\n   * @param {RenderActions} actions The list of actions to take during render(), rerender(), and convert() calls\n   * @returns {RenderList}    The newly created prioritied list\n   */\n  public static create<N, T, D>(actions: RenderActions<N, T, D>): RenderList<N, T, D> {\n    const list = new this<N, T, D>();\n    for (const id of Object.keys(actions)) {\n      const [action, priority] = this.action<N, T, D>(id, actions[id]);\n      if (priority) {\n        list.add(action, priority);\n      }\n    }\n    return list;\n  }\n\n  /**\n   * Parses a RenderAction to produce the correspinding RenderData item\n   *  (e.g., turn method names into actual functions that call the method)\n   *\n   * @param {string} id               The id of the action\n   * @param {RenderAction} action     The RenderAction defining the action\n   * @returns {[RenderData,number]}   The corresponding RenderData definition for the action and its priority\n   */\n  public static action<N, T, D>(id: string, action: RenderAction<N, T, D>): [RenderData<N, T, D>, number] {\n    let renderDoc, renderMath;\n    let convert = true;\n    let priority = action[0];\n    if (action.length === 1 || typeof action[1] === 'boolean') {\n      action.length === 2 && (convert = action[1] as boolean);\n      [renderDoc, renderMath] = this.methodActions(id);\n    } else if (typeof action[1] === 'string') {\n      if (typeof action[2] === 'string') {\n        action.length === 4 && (convert = action[3] as boolean);\n        const [method1, method2] = action.slice(1) as [string, string];\n        [renderDoc, renderMath] = this.methodActions(method1, method2);\n      } else {\n        action.length === 3 && (convert = action[2] as boolean);\n        [renderDoc, renderMath] = this.methodActions(action[1] as string);\n      }\n    } else {\n      action.length === 4 && (convert = action[3] as boolean);\n      [renderDoc, renderMath] = action.slice(1) as [RenderDoc<N, T, D>, RenderMath<N, T, D>];\n    }\n    return [{id, renderDoc, renderMath, convert} as RenderData<N, T, D>, priority];\n  }\n\n  /**\n   * Produces the doc and math actions for the given method name(s)\n   *   (a blank name is a no-op)\n   *\n   * @param {string} method1    The method to use for the render() call\n   * @param {string} method1    The method to use for the rerender() and convert() calls\n   */\n  protected static methodActions(method1: string, method2: string = method1) {\n    return [\n      (document: any) => {method1 && document[method1](); return false; },\n      (math: any, document: any) => {method2 && math[method2](document); return false; }\n    ];\n  }\n\n  /**\n   * Perform the document-level rendering functions\n   *\n   * @param {MathDocument} document   The MathDocument whose methods are to be called\n   * @param {number=} start           The state at which to start rendering (default is UNPROCESSED)\n   */\n  public renderDoc(document: MathDocument<N, T, D>, start: number = STATE.UNPROCESSED) {\n    for (const item of this.items) {\n      if (item.priority >= start) {\n        if (item.item.renderDoc(document)) return;\n      }\n    }\n  }\n\n  /**\n   * Perform the MathItem-level rendering functions\n   *\n   * @param {MathItem} math           The MathItem whose methods are to be called\n   * @param {MathDocument} document   The MathDocument to pass to the MathItem methods\n   * @param {number=} start           The state at which to start rendering (default is UNPROCESSED)\n   */\n  public renderMath(math: MathItem<N, T, D>, document: MathDocument<N, T, D>, start: number = STATE.UNPROCESSED) {\n    for (const item of this.items) {\n      if (item.priority >= start) {\n        if (item.item.renderMath(math, document)) return;\n      }\n    }\n  }\n\n  /**\n   * Perform the MathItem-level conversion functions\n   *\n   * @param {MathItem} math           The MathItem whose methods are to be called\n   * @param {MathDocument} document   The MathDocument to pass to the MathItem methods\n   * @param {number=} end             The state at which to end rendering (default is LAST)\n   */\n  public renderConvert(math: MathItem<N, T, D>, document: MathDocument<N, T, D>, end: number = STATE.LAST) {\n    for (const item of this.items) {\n      if (item.priority > end) return;\n      if (item.item.convert) {\n        if (item.item.renderMath(math, document)) return;\n      }\n    }\n  }\n\n  /**\n   * Find an entry in the list with a given ID\n   *\n   * @param {string} id            The id to search for\n   * @returns {RenderData|null}   The data for the given id, if found, or null\n   */\n  public findID(id: string): RenderData<N, T, D> | null {\n    for (const item of this.items) {\n      if (item.item.id === id) {\n        return item.item;\n      }\n    }\n    return null;\n  }\n\n}\n\n/*****************************************************************/\n\n/**\n * The ways of specifying a container (a selector string, an actual node,\n * or an array of those (e.g., the result of document.getElementsByTagName())\n *\n * @template N  The HTMLElement node class\n */\nexport type ContainerList<N> = string | N | (string | N | N[])[];\n\n/**\n * The options allowed for the reset() method\n */\nexport type ResetList = {\n  all?: boolean,\n  processed?: boolean,\n  inputJax?: any[],\n  outputJax?: any[]\n};\n\n/**\n * The default option list for the reset() method\n */\nexport const resetOptions: ResetList = {\n  all: false,\n  processed: false,\n  inputJax: null,\n  outputJax: null\n};\n\n/**\n * The option list for when all options are to be reset\n */\nexport const resetAllOptions: ResetList = {\n  all: true,\n  processed: true,\n  inputJax: [],\n  outputJax: []\n};\n\n/*****************************************************************/\n/**\n *  The MathDocument interface\n *\n *  The MathDocument is created by MathJax.Document() and holds the\n *  document, the math found in it, and so on.  The methods of the\n *  MathDocument all return the MathDocument itself, so you can\n *  chain the method calls.  E.g.,\n *\n *    const html = MathJax.Document('<html>...</html>');\n *    html.findMath()\n *        .compile()\n *        .getMetrics()\n *        .typeset()\n *        .updateDocument();\n *\n *  The MathDocument is the main interface for page authors to\n *  interact with MathJax.\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface MathDocument<N, T, D> {\n  /**\n   * The document being processed (e.g., DOM document, or Markdown string)\n   */\n  document: D;\n\n  /**\n   * The kind of MathDocument (e.g., \"HTML\")\n   */\n  kind: string;\n\n  /**\n   * The options for the document\n   */\n  options: OptionList;\n\n  /**\n   * The list of MathItems found in this page\n   */\n  math: MathList<N, T, D>;\n\n  /**\n   * The list of actions to take during a render() or convert() call\n   */\n  renderActions: RenderList<N, T, D>;\n\n  /**\n   * This object tracks what operations have been performed, so that (when\n   *  asynchronous operations are used), the ones that have already been\n   *  completed won't be performed again.\n   */\n  processed: BitField;\n\n  /**\n   * An array of input jax to run on the document\n   */\n  inputJax: InputJax<N, T, D>[];\n\n  /**\n   * The output jax to use for the document\n   */\n  outputJax: OutputJax<N, T, D>;\n\n  /**\n   * The DOM adaptor to use for input and output\n   */\n  adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * The MmlFactory to be used for input jax and error processing\n   */\n  mmlFactory: MmlFactory;\n\n  /**\n   * @param {string} id      The id of the action to add\n   * @param {any[]} action   The RenderAction to take\n   */\n  addRenderAction(id: string, ...action: any[]): void;\n\n  /**\n   * @param {string} id   The id of the action to remove\n   */\n  removeRenderAction(id: string): void;\n\n  /**\n   * Perform the renderActions on the document\n   */\n  render(): MathDocument<N, T, D>;\n\n  /**\n   * Rerender the MathItems on the page\n   *\n   * @param {number=} start    The state to start rerendering at\n   * @return {MathDocument}    The math document instance\n   */\n  rerender(start?: number): MathDocument<N, T, D>;\n\n  /**\n   * Convert a math string to the document's output format\n   *\n   * @param {string} math           The math string to convert\n   * @params {OptionList} options   The options for the conversion (e.g., format, ex, em, etc.)\n   * @return {MmlNode|N}            The MmlNode or N node for the converted content\n   */\n  convert(math: string, options?: OptionList): MmlNode | N;\n\n  /**\n   * Locates the math in the document and constructs the MathList\n   *  for the document.\n   *\n   * @param {OptionList} options  The options for locating the math\n   * @return {MathDocument}       The math document instance\n   */\n  findMath(options?: OptionList): MathDocument<N, T, D>;\n\n  /**\n   * Calls the input jax to process the MathItems in the MathList\n   *\n   * @return {MathDocument}  The math document instance\n   */\n  compile(): MathDocument<N, T, D>;\n\n  /**\n   * Gets the metric information for the MathItems\n   *\n   * @return {MathDocument}  The math document instance\n   */\n  getMetrics(): MathDocument<N, T, D>;\n\n  /**\n   * Calls the output jax to process the compiled math in the MathList\n   *\n   * @return {MathDocument}  The math document instance\n   */\n  typeset(): MathDocument<N, T, D>;\n\n  /**\n   * Updates the document to include the typeset math\n   *\n   * @return {MathDocument}  The math document instance\n   */\n  updateDocument(): MathDocument<N, T, D>;\n\n  /**\n   * Removes the typeset math from the document\n   *\n   * @param {boolean} restore  True if the original math should be put\n   *                            back into the document as well\n   * @return {MathDocument}    The math document instance\n   */\n  removeFromDocument(restore?: boolean): MathDocument<N, T, D>;\n\n  /**\n   * Set the state of the document (allowing you to roll back\n   *  the state to a previous one, if needed).\n   *\n   * @param {number} state     The new state of the document\n   * @param {boolean} restore  True if the original math should be put\n   *                            back into the document during the rollback\n   * @return {MathDocument}    The math document instance\n   */\n  state(state: number, restore?: boolean): MathDocument<N, T, D>;\n\n  /**\n   * Clear the processed values so that the document can be reprocessed\n   *\n   * @param {ResetList} options   The things to be reset\n   * @return {MathDocument}       The math document instance\n   */\n  reset(options?: ResetList): MathDocument<N, T, D>;\n\n  /**\n   * Reset the processed values and clear the MathList (so that new math\n   * can be processed in the document).\n   *\n   * @return {MathDocument}  The math document instance\n   */\n  clear(): MathDocument<N, T, D>;\n\n  /**\n   * Merges a MathList into the list for this document.\n   *\n   * @param {MathList} list   The MathList to be merged into this document's list\n   * @return {MathDocument}   The math document instance\n   */\n  concat(list: MathList<N, T, D>): MathDocument<N, T, D>;\n\n  /**\n   * Clear the typeset MathItems that are within the given container\n   *   from the document's MathList.  (E.g., when the content of the\n   *   container has been updated and you want to remove the\n   *   associated MathItems)\n   *\n   * @param {ContainerList<N>} elements   The container DOM elements whose math items are to be removed\n   * @return {MathItem<N,T,D>[]}          The removed MathItems\n   */\n  clearMathItemsWithin(containers: ContainerList<N>): MathItem<N, T, D>[];\n\n  /**\n   * Get the typeset MathItems that are within a given container.\n   *\n   * @param {ContainerList<N>} elements   The container DOM elements whose math items are to be found\n   * @return {MathItem<N,T,D>[]}          The list of MathItems within that container\n   */\n  getMathItemsWithin(elements: ContainerList<N>): MathItem<N, T, D>[];\n\n}\n\n/*****************************************************************/\n\n/**\n * Defaults used when input jax isn't specified\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nclass DefaultInputJax<N, T, D> extends AbstractInputJax<N, T, D> {\n  /**\n   * @override\n   */\n  public compile(_math: MathItem<N, T, D>) {\n    return null as MmlNode;\n  }\n}\n\n/**\n * Defaults used when ouput jax isn't specified\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nclass DefaultOutputJax<N, T, D> extends AbstractOutputJax<N, T, D> {\n  /**\n   * @override\n   */\n  public typeset(_math: MathItem<N, T, D>, _document: MathDocument<N, T, D> = null) {\n    return null as N;\n  }\n  /**\n   * @override\n   */\n  public escaped(_math: MathItem<N, T, D>, _document?: MathDocument<N, T, D>) {\n    return null as N;\n  }\n}\n\n/**\n * Default for the MathList when one isn't specified\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nclass DefaultMathList<N, T, D> extends AbstractMathList<N, T, D> {}\n\n/**\n * Default for the Mathitem when one isn't specified\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nclass DefaultMathItem<N, T, D> extends AbstractMathItem<N, T, D> {}\n\n/*****************************************************************/\n/**\n *  Implements the abstract MathDocument class\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractMathDocument<N, T, D> implements MathDocument<N, T, D> {\n\n  /**\n   * The type of MathDocument\n   */\n  public static KIND: string = 'MathDocument';\n\n  /**\n   * The default options for the document\n   */\n  public static OPTIONS: OptionList = {\n    OutputJax: null,           // instance of an OutputJax for the document\n    InputJax: null,            // instance of an InputJax or an array of them\n    MmlFactory: null,          // instance of a MmlFactory for this document\n    MathList: DefaultMathList, // constructor for a MathList to use for the document\n    MathItem: DefaultMathItem, // constructor for a MathItem to use for the MathList\n    compileError: (doc: AbstractMathDocument<any, any, any>, math: MathItem<any, any, any>, err: Error) => {\n      doc.compileError(math, err);\n    },\n    typesetError: (doc: AbstractMathDocument<any, any, any>, math: MathItem<any, any, any>, err: Error) => {\n      doc.typesetError(math, err);\n    },\n    renderActions: expandable({\n      find:    [STATE.FINDMATH, 'findMath', '', false],\n      compile: [STATE.COMPILED],\n      metrics: [STATE.METRICS, 'getMetrics', '', false],\n      typeset: [STATE.TYPESET],\n      update:  [STATE.INSERTED, 'updateDocument', false]\n    }) as RenderActions<any, any, any>\n  };\n\n  /**\n   * A bit-field for the actions that have been processed\n   */\n  public static ProcessBits = BitFieldClass('findMath', 'compile', 'getMetrics', 'typeset', 'updateDocument');\n\n  /**\n   * The document managed by this MathDocument\n   */\n  public document: D;\n  /**\n   * The actual options for this document (with user-supplied ones merged in)\n   */\n  public options: OptionList;\n\n  /**\n   * The list of MathItems for this document\n   */\n  public math: MathList<N, T, D>;\n\n  /**\n   * The list of render actions\n   */\n  public renderActions: RenderList<N, T, D>;\n\n  /**\n   * The bit-field used to tell what steps have been taken on the document (for retries)\n   */\n  public processed: BitField;\n\n  /**\n   * The list of input jax for the document\n   */\n  public inputJax: InputJax<N, T, D>[];\n\n  /**\n   * The output jax for the document\n   */\n  public outputJax: OutputJax<N, T, D>;\n\n  /**\n   * The DOM adaptor for the document\n   */\n  public adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * The MathML node factory for the internal MathML representation\n   */\n  public mmlFactory: MmlFactory;\n\n\n  /**\n   * @param {any} document           The document (HTML string, parsed DOM, etc.) to be processed\n   * @param {DOMAdaptor} adaptor     The DOM adaptor for this document\n   * @param {OptionList} options     The options for this document\n   * @constructor\n   */\n  constructor (document: D, adaptor: DOMAdaptor<N, T, D>, options: OptionList) {\n    let CLASS = this.constructor as typeof AbstractMathDocument;\n    this.document = document;\n    this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), options);\n    this.math = new (this.options['MathList'] || DefaultMathList)();\n    this.renderActions = RenderList.create<N, T, D>(this.options['renderActions']);\n    this.processed = new AbstractMathDocument.ProcessBits();\n    this.outputJax = this.options['OutputJax'] || new DefaultOutputJax<N, T, D>();\n    let inputJax = this.options['InputJax'] || [new DefaultInputJax<N, T, D>()];\n    if (!Array.isArray(inputJax)) {\n      inputJax = [inputJax];\n    }\n    this.inputJax = inputJax;\n    //\n    // Pass the DOM adaptor to the jax\n    //\n    this.adaptor = adaptor;\n    this.outputJax.setAdaptor(adaptor);\n    this.inputJax.map(jax => jax.setAdaptor(adaptor));\n    //\n    // Pass the MmlFactory to the jax\n    //\n    this.mmlFactory = this.options['MmlFactory'] || new MmlFactory();\n    this.inputJax.map(jax => jax.setMmlFactory(this.mmlFactory));\n    //\n    // Do any initialization that requires adaptors or factories\n    //\n    this.outputJax.initialize();\n    this.inputJax.map(jax => jax.initialize());\n  }\n\n  /**\n   * @return {string}  The kind of document\n   */\n  public get kind(): string {\n    return (this.constructor as typeof AbstractMathDocument).KIND;\n  }\n\n  /**\n   * @override\n   */\n  public addRenderAction(id: string, ...action: any[]) {\n    const [fn, p] = RenderList.action<N, T, D>(id, action as RenderAction<N, T, D>);\n    this.renderActions.add(fn, p);\n  }\n\n  /**\n   * @override\n   */\n  public removeRenderAction(id: string) {\n    const action = this.renderActions.findID(id);\n    if (action) {\n      this.renderActions.remove(action);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public render() {\n    this.renderActions.renderDoc(this);\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public rerender(start: number = STATE.RERENDER) {\n    this.state(start - 1);\n    this.render();\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public convert(math: string, options: OptionList = {}) {\n    let {format, display, end, ex, em, containerWidth, lineWidth, scale, family} = userOptions({\n      format: this.inputJax[0].name, display: true, end: STATE.LAST,\n      em: 16, ex: 8, containerWidth: null, lineWidth: 1000000, scale: 1, family: ''\n    }, options);\n    if (containerWidth === null) {\n      containerWidth = 80 * ex;\n    }\n    const jax = this.inputJax.reduce((jax, ijax) => (ijax.name === format ? ijax : jax), null);\n    const mitem = new this.options.MathItem(math, jax, display);\n    mitem.start.node = this.adaptor.body(this.document);\n    mitem.setMetrics(em, ex, containerWidth, lineWidth, scale);\n    if (this.outputJax.options.mtextInheritFont) {\n      mitem.outputData.mtextFamily = family;\n    }\n    if (this.outputJax.options.merrorInheritFont) {\n      mitem.outputData.merrorFamily = family;\n    }\n    mitem.convert(this, end);\n    return (mitem.typesetRoot || mitem.root);\n  }\n\n  /**\n   * @override\n   */\n  public findMath(_options: OptionList = null) {\n    this.processed.set('findMath');\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public compile() {\n    if (!this.processed.isSet('compile')) {\n      //\n      //  Compile all the math in the list\n      //\n      const recompile = [];\n      for (const math of this.math) {\n        this.compileMath(math);\n        if (math.inputData.recompile !== undefined) {\n          recompile.push(math);\n        }\n      }\n      //\n      //  If any were added to the recompile list,\n      //    compile them again\n      //\n      for (const math of recompile) {\n        const data = math.inputData.recompile;\n        math.state(data.state);\n        math.inputData.recompile = data;\n        this.compileMath(math);\n      }\n      this.processed.set('compile');\n    }\n    return this;\n  }\n\n  /**\n   * @param {MathItem} math   The item to compile\n   */\n  protected compileMath(math: MathItem<N, T, D>) {\n    try {\n      math.compile(this);\n    } catch (err) {\n      if (err.retry || err.restart) {\n        throw err;\n      }\n      this.options['compileError'](this, math, err);\n      math.inputData['error'] = err;\n    }\n  }\n\n  /**\n   * Produce an error using MmlNodes\n   *\n   * @param {MathItem} math  The MathItem producing the error\n   * @param {Error} err      The Error object for the error\n   */\n  public compileError(math: MathItem<N, T, D>, err: Error) {\n    math.root = this.mmlFactory.create('math', null, [\n      this.mmlFactory.create('merror', {'data-mjx-error': err.message, title: err.message}, [\n        this.mmlFactory.create('mtext', null, [\n          (this.mmlFactory.create('text') as TextNode).setText('Math input error')\n        ])\n      ])\n    ]);\n    if (math.display) {\n      math.root.attributes.set('display', 'block');\n    }\n    math.inputData.error = err.message;\n  }\n\n  /**\n   * @override\n   */\n  public typeset() {\n    if (!this.processed.isSet('typeset')) {\n      for (const math of this.math) {\n        try {\n          math.typeset(this);\n        } catch (err) {\n          if (err.retry || err.restart) {\n            throw err;\n          }\n          this.options['typesetError'](this, math, err);\n          math.outputData['error'] = err;\n        }\n      }\n      this.processed.set('typeset');\n    }\n    return this;\n  }\n\n  /**\n   * Produce an error using HTML\n   *\n   * @param {MathItem} math  The MathItem producing the error\n   * @param {Error} err      The Error object for the error\n   */\n  public typesetError(math: MathItem<N, T, D>, err: Error) {\n    math.typesetRoot = this.adaptor.node('mjx-container', {\n      class: 'MathJax mjx-output-error',\n      jax: this.outputJax.name,\n    }, [\n      this.adaptor.node('span', {\n        'data-mjx-error': err.message,\n        title: err.message,\n        style: {\n          color: 'red',\n          'background-color': 'yellow',\n          'line-height': 'normal'\n        }\n      }, [\n        this.adaptor.text('Math output error')\n      ])\n    ]);\n    if (math.display) {\n      this.adaptor.setAttributes(math.typesetRoot, {\n        style: {\n          display: 'block',\n          margin: '1em 0',\n          'text-align': 'center'\n        }\n      });\n    }\n    math.outputData.error = err.message;\n  }\n\n  /**\n   * @override\n   */\n  public getMetrics() {\n    if (!this.processed.isSet('getMetrics')) {\n      this.outputJax.getMetrics(this);\n      this.processed.set('getMetrics');\n    }\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public updateDocument() {\n    if (!this.processed.isSet('updateDocument')) {\n      for (const math of this.math.reversed()) {\n        math.updateDocument(this);\n      }\n      this.processed.set('updateDocument');\n    }\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public removeFromDocument(_restore: boolean = false) {\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public state(state: number, restore: boolean = false) {\n    for (const math of this.math) {\n      math.state(state, restore);\n    }\n    if (state < STATE.INSERTED) {\n      this.processed.clear('updateDocument');\n    }\n    if (state < STATE.TYPESET) {\n      this.processed.clear('typeset');\n      this.processed.clear('getMetrics');\n    }\n    if (state < STATE.COMPILED) {\n      this.processed.clear('compile');\n    }\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public reset(options: ResetList = {processed: true}) {\n    options = userOptions(Object.assign({}, resetOptions), options);\n    options.all && Object.assign(options, resetAllOptions);\n    options.processed && this.processed.reset();\n    options.inputJax && this.inputJax.forEach(jax => jax.reset(...options.inputJax));\n    options.outputJax && this.outputJax.reset(...options.outputJax);\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public clear() {\n    this.reset();\n    this.math.clear();\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public concat(list: MathList<N, T, D>) {\n    this.math.merge(list);\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public clearMathItemsWithin(containers: ContainerList<N>) {\n    const items = this.getMathItemsWithin(containers);\n    this.math.remove(...items);\n    return items;\n  }\n\n  /**\n   * @override\n   */\n  public getMathItemsWithin(elements: ContainerList<N>) {\n    if (!Array.isArray(elements)) {\n      elements = [elements];\n    }\n    const adaptor = this.adaptor;\n    const items = [] as MathItem<N, T, D>[];\n    const containers = adaptor.getElements(elements, this.document);\n    ITEMS:\n    for (const item of this.math) {\n      for (const container of containers) {\n        if (item.start.node && adaptor.contains(container, item.start.node)) {\n          items.push(item);\n          continue ITEMS;\n        }\n      }\n    }\n    return items;\n  }\n\n}\n\n/**\n * The constructor type for a MathDocument\n *\n * @template D    The MathDocument type this constructor is for\n */\nexport interface MathDocumentConstructor<D extends MathDocument<any, any, any>> {\n  KIND: string;\n  OPTIONS: OptionList;\n  ProcessBits: typeof BitField;\n  new (...args: any[]): D;\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Interfaces and abstract classes for Handler objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {MathDocument, AbstractMathDocument, MathDocumentConstructor} from './MathDocument.js';\nimport {OptionList} from '../util/Options.js';\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\n\n/*****************************************************************/\n/**\n *  The Handler interface\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface Handler<N, T, D> {\n  /**\n   * The name of the handler class\n   */\n  name: string;\n\n  /**\n   * The DOM Adaptor to use for managing HTML elements\n   */\n  adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * The priority for the handler when handlers are polled\n   *   to see which one can process a given document.\n   */\n  priority: number;\n\n  /**\n   * The class implementing the MathDocument for this handler\n   *   (so it can be subclassed by extensions as needed)\n   */\n  documentClass: MathDocumentConstructor<AbstractMathDocument<N, T, D>>;\n\n  /**\n   * Checks to see if the handler can process a given document\n   *\n   * @param {any} document  The document to be processed (string, window, etc.)\n   * @return {boolean}      True if this handler can process the given document\n   */\n  handlesDocument(document: any): boolean;\n\n  /**\n   * Creates a MathDocument for the given handler\n   *\n   * @param {any} document        The document to be handled\n   * @param {OptionList} options  The options for the handling of the document\n   * @return {MathDocument}       The MathDocument object that manages the processing\n   */\n  create(document: any, options: OptionList): MathDocument<N, T, D>;\n}\n\n/*****************************************************************/\n/**\n *  The default MathDocument class (subclasses use their own)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nclass DefaultMathDocument<N, T, D> extends AbstractMathDocument<N, T, D> {}\n\n/*****************************************************************/\n/**\n *  The Handler interface\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractHandler<N, T, D> implements Handler<N, T, D> {\n\n  /**\n   * The name of this class\n   */\n  public static NAME: string = 'generic';\n\n  /**\n   * The DOM Adaptor to use for managing HTML elements\n   */\n  public adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * The priority for this handler\n   */\n  public priority: number;\n\n  /**\n   * The class implementing the MathDocument for this handler\n   *   (so it can be subclassed by extensions as needed)\n   */\n  public documentClass: MathDocumentConstructor<AbstractMathDocument<N, T, D>> = DefaultMathDocument;\n\n  /**\n   * @param {number} priority  The priority to use for this handler\n   *\n   * @constructor\n   */\n  constructor(adaptor: DOMAdaptor<N, T, D>, priority: number = 5) {\n    this.adaptor = adaptor;\n    this.priority = priority;\n  }\n\n  /**\n   * @return {string}  The name of this handler class\n   */\n  public get name(): string {\n    return (this.constructor as typeof AbstractHandler).NAME;\n  }\n\n  /**\n   * @override\n   */\n  public handlesDocument(_document: any) {\n    return false;\n  }\n\n  /**\n   * @override\n   */\n  public create(document: any, options: OptionList) {\n    return new this.documentClass(document, this.adaptor, options) as MathDocument<N, T, D>;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the HTMLMathItem class\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractMathItem, Location, STATE} from '../../core/MathItem.js';\nimport {InputJax} from '../../core/InputJax.js';\nimport {HTMLDocument} from './HTMLDocument.js';\n\n/*****************************************************************/\n/**\n *  Implements the HTMLMathItem class (extends AbstractMathItem)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HTMLMathItem<N, T, D> extends AbstractMathItem<N, T, D> {\n\n  /**\n   * Easy access to DOM adaptor\n   */\n  get adaptor() {\n    return this.inputJax.adaptor;\n  }\n\n  /**\n   * @override\n   */\n  constructor(math: string, jax: InputJax<N, T, D>, display: boolean = true,\n              start: Location<N, T> = {node: null, n: 0, delim: ''},\n              end: Location<N, T> = {node: null, n: 0, delim: ''}) {\n    super(math, jax, display, start, end);\n  }\n\n  /**\n   * Insert the typeset MathItem into the document at the right location\n   *   If the starting and ending nodes are the same:\n   *     Split the text to isolate the math and its delimiters\n   *     Replace the math by the typeset version\n   *   Otherewise (spread over several nodes)\n   *     Split the start node, if needed\n   *     Remove nodes until we reach the end node\n   *     Insert the math before the end node\n   *     Split the end node, if needed\n   *     Remove the end node\n   *\n   * @override\n   */\n  public updateDocument(_html: HTMLDocument<N, T, D>) {\n    if (this.state() < STATE.INSERTED) {\n      if (this.inputJax.processStrings) {\n        let node = this.start.node as T;\n        if (node === this.end.node) {\n          if (this.end.n && this.end.n < this.adaptor.value(this.end.node).length) {\n            this.adaptor.split(this.end.node, this.end.n);\n          }\n          if (this.start.n) {\n            node = this.adaptor.split(this.start.node as T, this.start.n);\n          }\n          this.adaptor.replace(this.typesetRoot, node);\n        } else {\n          if (this.start.n) {\n            node = this.adaptor.split(node, this.start.n);\n          }\n          while (node !== this.end.node) {\n            let next = this.adaptor.next(node) as T;\n            this.adaptor.remove(node);\n            node = next;\n          }\n          this.adaptor.insert(this.typesetRoot, node);\n          if (this.end.n < this.adaptor.value(node).length) {\n            this.adaptor.split(node, this.end.n);\n          }\n          this.adaptor.remove(node);\n        }\n      } else {\n        this.adaptor.replace(this.typesetRoot, this.start.node);\n      }\n      this.start.node = this.end.node = this.typesetRoot;\n      this.start.n = this.end.n = 0;\n      this.state(STATE.INSERTED);\n    }\n  }\n\n  /**\n   * Update the style sheet for any changes due to rerendering\n   *\n   * @param {HTMLDocument} document   The document whose styles are to be updated\n   */\n  public updateStyleSheet(document: HTMLDocument<N, T, D>) {\n    document.addStyleSheet();\n  }\n\n  /**\n   * Remove the typeset math from the document, and put back the original\n   *  expression and its delimiters, if requested.\n   *\n   * @override\n   */\n  public removeFromDocument(restore: boolean = false) {\n    if (this.state() >= STATE.TYPESET) {\n      const adaptor = this.adaptor;\n      let node = this.start.node;\n      let math: N | T = adaptor.text('');\n      if (restore) {\n        let text = this.start.delim + this.math + this.end.delim;\n        if (this.inputJax.processStrings) {\n          math = adaptor.text(text);\n        } else {\n          const doc = adaptor.parse(text, 'text/html');\n          math = adaptor.firstChild(adaptor.body(doc));\n        }\n      }\n      if (adaptor.parent(node)) {\n        adaptor.replace(math, node);\n      }\n      this.start.node = this.end.node = math;\n      this.start.n = this.end.n = 0;\n    }\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the HTMLMathList object\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractMathList} from '../../core/MathList.js';\n\n/*****************************************************************/\n/**\n *  Implement the HTMLMathList class (extends AbstractMathList)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HTMLMathList<N, T, D> extends AbstractMathList<N, T, D> {\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the HTMLDomStrings class\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {userOptions, defaultOptions, OptionList, makeArray} from '../../util/Options.js';\nimport {DOMAdaptor} from '../../core/DOMAdaptor.js';\n\n/**\n *  List of consecutive text nodes and their text lengths\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n */\nexport type HTMLNodeList<N, T> = [N | T, number][];\n\n/*****************************************************************/\n/**\n *  The HTMLDocument class (extends AbstractMathDocument)\n *\n *  A class for extracting the text from DOM trees\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HTMLDomStrings<N, T, D> {\n\n  /**\n   * The default options for string processing\n   */\n  public static OPTIONS: OptionList = {\n    skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'annotation', 'annotation-xml'],\n                                        // The names of the tags whose contents will not be\n                                        // scanned for math delimiters\n\n    includeHtmlTags: {br: '\\n', wbr: '', '#comment': ''},\n                                        //  tags to be included in the text (and what\n                                        //  text to replace them with)\n\n    ignoreHtmlClass: 'mathjax_ignore',  // the class name of elements whose contents should\n                                        // NOT be processed by tex2jax.  Note that this\n                                        // is a regular expression, so be sure to quote any\n                                        // regexp special characters\n\n    processHtmlClass: 'mathjax_process' // the class name of elements whose contents SHOULD\n                                        // be processed when they appear inside ones that\n                                        // are ignored.  Note that this is a regular expression,\n                                        // so be sure to quote any regexp special characters\n  };\n\n  /**\n   * The options for this instance\n   */\n  protected options: OptionList;\n\n  /**\n   * The array of strings found in the DOM\n   */\n  protected strings: string[];\n\n  /**\n   * The string currently being constructed\n   */\n  protected string: string;\n\n  /**\n   * The list of nodes and lengths for the string being constructed\n   */\n  protected snodes: HTMLNodeList<N, T>;\n\n  /**\n   * The list of node lists corresponding to the strings in this.strings\n   */\n  protected nodes: HTMLNodeList<N, T>[];\n\n  /**\n   * The container nodes that are currently being traversed, and whether their\n   *  contents are being ignored or not\n   */\n  protected stack: [N | T, boolean][];\n\n  /**\n   * Regular expression for the tags to be skipped\n   *  processing of math\n   */\n  protected skipHtmlTags: RegExp;\n  /**\n   * Regular expression for which classes should stop processing of math\n   */\n  protected ignoreHtmlClass: RegExp;\n  /**\n   * Regular expression for which classes should start processing of math\n   */\n  protected processHtmlClass: RegExp;\n\n  /**\n   * The DOM Adaptor to managing HTML elements\n   */\n  public adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * @param {OptionList} options  The user-supplied options\n   * @constructor\n   */\n  constructor(options: OptionList = null) {\n    let CLASS = this.constructor as typeof HTMLDomStrings;\n    this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), options);\n    this.init();\n    this.getPatterns();\n  }\n\n  /**\n   * Set the initial values of the main properties\n   */\n  protected init() {\n    this.strings = [];\n    this.string = '';\n    this.snodes = [];\n    this.nodes = [];\n    this.stack = [];\n  }\n\n  /**\n   * Create the search patterns for skipHtmlTags, ignoreHtmlClass, and processHtmlClass\n   */\n  protected getPatterns() {\n    let skip = makeArray(this.options['skipHtmlTags']);\n    let ignore = makeArray(this.options['ignoreHtmlClass']);\n    let process = makeArray(this.options['processHtmlClass']);\n    this.skipHtmlTags = new RegExp('^(?:' + skip.join('|') + ')$', 'i');\n    this.ignoreHtmlClass = new RegExp('(?:^| )(?:' + ignore.join('|') + ')(?: |$)');\n    this.processHtmlClass = new RegExp('(?:^| )(?:' + process + ')(?: |$)');\n  }\n\n  /**\n   * Add a string to the string array and record its node list\n   */\n  protected pushString() {\n    if (this.string.match(/\\S/)) {\n      this.strings.push(this.string);\n      this.nodes.push(this.snodes);\n    }\n    this.string = '';\n    this.snodes = [];\n  }\n\n  /**\n   * Add more text to the current string, and record the\n   * node and its position in the string.\n   *\n   * @param {N|T} node        The node to be pushed\n   * @param {string} text   The text to be added (it may not be the actual text\n   *                         of the node, if it is one of the nodes that gets\n   *                         translated to text, like <br> to a newline).\n   */\n  protected extendString(node: N | T, text: string) {\n    this.snodes.push([node, text.length]);\n    this.string += text;\n  }\n\n  /**\n   * Handle a #text node (add its text to the current string)\n   *\n   * @param {T} node          The Text node to process\n   * @param {boolean} ignore  Whether we are currently ignoring content\n   * @return {N | T}          The next element to process\n   */\n  protected handleText(node: T, ignore: boolean): N | T {\n    if (!ignore) {\n      this.extendString(node, this.adaptor.value(node));\n    }\n    return this.adaptor.next(node);\n  }\n\n  /**\n   * Handle a BR, WBR, or #comment element (or others in the includeHtmlTags object).\n   *\n   * @param {N} node          The node to process\n   * @param {boolean} ignore  Whether we are currently ignoring content\n   * @return {N | T}          The next element to process\n   */\n  protected handleTag(node: N, ignore: boolean): N | T {\n    if (!ignore) {\n      let text = this.options['includeHtmlTags'][this.adaptor.kind(node)];\n      this.extendString(node, text);\n    }\n    return this.adaptor.next(node);\n  }\n\n  /**\n   * Handle an arbitrary DOM node:\n   *   Check the class to see if it matches the processHtmlClass regex\n   *   If the node has a child and is not marked as created by MathJax (data-MJX)\n   *       and either it is marked as restarting processing or is not a tag to be skipped, then\n   *     Save the next node (if there is one) and whether we are currently ignoring content\n   *     Move to the first child node\n   *     Update whether we are ignoring content\n   *   Otherwise\n   *     Move on to the next sibling\n   *   Return the next node to process and the ignore state\n   *\n   * @param {N} node               The node to process\n   * @param {boolean} ignore       Whether we are currently ignoring content\n   * @return {[N|T, boolean]}      The next element to process and whether to ignore its content\n   */\n  protected handleContainer(node: N, ignore: boolean): [N | T, boolean] {\n    this.pushString();\n    const cname = this.adaptor.getAttribute(node, 'class') || '';\n    const tname = this.adaptor.kind(node) || '';\n    const process = this.processHtmlClass.exec(cname);\n    let next = node as N | T;\n    if (this.adaptor.firstChild(node) && !this.adaptor.getAttribute(node, 'data-MJX') &&\n        (process || !this.skipHtmlTags.exec(tname))) {\n      if (this.adaptor.next(node)) {\n        this.stack.push([this.adaptor.next(node), ignore]);\n      }\n      next = this.adaptor.firstChild(node);\n      ignore = (ignore || this.ignoreHtmlClass.exec(cname)) && !process;\n    } else {\n      next = this.adaptor.next(node);\n    }\n    return [next, ignore];\n  }\n\n  /**\n   * Handle an unknown node type (nodeType other than 1, 3, 8)\n   *\n   * @param {N} node           The node to process\n   * @param {boolean} ignore   Whether we are currently ignoring content\n   * @return {N|T}             The next element to process\n   */\n  protected handleOther(node: N, _ignore: boolean): N | T {\n    this.pushString();\n    return this.adaptor.next(node);\n  }\n\n  /**\n   * Find the strings for a given DOM element:\n   *   Initialize the state\n   *   Get the element where we stop processing\n   *   While we still have a node, and it is not the one where we are to stop:\n   *     If it is a text node, handle it and get the next node\n   *     Otherwise, if it is in the includeHtmlTags list, handle it and get the next node\n   *     Otherwise, handle it as a container and get the next node and ignore status\n   *     If there is no next node, and there are more nodes on the stack:\n   *       Save the current string, and pop the node and ignore status from the stack\n   *   Push the final string\n   *   Get the string array and array of associated DOM nodes\n   *   Clear the internal values (so the memory can be freed)\n   *   Return the strings and node lists\n   *\n   * @param {N} node                       The node to search\n   * @return {[string[], HTMLNodeList[]]}  The array of strings and their associated lists of nodes\n   */\n  public find(node: N | T): [string[], HTMLNodeList<N, T>[]] {\n    this.init();\n    let stop = this.adaptor.next(node);\n    let ignore = false;\n    let include = this.options['includeHtmlTags'];\n\n    while (node && node !== stop) {\n      const kind = this.adaptor.kind(node);\n      if (kind === '#text') {\n        node = this.handleText(node as T, ignore);\n      } else if (include.hasOwnProperty(kind)) {\n        node = this.handleTag(node as N, ignore);\n      } else if (kind) {\n        [node, ignore] = this.handleContainer(node as N, ignore);\n      } else {\n        node = this.handleOther(node as N, ignore);\n      }\n      if (!node && this.stack.length) {\n        this.pushString();\n        [node, ignore] = this.stack.pop();\n      }\n    }\n\n    this.pushString();\n    let result = [this.strings, this.nodes] as [string[], HTMLNodeList<N, T>[]];\n    this.init(); // free up memory\n    return result;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the HTMLDocument class\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractMathDocument} from '../../core/MathDocument.js';\nimport {userOptions, separateOptions, OptionList, expandable} from '../../util/Options.js';\nimport {HTMLMathItem} from './HTMLMathItem.js';\nimport {HTMLMathList} from './HTMLMathList.js';\nimport {HTMLDomStrings} from './HTMLDomStrings.js';\nimport {DOMAdaptor} from '../../core/DOMAdaptor.js';\nimport {InputJax} from '../../core/InputJax.js';\nimport {STATE, ProtoItem, Location} from '../../core/MathItem.js';\nimport {StyleList} from '../../util/StyleList.js';\n\n/*****************************************************************/\n/**\n * List of Lists of pairs consisting of a DOM node and its text length\n *\n * These represent the Text elements that make up a single\n * string in the list of strings to be searched for math\n * (multiple consecutive Text nodes can form a single string).\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n */\nexport type HTMLNodeArray<N, T> = [N | T, number][][];\n\n/*****************************************************************/\n/**\n *  The HTMLDocument class (extends AbstractMathDocument)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HTMLDocument<N, T, D> extends AbstractMathDocument<N, T, D> {\n\n  /**\n   * The kind of document\n   */\n  public static KIND: string = 'HTML';\n\n  /**\n   * The default options for HTMLDocument\n   */\n  public static OPTIONS: OptionList = {\n    ...AbstractMathDocument.OPTIONS,\n    renderActions: expandable({\n      ...AbstractMathDocument.OPTIONS.renderActions,\n      styles: [STATE.INSERTED + 1, '', 'updateStyleSheet', false]  // update styles on a rerender() call\n    }),\n    MathList: HTMLMathList,           // Use the HTMLMathList for MathLists\n    MathItem: HTMLMathItem,           // Use the HTMLMathItem for MathItem\n    DomStrings: null                  // Use the default DomString parser\n  };\n\n  /**\n   * Extra styles to be included in the document's stylesheet (added by extensions)\n   */\n  protected styles: StyleList[];\n\n  /**\n   * The DomString parser for locating the text in DOM trees\n   */\n  public domStrings: HTMLDomStrings<N, T, D>;\n\n  /**\n   * @override\n   * @constructor\n   * @extends {AbstractMathDocument}\n   */\n  constructor(document: any, adaptor: DOMAdaptor<N, T, D>, options: OptionList) {\n    let [html, dom] = separateOptions(options, HTMLDomStrings.OPTIONS);\n    super(document, adaptor, html);\n    this.domStrings = this.options['DomStrings'] || new HTMLDomStrings<N, T, D>(dom);\n    this.domStrings.adaptor = adaptor;\n    this.styles = [];\n  }\n\n  /**\n   * Creates a Location object for a delimiter at the position given by index in the N's string\n   *  of the array of strings searched for math, recovering the original DOM node where the delimiter\n   *  was found.\n   *\n   * @param {number} N             The index of the string in the string array\n   * @param {number} index         The position within the N's string that needs to be found\n   * @param {string} delim         The delimiter for this position\n   * @param {HTMLNodeArray} nodes  The list of node lists representing the string array\n   * @return {Location}            The Location object for the position of the delimiter in the document\n   */\n  protected findPosition(N: number, index: number, delim: string, nodes: HTMLNodeArray<N, T>): Location<N, T> {\n    const adaptor = this.adaptor;\n    for (const list of nodes[N]) {\n      let [node, n] = list;\n      if (index <= n && adaptor.kind(node) === '#text') {\n        return {node: node, n: Math.max(index, 0), delim: delim};\n      }\n      index -= n;\n    }\n    return {node: null, n: 0, delim: delim};\n  }\n\n  /**\n   * Convert a ProtoItem to a MathItem (i.e., determine the actual Location\n   *  objects for its start and end)\n   *\n   * @param {ProtoItem} item       The proto math item to turn into an actual MathItem\n   * @param {InputJax} jax         The input jax to use for the MathItem\n   * @param {HTMLNodeArray} nodes  The array of node lists that produced the string array\n   * @return {HTMLMathItem}        The MathItem for the given proto item\n   */\n  protected mathItem(item: ProtoItem<N, T>, jax: InputJax<N, T, D>,\n                     nodes: HTMLNodeArray<N, T>): HTMLMathItem<N, T, D> {\n                       let math = item.math;\n                       let start = this.findPosition(item.n, item.start.n, item.open, nodes);\n                       let end = this.findPosition(item.n, item.end.n, item.close, nodes);\n                       return new this.options.MathItem(math, jax, item.display, start, end) as HTMLMathItem<N, T, D>;\n                     }\n\n  /**\n   * Find math within the document:\n   *  Get the list of containers (default is document.body), and for each:\n   *    For each input jax:\n   *      Make a new MathList to store the located math\n   *      If the input jax processes strings:\n   *        If we haven't already made the string array and corresponding node list, do so\n   *        Ask the jax to find the math in the string array, and\n   *          for each one, push it onto the math list\n   *      Otherwise (the jax processes DOM nodes):\n   *        Ask the jax to find the math in the container, and\n   *          for each one, make the result into a MathItem, and push it on the list\n   *      Merge the new math list into the document's math list\n   *        (we use merge to maintain a sorted list of MathItems)\n   *\n   * @override\n   */\n  public findMath(options: OptionList) {\n    if (!this.processed.isSet('findMath')) {\n      this.adaptor.document = this.document;\n      options = userOptions({elements: this.options.elements || [this.adaptor.body(this.document)]}, options);\n      for (const container of this.adaptor.getElements(options['elements'], this.document)) {\n        let [strings, nodes] = [null, null] as [string[], HTMLNodeArray<N, T>];\n        for (const jax of this.inputJax) {\n          let list = new (this.options['MathList'])();\n          if (jax.processStrings) {\n            if (strings === null) {\n              [strings, nodes] = this.domStrings.find(container);\n            }\n            for (const math of jax.findMath(strings)) {\n              list.push(this.mathItem(math, jax, nodes));\n            }\n          } else {\n            for (const math of jax.findMath(container)) {\n              let item: HTMLMathItem<N, T, D> =\n                new this.options.MathItem(math.math, jax, math.display, math.start, math.end);\n              list.push(item);\n            }\n          }\n          this.math.merge(list);\n        }\n      }\n      this.processed.set('findMath');\n    }\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public updateDocument() {\n    if (!this.processed.isSet('updateDocument')) {\n      this.addPageElements();\n      this.addStyleSheet();\n      super.updateDocument();\n      this.processed.set('updateDocument');\n    }\n    return this;\n  }\n\n  /**\n   *  Add any elements needed for the document\n   */\n  protected addPageElements() {\n    const body = this.adaptor.body(this.document);\n    const node = this.documentPageElements();\n    if (node) {\n      this.adaptor.append(body, node);\n    }\n  }\n\n  /**\n   * Add the stylesheet to the document\n   */\n  public addStyleSheet() {\n    const sheet = this.documentStyleSheet();\n    const adaptor = this.adaptor;\n    if (sheet && !adaptor.parent(sheet)) {\n      const head = adaptor.head(this.document);\n      let styles = this.findSheet(head, adaptor.getAttribute(sheet, 'id'));\n      if (styles) {\n        adaptor.replace(sheet, styles);\n      } else {\n        adaptor.append(head, sheet);\n      }\n    }\n  }\n\n  /**\n   * @param {N} head     The document <head>\n   * @param {string} id  The id of the stylesheet to find\n   * @param {N|null}     The stylesheet with the given ID\n   */\n  protected findSheet(head: N, id: string) {\n    if (id) {\n      for (const sheet of this.adaptor.tags(head, 'style')) {\n        if (this.adaptor.getAttribute(sheet, 'id') === id) {\n          return sheet;\n        }\n      }\n    }\n    return null as N;\n  }\n\n  /**\n   * @override\n   */\n  public removeFromDocument(restore: boolean = false) {\n    if (this.processed.isSet('updateDocument')) {\n      for (const math of this.math) {\n        if (math.state() >= STATE.INSERTED) {\n          math.state(STATE.TYPESET, restore);\n        }\n      }\n    }\n    this.processed.clear('updateDocument');\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public documentStyleSheet() {\n    return this.outputJax.styleSheet(this);\n  }\n\n  /**\n   * @override\n   */\n  public documentPageElements() {\n    return this.outputJax.pageElements(this);\n  }\n\n  /**\n   * Add styles to be included in the document's stylesheet\n   *\n   * @param {StyleList} styles   The styles to include\n   */\n  public addStyles(styles: StyleList) {\n    this.styles.push(styles);\n  }\n\n  /**\n   * Get the array of document-specific styles\n   */\n  public getStyles() {\n    return this.styles;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the HTMLHandler class\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractHandler} from '../../core/Handler.js';\nimport {MinHTMLAdaptor} from '../../adaptors/HTMLAdaptor.js';\nimport {HTMLDocument} from './HTMLDocument.js';\nimport {OptionList} from '../../util/Options.js';\n\n/*****************************************************************/\n/**\n *  Implements the HTMLHandler class (extends AbstractHandler)\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class HTMLHandler<N, T, D> extends AbstractHandler<N, T, D> {\n\n  /**\n   * The DOMAdaptor for the document being handled\n   */\n  public adaptor: MinHTMLAdaptor<N, T, D>;  // declare a more specific adaptor type\n\n  /**\n   * @override\n   */\n  public documentClass = HTMLDocument;\n\n  /**\n   * @override\n   */\n  public handlesDocument(document: any) {\n    const adaptor = this.adaptor;\n    if (typeof(document) === 'string') {\n      try {\n        document = adaptor.parse(document, 'text/html');\n      } catch (err) {}\n    }\n    if (document instanceof adaptor.window.Document ||\n        document instanceof adaptor.window.HTMLElement ||\n        document instanceof adaptor.window.DocumentFragment) {\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * If the document isn't already a Document object, create one\n   * using the given data\n   *\n   * @override\n   */\n  public create(document: any, options: OptionList) {\n    const adaptor = this.adaptor;\n    if (typeof(document) === 'string') {\n      document = adaptor.parse(document, 'text/html');\n    } else if (document instanceof adaptor.window.HTMLElement ||\n               document instanceof adaptor.window.DocumentFragment) {\n      let child = document as N;\n      document = adaptor.parse('', 'text/html');\n      adaptor.append(adaptor.body(document), child);\n    }\n    return super.create(document, options) as HTMLDocument<N, T, D>;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Registers the HTML document type\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {mathjax} from '../mathjax.js';\nimport {HTMLHandler} from './html/HTMLHandler.js';\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\n\n/**\n * Create the HTML handler object and register it with MathJax.\n *\n * @param {DOMAdaptor<N,T,D>} adaptor  The DOM adaptor to use with HTML\n * @return {HTMLHandler}               The newly created handler\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport function RegisterHTMLHandler<N, T, D>(adaptor: DOMAdaptor<N, T, D>): HTMLHandler<N, T, D> {\n  const handler = new HTMLHandler<N, T, D>(adaptor);\n  mathjax.handlers.register(handler);\n  return handler;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4Ba,YAAA,MAAM,OAAM;AAkBzB,QAAA,WAAA,2BAAA;AAmBE,eAAAA,UAAY,MAAgB;AAAhB,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAgB;AAVrB,aAAA,OAA4B;AAI5B,aAAA,OAA4B;AAOjC,aAAK,OAAO;MACd;AACF,aAAAA;IAAA,EAtBA;AAAa,YAAA,WAAA;AA+Bb,QAAA,aAAA,WAAA;AAiBE,eAAAC,cAAA;AAAY,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,eAAA,EAAA,IAAA,UAAA,EAAA;;AACV,aAAK,OAAO,IAAI,SAAoB,QAAA,GAAG;AACvC,aAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AACvC,aAAK,KAAI,MAAT,MAAI,cAAA,CAAA,GAAA,OAAS,IAAI,GAAA,KAAA,CAAA;MACnB;AASO,MAAAA,YAAA,UAAA,WAAP,SAAgB,GAAc,GAAY;AACxC,eAAO,IAAI;MACb;AAQO,MAAAA,YAAA,UAAA,OAAP,WAAA;;AAAY,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,eAAA,EAAA,IAAA,UAAA,EAAA;;;AACV,mBAAmB,SAAA,SAAA,IAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAApB,gBAAM,OAAI,SAAA;AACb,gBAAI,OAAO,IAAI,SAAoB,IAAI;AACvC,iBAAK,OAAO,KAAK;AACjB,iBAAK,OAAO,KAAK,KAAK;AACtB,iBAAK,KAAK,OAAO;AACjB,iBAAK,KAAK,OAAO;;;;;;;;;;;AAEnB,eAAO;MACT;AAOO,MAAAA,YAAA,UAAA,MAAP,WAAA;AACE,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,KAAK,SAAS,QAAA,KAAK;AACrB,iBAAO;;AAET,aAAK,KAAK,OAAO,KAAK;AACtB,aAAK,KAAK,OAAO,KAAK;AACtB,aAAK,OAAO,KAAK,OAAO;AACxB,eAAO,KAAK;MACd;AAQO,MAAAA,YAAA,UAAA,UAAP,WAAA;;AAAe,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,eAAA,EAAA,IAAA,UAAA,EAAA;;;AACb,mBAAmB,KAAA,SAAA,KAAK,MAAM,CAAC,EAAE,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,gBAAM,OAAI,GAAA;AACb,gBAAI,OAAO,IAAI,SAAoB,IAAI;AACvC,iBAAK,OAAO,KAAK,KAAK;AACtB,iBAAK,OAAO,KAAK;AACjB,iBAAK,KAAK,OAAO;AACjB,iBAAK,KAAK,OAAO;;;;;;;;;;;AAEnB,eAAO;MACT;AAOO,MAAAA,YAAA,UAAA,QAAP,WAAA;AACE,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,KAAK,SAAS,QAAA,KAAK;AACrB,iBAAO;;AAET,aAAK,KAAK,OAAO,KAAK;AACtB,aAAK,KAAK,OAAO,KAAK;AACtB,aAAK,OAAO,KAAK,OAAO;AACxB,eAAO,KAAK;MACd;AAOO,MAAAA,YAAA,UAAA,SAAP,WAAA;;AAAc,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAqB;AAArB,gBAAA,EAAA,IAAA,UAAA,EAAA;;AACZ,YAAM,MAAM,oBAAI,IAAG;;AACnB,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,SAAI,UAAA;AACb,gBAAI,IAAI,QAAM,IAAI;;;;;;;;;;;AAEpB,YAAI,OAAO,KAAK,KAAK;AACrB,eAAO,KAAK,SAAS,QAAA,KAAK;AACxB,cAAM,OAAO,KAAK;AAClB,cAAI,IAAI,IAAI,KAAK,IAAiB,GAAG;AACnC,iBAAK,KAAK,OAAO,KAAK;AACtB,iBAAK,KAAK,OAAO,KAAK;AACtB,iBAAK,OAAO,KAAK,OAAO;;AAE1B,iBAAO;;MAEX;AAOO,MAAAA,YAAA,UAAA,QAAP,WAAA;AACE,aAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO;AAC5C,aAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AACvC,eAAO;MACT;AAOQ,MAAAA,YAAA,UAAC,OAAO,QAAQ,IAAxB,WAAA;;;;;AACM,wBAAU,KAAK,KAAK;;;oBAEjB,QAAQ,SAAS,QAAA,KAAG,QAAA,CAAA,GAAA,CAAA;AACzB,qBAAA,CAAA,GAAM,QAAQ,IAAiB;;AAA/B,iBAAA,KAAA;AACA,wBAAU,QAAQ;;;;;;;AASd,MAAAA,YAAA,UAAA,WAAR,WAAA;;;;;AACM,wBAAU,KAAK,KAAK;;;oBAEjB,QAAQ,SAAS,QAAA,KAAG,QAAA,CAAA,GAAA,CAAA;AACzB,qBAAA,CAAA,GAAM,QAAQ,IAAiB;;AAA/B,iBAAA,KAAA;AACA,wBAAU,QAAQ;;;;;;;AAWf,MAAAA,YAAA,UAAA,SAAP,SAAc,MAAiB,UAAkC;AAAlC,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAkC;AAC/D,YAAI,aAAa,MAAM;AACrB,qBAAW,KAAK,SAAS,KAAK,IAAI;;AAEpC,YAAI,OAAO,IAAI,SAAoB,IAAI;AACvC,YAAI,MAAM,KAAK,KAAK;AACpB,eAAO,IAAI,SAAS,QAAA,OAAO,SAAS,IAAI,MAAmB,KAAK,IAAiB,GAAG;AAClF,gBAAM,IAAI;;AAEZ,aAAK,OAAO,IAAI;AAChB,aAAK,OAAO;AACZ,YAAI,KAAK,OAAO,IAAI,OAAO;AAC3B,eAAO;MACT;AAQO,MAAAA,YAAA,UAAA,OAAP,SAAY,UAAkC;;AAAlC,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAkC;AAC5C,YAAI,aAAa,MAAM;AACrB,qBAAW,KAAK,SAAS,KAAK,IAAI;;AAKpC,YAAI,QAAiC,CAAA;;AACrC,mBAAmB,KAAA,SAAA,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApB,gBAAM,OAAI,GAAA;AACb,kBAAM,KAAK,IAAIA,YAAsB,IAAiB,CAAC;;;;;;;;;;;AAKzD,aAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AAIvC,eAAO,MAAM,SAAS,GAAG;AACvB,cAAI,KAAK,MAAM,MAAK;AACpB,cAAI,KAAK,MAAM,MAAK;AACpB,aAAG,MAAM,IAAI,QAAQ;AACrB,gBAAM,KAAK,EAAE;;AAKf,YAAI,MAAM,QAAQ;AAChB,eAAK,OAAO,MAAM,CAAC,EAAE;;AAEvB,eAAO;MACT;AASO,MAAAA,YAAA,UAAA,QAAP,SAAa,MAA6B,UAAkC;;AAAlC,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAkC;AAC1E,YAAI,aAAa,MAAM;AACrB,qBAAW,KAAK,SAAS,KAAK,IAAI;;AAKpC,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,OAAO,KAAK,KAAK;AAIrB,eAAO,KAAK,SAAS,QAAA,OAAO,KAAK,SAAS,QAAA,KAAK;AAW7C,cAAI,SAAS,KAAK,MAAmB,KAAK,IAAiB,GAAG;AAC5D,iBAAA,OAAmC,CAAC,MAAM,IAAI,GAAC,CAAA,GAA9C,KAAK,KAAK,OAAI,GAAA,CAAA,GAAE,KAAK,KAAK,OAAI,GAAA,CAAA;AAC/B,iBAAA,OAAyB,CAAC,KAAK,MAAM,KAAK,IAAI,GAAC,CAAA,GAA9C,KAAK,OAAI,GAAA,CAAA,GAAE,KAAK,OAAI,GAAA,CAAA;AACrB,iBAAA,OAA6C,CAAC,KAAK,MAAM,KAAK,IAAI,GAAC,CAAA,GAAlE,KAAK,KAAK,KAAK,OAAI,GAAA,CAAA,GAAE,KAAK,KAAK,KAAK,OAAI,GAAA,CAAA;AACzC,iBAAA,OAAmC,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,GAAC,CAAA,GAAlE,KAAK,KAAK,OAAI,GAAA,CAAA,GAAE,KAAK,KAAK,OAAI,GAAA,CAAA;AAC/B,iBAAA,OAAe,CAAC,KAAK,MAAM,IAAI,GAAC,CAAA,GAA/B,OAAI,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;iBACN;AACL,mBAAO,KAAK;;;AAOhB,YAAI,KAAK,SAAS,QAAA,KAAK;AACrB,eAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAChC,eAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAChC,eAAK,KAAK,KAAK,OAAO,KAAK;AAC3B,eAAK,KAAK,OAAO,KAAK,KAAK;AAC3B,eAAK,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK;;AAEzC,eAAO;MACT;AACF,aAAAA;IAAA,EAnRA;AAAa,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtDb,QAAA,kBAAA;AA6BA,QAAA,mBAAA,SAAA,QAAA;AACA,gBAAAC,mBAAA,MAAA;AADA,eAAAA,oBAAA;;MAUA;AAJS,MAAAA,kBAAA,UAAA,WAAP,SAAgB,GAAsB,GAAoB;AACxD,eAAQ,EAAE,MAAM,IAAI,EAAE,MAAM,KAAM,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,MAAM;MACnF;AAEF,aAAAA;IAAA,EATA,gBAAA,UAAU;AADY,YAAA,mBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BtB,QAAA,eAAA;AA0BA,QAAA,sBAAA,SAAA,QAAA;AAAiG,gBAAAC,sBAAA,MAAA;AAAjG,eAAAA,uBAAA;;MAQA;AAJS,MAAAA,qBAAA,UAAA,SAAP,SAAc,MAAc,YAA+B,UAAkB;AAAjD,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA6B;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAkB;AAC3E,eAAO,KAAK,KAAK,IAAI,EAAE,YAAY,QAAQ;MAC7C;AAEF,aAAAA;IAAA,EARiG,aAAA,eAAe;AAA1F,YAAA,sBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1BtB,QAAA,eAAA;AACA,QAAA,kBAAA;AAOA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MA4DA;AAxCE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,WAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO,KAAK,WAAW,CAAC,KAAK,KAAK,WAAW,CAAC,EAAE,WAAW,WAAW;QACxE;;;;AAOU,MAAAA,WAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,YAAI,cAAc,KAAK,WAAW,YAAY,aAAa;AAC3D,YAAI,eAAe,MAAM;AACvB,wBAAc,YAAY,SAAQ;AAClC,cAAI,YAAY,MAAM,UAAU,GAAG;AACjC,qBAAS,SAAS,WAAW;iBACxB;AACL,oBAAQ,SAAS,WAAW;;AAE9B,kBAAQ;;AAEV,YAAI,eAAe,KAAK,WAAW,YAAY,cAAc;AAC7D,YAAI,gBAAgB,MAAM;AACxB,oBAAW,iBAAiB;AAC5B,kBAAQ;;AAEV,YAAM,UAAU,KAAK,WAAW,YAAY,cAAc;AAC1D,YAAI,WAAW,MAAM;AACnB,kBAAQ;;AAEV,qBAAa,KAAK,uBAAuB,YAAY,KAAK,WAAW,iBAAgB,CAAE;AACvF,aAAK,WAAW,CAAC,EAAE,uBAAuB,YAAY,SAAS,OAAO,KAAK;MAC7E;AArDc,MAAAA,WAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,sBAAsB,QAAQ,GAAA,EACjC,aAAa,gBAAA,SACb,cAAc,gBAAA,SACd,sBAAsB,IAAI,KAAK,KAAK,CAAC,GACrC,eAAe,OACf,gBAAgB,gBAAA,SAChB,WAAW,gBAAA,SACX,KAAK,gBAAA,SACL,qBAAqB,SAAQ,CAAA;AA8CjC,aAAAA;MA5D+B,aAAA,qBAAqB;AAAvC,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRb,QAAA,eAAA;AACA,QAAA,kBAAA;AAOA,QAAA,iBAAA,SAAA,QAAA;AAAoC,gBAAAC,iBAAA,MAAA;AAApC,eAAAA,kBAAA;;MAkCA;AArBE,aAAA,eAAWA,gBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,gBAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AAMU,MAAAA,gBAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;AAC9G,qBAAa,KAAK,uBAAuB,YAAY,KAAK,WAAW,iBAAgB,CAAE;AACvF,eAAA,UAAM,4BAA2B,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;MACrE;AA3Bc,MAAAA,gBAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,sBAAsB,QAAQ,GAAA,EACjC,YAAY,gBAAA,QAAO,CAAA;AA2BvB,aAAAA;MAlCoC,aAAA,qBAAqB;AAA5C,YAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRb,QAAA,eAAA;AAOA,QAAA,gBAAA,SAAA,QAAA;AAAmC,gBAAAC,gBAAA,MAAA;AAAnC,eAAAA,iBAAA;;MAiCA;AApBE,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,eAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,eAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AA1Bc,MAAAA,eAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,gBAAgB,QAAQ,GAAA,EAC3B,MAAM,OAAM,CAAA;AA0BhB,aAAAA;MAjCmC,aAAA,eAAe;AAArC,YAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPb,QAAA,eAAA;AAUA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MA6CA;AAjCE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,YAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,YAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AAQO,MAAAA,YAAA,UAAA,yBAAP,SAA8B,YAA2B,SAAkB,OAAe,OAAc;AACtG,YAAM,YAAa,UAAU,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,CAAC,CAAC,IAAI;AACnE,YAAM,QAAQ,KAAK,WAAW,SAAS,KAAK,KAAK,QAAQ,OAAO,MAAM;AACtE,aAAK,OAAO,aAAa,OAAO,IAAI;AACpC,cAAM,uBAAuB,YAAY,SAAS,OAAO,KAAK;MAChE;AAtCc,MAAAA,YAAA,WAAQ,SAAA,CAAA,GACjB,aAAA,oBAAoB,QAAQ;AAuCnC,aAAAA;MA7CgC,aAAA,mBAAmB;AAAtC,YAAA,aAAA;;;;;;;;;;;ACXb,QAAA,eAAA;AAEA,QAAA,YAAA;AAEA,QAAA,UAAA;AACA,QAAA,UAAA;AACA,QAAA,UAAA;AACA,QAAA,aAAA;AACA,QAAA,cAAA;AACA,QAAA,UAAA;AAEA,QAAA,YAAA;AACA,QAAA,aAAA;AACA,QAAA,aAAA;AACA,QAAA,aAAA;AACA,QAAA,cAAA;AACA,QAAA,cAAA;AACA,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,eAAA;AACA,QAAA,gBAAA;AAEA,QAAA,eAAA;AAEA,QAAA,eAAA;AACA,QAAA,kBAAA;AACA,QAAA,qBAAA;AAEA,QAAA,cAAA;AACA,QAAA,WAAA;AACA,QAAA,WAAA;AACA,QAAA,mBAAA;AACA,QAAA,kBAAA;AAEA,QAAA,cAAA;AAEA,QAAA,iBAAA;AAEA,QAAA,eAAA;AACA,QAAA,kBAAA;AAUW,YAAA,OAAG,KAAA,CAAA,GACZ,GAAC,UAAA,QAAQ,UAAU,IAAI,IAAG,UAAA,SAE1B,GAAC,QAAA,MAAM,UAAU,IAAI,IAAG,QAAA,OACxB,GAAC,QAAA,MAAM,UAAU,IAAI,IAAG,QAAA,OACxB,GAAC,QAAA,MAAM,UAAU,IAAI,IAAG,QAAA,OACxB,GAAC,WAAA,SAAS,UAAU,IAAI,IAAG,WAAA,UAC3B,GAAC,YAAA,UAAU,UAAU,IAAI,IAAG,YAAA,WAC5B,GAAC,QAAA,MAAM,UAAU,IAAI,IAAG,QAAA,OAExB,GAAC,UAAA,QAAQ,UAAU,IAAI,IAAG,UAAA,SAC1B,GAAC,UAAA,gBAAgB,UAAU,IAAI,IAAG,UAAA,iBAClC,GAAC,WAAA,SAAS,UAAU,IAAI,IAAG,WAAA,UAC3B,GAAC,WAAA,SAAS,UAAU,IAAI,IAAG,WAAA,UAC3B,GAAC,WAAA,SAAS,UAAU,IAAI,IAAG,WAAA,UAC3B,GAAC,YAAA,UAAU,UAAU,IAAI,IAAG,YAAA,WAC5B,GAAC,YAAA,UAAU,UAAU,IAAI,IAAG,YAAA,WAC5B,GAAC,aAAA,WAAW,UAAU,IAAI,IAAG,aAAA,YAC7B,GAAC,cAAA,YAAY,UAAU,IAAI,IAAG,cAAA,aAC9B,GAAC,aAAA,WAAW,UAAU,IAAI,IAAG,aAAA,YAC7B,GAAC,cAAA,YAAY,UAAU,IAAI,IAAG,cAAA,aAE9B,GAAC,aAAA,WAAW,UAAU,IAAI,IAAG,aAAA,YAE7B,GAAC,aAAA,QAAQ,UAAU,IAAI,IAAG,aAAA,SAC1B,GAAC,aAAA,QAAQ,UAAU,IAAI,IAAG,aAAA,SAC1B,GAAC,aAAA,WAAW,UAAU,IAAI,IAAG,aAAA,YAC7B,GAAC,gBAAA,UAAU,UAAU,IAAI,IAAG,gBAAA,WAC5B,GAAC,gBAAA,SAAS,UAAU,IAAI,IAAG,gBAAA,UAC3B,GAAC,gBAAA,cAAc,UAAU,IAAI,IAAG,gBAAA,eAChC,GAAC,mBAAA,iBAAiB,UAAU,IAAI,IAAG,mBAAA,kBACnC,GAAC,mBAAA,eAAe,UAAU,IAAI,IAAG,mBAAA,gBACjC,GAAC,mBAAA,QAAQ,UAAU,IAAI,IAAG,mBAAA,SAE1B,GAAC,YAAA,UAAU,UAAU,IAAI,IAAG,YAAA,WAC5B,GAAC,SAAA,cAAc,UAAU,IAAI,IAAG,SAAA,eAChC,GAAC,SAAA,OAAO,UAAU,IAAI,IAAG,SAAA,QACzB,GAAC,SAAA,OAAO,UAAU,IAAI,IAAG,SAAA,QACzB,GAAC,iBAAA,eAAe,UAAU,IAAI,IAAG,iBAAA,gBACjC,GAAC,gBAAA,cAAc,UAAU,IAAI,IAAG,gBAAA,eAEhC,GAAC,YAAA,UAAU,UAAU,IAAI,IAAG,YAAA,WAE5B,GAAC,eAAA,aAAa,UAAU,IAAI,IAAG,eAAA,cAC/B,GAAC,eAAA,cAAc,UAAU,IAAI,IAAG,eAAA,eAChC,GAAC,eAAA,iBAAiB,UAAU,IAAI,IAAG,eAAA,kBAEnC,GAAC,aAAA,QAAQ,UAAU,IAAI,IAAG,aAAA,SAC1B,GAAC,gBAAA,WAAW,UAAU,IAAI,IAAG,gBAAA,YAE7B,GAAC,aAAA,SAAS,UAAU,IAAI,IAAG,aAAA,UAC3B,GAAC,aAAA,QAAQ,UAAU,IAAI,IAAG,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpG5B,QAAA,mBAAA;AAEA,QAAA,WAAA;AAOA,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MAeA;AAJE,aAAA,eAAIA,YAAA,WAAA,OAAG;aAAP,WAAA;AACE,iBAAO,KAAK;QACd;;;;AARc,MAAAA,YAAA,eAAe,SAAA;AAU/B,aAAAA;MAfgC,iBAAA,mBAAmB;AAAtC,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTb,QAAA,WAAA,WAAA;AAAA,eAAAC,YAAA;AAoBY,aAAA,OAAe;MAmE3B;AA9DgB,MAAAA,UAAA,WAAd,WAAA;;AAAuB,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,gBAAA,EAAA,IAAA,UAAA,EAAA;;;AACrB,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,SAAI,UAAA;AACb,gBAAI,KAAK,IAAI,MAAI,GAAG;AAClB,oBAAM,IAAI,MAAM,+BAA+B,MAAI;;AAErD,gBAAI,KAAK,SAASA,UAAS,QAAQ;AACjC,oBAAM,IAAI,MAAM,0CAA0C;;AAE5D,iBAAK,MAAM,IAAI,QAAM,KAAK,IAAI;AAC9B,iBAAK,SAAS;;;;;;;;;;;MAElB;AAMc,MAAAA,UAAA,MAAd,SAAkB,MAAY;AAC5B,eAAO,KAAK,MAAM,IAAI,IAAI;MAC5B;AAKO,MAAAA,UAAA,UAAA,MAAP,SAAW,MAAY;AACrB,aAAK,QAAQ,KAAK,OAAO,IAAI;MAC/B;AAKO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAY;AACvB,aAAK,QAAQ,CAAC,KAAK,OAAO,IAAI;MAChC;AAMO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAY;AACvB,eAAO,CAAC,EAAE,KAAK,OAAO,KAAK,OAAO,IAAI;MACxC;AAKO,MAAAA,UAAA,UAAA,QAAP,WAAA;AACE,aAAK,OAAO;MACd;AAMU,MAAAA,UAAA,UAAA,SAAV,SAAiB,MAAY;AAC3B,YAAM,MAAO,KAAK,YAAgC,MAAM,IAAI,IAAI;AAChE,YAAI,CAAC,KAAK;AACR,gBAAM,IAAI,MAAM,6BAA6B,IAAI;;AAEnD,eAAO;MACT;AAhFiB,MAAAA,UAAA,SAAS,KAAK;AAKd,MAAAA,UAAA,OAAe;AAKf,MAAAA,UAAA,QAA6B,oBAAI,IAAG;AAwEvD,aAAAA;MAvFA;AAAa,YAAA,WAAA;AA6Fb,aAAgB,gBAAa;AAAC,UAAA,QAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,cAAA,EAAA,IAAA,UAAA,EAAA;;AAC5B,UAAM,OAAI,SAAA,QAAA;AAAiB,kBAAAC,OAAA,MAAA;AAAd,iBAAAA,QAAA;;QAAwB;AAAA,eAAAA;MAAA,EAAV,QAAQ;AACnC,WAAK,SAAQ,MAAb,MAAI,cAAA,CAAA,GAAA,OAAa,KAAK,GAAA,KAAA,CAAA;AACtB,aAAO;IACT;AAJA,YAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7FA,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,iBAAA;AACA,QAAA,gBAAA;AACA,QAAA,gBAAA;AAEA,QAAA,kBAAA;AAEA,QAAA,gBAAA;AAEA,QAAA,uBAAA;AAyEA,QAAA,aAAA,SAAA,QAAA;AAAyC,gBAAAC,aAAA,MAAA;AAAzC,eAAAA,cAAA;;MA4HA;AApHgB,MAAAA,YAAA,SAAd,SAA8B,SAA+B;;AAC3D,YAAM,OAAO,IAAI,KAAI;;AACrB,mBAAiB,KAAA,SAAA,OAAO,KAAK,OAAO,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAlC,gBAAM,KAAE,GAAA;AACL,gBAAA,KAAA,OAAqB,KAAK,OAAgB,IAAI,QAAQ,EAAE,CAAC,GAAC,CAAA,GAAzD,SAAM,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AACvB,gBAAI,UAAU;AACZ,mBAAK,IAAI,QAAQ,QAAQ;;;;;;;;;;;;AAG7B,eAAO;MACT;AAUc,MAAAA,YAAA,SAAd,SAA8B,IAAY,QAA6B;;AACrE,YAAI,WAAW;AACf,YAAI,UAAU;AACd,YAAI,WAAW,OAAO,CAAC;AACvB,YAAI,OAAO,WAAW,KAAK,OAAO,OAAO,CAAC,MAAM,WAAW;AACzD,iBAAO,WAAW,MAAM,UAAU,OAAO,CAAC;AAC1C,eAAA,OAA0B,KAAK,cAAc,EAAE,GAAC,CAAA,GAA/C,YAAS,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA;mBACb,OAAO,OAAO,CAAC,MAAM,UAAU;AACxC,cAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,mBAAO,WAAW,MAAM,UAAU,OAAO,CAAC;AACpC,gBAAA,KAAA,OAAqB,OAAO,MAAM,CAAC,GAAqB,CAAA,GAAvD,UAAO,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AACvB,iBAAA,OAA0B,KAAK,cAAc,SAAS,OAAO,GAAC,CAAA,GAA7D,YAAS,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA;iBACjB;AACL,mBAAO,WAAW,MAAM,UAAU,OAAO,CAAC;AAC1C,iBAAA,OAA0B,KAAK,cAAc,OAAO,CAAC,CAAW,GAAC,CAAA,GAAhE,YAAS,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA;;eAEnB;AACL,iBAAO,WAAW,MAAM,UAAU,OAAO,CAAC;AAC1C,eAAA,OAA0B,OAAO,MAAM,CAAC,GAA8C,CAAA,GAArF,YAAS,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA;;AAExB,eAAO,CAAC,EAAC,IAAI,WAAW,YAAY,QAAO,GAA0B,QAAQ;MAC/E;AASiB,MAAAA,YAAA,gBAAjB,SAA+B,SAAiB,SAAyB;AAAzB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAyB;AACvE,eAAO;UACL,SAAC,UAAa;AAAM,uBAAW,SAAS,OAAO,EAAC;AAAI,mBAAO;UAAO;UAClE,SAAC,MAAW,UAAa;AAAM,uBAAW,KAAK,OAAO,EAAE,QAAQ;AAAG,mBAAO;UAAO;;MAErF;AAQO,MAAAA,YAAA,UAAA,YAAP,SAAiB,UAAiC,OAAiC;;AAAjC,YAAA,UAAA,QAAA;AAAA,kBAAgB,cAAA,MAAM;QAAW;;AACjF,mBAAmB,KAAA,SAAA,KAAK,KAAK,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA1B,gBAAM,OAAI,GAAA;AACb,gBAAI,KAAK,YAAY,OAAO;AAC1B,kBAAI,KAAK,KAAK,UAAU,QAAQ;AAAG;;;;;;;;;;;;MAGzC;AASO,MAAAA,YAAA,UAAA,aAAP,SAAkB,MAAyB,UAAiC,OAAiC;;AAAjC,YAAA,UAAA,QAAA;AAAA,kBAAgB,cAAA,MAAM;QAAW;;AAC3G,mBAAmB,KAAA,SAAA,KAAK,KAAK,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA1B,gBAAM,OAAI,GAAA;AACb,gBAAI,KAAK,YAAY,OAAO;AAC1B,kBAAI,KAAK,KAAK,WAAW,MAAM,QAAQ;AAAG;;;;;;;;;;;;MAGhD;AASO,MAAAA,YAAA,UAAA,gBAAP,SAAqB,MAAyB,UAAiC,KAAwB;;AAAxB,YAAA,QAAA,QAAA;AAAA,gBAAc,cAAA,MAAM;QAAI;;AACrG,mBAAmB,KAAA,SAAA,KAAK,KAAK,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA1B,gBAAM,OAAI,GAAA;AACb,gBAAI,KAAK,WAAW;AAAK;AACzB,gBAAI,KAAK,KAAK,SAAS;AACrB,kBAAI,KAAK,KAAK,WAAW,MAAM,QAAQ;AAAG;;;;;;;;;;;;MAGhD;AAQO,MAAAA,YAAA,UAAA,SAAP,SAAc,IAAU;;;AACtB,mBAAmB,KAAA,SAAA,KAAK,KAAK,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA1B,gBAAM,OAAI,GAAA;AACb,gBAAI,KAAK,KAAK,OAAO,IAAI;AACvB,qBAAO,KAAK;;;;;;;;;;;;AAGhB,eAAO;MACT;AAEF,aAAAA;IAAA,EA5HyC,qBAAA,eAAe;AAA3C,YAAA,aAAA;AAqJA,YAAA,eAA0B;MACrC,KAAK;MACL,WAAW;MACX,UAAU;MACV,WAAW;;AAMA,YAAA,kBAA6B;MACxC,KAAK;MACL,WAAW;MACX,UAAU,CAAA;MACV,WAAW,CAAA;;AA+Nb,QAAA,kBAAA,SAAA,QAAA;AAAuC,gBAAAC,kBAAA,MAAA;AAAvC,eAAAA,mBAAA;;MAOA;AAHS,MAAAA,iBAAA,UAAA,UAAP,SAAe,OAAwB;AACrC,eAAO;MACT;AACF,aAAAA;IAAA,EAPuC,cAAA,gBAAgB;AAgBvD,QAAA,mBAAA,SAAA,QAAA;AAAwC,gBAAAC,mBAAA,MAAA;AAAxC,eAAAA,oBAAA;;MAaA;AATS,MAAAA,kBAAA,UAAA,UAAP,SAAe,OAA0B,WAAuC;AAAvC,YAAA,cAAA,QAAA;AAAA,sBAAA;QAAuC;AAC9E,eAAO;MACT;AAIO,MAAAA,kBAAA,UAAA,UAAP,SAAe,OAA0B,WAAiC;AACxE,eAAO;MACT;AACF,aAAAA;IAAA,EAbwC,eAAA,iBAAiB;AAsBzD,QAAA,kBAAA,SAAA,QAAA;AAAuC,gBAAAC,kBAAA,MAAA;AAAvC,eAAAA,mBAAA;;MAAkE;AAAA,aAAAA;IAAA,EAA3B,cAAA,gBAAgB;AASvD,QAAA,kBAAA,SAAA,QAAA;AAAuC,gBAAAC,kBAAA,MAAA;AAAvC,eAAAA,mBAAA;;MAAkE;AAAA,aAAAA;IAAA,EAA3B,cAAA,gBAAgB;AAUvD,QAAA,uBAAA,WAAA;AAuFE,eAAAC,sBAAa,UAAa,SAA8B,SAAmB;AAA3E,YAAA,QAAA;AACE,YAAI,QAAQ,KAAK;AACjB,aAAK,WAAW;AAChB,aAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,OAAO;AACrE,aAAK,OAAO,KAAK,KAAK,QAAQ,UAAU,KAAK,iBAAgB;AAC7D,aAAK,gBAAgB,WAAW,OAAgB,KAAK,QAAQ,eAAe,CAAC;AAC7E,aAAK,YAAY,IAAIA,sBAAqB,YAAW;AACrD,aAAK,YAAY,KAAK,QAAQ,WAAW,KAAK,IAAI,iBAAgB;AAClE,YAAI,WAAW,KAAK,QAAQ,UAAU,KAAK,CAAC,IAAI,gBAAe,CAAW;AAC1E,YAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,qBAAW,CAAC,QAAQ;;AAEtB,aAAK,WAAW;AAIhB,aAAK,UAAU;AACf,aAAK,UAAU,WAAW,OAAO;AACjC,aAAK,SAAS,IAAI,SAAA,KAAG;AAAI,iBAAA,IAAI,WAAW,OAAO;QAAtB,CAAuB;AAIhD,aAAK,aAAa,KAAK,QAAQ,YAAY,KAAK,IAAI,gBAAA,WAAU;AAC9D,aAAK,SAAS,IAAI,SAAA,KAAG;AAAI,iBAAA,IAAI,cAAc,MAAK,UAAU;QAAjC,CAAkC;AAI3D,aAAK,UAAU,WAAU;AACzB,aAAK,SAAS,IAAI,SAAA,KAAG;AAAI,iBAAA,IAAI,WAAU;QAAd,CAAgB;MAC3C;AAKA,aAAA,eAAWA,sBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAQ,KAAK,YAA4C;QAC3D;;;;AAKO,MAAAA,sBAAA,UAAA,kBAAP,SAAuB,IAAU;AAAE,YAAA,SAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAgB;AAAhB,iBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC3B,YAAA,KAAA,OAAU,WAAW,OAAgB,IAAI,MAA+B,GAAC,CAAA,GAAxE,KAAE,GAAA,CAAA,GAAE,IAAC,GAAA,CAAA;AACZ,aAAK,cAAc,IAAI,IAAI,CAAC;MAC9B;AAKO,MAAAA,sBAAA,UAAA,qBAAP,SAA0B,IAAU;AAClC,YAAM,SAAS,KAAK,cAAc,OAAO,EAAE;AAC3C,YAAI,QAAQ;AACV,eAAK,cAAc,OAAO,MAAM;;MAEpC;AAKO,MAAAA,sBAAA,UAAA,SAAP,WAAA;AACE,aAAK,cAAc,UAAU,IAAI;AACjC,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,WAAP,SAAgB,OAA8B;AAA9B,YAAA,UAAA,QAAA;AAAA,kBAAgB,cAAA,MAAM;QAAQ;AAC5C,aAAK,MAAM,QAAQ,CAAC;AACpB,aAAK,OAAM;AACX,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,UAAP,SAAe,MAAc,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AAC/C,YAAA,MAA2E,GAAA,aAAA,aAAY;UACzF,QAAQ,KAAK,SAAS,CAAC,EAAE;UAAM,SAAS;UAAM,KAAK,cAAA,MAAM;UACzD,IAAI;UAAI,IAAI;UAAG,gBAAgB;UAAM,WAAW;UAAS,OAAO;UAAG,QAAQ;WAC1E,OAAO,GAHL,SAAM,GAAA,QAAE,UAAO,GAAA,SAAE,MAAG,GAAA,KAAE,KAAE,GAAA,IAAE,KAAE,GAAA,IAAE,iBAAc,GAAA,gBAAE,YAAS,GAAA,WAAE,QAAK,GAAA,OAAE,SAAM,GAAA;AAI3E,YAAI,mBAAmB,MAAM;AAC3B,2BAAiB,KAAK;;AAExB,YAAM,MAAM,KAAK,SAAS,OAAO,SAACC,MAAK,MAAI;AAAK,iBAAC,KAAK,SAAS,SAAS,OAAOA;QAA/B,GAAqC,IAAI;AACzF,YAAM,QAAQ,IAAI,KAAK,QAAQ,SAAS,MAAM,KAAK,OAAO;AAC1D,cAAM,MAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAClD,cAAM,WAAW,IAAI,IAAI,gBAAgB,WAAW,KAAK;AACzD,YAAI,KAAK,UAAU,QAAQ,kBAAkB;AAC3C,gBAAM,WAAW,cAAc;;AAEjC,YAAI,KAAK,UAAU,QAAQ,mBAAmB;AAC5C,gBAAM,WAAW,eAAe;;AAElC,cAAM,QAAQ,MAAM,GAAG;AACvB,eAAQ,MAAM,eAAe,MAAM;MACrC;AAKO,MAAAD,sBAAA,UAAA,WAAP,SAAgB,UAA2B;AAA3B,YAAA,aAAA,QAAA;AAAA,qBAAA;QAA2B;AACzC,aAAK,UAAU,IAAI,UAAU;AAC7B,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,UAAP,WAAA;;AACE,YAAI,CAAC,KAAK,UAAU,MAAM,SAAS,GAAG;AAIpC,cAAM,YAAY,CAAA;;AAClB,qBAAmB,KAAA,SAAA,KAAK,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzB,kBAAM,OAAI,GAAA;AACb,mBAAK,YAAY,IAAI;AACrB,kBAAI,KAAK,UAAU,cAAc,QAAW;AAC1C,0BAAU,KAAK,IAAI;;;;;;;;;;;;;AAOvB,qBAAmB,cAAA,SAAA,SAAS,GAAA,gBAAA,YAAA,KAAA,GAAA,CAAA,cAAA,MAAA,gBAAA,YAAA,KAAA,GAAE;AAAzB,kBAAM,OAAI,cAAA;AACb,kBAAM,OAAO,KAAK,UAAU;AAC5B,mBAAK,MAAM,KAAK,KAAK;AACrB,mBAAK,UAAU,YAAY;AAC3B,mBAAK,YAAY,IAAI;;;;;;;;;;;AAEvB,eAAK,UAAU,IAAI,SAAS;;AAE9B,eAAO;MACT;AAKU,MAAAA,sBAAA,UAAA,cAAV,SAAsB,MAAuB;AAC3C,YAAI;AACF,eAAK,QAAQ,IAAI;iBACV,KAAK;AACZ,cAAI,IAAI,SAAS,IAAI,SAAS;AAC5B,kBAAM;;AAER,eAAK,QAAQ,cAAc,EAAE,MAAM,MAAM,GAAG;AAC5C,eAAK,UAAU,OAAO,IAAI;;MAE9B;AAQO,MAAAA,sBAAA,UAAA,eAAP,SAAoB,MAAyB,KAAU;AACrD,aAAK,OAAO,KAAK,WAAW,OAAO,QAAQ,MAAM;UAC/C,KAAK,WAAW,OAAO,UAAU,EAAC,kBAAkB,IAAI,SAAS,OAAO,IAAI,QAAO,GAAG;YACpF,KAAK,WAAW,OAAO,SAAS,MAAM;cACnC,KAAK,WAAW,OAAO,MAAM,EAAe,QAAQ,kBAAkB;aACxE;WACF;SACF;AACD,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,WAAW,IAAI,WAAW,OAAO;;AAE7C,aAAK,UAAU,QAAQ,IAAI;MAC7B;AAKO,MAAAA,sBAAA,UAAA,UAAP,WAAA;;AACE,YAAI,CAAC,KAAK,UAAU,MAAM,SAAS,GAAG;;AACpC,qBAAmB,KAAA,SAAA,KAAK,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzB,kBAAM,OAAI,GAAA;AACb,kBAAI;AACF,qBAAK,QAAQ,IAAI;uBACV,KAAK;AACZ,oBAAI,IAAI,SAAS,IAAI,SAAS;AAC5B,wBAAM;;AAER,qBAAK,QAAQ,cAAc,EAAE,MAAM,MAAM,GAAG;AAC5C,qBAAK,WAAW,OAAO,IAAI;;;;;;;;;;;;AAG/B,eAAK,UAAU,IAAI,SAAS;;AAE9B,eAAO;MACT;AAQO,MAAAA,sBAAA,UAAA,eAAP,SAAoB,MAAyB,KAAU;AACrD,aAAK,cAAc,KAAK,QAAQ,KAAK,iBAAiB;UACpD,OAAO;UACP,KAAK,KAAK,UAAU;WACnB;UACD,KAAK,QAAQ,KAAK,QAAQ;YACxB,kBAAkB,IAAI;YACtB,OAAO,IAAI;YACX,OAAO;cACL,OAAO;cACP,oBAAoB;cACpB,eAAe;;aAEhB;YACD,KAAK,QAAQ,KAAK,mBAAmB;WACtC;SACF;AACD,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,cAAc,KAAK,aAAa;YAC3C,OAAO;cACL,SAAS;cACT,QAAQ;cACR,cAAc;;WAEjB;;AAEH,aAAK,WAAW,QAAQ,IAAI;MAC9B;AAKO,MAAAA,sBAAA,UAAA,aAAP,WAAA;AACE,YAAI,CAAC,KAAK,UAAU,MAAM,YAAY,GAAG;AACvC,eAAK,UAAU,WAAW,IAAI;AAC9B,eAAK,UAAU,IAAI,YAAY;;AAEjC,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,iBAAP,WAAA;;AACE,YAAI,CAAC,KAAK,UAAU,MAAM,gBAAgB,GAAG;;AAC3C,qBAAmB,KAAA,SAAA,KAAK,KAAK,SAAQ,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApC,kBAAM,OAAI,GAAA;AACb,mBAAK,eAAe,IAAI;;;;;;;;;;;AAE1B,eAAK,UAAU,IAAI,gBAAgB;;AAErC,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,qBAAP,SAA0B,UAAyB;AAAzB,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAyB;AACjD,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,QAAP,SAAa,OAAe,SAAwB;;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;;AAClD,mBAAmB,KAAA,SAAA,KAAK,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzB,gBAAM,OAAI,GAAA;AACb,iBAAK,MAAM,OAAO,OAAO;;;;;;;;;;;AAE3B,YAAI,QAAQ,cAAA,MAAM,UAAU;AAC1B,eAAK,UAAU,MAAM,gBAAgB;;AAEvC,YAAI,QAAQ,cAAA,MAAM,SAAS;AACzB,eAAK,UAAU,MAAM,SAAS;AAC9B,eAAK,UAAU,MAAM,YAAY;;AAEnC,YAAI,QAAQ,cAAA,MAAM,UAAU;AAC1B,eAAK,UAAU,MAAM,SAAS;;AAEhC,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,QAAP,SAAa,SAAsC;;AAAtC,YAAA,YAAA,QAAA;AAAA,oBAAA,EAAsB,WAAW,KAAI;QAAC;AACjD,mBAAU,GAAA,aAAA,aAAY,OAAO,OAAO,CAAA,GAAI,QAAA,YAAY,GAAG,OAAO;AAC9D,gBAAQ,OAAO,OAAO,OAAO,SAAS,QAAA,eAAe;AACrD,gBAAQ,aAAa,KAAK,UAAU,MAAK;AACzC,gBAAQ,YAAY,KAAK,SAAS,QAAQ,SAAA,KAAG;AAAI,iBAAA,IAAI,MAAK,MAAT,KAAG,cAAA,CAAA,GAAA,OAAU,QAAQ,QAAQ,GAAA,KAAA,CAAA;QAA7B,CAA8B;AAC/E,gBAAQ,cAAa,KAAA,KAAK,WAAU,MAAK,MAAA,IAAA,cAAA,CAAA,GAAA,OAAI,QAAQ,SAAS,GAAA,KAAA,CAAA;AAC9D,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,QAAP,WAAA;AACE,aAAK,MAAK;AACV,aAAK,KAAK,MAAK;AACf,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,SAAP,SAAc,MAAuB;AACnC,aAAK,KAAK,MAAM,IAAI;AACpB,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,uBAAP,SAA4B,YAA4B;;AACtD,YAAM,QAAQ,KAAK,mBAAmB,UAAU;AAChD,SAAA,KAAA,KAAK,MAAK,OAAM,MAAA,IAAA,cAAA,CAAA,GAAA,OAAI,KAAK,GAAA,KAAA,CAAA;AACzB,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,qBAAP,SAA0B,UAA0B;;AAClD,YAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,qBAAW,CAAC,QAAQ;;AAEtB,YAAM,UAAU,KAAK;AACrB,YAAM,QAAQ,CAAA;AACd,YAAM,aAAa,QAAQ,YAAY,UAAU,KAAK,QAAQ;;AAC9D,gBACA,UAAmB,KAAA,SAAA,KAAK,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzB,gBAAM,OAAI,GAAA;;AACb,uBAAwB,gBAAA,OAAA,QAAA,SAAA,UAAU,IAAA,iBAAA,aAAA,KAAA,GAAA,CAAA,eAAA,MAAA,iBAAA,aAAA,KAAA,GAAE;AAA/B,oBAAM,YAAS,eAAA;AAClB,oBAAI,KAAK,MAAM,QAAQ,QAAQ,SAAS,WAAW,KAAK,MAAM,IAAI,GAAG;AACnE,wBAAM,KAAK,IAAI;AACf,2BAAS;;;;;;;;;;;;;;;;;;;;;;AAIf,eAAO;MACT;AAlac,MAAAA,sBAAA,OAAe;AAKf,MAAAA,sBAAA,UAAsB;QAClC,WAAW;QACX,UAAU;QACV,YAAY;QACZ,UAAU;QACV,UAAU;QACV,cAAc,SAAC,KAA0C,MAA+B,KAAU;AAChG,cAAI,aAAa,MAAM,GAAG;QAC5B;QACA,cAAc,SAAC,KAA0C,MAA+B,KAAU;AAChG,cAAI,aAAa,MAAM,GAAG;QAC5B;QACA,gBAAe,GAAA,aAAA,YAAW;UACxB,MAAS,CAAC,cAAA,MAAM,UAAU,YAAY,IAAI,KAAK;UAC/C,SAAS,CAAC,cAAA,MAAM,QAAQ;UACxB,SAAS,CAAC,cAAA,MAAM,SAAS,cAAc,IAAI,KAAK;UAChD,SAAS,CAAC,cAAA,MAAM,OAAO;UACvB,QAAS,CAAC,cAAA,MAAM,UAAU,kBAAkB,KAAK;SAClD;;AAMW,MAAAA,sBAAA,eAAc,GAAA,cAAA,eAAc,YAAY,WAAW,cAAc,WAAW,gBAAgB;AAuY5G,aAAAA;MAzaA;AAAsB,YAAA,uBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9gBtB,QAAA,oBAAA;AA6DA,QAAA,sBAAA,SAAA,QAAA;AAA2C,gBAAAE,sBAAA,MAAA;AAA3C,eAAAA,uBAAA;;MAA0E;AAAA,aAAAA;IAAA,EAA/B,kBAAA,oBAAoB;AAU/D,QAAA,kBAAA,WAAA;AA4BE,eAAAC,iBAAY,SAA8B,UAAoB;AAApB,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAoB;AAPvD,aAAA,gBAAwE;AAQ7E,aAAK,UAAU;AACf,aAAK,WAAW;MAClB;AAKA,aAAA,eAAWA,iBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAQ,KAAK,YAAuC;QACtD;;;;AAKO,MAAAA,iBAAA,UAAA,kBAAP,SAAuB,WAAc;AACnC,eAAO;MACT;AAKO,MAAAA,iBAAA,UAAA,SAAP,SAAc,UAAe,SAAmB;AAC9C,eAAO,IAAI,KAAK,cAAc,UAAU,KAAK,SAAS,OAAO;MAC/D;AA/Cc,MAAAA,iBAAA,OAAe;AAiD/B,aAAAA;MAtDA;AAAsB,YAAA,kBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvEtB,QAAA,gBAAA;AAYA,QAAA,eAAA,SAAA,QAAA;AAA2C,gBAAAC,eAAA,MAAA;AAYzC,eAAAA,cAAY,MAAc,KAAwB,SACtC,OACA,KAAmD;AAFb,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAuB;AAC7D,YAAA,UAAA,QAAA;AAAA,kBAAA,EAAyB,MAAM,MAAM,GAAG,GAAG,OAAO,GAAE;QAAC;AACrD,YAAA,QAAA,QAAA;AAAA,gBAAA,EAAuB,MAAM,MAAM,GAAG,GAAG,OAAO,GAAE;QAAC;eAC7D,OAAA,KAAA,MAAM,MAAM,KAAK,SAAS,OAAO,GAAG,KAAC;MACvC;AAXA,aAAA,eAAIA,cAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO,KAAK,SAAS;QACvB;;;;AAyBO,MAAAA,cAAA,UAAA,iBAAP,SAAsB,OAA4B;AAChD,YAAI,KAAK,MAAK,IAAK,cAAA,MAAM,UAAU;AACjC,cAAI,KAAK,SAAS,gBAAgB;AAChC,gBAAI,OAAO,KAAK,MAAM;AACtB,gBAAI,SAAS,KAAK,IAAI,MAAM;AAC1B,kBAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,MAAM,KAAK,IAAI,IAAI,EAAE,QAAQ;AACvE,qBAAK,QAAQ,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;;AAE9C,kBAAI,KAAK,MAAM,GAAG;AAChB,uBAAO,KAAK,QAAQ,MAAM,KAAK,MAAM,MAAW,KAAK,MAAM,CAAC;;AAE9D,mBAAK,QAAQ,QAAQ,KAAK,aAAa,IAAI;mBACtC;AACL,kBAAI,KAAK,MAAM,GAAG;AAChB,uBAAO,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,CAAC;;AAE9C,qBAAO,SAAS,KAAK,IAAI,MAAM;AAC7B,oBAAI,OAAO,KAAK,QAAQ,KAAK,IAAI;AACjC,qBAAK,QAAQ,OAAO,IAAI;AACxB,uBAAO;;AAET,mBAAK,QAAQ,OAAO,KAAK,aAAa,IAAI;AAC1C,kBAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ;AAChD,qBAAK,QAAQ,MAAM,MAAM,KAAK,IAAI,CAAC;;AAErC,mBAAK,QAAQ,OAAO,IAAI;;iBAErB;AACL,iBAAK,QAAQ,QAAQ,KAAK,aAAa,KAAK,MAAM,IAAI;;AAExD,eAAK,MAAM,OAAO,KAAK,IAAI,OAAO,KAAK;AACvC,eAAK,MAAM,IAAI,KAAK,IAAI,IAAI;AAC5B,eAAK,MAAM,cAAA,MAAM,QAAQ;;MAE7B;AAOO,MAAAA,cAAA,UAAA,mBAAP,SAAwB,UAA+B;AACrD,iBAAS,cAAa;MACxB;AAQO,MAAAA,cAAA,UAAA,qBAAP,SAA0B,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAChD,YAAI,KAAK,MAAK,KAAM,cAAA,MAAM,SAAS;AACjC,cAAM,UAAU,KAAK;AACrB,cAAI,OAAO,KAAK,MAAM;AACtB,cAAI,OAAc,QAAQ,KAAK,EAAE;AACjC,cAAI,SAAS;AACX,gBAAI,OAAO,KAAK,MAAM,QAAQ,KAAK,OAAO,KAAK,IAAI;AACnD,gBAAI,KAAK,SAAS,gBAAgB;AAChC,qBAAO,QAAQ,KAAK,IAAI;mBACnB;AACL,kBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW;AAC3C,qBAAO,QAAQ,WAAW,QAAQ,KAAK,GAAG,CAAC;;;AAG/C,cAAI,QAAQ,OAAO,IAAI,GAAG;AACxB,oBAAQ,QAAQ,MAAM,IAAI;;AAE5B,eAAK,MAAM,OAAO,KAAK,IAAI,OAAO;AAClC,eAAK,MAAM,IAAI,KAAK,IAAI,IAAI;;MAEhC;AAEF,aAAAA;IAAA,EAzG2C,cAAA,gBAAgB;AAA9C,YAAA,eAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZb,QAAA,gBAAA;AAUA,QAAA,eAAA,SAAA,QAAA;AAA2C,gBAAAC,eAAA,MAAA;AAA3C,eAAAA,gBAAA;;MACA;AAAA,aAAAA;IAAA,EAD2C,cAAA,gBAAgB;AAA9C,YAAA,eAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVb,QAAA,eAAA;AAqBA,QAAA,iBAAA,WAAA;AA+EE,eAAAC,gBAAY,SAA0B;AAA1B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAA0B;AACpC,YAAI,QAAQ,KAAK;AACjB,aAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,OAAO;AACrE,aAAK,KAAI;AACT,aAAK,YAAW;MAClB;AAKU,MAAAA,gBAAA,UAAA,OAAV,WAAA;AACE,aAAK,UAAU,CAAA;AACf,aAAK,SAAS;AACd,aAAK,SAAS,CAAA;AACd,aAAK,QAAQ,CAAA;AACb,aAAK,QAAQ,CAAA;MACf;AAKU,MAAAA,gBAAA,UAAA,cAAV,WAAA;AACE,YAAI,QAAO,GAAA,aAAA,WAAU,KAAK,QAAQ,cAAc,CAAC;AACjD,YAAI,UAAS,GAAA,aAAA,WAAU,KAAK,QAAQ,iBAAiB,CAAC;AACtD,YAAI,WAAU,GAAA,aAAA,WAAU,KAAK,QAAQ,kBAAkB,CAAC;AACxD,aAAK,eAAe,IAAI,OAAO,SAAS,KAAK,KAAK,GAAG,IAAI,MAAM,GAAG;AAClE,aAAK,kBAAkB,IAAI,OAAO,eAAe,OAAO,KAAK,GAAG,IAAI,UAAU;AAC9E,aAAK,mBAAmB,IAAI,OAAO,eAAe,UAAU,UAAU;MACxE;AAKU,MAAAA,gBAAA,UAAA,aAAV,WAAA;AACE,YAAI,KAAK,OAAO,MAAM,IAAI,GAAG;AAC3B,eAAK,QAAQ,KAAK,KAAK,MAAM;AAC7B,eAAK,MAAM,KAAK,KAAK,MAAM;;AAE7B,aAAK,SAAS;AACd,aAAK,SAAS,CAAA;MAChB;AAWU,MAAAA,gBAAA,UAAA,eAAV,SAAuB,MAAa,MAAY;AAC9C,aAAK,OAAO,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;AACpC,aAAK,UAAU;MACjB;AASU,MAAAA,gBAAA,UAAA,aAAV,SAAqB,MAAS,QAAe;AAC3C,YAAI,CAAC,QAAQ;AACX,eAAK,aAAa,MAAM,KAAK,QAAQ,MAAM,IAAI,CAAC;;AAElD,eAAO,KAAK,QAAQ,KAAK,IAAI;MAC/B;AASU,MAAAA,gBAAA,UAAA,YAAV,SAAoB,MAAS,QAAe;AAC1C,YAAI,CAAC,QAAQ;AACX,cAAI,OAAO,KAAK,QAAQ,iBAAiB,EAAE,KAAK,QAAQ,KAAK,IAAI,CAAC;AAClE,eAAK,aAAa,MAAM,IAAI;;AAE9B,eAAO,KAAK,QAAQ,KAAK,IAAI;MAC/B;AAkBU,MAAAA,gBAAA,UAAA,kBAAV,SAA0B,MAAS,QAAe;AAChD,aAAK,WAAU;AACf,YAAM,QAAQ,KAAK,QAAQ,aAAa,MAAM,OAAO,KAAK;AAC1D,YAAM,QAAQ,KAAK,QAAQ,KAAK,IAAI,KAAK;AACzC,YAAM,UAAU,KAAK,iBAAiB,KAAK,KAAK;AAChD,YAAI,OAAO;AACX,YAAI,KAAK,QAAQ,WAAW,IAAI,KAAK,CAAC,KAAK,QAAQ,aAAa,MAAM,UAAU,MAC3E,WAAW,CAAC,KAAK,aAAa,KAAK,KAAK,IAAI;AAC/C,cAAI,KAAK,QAAQ,KAAK,IAAI,GAAG;AAC3B,iBAAK,MAAM,KAAK,CAAC,KAAK,QAAQ,KAAK,IAAI,GAAG,MAAM,CAAC;;AAEnD,iBAAO,KAAK,QAAQ,WAAW,IAAI;AACnC,oBAAU,UAAU,KAAK,gBAAgB,KAAK,KAAK,MAAM,CAAC;eACrD;AACL,iBAAO,KAAK,QAAQ,KAAK,IAAI;;AAE/B,eAAO,CAAC,MAAM,MAAM;MACtB;AASU,MAAAA,gBAAA,UAAA,cAAV,SAAsB,MAAS,SAAgB;AAC7C,aAAK,WAAU;AACf,eAAO,KAAK,QAAQ,KAAK,IAAI;MAC/B;AAoBO,MAAAA,gBAAA,UAAA,OAAP,SAAY,MAAW;;AACrB,aAAK,KAAI;AACT,YAAI,OAAO,KAAK,QAAQ,KAAK,IAAI;AACjC,YAAI,SAAS;AACb,YAAI,UAAU,KAAK,QAAQ,iBAAiB;AAE5C,eAAO,QAAQ,SAAS,MAAM;AAC5B,cAAM,OAAO,KAAK,QAAQ,KAAK,IAAI;AACnC,cAAI,SAAS,SAAS;AACpB,mBAAO,KAAK,WAAW,MAAW,MAAM;qBAC/B,QAAQ,eAAe,IAAI,GAAG;AACvC,mBAAO,KAAK,UAAU,MAAW,MAAM;qBAC9B,MAAM;AACf,iBAAA,OAAiB,KAAK,gBAAgB,MAAW,MAAM,GAAC,CAAA,GAAvD,OAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;iBACR;AACL,mBAAO,KAAK,YAAY,MAAW,MAAM;;AAE3C,cAAI,CAAC,QAAQ,KAAK,MAAM,QAAQ;AAC9B,iBAAK,WAAU;AACf,iBAAA,OAAiB,KAAK,MAAM,IAAG,GAAE,CAAA,GAAhC,OAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;;;AAIjB,aAAK,WAAU;AACf,YAAI,SAAS,CAAC,KAAK,SAAS,KAAK,KAAK;AACtC,aAAK,KAAI;AACT,eAAO;MACT;AA3Pc,MAAAA,gBAAA,UAAsB;QAClC,cAAc,CAAC,UAAU,YAAY,SAAS,YAAY,OAAO,QAAQ,cAAc,gBAAgB;QAIvG,iBAAiB,EAAC,IAAI,MAAM,KAAK,IAAI,YAAY,GAAE;QAInD,iBAAiB;QAKjB,kBAAkB;;AA+OtB,aAAAA;MAlQA;AAAa,YAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBb,QAAA,oBAAA;AACA,QAAA,eAAA;AACA,QAAA,oBAAA;AACA,QAAA,oBAAA;AACA,QAAA,sBAAA;AAGA,QAAA,gBAAA;AAwBA,QAAA,eAAA,SAAA,QAAA;AAA2C,gBAAAC,eAAA,MAAA;AAoCzC,eAAAA,cAAY,UAAe,SAA8B,SAAmB;AAA5E,YAAA,QAAA;AACM,YAAA,KAAA,QAAc,GAAA,aAAA,iBAAgB,SAAS,oBAAA,eAAe,OAAO,GAAC,CAAA,GAA7D,OAAI,GAAA,CAAA,GAAE,MAAG,GAAA,CAAA;gBACd,OAAA,KAAA,MAAM,UAAU,SAAS,IAAI,KAAC;AAC9B,cAAK,aAAa,MAAK,QAAQ,YAAY,KAAK,IAAI,oBAAA,eAAwB,GAAG;AAC/E,cAAK,WAAW,UAAU;AAC1B,cAAK,SAAS,CAAA;;MAChB;AAaU,MAAAA,cAAA,UAAA,eAAV,SAAuB,GAAW,OAAe,OAAe,OAA0B;;AACxF,YAAM,UAAU,KAAK;;AACrB,mBAAmB,KAAA,SAAA,MAAM,CAAC,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAxB,gBAAM,OAAI,GAAA;AACT,gBAAA,KAAA,OAAY,MAAI,CAAA,GAAf,OAAI,GAAA,CAAA,GAAE,IAAC,GAAA,CAAA;AACZ,gBAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,MAAM,SAAS;AAChD,qBAAO,EAAC,MAAY,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,MAAY;;AAEzD,qBAAS;;;;;;;;;;;AAEX,eAAO,EAAC,MAAM,MAAM,GAAG,GAAG,MAAY;MACxC;AAWU,MAAAA,cAAA,UAAA,WAAV,SAAmB,MAAuB,KACvB,OAA0B;AACxB,YAAI,OAAO,KAAK;AAChB,YAAI,QAAQ,KAAK,aAAa,KAAK,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK;AACpE,YAAI,MAAM,KAAK,aAAa,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK;AACjE,eAAO,IAAI,KAAK,QAAQ,SAAS,MAAM,KAAK,KAAK,SAAS,OAAO,GAAG;MACtE;AAmBZ,MAAAA,cAAA,UAAA,WAAP,SAAgB,SAAmB;;AACjC,YAAI,CAAC,KAAK,UAAU,MAAM,UAAU,GAAG;AACrC,eAAK,QAAQ,WAAW,KAAK;AAC7B,qBAAU,GAAA,aAAA,aAAY,EAAC,UAAU,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC,EAAC,GAAG,OAAO;;AACtG,qBAAwB,KAAA,SAAA,KAAK,QAAQ,YAAY,QAAQ,UAAU,GAAG,KAAK,QAAQ,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAjF,kBAAM,YAAS,GAAA;AACd,kBAAA,KAAA,OAAmB,CAAC,MAAM,IAAI,GAAoC,CAAA,GAAjE,UAAO,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;;AACnB,yBAAkB,MAAA,MAAA,QAAA,SAAA,KAAK,QAAQ,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA5B,sBAAM,MAAG,GAAA;AACZ,sBAAI,OAAO,IAAK,KAAK,QAAQ,UAAU,EAAE;AACzC,sBAAI,IAAI,gBAAgB;AACtB,wBAAI,YAAY,MAAM;AACpB,2BAAA,OAAmB,KAAK,WAAW,KAAK,SAAS,GAAC,CAAA,GAAjD,UAAO,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;;;AAEjB,+BAAmB,MAAA,MAAA,QAAA,SAAA,IAAI,SAAS,OAAO,CAAC,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAArC,4BAAM,OAAI,GAAA;AACb,6BAAK,KAAK,KAAK,SAAS,MAAM,KAAK,KAAK,CAAC;;;;;;;;;;;yBAEtC;;AACL,+BAAmB,MAAA,MAAA,QAAA,SAAA,IAAI,SAAS,SAAS,CAAC,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,4BAAM,OAAI,GAAA;AACb,4BAAI,OACF,IAAI,KAAK,QAAQ,SAAS,KAAK,MAAM,KAAK,KAAK,SAAS,KAAK,OAAO,KAAK,GAAG;AAC9E,6BAAK,KAAK,IAAI;;;;;;;;;;;;AAGlB,uBAAK,KAAK,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;AAGxB,eAAK,UAAU,IAAI,UAAU;;AAE/B,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,iBAAP,WAAA;AACE,YAAI,CAAC,KAAK,UAAU,MAAM,gBAAgB,GAAG;AAC3C,eAAK,gBAAe;AACpB,eAAK,cAAa;AAClB,iBAAA,UAAM,eAAc,KAAA,IAAA;AACpB,eAAK,UAAU,IAAI,gBAAgB;;AAErC,eAAO;MACT;AAKU,MAAAA,cAAA,UAAA,kBAAV,WAAA;AACE,YAAM,OAAO,KAAK,QAAQ,KAAK,KAAK,QAAQ;AAC5C,YAAM,OAAO,KAAK,qBAAoB;AACtC,YAAI,MAAM;AACR,eAAK,QAAQ,OAAO,MAAM,IAAI;;MAElC;AAKO,MAAAA,cAAA,UAAA,gBAAP,WAAA;AACE,YAAM,QAAQ,KAAK,mBAAkB;AACrC,YAAM,UAAU,KAAK;AACrB,YAAI,SAAS,CAAC,QAAQ,OAAO,KAAK,GAAG;AACnC,cAAM,OAAO,QAAQ,KAAK,KAAK,QAAQ;AACvC,cAAI,SAAS,KAAK,UAAU,MAAM,QAAQ,aAAa,OAAO,IAAI,CAAC;AACnE,cAAI,QAAQ;AACV,oBAAQ,QAAQ,OAAO,MAAM;iBACxB;AACL,oBAAQ,OAAO,MAAM,KAAK;;;MAGhC;AAOU,MAAAA,cAAA,UAAA,YAAV,SAAoB,MAAS,IAAU;;AACrC,YAAI,IAAI;;AACN,qBAAoB,KAAA,SAAA,KAAK,QAAQ,KAAK,MAAM,OAAO,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAjD,kBAAM,QAAK,GAAA;AACd,kBAAI,KAAK,QAAQ,aAAa,OAAO,IAAI,MAAM,IAAI;AACjD,uBAAO;;;;;;;;;;;;;AAIb,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,qBAAP,SAA0B,SAAwB;;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAChD,YAAI,KAAK,UAAU,MAAM,gBAAgB,GAAG;;AAC1C,qBAAmB,KAAA,SAAA,KAAK,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzB,kBAAM,OAAI,GAAA;AACb,kBAAI,KAAK,MAAK,KAAM,cAAA,MAAM,UAAU;AAClC,qBAAK,MAAM,cAAA,MAAM,SAAS,OAAO;;;;;;;;;;;;;AAIvC,aAAK,UAAU,MAAM,gBAAgB;AACrC,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,qBAAP,WAAA;AACE,eAAO,KAAK,UAAU,WAAW,IAAI;MACvC;AAKO,MAAAA,cAAA,UAAA,uBAAP,WAAA;AACE,eAAO,KAAK,UAAU,aAAa,IAAI;MACzC;AAOO,MAAAA,cAAA,UAAA,YAAP,SAAiB,QAAiB;AAChC,aAAK,OAAO,KAAK,MAAM;MACzB;AAKO,MAAAA,cAAA,UAAA,YAAP,WAAA;AACE,eAAO,KAAK;MACd;AAlOc,MAAAA,cAAA,OAAe;AAKf,MAAAA,cAAA,UAAO,SAAA,SAAA,CAAA,GAChB,kBAAA,qBAAqB,OAAO,GAAA,EAC/B,gBAAe,GAAA,aAAA,YAAU,SAAA,SAAA,CAAA,GACpB,kBAAA,qBAAqB,QAAQ,aAAa,GAAA,EAC7C,QAAQ,CAAC,cAAA,MAAM,WAAW,GAAG,IAAI,oBAAoB,KAAK,EAAC,CAAA,CAAA,GAE7D,UAAU,kBAAA,cACV,UAAU,kBAAA,cACV,YAAY,KAAI,CAAA;AAuNpB,aAAAA;MAzO2C,kBAAA,oBAAoB;AAAlD,YAAA,eAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Bb,QAAA,eAAA;AAEA,QAAA,oBAAA;AAWA,QAAA,cAAA,SAAA,QAAA;AAA0C,gBAAAC,cAAA,MAAA;AAA1C,eAAAA,eAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAUS,cAAA,gBAAgB,kBAAA;;MAuCzB;AAlCS,MAAAA,aAAA,UAAA,kBAAP,SAAuB,UAAa;AAClC,YAAM,UAAU,KAAK;AACrB,YAAI,OAAO,aAAc,UAAU;AACjC,cAAI;AACF,uBAAW,QAAQ,MAAM,UAAU,WAAW;mBACvC,KAAK;UAAA;;AAEhB,YAAI,oBAAoB,QAAQ,OAAO,YACnC,oBAAoB,QAAQ,OAAO,eACnC,oBAAoB,QAAQ,OAAO,kBAAkB;AACvD,iBAAO;;AAET,eAAO;MACT;AAQO,MAAAA,aAAA,UAAA,SAAP,SAAc,UAAe,SAAmB;AAC9C,YAAM,UAAU,KAAK;AACrB,YAAI,OAAO,aAAc,UAAU;AACjC,qBAAW,QAAQ,MAAM,UAAU,WAAW;mBACrC,oBAAoB,QAAQ,OAAO,eACnC,oBAAoB,QAAQ,OAAO,kBAAkB;AAC9D,cAAI,QAAQ;AACZ,qBAAW,QAAQ,MAAM,IAAI,WAAW;AACxC,kBAAQ,OAAO,QAAQ,KAAK,QAAQ,GAAG,KAAK;;AAE9C,eAAO,OAAA,UAAM,OAAM,KAAA,MAAC,UAAU,OAAO;MACvC;AAEF,aAAAA;IAAA,EAjD0C,aAAA,eAAe;AAA5C,YAAA,cAAA;;;;;;;;;ACbb,QAAA,eAAA;AACA,QAAA,mBAAA;AAaA,aAAgB,oBAA6B,SAA4B;AACvE,UAAM,UAAU,IAAI,iBAAA,YAAqB,OAAO;AAChD,mBAAA,QAAQ,SAAS,SAAS,OAAO;AACjC,aAAO;IACT;AAJA,YAAA,sBAAA;;;", "names": ["ListItem", "LinkedList", "AbstractMathList", "AbstractNodeFactory", "MmlMstyle", "MmlMaligngroup", "MmlMalignmark", "MathChoice", "MmlFactory", "BitField", "Bits", "RenderList", "DefaultInputJax", "DefaultOutputJax", "DefaultMathList", "DefaultMathItem", "AbstractMathDocument", "jax", "DefaultMathDocument", "AbstractHandler", "HTMLMathItem", "HTMLMathList", "HTMLDomStrings", "HTMLDocument", "HTMLHandler"]}