<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'

// 异步导入ChatArea组件
const ChatArea = defineAsyncComponent(() => import('@/components/ChatArea.vue'))

const props = defineProps<{
  id: string | number
}>()

const router = useRouter()
const title = ref('')

// 根据ID设置标题
onMounted(() => {
  // 根据ID获取对应的标题
  const featureMap: Record<string, string> = {
    '1': '数理辅导',
    '2': '旅游定制',
    '3': '电影推荐',
    '4': '短视频推荐',
    '5': '音乐推荐',
    '6': '创漫空间',
    '7': '音乐定制',
    '8': '新闻推荐'
  }
  
  title.value = featureMap[props.id.toString()] || 'AI助手'
})

// 返回首页
const goBack = () => {
  router.push('/')
}
</script>

<template>
  <div class="chat-page">
    <div class="chat-header">
      <button class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <h1 class="chat-title">{{ title }}</h1>
    </div>
    
    <div class="chat-container">
      <div class="chat-area-wrapper">
        <ChatArea :sidebar-collapsed="true" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.back-button {
  background: none;
  border: none;
  color: #4b5563;
  cursor: pointer;
  padding: 8px;
  margin-right: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.chat-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chat-area-wrapper {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style> 