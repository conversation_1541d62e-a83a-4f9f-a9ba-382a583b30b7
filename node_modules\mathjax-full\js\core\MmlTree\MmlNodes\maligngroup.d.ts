import { PropertyList } from '../../Tree/Node.js';
import { AbstractMmlLayoutNode, AttributeList } from '../MmlNode.js';
export declare class MmlMaligngroup extends AbstractMmlLayoutNode {
    static defaults: PropertyList;
    get kind(): string;
    get isSpacelike(): boolean;
    protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean): void;
}
