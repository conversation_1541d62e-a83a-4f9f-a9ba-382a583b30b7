{"version": 3, "file": "scriptbase.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/scriptbase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,4CAA6D;AAC7D,qEAA0E;AAc1E;IACA,mCAAiG;IADjG;;IA4EA,CAAC;IA9DQ,iCAAO,GAAd,UAAe,MAAS;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtC,IAAA,KAAA,OAAS,IAAI,CAAC,SAAS,EAAE,IAAA,EAAxB,CAAC,QAAA,EAAE,CAAC,QAAoB,CAAC;QAChC,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,IAAM,KAAK,GAAc,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC;QACxD,IAAI,EAAE,EAAE;YACN,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAC,KAAK,OAAA,EAAC,CAAC,CAAM,CAAC,CAAC;IACnG,CAAC;IAMS,mCAAS,GAAnB,UAAoB,KAAU,EAAE,EAAY;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;gBACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChE;SACF;IACH,CAAC;IAMS,yCAAe,GAAzB,UAA0B,IAAO,EAAE,OAAa;QAC9C,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC;YAAE,OAAO;QAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACnF,CAAC;IAMS,0CAAgB,GAA1B,UAA2B,KAAQ,EAAE,QAAc;;QACjD,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC;YAAE,OAAO;QAC5B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAC,KAAK,EAAE,EAAC,eAAe,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAC,EAAC,CAAC,CAAC;;YACrF,KAAoB,IAAA,KAAA,SAAA,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAM,CAAQ,CAAA,gBAAA,4BAAE;gBAA1E,IAAM,KAAK,WAAA;gBACd,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAC5B;;;;;;;;;QACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAM,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IAMS,0CAAgB,GAA1B,UAA2B,IAAO,EAAE,OAAa;QAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACtC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;YACvD,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;aAClB;SACF;IACH,CAAC;IApEa,oBAAI,GAAG,YAAY,CAAC;IAsEpC,sBAAC;CAAA,AA5ED,CACA,IAAA,qCAAqB,EAA+D,yBAAY,CAAC,GA2EhG;AA5EY,0CAAe"}