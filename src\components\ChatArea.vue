<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import { chatApi, sessionManager, musicApi, travelApi } from '@/api'
import type { Message } from '@/api/types'
import VueMarkdownRenderer from '@/components/VueMarkdownRenderer.vue'

interface Props {
  sidebarCollapsed: boolean
  featureId?: number
}

const props = defineProps<Props>()

// 聊天状态
const messages = ref<Message[]>([])
const inputText = ref('')
const isLoading = ref(false)
const chatContainer = ref<HTMLElement>()
const selectedFile = ref<File | null>(null)
const previewUrl = ref<string | null>(null)
const uploadType = ref<'image' | 'video' | null>(null)
const uploadError = ref<string | null>(null)
const sessionId = ref<string>('')
const currentAssistantMessage = ref<Message | null>(null)
const streamedContent = ref<string>('') // 新增：用于存储完整的流式响应内容
const isThinking = ref(false) // 新增：是否处于"思考中"状态
const thinkingText = ref('正在思考中...') // 新增：思考中的文本
const thinkingInterval = ref<number | null>(null) // 新增：思考动画的定时器ID

// 滚动到底部
const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 处理转义序列，将\n等转换为实际字符
const processEscapeSequences = (text: string): string => {
  return text
    .replace(/\\n/g, '\n')  // 换行符
    .replace(/\\t/g, '\t')  // 制表符
    .replace(/\\r/g, '\r')  // 回车符
    .replace(/\\"/g, '"')   // 双引号
    .replace(/\\'/g, "'")   // 单引号
    .replace(/\\\\/g, '\\') // 反斜杠
}

// 添加对旅行API登录状态的检查函数
const checkTravelApiLogin = async () => {
  if (props.featureId === 2) {
    // 检查旅行API是否已登录
    if (!travelApi.isLoggedIn()) {
      console.log('旅行API未登录，尝试自动登录')

      // 自动登录（使用默认用户ID）
      try {
        // 使用默认用户ID
        const defaultUserId = 'nameppp'

        const result = await travelApi.login({
          user_id: defaultUserId
        })

        if (result.success) {
          // 登录成功
          console.log('旅行API自动登录成功')

          // 添加一条系统消息，告知用户登录成功
          messages.value.push({
            id: Date.now(),
            type: 'assistant',
            content: '旅行定制服务已准备就绪，您现在可以使用旅行定制功能。',
            timestamp: new Date(),
            mediaType: 'text'
          })

          nextTick(() => scrollToBottom())
        } else {
          // 登录失败
          console.error('旅行API自动登录失败:', result.message)

          // 添加一条系统消息，告知用户登录失败
          messages.value.push({
            id: Date.now(),
            type: 'assistant',
            content: '旅行定制服务暂时不可用，请稍后再试。',
            timestamp: new Date(),
            mediaType: 'text'
          })
        }
      } catch (error) {
        console.error('旅行API自动登录出错:', error)

        // 添加一条系统消息，告知用户登录出错
        messages.value.push({
          id: Date.now(),
          type: 'assistant',
          content: '旅行定制服务连接失败，请稍后再试。',
          timestamp: new Date(),
          mediaType: 'text'
        })
      }
    } else {
      console.log('旅行API已登录，可以使用旅行定制功能')
      // 添加一条系统消息，告知用户可以使用旅行定制功能
      messages.value.push({
        id: Date.now(),
        type: 'assistant',
        content: '欢迎使用旅行定制功能，请告诉我您的旅行偏好和目的地。',
        timestamp: new Date(),
        mediaType: 'text'
      })

      nextTick(() => scrollToBottom())
    }
  }
}

// 添加对音乐API登录状态的检查函数
const checkMusicApiLogin = async () => {
  if (props.featureId === 5) {
    // 音乐API不再需要登录，直接显示欢迎消息
    console.log('音乐API可以使用音乐推荐功能')
    // 添加一条系统消息，告知用户可以使用音乐推荐功能
    messages.value.push({
      id: Date.now(),
      type: 'assistant',
      content: '欢迎使用音乐推荐功能，请输入您想听的音乐类型、心情或场景。',
      timestamp: new Date(),
      mediaType: 'text'
    })

    nextTick(() => scrollToBottom())
  }
}

// 初始化会话ID
onMounted(async () => {
  sessionId.value = sessionManager.getSessionId()

  // 检查是否为音乐推荐功能(featureId为5)
  if (props.featureId === 5) {
    // 调用检查音乐API登录状态的函数
    await checkMusicApiLogin()
  }

  // 检查是否为旅行定制功能(featureId为2)
  if (props.featureId === 2) {
    // 调用检查旅行API登录状态的函数
    await checkTravelApiLogin()
  }
})

// 启动思考动画
const startThinking = () => {
  isThinking.value = true
  let dots = 0

  // 清除可能存在的旧定时器
  if (thinkingInterval.value) {
    clearInterval(thinkingInterval.value)
    thinkingInterval.value = null
  }

  // 设置初始的思考文本
  thinkingText.value = '正在思考中'

  // 启动新的定时器，实现省略号动画效果
  thinkingInterval.value = window.setInterval(() => {
    dots = (dots % 3) + 1
    thinkingText.value = '正在思考中' + '.'.repeat(dots)

    if (currentAssistantMessage.value) {
      currentAssistantMessage.value.content = thinkingText.value
      nextTick(() => scrollToBottom())
    }
  }, 500) // 每500毫秒更新一次
}

// 停止思考动画
const stopThinking = () => {
  isThinking.value = false
  if (thinkingInterval.value) {
    clearInterval(thinkingInterval.value)
    thinkingInterval.value = null
  }

  // 清空思考文本
  if (currentAssistantMessage.value && currentAssistantMessage.value.content.startsWith('正在思考中')) {
    currentAssistantMessage.value.content = ''
  }
}

// 处理文件上传
const handleFileUpload = async (event: Event, type: 'image' | 'video') => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    const file = input.files[0]

    // 检查文件类型
    if (type === 'image' && !file.type.match('image/(jpeg|jpg|png)')) {
      alert('请上传jpg或png格式的图片')
      return
    }

    if (type === 'video' && !file.type.match('video/*')) {
      alert('请上传有效的视频文件')
      return
    }

    // 检查文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('文件大小不能超过10MB')
      return
    }

    selectedFile.value = file
    uploadType.value = type
    uploadError.value = null

    // 创建预览URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value)
    }

    try {
      // 上传文件到服务器
      // 注意：这里我们使用本地预览，实际项目中应该等待上传完成
      previewUrl.value = URL.createObjectURL(file)

      // 异步上传文件
      // 在实际项目中，可以等待上传完成后再允许发送消息
      /* 
      const uploadResult = await chatApi.uploadFile(file)
      previewUrl.value = uploadResult.url
      */
    } catch (error) {
      console.error('文件上传预览失败:', error)
      uploadError.value = '文件预览失败，请重试'
      clearSelectedFile()
    }
  }
}

// 清除选择的文件
const clearSelectedFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  selectedFile.value = null
  previewUrl.value = null
  uploadType.value = null
  uploadError.value = null
}

// 处理流式响应
const handleStreamChunk = (chunk: string) => {
  console.log(chunk)
  // 过滤掉结束标记和状态消息
  if (
    chunk.includes('[DONE]') ||
    chunk.includes('"status": "completed"')
  ) {
    return
  }

  // 处理转义序列，如\n等
  const processedChunk = processEscapeSequences(chunk)

  // 如果是第一个有效的数据块，停止思考动画
  if (isThinking.value && processedChunk.trim()) {
    stopThinking()
  }

  if (currentAssistantMessage.value) {
    // 更新现有消息内容
    currentAssistantMessage.value.content += processedChunk
    // 累积流式数据
    streamedContent.value += processedChunk
    // 添加调试日志来查看接收到的内容
    console.log('接收到流式数据块:', JSON.stringify(processedChunk))
    nextTick(() => scrollToBottom())
  } else {
    console.warn('currentAssistantMessage 为空，无法更新内容:', JSON.stringify(processedChunk))
  }
}

// 发送消息
const sendMessage = async () => {
  if ((!inputText.value.trim() && !selectedFile.value) || isLoading.value) return

  const userMessage: Message = {
    id: Date.now(),
    type: 'user',
    content: inputText.value.trim(),
    timestamp: new Date(),
    mediaType: uploadType.value || 'text',
    mediaUrl: previewUrl.value || undefined
  }

  messages.value.push(userMessage)
  const currentInput = inputText.value
  inputText.value = ''
  isLoading.value = true

  // 保存文件引用
  const file = selectedFile.value

  // 清除已上传的文件
  clearSelectedFile()

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 检查是否为音乐推荐功能(featureId为5)
    if (props.featureId === 5) {
      // 调用音乐推荐处理函数
      const handled = await handleMusicRecommendation(currentInput)
      if (handled) return // 如果已经处理了，就不再继续执行
    }

    // 检查是否为旅行定制功能(featureId为2)
    if (props.featureId === 2) {
      // 调用旅行问题处理函数
      const handled = await handleTravelQuestion(currentInput)
      if (handled) return // 如果已经处理了，就不再继续执行
    }

    // 创建助手消息占位符
    currentAssistantMessage.value = {
      id: Date.now() + 1,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      mediaType: 'text'
    }

    // 添加到消息列表
    messages.value.push(currentAssistantMessage.value)

    // 重置流式内容
    streamedContent.value = ''

    // 启动思考动画
    startThinking()

    // 调用API发送消息，使用流式处理
    await chatApi.sendMessage(
      {
        message: currentInput,
        session_id: sessionId.value,
        image_0: file
      },
      handleStreamChunk
    )

    // 确保停止思考动画
    stopThinking()

    // 流处理完成后，打印累积的流式内容
    console.log('流式响应完整内容:', streamedContent.value)

    // 流处理完成后，currentAssistantMessage已经包含完整内容
    currentAssistantMessage.value = null
  } catch (error) {
    // 确保停止思考动画
    stopThinking()

    console.error('发送消息失败:', error)

    // 显示错误消息
    if (currentAssistantMessage.value) {
      if (currentAssistantMessage.value.content.length === 0) {
        currentAssistantMessage.value.content = '抱歉，发送消息失败，请稍后再试。'
      }
      currentAssistantMessage.value = null
    } else {
      const errorMessage: Message = {
        id: Date.now() + 1,
        type: 'assistant',
        content: '抱歉，发送消息失败，请稍后再试。',
        timestamp: new Date(),
        mediaType: 'text'
      }

      messages.value.push(errorMessage)
    }
  } finally {
    isLoading.value = false
    nextTick(() => scrollToBottom())
  }
}

// 创建新会话
const createNewSession = () => {
  sessionId.value = sessionManager.createNewSession()
  messages.value = []
}

// 处理回车发送
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (thinkingInterval.value) {
    clearInterval(thinkingInterval.value)
  }
})

// 添加对音乐推荐的处理函数
const handleMusicRecommendation = async (query: string) => {
  if (props.featureId !== 5) return false

  try {
    isLoading.value = true

    // 创建助手消息占位符
    currentAssistantMessage.value = {
      id: Date.now() + 1,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      mediaType: 'text'
    }

    // 添加到消息列表
    messages.value.push(currentAssistantMessage.value)

    // 重置流式内容
    streamedContent.value = ''

    // 启动思考动画
    startThinking()

    // 使用音乐API处理流式响应
    await musicApi.getRecommendation(
      {
        query: query,
        conversation_id: sessionId.value
      },
      (chunk) => {
        console.log('接收到音乐推荐数据:', chunk);
        // 如果是第一个有效的数据块，停止思考动画
        if (isThinking.value && chunk.trim()) {
          stopThinking();
        }

        // 更新现有消息内容
        if (currentAssistantMessage.value) {
          currentAssistantMessage.value.content += chunk;
          // 累积流式数据
          streamedContent.value += chunk;
          nextTick(() => scrollToBottom());
        }
      }
    );

    // 流处理完成后，清理状态
    stopThinking();
    currentAssistantMessage.value = null;

    return true
  } catch (error) {
    // 确保停止思考动画
    stopThinking()

    console.error('获取音乐推荐失败:', error)

    // 显示错误消息
    if (currentAssistantMessage.value) {
      if (currentAssistantMessage.value.content.length === 0) {
        currentAssistantMessage.value.content = '抱歉，获取音乐推荐失败，请稍后再试。'
      }
      currentAssistantMessage.value = null
    } else {
      const errorMessage: Message = {
        id: Date.now() + 1,
        type: 'assistant',
        content: '抱歉，获取音乐推荐失败，请稍后再试。',
        timestamp: new Date(),
        mediaType: 'text'
      }

      messages.value.push(errorMessage)
    }

    return true
  } finally {
    isLoading.value = false
    nextTick(() => scrollToBottom())
  }
}

// 添加对旅行问题的处理函数
const handleTravelQuestion = async (query: string) => {
  if (props.featureId !== 2) return false

  try {
    isLoading.value = true

    // 创建助手消息占位符
    currentAssistantMessage.value = {
      id: Date.now() + 1,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      mediaType: 'text'
    }

    // 添加到消息列表
    messages.value.push(currentAssistantMessage.value)

    // 重置流式内容
    streamedContent.value = ''

    // 启动思考动画
    startThinking()

    // 调用旅行API发送问题
    await travelApi.askQuestion(
      {
        user_question: query,
        follow_up: false
      },
      handleStreamChunk
    )

    // 确保停止思考动画
    stopThinking()

    // 流处理完成后，currentAssistantMessage已经包含完整内容
    currentAssistantMessage.value = null

    return true
  } catch (error) {
    // 确保停止思考动画
    stopThinking()

    console.error('获取旅行问题回答失败:', error)

    // 显示错误消息
    if (currentAssistantMessage.value) {
      if (currentAssistantMessage.value.content.length === 0) {
        currentAssistantMessage.value.content = '抱歉，获取旅行问题回答失败，请稍后再试。'
      }
      currentAssistantMessage.value = null
    } else {
      const errorMessage: Message = {
        id: Date.now() + 1,
        type: 'assistant',
        content: '抱歉，获取旅行问题回答失败，请稍后再试。',
        timestamp: new Date(),
        mediaType: 'text'
      }

      messages.value.push(errorMessage)
    }

    return true
  } finally {
    isLoading.value = false
    nextTick(() => scrollToBottom())
  }
}
</script>

<template>
  <div class="chat-area">
    <!-- 聊天消息区域 -->
    <div ref="chatContainer" class="messages-container">
      <div v-if="messages.length === 0" class="welcome-message">
        <h3>欢迎使用AI助手</h3>
        <p>请在下方输入您的问题，我将为您提供帮助。</p>
        <div class="test-buttons">
          <button v-if="messages.length > 0" class="new-session-btn" @click="createNewSession">
            开始新会话
          </button>
        </div>
      </div>

      <div v-for="message in messages" :key="message.id" class="message" :class="message.type">
        <div class="message-content">
          <!-- 文本消息 -->
          <div v-if="!message.mediaType || message.mediaType === 'text'" class="message-text">
            <!-- 如果是助手消息且处于思考状态 -->
            <template
              v-if="message.type === 'assistant' && isThinking && currentAssistantMessage && currentAssistantMessage.id === message.id">
              <span class="thinking-text">{{ thinkingText }}</span><span class="cursor"></span>
            </template>
            <!-- 如果是助手消息且非思考状态，使用 Markdown 渲染 -->
            <template v-else-if="message.type === 'assistant'">
              <VueMarkdownRenderer :content="message.content"
                :is-streaming="!!(currentAssistantMessage && currentAssistantMessage.id === message.id)" />
              <span v-if="currentAssistantMessage && currentAssistantMessage.id === message.id" class="cursor"></span>
            </template>
            <!-- 用户消息保持原样 -->
            <pre v-else class="message-pre">{{ message.content }}</pre>
          </div>

          <!-- 图片消息 -->
          <div v-else-if="message.mediaType === 'image'" class="message-media">
            <img :src="message.mediaUrl" alt="图片消息" class="message-image" />
            <div v-if="message.content" class="message-text">
              <!-- 如果是助手消息，使用 Markdown 渲染 -->
              <VueMarkdownRenderer v-if="message.type === 'assistant'" :content="message.content"
                :is-streaming="!!(currentAssistantMessage && currentAssistantMessage.id === message.id)" />
              <!-- 用户消息保持原样 -->
              <pre v-else class="message-pre">{{ message.content }}</pre>
            </div>
          </div>

          <!-- 视频消息 -->
          <div v-else-if="message.mediaType === 'video'" class="message-media">
            <video :src="message.mediaUrl" controls class="message-video"></video>
            <div v-if="message.content" class="message-text">
              <!-- 如果是助手消息，使用 Markdown 渲染 -->
              <VueMarkdownRenderer v-if="message.type === 'assistant'" :content="message.content"
                :is-streaming="!!(currentAssistantMessage && currentAssistantMessage.id === message.id)" />
              <!-- 用户消息保持原样 -->
              <pre v-else class="message-pre">{{ message.content }}</pre>
            </div>
          </div>

          <div class="message-time">
            {{ message.timestamp.toLocaleTimeString() }}
          </div>
        </div>
      </div>
      <!-- 加载状态 -->
      <div v-if="isLoading && !currentAssistantMessage" class="message assistant">
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <!-- 预览区域 -->
      <div v-if="previewUrl" class="preview-container">
        <div class="preview-header">
          <span>{{ uploadType === 'image' ? '图片预览' : '视频预览' }}</span>
          <button class="clear-preview" @click="clearSelectedFile">×</button>
        </div>
        <img v-if="uploadType === 'image'" :src="previewUrl" class="media-preview" alt="图片预览" />
        <video v-if="uploadType === 'video'" :src="previewUrl" class="media-preview" controls></video>
      </div>

      <!-- 上传错误提示 -->
      <div v-if="uploadError" class="upload-error">
        {{ uploadError }}
        <button class="clear-error" @click="uploadError = null">×</button>
      </div>

      <div class="input-container">
        <textarea v-model="inputText" placeholder="请输入您的问题..." class="message-input" rows="10"
          @keydown="handleKeydown"></textarea>

        <div class="action-buttons">
          <input type="file" id="image-upload" accept="image/jpeg,image/jpg,image/png" class="file-input"
            @change="(e) => handleFileUpload(e, 'image')" />
          <label for="image-upload" class="upload-button">
            上传图片
          </label>

          <input type="file" id="video-upload" accept="video/*" class="file-input"
            @change="(e) => handleFileUpload(e, 'video')" />
          <label for="video-upload" class="upload-button">
            上传视频
          </label>

          <button class="send-button" :disabled="(!inputText.trim() && !selectedFile) || isLoading"
            @click="sendMessage">
            提交
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.welcome-message h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #111827;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background: #f3f4f6;
  color: #111827;
  border-bottom-left-radius: 4px;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
}

.thinking-text {
  display: inline;
  color: #6b7280;
}

/* 思考中的省略号动画 - 无跳动效果 */
@keyframes thinking-dots {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }
}

.message-pre {
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {

  0%,
  60%,
  100% {
    transform: translateY(0);
  }

  30% {
    transform: translateY(-10px);
  }
}

/* 打字光标动画 */
.cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: currentColor;
  margin-left: 2px;
  vertical-align: middle;
  animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {

  from,
  to {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.input-area {
  padding: 20px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.message-input:focus {
  border-color: #3b82f6;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: flex-start;
  width: 100%;
  flex-wrap: wrap;
}

.file-input {
  display: none;
}

.upload-button {
  background: #3b82f6;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
  min-width: 80px;
  text-align: center;
}

.upload-button:hover {
  background: #2563eb;
}

.upload-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.send-button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  margin-left: auto;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
}

.send-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.preview-container {
  max-width: 70%;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
}

.preview-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.preview-header span {
  font-size: 14px;
  color: #4b5563;
}

.clear-preview {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 20px;
  padding: 0;
  line-height: 1;
}

.clear-preview:hover {
  color: #6b7280;
}

.media-preview {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.message-media {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  object-fit: contain;
}

.message-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.upload-error {
  width: 100%;
  padding: 10px;
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #b91c1c;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clear-error {
  background: none;
  border: none;
  color: #b91c1c;
  cursor: pointer;
  font-size: 18px;
  padding: 0 8px;
}

.new-session-btn,
.test-markdown-btn,
.test-url-btn {
  margin-top: 16px;
  margin-right: 8px;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.new-session-btn:hover,
.test-markdown-btn:hover,
.test-url-btn:hover {
  background-color: #2563eb;
}

.test-markdown-btn {
  background-color: #10b981;
}

.test-markdown-btn:hover {
  background-color: #059669;
}

.test-url-btn {
  background-color: #f59e0b;
}

.test-url-btn:hover {
  background-color: #d97706;
}

.test-buttons {
  margin-bottom: 20px;
}

.math-test {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.math-test p {
  margin-bottom: 15px;
}
</style>