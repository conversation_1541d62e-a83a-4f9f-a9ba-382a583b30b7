"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CssStyles = void 0;
var html_classes_js_1 = require("./html_classes.js");
var CssStyles;
(function (CssStyles) {
    function makeClass_(name) {
        return '.' + (html_classes_js_1.HtmlClasses[name] || name);
    }
    var INFO_STYLES = {};
    INFO_STYLES[makeClass_('INFOCLOSE')] = '{' +
        '  top:.2em; right:.2em;' +
        '}',
        INFO_STYLES[makeClass_('INFOCONTENT')] = '{' +
            '  overflow:auto; text-align:left; font-size:80%;' +
            '  padding:.4em .6em; border:1px inset; margin:1em 0px;' +
            '  max-height:20em; max-width:30em; background-color:#EEEEEE;' +
            '  white-space:normal;' +
            '}',
        INFO_STYLES[makeClass_('INFO') + makeClass_('MOUSEPOST')] = '{' +
            'outline:none;' +
            '}',
        INFO_STYLES[makeClass_('INFO')] = '{' +
            '  position:fixed; left:50%; width:auto; text-align:center;' +
            '  border:3px outset; padding:1em 2em; background-color:#DDDDDD;' +
            '  color:black;' +
            '  cursor:default; font-family:message-box; font-size:120%;' +
            '  font-style:normal; text-indent:0; text-transform:none;' +
            '  line-height:normal; letter-spacing:normal; word-spacing:normal;' +
            '  word-wrap:normal; white-space:nowrap; float:none; z-index:201;' +
            '  border-radius: 15px;                     /* Opera 10.5 and IE9 */' +
            '  -webkit-border-radius:15px;               /* Safari and Chrome */' +
            '  -moz-border-radius:15px;                  /* Firefox */' +
            '  -khtml-border-radius:15px;                /* Konqueror */' +
            '  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */' +
            '  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */' +
            '  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */' +
            '  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */' +
            '  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2,' +
            ' OffY=2, Color="gray", Positive="true"); /* IE */' +
            '}';
    var MENU_STYLES = {};
    MENU_STYLES[makeClass_('MENU')] = '{' +
        '  position:absolute;' +
        '  background-color:white;' +
        '  color:black;' +
        '  width:auto; padding:5px 0px;' +
        '  border:1px solid #CCCCCC; margin:0; cursor:default;' +
        '  font: menu; text-align:left; text-indent:0; text-transform:none;' +
        '  line-height:normal; letter-spacing:normal; word-spacing:normal;' +
        '  word-wrap:normal; white-space:nowrap; float:none; z-index:201;' +
        '  border-radius: 5px;                     /* Opera 10.5 and IE9 */' +
        '  -webkit-border-radius: 5px;             /* Safari and Chrome */' +
        '  -moz-border-radius: 5px;                /* Firefox */' +
        '  -khtml-border-radius: 5px;              /* Konqueror */' +
        '  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */' +
        '  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */' +
        '  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */' +
        '  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */' +
        '}',
        MENU_STYLES[makeClass_('MENUITEM')] = '{' +
            '  padding: 1px 2em;' +
            '  background:transparent;' +
            '}',
        MENU_STYLES[makeClass_('MENUARROW')] = '{' +
            '  position:absolute; right:.5em; padding-top:.25em; color:#666666;' +
            '  font-family: null; font-size: .75em' +
            '}',
        MENU_STYLES[makeClass_('MENUACTIVE') + ' ' + makeClass_('MENUARROW')] =
            '{color:white}',
        MENU_STYLES[makeClass_('MENUARROW') + makeClass_('RTL')] =
            '{left:.5em; right:auto}',
        MENU_STYLES[makeClass_('MENUCHECK')] = '{' +
            '  position:absolute; left:.7em;' +
            '  font-family: null' +
            '}',
        MENU_STYLES[makeClass_('MENUCHECK') + makeClass_('RTL')] =
            '{ right:.7em; left:auto }',
        MENU_STYLES[makeClass_('MENURADIOCHECK')] = '{' +
            '  position:absolute; left: .7em;' +
            '}',
        MENU_STYLES[makeClass_('MENURADIOCHECK') + makeClass_('RTL')] = '{' +
            '  right: .7em; left:auto' +
            '}',
        MENU_STYLES[makeClass_('MENUINPUTBOX')] = '{' +
            '  padding-left: 1em; right:.5em; color:#666666;' +
            '  font-family: null;' +
            '}',
        MENU_STYLES[makeClass_('MENUINPUTBOX') + makeClass_('RTL')] = '{' +
            '  left: .1em;' +
            '}',
        MENU_STYLES[makeClass_('MENUCOMBOBOX')] = '{' +
            '  left:.1em; padding-bottom:.5em;' +
            '}',
        MENU_STYLES[makeClass_('MENUSLIDER')] = '{' +
            '  left: .1em;' +
            '}',
        MENU_STYLES[makeClass_('SLIDERVALUE')] = '{' +
            '  position:absolute; right:.1em; padding-top:.25em; color:#333333;' +
            '  font-size: .75em' +
            '}',
        MENU_STYLES[makeClass_('SLIDERBAR')] = '{' +
            '  outline: none; background: #d3d3d3' +
            '}',
        MENU_STYLES[makeClass_('MENULABEL')] = '{' +
            '  padding: 1px 2em 3px 1.33em;' +
            '  font-style:italic' +
            '}',
        MENU_STYLES[makeClass_('MENURULE')] = '{' +
            '  border-top: 1px solid #DDDDDD;' +
            '  margin: 4px 3px;' +
            '}',
        MENU_STYLES[makeClass_('MENUDISABLED')] = '{' +
            '  color:GrayText' +
            '}',
        MENU_STYLES[makeClass_('MENUACTIVE')] = '{' +
            '  background-color: #606872;' +
            '  color: white;' +
            '}',
        MENU_STYLES[makeClass_('MENUDISABLED') + ':focus'] = '{' +
            '  background-color: #E8E8E8' +
            '}',
        MENU_STYLES[makeClass_('MENULABEL') + ':focus'] = '{' +
            '  background-color: #E8E8E8' +
            '}',
        MENU_STYLES[makeClass_('CONTEXTMENU') + ':focus'] = '{' +
            '  outline:none' +
            '}',
        MENU_STYLES[makeClass_('CONTEXTMENU') + ' ' +
            makeClass_('MENUITEM') + ':focus'] = '{' +
            '  outline:none' +
            '}',
        MENU_STYLES[makeClass_('SELECTIONMENU')] = '{' +
            '  position:relative; float:left;' +
            '  border-bottom: none;' +
            ' -webkit-box-shadow:none;' +
            ' -webkit-border-radius:0px; ' +
            '}',
        MENU_STYLES[makeClass_('SELECTIONITEM')] = '{' +
            '  padding-right: 1em;' +
            '}',
        MENU_STYLES[makeClass_('SELECTION')] = '{' +
            '  right: 40%; width:50%; ' +
            '}',
        MENU_STYLES[makeClass_('SELECTIONBOX')] = '{' +
            '  padding: 0em; max-height:20em; max-width: none;' +
            '  background-color:#FFFFFF;' +
            '}',
        MENU_STYLES[makeClass_('SELECTIONDIVIDER')] = '{' +
            '  clear: both; border-top: 2px solid #000000;' +
            '}',
        MENU_STYLES[makeClass_('MENU') + ' ' + makeClass_('MENUCLOSE')] = '{' +
            '  top:-10px; left:-10px' +
            '}';
    var CLOSE_ICON_STYLES = {};
    CLOSE_ICON_STYLES[makeClass_('MENUCLOSE')] = '{' +
        '  position:absolute;' +
        '  cursor:pointer;' +
        '  display:inline-block;' +
        '  border:2px solid #AAA;' +
        '  border-radius:18px;' +
        '  -webkit-border-radius: 18px;             /* Safari and Chrome */' +
        '  -moz-border-radius: 18px;                /* Firefox */' +
        '  -khtml-border-radius: 18px;              /* Konqueror */' +
        '  font-family: "Courier New", Courier;' +
        '  font-size:24px;' +
        '  color:#F0F0F0' +
        '}',
        CLOSE_ICON_STYLES[makeClass_('MENUCLOSE') + ' span'] = '{' +
            '  display:block; background-color:#AAA; border:1.5px solid;' +
            '  border-radius:18px;' +
            '  -webkit-border-radius: 18px;             /* Safari and Chrome */' +
            '  -moz-border-radius: 18px;                /* Firefox */' +
            '  -khtml-border-radius: 18px;              /* Konqueror */' +
            '  line-height:0;' +
            '  padding:8px 0 6px     /* may need to be browser-specific */' +
            '}',
        CLOSE_ICON_STYLES[makeClass_('MENUCLOSE') + ':hover'] = '{' +
            '  color:white!important;' +
            '  border:2px solid #CCC!important' +
            '}',
        CLOSE_ICON_STYLES[makeClass_('MENUCLOSE') + ':hover span'] = '{' +
            '  background-color:#CCC!important' +
            '}',
        CLOSE_ICON_STYLES[makeClass_('MENUCLOSE') + ':hover:focus'] = '{' +
            '  outline:none' +
            '}';
    var INFO_ADDED = false;
    var MENU_ADDED = false;
    var CLOSE_ICON_ADDED = false;
    function addMenuStyles(opt_document) {
        if (MENU_ADDED) {
            return;
        }
        addStyles_(MENU_STYLES, opt_document);
        MENU_ADDED = true;
        addCloseIconStyles_(opt_document);
    }
    CssStyles.addMenuStyles = addMenuStyles;
    function addInfoStyles(opt_document) {
        if (INFO_ADDED) {
            return;
        }
        addStyles_(INFO_STYLES, opt_document);
        INFO_ADDED = true;
        addCloseIconStyles_(opt_document);
    }
    CssStyles.addInfoStyles = addInfoStyles;
    function addCloseIconStyles_(opt_document) {
        if (CLOSE_ICON_ADDED) {
            return;
        }
        addStyles_(CLOSE_ICON_STYLES, opt_document);
        CLOSE_ICON_ADDED = true;
    }
    function addStyles_(styles, opt_document) {
        var doc = opt_document || document;
        var element = doc.createElement('style');
        element.type = 'text/css';
        var inner = '';
        for (var style in styles) {
            inner += style;
            inner += ' ';
            inner += styles[style];
            inner += '\n';
        }
        element.innerHTML = inner;
        doc.head.appendChild(element);
    }
})(CssStyles = exports.CssStyles || (exports.CssStyles = {}));
//# sourceMappingURL=css_util.js.map