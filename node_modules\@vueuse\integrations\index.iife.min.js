(function(w,C,U,n,L,$,k,p,K,X,Y,P,Z,V,x,ee){"use strict";function te(t){var o=Object.create(null);return t&&Object.keys(t).forEach(function(e){if(e!=="default"){var a=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(o,e,a.get?a:{enumerable:!0,get:function(){return t[e]}})}}),o.default=t,Object.freeze(o)}var ne=te($);const ae=U.default||U;function oe(t,o,e={}){const{validateOption:a={},immediate:r=!0,manual:s=!1}=e,l=C.toRef(t),c=n.shallowRef(null),u=n.shallowRef(!0),d=n.shallowRef(!r||s),v=n.computed(()=>{var i;return((i=c.value)==null?void 0:i.errors)||[]}),g=n.computed(()=>{var i;return((i=c.value)==null?void 0:i.fields)||{}}),h=n.computed(()=>new ae(n.toValue(o))),y=async()=>{u.value=!1,d.value=!1;try{await h.value.validate(l.value,a),d.value=!0,c.value=null}catch(i){c.value=i}finally{u.value=!0}return{pass:d.value,errorInfo:c.value,errors:v.value,errorFields:g.value}};s||n.watch([l,h],()=>y(),{immediate:r,deep:!0});const R={isFinished:u,pass:d,errors:v,errorInfo:c,errorFields:g,execute:y};function E(){return new Promise((i,b)=>{C.until(u).toBe(!0).then(()=>i(R)).catch(A=>b(A))})}return{...R,then(i,b){return E().then(i,b)}}}function le(...t){const o=typeof t[0]=="string"?t[0]:void 0,e=typeof o=="string"?1:0,a={immediate:!!e,shallow:!0,abortPrevious:!0};let r={},s=L,l=a;const c=m=>!!m?.request;t.length>0+e&&(c(t[0+e])?s=t[0+e]:r=t[0+e]),t.length>1+e&&c(t[1+e])&&(s=t[1+e]),(t.length===2+e&&!c(t[1+e])||t.length===3+e)&&(l=t[t.length-1]||a);const{shallow:u,onSuccess:d=C.noop,onError:v=C.noop,immediate:g,resetOnExecute:h=!1}=l,y=l.initialData,R=n.shallowRef(),E=(u?n.shallowRef:n.ref)(y),i=n.shallowRef(!1),b=n.shallowRef(!1),A=n.shallowRef(!1),D=n.shallowRef();let S=new AbortController;const F=m=>{i.value||!b.value||(S.abort(m),S=new AbortController,A.value=!0,b.value=!1,i.value=!1)},f=m=>{b.value=m,i.value=!m},_=()=>{h&&(E.value=y)},Q=()=>new Promise((m,I)=>{C.until(i).toBe(!0).then(()=>D.value?I(D.value):m(q))}),H={then:(...m)=>Q().then(...m),catch:(...m)=>Q().catch(...m)};let j=0;const z=(m=o,I={})=>{D.value=void 0;const J=typeof m=="string"?m:o??I.url;if(J===void 0)return D.value=new L.AxiosError(L.AxiosError.ERR_INVALID_URL),i.value=!0,H;_(),l.abortPrevious!==!1&&F(),f(!0),j+=1;const pe=j;return A.value=!1,s(J,{...r,...typeof m=="object"?m:I,signal:S.signal}).then(O=>{if(A.value)return;R.value=O;const M=O.data;E.value=M,d(M)}).catch(O=>{D.value=O,v(O)}).finally(()=>{var O;(O=l.onFinish)==null||O.call(l),pe===j&&f(!1)}),H};g&&o&&z();const q={response:R,data:E,error:D,isFinished:i,isLoading:b,cancel:F,isAborted:A,isCanceled:A,abort:F,execute:z};return{...q,...H}}const N=Object.entries(ne).filter(([t,o])=>typeof o=="function"&&t.endsWith("Case")).reduce((t,[o,e])=>(t[o]=e,t),{});function re(t,o,e){const a=n.computed(()=>{const s=n.toValue(o);if(!N[s])throw new Error(`Invalid change case type "${s}"`);return s});if(typeof t=="function")return n.computed(()=>N[a.value](n.toValue(t),n.toValue(e)));const r=n.ref(t);return n.computed({get(){return N[a.value](r.value,n.toValue(e))},set(s){r.value=s}})}function se(t){const o=new k(t?t.headers.cookie:null);return(e,{doNotParse:a=!1,autoUpdateDependencies:r=!1}={})=>T(e,{doNotParse:a,autoUpdateDependencies:r},o)}function T(t,{doNotParse:o=!1,autoUpdateDependencies:e=!1}={},a=new k){const r=e?[...t||[]]:t;let s=a.getAll({doNotParse:!0});const l=n.shallowRef(0),c=()=>{const u=a.getAll({doNotParse:!0});ue(r||null,u,s)&&l.value++,s=u};return a.addChangeListener(c),C.tryOnScopeDispose(()=>{a.removeChangeListener(c)}),{get:(...u)=>(e&&r&&!r.includes(u[0])&&r.push(u[0]),l.value,a.get(u[0],{doNotParse:o,...u[1]})),getAll:(...u)=>(l.value,a.getAll({doNotParse:o,...u[0]})),set:(...u)=>a.set(...u),remove:(...u)=>a.remove(...u),addChangeListener:(...u)=>a.addChangeListener(...u),removeChangeListener:(...u)=>a.removeChangeListener(...u)}}function ue(t,o,e){if(!t)return!0;for(const a of t)if(o[a]!==e[a])return!0;return!1}function ce(t,o){const e=n.ref();let a=[];const r=p.createEventHook(),s=p.createEventHook(),l=p.createEventHook(),c=p.createEventHook(),u=p.createEventHook(),d=n.shallowRef(!1),v=n.shallowRef(!1),g=n.shallowRef(!1),h=n.shallowRef(!1),y=n.ref({color:"black",size:3,arrowEnd:!1,cornerRadius:0,dasharray:void 0,fill:"transparent",mode:"draw",...o?.brush});n.watch(y,()=>{const f=e.value;f&&(f.brush=y.value,f.mode=y.value.mode)},{deep:!0});const R=()=>{var f;return(f=e.value)==null?void 0:f.undo()},E=()=>{var f;return(f=e.value)==null?void 0:f.redo()},i=()=>{var f;return(f=e.value)==null?void 0:f.clear()},b=()=>{var f;return(f=e.value)==null?void 0:f.cancel()},A=f=>{var _;return(_=e.value)==null?void 0:_.load(f)},D=()=>{var f;return(f=e.value)==null?void 0:f.dump()},S=()=>{var f;a.forEach(_=>_()),(f=e.value)==null||f.unmount()},F=()=>{e.value&&(d.value=e.value.canUndo(),v.value=e.value.canRedo(),g.value=e.value.altPressed,h.value=e.value.shiftPressed)};return n.watch(()=>p.unrefElement(t),f=>{!f||typeof SVGSVGElement>"u"||!(f instanceof SVGSVGElement)||(e.value&&S(),e.value=K.createDrauu({el:f,...o}),F(),a=[e.value.on("canceled",()=>s.trigger()),e.value.on("committed",_=>l.trigger(_)),e.value.on("start",()=>c.trigger()),e.value.on("end",()=>u.trigger()),e.value.on("changed",()=>{F(),r.trigger()})])},{flush:"post"}),C.tryOnScopeDispose(()=>S()),{drauuInstance:e,load:A,dump:D,clear:i,cancel:b,undo:R,redo:E,canUndo:d,canRedo:v,brush:y,onChanged:r.on,onCommitted:l.on,onStart:c.on,onEnd:u.on,onCanceled:s.on}}function ie(t,o={}){let e;const{immediate:a,...r}=o,s=n.shallowRef(!1),l=n.shallowRef(!1),c=h=>e&&e.activate(h),u=h=>e&&e.deactivate(h),d=()=>{e&&(e.pause(),l.value=!0)},v=()=>{e&&(e.unpause(),l.value=!1)},g=n.computed(()=>{const h=n.toValue(t);return p.toArray(h).map(y=>{const R=n.toValue(y);return typeof R=="string"?R:p.unrefElement(R)}).filter(C.notNullish)});return n.watch(g,h=>{h.length&&(e=X.createFocusTrap(h,{...r,onActivate(){s.value=!0,o.onActivate&&o.onActivate()},onDeactivate(){s.value=!1,o.onDeactivate&&o.onDeactivate()}}),a&&c())},{flush:"post"}),p.tryOnScopeDispose(()=>u()),{hasFocus:s,isPaused:l,activate:c,deactivate:u,pause:d,unpause:v}}function fe(t,o,e){const a=()=>{var l,c;return new Y((l=n.toValue(o))!=null?l:[],(c=n.toValue(e))==null?void 0:c.fuseOptions)},r=n.ref(a());n.watch(()=>{var l;return(l=n.toValue(e))==null?void 0:l.fuseOptions},()=>{r.value=a()},{deep:!0}),n.watch(()=>n.toValue(o),l=>{r.value.setCollection(l)},{deep:!0});const s=n.computed(()=>{const l=n.toValue(e);if(l?.matchAllWhenSearchEmpty&&!n.toValue(t))return n.toValue(o).map((u,d)=>({item:u,refIndex:d}));const c=l?.resultLimit;return r.value.search(n.toValue(t),c?{limit:c}:void 0)});return{fuse:r,results:s}}function de(t,o,e={}){const{flush:a="pre",deep:r=!0,shallow:s=!1,onError:l=i=>{console.error(i)},writeDefaults:c=!0}=e,u=n.shallowRef(!1),d=(s?n.shallowRef:n.ref)(o),v=n.toValue(o);async function g(){try{const i=await P.get(t);i===void 0?v!=null&&c&&await P.set(t,v):d.value=i}catch(i){l(i)}u.value=!0}g();async function h(){try{d.value==null?await P.del(t):await P.update(t,()=>n.toRaw(d.value))}catch(i){l(i)}}const{pause:y,resume:R}=p.watchPausable(d,()=>h(),{flush:a,deep:r});async function E(i){y(),d.value=i,await h(),R()}return{set:E,isFinished:u,data:d}}function ve(t,o={}){const{onError:e,fallbackValue:a=null}=o,r=(c,u)=>{try{return Z.jwtDecode(c,u)}catch(d){return e?.(d),a}},s=n.computed(()=>r(n.toValue(t),{header:!0})),l=n.computed(()=>r(n.toValue(t)));return{header:s,payload:l}}function he(t=null,o){const e=C.toRef(t),a=n.computed({set:s=>s?V.start():V.done(),get:()=>typeof e.value=="number"&&e.value<1});o&&V.configure(o);const r=V.set;return V.set=s=>(e.value=s,r.call(V,s)),n.watchEffect(()=>{typeof e.value=="number"&&C.isClient&&r.call(V,e.value)}),C.tryOnScopeDispose(V.remove),{isLoading:a,progress:e,start:V.start,done:V.done,remove:()=>{e.value=null,V.remove()}}}function me(t,o){const e=C.toRef(t),a=n.shallowRef("");return n.watch(e,async r=>{e.value&&C.isClient&&(a.value=await x.toDataURL(r,o))},{immediate:!0}),a}function we(t,o,e={}){let a;const{document:r=p.defaultDocument,...s}=e,l={onUpdate:v=>{G(o,v.oldIndex,v.newIndex,v)}},c=()=>{const v=typeof t=="string"?r?.querySelector(t):p.unrefElement(t);!v||a!==void 0||(a=new ee(v,{...l,...s}))},u=()=>{a?.destroy(),a=void 0},d=(v,g)=>{if(g!==void 0)a?.option(v,g);else return a?.option(v)};return p.tryOnMounted(c),p.tryOnScopeDispose(u),{stop:u,start:c,option:d}}function B(t,o,e){const a=t.children[e];t.insertBefore(o,a)}function W(t){t.parentNode&&t.parentNode.removeChild(t)}function G(t,o,e,a=null){a!=null&&(W(a.item),B(a.from,a.item,o));const r=n.isRef(t),s=r?[...n.toValue(t)]:n.toValue(t);if(e>=0&&e<s.length){const l=s.splice(o,1)[0];n.nextTick(()=>{s.splice(e,0,l),r&&(t.value=s)})}}w.createCookies=se,w.insertNodeAt=B,w.moveArrayElement=G,w.removeNode=W,w.useAsyncValidator=oe,w.useAxios=le,w.useChangeCase=re,w.useCookies=T,w.useDrauu=ce,w.useFocusTrap=ie,w.useFuse=fe,w.useIDBKeyval=de,w.useJwt=ve,w.useNProgress=he,w.useQRCode=me,w.useSortable=we})(this.VueUse=this.VueUse||{},VueUse,AsyncValidator,Vue,axios,changeCase,UniversalCookie,VueUse,Drauu,focusTrap,Fuse,idbKeyval,jwt_decode,nprogress,QRCode,Sortable);
