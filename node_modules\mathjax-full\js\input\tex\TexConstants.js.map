{"version": 3, "file": "TexConstants.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/TexConstants.ts"], "names": [], "mappings": ";;;AAyBA,IAAiB,WAAW,CA+I3B;AA/ID,WAAiB,WAAW;IAEb,mBAAO,GAAG;QACrB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,aAAa;QACzB,YAAY,EAAE,eAAe;QAC7B,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,cAAc;QAC3B,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,aAAa;QACzB,SAAS,EAAE,YAAY;QACvB,aAAa,EAAE,iBAAiB;QAChC,eAAe,EAAE,mBAAmB;QACpC,mBAAmB,EAAE,wBAAwB;QAC7C,SAAS,EAAE,WAAW;QACtB,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,WAAW;QACtB,YAAY,EAAE,mBAAmB;QACjC,gBAAgB,EAAE,wBAAwB;QAC1C,QAAQ,EAAE,eAAe;QACzB,YAAY,EAAE,oBAAoB;QAClC,UAAU,EAAE,aAAa;KAC1B,CAAC;IAEW,gBAAI,GAAG;QAClB,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,SAAS;KACnB,CAAC;IAEW,qBAAS,GAAG;QACvB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,WAAW;QACtB,QAAQ,EAAE,UAAU;KACrB,CAAC;IAEW,0BAAc,GAAG;QAC5B,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;QACd,SAAS,EAAE,WAAW;QACtB,kBAAkB,EAAE,qBAAqB;KAC1C,CAAC;IAEW,uBAAW,GAAG;QACzB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;QACd,IAAI,EAAE,MAAM;QACZ,EAAE,EAAE,IAAI;QACR,WAAW,EAAE,aAAa;KAC3B,CAAC;IAEW,uBAAW,GAAG;QACzB,WAAW,EAAE,aAAa;KAC3B,CAAC;IAEW,yBAAa,GAAG;QAC3B,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;KACf,CAAC;IAEW,oBAAQ,GAAG;QACtB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,WAAW;QACtB,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,SAAS;QAClB,GAAG,EAAE,KAAK;QACV,UAAU,EAAE,YAAY;QACxB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,OAAO;QACd,GAAG,EAAE,KAAK;QACV,MAAM,EAAE,QAAQ;QAChB,gBAAgB,EAAE,kBAAkB;QACpC,kBAAkB,EAAE,oBAAoB;QACxC,cAAc,EAAE,gBAAgB;QAChC,gBAAgB,EAAE,kBAAkB;QACpC,cAAc,EAAE,gBAAgB;QAChC,OAAO,EAAE,SAAS;QAClB,eAAe,EAAE,iBAAiB;KACnC,CAAC;IAEW,iBAAK,GAAG;QACnB,GAAG,EAAE,KAAK;QACV,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,OAAO;KACf,CAAC;IAEW,iBAAK,GAAG;QACnB,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,QAAQ;KACjB,CAAC;IAEW,gBAAI,GAAG;QAClB,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,OAAO;QACd,WAAW,EAAE,aAAa;QAC1B,YAAY,EAAE,cAAc;KAC7B,CAAC;IAEW,iBAAK,GAAG;QACnB,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,KAAK;KACX,CAAC;IAEW,sBAAU,GAAG;QACxB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,YAAY;QACxB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,OAAO;KACf,CAAC;IAEW,oBAAQ,GAAG;QACtB,QAAQ,EAAE,WAAW;QACrB,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,UAAU;QACpB,KAAK,EAAE,OAAO;KACf,CAAC;IAEW,gBAAI,GAAG;QAClB,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,IAAI;KACT,CAAC;AAEJ,CAAC,EA/IgB,WAAW,GAAX,mBAAW,KAAX,mBAAW,QA+I3B"}