"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CHTMLWrappers = void 0;
var Wrapper_js_1 = require("./Wrapper.js");
var math_js_1 = require("./Wrappers/math.js");
var mi_js_1 = require("./Wrappers/mi.js");
var mo_js_1 = require("./Wrappers/mo.js");
var mn_js_1 = require("./Wrappers/mn.js");
var ms_js_1 = require("./Wrappers/ms.js");
var mtext_js_1 = require("./Wrappers/mtext.js");
var mspace_js_1 = require("./Wrappers/mspace.js");
var mpadded_js_1 = require("./Wrappers/mpadded.js");
var menclose_js_1 = require("./Wrappers/menclose.js");
var mrow_js_1 = require("./Wrappers/mrow.js");
var mfenced_js_1 = require("./Wrappers/mfenced.js");
var mfrac_js_1 = require("./Wrappers/mfrac.js");
var msqrt_js_1 = require("./Wrappers/msqrt.js");
var mroot_js_1 = require("./Wrappers/mroot.js");
var msubsup_js_1 = require("./Wrappers/msubsup.js");
var munderover_js_1 = require("./Wrappers/munderover.js");
var mmultiscripts_js_1 = require("./Wrappers/mmultiscripts.js");
var mtable_js_1 = require("./Wrappers/mtable.js");
var mtr_js_1 = require("./Wrappers/mtr.js");
var mtd_js_1 = require("./Wrappers/mtd.js");
var maction_js_1 = require("./Wrappers/maction.js");
var mglyph_js_1 = require("./Wrappers/mglyph.js");
var semantics_js_1 = require("./Wrappers/semantics.js");
var TeXAtom_js_1 = require("./Wrappers/TeXAtom.js");
var TextNode_js_1 = require("./Wrappers/TextNode.js");
exports.CHTMLWrappers = (_a = {},
    _a[math_js_1.CHTMLmath.kind] = math_js_1.CHTMLmath,
    _a[mrow_js_1.CHTMLmrow.kind] = mrow_js_1.CHTMLmrow,
    _a[mrow_js_1.CHTMLinferredMrow.kind] = mrow_js_1.CHTMLinferredMrow,
    _a[mi_js_1.CHTMLmi.kind] = mi_js_1.CHTMLmi,
    _a[mo_js_1.CHTMLmo.kind] = mo_js_1.CHTMLmo,
    _a[mn_js_1.CHTMLmn.kind] = mn_js_1.CHTMLmn,
    _a[ms_js_1.CHTMLms.kind] = ms_js_1.CHTMLms,
    _a[mtext_js_1.CHTMLmtext.kind] = mtext_js_1.CHTMLmtext,
    _a[mspace_js_1.CHTMLmspace.kind] = mspace_js_1.CHTMLmspace,
    _a[mpadded_js_1.CHTMLmpadded.kind] = mpadded_js_1.CHTMLmpadded,
    _a[menclose_js_1.CHTMLmenclose.kind] = menclose_js_1.CHTMLmenclose,
    _a[mfrac_js_1.CHTMLmfrac.kind] = mfrac_js_1.CHTMLmfrac,
    _a[msqrt_js_1.CHTMLmsqrt.kind] = msqrt_js_1.CHTMLmsqrt,
    _a[mroot_js_1.CHTMLmroot.kind] = mroot_js_1.CHTMLmroot,
    _a[msubsup_js_1.CHTMLmsub.kind] = msubsup_js_1.CHTMLmsub,
    _a[msubsup_js_1.CHTMLmsup.kind] = msubsup_js_1.CHTMLmsup,
    _a[msubsup_js_1.CHTMLmsubsup.kind] = msubsup_js_1.CHTMLmsubsup,
    _a[munderover_js_1.CHTMLmunder.kind] = munderover_js_1.CHTMLmunder,
    _a[munderover_js_1.CHTMLmover.kind] = munderover_js_1.CHTMLmover,
    _a[munderover_js_1.CHTMLmunderover.kind] = munderover_js_1.CHTMLmunderover,
    _a[mmultiscripts_js_1.CHTMLmmultiscripts.kind] = mmultiscripts_js_1.CHTMLmmultiscripts,
    _a[mfenced_js_1.CHTMLmfenced.kind] = mfenced_js_1.CHTMLmfenced,
    _a[mtable_js_1.CHTMLmtable.kind] = mtable_js_1.CHTMLmtable,
    _a[mtr_js_1.CHTMLmtr.kind] = mtr_js_1.CHTMLmtr,
    _a[mtr_js_1.CHTMLmlabeledtr.kind] = mtr_js_1.CHTMLmlabeledtr,
    _a[mtd_js_1.CHTMLmtd.kind] = mtd_js_1.CHTMLmtd,
    _a[maction_js_1.CHTMLmaction.kind] = maction_js_1.CHTMLmaction,
    _a[mglyph_js_1.CHTMLmglyph.kind] = mglyph_js_1.CHTMLmglyph,
    _a[semantics_js_1.CHTMLsemantics.kind] = semantics_js_1.CHTMLsemantics,
    _a[semantics_js_1.CHTMLannotation.kind] = semantics_js_1.CHTMLannotation,
    _a[semantics_js_1.CHTMLannotationXML.kind] = semantics_js_1.CHTMLannotationXML,
    _a[semantics_js_1.CHTMLxml.kind] = semantics_js_1.CHTMLxml,
    _a[TeXAtom_js_1.CHTMLTeXAtom.kind] = TeXAtom_js_1.CHTMLTeXAtom,
    _a[TextNode_js_1.CHTMLTextNode.kind] = TextNode_js_1.CHTMLTextNode,
    _a[Wrapper_js_1.CHTMLWrapper.kind] = Wrapper_js_1.CHTMLWrapper,
    _a);
//# sourceMappingURL=Wrappers.js.map