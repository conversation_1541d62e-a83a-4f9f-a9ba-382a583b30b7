import { PropertyList } from '../../Tree/Node.js';
import { AbstractMmlTokenNode, AbstractMmlNode, AttributeList } from '../MmlNode.js';
export declare class MmlMi extends AbstractMmlTokenNode {
    static defaults: PropertyList;
    static operatorName: RegExp;
    static singleCharacter: RegExp;
    protected texclass: number;
    get kind(): string;
    setInheritedAttributes(attributes?: AttributeList, display?: boolean, level?: number, prime?: boolean): void;
    setTeXclass(prev: AbstractMmlNode): this;
}
