{"version": 3, "file": "Notation.js", "sourceRoot": "", "sources": ["../../../ts/output/chtml/Notation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,8DAAkD;AAClD,wDAAsC;AAe/B,IAAM,aAAa,GAAG,UAAkB,IAAY,EAAE,MAAmB;IAAnB,uBAAA,EAAA,WAAmB;IAC9E,OAAO,CAAC,UAAC,IAAI,EAAE,MAAM;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QAC1D,IAAI,MAAM,EAAE;YACV,IAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,IAAI,CAAC,EAAG;gBAC/C,IAAM,SAAS,GAAG,mBAAY,MAAM,cAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,MAAG,CAAC;gBAC3E,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;aACtD;SACF;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC,CAAiD,CAAC;AACrD,CAAC,CAAC;AAZW,QAAA,aAAa,iBAYxB;AAMK,IAAM,MAAM,GAAG,UAAkB,IAAmB;IACzD,OAAO,QAAQ,CAAC,YAAY,CAA4B,UAAC,IAAI,EAAE,KAAK;QAClE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,MAAM,UAIjB;AASK,IAAM,OAAO,GAAG,UAAkB,IAAY,EAAE,KAAoB,EAAE,KAAoB;IAC/F,OAAO,QAAQ,CAAC,aAAa,CAA4B,UAAC,IAAI,EAAE,KAAK;QACnE,IAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACzB,CAAC,CAAC;AANW,QAAA,OAAO,WAMlB;AAOK,IAAM,cAAc,GAAG,UAAkB,IAAY,EAAE,GAAW;IACvE,OAAO,QAAQ,CAAC,oBAAoB,CAA4B,UAAC,KAAa,IAAK,OAAA,UAAC,IAAI,EAAE,MAAM;QACxF,IAAA,KAAY,IAAI,CAAC,OAAO,EAAE,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;QAC3B,IAAA,KAAA,OAAS,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAA,EAAhC,CAAC,QAAA,EAAE,CAAC,QAA4B,CAAC;QACxC,IAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAC,KAAK,EAAE;gBACxD,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjB,SAAS,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,KAAK;aAC7E,EAAC,CAAC,CAAC,CAAC;QACL,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC1C,CAAC,EATkF,CASlF,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB;AAMK,IAAM,aAAa,GAAG,UAAkB,IAAY;IACzD,OAAO,QAAQ,CAAC,mBAAmB,CAA4B,UAAC,IAAI,EAAE,KAAK;QACzE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,aAAa,iBAIxB;AAMK,IAAM,KAAK,GAAG,UAAkB,IAAY;IACjD,OAAO,QAAQ,CAAC,WAAW,CAA4B,UAAC,IAAI,EAAE,KAAK;QACjE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,KAAK,SAIhB"}