{"version": 3, "file": "mroot.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mroot.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,uCAAsC;AACtC,2DAAkF;AAElF,oEAAiE;AAUjE;IAAyC,8BAA8C;IAAvF;;IAoBA,CAAC;IAVW,4BAAO,GAAjB,UAAkB,IAAO,EAAE,IAA2B,EAAE,IAAU,EAAE,CAAS;QAC3E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACb,IAAA,KAAA,OAAa,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,IAAA,EAAvC,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAA+B,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,EAAE,EAAE;YACN,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAM,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACvF;IACH,CAAC;IAba,eAAI,GAAG,mBAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;IAe/C,iBAAC;CAAA,AApBD,CAAyC,IAAA,2BAAgB,EAAmB,qBAAU,CAAC,GAoBtF;AApBY,gCAAU"}