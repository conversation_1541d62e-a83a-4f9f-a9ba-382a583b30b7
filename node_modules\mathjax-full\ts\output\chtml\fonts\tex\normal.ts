/*************************************************************
 *
 *  Copyright (c) 2018-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
import {CHTMLCharMap, AddCSS} from '../../FontData.js';
import {normal as font} from '../../../common/fonts/tex/normal.js';

export const normal: CHTMLCharMap = AddCSS(font, {
    0xA3: {f: 'MI'},
    0xA5: {f: 'A'},
    0xAE: {f: 'A'},
    0xB7: {c: '\\22C5'},
    0xF0: {f: 'A'},
    0x2B9: {c: '\\2032'},
    0x391: {c: 'A'},
    0x392: {c: 'B'},
    0x395: {c: 'E'},
    0x396: {c: 'Z'},
    0x397: {c: 'H'},
    0x399: {c: 'I'},
    0x39A: {c: 'K'},
    0x39C: {c: 'M'},
    0x39D: {c: 'N'},
    0x39F: {c: 'O'},
    0x3A1: {c: 'P'},
    0x3A4: {c: 'T'},
    0x3A7: {c: 'X'},
    0x2000: {c: ''},
    0x2001: {c: ''},
    0x2002: {c: ''},
    0x2003: {c: ''},
    0x2004: {c: ''},
    0x2005: {c: ''},
    0x2006: {c: ''},
    0x2009: {c: ''},
    0x200A: {c: ''},
    0x200B: {c: ''},
    0x200C: {c: ''},
    0x2015: {c: '\\2014'},
    0x2016: {c: '\\2225'},
    0x2017: {c: '_'},
    0x2022: {c: '\\2219'},
    0x2033: {c: '\\2032\\2032'},
    0x2034: {c: '\\2032\\2032\\2032'},
    0x2035: {f: 'A'},
    0x2036: {c: '\\2035\\2035', f: 'A'},
    0x2037: {c: '\\2035\\2035\\2035', f: 'A'},
    0x203E: {c: '\\2C9'},
    0x2044: {c: '/'},
    0x2057: {c: '\\2032\\2032\\2032\\2032'},
    0x2060: {c: ''},
    0x2061: {c: ''},
    0x2062: {c: ''},
    0x2063: {c: ''},
    0x2064: {c: ''},
    0x20D7: {c: '\\2192', f: 'V'},
    0x2102: {c: 'C', f: 'A'},
    0x210B: {c: 'H', f: 'SC'},
    0x210C: {c: 'H', f: 'FR'},
    0x210D: {c: 'H', f: 'A'},
    0x210E: {c: 'h', f: 'I'},
    0x210F: {f: 'A'},
    0x2110: {c: 'I', f: 'SC'},
    0x2111: {c: 'I', f: 'FR'},
    0x2112: {c: 'L', f: 'SC'},
    0x2115: {c: 'N', f: 'A'},
    0x2119: {c: 'P', f: 'A'},
    0x211A: {c: 'Q', f: 'A'},
    0x211B: {c: 'R', f: 'SC'},
    0x211C: {c: 'R', f: 'FR'},
    0x211D: {c: 'R', f: 'A'},
    0x2124: {c: 'Z', f: 'A'},
    0x2126: {c: '\\3A9'},
    0x2127: {f: 'A'},
    0x2128: {c: 'Z', f: 'FR'},
    0x212C: {c: 'B', f: 'SC'},
    0x212D: {c: 'C', f: 'FR'},
    0x2130: {c: 'E', f: 'SC'},
    0x2131: {c: 'F', f: 'SC'},
    0x2132: {f: 'A'},
    0x2133: {c: 'M', f: 'SC'},
    0x2136: {f: 'A'},
    0x2137: {f: 'A'},
    0x2138: {f: 'A'},
    0x2141: {f: 'A'},
    0x219A: {f: 'A'},
    0x219B: {f: 'A'},
    0x219E: {f: 'A'},
    0x21A0: {f: 'A'},
    0x21A2: {f: 'A'},
    0x21A3: {f: 'A'},
    0x21AB: {f: 'A'},
    0x21AC: {f: 'A'},
    0x21AD: {f: 'A'},
    0x21AE: {f: 'A'},
    0x21B0: {f: 'A'},
    0x21B1: {f: 'A'},
    0x21B6: {f: 'A'},
    0x21B7: {f: 'A'},
    0x21BA: {f: 'A'},
    0x21BB: {f: 'A'},
    0x21BE: {f: 'A'},
    0x21BF: {f: 'A'},
    0x21C2: {f: 'A'},
    0x21C3: {f: 'A'},
    0x21C4: {f: 'A'},
    0x21C6: {f: 'A'},
    0x21C7: {f: 'A'},
    0x21C8: {f: 'A'},
    0x21C9: {f: 'A'},
    0x21CA: {f: 'A'},
    0x21CB: {f: 'A'},
    0x21CD: {f: 'A'},
    0x21CE: {f: 'A'},
    0x21CF: {f: 'A'},
    0x21DA: {f: 'A'},
    0x21DB: {f: 'A'},
    0x21DD: {f: 'A'},
    0x21E0: {f: 'A'},
    0x21E2: {f: 'A'},
    0x2201: {f: 'A'},
    0x2204: {c: '\\2203\\338'},
    0x2206: {c: '\\394'},
    0x220C: {c: '\\220B\\338'},
    0x220D: {f: 'A'},
    0x220F: {f: 'S1'},
    0x2210: {f: 'S1'},
    0x2211: {f: 'S1'},
    0x2214: {f: 'A'},
    0x2221: {f: 'A'},
    0x2222: {f: 'A'},
    0x2224: {f: 'A'},
    0x2226: {f: 'A'},
    0x222C: {f: 'S1'},
    0x222D: {f: 'S1'},
    0x222E: {f: 'S1'},
    0x2234: {f: 'A'},
    0x2235: {f: 'A'},
    0x223D: {f: 'A'},
    0x2241: {f: 'A'},
    0x2242: {f: 'A'},
    0x2244: {c: '\\2243\\338'},
    0x2247: {c: '\\2246', f: 'A'},
    0x2249: {c: '\\2248\\338'},
    0x224A: {f: 'A'},
    0x224E: {f: 'A'},
    0x224F: {f: 'A'},
    0x2251: {f: 'A'},
    0x2252: {f: 'A'},
    0x2253: {f: 'A'},
    0x2256: {f: 'A'},
    0x2257: {f: 'A'},
    0x225C: {f: 'A'},
    0x2262: {c: '\\2261\\338'},
    0x2266: {f: 'A'},
    0x2267: {f: 'A'},
    0x2268: {f: 'A'},
    0x2269: {f: 'A'},
    0x226C: {f: 'A'},
    0x226D: {c: '\\224D\\338'},
    0x226E: {f: 'A'},
    0x226F: {f: 'A'},
    0x2270: {f: 'A'},
    0x2271: {f: 'A'},
    0x2272: {f: 'A'},
    0x2273: {f: 'A'},
    0x2274: {c: '\\2272\\338'},
    0x2275: {c: '\\2273\\338'},
    0x2276: {f: 'A'},
    0x2277: {f: 'A'},
    0x2278: {c: '\\2276\\338'},
    0x2279: {c: '\\2277\\338'},
    0x227C: {f: 'A'},
    0x227D: {f: 'A'},
    0x227E: {f: 'A'},
    0x227F: {f: 'A'},
    0x2280: {f: 'A'},
    0x2281: {f: 'A'},
    0x2284: {c: '\\2282\\338'},
    0x2285: {c: '\\2283\\338'},
    0x2288: {f: 'A'},
    0x2289: {f: 'A'},
    0x228A: {f: 'A'},
    0x228B: {f: 'A'},
    0x228F: {f: 'A'},
    0x2290: {f: 'A'},
    0x229A: {f: 'A'},
    0x229B: {f: 'A'},
    0x229D: {f: 'A'},
    0x229E: {f: 'A'},
    0x229F: {f: 'A'},
    0x22A0: {f: 'A'},
    0x22A1: {f: 'A'},
    0x22A9: {f: 'A'},
    0x22AA: {f: 'A'},
    0x22AC: {f: 'A'},
    0x22AD: {f: 'A'},
    0x22AE: {f: 'A'},
    0x22AF: {f: 'A'},
    0x22B2: {f: 'A'},
    0x22B3: {f: 'A'},
    0x22B4: {f: 'A'},
    0x22B5: {f: 'A'},
    0x22B8: {f: 'A'},
    0x22BA: {f: 'A'},
    0x22BB: {f: 'A'},
    0x22BC: {f: 'A'},
    0x22C0: {f: 'S1'},
    0x22C1: {f: 'S1'},
    0x22C2: {f: 'S1'},
    0x22C3: {f: 'S1'},
    0x22C7: {f: 'A'},
    0x22C9: {f: 'A'},
    0x22CA: {f: 'A'},
    0x22CB: {f: 'A'},
    0x22CC: {f: 'A'},
    0x22CD: {f: 'A'},
    0x22CE: {f: 'A'},
    0x22CF: {f: 'A'},
    0x22D0: {f: 'A'},
    0x22D1: {f: 'A'},
    0x22D2: {f: 'A'},
    0x22D3: {f: 'A'},
    0x22D4: {f: 'A'},
    0x22D6: {f: 'A'},
    0x22D7: {f: 'A'},
    0x22D8: {f: 'A'},
    0x22D9: {f: 'A'},
    0x22DA: {f: 'A'},
    0x22DB: {f: 'A'},
    0x22DE: {f: 'A'},
    0x22DF: {f: 'A'},
    0x22E0: {f: 'A'},
    0x22E1: {f: 'A'},
    0x22E2: {c: '\\2291\\338'},
    0x22E3: {c: '\\2292\\338'},
    0x22E6: {f: 'A'},
    0x22E7: {f: 'A'},
    0x22E8: {f: 'A'},
    0x22E9: {f: 'A'},
    0x22EA: {f: 'A'},
    0x22EB: {f: 'A'},
    0x22EC: {f: 'A'},
    0x22ED: {f: 'A'},
    0x2305: {c: '\\22BC', f: 'A'},
    0x2306: {c: '\\2A5E', f: 'A'},
    0x231C: {c: '\\250C', f: 'A'},
    0x231D: {c: '\\2510', f: 'A'},
    0x231E: {c: '\\2514', f: 'A'},
    0x231F: {c: '\\2518', f: 'A'},
    0x2329: {c: '\\27E8'},
    0x232A: {c: '\\27E9'},
    0x23D0: {f: 'S1'},
    0x24C8: {f: 'A'},
    0x250C: {f: 'A'},
    0x2510: {f: 'A'},
    0x2514: {f: 'A'},
    0x2518: {f: 'A'},
    0x2571: {f: 'A'},
    0x2572: {f: 'A'},
    0x25A0: {f: 'A'},
    0x25A1: {f: 'A'},
    0x25AA: {c: '\\25A0', f: 'A'},
    0x25B2: {f: 'A'},
    0x25B4: {c: '\\25B2', f: 'A'},
    0x25B5: {c: '\\25B3'},
    0x25B6: {f: 'A'},
    0x25B8: {c: '\\25B6', f: 'A'},
    0x25BC: {f: 'A'},
    0x25BE: {c: '\\25BC', f: 'A'},
    0x25BF: {c: '\\25BD'},
    0x25C0: {f: 'A'},
    0x25C2: {c: '\\25C0', f: 'A'},
    0x25CA: {f: 'A'},
    0x25FB: {c: '\\25A1', f: 'A'},
    0x25FC: {c: '\\25A0', f: 'A'},
    0x2605: {f: 'A'},
    0x2713: {f: 'A'},
    0x2720: {f: 'A'},
    0x2758: {c: '\\2223'},
    0x29EB: {f: 'A'},
    0x29F8: {c: '/', f: 'I'},
    0x2A00: {f: 'S1'},
    0x2A01: {f: 'S1'},
    0x2A02: {f: 'S1'},
    0x2A04: {f: 'S1'},
    0x2A06: {f: 'S1'},
    0x2A0C: {c: '\\222C\\222C', f: 'S1'},
    0x2A2F: {c: '\\D7'},
    0x2A5E: {f: 'A'},
    0x2A7D: {f: 'A'},
    0x2A7E: {f: 'A'},
    0x2A85: {f: 'A'},
    0x2A86: {f: 'A'},
    0x2A87: {f: 'A'},
    0x2A88: {f: 'A'},
    0x2A89: {f: 'A'},
    0x2A8A: {f: 'A'},
    0x2A8B: {f: 'A'},
    0x2A8C: {f: 'A'},
    0x2A95: {f: 'A'},
    0x2A96: {f: 'A'},
    0x2AB5: {f: 'A'},
    0x2AB6: {f: 'A'},
    0x2AB7: {f: 'A'},
    0x2AB8: {f: 'A'},
    0x2AB9: {f: 'A'},
    0x2ABA: {f: 'A'},
    0x2AC5: {f: 'A'},
    0x2AC6: {f: 'A'},
    0x2ACB: {f: 'A'},
    0x2ACC: {f: 'A'},
    0x3008: {c: '\\27E8'},
    0x3009: {c: '\\27E9'},
    0xE006: {f: 'A'},
    0xE007: {f: 'A'},
    0xE008: {f: 'A'},
    0xE009: {f: 'A'},
    0xE00C: {f: 'A'},
    0xE00D: {f: 'A'},
    0xE00E: {f: 'A'},
    0xE00F: {f: 'A'},
    0xE010: {f: 'A'},
    0xE011: {f: 'A'},
    0xE016: {f: 'A'},
    0xE017: {f: 'A'},
    0xE018: {f: 'A'},
    0xE019: {f: 'A'},
    0xE01A: {f: 'A'},
    0xE01B: {f: 'A'},
    0x1D400: {c: 'A', f: 'B'},
    0x1D401: {c: 'B', f: 'B'},
    0x1D402: {c: 'C', f: 'B'},
    0x1D403: {c: 'D', f: 'B'},
    0x1D404: {c: 'E', f: 'B'},
    0x1D405: {c: 'F', f: 'B'},
    0x1D406: {c: 'G', f: 'B'},
    0x1D407: {c: 'H', f: 'B'},
    0x1D408: {c: 'I', f: 'B'},
    0x1D409: {c: 'J', f: 'B'},
    0x1D40A: {c: 'K', f: 'B'},
    0x1D40B: {c: 'L', f: 'B'},
    0x1D40C: {c: 'M', f: 'B'},
    0x1D40D: {c: 'N', f: 'B'},
    0x1D40E: {c: 'O', f: 'B'},
    0x1D40F: {c: 'P', f: 'B'},
    0x1D410: {c: 'Q', f: 'B'},
    0x1D411: {c: 'R', f: 'B'},
    0x1D412: {c: 'S', f: 'B'},
    0x1D413: {c: 'T', f: 'B'},
    0x1D414: {c: 'U', f: 'B'},
    0x1D415: {c: 'V', f: 'B'},
    0x1D416: {c: 'W', f: 'B'},
    0x1D417: {c: 'X', f: 'B'},
    0x1D418: {c: 'Y', f: 'B'},
    0x1D419: {c: 'Z', f: 'B'},
    0x1D41A: {c: 'a', f: 'B'},
    0x1D41B: {c: 'b', f: 'B'},
    0x1D41C: {c: 'c', f: 'B'},
    0x1D41D: {c: 'd', f: 'B'},
    0x1D41E: {c: 'e', f: 'B'},
    0x1D41F: {c: 'f', f: 'B'},
    0x1D420: {c: 'g', f: 'B'},
    0x1D421: {c: 'h', f: 'B'},
    0x1D422: {c: 'i', f: 'B'},
    0x1D423: {c: 'j', f: 'B'},
    0x1D424: {c: 'k', f: 'B'},
    0x1D425: {c: 'l', f: 'B'},
    0x1D426: {c: 'm', f: 'B'},
    0x1D427: {c: 'n', f: 'B'},
    0x1D428: {c: 'o', f: 'B'},
    0x1D429: {c: 'p', f: 'B'},
    0x1D42A: {c: 'q', f: 'B'},
    0x1D42B: {c: 'r', f: 'B'},
    0x1D42C: {c: 's', f: 'B'},
    0x1D42D: {c: 't', f: 'B'},
    0x1D42E: {c: 'u', f: 'B'},
    0x1D42F: {c: 'v', f: 'B'},
    0x1D430: {c: 'w', f: 'B'},
    0x1D431: {c: 'x', f: 'B'},
    0x1D432: {c: 'y', f: 'B'},
    0x1D433: {c: 'z', f: 'B'},
    0x1D434: {c: 'A', f: 'I'},
    0x1D435: {c: 'B', f: 'I'},
    0x1D436: {c: 'C', f: 'I'},
    0x1D437: {c: 'D', f: 'I'},
    0x1D438: {c: 'E', f: 'I'},
    0x1D439: {c: 'F', f: 'I'},
    0x1D43A: {c: 'G', f: 'I'},
    0x1D43B: {c: 'H', f: 'I'},
    0x1D43C: {c: 'I', f: 'I'},
    0x1D43D: {c: 'J', f: 'I'},
    0x1D43E: {c: 'K', f: 'I'},
    0x1D43F: {c: 'L', f: 'I'},
    0x1D440: {c: 'M', f: 'I'},
    0x1D441: {c: 'N', f: 'I'},
    0x1D442: {c: 'O', f: 'I'},
    0x1D443: {c: 'P', f: 'I'},
    0x1D444: {c: 'Q', f: 'I'},
    0x1D445: {c: 'R', f: 'I'},
    0x1D446: {c: 'S', f: 'I'},
    0x1D447: {c: 'T', f: 'I'},
    0x1D448: {c: 'U', f: 'I'},
    0x1D449: {c: 'V', f: 'I'},
    0x1D44A: {c: 'W', f: 'I'},
    0x1D44B: {c: 'X', f: 'I'},
    0x1D44C: {c: 'Y', f: 'I'},
    0x1D44D: {c: 'Z', f: 'I'},
    0x1D44E: {c: 'a', f: 'I'},
    0x1D44F: {c: 'b', f: 'I'},
    0x1D450: {c: 'c', f: 'I'},
    0x1D451: {c: 'd', f: 'I'},
    0x1D452: {c: 'e', f: 'I'},
    0x1D453: {c: 'f', f: 'I'},
    0x1D454: {c: 'g', f: 'I'},
    0x1D456: {c: 'i', f: 'I'},
    0x1D457: {c: 'j', f: 'I'},
    0x1D458: {c: 'k', f: 'I'},
    0x1D459: {c: 'l', f: 'I'},
    0x1D45A: {c: 'm', f: 'I'},
    0x1D45B: {c: 'n', f: 'I'},
    0x1D45C: {c: 'o', f: 'I'},
    0x1D45D: {c: 'p', f: 'I'},
    0x1D45E: {c: 'q', f: 'I'},
    0x1D45F: {c: 'r', f: 'I'},
    0x1D460: {c: 's', f: 'I'},
    0x1D461: {c: 't', f: 'I'},
    0x1D462: {c: 'u', f: 'I'},
    0x1D463: {c: 'v', f: 'I'},
    0x1D464: {c: 'w', f: 'I'},
    0x1D465: {c: 'x', f: 'I'},
    0x1D466: {c: 'y', f: 'I'},
    0x1D467: {c: 'z', f: 'I'},
    0x1D468: {c: 'A', f: 'BI'},
    0x1D469: {c: 'B', f: 'BI'},
    0x1D46A: {c: 'C', f: 'BI'},
    0x1D46B: {c: 'D', f: 'BI'},
    0x1D46C: {c: 'E', f: 'BI'},
    0x1D46D: {c: 'F', f: 'BI'},
    0x1D46E: {c: 'G', f: 'BI'},
    0x1D46F: {c: 'H', f: 'BI'},
    0x1D470: {c: 'I', f: 'BI'},
    0x1D471: {c: 'J', f: 'BI'},
    0x1D472: {c: 'K', f: 'BI'},
    0x1D473: {c: 'L', f: 'BI'},
    0x1D474: {c: 'M', f: 'BI'},
    0x1D475: {c: 'N', f: 'BI'},
    0x1D476: {c: 'O', f: 'BI'},
    0x1D477: {c: 'P', f: 'BI'},
    0x1D478: {c: 'Q', f: 'BI'},
    0x1D479: {c: 'R', f: 'BI'},
    0x1D47A: {c: 'S', f: 'BI'},
    0x1D47B: {c: 'T', f: 'BI'},
    0x1D47C: {c: 'U', f: 'BI'},
    0x1D47D: {c: 'V', f: 'BI'},
    0x1D47E: {c: 'W', f: 'BI'},
    0x1D47F: {c: 'X', f: 'BI'},
    0x1D480: {c: 'Y', f: 'BI'},
    0x1D481: {c: 'Z', f: 'BI'},
    0x1D482: {c: 'a', f: 'BI'},
    0x1D483: {c: 'b', f: 'BI'},
    0x1D484: {c: 'c', f: 'BI'},
    0x1D485: {c: 'd', f: 'BI'},
    0x1D486: {c: 'e', f: 'BI'},
    0x1D487: {c: 'f', f: 'BI'},
    0x1D488: {c: 'g', f: 'BI'},
    0x1D489: {c: 'h', f: 'BI'},
    0x1D48A: {c: 'i', f: 'BI'},
    0x1D48B: {c: 'j', f: 'BI'},
    0x1D48C: {c: 'k', f: 'BI'},
    0x1D48D: {c: 'l', f: 'BI'},
    0x1D48E: {c: 'm', f: 'BI'},
    0x1D48F: {c: 'n', f: 'BI'},
    0x1D490: {c: 'o', f: 'BI'},
    0x1D491: {c: 'p', f: 'BI'},
    0x1D492: {c: 'q', f: 'BI'},
    0x1D493: {c: 'r', f: 'BI'},
    0x1D494: {c: 's', f: 'BI'},
    0x1D495: {c: 't', f: 'BI'},
    0x1D496: {c: 'u', f: 'BI'},
    0x1D497: {c: 'v', f: 'BI'},
    0x1D498: {c: 'w', f: 'BI'},
    0x1D499: {c: 'x', f: 'BI'},
    0x1D49A: {c: 'y', f: 'BI'},
    0x1D49B: {c: 'z', f: 'BI'},
    0x1D49C: {c: 'A', f: 'SC'},
    0x1D49E: {c: 'C', f: 'SC'},
    0x1D49F: {c: 'D', f: 'SC'},
    0x1D4A2: {c: 'G', f: 'SC'},
    0x1D4A5: {c: 'J', f: 'SC'},
    0x1D4A6: {c: 'K', f: 'SC'},
    0x1D4A9: {c: 'N', f: 'SC'},
    0x1D4AA: {c: 'O', f: 'SC'},
    0x1D4AB: {c: 'P', f: 'SC'},
    0x1D4AC: {c: 'Q', f: 'SC'},
    0x1D4AE: {c: 'S', f: 'SC'},
    0x1D4AF: {c: 'T', f: 'SC'},
    0x1D4B0: {c: 'U', f: 'SC'},
    0x1D4B1: {c: 'V', f: 'SC'},
    0x1D4B2: {c: 'W', f: 'SC'},
    0x1D4B3: {c: 'X', f: 'SC'},
    0x1D4B4: {c: 'Y', f: 'SC'},
    0x1D4B5: {c: 'Z', f: 'SC'},
    0x1D504: {c: 'A', f: 'FR'},
    0x1D505: {c: 'B', f: 'FR'},
    0x1D507: {c: 'D', f: 'FR'},
    0x1D508: {c: 'E', f: 'FR'},
    0x1D509: {c: 'F', f: 'FR'},
    0x1D50A: {c: 'G', f: 'FR'},
    0x1D50D: {c: 'J', f: 'FR'},
    0x1D50E: {c: 'K', f: 'FR'},
    0x1D50F: {c: 'L', f: 'FR'},
    0x1D510: {c: 'M', f: 'FR'},
    0x1D511: {c: 'N', f: 'FR'},
    0x1D512: {c: 'O', f: 'FR'},
    0x1D513: {c: 'P', f: 'FR'},
    0x1D514: {c: 'Q', f: 'FR'},
    0x1D516: {c: 'S', f: 'FR'},
    0x1D517: {c: 'T', f: 'FR'},
    0x1D518: {c: 'U', f: 'FR'},
    0x1D519: {c: 'V', f: 'FR'},
    0x1D51A: {c: 'W', f: 'FR'},
    0x1D51B: {c: 'X', f: 'FR'},
    0x1D51C: {c: 'Y', f: 'FR'},
    0x1D51E: {c: 'a', f: 'FR'},
    0x1D51F: {c: 'b', f: 'FR'},
    0x1D520: {c: 'c', f: 'FR'},
    0x1D521: {c: 'd', f: 'FR'},
    0x1D522: {c: 'e', f: 'FR'},
    0x1D523: {c: 'f', f: 'FR'},
    0x1D524: {c: 'g', f: 'FR'},
    0x1D525: {c: 'h', f: 'FR'},
    0x1D526: {c: 'i', f: 'FR'},
    0x1D527: {c: 'j', f: 'FR'},
    0x1D528: {c: 'k', f: 'FR'},
    0x1D529: {c: 'l', f: 'FR'},
    0x1D52A: {c: 'm', f: 'FR'},
    0x1D52B: {c: 'n', f: 'FR'},
    0x1D52C: {c: 'o', f: 'FR'},
    0x1D52D: {c: 'p', f: 'FR'},
    0x1D52E: {c: 'q', f: 'FR'},
    0x1D52F: {c: 'r', f: 'FR'},
    0x1D530: {c: 's', f: 'FR'},
    0x1D531: {c: 't', f: 'FR'},
    0x1D532: {c: 'u', f: 'FR'},
    0x1D533: {c: 'v', f: 'FR'},
    0x1D534: {c: 'w', f: 'FR'},
    0x1D535: {c: 'x', f: 'FR'},
    0x1D536: {c: 'y', f: 'FR'},
    0x1D537: {c: 'z', f: 'FR'},
    0x1D538: {c: 'A', f: 'A'},
    0x1D539: {c: 'B', f: 'A'},
    0x1D53B: {c: 'D', f: 'A'},
    0x1D53C: {c: 'E', f: 'A'},
    0x1D53D: {c: 'F', f: 'A'},
    0x1D53E: {c: 'G', f: 'A'},
    0x1D540: {c: 'I', f: 'A'},
    0x1D541: {c: 'J', f: 'A'},
    0x1D542: {c: 'K', f: 'A'},
    0x1D543: {c: 'L', f: 'A'},
    0x1D544: {c: 'M', f: 'A'},
    0x1D546: {c: 'O', f: 'A'},
    0x1D54A: {c: 'S', f: 'A'},
    0x1D54B: {c: 'T', f: 'A'},
    0x1D54C: {c: 'U', f: 'A'},
    0x1D54D: {c: 'V', f: 'A'},
    0x1D54E: {c: 'W', f: 'A'},
    0x1D54F: {c: 'X', f: 'A'},
    0x1D550: {c: 'Y', f: 'A'},
    0x1D56C: {c: 'A', f: 'FRB'},
    0x1D56D: {c: 'B', f: 'FRB'},
    0x1D56E: {c: 'C', f: 'FRB'},
    0x1D56F: {c: 'D', f: 'FRB'},
    0x1D570: {c: 'E', f: 'FRB'},
    0x1D571: {c: 'F', f: 'FRB'},
    0x1D572: {c: 'G', f: 'FRB'},
    0x1D573: {c: 'H', f: 'FRB'},
    0x1D574: {c: 'I', f: 'FRB'},
    0x1D575: {c: 'J', f: 'FRB'},
    0x1D576: {c: 'K', f: 'FRB'},
    0x1D577: {c: 'L', f: 'FRB'},
    0x1D578: {c: 'M', f: 'FRB'},
    0x1D579: {c: 'N', f: 'FRB'},
    0x1D57A: {c: 'O', f: 'FRB'},
    0x1D57B: {c: 'P', f: 'FRB'},
    0x1D57C: {c: 'Q', f: 'FRB'},
    0x1D57D: {c: 'R', f: 'FRB'},
    0x1D57E: {c: 'S', f: 'FRB'},
    0x1D57F: {c: 'T', f: 'FRB'},
    0x1D580: {c: 'U', f: 'FRB'},
    0x1D581: {c: 'V', f: 'FRB'},
    0x1D582: {c: 'W', f: 'FRB'},
    0x1D583: {c: 'X', f: 'FRB'},
    0x1D584: {c: 'Y', f: 'FRB'},
    0x1D585: {c: 'Z', f: 'FRB'},
    0x1D586: {c: 'a', f: 'FRB'},
    0x1D587: {c: 'b', f: 'FRB'},
    0x1D588: {c: 'c', f: 'FRB'},
    0x1D589: {c: 'd', f: 'FRB'},
    0x1D58A: {c: 'e', f: 'FRB'},
    0x1D58B: {c: 'f', f: 'FRB'},
    0x1D58C: {c: 'g', f: 'FRB'},
    0x1D58D: {c: 'h', f: 'FRB'},
    0x1D58E: {c: 'i', f: 'FRB'},
    0x1D58F: {c: 'j', f: 'FRB'},
    0x1D590: {c: 'k', f: 'FRB'},
    0x1D591: {c: 'l', f: 'FRB'},
    0x1D592: {c: 'm', f: 'FRB'},
    0x1D593: {c: 'n', f: 'FRB'},
    0x1D594: {c: 'o', f: 'FRB'},
    0x1D595: {c: 'p', f: 'FRB'},
    0x1D596: {c: 'q', f: 'FRB'},
    0x1D597: {c: 'r', f: 'FRB'},
    0x1D598: {c: 's', f: 'FRB'},
    0x1D599: {c: 't', f: 'FRB'},
    0x1D59A: {c: 'u', f: 'FRB'},
    0x1D59B: {c: 'v', f: 'FRB'},
    0x1D59C: {c: 'w', f: 'FRB'},
    0x1D59D: {c: 'x', f: 'FRB'},
    0x1D59E: {c: 'y', f: 'FRB'},
    0x1D59F: {c: 'z', f: 'FRB'},
    0x1D5A0: {c: 'A', f: 'SS'},
    0x1D5A1: {c: 'B', f: 'SS'},
    0x1D5A2: {c: 'C', f: 'SS'},
    0x1D5A3: {c: 'D', f: 'SS'},
    0x1D5A4: {c: 'E', f: 'SS'},
    0x1D5A5: {c: 'F', f: 'SS'},
    0x1D5A6: {c: 'G', f: 'SS'},
    0x1D5A7: {c: 'H', f: 'SS'},
    0x1D5A8: {c: 'I', f: 'SS'},
    0x1D5A9: {c: 'J', f: 'SS'},
    0x1D5AA: {c: 'K', f: 'SS'},
    0x1D5AB: {c: 'L', f: 'SS'},
    0x1D5AC: {c: 'M', f: 'SS'},
    0x1D5AD: {c: 'N', f: 'SS'},
    0x1D5AE: {c: 'O', f: 'SS'},
    0x1D5AF: {c: 'P', f: 'SS'},
    0x1D5B0: {c: 'Q', f: 'SS'},
    0x1D5B1: {c: 'R', f: 'SS'},
    0x1D5B2: {c: 'S', f: 'SS'},
    0x1D5B3: {c: 'T', f: 'SS'},
    0x1D5B4: {c: 'U', f: 'SS'},
    0x1D5B5: {c: 'V', f: 'SS'},
    0x1D5B6: {c: 'W', f: 'SS'},
    0x1D5B7: {c: 'X', f: 'SS'},
    0x1D5B8: {c: 'Y', f: 'SS'},
    0x1D5B9: {c: 'Z', f: 'SS'},
    0x1D5BA: {c: 'a', f: 'SS'},
    0x1D5BB: {c: 'b', f: 'SS'},
    0x1D5BC: {c: 'c', f: 'SS'},
    0x1D5BD: {c: 'd', f: 'SS'},
    0x1D5BE: {c: 'e', f: 'SS'},
    0x1D5BF: {c: 'f', f: 'SS'},
    0x1D5C0: {c: 'g', f: 'SS'},
    0x1D5C1: {c: 'h', f: 'SS'},
    0x1D5C2: {c: 'i', f: 'SS'},
    0x1D5C3: {c: 'j', f: 'SS'},
    0x1D5C4: {c: 'k', f: 'SS'},
    0x1D5C5: {c: 'l', f: 'SS'},
    0x1D5C6: {c: 'm', f: 'SS'},
    0x1D5C7: {c: 'n', f: 'SS'},
    0x1D5C8: {c: 'o', f: 'SS'},
    0x1D5C9: {c: 'p', f: 'SS'},
    0x1D5CA: {c: 'q', f: 'SS'},
    0x1D5CB: {c: 'r', f: 'SS'},
    0x1D5CC: {c: 's', f: 'SS'},
    0x1D5CD: {c: 't', f: 'SS'},
    0x1D5CE: {c: 'u', f: 'SS'},
    0x1D5CF: {c: 'v', f: 'SS'},
    0x1D5D0: {c: 'w', f: 'SS'},
    0x1D5D1: {c: 'x', f: 'SS'},
    0x1D5D2: {c: 'y', f: 'SS'},
    0x1D5D3: {c: 'z', f: 'SS'},
    0x1D5D4: {c: 'A', f: 'SSB'},
    0x1D5D5: {c: 'B', f: 'SSB'},
    0x1D5D6: {c: 'C', f: 'SSB'},
    0x1D5D7: {c: 'D', f: 'SSB'},
    0x1D5D8: {c: 'E', f: 'SSB'},
    0x1D5D9: {c: 'F', f: 'SSB'},
    0x1D5DA: {c: 'G', f: 'SSB'},
    0x1D5DB: {c: 'H', f: 'SSB'},
    0x1D5DC: {c: 'I', f: 'SSB'},
    0x1D5DD: {c: 'J', f: 'SSB'},
    0x1D5DE: {c: 'K', f: 'SSB'},
    0x1D5DF: {c: 'L', f: 'SSB'},
    0x1D5E0: {c: 'M', f: 'SSB'},
    0x1D5E1: {c: 'N', f: 'SSB'},
    0x1D5E2: {c: 'O', f: 'SSB'},
    0x1D5E3: {c: 'P', f: 'SSB'},
    0x1D5E4: {c: 'Q', f: 'SSB'},
    0x1D5E5: {c: 'R', f: 'SSB'},
    0x1D5E6: {c: 'S', f: 'SSB'},
    0x1D5E7: {c: 'T', f: 'SSB'},
    0x1D5E8: {c: 'U', f: 'SSB'},
    0x1D5E9: {c: 'V', f: 'SSB'},
    0x1D5EA: {c: 'W', f: 'SSB'},
    0x1D5EB: {c: 'X', f: 'SSB'},
    0x1D5EC: {c: 'Y', f: 'SSB'},
    0x1D5ED: {c: 'Z', f: 'SSB'},
    0x1D5EE: {c: 'a', f: 'SSB'},
    0x1D5EF: {c: 'b', f: 'SSB'},
    0x1D5F0: {c: 'c', f: 'SSB'},
    0x1D5F1: {c: 'd', f: 'SSB'},
    0x1D5F2: {c: 'e', f: 'SSB'},
    0x1D5F3: {c: 'f', f: 'SSB'},
    0x1D5F4: {c: 'g', f: 'SSB'},
    0x1D5F5: {c: 'h', f: 'SSB'},
    0x1D5F6: {c: 'i', f: 'SSB'},
    0x1D5F7: {c: 'j', f: 'SSB'},
    0x1D5F8: {c: 'k', f: 'SSB'},
    0x1D5F9: {c: 'l', f: 'SSB'},
    0x1D5FA: {c: 'm', f: 'SSB'},
    0x1D5FB: {c: 'n', f: 'SSB'},
    0x1D5FC: {c: 'o', f: 'SSB'},
    0x1D5FD: {c: 'p', f: 'SSB'},
    0x1D5FE: {c: 'q', f: 'SSB'},
    0x1D5FF: {c: 'r', f: 'SSB'},
    0x1D600: {c: 's', f: 'SSB'},
    0x1D601: {c: 't', f: 'SSB'},
    0x1D602: {c: 'u', f: 'SSB'},
    0x1D603: {c: 'v', f: 'SSB'},
    0x1D604: {c: 'w', f: 'SSB'},
    0x1D605: {c: 'x', f: 'SSB'},
    0x1D606: {c: 'y', f: 'SSB'},
    0x1D607: {c: 'z', f: 'SSB'},
    0x1D608: {c: 'A', f: 'SSI'},
    0x1D609: {c: 'B', f: 'SSI'},
    0x1D60A: {c: 'C', f: 'SSI'},
    0x1D60B: {c: 'D', f: 'SSI'},
    0x1D60C: {c: 'E', f: 'SSI'},
    0x1D60D: {c: 'F', f: 'SSI'},
    0x1D60E: {c: 'G', f: 'SSI'},
    0x1D60F: {c: 'H', f: 'SSI'},
    0x1D610: {c: 'I', f: 'SSI'},
    0x1D611: {c: 'J', f: 'SSI'},
    0x1D612: {c: 'K', f: 'SSI'},
    0x1D613: {c: 'L', f: 'SSI'},
    0x1D614: {c: 'M', f: 'SSI'},
    0x1D615: {c: 'N', f: 'SSI'},
    0x1D616: {c: 'O', f: 'SSI'},
    0x1D617: {c: 'P', f: 'SSI'},
    0x1D618: {c: 'Q', f: 'SSI'},
    0x1D619: {c: 'R', f: 'SSI'},
    0x1D61A: {c: 'S', f: 'SSI'},
    0x1D61B: {c: 'T', f: 'SSI'},
    0x1D61C: {c: 'U', f: 'SSI'},
    0x1D61D: {c: 'V', f: 'SSI'},
    0x1D61E: {c: 'W', f: 'SSI'},
    0x1D61F: {c: 'X', f: 'SSI'},
    0x1D620: {c: 'Y', f: 'SSI'},
    0x1D621: {c: 'Z', f: 'SSI'},
    0x1D622: {c: 'a', f: 'SSI'},
    0x1D623: {c: 'b', f: 'SSI'},
    0x1D624: {c: 'c', f: 'SSI'},
    0x1D625: {c: 'd', f: 'SSI'},
    0x1D626: {c: 'e', f: 'SSI'},
    0x1D627: {c: 'f', f: 'SSI'},
    0x1D628: {c: 'g', f: 'SSI'},
    0x1D629: {c: 'h', f: 'SSI'},
    0x1D62A: {c: 'i', f: 'SSI'},
    0x1D62B: {c: 'j', f: 'SSI'},
    0x1D62C: {c: 'k', f: 'SSI'},
    0x1D62D: {c: 'l', f: 'SSI'},
    0x1D62E: {c: 'm', f: 'SSI'},
    0x1D62F: {c: 'n', f: 'SSI'},
    0x1D630: {c: 'o', f: 'SSI'},
    0x1D631: {c: 'p', f: 'SSI'},
    0x1D632: {c: 'q', f: 'SSI'},
    0x1D633: {c: 'r', f: 'SSI'},
    0x1D634: {c: 's', f: 'SSI'},
    0x1D635: {c: 't', f: 'SSI'},
    0x1D636: {c: 'u', f: 'SSI'},
    0x1D637: {c: 'v', f: 'SSI'},
    0x1D638: {c: 'w', f: 'SSI'},
    0x1D639: {c: 'x', f: 'SSI'},
    0x1D63A: {c: 'y', f: 'SSI'},
    0x1D63B: {c: 'z', f: 'SSI'},
    0x1D670: {c: 'A', f: 'T'},
    0x1D671: {c: 'B', f: 'T'},
    0x1D672: {c: 'C', f: 'T'},
    0x1D673: {c: 'D', f: 'T'},
    0x1D674: {c: 'E', f: 'T'},
    0x1D675: {c: 'F', f: 'T'},
    0x1D676: {c: 'G', f: 'T'},
    0x1D677: {c: 'H', f: 'T'},
    0x1D678: {c: 'I', f: 'T'},
    0x1D679: {c: 'J', f: 'T'},
    0x1D67A: {c: 'K', f: 'T'},
    0x1D67B: {c: 'L', f: 'T'},
    0x1D67C: {c: 'M', f: 'T'},
    0x1D67D: {c: 'N', f: 'T'},
    0x1D67E: {c: 'O', f: 'T'},
    0x1D67F: {c: 'P', f: 'T'},
    0x1D680: {c: 'Q', f: 'T'},
    0x1D681: {c: 'R', f: 'T'},
    0x1D682: {c: 'S', f: 'T'},
    0x1D683: {c: 'T', f: 'T'},
    0x1D684: {c: 'U', f: 'T'},
    0x1D685: {c: 'V', f: 'T'},
    0x1D686: {c: 'W', f: 'T'},
    0x1D687: {c: 'X', f: 'T'},
    0x1D688: {c: 'Y', f: 'T'},
    0x1D689: {c: 'Z', f: 'T'},
    0x1D68A: {c: 'a', f: 'T'},
    0x1D68B: {c: 'b', f: 'T'},
    0x1D68C: {c: 'c', f: 'T'},
    0x1D68D: {c: 'd', f: 'T'},
    0x1D68E: {c: 'e', f: 'T'},
    0x1D68F: {c: 'f', f: 'T'},
    0x1D690: {c: 'g', f: 'T'},
    0x1D691: {c: 'h', f: 'T'},
    0x1D692: {c: 'i', f: 'T'},
    0x1D693: {c: 'j', f: 'T'},
    0x1D694: {c: 'k', f: 'T'},
    0x1D695: {c: 'l', f: 'T'},
    0x1D696: {c: 'm', f: 'T'},
    0x1D697: {c: 'n', f: 'T'},
    0x1D698: {c: 'o', f: 'T'},
    0x1D699: {c: 'p', f: 'T'},
    0x1D69A: {c: 'q', f: 'T'},
    0x1D69B: {c: 'r', f: 'T'},
    0x1D69C: {c: 's', f: 'T'},
    0x1D69D: {c: 't', f: 'T'},
    0x1D69E: {c: 'u', f: 'T'},
    0x1D69F: {c: 'v', f: 'T'},
    0x1D6A0: {c: 'w', f: 'T'},
    0x1D6A1: {c: 'x', f: 'T'},
    0x1D6A2: {c: 'y', f: 'T'},
    0x1D6A3: {c: 'z', f: 'T'},
    0x1D6A8: {c: 'A', f: 'B'},
    0x1D6A9: {c: 'B', f: 'B'},
    0x1D6AA: {c: '\\393', f: 'B'},
    0x1D6AB: {c: '\\394', f: 'B'},
    0x1D6AC: {c: 'E', f: 'B'},
    0x1D6AD: {c: 'Z', f: 'B'},
    0x1D6AE: {c: 'H', f: 'B'},
    0x1D6AF: {c: '\\398', f: 'B'},
    0x1D6B0: {c: 'I', f: 'B'},
    0x1D6B1: {c: 'K', f: 'B'},
    0x1D6B2: {c: '\\39B', f: 'B'},
    0x1D6B3: {c: 'M', f: 'B'},
    0x1D6B4: {c: 'N', f: 'B'},
    0x1D6B5: {c: '\\39E', f: 'B'},
    0x1D6B6: {c: 'O', f: 'B'},
    0x1D6B7: {c: '\\3A0', f: 'B'},
    0x1D6B8: {c: 'P', f: 'B'},
    0x1D6BA: {c: '\\3A3', f: 'B'},
    0x1D6BB: {c: 'T', f: 'B'},
    0x1D6BC: {c: '\\3A5', f: 'B'},
    0x1D6BD: {c: '\\3A6', f: 'B'},
    0x1D6BE: {c: 'X', f: 'B'},
    0x1D6BF: {c: '\\3A8', f: 'B'},
    0x1D6C0: {c: '\\3A9', f: 'B'},
    0x1D6C1: {c: '\\2207', f: 'B'},
    0x1D6E2: {c: 'A', f: 'I'},
    0x1D6E3: {c: 'B', f: 'I'},
    0x1D6E4: {c: '\\393', f: 'I'},
    0x1D6E5: {c: '\\394', f: 'I'},
    0x1D6E6: {c: 'E', f: 'I'},
    0x1D6E7: {c: 'Z', f: 'I'},
    0x1D6E8: {c: 'H', f: 'I'},
    0x1D6E9: {c: '\\398', f: 'I'},
    0x1D6EA: {c: 'I', f: 'I'},
    0x1D6EB: {c: 'K', f: 'I'},
    0x1D6EC: {c: '\\39B', f: 'I'},
    0x1D6ED: {c: 'M', f: 'I'},
    0x1D6EE: {c: 'N', f: 'I'},
    0x1D6EF: {c: '\\39E', f: 'I'},
    0x1D6F0: {c: 'O', f: 'I'},
    0x1D6F1: {c: '\\3A0', f: 'I'},
    0x1D6F2: {c: 'P', f: 'I'},
    0x1D6F4: {c: '\\3A3', f: 'I'},
    0x1D6F5: {c: 'T', f: 'I'},
    0x1D6F6: {c: '\\3A5', f: 'I'},
    0x1D6F7: {c: '\\3A6', f: 'I'},
    0x1D6F8: {c: 'X', f: 'I'},
    0x1D6F9: {c: '\\3A8', f: 'I'},
    0x1D6FA: {c: '\\3A9', f: 'I'},
    0x1D6FC: {c: '\\3B1', f: 'I'},
    0x1D6FD: {c: '\\3B2', f: 'I'},
    0x1D6FE: {c: '\\3B3', f: 'I'},
    0x1D6FF: {c: '\\3B4', f: 'I'},
    0x1D700: {c: '\\3B5', f: 'I'},
    0x1D701: {c: '\\3B6', f: 'I'},
    0x1D702: {c: '\\3B7', f: 'I'},
    0x1D703: {c: '\\3B8', f: 'I'},
    0x1D704: {c: '\\3B9', f: 'I'},
    0x1D705: {c: '\\3BA', f: 'I'},
    0x1D706: {c: '\\3BB', f: 'I'},
    0x1D707: {c: '\\3BC', f: 'I'},
    0x1D708: {c: '\\3BD', f: 'I'},
    0x1D709: {c: '\\3BE', f: 'I'},
    0x1D70A: {c: '\\3BF', f: 'I'},
    0x1D70B: {c: '\\3C0', f: 'I'},
    0x1D70C: {c: '\\3C1', f: 'I'},
    0x1D70D: {c: '\\3C2', f: 'I'},
    0x1D70E: {c: '\\3C3', f: 'I'},
    0x1D70F: {c: '\\3C4', f: 'I'},
    0x1D710: {c: '\\3C5', f: 'I'},
    0x1D711: {c: '\\3C6', f: 'I'},
    0x1D712: {c: '\\3C7', f: 'I'},
    0x1D713: {c: '\\3C8', f: 'I'},
    0x1D714: {c: '\\3C9', f: 'I'},
    0x1D715: {c: '\\2202'},
    0x1D716: {c: '\\3F5', f: 'I'},
    0x1D717: {c: '\\3D1', f: 'I'},
    0x1D718: {c: '\\E009', f: 'A'},
    0x1D719: {c: '\\3D5', f: 'I'},
    0x1D71A: {c: '\\3F1', f: 'I'},
    0x1D71B: {c: '\\3D6', f: 'I'},
    0x1D71C: {c: 'A', f: 'BI'},
    0x1D71D: {c: 'B', f: 'BI'},
    0x1D71E: {c: '\\393', f: 'BI'},
    0x1D71F: {c: '\\394', f: 'BI'},
    0x1D720: {c: 'E', f: 'BI'},
    0x1D721: {c: 'Z', f: 'BI'},
    0x1D722: {c: 'H', f: 'BI'},
    0x1D723: {c: '\\398', f: 'BI'},
    0x1D724: {c: 'I', f: 'BI'},
    0x1D725: {c: 'K', f: 'BI'},
    0x1D726: {c: '\\39B', f: 'BI'},
    0x1D727: {c: 'M', f: 'BI'},
    0x1D728: {c: 'N', f: 'BI'},
    0x1D729: {c: '\\39E', f: 'BI'},
    0x1D72A: {c: 'O', f: 'BI'},
    0x1D72B: {c: '\\3A0', f: 'BI'},
    0x1D72C: {c: 'P', f: 'BI'},
    0x1D72E: {c: '\\3A3', f: 'BI'},
    0x1D72F: {c: 'T', f: 'BI'},
    0x1D730: {c: '\\3A5', f: 'BI'},
    0x1D731: {c: '\\3A6', f: 'BI'},
    0x1D732: {c: 'X', f: 'BI'},
    0x1D733: {c: '\\3A8', f: 'BI'},
    0x1D734: {c: '\\3A9', f: 'BI'},
    0x1D736: {c: '\\3B1', f: 'BI'},
    0x1D737: {c: '\\3B2', f: 'BI'},
    0x1D738: {c: '\\3B3', f: 'BI'},
    0x1D739: {c: '\\3B4', f: 'BI'},
    0x1D73A: {c: '\\3B5', f: 'BI'},
    0x1D73B: {c: '\\3B6', f: 'BI'},
    0x1D73C: {c: '\\3B7', f: 'BI'},
    0x1D73D: {c: '\\3B8', f: 'BI'},
    0x1D73E: {c: '\\3B9', f: 'BI'},
    0x1D73F: {c: '\\3BA', f: 'BI'},
    0x1D740: {c: '\\3BB', f: 'BI'},
    0x1D741: {c: '\\3BC', f: 'BI'},
    0x1D742: {c: '\\3BD', f: 'BI'},
    0x1D743: {c: '\\3BE', f: 'BI'},
    0x1D744: {c: '\\3BF', f: 'BI'},
    0x1D745: {c: '\\3C0', f: 'BI'},
    0x1D746: {c: '\\3C1', f: 'BI'},
    0x1D747: {c: '\\3C2', f: 'BI'},
    0x1D748: {c: '\\3C3', f: 'BI'},
    0x1D749: {c: '\\3C4', f: 'BI'},
    0x1D74A: {c: '\\3C5', f: 'BI'},
    0x1D74B: {c: '\\3C6', f: 'BI'},
    0x1D74C: {c: '\\3C7', f: 'BI'},
    0x1D74D: {c: '\\3C8', f: 'BI'},
    0x1D74E: {c: '\\3C9', f: 'BI'},
    0x1D74F: {c: '\\2202', f: 'B'},
    0x1D750: {c: '\\3F5', f: 'BI'},
    0x1D751: {c: '\\3D1', f: 'BI'},
    0x1D752: {c: '\\E009', f: 'A'},
    0x1D753: {c: '\\3D5', f: 'BI'},
    0x1D754: {c: '\\3F1', f: 'BI'},
    0x1D755: {c: '\\3D6', f: 'BI'},
    0x1D756: {c: 'A', f: 'SSB'},
    0x1D757: {c: 'B', f: 'SSB'},
    0x1D758: {c: '\\393', f: 'SSB'},
    0x1D759: {c: '\\394', f: 'SSB'},
    0x1D75A: {c: 'E', f: 'SSB'},
    0x1D75B: {c: 'Z', f: 'SSB'},
    0x1D75C: {c: 'H', f: 'SSB'},
    0x1D75D: {c: '\\398', f: 'SSB'},
    0x1D75E: {c: 'I', f: 'SSB'},
    0x1D75F: {c: 'K', f: 'SSB'},
    0x1D760: {c: '\\39B', f: 'SSB'},
    0x1D761: {c: 'M', f: 'SSB'},
    0x1D762: {c: 'N', f: 'SSB'},
    0x1D763: {c: '\\39E', f: 'SSB'},
    0x1D764: {c: 'O', f: 'SSB'},
    0x1D765: {c: '\\3A0', f: 'SSB'},
    0x1D766: {c: 'P', f: 'SSB'},
    0x1D768: {c: '\\3A3', f: 'SSB'},
    0x1D769: {c: 'T', f: 'SSB'},
    0x1D76A: {c: '\\3A5', f: 'SSB'},
    0x1D76B: {c: '\\3A6', f: 'SSB'},
    0x1D76C: {c: 'X', f: 'SSB'},
    0x1D76D: {c: '\\3A8', f: 'SSB'},
    0x1D76E: {c: '\\3A9', f: 'SSB'},
    0x1D7CE: {c: '0', f: 'B'},
    0x1D7CF: {c: '1', f: 'B'},
    0x1D7D0: {c: '2', f: 'B'},
    0x1D7D1: {c: '3', f: 'B'},
    0x1D7D2: {c: '4', f: 'B'},
    0x1D7D3: {c: '5', f: 'B'},
    0x1D7D4: {c: '6', f: 'B'},
    0x1D7D5: {c: '7', f: 'B'},
    0x1D7D6: {c: '8', f: 'B'},
    0x1D7D7: {c: '9', f: 'B'},
    0x1D7E2: {c: '0', f: 'SS'},
    0x1D7E3: {c: '1', f: 'SS'},
    0x1D7E4: {c: '2', f: 'SS'},
    0x1D7E5: {c: '3', f: 'SS'},
    0x1D7E6: {c: '4', f: 'SS'},
    0x1D7E7: {c: '5', f: 'SS'},
    0x1D7E8: {c: '6', f: 'SS'},
    0x1D7E9: {c: '7', f: 'SS'},
    0x1D7EA: {c: '8', f: 'SS'},
    0x1D7EB: {c: '9', f: 'SS'},
    0x1D7EC: {c: '0', f: 'SSB'},
    0x1D7ED: {c: '1', f: 'SSB'},
    0x1D7EE: {c: '2', f: 'SSB'},
    0x1D7EF: {c: '3', f: 'SSB'},
    0x1D7F0: {c: '4', f: 'SSB'},
    0x1D7F1: {c: '5', f: 'SSB'},
    0x1D7F2: {c: '6', f: 'SSB'},
    0x1D7F3: {c: '7', f: 'SSB'},
    0x1D7F4: {c: '8', f: 'SSB'},
    0x1D7F5: {c: '9', f: 'SSB'},
    0x1D7F6: {c: '0', f: 'T'},
    0x1D7F7: {c: '1', f: 'T'},
    0x1D7F8: {c: '2', f: 'T'},
    0x1D7F9: {c: '3', f: 'T'},
    0x1D7FA: {c: '4', f: 'T'},
    0x1D7FB: {c: '5', f: 'T'},
    0x1D7FC: {c: '6', f: 'T'},
    0x1D7FD: {c: '7', f: 'T'},
    0x1D7FE: {c: '8', f: 'T'},
    0x1D7FF: {c: '9', f: 'T'},
});
