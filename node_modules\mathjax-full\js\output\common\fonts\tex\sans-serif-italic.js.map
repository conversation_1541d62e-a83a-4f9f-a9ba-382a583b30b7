{"version": 3, "file": "sans-serif-italic.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/sans-serif-italic.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,eAAe,GAAyB;IACjD,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACjC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACjC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACvB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;IACtB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAAC,CAAC;IAC9B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;IACnB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAC/B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAC/B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACjC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACvB,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACjC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACjC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACjC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACrC,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACtC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC;IAC1B,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,EAAE,EAAC,CAAC;IAChC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;CAC1B,CAAC"}