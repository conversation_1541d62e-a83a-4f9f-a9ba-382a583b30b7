"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Entities = __importStar(require("../Entities.js"));
Entities.add({
    ENG: '\u014A',
    ETH: '\u00D0',
    Eacute: '\u00C9',
    Ecaron: '\u011A',
    Ecirc: '\u00CA',
    Ecy: '\u042D',
    Edot: '\u0116',
    Egrave: '\u00C8',
    Emacr: '\u0112',
    EmptySmallSquare: '\u25FB',
    EmptyVerySmallSquare: '\u25AB',
    Eogon: '\u0118',
    Epsilon: '\u0395',
    Equal: '\u2A75',
    Esim: '\u2A73',
    Eta: '\u0397',
    Euml: '\u00CB',
    eDDot: '\u2A77',
    eDot: '\u2251',
    eacute: '\u00E9',
    easter: '\u2A6E',
    ecaron: '\u011B',
    ecirc: '\u00EA',
    ecolon: '\u2255',
    ecy: '\u044D',
    edot: '\u0117',
    ee: '\u2147',
    eg: '\u2A9A',
    egrave: '\u00E8',
    egsdot: '\u2A98',
    el: '\u2A99',
    elinters: '\u23E7',
    elsdot: '\u2A97',
    emacr: '\u0113',
    emptyset: '\u2205',
    emptyv: '\u2205',
    emsp: '\u2003',
    emsp13: '\u2004',
    emsp14: '\u2005',
    eng: '\u014B',
    ensp: '\u2002',
    eogon: '\u0119',
    epar: '\u22D5',
    eparsl: '\u29E3',
    eplus: '\u2A71',
    epsilon: '\u03B5',
    eqcirc: '\u2256',
    eqcolon: '\u2255',
    eqsim: '\u2242',
    eqslantgtr: '\u2A96',
    eqslantless: '\u2A95',
    equals: '\u003D',
    equest: '\u225F',
    equiv: '\u2261',
    equivDD: '\u2A78',
    eqvparsl: '\u29E5',
    erarr: '\u2971',
    esdot: '\u2250',
    esim: '\u2242',
    euml: '\u00EB',
    euro: '\u20AC',
    excl: '\u0021',
    exist: '\u2203',
    expectation: '\u2130',
    exponentiale: '\u2147'
}, 'e');
//# sourceMappingURL=e.js.map