{"version": 3, "file": "Notation.js", "sourceRoot": "", "sources": ["../../../ts/output/svg/Notation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,8DAAkD;AAClD,wDAAsC;AA6BzB,QAAA,eAAe,GAAG;IAC7B,GAAG,EAAE,UAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAApB,CAAoB;IAC1C,KAAK,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAArB,CAAqB;IAC5C,MAAM,EAAE,UAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAApB,CAAoB;IAC7C,IAAI,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAb,CAAa;IACpC,QAAQ,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAK,OAAA,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAArB,CAAqB;IAChD,UAAU,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAhC,CAAgC;IAC7D,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAxB,CAAwB;IAC5C,IAAI,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAxB,CAAwB;CAC+B,CAAC;AAUzE,IAAM,QAAQ,GAAG,UAAS,IAAc,EAAE,IAAc,EAAE,MAAmB;IAAnB,uBAAA,EAAA,WAAmB;IAC5E,IAAA,KAAY,IAAI,CAAC,OAAO,EAAE,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;IACjC,IAAM,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IAC7B,OAAO,IAAA,kBAAU,EAAC,uBAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACrE,CAAC,CAAC;AAJW,QAAA,QAAQ,YAInB;AASK,IAAM,UAAU,GAAG,UAAS,IAAc,EAAE,IAAc,EAAE,MAAc;IAC/E,IAAI,MAAM,EAAE;QACV,IAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE;YACL,IAAI,MAAM,KAAK,GAAG,EAAE;gBAClB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACb,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACd;iBAAM;gBACL,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACb,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACd;SACF;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAdW,QAAA,UAAU,cAcrB;AASK,IAAM,UAAU,GAAG,UAAkB,IAAc,EAAE,MAAmB;IAAnB,uBAAA,EAAA,WAAmB;IAC7E,OAAO,CAAC,UAAC,IAAI,EAAE,MAAM;QACnB,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AALW,QAAA,UAAU,cAKrB;AAQK,IAAM,MAAM,GAAG,UAAkB,IAAmB;IACzD,OAAO,QAAQ,CAAC,YAAY,CAA0B,UAAC,IAAI,EAAE,MAAM;QACjE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,MAAM,UAIjB;AASK,IAAM,OAAO,GAAG,UAAkB,IAAY,EAAE,KAAoB,EAAE,KAAoB;IAC/F,OAAO,QAAQ,CAAC,aAAa,CAA0B,UAAC,IAAI,EAAE,MAAM;QAClE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACzB,CAAC,CAAC;AALW,QAAA,OAAO,WAKlB;AAQK,IAAM,cAAc,GAAG,UAAkB,IAAc;IAC5D,OAAO,QAAQ,CAAC,oBAAoB,CAA0B,UAAC,MAAc,IAAK,OAAA,UAAC,IAAI,EAAE,MAAM;QAC7F,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,EAFiF,CAEjF,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,cAAc,kBAIzB;AAQK,IAAM,aAAa,GAAG,UAAkB,IAAY;IACzD,OAAO,QAAQ,CAAC,mBAAmB,CAA0B,UAAC,IAAI,EAAE,KAAK;QACvE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,aAAa,iBAIxB;AAMK,IAAM,KAAK,GAAG,UAAkB,IAAY;IACjD,OAAO,QAAQ,CAAC,WAAW,CAA0B,UAAC,IAAI,EAAE,KAAK;QAC/D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAJW,QAAA,KAAK,SAIhB"}