{"version": 3, "file": "BaseItems.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/base/BaseItems.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,kDAA4C;AAE5C,yDAAmD;AACnD,+DAA6E;AAE7E,+DAAsC;AACtC,iEAAwC;AACxC,+DAAsC;AAGtC,gDAAwE;AAMxE;IAA+B,6BAAQ;IAKrC,mBAAY,OAAyB,EAAS,MAAe;QAA7D,YACE,kBAAM,OAAO,CAAC,SACf;QAF6C,YAAM,GAAN,MAAM,CAAS;;IAE7D,CAAC;IAMD,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,6BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,6BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACxB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;aACjE;YACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SACnD;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,gBAAC;AAAD,CAAC,AAvCD,CAA+B,uBAAQ,GAuCtC;AAvCY,8BAAS;AA8CtB;IAA8B,4BAAQ;IAAtC;;IAiBA,CAAC;IAZC,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,6BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEH,eAAC;AAAD,CAAC,AAjBD,CAA8B,uBAAQ,GAiBrC;AAjBY,4BAAQ;AAuBrB;IAA8B,4BAAQ;IAAtC;;IAuCA,CAAC;IAxBC,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,4BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,4BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YAExB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACnD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SACnD;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAhCgB,eAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAQ,CAAC,MAAM,CAAC,EAAE;QAEtE,MAAM,EAAE,CAAC,uBAAuB;YACvB,yCAAyC,CAAC;KACpD,CAAC,CAAC;IA6BL,eAAC;CAAA,AAvCD,CAA8B,uBAAQ,GAuCrC;AAvCY,4BAAQ;AA6CrB;IAA+B,6BAAQ;IAAvC;;IAiBA,CAAC;IAZC,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,8BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEH,gBAAC;AAAD,CAAC,AAjBD,CAA+B,uBAAQ,GAiBtC;AAjBY,8BAAS;AAuBtB;IAA+B,6BAAQ;IAAvC;;IAsBA,CAAC;IAjBC,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAKM,6BAAS,GAAhB,UAAiB,IAAe;QAC1B,IAAA,KAAA,OAAe,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,EAA1B,IAAI,QAAA,EAAE,IAAI,QAAgB,CAAC;QAChC,IAAI,CAAC,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YAEtE,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACvD,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SAC7B;QACD,qBAAQ,CAAC,QAAQ,CAAC,IAAI,EAAG,IAAmB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACxD,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACH,gBAAC;AAAD,CAAC,AAtBD,CAA+B,uBAAQ,GAsBtC;AAtBY,8BAAS;AA6BtB;IAAgC,8BAAQ;IAAxC;;IA6DA,CAAC;IAzCC,sBAAW,4BAAI;aAAf;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAKM,8BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC9C,OAAO,uBAAQ,CAAC,OAAO,CAAC;SACzB;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAW,CAAC;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;gBAC9B,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAElB,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAY,CAAC,CAAC;iBAClE;qBAAM;oBAEL,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;oBACjF,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC9F,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;iBACnB;aACF;YACD,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE;gBAE1C,qBAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAa,CAAC,CAAC;aACrF;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC/C,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;SACzB;QACD,IAAI,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAE5B,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3D,WAAU,qBAAQ,YAAR,qBAAQ,yBAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,UAAK,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,cAAE;SAC5D;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAtDgB,iBAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAQ,CAAC,MAAM,CAAC,EAAE;QAEtE,MAAM,EAAE,CAAC,eAAe;YACf,2CAA2C,CAAC;QAErD,KAAK,EAAE,CAAC,mBAAmB;YACnB,oCAAoC,CAAC;QAE7C,KAAK,EAAE,CAAC,mBAAmB;YACnB,kCAAkC,CAAC;KAC5C,CAAC,CAAC;IA8CL,iBAAC;CAAA,AA7DD,CAAgC,uBAAQ,GA6DvC;AA7DY,gCAAU;AAmEvB;IAA8B,4BAAQ;IAKpC,kBAAY,OAAyB;QAArC,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;;IACrC,CAAC;IAKD,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,6BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAMM,4BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAEvB,MAAM,IAAI,qBAAQ,CAChB,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC5D;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YAEhB,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EACN,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAY,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxF,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;gBAEzC,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,eAAe,EACpB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAW,CAAC,CAAC;aAChE;YACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;gBAEzD,qBAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;gBAC9C,GAAG,GAAG,sBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAW,EAAE,GAAG,EACvC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,CAAC,CAAC;aACjE;YACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SACxD;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAMM,2BAAQ,GAAf;QACE,OAAO,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACtC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;IACxC,CAAC;IAEH,eAAC;AAAD,CAAC,AAjED,CAA8B,uBAAQ,GAiErC;AAjEY,4BAAQ;AAuErB;IAA8B,4BAAQ;IAepC,kBAAY,OAAyB,EAAE,KAAa;QAApD,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;IACnC,CAAC;IAKD,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,4BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAMM,4BAAS,GAAhB,UAAiB,IAAe;QAE9B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YAIxB,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,sBAAS,CAAC,MAAM,CAClD,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,EAAE,IAAI,CAAC,KAAK,EAAE,EACjD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC1F;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAIzB,IAAM,GAAG,GAAG,EAAC,QAAQ,EAAE,IAAI,EAAQ,CAAC;YACpC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;gBAC7B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aAC3C;YACD,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,KAAK,EAAC,CAAC,EAC9D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAC1D,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,IAAI,EAAC,CAAC,CAC9D,CAAC;YACF,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SACvB;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IA9DgB,eAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAQ,CAAC,MAAM,CAAC,EAAE;QAEtE,MAAM,EAAE,CAAC,uBAAuB;YACvB,iCAAiC,CAAC;KAC5C,CAAC,CAAC;IA4DL,eAAC;CAAA,AArED,CAA8B,uBAAQ,GAqErC;AArEY,4BAAQ;AA2ErB;IAA4B,0BAAQ;IAKlC,gBAAY,OAAyB,EAAE,KAAa,EAAE,KAAa;QAAnE,YACE,kBAAM,OAAO,CAAC,SAGf;QAFC,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACjC,KAAK,IAAI,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;IAC5C,CAAC;IAKD,sBAAW,wBAAI;aAAf;YACE,OAAO,QAAQ,CAAC;QAClB,CAAC;;;OAAA;IAMD,sBAAI,2BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEH,aAAC;AAAD,CAAC,AA1BD,CAA4B,uBAAQ,GA0BnC;AA1BY,wBAAM;AAgCnB;IAA+B,6BAAQ;IAKrC,mBAAY,OAAyB,EAAE,KAAa,EAAE,KAAa;QAAnE,YACE,kBAAM,OAAO,CAAC,SAGf;QAFC,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACjC,KAAK,IAAI,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;IAC5C,CAAC;IAKD,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,8BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEH,gBAAC;AAAD,CAAC,AA1BD,CAA+B,uBAAQ,GA0BtC;AA1BY,8BAAS;AAgCtB;IAA+B,6BAAQ;IAAvC;;IAwCA,CAAC;IAnCC,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,6BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,6BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,EAAE;gBAErC,MAAM,IAAI,qBAAQ,CAAC,WAAW,EAAE,kCAAkC,EAC/C,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;aACpD;YACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBAE5B,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC3D;YACD,OAAO,uBAAQ,CAAC,IAAI,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAEvB,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC1E;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,gBAAC;AAAD,CAAC,AAxCD,CAA+B,uBAAQ,GAwCtC;AAxCY,8BAAS;AAgDtB;IAA6B,2BAAQ;IAArC;;IAiBA,CAAC;IAZC,sBAAW,yBAAI;aAAf;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAMD,sBAAI,4BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAEH,cAAC;AAAD,CAAC,AAjBD,CAA6B,uBAAQ,GAiBpC;AAjBY,0BAAO;AAuBpB;IAA+B,6BAAQ;IAAvC;;IAqBA,CAAC;IAhBC,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAKM,6BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;SAC9B;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClF,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAEH,gBAAC;AAAD,CAAC,AArBD,CAA+B,uBAAQ,GAqBtC;AArBY,8BAAS;AA2BtB;IAAkC,gCAAQ;IAA1C;;IAoCA,CAAC;IA/BC,sBAAW,8BAAI;aAAf;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAMM,gCAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,OAAO,EAAE;YAEhB,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,oBAAoB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3E;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBAClC,KAAK,UAAU;oBAEb,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,EACxB,EAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;wBAC9B,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;wBAC7B,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAC,CAAC,CAAC;oBACrD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACnD,KAAK,YAAY;oBAEf,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAY,CAAC,EAAE,IAAI;4BACrE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACnF;SACF;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACH,mBAAC;AAAD,CAAC,AApCD,CAAkC,uBAAQ,GAoCzC;AApCY,oCAAY;AA0CzB;IAA8B,4BAAQ;IAAtC;;IAgBA,CAAC;IAXC,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAMD,sBAAI,6BAAO;aAAX;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IACH,eAAC;AAAD,CAAC,AAhBD,CAA8B,uBAAQ,GAgBrC;AAhBY,4BAAQ;AAsBrB;IAA6B,2BAAQ;IAArC;;IAgBA,CAAC;IAXC,sBAAW,4BAAO;aAAlB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKD,sBAAW,yBAAI;aAAf;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAEH,cAAC;AAAD,CAAC,AAhBD,CAA6B,uBAAQ,GAgBpC;AAhBY,0BAAO;AAsBpB;IAA4B,0BAAQ;IAApC;;IAkDA,CAAC;IA7CC,sBAAW,wBAAI;aAAf;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,0BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,GAAG,EAAE;YACP,IAAI,IAAI,CAAC,MAAM,EAAE;gBAEf,OAAO,uBAAQ,CAAC,OAAO,CAAC;aACzB;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBAEtB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;oBAE/B,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC5B;gBACD,IAAI,CAAC,qBAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM;oBACvD,qBAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,EAAE,QAAQ,CAAC,CAAC;oBACtE,qBAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;oBAEnC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC5B;gBACD,IAAI,qBAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;oBAE/B,GAAG,GAAG,qBAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;iBAC/B;gBACD,IAAM,IAAI,GAAG,qBAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACnC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;oBAE3D,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC5B;aACF;YAED,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,IAAI,EAAC,EACxC,sBAAQ,CAAC,aAAa,CAAC,CAAC;YACjD,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SAClC;QAED,OAAO,iBAAM,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IACH,aAAC;AAAD,CAAC,AAlDD,CAA4B,uBAAQ,GAkDnC;AAlDY,wBAAM;AAyDnB;IAA6B,2BAAQ;IAArC;QAAA,qEAgDC;QA9CS,WAAK,GAAG,0BAAU,CAAC,MAAM,CAAC,WAAW,CAAiB,CAAC;;IA8CjE,CAAC;IAzCC,sBAAW,yBAAI;aAAf;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAKM,2BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,GAAuB,CAAC;QAC5B,IAAI,CAAS,CAAC;QACd,IAAI,QAAkB,CAAC;QACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAE9C,OAAO,uBAAQ,CAAC,OAAO,CAAC;SACzB;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAClB,CAAC,qBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,qBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;gBACtE,qBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;YAC1C,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YACjB,CAAC,GAAG,qBAAQ,CAAC,OAAO,CAAC,GAAe,CAAC,CAAC;YACtC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,qBAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC;gBAC1D,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAE1B,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAa,CAAC;oBACtE,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACrC;qBAAM;oBAEL,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAa,CAAC;oBACrD,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC1C;gBACD,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;aACvB;SACF;QAED,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAa,CAAC;QACrD,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjE,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,EAAE,EAAC,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC;QAC3E,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,UAAU,CAAC,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,GAAG,EAAC,CAAC,CAAC;QAC7E,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IACH,cAAC;AAAD,CAAC,AAhDD,CAA6B,uBAAQ,GAgDpC;AAhDY,0BAAO;AAqDpB;IAAmC,iCAAQ;IAA3C;;IA2CA,CAAC;IAtCC,sBAAW,+BAAI;aAAf;YACE,OAAO,WAAW,CAAC;QACrB,CAAC;;;OAAA;IAKM,iCAAS,GAAhB,UAAiB,IAAe;QAI9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;YAC3C,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YAKrB,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE;gBACzC,GAAG,GAAG,qBAAQ,CAAC,WAAW,CAAC,qBAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7D;YACD,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;gBAMxB,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,EAAE;oBACtB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBACvD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACjB;gBAID,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;aAC7D;SACF;QACD,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IACxB,CAAC;IACH,oBAAC;AAAD,CAAC,AA3CD,CAAmC,uBAAQ,GA2C1C;AA3CY,sCAAa;AAgD1B;IAA8B,4BAAQ;IAAtC;;IA2BA,CAAC;IAtBC,sBAAW,0BAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAKM,4BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC9C,OAAO,uBAAQ,CAAC,OAAO,CAAC;SACzB;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAY,CAAC;QAChD,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QAErB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,qBAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YACrD,IAAM,MAAM,GAAG,qBAAQ,CAAC,WAAW,CAAC,qBAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7D,IAAI,MAAM,KAAK,qBAAQ,CAAC,GAAG,IAAI,MAAM,KAAK,qBAAQ,CAAC,GAAG,EAAE;gBACtD,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAY,CAAC;aAC7C;SACF;QACD,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACH,eAAC;AAAD,CAAC,AA3BD,CAA8B,uBAAQ,GA2BrC;AA3BY,4BAAQ;AAkCrB;IAA+B,6BAAQ;IAAvC;QAAA,qEA6OC;QAvOQ,WAAK,GAAc,EAAE,CAAC;QAMtB,SAAG,GAAc,EAAE,CAAC;QAMpB,WAAK,GAAa,EAAE,CAAC;QAMrB,WAAK,GAAa,EAAE,CAAC;QAMrB,cAAQ,GAA+C,EAAE,CAAC;QAM1D,YAAM,GAAY,KAAK,CAAC;;IAyMjC,CAAC;IApMC,sBAAW,2BAAI;aAAf;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAMD,sBAAI,6BAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKD,sBAAI,8BAAO;aAAX;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAKM,6BAAS,GAAhB,UAAiB,IAAe;QAE9B,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAExC,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBAE/B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,OAAO,uBAAQ,CAAC,IAAI,CAAC;aACtB;YACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBAE5B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,OAAO,uBAAQ,CAAC,IAAI,CAAC;aACtB;YACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3D,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE;gBAEpC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBAExB,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC1B;gBAED,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;aAChE;YACD,OAAO,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SAChC;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAOM,6BAAS,GAAhB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACpC,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,WAAW,EAAE;YACf,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;SAC7C;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAE3B,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACvE;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAE5B,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAE7B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACtB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAY,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;aAC1E;YAED,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACxC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC;YAC/E,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,MAAM,CAAC,KAAK,MAAM;gBACnD,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,KAAK,MAAM,EAAE;gBAGpD,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC;aAC/C;SACF;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;YAEzD,GAAG,GAAG,sBAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAW,EAAE,GAAG,EACvC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAW,CAAC,CAAC;SAC7D;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAKM,4BAAQ,GAAf;QAEE,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACvB,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aACpD;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,EAAE;gBACrD,qBAAQ,CAAC,YAAY,CACnB,GAAG,EAAE,aAAa,EAClB,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;aAClE;SACF;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAMM,0BAAM,GAAb;QACE,IAAI,IAAa,CAAC;QAClB,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAE3D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;YAEjC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;SACpD;aAAM;YAEL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;SAC7C;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAMM,4BAAQ,GAAf;QACE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAMM,8BAAU,GAAjB;QACE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC7B,IAAM,KAAK,GAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/D,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,KAAK,CAAC,GAAG,EAAE,CAAC;gBACZ,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC7C;iBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC;aACtC;SACF;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;YAClC,IAAM,IAAI,GAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACtC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC;IAOM,iCAAa,GAApB,UAAqB,OAAe;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YAC/B,IAAM,IAAI,GAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;gBAEnC,IAAI,KAAK,GAAG,sBAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;aACvC;YACD,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAW,CAAC;YAC5D,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACtC,IAAI,CAAC,IAAI,CAAC,sBAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;aACrC;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,sBAAS,CAAC,EAAE,CACxC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,sBAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC;IAEH,gBAAC;AAAD,CAAC,AA7OD,CAA+B,uBAAQ,GA6OtC;AA7OY,8BAAS;AAoPtB;IAAkC,gCAAS;IAUzC,sBAAY,OAAY;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAAxC,YACE,kBAAM,OAAO,CAAC,SAEf;QARM,YAAM,GAAW,CAAC,CAAC;QAOxB,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IACnE,CAAC;IAMD,sBAAI,8BAAI;aAAR;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAMM,+BAAQ,GAAf;QAEE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACnB,sBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SAChE;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAKM,6BAAM,GAAb;QACE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;SAC/B;QAED,IAAI,GAAG,GAAG,KAAK,CAAC;QAChB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QACnD,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,GAAG,GAAG,YAAY,CAAC;SACpB;QACD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAKM,+BAAQ,GAAf;QAEE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAKtC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAMS,kCAAW,GAArB,UAAsB,IAAY,EAAE,GAAW;QAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO;QACjC,IAAM,MAAM,GAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAM,OAAO,4BAAO,MAAM,SAAC,CAAC;QAC5B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE;gBAC3B,OAAO,CAAC,IAAI,OAAZ,OAAO,2BAAS,MAAM,WAAE;aACzB;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvD;IACH,CAAC;IACH,mBAAC;AAAD,CAAC,AAxFD,CAAkC,SAAS,GAwF1C;AAxFY,oCAAY;AA+FzB;IAAkC,gCAAQ;IAKxC,sBAAY,OAAY;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAAxC,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IACnE,CAAC;IAMD,sBAAI,8BAAI;aAAR;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAKD,sBAAI,gCAAM;aAAV;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,gCAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACtC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SACpF;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAEvB,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC1E;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,mBAAC;AAAD,CAAC,AA1CD,CAAkC,uBAAQ,GA0CzC;AA1CY,oCAAY"}