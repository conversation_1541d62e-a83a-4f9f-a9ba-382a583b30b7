import { SVGWrapper, Constructor } from '../Wrapper.js';
import { SVGscriptbase } from './scriptbase.js';
declare const SVGmsub_base: import("../../common/Wrappers/msubsup.js").MsubConstructor<SVGWrapper<any, any, any>> & Constructor<SVGscriptbase<any, any, any>>;
export declare class SVGmsub<N, T, D> extends SVGmsub_base {
    static kind: string;
}
declare const SVGmsup_base: import("../../common/Wrappers/msubsup.js").MsupConstructor<SVGWrapper<any, any, any>> & Constructor<SVGscriptbase<any, any, any>>;
export declare class SVGmsup<N, T, D> extends SVGmsup_base {
    static kind: string;
}
declare const SVGmsubsup_base: import("../../common/Wrappers/msubsup.js").MsubsupConstructor<SVGWrapper<any, any, any>> & Constructor<SVGscriptbase<any, any, any>>;
export declare class SVGmsubsup<N, T, D> extends SVGmsubsup_base {
    static kind: string;
    toSVG(parent: N): void;
}
export {};
