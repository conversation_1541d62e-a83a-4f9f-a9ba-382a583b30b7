{"version": 3, "file": "BraketMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/braket/BraketMethods.ts"], "names": [], "mappings": ";;;;;AAyBA,0EAAiD;AAEjD,+DAA0D;AAC1D,+DAAsC;AAGtC,IAAI,aAAa,GAAgC,EAAE,CAAC;AAEpD,aAAa,CAAC,KAAK,GAAG,wBAAW,CAAC,KAAK,CAAC;AAYxC,aAAa,CAAC,MAAM,GAAG,UAAS,MAAiB,EAAE,KAAa,EAChC,IAAY,EAAE,KAAa,EAC3B,QAAiB,EAAE,MAAc;IAC/D,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAC5B,IAAI,IAAI,KAAK,EAAE,EAAE;QACf,MAAM,IAAI,qBAAQ,CAAC,eAAe,EAAE,yBAAyB,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;KAClF;IACD,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,IAAI,IAAI,KAAK,GAAG,EAAE;QAChB,MAAM,CAAC,CAAC,EAAE,CAAC;QACX,MAAM,GAAG,KAAK,CAAC;KAChB;IACD,MAAM,CAAC,IAAI,CACT,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;SAChC,aAAa,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI;QACvC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC;AAQF,aAAa,CAAC,GAAG,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC1D,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;IACtC,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;QACrB,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;QAC5D,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjB,OAAO;KACR;IACD,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;QACzC,MAAM,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,GAAG,QAAQ,CAAC;KACd;IACD,IAAI,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC3C,IAAI,CAAC,QAAQ,EAAE;QACb,IAAI,MAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC,CAAC;QAC/E,MAAM,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;QAClB,OAAO;KACR;IACD,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,KAAK,EAAC,CAAC,CAAC;IAC5E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,UAAU,CAAW,GAAG,CAAC,CAAC,CAAC;IACvE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC,CAAC;IAC1E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,QAAQ,EAAE,qBAAQ,CAAC,IAAI,EAAC,CAAC,CAAC;IACvE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC,CAAC;AAGF,kBAAe,aAAa,CAAC"}