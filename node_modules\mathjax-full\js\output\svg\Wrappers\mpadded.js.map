{"version": 3, "file": "mpadded.js", "sourceRoot": "", "sources": ["../../../../ts/output/svg/Wrappers/mpadded.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAAyD;AACzD,+DAAoE;AACpE,wEAAqE;AAWrE;IACA,8BAA6D;IAD7D;;IA2BA,CAAC;IAhBQ,0BAAK,GAAZ,UAAa,MAAS;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,IAAA,KAAA,OAA4B,IAAI,CAAC,SAAS,EAAE,IAAA,EAAhC,EAAE,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAAoB,CAAC;QACnD,IAAM,KAAK,GAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAY,IAAI,MAAM,CAAC;QAC3E,IAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAKvF,IAAI,CAAC,IAAI,CAAC,EAAE;YACV,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;SACvB;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAnBa,eAAI,GAAG,uBAAU,CAAC,SAAS,CAAC,IAAI,CAAC;IAqBjD,iBAAC;CAAA,AA3BD,CACA,IAAA,+BAAkB,EAAgC,uBAAU,CAAC,GA0B5D;AA3BY,gCAAU"}