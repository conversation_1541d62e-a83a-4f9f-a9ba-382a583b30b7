import { Configuration } from '../Configuration.js';
import TexParser from '../TexParser.js';
export declare const SetOptionsUtil: {
    filterPackage(parser: TexParser, extension: string): boolean;
    filterOption(parser: TexParser, extension: string, option: string): boolean;
    filterValue(_parser: TexParser, _extension: string, _option: string, value: string): string;
};
export declare const SetOptionsConfiguration: Configuration;
