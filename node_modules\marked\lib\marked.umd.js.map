{"version": 3, "sources": ["../src/marked.ts", "../src/defaults.ts", "../src/rules.ts", "../src/helpers.ts", "../src/Tokenizer.ts", "../src/Lexer.ts", "../src/Renderer.ts", "../src/TextRenderer.ts", "../src/Parser.ts", "../src/Hooks.ts", "../src/Instance.ts"], "sourcesContent": ["import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport {\n  _getDefaults,\n  changeDefaults,\n  _defaults,\n} from './defaults.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\nimport type { MaybePromise } from './Instance.ts';\n\nconst markedInstance = new Marked();\n\n/**\n * Compiles markdown to HTML asynchronously.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options, having async: true\n * @return Promise of string of compiled HTML\n */\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\n\n/**\n * Compiles markdown to HTML.\n *\n * @param src String of markdown source to be compiled\n * @param options Optional hash of options\n * @return String of compiled HTML. Will be a Promise of string if async is set to true by any extensions.\n */\nexport function marked(src: string, options: MarkedOptions & { async: false }): string;\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\nexport function marked(src: string, options?: MarkedOptions | null): string | Promise<string>;\nexport function marked(src: string, opt?: MarkedOptions | null): string | Promise<string> {\n  return markedInstance.parse(src, opt);\n}\n\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\nmarked.setOptions = function(options: MarkedOptions) {\n  markedInstance.setOptions(options);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\n\nmarked.defaults = _defaults;\n\n/**\n * Use Extension\n */\n\nmarked.use = function(...args: MarkedExtension[]) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Run callback for every token\n */\n\nmarked.walkTokens = function(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n  return markedInstance.walkTokens(tokens, callback);\n};\n\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\n\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\nexport type * from './MarkedOptions.ts';\nexport type * from './Tokens.ts';\n", "import type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Gets the original marked default options.\n */\nexport function _getDefaults<ParserOutput = string, RendererOutput = string>(): MarkedOptions<ParserOutput, RendererOutput> {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null,\n  };\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let _defaults: MarkedOptions<any, any> = _getDefaults();\n\nexport function changeDefaults<ParserOutput = string, RendererOutput = string>(newDefaults: MarkedOptions<ParserOutput, RendererOutput>) {\n  _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null } as unknown as RegExp;\n\nfunction edit(regex: string | RegExp, opt = '') {\n  let source = typeof regex === 'string' ? regex : regex.source;\n  const obj = {\n    replace: (name: string | RegExp, val: string | RegExp) => {\n      let valSource = typeof val === 'string' ? val : val.source;\n      valSource = valSource.replace(other.caret, '$1');\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    },\n  };\n  return obj;\n}\n\nexport const other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull: string) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n\n/**\n * Block-Level Grammar\n */\n\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/\\|table/g, '') // table not in commonmark\n  .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n  .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n  .replace('label', _blockLabel)\n  .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n  .getRegex();\n\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n  .replace(/bull/g, bullet)\n  .getRegex();\n\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n  + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n  + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n  + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n  + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n  + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit(\n  '^ {0,3}(?:' // optional indentation\n+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n+ ')', 'i')\n  .replace('comment', _comment)\n  .replace('tag', _tag)\n  .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst paragraph = edit(_paragraph)\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n  .replace('|table', '')\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n  .replace('paragraph', paragraph)\n  .getRegex();\n\n/**\n * Normal Block Grammar\n */\n\nconst blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText,\n};\n\ntype BlockKeys = keyof typeof blockNormal;\n\n/**\n * GFM Block Grammar\n */\n\nconst gfmTable = edit(\n  '^ *([^\\\\n ].*)\\\\n' // Header\n+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('blockquote', ' {0,3}>')\n  .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockGfm: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('table', gfmTable) // interrupt paragraphs with table\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex(),\n};\n\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\n\nconst blockPedantic: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  html: edit(\n    '^ *(?:comment *(?:\\\\n|\\\\s*$)'\n    + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n    + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n    .replace('comment', _comment)\n    .replace(/tag/g, '(?!(?:'\n      + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n      + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n      + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n    .getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest, // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' *#{1,6} *[^\\n]')\n    .replace('lheading', lheading)\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('|fences', '')\n    .replace('|list', '')\n    .replace('|html', '')\n    .replace('|tag', '')\n    .getRegex(),\n};\n\n/**\n * Inline-Level Grammar\n */\n\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n  .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<(?! )[^<>]*?>/g;\n\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\n\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\nconst emStrongRDelimAstCore =\n  '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n+ '|[^*]+(?=[^*])' // Consume to delim\n+ '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n+ '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n+ '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\n\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n  .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit(\n  '^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n+ '|[^_]+(?=[^_])' // Consume to delim\n+ '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n+ '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n+ '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n+ '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n+ '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n  .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n  .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n  .getRegex();\n\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit(\n  '^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n  .replace('comment', _inlineComment)\n  .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/)\n  .replace('label', _inlineLabel)\n  .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/)\n  .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n  .getRegex();\n\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n  .replace('label', _inlineLabel)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n  .replace('reflink', reflink)\n  .replace('nolink', nolink)\n  .getRegex();\n\n/**\n * Normal Inline Grammar\n */\n\nconst inlineNormal = {\n  _backpedal: noopTest, // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest,\n};\n\ntype InlineKeys = keyof typeof inlineNormal;\n\n/**\n * Pedantic Inline Grammar\n */\n\nconst inlinePedantic: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n};\n\n/**\n * GFM Inline Grammar\n */\n\nconst inlineGfm: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n    .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n    .getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n\n/**\n * GFM + Line Breaks Inline Grammar\n */\n\nconst inlineBreaks: Record<InlineKeys, RegExp> = {\n  ...inlineGfm,\n  br: edit(br).replace('{2,}', '*').getRegex(),\n  text: edit(inlineGfm.text)\n    .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n    .replace(/\\{2,\\}/g, '*')\n    .getRegex(),\n};\n\n/**\n * exports\n */\n\nexport const block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic,\n};\n\nexport const inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic,\n};\n\nexport interface Rules {\n  other: typeof other\n  block: Record<BlockKeys, RegExp>\n  inline: Record<InlineKeys, RegExp>\n}\n", "import { other } from './rules.ts';\n\n/**\n * Helpers\n */\nconst escapeReplacements: { [index: string]: string } = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch: string) => escapeReplacements[ch];\n\nexport function escape(html: string, encode?: boolean) {\n  if (encode) {\n    if (other.escapeTest.test(html)) {\n      return html.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html)) {\n      return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n\nexport function unescape(html: string) {\n  // explicitly match decimal, hex, and named HTML entities\n  return html.replace(other.unescapeTest, (_, n) => {\n    n = n.toLowerCase();\n    if (n === 'colon') return ':';\n    if (n.charAt(0) === '#') {\n      return n.charAt(1) === 'x'\n        ? String.fromCharCode(parseInt(n.substring(2), 16))\n        : String.fromCharCode(+n.substring(1));\n    }\n    return '';\n  });\n}\n\nexport function cleanUrl(href: string) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, '%');\n  } catch {\n    return null;\n  }\n  return href;\n}\n\nexport function splitCells(tableRow: string, count?: number) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push('');\n    }\n  }\n\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n  }\n  return cells;\n}\n\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str: string, c: string, invert?: boolean) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n\n  return str.slice(0, l - suffLen);\n}\n\nexport function findClosingBracket(str: string, b: string) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n\n  return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  rtrim,\n  splitCells,\n  findClosingBracket,\n} from './helpers.ts';\nimport type { Rules } from './rules.ts';\nimport type { _Lexer } from './Lexer.ts';\nimport type { Links, Tokens, Token } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\nfunction outputLink(cap: string[], link: Pick<Tokens.Link, 'href' | 'title'>, raw: string, lexer: _Lexer, rules: Rules): Tokens.Link | Tokens.Image {\n  const href = link.href;\n  const title = link.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n\n  lexer.state.inLink = true;\n  const token: Tokens.Link | Tokens.Image = {\n    type: cap[0].charAt(0) === '!' ? 'image' : 'link',\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer.inlineTokens(text),\n  };\n  lexer.state.inLink = false;\n  return token;\n}\n\nfunction indentCodeCompensation(raw: string, text: string, rules: Rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n\n  if (matchIndentToCode === null) {\n    return text;\n  }\n\n  const indentToCode = matchIndentToCode[1];\n\n  return text\n    .split('\\n')\n    .map(node => {\n      const matchIndentInNode = node.match(rules.other.beginningSpace);\n      if (matchIndentInNode === null) {\n        return node;\n      }\n\n      const [indentInNode] = matchIndentInNode;\n\n      if (indentInNode.length >= indentToCode.length) {\n        return node.slice(indentToCode.length);\n      }\n\n      return node;\n    })\n    .join('\\n');\n}\n\n/**\n * Tokenizer\n */\nexport class _Tokenizer<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  rules!: Rules; // set by the lexer\n  lexer!: _Lexer<ParserOutput, RendererOutput>; // set by the lexer\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  space(src: string): Tokens.Space | undefined {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0],\n      };\n    }\n  }\n\n  code(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic\n          ? rtrim(text, '\\n')\n          : text,\n      };\n    }\n  }\n\n  fences(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n        text,\n      };\n    }\n  }\n\n  heading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n\n      // remove trailing #s\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  hr(src: string): Tokens.Hr | undefined {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: rtrim(cap[0], '\\n'),\n      };\n    }\n  }\n\n  blockquote(src: string): Tokens.Blockquote | undefined {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], '\\n').split('\\n');\n      let raw = '';\n      let text = '';\n      const tokens: Token[] = [];\n\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          // get lines up to a continuation\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n\n        const currentRaw = currentLines.join('\\n');\n        const currentText = currentRaw\n          // precede setext continuation with 4 spaces so it isn't a setext\n          .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n          .replace(this.rules.other.blockquoteSetextReplace2, '');\n        raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\\n${currentText}` : currentText;\n\n        // parse blockquote lines as top level tokens\n        // merge paragraphs if this is a continuation\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n\n        // if there is no continuation then we are done\n        if (lines.length === 0) {\n          break;\n        }\n\n        const lastToken = tokens.at(-1);\n\n        if (lastToken?.type === 'code') {\n          // blockquote continuation cannot be preceded by a code block\n          break;\n        } else if (lastToken?.type === 'blockquote') {\n          // include continuation in nested blockquote\n          const oldToken = lastToken as Tokens.Blockquote;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.blockquote(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === 'list') {\n          // include continuation in nested list\n          const oldToken = lastToken as Tokens.List;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.list(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1)!.raw.length).split('\\n');\n          continue;\n        }\n      }\n\n      return {\n        type: 'blockquote',\n        raw,\n        tokens,\n        text,\n      };\n    }\n  }\n\n  list(src: string): Tokens.List | undefined {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n\n      const list: Tokens.List = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: [],\n      };\n\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n\n      // Get next list item\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        let endEarly = false;\n        let raw = '';\n        let itemContents = '';\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n\n        if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n\n        raw = cap[0];\n        src = src.substring(raw.length);\n\n        let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t: string) => ' '.repeat(3 * t.length));\n        let nextLine = src.split('\\n', 1)[0];\n        let blankLine = !line.trim();\n\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n\n          // Check if following lines should be included in List Item\n          while (src) {\n            const rawLine = src.split('\\n', 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n            }\n\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of html block\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n\n            // Horizontal rule found\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n              itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n\n              // paragraph continuation unless last line was a different block level element\n              if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n\n              itemContents += '\\n' + nextLine;\n            }\n\n            if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n              blankLine = true;\n            }\n\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n\n        let istask: RegExpExecArray | null = null;\n        let ischecked: boolean | undefined;\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n          }\n        }\n\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: [],\n        });\n\n        list.raw += raw;\n      }\n\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      const lastItem = list.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        // not a list since there were no items\n        return;\n      }\n      list.raw = list.raw.trimEnd();\n\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (let i = 0; i < list.items.length; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (let i = 0; i < list.items.length; i++) {\n          list.items[i].loose = true;\n        }\n      }\n\n      return list;\n    }\n  }\n\n  html(src: string): Tokens.HTML | undefined {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token: Tokens.HTML = {\n        type: 'html',\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n        text: cap[0],\n      };\n      return token;\n    }\n  }\n\n  def(src: string): Tokens.Def | undefined {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title,\n      };\n    }\n  }\n\n  table(src: string): Tokens.Table | undefined {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n      return;\n    }\n\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n\n    const item: Tokens.Table = {\n      type: 'table',\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: [],\n    };\n\n    if (headers.length !== aligns.length) {\n      // header and align columns must be equal, rows can be different.\n      return;\n    }\n\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push('right');\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push('center');\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push('left');\n      } else {\n        item.align.push(null);\n      }\n    }\n\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i],\n      });\n    }\n\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i],\n        };\n      }));\n    }\n\n    return item;\n  }\n\n  lheading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1]),\n      };\n    }\n  }\n\n  paragraph(src: string): Tokens.Paragraph | undefined {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n        ? cap[1].slice(0, -1)\n        : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  text(src: string): Tokens.Text | undefined {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0]),\n      };\n    }\n  }\n\n  escape(src: string): Tokens.Escape | undefined {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: cap[1],\n      };\n    }\n  }\n\n  tag(src: string): Tokens.Tag | undefined {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n\n      return {\n        type: 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0],\n      };\n    }\n  }\n\n  link(src: string): Tokens.Link | Tokens.Image | undefined {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          return;\n        }\n\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex === -2) {\n          // more open parens than closed\n          return;\n        }\n\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = this.rules.other.pedanticHrefTitle.exec(href);\n\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  reflink(src: string, links: Links): Tokens.Link | Tokens.Image | Tokens.Text | undefined {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src))\n      || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const link = links[linkString.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text,\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  emStrong(src: string, maskedSrc: string, prevChar = ''): Tokens.Em | Tokens.Strong | undefined {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n\n    const nextChar = match[1] || match[2] || '';\n\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\n        if (!rDelim) continue; // skip single * in __abc*abc__\n\n        rLength = [...rDelim].length;\n\n        if (match[3] || match[4]) { // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) { // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n\n        delimTotal -= rLength;\n\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        // char length can be >1 for unicode characters;\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text),\n          };\n        }\n\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text),\n        };\n      }\n    }\n  }\n\n  codespan(src: string): Tokens.Codespan | undefined {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text,\n      };\n    }\n  }\n\n  br(src: string): Tokens.Br | undefined {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0],\n      };\n    }\n  }\n\n  del(src: string): Tokens.Del | undefined {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2]),\n      };\n    }\n  }\n\n  autolink(src: string): Tokens.Link | undefined {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[1];\n        href = 'mailto:' + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  url(src: string): Tokens.Link | undefined {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[0];\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  inlineText(src: string): Tokens.Text | undefined {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        escaped,\n      };\n    }\n  }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\nimport type { Token, TokensList, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Block Lexer\n */\nexport class _Lexer<ParserOutput = string, RendererOutput = string> {\n  tokens: TokensList;\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  state: {\n    inLink: boolean;\n    inRawBlock: boolean;\n    top: boolean;\n  };\n\n  private tokenizer: _Tokenizer<ParserOutput, RendererOutput>;\n  private inlineQueue: { src: string, tokens: Token[] }[];\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    // TokenList cannot be created in one go\n    this.tokens = [] as unknown as TokensList;\n    this.tokens.links = Object.create(null);\n    this.options = options || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer<ParserOutput, RendererOutput>();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true,\n    };\n\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal,\n    };\n\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline,\n    };\n  }\n\n  /**\n   * Static Lex Method\n   */\n  static lex<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const lexer = new _Lexer<ParserOutput, RendererOutput>(options);\n    return lexer.lex(src);\n  }\n\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const lexer = new _Lexer<ParserOutput, RendererOutput>(options);\n    return lexer.inlineTokens(src);\n  }\n\n  /**\n   * Preprocessing\n   */\n  lex(src: string) {\n    src = src.replace(other.carriageReturn, '\\n');\n\n    this.blockTokens(src, this.tokens);\n\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n\n    return this.tokens;\n  }\n\n  /**\n   * Lexing\n   */\n  blockTokens(src: string, tokens?: Token[], lastParagraphClipped?: boolean): Token[];\n  blockTokens(src: string, tokens?: TokensList, lastParagraphClipped?: boolean): TokensList;\n  blockTokens(src: string, tokens: Token[] = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n    }\n\n    while (src) {\n      let token: Tokens.Generic | undefined;\n\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== undefined) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unnecessary paragraph tags\n          lastToken.raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title,\n          };\n        }\n        continue;\n      }\n\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    this.state.top = true;\n    return tokens;\n  }\n\n  inline(src: string, tokens: Token[] = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src: string, tokens: Token[] = []): Token[] {\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match: RegExpExecArray | null = null;\n\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index)\n              + '[' + 'a'.repeat(match[0].length - 2) + ']'\n              + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n\n    // Mask out escaped characters\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n\n    let keepPrevChar = false;\n    let prevChar = '';\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n\n      let token: Tokens.Generic | undefined;\n\n      // extensions\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === 'text' && lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // autolink\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    return tokens;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  cleanUrl,\n  escape,\n} from './helpers.ts';\nimport { other } from './rules.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Tokens } from './Tokens.ts';\nimport type { _Parser } from './Parser.ts';\n\n/**\n * Renderer\n */\nexport class _Renderer<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  parser!: _Parser<ParserOutput, RendererOutput>; // set by the parser\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  space(token: Tokens.Space): RendererOutput {\n    return '' as RendererOutput;\n  }\n\n  code({ text, lang, escaped }: Tokens.Code): RendererOutput {\n    const langString = (lang || '').match(other.notSpaceStart)?.[0];\n\n    const code = text.replace(other.endingNewline, '') + '\\n';\n\n    if (!langString) {\n      return '<pre><code>'\n        + (escaped ? code : escape(code, true))\n        + '</code></pre>\\n' as RendererOutput;\n    }\n\n    return '<pre><code class=\"language-'\n      + escape(langString)\n      + '\">'\n      + (escaped ? code : escape(code, true))\n      + '</code></pre>\\n' as RendererOutput;\n  }\n\n  blockquote({ tokens }: Tokens.Blockquote): RendererOutput {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\\n${body}</blockquote>\\n` as RendererOutput;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  heading({ tokens, depth }: Tokens.Heading): RendererOutput {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n` as RendererOutput;\n  }\n\n  hr(token: Tokens.Hr): RendererOutput {\n    return '<hr>\\n' as RendererOutput;\n  }\n\n  list(token: Tokens.List): RendererOutput {\n    const ordered = token.ordered;\n    const start = token.start;\n\n    let body = '';\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n\n    const type = ordered ? 'ol' : 'ul';\n    const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n    return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n' as RendererOutput;\n  }\n\n  listitem(item: Tokens.ListItem): RendererOutput {\n    let itemBody = '';\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === 'paragraph') {\n          item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n            item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: 'text',\n            raw: checkbox + ' ',\n            text: checkbox + ' ',\n            escaped: true,\n          });\n        }\n      } else {\n        itemBody += checkbox + ' ';\n      }\n    }\n\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n\n    return `<li>${itemBody}</li>\\n` as RendererOutput;\n  }\n\n  checkbox({ checked }: Tokens.Checkbox): RendererOutput {\n    return '<input '\n      + (checked ? 'checked=\"\" ' : '')\n      + 'disabled=\"\" type=\"checkbox\">' as RendererOutput;\n  }\n\n  paragraph({ tokens }: Tokens.Paragraph): RendererOutput {\n    return `<p>${this.parser.parseInline(tokens)}</p>\\n` as RendererOutput;\n  }\n\n  table(token: Tokens.Table): RendererOutput {\n    let header = '';\n\n    // header\n    let cell = '';\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell as ParserOutput });\n\n    let body = '';\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n\n      cell = '';\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n\n      body += this.tablerow({ text: cell as ParserOutput });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n\n    return '<table>\\n'\n      + '<thead>\\n'\n      + header\n      + '</thead>\\n'\n      + body\n      + '</table>\\n' as RendererOutput;\n  }\n\n  tablerow({ text }: Tokens.TableRow<ParserOutput>): RendererOutput {\n    return `<tr>\\n${text}</tr>\\n` as RendererOutput;\n  }\n\n  tablecell(token: Tokens.TableCell): RendererOutput {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? 'th' : 'td';\n    const tag = token.align\n      ? `<${type} align=\"${token.align}\">`\n      : `<${type}>`;\n    return tag + content + `</${type}>\\n` as RendererOutput;\n  }\n\n  /**\n   * span level renderer\n   */\n  strong({ tokens }: Tokens.Strong): RendererOutput {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>` as RendererOutput;\n  }\n\n  em({ tokens }: Tokens.Em): RendererOutput {\n    return `<em>${this.parser.parseInline(tokens)}</em>` as RendererOutput;\n  }\n\n  codespan({ text }: Tokens.Codespan): RendererOutput {\n    return `<code>${escape(text, true)}</code>` as RendererOutput;\n  }\n\n  br(token: Tokens.Br): RendererOutput {\n    return '<br>' as RendererOutput;\n  }\n\n  del({ tokens }: Tokens.Del): RendererOutput {\n    return `<del>${this.parser.parseInline(tokens)}</del>` as RendererOutput;\n  }\n\n  link({ href, title, tokens }: Tokens.Link): RendererOutput {\n    const text = this.parser.parseInline(tokens) as string;\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text as RendererOutput;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + (escape(title)) + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out as RendererOutput;\n  }\n\n  image({ href, title, text, tokens }: Tokens.Image): RendererOutput {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer) as string;\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape(text) as RendererOutput;\n    }\n    href = cleanHref;\n\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape(title)}\"`;\n    }\n    out += '>';\n    return out as RendererOutput;\n  }\n\n  text(token: Tokens.Text | Tokens.Escape): RendererOutput {\n    return 'tokens' in token && token.tokens\n      ? this.parser.parseInline(token.tokens) as unknown as RendererOutput\n      : ('escaped' in token && token.escaped ? token.text as RendererOutput : escape(token.text) as RendererOutput);\n  }\n}\n", "import type { Tokens } from './Tokens.ts';\n\n/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer<RendererOutput = string> {\n  // no need for block level renderers\n  strong({ text }: Tokens.Strong): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  em({ text }: Tokens.Em): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  codespan({ text }: Tokens.Codespan): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  del({ text }: Tokens.Del): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  text({ text }: Tokens.Text | Tokens.Escape | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  link({ text }: Tokens.Link): RendererOutput {\n    return '' + text as RendererOutput;\n  }\n\n  image({ text }: Tokens.Image): RendererOutput {\n    return '' + text as RendererOutput;\n  }\n\n  br(): RendererOutput {\n    return '' as RendererOutput;\n  }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\nimport type { MarkedToken, Token, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Parsing & Compiling\n */\nexport class _Parser<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  renderer: _Renderer<ParserOutput, RendererOutput>;\n  textRenderer: _TextRenderer<RendererOutput>;\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer<ParserOutput, RendererOutput>();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer<RendererOutput>();\n  }\n\n  /**\n   * Static Parse Method\n   */\n  static parse<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const parser = new _Parser<ParserOutput, RendererOutput>(options);\n    return parser.parse(tokens);\n  }\n\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const parser = new _Parser<ParserOutput, RendererOutput>(options);\n    return parser.parseInline(tokens);\n  }\n\n  /**\n   * Parse Loop\n   */\n  parse(tokens: Token[], top = true): ParserOutput {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken as Tokens.Generic;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'space': {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case 'hr': {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case 'heading': {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case 'code': {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case 'table': {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case 'blockquote': {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case 'list': {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case 'html': {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case 'paragraph': {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case 'text': {\n          let textToken = token;\n          let body = this.renderer.text(textToken) as string;\n          while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n            textToken = tokens[++i] as Tokens.Text;\n            body += ('\\n' + this.renderer.text(textToken));\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: 'paragraph',\n              raw: body,\n              text: body,\n              tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '' as ParserOutput;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n\n    return out as ParserOutput;\n  }\n\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens: Token[], renderer: _Renderer<ParserOutput, RendererOutput> | _TextRenderer<RendererOutput> = this.renderer): ParserOutput {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'escape': {\n          out += renderer.text(token);\n          break;\n        }\n        case 'html': {\n          out += renderer.html(token);\n          break;\n        }\n        case 'link': {\n          out += renderer.link(token);\n          break;\n        }\n        case 'image': {\n          out += renderer.image(token);\n          break;\n        }\n        case 'strong': {\n          out += renderer.strong(token);\n          break;\n        }\n        case 'em': {\n          out += renderer.em(token);\n          break;\n        }\n        case 'codespan': {\n          out += renderer.codespan(token);\n          break;\n        }\n        case 'br': {\n          out += renderer.br(token);\n          break;\n        }\n        case 'del': {\n          out += renderer.del(token);\n          break;\n        }\n        case 'text': {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '' as ParserOutput;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out as ParserOutput;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\n\nexport class _Hooks<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  block?: boolean;\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  static passThroughHooks = new Set([\n    'preprocess',\n    'postprocess',\n    'processAllTokens',\n  ]);\n\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown: string) {\n    return markdown;\n  }\n\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html: ParserOutput) {\n    return html;\n  }\n\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens: Token[] | TokensList) {\n    return tokens;\n  }\n\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse<ParserOutput, RendererOutput> : _Parser.parseInline<ParserOutput, RendererOutput>;\n  }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, Tokens, TokensList } from './Tokens.ts';\n\nexport type MaybePromise = void | Promise<void>;\n\ntype UnknownFunction = (...args: unknown[]) => unknown;\ntype GenericRendererFunction = (...args: unknown[]) => string | false;\n\nexport class Marked<ParserOutput = string, RendererOutput = string> {\n  defaults = _getDefaults<ParserOutput, RendererOutput>();\n  options = this.setOptions;\n\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n\n  Parser = _Parser<ParserOutput, RendererOutput>;\n  Renderer = _Renderer<ParserOutput, RendererOutput>;\n  TextRenderer = _TextRenderer<RendererOutput>;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer<ParserOutput, RendererOutput>;\n  Hooks = _Hooks<ParserOutput, RendererOutput>;\n\n  constructor(...args: MarkedExtension<ParserOutput, RendererOutput>[]) {\n    this.use(...args);\n  }\n\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n    let values: MaybePromise[] = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case 'table': {\n          const tableToken = token as Tokens.Table;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case 'list': {\n          const listToken = token as Tokens.List;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token as Tokens.Generic;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens = genericToken[childTokens].flat(Infinity) as Token[] | TokensList;\n              values = values.concat(this.walkTokens(tokens, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n\n  use(...args: MarkedExtension<ParserOutput, RendererOutput>[]) {\n    const extensions: MarkedOptions<ParserOutput, RendererOutput>['extensions'] = this.defaults.extensions || { renderers: {}, childTokens: {} };\n\n    args.forEach((pack) => {\n      // copy options to new object\n      const opts = { ...pack } as MarkedOptions<ParserOutput, RendererOutput>;\n\n      // set async to true if it was set to true before\n      opts.async = this.defaults.async || opts.async || false;\n\n      // ==-- Parse \"addon\" extensions --== //\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error('extension name required');\n          }\n          if ('renderer' in ext) { // Renderer extensions\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              // Replace extension with func to run new extension but fall back if false\n              extensions.renderers[ext.name] = function(...args) {\n                let ret = ext.renderer.apply(this, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if ('tokenizer' in ext) { // Tokenizer Extensions\n            if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) { // Function to check for start of token\n              if (ext.level === 'block') {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === 'inline') {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n\n      // ==-- Parse \"overwrite\" extensions --== //\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer<ParserOutput, RendererOutput>(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if (['options', 'parser'].includes(prop)) {\n            // ignore options property\n            continue;\n          }\n          const rendererProp = prop as Exclude<keyof _Renderer<ParserOutput, RendererOutput>, 'options' | 'parser'>;\n          const rendererFunc = pack.renderer[rendererProp] as GenericRendererFunction;\n          const prevRenderer = renderer[rendererProp] as GenericRendererFunction;\n          // Replace renderer with func to run extension, but fall back if false\n          renderer[rendererProp] = (...args: unknown[]) => {\n            let ret = rendererFunc.apply(renderer, args);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args);\n            }\n            return (ret || '') as RendererOutput;\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer<ParserOutput, RendererOutput>(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if (['options', 'rules', 'lexer'].includes(prop)) {\n            // ignore options, rules, and lexer properties\n            continue;\n          }\n          const tokenizerProp = prop as Exclude<keyof _Tokenizer<ParserOutput, RendererOutput>, 'options' | 'rules' | 'lexer'>;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp] as UnknownFunction;\n          const prevTokenizer = tokenizer[tokenizerProp] as UnknownFunction;\n          // Replace tokenizer with func to run extension, but fall back if false\n          // @ts-expect-error cannot type tokenizer function dynamically\n          tokenizer[tokenizerProp] = (...args: unknown[]) => {\n            let ret = tokenizerFunc.apply(tokenizer, args);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n\n      // ==-- Parse Hooks extensions --== //\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks<ParserOutput, RendererOutput>();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if (['options', 'block'].includes(prop)) {\n            // ignore options and block properties\n            continue;\n          }\n          const hooksProp = prop as Exclude<keyof _Hooks<ParserOutput, RendererOutput>, 'options' | 'block'>;\n          const hooksFunc = pack.hooks[hooksProp] as UnknownFunction;\n          const prevHook = hooks[hooksProp] as UnknownFunction;\n          if (_Hooks.passThroughHooks.has(prop)) {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (arg: unknown) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                  return prevHook.call(hooks, ret);\n                });\n              }\n\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (...args: unknown[]) => {\n              let ret = hooksFunc.apply(hooks, args);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n\n      // ==-- Parse WalkTokens extensions --== //\n      if (pack.walkTokens) {\n        const walkTokens = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values: MaybePromise[] = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens) {\n            values = values.concat(walkTokens.call(this, token));\n          }\n          return values;\n        };\n      }\n\n      this.defaults = { ...this.defaults, ...opts };\n    });\n\n    return this;\n  }\n\n  setOptions(opt: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n\n  lexer(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    return _Lexer.lex(src, options ?? this.defaults);\n  }\n\n  parser(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    return _Parser.parse<ParserOutput, RendererOutput>(tokens, options ?? this.defaults);\n  }\n\n  private parseMarkdown(blockType: boolean) {\n    type overloadedParse = {\n      (src: string, options: MarkedOptions<ParserOutput, RendererOutput> & { async: true }): Promise<ParserOutput>;\n      (src: string, options: MarkedOptions<ParserOutput, RendererOutput> & { async: false }): ParserOutput;\n      (src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): ParserOutput | Promise<ParserOutput>;\n    };\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const parse: overloadedParse = (src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): any => {\n      const origOpt = { ...options };\n      const opt = { ...this.defaults, ...origOpt };\n\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n\n      // throw error if an extension set async to true but parse was called with async: false\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n      }\n\n      // throw error in case of non string input\n      if (typeof src === 'undefined' || src === null) {\n        return throwError(new Error('marked(): input parameter is undefined or null'));\n      }\n      if (typeof src !== 'string') {\n        return throwError(new Error('marked(): input parameter is of type '\n          + Object.prototype.toString.call(src) + ', string expected'));\n      }\n\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n\n      const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n      const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n          .then(src => lexer(src, opt))\n          .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n          .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n          .then(tokens => parser(tokens, opt))\n          .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n          .catch(throwError);\n      }\n\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src) as string;\n        }\n        let tokens = lexer(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html = parser(tokens, opt);\n        if (opt.hooks) {\n          html = opt.hooks.postprocess(html);\n        }\n        return html;\n      } catch(e) {\n        return throwError(e as Error);\n      }\n    };\n\n    return parse;\n  }\n\n  private onError(silent: boolean, async: boolean) {\n    return (e: Error): string | Promise<string> => {\n      e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\n      if (silent) {\n        const msg = '<p>An error occurred:</p><pre>'\n          + escape(e.message + '', true)\n          + '</pre>';\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;;;mbAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,WAAAE,EAAA,UAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,aAAAC,EAAA,iBAAAC,EAAA,cAAAC,EAAA,aAAAC,EAAA,gBAAAC,EAAA,UAAAC,GAAA,WAAAC,EAAA,YAAAC,GAAA,UAAAC,GAAA,gBAAAC,GAAA,WAAAC,GAAA,eAAAC,GAAA,QAAAC,GAAA,eAAAC,KAAA,eAAAC,GAAApB,ICKO,SAASqB,GAA4G,CAC1H,MAAO,CACL,MAAO,GACP,OAAQ,GACR,WAAY,KACZ,IAAK,GACL,MAAO,KACP,SAAU,GACV,SAAU,KACV,OAAQ,GACR,UAAW,KACX,WAAY,IACd,CACF,CAEO,IAAIC,EAAqCD,EAAa,EAEtD,SAASE,EAA+DC,EAA0D,CACvIF,EAAYE,CACd,CCxBA,IAAMC,EAAW,CAAE,KAAM,IAAM,IAAK,EAEpC,SAASC,EAAKC,EAAwBC,EAAM,GAAI,CAC9C,IAAIC,EAAS,OAAOF,GAAU,SAAWA,EAAQA,EAAM,OACjDG,EAAM,CACV,QAAS,CAACC,EAAuBC,IAAyB,CACxD,IAAIC,EAAY,OAAOD,GAAQ,SAAWA,EAAMA,EAAI,OACpD,OAAAC,EAAYA,EAAU,QAAQC,EAAM,MAAO,IAAI,EAC/CL,EAASA,EAAO,QAAQE,EAAME,CAAS,EAChCH,CACT,EACA,SAAU,IACD,IAAI,OAAOD,EAAQD,CAAG,CAEjC,EACA,OAAOE,CACT,CAEO,IAAMI,EAAQ,CACnB,iBAAkB,yBAClB,kBAAmB,cACnB,uBAAwB,gBACxB,eAAgB,OAChB,WAAY,KACZ,kBAAmB,KACnB,gBAAiB,KACjB,aAAc,OACd,kBAAmB,MACnB,cAAe,MACf,oBAAqB,OACrB,UAAW,WACX,gBAAiB,oBACjB,gBAAiB,WACjB,wBAAyB,iCACzB,yBAA0B,mBAC1B,gBAAiB,OACjB,mBAAoB,0BACpB,WAAY,cACZ,gBAAiB,eACjB,QAAS,SACT,aAAc,WACd,eAAgB,OAChB,gBAAiB,aACjB,kBAAmB,YACnB,gBAAiB,YACjB,iBAAkB,aAClB,eAAgB,YAChB,UAAW,QACX,QAAS,UACT,kBAAmB,iCACnB,gBAAiB,mCACjB,kBAAmB,KACnB,gBAAiB,KACjB,kBAAmB,gCACnB,oBAAqB,gBACrB,WAAY,UACZ,cAAe,WACf,mBAAoB,oDACpB,sBAAuB,qDACvB,aAAc,6CACd,MAAO,eACP,cAAe,OACf,SAAU,MACV,UAAW,MACX,UAAW,QACX,eAAgB,WAChB,UAAW,SACX,cAAe,OACf,cAAe,MACf,cAAgBC,GAAiB,IAAI,OAAO,WAAWA,CAAI,8BAA+B,EAC1F,gBAAkBC,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAqD,EACpI,QAAUA,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAoD,EAC3H,iBAAmBA,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,iBAAiB,EACjG,kBAAoBA,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,IAAI,EACrF,eAAiBA,GAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,qBAAsB,GAAG,CACzG,EAMMC,GAAU,uBACVC,GAAY,wDACZC,GAAS,8GACTC,EAAK,qEACLC,GAAU,uCACVC,EAAS,wBACTC,GAAe,iKACfC,GAAWlB,EAAKiB,EAAY,EAC/B,QAAQ,QAASD,CAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,WAAY,EAAE,EACtB,SAAS,EACNG,GAAcnB,EAAKiB,EAAY,EAClC,QAAQ,QAASD,CAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,SAAU,mCAAmC,EACrD,SAAS,EACNI,EAAa,uFACbC,GAAY,UACZC,EAAc,8BACdC,GAAMvB,EAAK,6GAA6G,EAC3H,QAAQ,QAASsB,CAAW,EAC5B,QAAQ,QAAS,8DAA8D,EAC/E,SAAS,EAENE,GAAOxB,EAAK,sCAAsC,EACrD,QAAQ,QAASgB,CAAM,EACvB,SAAS,EAENS,EAAO,gWAMPC,EAAW,gCACXC,GAAO3B,EACX,4dASK,GAAG,EACP,QAAQ,UAAW0B,CAAQ,EAC3B,QAAQ,MAAOD,CAAI,EACnB,QAAQ,YAAa,0EAA0E,EAC/F,SAAS,EAENG,GAAY5B,EAAKoB,CAAU,EAC9B,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,CAAI,EACnB,SAAS,EAENI,GAAa7B,EAAK,yCAAyC,EAC9D,QAAQ,YAAa4B,EAAS,EAC9B,SAAS,EAMNE,EAAc,CAClB,WAAAD,GACA,KAAMjB,GACN,IAAAW,GACA,OAAAV,GACA,QAAAE,GACA,GAAAD,EACA,KAAAa,GACA,SAAAT,GACA,KAAAM,GACA,QAAAb,GACA,UAAAiB,GACA,MAAO7B,EACP,KAAMsB,EACR,EAQMU,GAAW/B,EACf,6JAEsF,EACrF,QAAQ,KAAMc,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,aAAc,SAAS,EAC/B,QAAQ,OAAQ,wBAAyB,EACzC,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,CAAI,EACnB,SAAS,EAENO,GAAsC,CAC1C,GAAGF,EACH,SAAUX,GACV,MAAOY,GACP,UAAW/B,EAAKoB,CAAU,EACvB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,QAASiB,EAAQ,EACzB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAON,CAAI,EACnB,SAAS,CACd,EAMMQ,GAA2C,CAC/C,GAAGH,EACH,KAAM9B,EACJ,wIAEwE,EACvE,QAAQ,UAAW0B,CAAQ,EAC3B,QAAQ,OAAQ,mKAGkB,EAClC,SAAS,EACZ,IAAK,oEACL,QAAS,yBACT,OAAQ3B,EACR,SAAU,mCACV,UAAWC,EAAKoB,CAAU,EACvB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW;AAAA,EAAiB,EACpC,QAAQ,WAAYI,EAAQ,EAC5B,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,UAAW,EAAE,EACrB,QAAQ,QAAS,EAAE,EACnB,QAAQ,QAAS,EAAE,EACnB,QAAQ,OAAQ,EAAE,EAClB,SAAS,CACd,EAMMgB,GAAS,8CACTC,GAAa,sCACbC,GAAK,wBACLC,GAAa,8EAGbC,EAAe,gBACfC,EAAsB,kBACtBC,GAAyB,mBACzBC,GAAczC,EAAK,wBAAyB,GAAG,EAClD,QAAQ,cAAeuC,CAAmB,EAAE,SAAS,EAGlDG,GAA0B,qBAC1BC,GAAiC,uBACjCC,GAAoC,yBAGpCC,GAAY,qFAEZC,GAAqB,gEAErBC,GAAiB/C,EAAK8C,GAAoB,GAAG,EAChD,QAAQ,SAAUR,CAAY,EAC9B,SAAS,EAENU,GAAoBhD,EAAK8C,GAAoB,GAAG,EACnD,QAAQ,SAAUJ,EAAuB,EACzC,SAAS,EAENO,GACJ,wQASIC,GAAoBlD,EAAKiD,GAAuB,IAAI,EACvD,QAAQ,iBAAkBT,EAAsB,EAChD,QAAQ,cAAeD,CAAmB,EAC1C,QAAQ,SAAUD,CAAY,EAC9B,SAAS,EAENa,GAAuBnD,EAAKiD,GAAuB,IAAI,EAC1D,QAAQ,iBAAkBL,EAAiC,EAC3D,QAAQ,cAAeD,EAA8B,EACrD,QAAQ,SAAUD,EAAuB,EACzC,SAAS,EAGNU,GAAoBpD,EACxB,mNAMiC,IAAI,EACpC,QAAQ,iBAAkBwC,EAAsB,EAChD,QAAQ,cAAeD,CAAmB,EAC1C,QAAQ,SAAUD,CAAY,EAC9B,SAAS,EAENe,GAAiBrD,EAAK,YAAa,IAAI,EAC1C,QAAQ,SAAUsC,CAAY,EAC9B,SAAS,EAENgB,GAAWtD,EAAK,qCAAqC,EACxD,QAAQ,SAAU,8BAA8B,EAChD,QAAQ,QAAS,8IAA8I,EAC/J,SAAS,EAENuD,GAAiBvD,EAAK0B,CAAQ,EAAE,QAAQ,YAAa,KAAK,EAAE,SAAS,EACrE8B,GAAMxD,EACV,0JAKsC,EACrC,QAAQ,UAAWuD,EAAc,EACjC,QAAQ,YAAa,6EAA6E,EAClG,SAAS,EAENE,EAAe,sDAEfC,GAAO1D,EAAK,mEAAmE,EAClF,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,OAAQ,yCAAyC,EACzD,QAAQ,QAAS,6DAA6D,EAC9E,SAAS,EAENE,GAAU3D,EAAK,yBAAyB,EAC3C,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,MAAOnC,CAAW,EAC1B,SAAS,EAENsC,GAAS5D,EAAK,uBAAuB,EACxC,QAAQ,MAAOsB,CAAW,EAC1B,SAAS,EAENuC,GAAgB7D,EAAK,wBAAyB,GAAG,EACpD,QAAQ,UAAW2D,EAAO,EAC1B,QAAQ,SAAUC,EAAM,EACxB,SAAS,EAMNE,EAAe,CACnB,WAAY/D,EACZ,eAAAsD,GACA,SAAAC,GACA,UAAAT,GACA,GAAAT,GACA,KAAMD,GACN,IAAKpC,EACL,eAAAgD,GACA,kBAAAG,GACA,kBAAAE,GACA,OAAAlB,GACA,KAAAwB,GACA,OAAAE,GACA,YAAAnB,GACA,QAAAkB,GACA,cAAAE,GACA,IAAAL,GACA,KAAMnB,GACN,IAAKtC,CACP,EAQMgE,GAA6C,CACjD,GAAGD,EACH,KAAM9D,EAAK,yBAAyB,EACjC,QAAQ,QAASyD,CAAY,EAC7B,SAAS,EACZ,QAASzD,EAAK,+BAA+B,EAC1C,QAAQ,QAASyD,CAAY,EAC7B,SAAS,CACd,EAMMO,EAAwC,CAC5C,GAAGF,EACH,kBAAmBX,GACnB,eAAgBH,GAChB,IAAKhD,EAAK,mEAAoE,GAAG,EAC9E,QAAQ,QAAS,2EAA2E,EAC5F,SAAS,EACZ,WAAY,6EACZ,IAAK,gEACL,KAAM,4NACR,EAMMiE,GAA2C,CAC/C,GAAGD,EACH,GAAIhE,EAAKoC,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAAE,SAAS,EAC3C,KAAMpC,EAAKgE,EAAU,IAAI,EACtB,QAAQ,OAAQ,eAAe,EAC/B,QAAQ,UAAW,GAAG,EACtB,SAAS,CACd,EAMaE,EAAQ,CACnB,OAAQpC,EACR,IAAKE,GACL,SAAUC,EACZ,EAEakC,EAAS,CACpB,OAAQL,EACR,IAAKE,EACL,OAAQC,GACR,SAAUF,EACZ,ECzbA,IAAMK,GAAkD,CACtD,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACP,EACMC,GAAwBC,GAAeF,GAAmBE,CAAE,EAE3D,SAASC,EAAOC,EAAcC,EAAkB,CACrD,GAAIA,GACF,GAAIC,EAAM,WAAW,KAAKF,CAAI,EAC5B,OAAOA,EAAK,QAAQE,EAAM,cAAeL,EAAoB,UAG3DK,EAAM,mBAAmB,KAAKF,CAAI,EACpC,OAAOA,EAAK,QAAQE,EAAM,sBAAuBL,EAAoB,EAIzE,OAAOG,CACT,CAgBO,SAASG,EAASC,EAAc,CACrC,GAAI,CACFA,EAAO,UAAUA,CAAI,EAAE,QAAQC,EAAM,cAAe,GAAG,CACzD,MAAQ,CACN,OAAO,IACT,CACA,OAAOD,CACT,CAEO,SAASE,EAAWC,EAAkBC,EAAgB,CAG3D,IAAMC,EAAMF,EAAS,QAAQF,EAAM,SAAU,CAACK,EAAOC,EAAQC,IAAQ,CACjE,IAAIC,EAAU,GACVC,EAAOH,EACX,KAAO,EAAEG,GAAQ,GAAKF,EAAIE,CAAI,IAAM,MAAMD,EAAU,CAACA,EACrD,OAAIA,EAGK,IAGA,IAEX,CAAC,EACDE,EAAQN,EAAI,MAAMJ,EAAM,SAAS,EAC/BW,EAAI,EAUR,GAPKD,EAAM,CAAC,EAAE,KAAK,GACjBA,EAAM,MAAM,EAEVA,EAAM,OAAS,GAAK,CAACA,EAAM,GAAG,EAAE,GAAG,KAAK,GAC1CA,EAAM,IAAI,EAGRP,EACF,GAAIO,EAAM,OAASP,EACjBO,EAAM,OAAOP,CAAK,MAElB,MAAOO,EAAM,OAASP,GAAOO,EAAM,KAAK,EAAE,EAI9C,KAAOC,EAAID,EAAM,OAAQC,IAEvBD,EAAMC,CAAC,EAAID,EAAMC,CAAC,EAAE,KAAK,EAAE,QAAQX,EAAM,UAAW,GAAG,EAEzD,OAAOU,CACT,CAUO,SAASE,EAAML,EAAaM,EAAWC,EAAkB,CAC9D,IAAMC,EAAIR,EAAI,OACd,GAAIQ,IAAM,EACR,MAAO,GAIT,IAAIC,EAAU,EAGd,KAAOA,EAAUD,GAAG,CAClB,IAAME,EAAWV,EAAI,OAAOQ,EAAIC,EAAU,CAAC,EAC3C,GAAIC,IAAaJ,GAAK,CAACC,EACrBE,YACSC,IAAaJ,GAAKC,EAC3BE,QAEA,MAEJ,CAEA,OAAOT,EAAI,MAAM,EAAGQ,EAAIC,CAAO,CACjC,CAEO,SAASE,GAAmBX,EAAaY,EAAW,CACzD,GAAIZ,EAAI,QAAQY,EAAE,CAAC,CAAC,IAAM,GACxB,MAAO,GAGT,IAAIC,EAAQ,EACZ,QAAST,EAAI,EAAGA,EAAIJ,EAAI,OAAQI,IAC9B,GAAIJ,EAAII,CAAC,IAAM,KACbA,YACSJ,EAAII,CAAC,IAAMQ,EAAE,CAAC,EACvBC,YACSb,EAAII,CAAC,IAAMQ,EAAE,CAAC,IACvBC,IACIA,EAAQ,GACV,OAAOT,EAIb,OAAIS,EAAQ,EACH,GAGF,EACT,CCzIA,SAASC,GAAWC,EAAeC,EAA2CC,EAAaC,EAAeC,EAA0C,CAClJ,IAAMC,EAAOJ,EAAK,KACZK,EAAQL,EAAK,OAAS,KACtBM,EAAOP,EAAI,CAAC,EAAE,QAAQI,EAAM,MAAM,kBAAmB,IAAI,EAE/DD,EAAM,MAAM,OAAS,GACrB,IAAMK,EAAoC,CACxC,KAAMR,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,QAAU,OAC3C,IAAAE,EACA,KAAAG,EACA,MAAAC,EACA,KAAAC,EACA,OAAQJ,EAAM,aAAaI,CAAI,CACjC,EACA,OAAAJ,EAAM,MAAM,OAAS,GACdK,CACT,CAEA,SAASC,GAAuBP,EAAaK,EAAcH,EAAc,CACvE,IAAMM,EAAoBR,EAAI,MAAME,EAAM,MAAM,sBAAsB,EAEtE,GAAIM,IAAsB,KACxB,OAAOH,EAGT,IAAMI,EAAeD,EAAkB,CAAC,EAExC,OAAOH,EACJ,MAAM;AAAA,CAAI,EACV,IAAIK,GAAQ,CACX,IAAMC,EAAoBD,EAAK,MAAMR,EAAM,MAAM,cAAc,EAC/D,GAAIS,IAAsB,KACxB,OAAOD,EAGT,GAAM,CAACE,CAAY,EAAID,EAEvB,OAAIC,EAAa,QAAUH,EAAa,OAC/BC,EAAK,MAAMD,EAAa,MAAM,EAGhCC,CACT,CAAC,EACA,KAAK;AAAA,CAAI,CACd,CAKO,IAAMG,EAAN,KAAiE,CACtE,QACA,MACA,MAEA,YAAYC,EAAuD,CACjE,KAAK,QAAUA,GAAWC,CAC5B,CAEA,MAAMC,EAAuC,CAC3C,IAAMlB,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKkB,CAAG,EAC7C,GAAIlB,GAAOA,EAAI,CAAC,EAAE,OAAS,EACzB,MAAO,CACL,KAAM,QACN,IAAKA,EAAI,CAAC,CACZ,CAEJ,CAEA,KAAKkB,EAAsC,CACzC,IAAMlB,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKkB,CAAG,EAC1C,GAAIlB,EAAK,CACP,IAAMO,EAAOP,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAkB,EAAE,EACjE,MAAO,CACL,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,eAAgB,WAChB,KAAO,KAAK,QAAQ,SAEhBO,EADAY,EAAMZ,EAAM;AAAA,CAAI,CAEtB,CACF,CACF,CAEA,OAAOW,EAAsC,CAC3C,IAAMlB,EAAM,KAAK,MAAM,MAAM,OAAO,KAAKkB,CAAG,EAC5C,GAAIlB,EAAK,CACP,IAAME,EAAMF,EAAI,CAAC,EACXO,EAAOE,GAAuBP,EAAKF,EAAI,CAAC,GAAK,GAAI,KAAK,KAAK,EAEjE,MAAO,CACL,KAAM,OACN,IAAAE,EACA,KAAMF,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACpF,KAAAO,CACF,CACF,CACF,CAEA,QAAQW,EAAyC,CAC/C,IAAMlB,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKkB,CAAG,EAC7C,GAAIlB,EAAK,CACP,IAAIO,EAAOP,EAAI,CAAC,EAAE,KAAK,EAGvB,GAAI,KAAK,MAAM,MAAM,WAAW,KAAKO,CAAI,EAAG,CAC1C,IAAMa,EAAUD,EAAMZ,EAAM,GAAG,GAC3B,KAAK,QAAQ,UAEN,CAACa,GAAW,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAO,KAElEb,EAAOa,EAAQ,KAAK,EAExB,CAEA,MAAO,CACL,KAAM,UACN,IAAKpB,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OACd,KAAAO,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAChC,CACF,CACF,CAEA,GAAGW,EAAoC,CACrC,IAAMlB,EAAM,KAAK,MAAM,MAAM,GAAG,KAAKkB,CAAG,EACxC,GAAIlB,EACF,MAAO,CACL,KAAM,KACN,IAAKmB,EAAMnB,EAAI,CAAC,EAAG;AAAA,CAAI,CACzB,CAEJ,CAEA,WAAWkB,EAA4C,CACrD,IAAMlB,EAAM,KAAK,MAAM,MAAM,WAAW,KAAKkB,CAAG,EAChD,GAAIlB,EAAK,CACP,IAAIqB,EAAQF,EAAMnB,EAAI,CAAC,EAAG;AAAA,CAAI,EAAE,MAAM;AAAA,CAAI,EACtCE,EAAM,GACNK,EAAO,GACLe,EAAkB,CAAC,EAEzB,KAAOD,EAAM,OAAS,GAAG,CACvB,IAAIE,EAAe,GACbC,EAAe,CAAC,EAElBC,EACJ,IAAKA,EAAI,EAAGA,EAAIJ,EAAM,OAAQI,IAE5B,GAAI,KAAK,MAAM,MAAM,gBAAgB,KAAKJ,EAAMI,CAAC,CAAC,EAChDD,EAAa,KAAKH,EAAMI,CAAC,CAAC,EAC1BF,EAAe,WACN,CAACA,EACVC,EAAa,KAAKH,EAAMI,CAAC,CAAC,MAE1B,OAGJJ,EAAQA,EAAM,MAAMI,CAAC,EAErB,IAAMC,EAAaF,EAAa,KAAK;AAAA,CAAI,EACnCG,EAAcD,EAEjB,QAAQ,KAAK,MAAM,MAAM,wBAAyB;AAAA,OAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,yBAA0B,EAAE,EACxDxB,EAAMA,EAAM,GAAGA,CAAG;AAAA,EAAKwB,CAAU,GAAKA,EACtCnB,EAAOA,EAAO,GAAGA,CAAI;AAAA,EAAKoB,CAAW,GAAKA,EAI1C,IAAMC,EAAM,KAAK,MAAM,MAAM,IAM7B,GALA,KAAK,MAAM,MAAM,IAAM,GACvB,KAAK,MAAM,YAAYD,EAAaL,EAAQ,EAAI,EAChD,KAAK,MAAM,MAAM,IAAMM,EAGnBP,EAAM,SAAW,EACnB,MAGF,IAAMQ,EAAYP,EAAO,GAAG,EAAE,EAE9B,GAAIO,GAAW,OAAS,OAEtB,MACK,GAAIA,GAAW,OAAS,aAAc,CAE3C,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;AAAA,EAAOT,EAAM,KAAK;AAAA,CAAI,EAC/CW,EAAW,KAAK,WAAWD,CAAO,EACxCT,EAAOA,EAAO,OAAS,CAAC,EAAIU,EAE5B9B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAAS4B,EAAS,IAAI,MAAM,EAAIE,EAAS,IACpEzB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASuB,EAAS,KAAK,MAAM,EAAIE,EAAS,KACxE,KACF,SAAWH,GAAW,OAAS,OAAQ,CAErC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;AAAA,EAAOT,EAAM,KAAK;AAAA,CAAI,EAC/CW,EAAW,KAAK,KAAKD,CAAO,EAClCT,EAAOA,EAAO,OAAS,CAAC,EAAIU,EAE5B9B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAAS2B,EAAU,IAAI,MAAM,EAAIG,EAAS,IACrEzB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASuB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACvEX,EAAQU,EAAQ,UAAUT,EAAO,GAAG,EAAE,EAAG,IAAI,MAAM,EAAE,MAAM;AAAA,CAAI,EAC/D,QACF,CACF,CAEA,MAAO,CACL,KAAM,aACN,IAAApB,EACA,OAAAoB,EACA,KAAAf,CACF,CACF,CACF,CAEA,KAAKW,EAAsC,CACzC,IAAIlB,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKkB,CAAG,EACxC,GAAIlB,EAAK,CACP,IAAIiC,EAAOjC,EAAI,CAAC,EAAE,KAAK,EACjBkC,EAAYD,EAAK,OAAS,EAE1BE,EAAoB,CACxB,KAAM,OACN,IAAK,GACL,QAASD,EACT,MAAOA,EAAY,CAACD,EAAK,MAAM,EAAG,EAAE,EAAI,GACxC,MAAO,GACP,MAAO,CAAC,CACV,EAEAA,EAAOC,EAAY,aAAaD,EAAK,MAAM,EAAE,CAAC,GAAK,KAAKA,CAAI,GAExD,KAAK,QAAQ,WACfA,EAAOC,EAAYD,EAAO,SAI5B,IAAMG,EAAY,KAAK,MAAM,MAAM,cAAcH,CAAI,EACjDI,EAAoB,GAExB,KAAOnB,GAAK,CACV,IAAIoB,EAAW,GACXpC,EAAM,GACNqC,EAAe,GAKnB,GAJI,EAAEvC,EAAMoC,EAAU,KAAKlB,CAAG,IAI1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC9B,MAGFhB,EAAMF,EAAI,CAAC,EACXkB,EAAMA,EAAI,UAAUhB,EAAI,MAAM,EAE9B,IAAIsC,EAAOxC,EAAI,CAAC,EAAE,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAkByC,GAAc,IAAI,OAAO,EAAIA,EAAE,MAAM,CAAC,EACjHC,EAAWxB,EAAI,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAC/ByB,EAAY,CAACH,EAAK,KAAK,EAEvBI,EAAS,EAmBb,GAlBI,KAAK,QAAQ,UACfA,EAAS,EACTL,EAAeC,EAAK,UAAU,GACrBG,EACTC,EAAS5C,EAAI,CAAC,EAAE,OAAS,GAEzB4C,EAAS5C,EAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,EACpD4C,EAASA,EAAS,EAAI,EAAIA,EAC1BL,EAAeC,EAAK,MAAMI,CAAM,EAChCA,GAAU5C,EAAI,CAAC,EAAE,QAGf2C,GAAa,KAAK,MAAM,MAAM,UAAU,KAAKD,CAAQ,IACvDxC,GAAOwC,EAAW;AAAA,EAClBxB,EAAMA,EAAI,UAAUwB,EAAS,OAAS,CAAC,EACvCJ,EAAW,IAGT,CAACA,EAAU,CACb,IAAMO,EAAkB,KAAK,MAAM,MAAM,gBAAgBD,CAAM,EACzDE,GAAU,KAAK,MAAM,MAAM,QAAQF,CAAM,EACzCG,GAAmB,KAAK,MAAM,MAAM,iBAAiBH,CAAM,EAC3DI,GAAoB,KAAK,MAAM,MAAM,kBAAkBJ,CAAM,EAC7DK,GAAiB,KAAK,MAAM,MAAM,eAAeL,CAAM,EAG7D,KAAO1B,GAAK,CACV,IAAMgC,EAAUhC,EAAI,MAAM;AAAA,EAAM,CAAC,EAAE,CAAC,EAChCiC,EAgCJ,GA/BAT,EAAWQ,EAGP,KAAK,QAAQ,UACfR,EAAWA,EAAS,QAAQ,KAAK,MAAM,MAAM,mBAAoB,IAAI,EACrES,EAAsBT,GAEtBS,EAAsBT,EAAS,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAI3EK,GAAiB,KAAKL,CAAQ,GAK9BM,GAAkB,KAAKN,CAAQ,GAK/BO,GAAe,KAAKP,CAAQ,GAK5BG,EAAgB,KAAKH,CAAQ,GAK7BI,GAAQ,KAAKJ,CAAQ,EACvB,MAGF,GAAIS,EAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,GAAKP,GAAU,CAACF,EAAS,KAAK,EACxFH,GAAgB;AAAA,EAAOY,EAAoB,MAAMP,CAAM,MAClD,CAgBL,GAdID,GAKAH,EAAK,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,GAAK,GAG9FO,GAAiB,KAAKP,CAAI,GAG1BQ,GAAkB,KAAKR,CAAI,GAG3BM,GAAQ,KAAKN,CAAI,EACnB,MAGFD,GAAgB;AAAA,EAAOG,CACzB,CAEI,CAACC,GAAa,CAACD,EAAS,KAAK,IAC/BC,EAAY,IAGdzC,GAAOgD,EAAU;AAAA,EACjBhC,EAAMA,EAAI,UAAUgC,EAAQ,OAAS,CAAC,EACtCV,EAAOW,EAAoB,MAAMP,CAAM,CACzC,CACF,CAEKT,EAAK,QAEJE,EACFF,EAAK,MAAQ,GACJ,KAAK,MAAM,MAAM,gBAAgB,KAAKjC,CAAG,IAClDmC,EAAoB,KAIxB,IAAIe,EAAiC,KACjCC,GAEA,KAAK,QAAQ,MACfD,EAAS,KAAK,MAAM,MAAM,WAAW,KAAKb,CAAY,EAClDa,IACFC,GAAYD,EAAO,CAAC,IAAM,OAC1Bb,EAAeA,EAAa,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,IAI5EJ,EAAK,MAAM,KAAK,CACd,KAAM,YACN,IAAAjC,EACA,KAAM,CAAC,CAACkD,EACR,QAASC,GACT,MAAO,GACP,KAAMd,EACN,OAAQ,CAAC,CACX,CAAC,EAEDJ,EAAK,KAAOjC,CACd,CAGA,IAAMoD,EAAWnB,EAAK,MAAM,GAAG,EAAE,EACjC,GAAImB,EACFA,EAAS,IAAMA,EAAS,IAAI,QAAQ,EACpCA,EAAS,KAAOA,EAAS,KAAK,QAAQ,MAGtC,QAEFnB,EAAK,IAAMA,EAAK,IAAI,QAAQ,EAG5B,QAASV,EAAI,EAAGA,EAAIU,EAAK,MAAM,OAAQV,IAIrC,GAHA,KAAK,MAAM,MAAM,IAAM,GACvBU,EAAK,MAAMV,CAAC,EAAE,OAAS,KAAK,MAAM,YAAYU,EAAK,MAAMV,CAAC,EAAE,KAAM,CAAC,CAAC,EAEhE,CAACU,EAAK,MAAO,CAEf,IAAMoB,EAAUpB,EAAK,MAAMV,CAAC,EAAE,OAAO,OAAOgB,GAAKA,EAAE,OAAS,OAAO,EAC7De,EAAwBD,EAAQ,OAAS,GAAKA,EAAQ,KAAKd,GAAK,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAE,GAAG,CAAC,EAE1GN,EAAK,MAAQqB,CACf,CAIF,GAAIrB,EAAK,MACP,QAASV,EAAI,EAAGA,EAAIU,EAAK,MAAM,OAAQV,IACrCU,EAAK,MAAMV,CAAC,EAAE,MAAQ,GAI1B,OAAOU,CACT,CACF,CAEA,KAAKjB,EAAsC,CACzC,IAAMlB,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKkB,CAAG,EAC1C,GAAIlB,EAQF,MAP2B,CACzB,KAAM,OACN,MAAO,GACP,IAAKA,EAAI,CAAC,EACV,IAAKA,EAAI,CAAC,IAAM,OAASA,EAAI,CAAC,IAAM,UAAYA,EAAI,CAAC,IAAM,QAC3D,KAAMA,EAAI,CAAC,CACb,CAGJ,CAEA,IAAIkB,EAAqC,CACvC,IAAMlB,EAAM,KAAK,MAAM,MAAM,IAAI,KAAKkB,CAAG,EACzC,GAAIlB,EAAK,CACP,IAAMyD,EAAMzD,EAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EAC5EK,EAAOL,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,aAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAI,GACtHM,EAAQN,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGA,EAAI,CAAC,EAAE,OAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACrH,MAAO,CACL,KAAM,MACN,IAAAyD,EACA,IAAKzD,EAAI,CAAC,EACV,KAAAK,EACA,MAAAC,CACF,CACF,CACF,CAEA,MAAMY,EAAuC,CAC3C,IAAMlB,EAAM,KAAK,MAAM,MAAM,MAAM,KAAKkB,CAAG,EAK3C,GAJI,CAAClB,GAID,CAAC,KAAK,MAAM,MAAM,eAAe,KAAKA,EAAI,CAAC,CAAC,EAE9C,OAGF,IAAM0D,EAAUC,EAAW3D,EAAI,CAAC,CAAC,EAC3B4D,EAAS5D,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,EAAE,MAAM,GAAG,EACvE6D,EAAO7D,EAAI,CAAC,GAAG,KAAK,EAAIA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,EAAE,EAAE,MAAM;AAAA,CAAI,EAAI,CAAC,EAE9F8D,EAAqB,CACzB,KAAM,QACN,IAAK9D,EAAI,CAAC,EACV,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,KAAM,CAAC,CACT,EAEA,GAAI0D,EAAQ,SAAWE,EAAO,OAK9B,SAAWG,KAASH,EACd,KAAK,MAAM,MAAM,gBAAgB,KAAKG,CAAK,EAC7CD,EAAK,MAAM,KAAK,OAAO,EACd,KAAK,MAAM,MAAM,iBAAiB,KAAKC,CAAK,EACrDD,EAAK,MAAM,KAAK,QAAQ,EACf,KAAK,MAAM,MAAM,eAAe,KAAKC,CAAK,EACnDD,EAAK,MAAM,KAAK,MAAM,EAEtBA,EAAK,MAAM,KAAK,IAAI,EAIxB,QAASrC,EAAI,EAAGA,EAAIiC,EAAQ,OAAQjC,IAClCqC,EAAK,OAAO,KAAK,CACf,KAAMJ,EAAQjC,CAAC,EACf,OAAQ,KAAK,MAAM,OAAOiC,EAAQjC,CAAC,CAAC,EACpC,OAAQ,GACR,MAAOqC,EAAK,MAAMrC,CAAC,CACrB,CAAC,EAGH,QAAWuC,KAAOH,EAChBC,EAAK,KAAK,KAAKH,EAAWK,EAAKF,EAAK,OAAO,MAAM,EAAE,IAAI,CAACG,EAAMxC,KACrD,CACL,KAAMwC,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAI,EAC9B,OAAQ,GACR,MAAOH,EAAK,MAAMrC,CAAC,CACrB,EACD,CAAC,EAGJ,OAAOqC,EACT,CAEA,SAAS5C,EAAyC,CAChD,IAAMlB,EAAM,KAAK,MAAM,MAAM,SAAS,KAAKkB,CAAG,EAC9C,GAAIlB,EACF,MAAO,CACL,KAAM,UACN,IAAKA,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,EAAI,EACtC,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAClC,CAEJ,CAEA,UAAUkB,EAA2C,CACnD,IAAMlB,EAAM,KAAK,MAAM,MAAM,UAAU,KAAKkB,CAAG,EAC/C,GAAIlB,EAAK,CACP,IAAMO,EAAOP,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,OAAS,CAAC,IAAM;AAAA,EAC9CA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAClBA,EAAI,CAAC,EACT,MAAO,CACL,KAAM,YACN,IAAKA,EAAI,CAAC,EACV,KAAAO,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAChC,CACF,CACF,CAEA,KAAKW,EAAsC,CACzC,IAAMlB,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKkB,CAAG,EAC1C,GAAIlB,EACF,MAAO,CACL,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAClC,CAEJ,CAEA,OAAOkB,EAAwC,CAC7C,IAAMlB,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKkB,CAAG,EAC7C,GAAIlB,EACF,MAAO,CACL,KAAM,SACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,CACb,CAEJ,CAEA,IAAIkB,EAAqC,CACvC,IAAMlB,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKkB,CAAG,EAC1C,GAAIlB,EACF,MAAI,CAAC,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,UAAU,KAAKA,EAAI,CAAC,CAAC,EACpE,KAAK,MAAM,MAAM,OAAS,GACjB,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAI,CAAC,CAAC,IACxE,KAAK,MAAM,MAAM,OAAS,IAExB,CAAC,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,kBAAkB,KAAKA,EAAI,CAAC,CAAC,EAChF,KAAK,MAAM,MAAM,WAAa,GACrB,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,gBAAgB,KAAKA,EAAI,CAAC,CAAC,IACpF,KAAK,MAAM,MAAM,WAAa,IAGzB,CACL,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,OAAQ,KAAK,MAAM,MAAM,OACzB,WAAY,KAAK,MAAM,MAAM,WAC7B,MAAO,GACP,KAAMA,EAAI,CAAC,CACb,CAEJ,CAEA,KAAKkB,EAAqD,CACxD,IAAMlB,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKkB,CAAG,EAC3C,GAAIlB,EAAK,CACP,IAAMkE,EAAalE,EAAI,CAAC,EAAE,KAAK,EAC/B,GAAI,CAAC,KAAK,QAAQ,UAAY,KAAK,MAAM,MAAM,kBAAkB,KAAKkE,CAAU,EAAG,CAEjF,GAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAU,EACpD,OAIF,IAAMC,EAAahD,EAAM+C,EAAW,MAAM,EAAG,EAAE,EAAG,IAAI,EACtD,IAAKA,EAAW,OAASC,EAAW,QAAU,IAAM,EAClD,MAEJ,KAAO,CAEL,IAAMC,EAAiBC,GAAmBrE,EAAI,CAAC,EAAG,IAAI,EACtD,GAAIoE,IAAmB,GAErB,OAGF,GAAIA,EAAiB,GAAI,CAEvB,IAAME,GADQtE,EAAI,CAAC,EAAE,QAAQ,GAAG,IAAM,EAAI,EAAI,GACtBA,EAAI,CAAC,EAAE,OAASoE,EACxCpE,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGoE,CAAc,EAC3CpE,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGsE,CAAO,EAAE,KAAK,EAC3CtE,EAAI,CAAC,EAAI,EACX,CACF,CACA,IAAIK,EAAOL,EAAI,CAAC,EACZM,EAAQ,GACZ,GAAI,KAAK,QAAQ,SAAU,CAEzB,IAAML,EAAO,KAAK,MAAM,MAAM,kBAAkB,KAAKI,CAAI,EAErDJ,IACFI,EAAOJ,EAAK,CAAC,EACbK,EAAQL,EAAK,CAAC,EAElB,MACEK,EAAQN,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAAI,GAGzC,OAAAK,EAAOA,EAAK,KAAK,EACb,KAAK,MAAM,MAAM,kBAAkB,KAAKA,CAAI,IAC1C,KAAK,QAAQ,UAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK6D,CAAU,EAE7E7D,EAAOA,EAAK,MAAM,CAAC,EAEnBA,EAAOA,EAAK,MAAM,EAAG,EAAE,GAGpBN,GAAWC,EAAK,CACrB,KAAMK,GAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAChE,MAAOC,GAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,CACrE,EAAGN,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CACnC,CACF,CAEA,QAAQkB,EAAaqD,EAAoE,CACvF,IAAIvE,EACJ,IAAKA,EAAM,KAAK,MAAM,OAAO,QAAQ,KAAKkB,CAAG,KACvClB,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKkB,CAAG,GAAI,CAC/C,IAAMsD,GAAcxE,EAAI,CAAC,GAAKA,EAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EACjFC,EAAOsE,EAAMC,EAAW,YAAY,CAAC,EAC3C,GAAI,CAACvE,EAAM,CACT,IAAMM,EAAOP,EAAI,CAAC,EAAE,OAAO,CAAC,EAC5B,MAAO,CACL,KAAM,OACN,IAAKO,EACL,KAAAA,CACF,CACF,CACA,OAAOR,GAAWC,EAAKC,EAAMD,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CAC7D,CACF,CAEA,SAASkB,EAAauD,EAAmBC,EAAW,GAA2C,CAC7F,IAAIC,EAAQ,KAAK,MAAM,OAAO,eAAe,KAAKzD,CAAG,EAIrD,GAHI,CAACyD,GAGDA,EAAM,CAAC,GAAKD,EAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAAG,OAItE,GAAI,EAFaC,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,KAExB,CAACD,GAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,EAAG,CAE1E,IAAME,EAAU,CAAC,GAAGD,EAAM,CAAC,CAAC,EAAE,OAAS,EACnCE,EAAQC,EAASC,EAAaH,EAASI,EAAgB,EAErDC,EAASN,EAAM,CAAC,EAAE,CAAC,IAAM,IAAM,KAAK,MAAM,OAAO,kBAAoB,KAAK,MAAM,OAAO,kBAM7F,IALAM,EAAO,UAAY,EAGnBR,EAAYA,EAAU,MAAM,GAAKvD,EAAI,OAAS0D,CAAO,GAE7CD,EAAQM,EAAO,KAAKR,CAAS,IAAM,MAAM,CAG/C,GAFAI,EAASF,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,EAExE,CAACE,EAAQ,SAIb,GAFAC,EAAU,CAAC,GAAGD,CAAM,EAAE,OAElBF,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAG,CACxBI,GAAcD,EACd,QACF,UAAWH,EAAM,CAAC,GAAKA,EAAM,CAAC,IACxBC,EAAU,GAAK,GAAGA,EAAUE,GAAW,GAAI,CAC7CE,GAAiBF,EACjB,QACF,CAKF,GAFAC,GAAcD,EAEVC,EAAa,EAAG,SAGpBD,EAAU,KAAK,IAAIA,EAASA,EAAUC,EAAaC,CAAa,EAEhE,IAAME,EAAiB,CAAC,GAAGP,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAClCzE,EAAMgB,EAAI,MAAM,EAAG0D,EAAUD,EAAM,MAAQO,EAAiBJ,CAAO,EAGzE,GAAI,KAAK,IAAIF,EAASE,CAAO,EAAI,EAAG,CAClC,IAAMvE,EAAOL,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACL,KAAM,KACN,IAAAA,EACA,KAAAK,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACtC,CACF,CAGA,IAAMA,EAAOL,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACL,KAAM,SACN,IAAAA,EACA,KAAAK,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACtC,CACF,CACF,CACF,CAEA,SAASW,EAA0C,CACjD,IAAMlB,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKkB,CAAG,EAC3C,GAAIlB,EAAK,CACP,IAAIO,EAAOP,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,GAAG,EAC3DmF,EAAmB,KAAK,MAAM,MAAM,aAAa,KAAK5E,CAAI,EAC1D6E,EAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK7E,CAAI,GAAK,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAI,EAC3H,OAAI4E,GAAoBC,IACtB7E,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAAS,CAAC,GAEnC,CACL,KAAM,WACN,IAAKP,EAAI,CAAC,EACV,KAAAO,CACF,CACF,CACF,CAEA,GAAGW,EAAoC,CACrC,IAAMlB,EAAM,KAAK,MAAM,OAAO,GAAG,KAAKkB,CAAG,EACzC,GAAIlB,EACF,MAAO,CACL,KAAM,KACN,IAAKA,EAAI,CAAC,CACZ,CAEJ,CAEA,IAAIkB,EAAqC,CACvC,IAAMlB,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKkB,CAAG,EAC1C,GAAIlB,EACF,MAAO,CACL,KAAM,MACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,CACxC,CAEJ,CAEA,SAASkB,EAAsC,CAC7C,IAAMlB,EAAM,KAAK,MAAM,OAAO,SAAS,KAAKkB,CAAG,EAC/C,GAAIlB,EAAK,CACP,IAAIO,EAAMF,EACV,OAAIL,EAAI,CAAC,IAAM,KACbO,EAAOP,EAAI,CAAC,EACZK,EAAO,UAAYE,IAEnBA,EAAOP,EAAI,CAAC,EACZK,EAAOE,GAGF,CACL,KAAM,OACN,IAAKP,EAAI,CAAC,EACV,KAAAO,EACA,KAAAF,EACA,OAAQ,CACN,CACE,KAAM,OACN,IAAKE,EACL,KAAAA,CACF,CACF,CACF,CACF,CACF,CAEA,IAAIW,EAAsC,CACxC,IAAIlB,EACJ,GAAIA,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKkB,CAAG,EAAG,CACzC,IAAIX,EAAMF,EACV,GAAIL,EAAI,CAAC,IAAM,IACbO,EAAOP,EAAI,CAAC,EACZK,EAAO,UAAYE,MACd,CAEL,IAAI8E,EACJ,GACEA,EAAcrF,EAAI,CAAC,EACnBA,EAAI,CAAC,EAAI,KAAK,MAAM,OAAO,WAAW,KAAKA,EAAI,CAAC,CAAC,IAAI,CAAC,GAAK,SACpDqF,IAAgBrF,EAAI,CAAC,GAC9BO,EAAOP,EAAI,CAAC,EACRA,EAAI,CAAC,IAAM,OACbK,EAAO,UAAYL,EAAI,CAAC,EAExBK,EAAOL,EAAI,CAAC,CAEhB,CACA,MAAO,CACL,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAO,EACA,KAAAF,EACA,OAAQ,CACN,CACE,KAAM,OACN,IAAKE,EACL,KAAAA,CACF,CACF,CACF,CACF,CACF,CAEA,WAAWW,EAAsC,CAC/C,IAAMlB,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKkB,CAAG,EAC3C,GAAIlB,EAAK,CACP,IAAMsF,EAAU,KAAK,MAAM,MAAM,WACjC,MAAO,CACL,KAAM,OACN,IAAKtF,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,QAAAsF,CACF,CACF,CACF,CACF,ECn2BO,IAAMC,EAAN,MAAMC,CAAuD,CAClE,OACA,QACA,MAMQ,UACA,YAER,YAAYC,EAAuD,CAEjE,KAAK,OAAS,CAAC,EACf,KAAK,OAAO,MAAQ,OAAO,OAAO,IAAI,EACtC,KAAK,QAAUA,GAAWC,EAC1B,KAAK,QAAQ,UAAY,KAAK,QAAQ,WAAa,IAAIC,EACvD,KAAK,UAAY,KAAK,QAAQ,UAC9B,KAAK,UAAU,QAAU,KAAK,QAC9B,KAAK,UAAU,MAAQ,KACvB,KAAK,YAAc,CAAC,EACpB,KAAK,MAAQ,CACX,OAAQ,GACR,WAAY,GACZ,IAAK,EACP,EAEA,IAAMC,EAAQ,CACZ,MAAAC,EACA,MAAOC,EAAM,OACb,OAAQC,EAAO,MACjB,EAEI,KAAK,QAAQ,UACfH,EAAM,MAAQE,EAAM,SACpBF,EAAM,OAASG,EAAO,UACb,KAAK,QAAQ,MACtBH,EAAM,MAAQE,EAAM,IAChB,KAAK,QAAQ,OACfF,EAAM,OAASG,EAAO,OAEtBH,EAAM,OAASG,EAAO,KAG1B,KAAK,UAAU,MAAQH,CACzB,CAKA,WAAW,OAAQ,CACjB,MAAO,CACL,MAAAE,EACA,OAAAC,CACF,CACF,CAKA,OAAO,IAAoDC,EAAaP,EAAuD,CAE7H,OADc,IAAID,EAAqCC,CAAO,EACjD,IAAIO,CAAG,CACtB,CAKA,OAAO,UAA0DA,EAAaP,EAAuD,CAEnI,OADc,IAAID,EAAqCC,CAAO,EACjD,aAAaO,CAAG,CAC/B,CAKA,IAAIA,EAAa,CACfA,EAAMA,EAAI,QAAQH,EAAM,eAAgB;AAAA,CAAI,EAE5C,KAAK,YAAYG,EAAK,KAAK,MAAM,EAEjC,QAASC,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAAK,CAChD,IAAMC,EAAO,KAAK,YAAYD,CAAC,EAC/B,KAAK,aAAaC,EAAK,IAAKA,EAAK,MAAM,CACzC,CACA,YAAK,YAAc,CAAC,EAEb,KAAK,MACd,CAOA,YAAYF,EAAaG,EAAkB,CAAC,EAAGC,EAAuB,GAAO,CAK3E,IAJI,KAAK,QAAQ,WACfJ,EAAMA,EAAI,QAAQH,EAAM,cAAe,MAAM,EAAE,QAAQA,EAAM,UAAW,EAAE,GAGrEG,GAAK,CACV,IAAIK,EAEJ,GAAI,KAAK,QAAQ,YAAY,OAAO,KAAMC,IACpCD,EAAQC,EAAa,KAAK,CAAE,MAAO,IAAK,EAAGN,EAAKG,CAAM,IACxDH,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACV,IAEF,EACR,EACC,SAIF,GAAIA,EAAQ,KAAK,UAAU,MAAML,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,IAAME,EAAYJ,EAAO,GAAG,EAAE,EAC1BE,EAAM,IAAI,SAAW,GAAKE,IAAc,OAG1CA,EAAU,KAAO;AAAA,EAEjBJ,EAAO,KAAKE,CAAK,EAEnB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,KAAKL,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,IAAME,EAAYJ,EAAO,GAAG,EAAE,EAE1BI,GAAW,OAAS,aAAeA,GAAW,OAAS,QACzDA,EAAU,KAAO;AAAA,EAAOF,EAAM,IAC9BE,EAAU,MAAQ;AAAA,EAAOF,EAAM,KAC/B,KAAK,YAAY,GAAG,EAAE,EAAG,IAAME,EAAU,MAEzCJ,EAAO,KAAKE,CAAK,EAEnB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,OAAOL,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,QAAQL,CAAG,EAAG,CACvCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,GAAGL,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,WAAWL,CAAG,EAAG,CAC1CA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,KAAKL,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,KAAKL,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,IAAIL,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,IAAME,EAAYJ,EAAO,GAAG,EAAE,EAC1BI,GAAW,OAAS,aAAeA,GAAW,OAAS,QACzDA,EAAU,KAAO;AAAA,EAAOF,EAAM,IAC9BE,EAAU,MAAQ;AAAA,EAAOF,EAAM,IAC/B,KAAK,YAAY,GAAG,EAAE,EAAG,IAAME,EAAU,MAC/B,KAAK,OAAO,MAAMF,EAAM,GAAG,IACrC,KAAK,OAAO,MAAMA,EAAM,GAAG,EAAI,CAC7B,KAAMA,EAAM,KACZ,MAAOA,EAAM,KACf,GAEF,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,MAAML,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,SAASL,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAIA,IAAIG,EAASR,EACb,GAAI,KAAK,QAAQ,YAAY,WAAY,CACvC,IAAIS,EAAa,IACXC,EAAUV,EAAI,MAAM,CAAC,EACvBW,EACJ,KAAK,QAAQ,WAAW,WAAW,QAASC,GAAkB,CAC5DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAK,EAAGF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAChDF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAE/C,CAAC,EACGF,EAAa,KAAYA,GAAc,IACzCD,EAASR,EAAI,UAAU,EAAGS,EAAa,CAAC,EAE5C,CACA,GAAI,KAAK,MAAM,MAAQJ,EAAQ,KAAK,UAAU,UAAUG,CAAM,GAAI,CAChE,IAAMD,EAAYJ,EAAO,GAAG,EAAE,EAC1BC,GAAwBG,GAAW,OAAS,aAC9CA,EAAU,KAAO;AAAA,EAAOF,EAAM,IAC9BE,EAAU,MAAQ;AAAA,EAAOF,EAAM,KAC/B,KAAK,YAAY,IAAI,EACrB,KAAK,YAAY,GAAG,EAAE,EAAG,IAAME,EAAU,MAEzCJ,EAAO,KAAKE,CAAK,EAEnBD,EAAuBI,EAAO,SAAWR,EAAI,OAC7CA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,KAAKL,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,IAAME,EAAYJ,EAAO,GAAG,EAAE,EAC1BI,GAAW,OAAS,QACtBA,EAAU,KAAO;AAAA,EAAOF,EAAM,IAC9BE,EAAU,MAAQ;AAAA,EAAOF,EAAM,KAC/B,KAAK,YAAY,IAAI,EACrB,KAAK,YAAY,GAAG,EAAE,EAAG,IAAME,EAAU,MAEzCJ,EAAO,KAAKE,CAAK,EAEnB,QACF,CAEA,GAAIL,EAAK,CACP,IAAMa,EAAS,0BAA4Bb,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACvB,QAAQ,MAAMa,CAAM,EACpB,KACF,KACE,OAAM,IAAI,MAAMA,CAAM,CAE1B,CACF,CAEA,YAAK,MAAM,IAAM,GACVV,CACT,CAEA,OAAOH,EAAaG,EAAkB,CAAC,EAAG,CACxC,YAAK,YAAY,KAAK,CAAE,IAAAH,EAAK,OAAAG,CAAO,CAAC,EAC9BA,CACT,CAKA,aAAaH,EAAaG,EAAkB,CAAC,EAAY,CAEvD,IAAIW,EAAYd,EACZe,EAAgC,KAGpC,GAAI,KAAK,OAAO,MAAO,CACrB,IAAMC,EAAQ,OAAO,KAAK,KAAK,OAAO,KAAK,EAC3C,GAAIA,EAAM,OAAS,EACjB,MAAQD,EAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAKD,CAAS,IAAM,MACxEE,EAAM,SAASD,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,EAAI,EAAG,EAAE,CAAC,IAClED,EAAYA,EAAU,MAAM,EAAGC,EAAM,KAAK,EACtC,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IACxCD,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS,EAI/E,CAGA,MAAQC,EAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAKD,CAAS,IAAM,MAC7EA,EAAYA,EAAU,MAAM,EAAGC,EAAM,KAAK,EAAI,KAAOD,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS,EAI3H,MAAQC,EAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAKD,CAAS,IAAM,MACxEA,EAAYA,EAAU,MAAM,EAAGC,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAMD,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS,EAG7J,IAAIG,EAAe,GACfC,EAAW,GACf,KAAOlB,GAAK,CACLiB,IACHC,EAAW,IAEbD,EAAe,GAEf,IAAIZ,EAGJ,GAAI,KAAK,QAAQ,YAAY,QAAQ,KAAMC,IACrCD,EAAQC,EAAa,KAAK,CAAE,MAAO,IAAK,EAAGN,EAAKG,CAAM,IACxDH,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACV,IAEF,EACR,EACC,SAIF,GAAIA,EAAQ,KAAK,UAAU,OAAOL,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,IAAIL,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,KAAKL,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,QAAQL,EAAK,KAAK,OAAO,KAAK,EAAG,CAC1DA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpC,IAAME,EAAYJ,EAAO,GAAG,EAAE,EAC1BE,EAAM,OAAS,QAAUE,GAAW,OAAS,QAC/CA,EAAU,KAAOF,EAAM,IACvBE,EAAU,MAAQF,EAAM,MAExBF,EAAO,KAAKE,CAAK,EAEnB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,SAASL,EAAKc,EAAWI,CAAQ,EAAG,CAC7DlB,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,SAASL,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,GAAGL,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,IAAIL,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAIA,EAAQ,KAAK,UAAU,SAASL,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAGA,GAAI,CAAC,KAAK,MAAM,SAAWA,EAAQ,KAAK,UAAU,IAAIL,CAAG,GAAI,CAC3DA,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EACpCF,EAAO,KAAKE,CAAK,EACjB,QACF,CAIA,IAAIG,EAASR,EACb,GAAI,KAAK,QAAQ,YAAY,YAAa,CACxC,IAAIS,EAAa,IACXC,EAAUV,EAAI,MAAM,CAAC,EACvBW,EACJ,KAAK,QAAQ,WAAW,YAAY,QAASC,GAAkB,CAC7DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAK,EAAGF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAChDF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAE/C,CAAC,EACGF,EAAa,KAAYA,GAAc,IACzCD,EAASR,EAAI,UAAU,EAAGS,EAAa,CAAC,EAE5C,CACA,GAAIJ,EAAQ,KAAK,UAAU,WAAWG,CAAM,EAAG,CAC7CR,EAAMA,EAAI,UAAUK,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,MAAM,EAAE,IAAM,MAC1Ba,EAAWb,EAAM,IAAI,MAAM,EAAE,GAE/BY,EAAe,GACf,IAAMV,EAAYJ,EAAO,GAAG,EAAE,EAC1BI,GAAW,OAAS,QACtBA,EAAU,KAAOF,EAAM,IACvBE,EAAU,MAAQF,EAAM,MAExBF,EAAO,KAAKE,CAAK,EAEnB,QACF,CAEA,GAAIL,EAAK,CACP,IAAMa,EAAS,0BAA4Bb,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACvB,QAAQ,MAAMa,CAAM,EACpB,KACF,KACE,OAAM,IAAI,MAAMA,CAAM,CAE1B,CACF,CAEA,OAAOV,CACT,CACF,ECxcO,IAAMgB,EAAN,KAAgE,CACrE,QACA,OACA,YAAYC,EAAuD,CACjE,KAAK,QAAUA,GAAWC,CAC5B,CAEA,MAAMC,EAAqC,CACzC,MAAO,EACT,CAEA,KAAK,CAAE,KAAAC,EAAM,KAAAC,EAAM,QAAAC,CAAQ,EAAgC,CACzD,IAAMC,GAAcF,GAAQ,IAAI,MAAMG,EAAM,aAAa,IAAI,CAAC,EAExDC,EAAOL,EAAK,QAAQI,EAAM,cAAe,EAAE,EAAI;AAAA,EAErD,OAAKD,EAME,8BACHG,EAAOH,CAAU,EACjB,MACCD,EAAUG,EAAOC,EAAOD,EAAM,EAAI,GACnC;AAAA,EATK,eACFH,EAAUG,EAAOC,EAAOD,EAAM,EAAI,GACnC;AAAA,CAQR,CAEA,WAAW,CAAE,OAAAE,CAAO,EAAsC,CAExD,MAAO;AAAA,EADM,KAAK,OAAO,MAAMA,CAAM,CACT;AAAA,CAC9B,CAEA,KAAK,CAAE,KAAAP,CAAK,EAA6C,CACvD,OAAOA,CACT,CAEA,QAAQ,CAAE,OAAAO,EAAQ,MAAAC,CAAM,EAAmC,CACzD,MAAO,KAAKA,CAAK,IAAI,KAAK,OAAO,YAAYD,CAAM,CAAC,MAAMC,CAAK;AAAA,CACjE,CAEA,GAAGT,EAAkC,CACnC,MAAO;AAAA,CACT,CAEA,KAAKA,EAAoC,CACvC,IAAMU,EAAUV,EAAM,QAChBW,EAAQX,EAAM,MAEhBY,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIb,EAAM,MAAM,OAAQa,IAAK,CAC3C,IAAMC,EAAOd,EAAM,MAAMa,CAAC,EAC1BD,GAAQ,KAAK,SAASE,CAAI,CAC5B,CAEA,IAAMC,EAAOL,EAAU,KAAO,KACxBM,EAAaN,GAAWC,IAAU,EAAM,WAAaA,EAAQ,IAAO,GAC1E,MAAO,IAAMI,EAAOC,EAAY;AAAA,EAAQJ,EAAO,KAAOG,EAAO;AAAA,CAC/D,CAEA,SAASD,EAAuC,CAC9C,IAAIG,EAAW,GACf,GAAIH,EAAK,KAAM,CACb,IAAMI,EAAW,KAAK,SAAS,CAAE,QAAS,CAAC,CAACJ,EAAK,OAAQ,CAAC,EACtDA,EAAK,MACHA,EAAK,OAAO,CAAC,GAAG,OAAS,aAC3BA,EAAK,OAAO,CAAC,EAAE,KAAOI,EAAW,IAAMJ,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SACjGA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAOI,EAAW,IAAMX,EAAOO,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,EACrFA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAU,KAGrCA,EAAK,OAAO,QAAQ,CAClB,KAAM,OACN,IAAKI,EAAW,IAChB,KAAMA,EAAW,IACjB,QAAS,EACX,CAAC,EAGHD,GAAYC,EAAW,GAE3B,CAEA,OAAAD,GAAY,KAAK,OAAO,MAAMH,EAAK,OAAQ,CAAC,CAACA,EAAK,KAAK,EAEhD,OAAOG,CAAQ;AAAA,CACxB,CAEA,SAAS,CAAE,QAAAE,CAAQ,EAAoC,CACrD,MAAO,WACFA,EAAU,cAAgB,IAC3B,8BACN,CAEA,UAAU,CAAE,OAAAX,CAAO,EAAqC,CACtD,MAAO,MAAM,KAAK,OAAO,YAAYA,CAAM,CAAC;AAAA,CAC9C,CAEA,MAAMR,EAAqC,CACzC,IAAIoB,EAAS,GAGTC,EAAO,GACX,QAASR,EAAI,EAAGA,EAAIb,EAAM,OAAO,OAAQa,IACvCQ,GAAQ,KAAK,UAAUrB,EAAM,OAAOa,CAAC,CAAC,EAExCO,GAAU,KAAK,SAAS,CAAE,KAAMC,CAAqB,CAAC,EAEtD,IAAIT,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIb,EAAM,KAAK,OAAQa,IAAK,CAC1C,IAAMS,EAAMtB,EAAM,KAAKa,CAAC,EAExBQ,EAAO,GACP,QAASE,EAAI,EAAGA,EAAID,EAAI,OAAQC,IAC9BF,GAAQ,KAAK,UAAUC,EAAIC,CAAC,CAAC,EAG/BX,GAAQ,KAAK,SAAS,CAAE,KAAMS,CAAqB,CAAC,CACtD,CACA,OAAIT,IAAMA,EAAO,UAAUA,CAAI,YAExB;AAAA;AAAA,EAEHQ,EACA;AAAA,EACAR,EACA;AAAA,CACN,CAEA,SAAS,CAAE,KAAAX,CAAK,EAAkD,CAChE,MAAO;AAAA,EAASA,CAAI;AAAA,CACtB,CAEA,UAAUD,EAAyC,CACjD,IAAMwB,EAAU,KAAK,OAAO,YAAYxB,EAAM,MAAM,EAC9Ce,EAAOf,EAAM,OAAS,KAAO,KAInC,OAHYA,EAAM,MACd,IAAIe,CAAI,WAAWf,EAAM,KAAK,KAC9B,IAAIe,CAAI,KACCS,EAAU,KAAKT,CAAI;AAAA,CAClC,CAKA,OAAO,CAAE,OAAAP,CAAO,EAAkC,CAChD,MAAO,WAAW,KAAK,OAAO,YAAYA,CAAM,CAAC,WACnD,CAEA,GAAG,CAAE,OAAAA,CAAO,EAA8B,CACxC,MAAO,OAAO,KAAK,OAAO,YAAYA,CAAM,CAAC,OAC/C,CAEA,SAAS,CAAE,KAAAP,CAAK,EAAoC,CAClD,MAAO,SAASM,EAAON,EAAM,EAAI,CAAC,SACpC,CAEA,GAAGD,EAAkC,CACnC,MAAO,MACT,CAEA,IAAI,CAAE,OAAAQ,CAAO,EAA+B,CAC1C,MAAO,QAAQ,KAAK,OAAO,YAAYA,CAAM,CAAC,QAChD,CAEA,KAAK,CAAE,KAAAiB,EAAM,MAAAC,EAAO,OAAAlB,CAAO,EAAgC,CACzD,IAAMP,EAAO,KAAK,OAAO,YAAYO,CAAM,EACrCmB,EAAYC,EAASH,CAAI,EAC/B,GAAIE,IAAc,KAChB,OAAO1B,EAETwB,EAAOE,EACP,IAAIE,EAAM,YAAcJ,EAAO,IAC/B,OAAIC,IACFG,GAAO,WAActB,EAAOmB,CAAK,EAAK,KAExCG,GAAO,IAAM5B,EAAO,OACb4B,CACT,CAEA,MAAM,CAAE,KAAAJ,EAAM,MAAAC,EAAO,KAAAzB,EAAM,OAAAO,CAAO,EAAiC,CAC7DA,IACFP,EAAO,KAAK,OAAO,YAAYO,EAAQ,KAAK,OAAO,YAAY,GAEjE,IAAMmB,EAAYC,EAASH,CAAI,EAC/B,GAAIE,IAAc,KAChB,OAAOpB,EAAON,CAAI,EAEpBwB,EAAOE,EAEP,IAAIE,EAAM,aAAaJ,CAAI,UAAUxB,CAAI,IACzC,OAAIyB,IACFG,GAAO,WAAWtB,EAAOmB,CAAK,CAAC,KAEjCG,GAAO,IACAA,CACT,CAEA,KAAK7B,EAAoD,CACvD,MAAO,WAAYA,GAASA,EAAM,OAC9B,KAAK,OAAO,YAAYA,EAAM,MAAM,EACnC,YAAaA,GAASA,EAAM,QAAUA,EAAM,KAAyBO,EAAOP,EAAM,IAAI,CAC7F,CACF,ECpNO,IAAM8B,EAAN,KAA6C,CAElD,OAAO,CAAE,KAAAC,CAAK,EAAkC,CAC9C,OAAOA,CACT,CAEA,GAAG,CAAE,KAAAA,CAAK,EAA8B,CACtC,OAAOA,CACT,CAEA,SAAS,CAAE,KAAAA,CAAK,EAAoC,CAClD,OAAOA,CACT,CAEA,IAAI,CAAE,KAAAA,CAAK,EAA+B,CACxC,OAAOA,CACT,CAEA,KAAK,CAAE,KAAAA,CAAK,EAA6C,CACvD,OAAOA,CACT,CAEA,KAAK,CAAE,KAAAA,CAAK,EAA6D,CACvE,OAAOA,CACT,CAEA,KAAK,CAAE,KAAAA,CAAK,EAAgC,CAC1C,MAAO,GAAKA,CACd,CAEA,MAAM,CAAE,KAAAA,CAAK,EAAiC,CAC5C,MAAO,GAAKA,CACd,CAEA,IAAqB,CACnB,MAAO,EACT,CACF,EClCO,IAAMC,EAAN,MAAMC,CAAwD,CACnE,QACA,SACA,aACA,YAAYC,EAAuD,CACjE,KAAK,QAAUA,GAAWC,EAC1B,KAAK,QAAQ,SAAW,KAAK,QAAQ,UAAY,IAAIC,EACrD,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,SAAS,QAAU,KAAK,QAC7B,KAAK,SAAS,OAAS,KACvB,KAAK,aAAe,IAAIC,CAC1B,CAKA,OAAO,MAAsDC,EAAiBJ,EAAuD,CAEnI,OADe,IAAID,EAAsCC,CAAO,EAClD,MAAMI,CAAM,CAC5B,CAKA,OAAO,YAA4DA,EAAiBJ,EAAuD,CAEzI,OADe,IAAID,EAAsCC,CAAO,EAClD,YAAYI,CAAM,CAClC,CAKA,MAAMA,EAAiBC,EAAM,GAAoB,CAC/C,IAAIC,EAAM,GAEV,QAASC,EAAI,EAAGA,EAAIH,EAAO,OAAQG,IAAK,CACtC,IAAMC,EAAWJ,EAAOG,CAAC,EAGzB,GAAI,KAAK,QAAQ,YAAY,YAAYC,EAAS,IAAI,EAAG,CACvD,IAAMC,EAAeD,EACfE,EAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAK,EAAGA,CAAY,EACpG,GAAIC,IAAQ,IAAS,CAAC,CAAC,QAAS,KAAM,UAAW,OAAQ,QAAS,aAAc,OAAQ,OAAQ,YAAa,MAAM,EAAE,SAASD,EAAa,IAAI,EAAG,CAChJH,GAAOI,GAAO,GACd,QACF,CACF,CAEA,IAAMC,EAAQH,EAEd,OAAQG,EAAM,KAAM,CAClB,IAAK,QAAS,CACZL,GAAO,KAAK,SAAS,MAAMK,CAAK,EAChC,QACF,CACA,IAAK,KAAM,CACTL,GAAO,KAAK,SAAS,GAAGK,CAAK,EAC7B,QACF,CACA,IAAK,UAAW,CACdL,GAAO,KAAK,SAAS,QAAQK,CAAK,EAClC,QACF,CACA,IAAK,OAAQ,CACXL,GAAO,KAAK,SAAS,KAAKK,CAAK,EAC/B,QACF,CACA,IAAK,QAAS,CACZL,GAAO,KAAK,SAAS,MAAMK,CAAK,EAChC,QACF,CACA,IAAK,aAAc,CACjBL,GAAO,KAAK,SAAS,WAAWK,CAAK,EACrC,QACF,CACA,IAAK,OAAQ,CACXL,GAAO,KAAK,SAAS,KAAKK,CAAK,EAC/B,QACF,CACA,IAAK,OAAQ,CACXL,GAAO,KAAK,SAAS,KAAKK,CAAK,EAC/B,QACF,CACA,IAAK,YAAa,CAChBL,GAAO,KAAK,SAAS,UAAUK,CAAK,EACpC,QACF,CACA,IAAK,OAAQ,CACX,IAAIC,EAAYD,EACZE,EAAO,KAAK,SAAS,KAAKD,CAAS,EACvC,KAAOL,EAAI,EAAIH,EAAO,QAAUA,EAAOG,EAAI,CAAC,EAAE,OAAS,QACrDK,EAAYR,EAAO,EAAEG,CAAC,EACtBM,GAAS;AAAA,EAAO,KAAK,SAAS,KAAKD,CAAS,EAE1CP,EACFC,GAAO,KAAK,SAAS,UAAU,CAC7B,KAAM,YACN,IAAKO,EACL,KAAMA,EACN,OAAQ,CAAC,CAAE,KAAM,OAAQ,IAAKA,EAAM,KAAMA,EAAM,QAAS,EAAK,CAAC,CACjE,CAAC,EAEDP,GAAOO,EAET,QACF,CAEA,QAAS,CACP,IAAMC,EAAS,eAAiBH,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACf,eAAQ,MAAMG,CAAM,EACb,GAEP,MAAM,IAAI,MAAMA,CAAM,CAE1B,CACF,CACF,CAEA,OAAOR,CACT,CAKA,YAAYF,EAAiBW,EAAoF,KAAK,SAAwB,CAC5I,IAAIT,EAAM,GAEV,QAASC,EAAI,EAAGA,EAAIH,EAAO,OAAQG,IAAK,CACtC,IAAMC,EAAWJ,EAAOG,CAAC,EAGzB,GAAI,KAAK,QAAQ,YAAY,YAAYC,EAAS,IAAI,EAAG,CACvD,IAAME,EAAM,KAAK,QAAQ,WAAW,UAAUF,EAAS,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAK,EAAGA,CAAQ,EAC5F,GAAIE,IAAQ,IAAS,CAAC,CAAC,SAAU,OAAQ,OAAQ,QAAS,SAAU,KAAM,WAAY,KAAM,MAAO,MAAM,EAAE,SAASF,EAAS,IAAI,EAAG,CAClIF,GAAOI,GAAO,GACd,QACF,CACF,CAEA,IAAMC,EAAQH,EAEd,OAAQG,EAAM,KAAM,CAClB,IAAK,SAAU,CACbL,GAAOS,EAAS,KAAKJ,CAAK,EAC1B,KACF,CACA,IAAK,OAAQ,CACXL,GAAOS,EAAS,KAAKJ,CAAK,EAC1B,KACF,CACA,IAAK,OAAQ,CACXL,GAAOS,EAAS,KAAKJ,CAAK,EAC1B,KACF,CACA,IAAK,QAAS,CACZL,GAAOS,EAAS,MAAMJ,CAAK,EAC3B,KACF,CACA,IAAK,SAAU,CACbL,GAAOS,EAAS,OAAOJ,CAAK,EAC5B,KACF,CACA,IAAK,KAAM,CACTL,GAAOS,EAAS,GAAGJ,CAAK,EACxB,KACF,CACA,IAAK,WAAY,CACfL,GAAOS,EAAS,SAASJ,CAAK,EAC9B,KACF,CACA,IAAK,KAAM,CACTL,GAAOS,EAAS,GAAGJ,CAAK,EACxB,KACF,CACA,IAAK,MAAO,CACVL,GAAOS,EAAS,IAAIJ,CAAK,EACzB,KACF,CACA,IAAK,OAAQ,CACXL,GAAOS,EAAS,KAAKJ,CAAK,EAC1B,KACF,CACA,QAAS,CACP,IAAMG,EAAS,eAAiBH,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACf,eAAQ,MAAMG,CAAM,EACb,GAEP,MAAM,IAAI,MAAMA,CAAM,CAE1B,CACF,CACF,CACA,OAAOR,CACT,CACF,ECvMO,IAAMU,EAAN,KAA6D,CAClE,QACA,MAEA,YAAYC,EAAuD,CACjE,KAAK,QAAUA,GAAWC,CAC5B,CAEA,OAAO,iBAAmB,IAAI,IAAI,CAChC,aACA,cACA,kBACF,CAAC,EAKD,WAAWC,EAAkB,CAC3B,OAAOA,CACT,CAKA,YAAYC,EAAoB,CAC9B,OAAOA,CACT,CAKA,iBAAiBC,EAA8B,CAC7C,OAAOA,CACT,CAKA,cAAe,CACb,OAAO,KAAK,MAAQC,EAAO,IAAMA,EAAO,SAC1C,CAKA,eAAgB,CACd,OAAO,KAAK,MAAQC,EAAQ,MAAsCA,EAAQ,WAC5E,CACF,ECtCO,IAAMC,EAAN,KAA6D,CAClE,SAAWC,EAA2C,EACtD,QAAU,KAAK,WAEf,MAAQ,KAAK,cAAc,EAAI,EAC/B,YAAc,KAAK,cAAc,EAAK,EAEtC,OAASC,EACT,SAAWC,EACX,aAAeC,EACf,MAAQC,EACR,UAAYC,EACZ,MAAQC,EAER,eAAeC,EAAuD,CACpE,KAAK,IAAI,GAAGA,CAAI,CAClB,CAKA,WAAWC,EAA8BC,EAA2D,CAClG,IAAIC,EAAyB,CAAC,EAC9B,QAAWC,KAASH,EAElB,OADAE,EAASA,EAAO,OAAOD,EAAS,KAAK,KAAME,CAAK,CAAC,EACzCA,EAAM,KAAM,CAClB,IAAK,QAAS,CACZ,IAAMC,EAAaD,EACnB,QAAWE,KAAQD,EAAW,OAC5BF,EAASA,EAAO,OAAO,KAAK,WAAWG,EAAK,OAAQJ,CAAQ,CAAC,EAE/D,QAAWK,KAAOF,EAAW,KAC3B,QAAWC,KAAQC,EACjBJ,EAASA,EAAO,OAAO,KAAK,WAAWG,EAAK,OAAQJ,CAAQ,CAAC,EAGjE,KACF,CACA,IAAK,OAAQ,CACX,IAAMM,EAAYJ,EAClBD,EAASA,EAAO,OAAO,KAAK,WAAWK,EAAU,MAAON,CAAQ,CAAC,EACjE,KACF,CACA,QAAS,CACP,IAAMO,EAAeL,EACjB,KAAK,SAAS,YAAY,cAAcK,EAAa,IAAI,EAC3D,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAASC,GAAgB,CAC/E,IAAMT,EAASQ,EAAaC,CAAW,EAAE,KAAK,GAAQ,EACtDP,EAASA,EAAO,OAAO,KAAK,WAAWF,EAAQC,CAAQ,CAAC,CAC1D,CAAC,EACQO,EAAa,SACtBN,EAASA,EAAO,OAAO,KAAK,WAAWM,EAAa,OAAQP,CAAQ,CAAC,EAEzE,CACF,CAEF,OAAOC,CACT,CAEA,OAAOH,EAAuD,CAC5D,IAAMW,EAAwE,KAAK,SAAS,YAAc,CAAE,UAAW,CAAC,EAAG,YAAa,CAAC,CAAE,EAE3I,OAAAX,EAAK,QAASY,GAAS,CAErB,IAAMC,EAAO,CAAE,GAAGD,CAAK,EA4DvB,GAzDAC,EAAK,MAAQ,KAAK,SAAS,OAASA,EAAK,OAAS,GAG9CD,EAAK,aACPA,EAAK,WAAW,QAASE,GAAQ,CAC/B,GAAI,CAACA,EAAI,KACP,MAAM,IAAI,MAAM,yBAAyB,EAE3C,GAAI,aAAcA,EAAK,CACrB,IAAMC,EAAeJ,EAAW,UAAUG,EAAI,IAAI,EAC9CC,EAEFJ,EAAW,UAAUG,EAAI,IAAI,EAAI,YAAYd,EAAM,CACjD,IAAIgB,EAAMF,EAAI,SAAS,MAAM,KAAMd,CAAI,EACvC,OAAIgB,IAAQ,KACVA,EAAMD,EAAa,MAAM,KAAMf,CAAI,GAE9BgB,CACT,EAEAL,EAAW,UAAUG,EAAI,IAAI,EAAIA,EAAI,QAEzC,CACA,GAAI,cAAeA,EAAK,CACtB,GAAI,CAACA,EAAI,OAAUA,EAAI,QAAU,SAAWA,EAAI,QAAU,SACxD,MAAM,IAAI,MAAM,6CAA6C,EAE/D,IAAMG,EAAWN,EAAWG,EAAI,KAAK,EACjCG,EACFA,EAAS,QAAQH,EAAI,SAAS,EAE9BH,EAAWG,EAAI,KAAK,EAAI,CAACA,EAAI,SAAS,EAEpCA,EAAI,QACFA,EAAI,QAAU,QACZH,EAAW,WACbA,EAAW,WAAW,KAAKG,EAAI,KAAK,EAEpCH,EAAW,WAAa,CAACG,EAAI,KAAK,EAE3BA,EAAI,QAAU,WACnBH,EAAW,YACbA,EAAW,YAAY,KAAKG,EAAI,KAAK,EAErCH,EAAW,YAAc,CAACG,EAAI,KAAK,GAI3C,CACI,gBAAiBA,GAAOA,EAAI,cAC9BH,EAAW,YAAYG,EAAI,IAAI,EAAIA,EAAI,YAE3C,CAAC,EACDD,EAAK,WAAaF,GAIhBC,EAAK,SAAU,CACjB,IAAMM,EAAW,KAAK,SAAS,UAAY,IAAIvB,EAAwC,KAAK,QAAQ,EACpG,QAAWwB,KAAQP,EAAK,SAAU,CAChC,GAAI,EAAEO,KAAQD,GACZ,MAAM,IAAI,MAAM,aAAaC,CAAI,kBAAkB,EAErD,GAAI,CAAC,UAAW,QAAQ,EAAE,SAASA,CAAI,EAErC,SAEF,IAAMC,EAAeD,EACfE,EAAeT,EAAK,SAASQ,CAAY,EACzCL,EAAeG,EAASE,CAAY,EAE1CF,EAASE,CAAY,EAAI,IAAIpB,IAAoB,CAC/C,IAAIgB,EAAMK,EAAa,MAAMH,EAAUlB,CAAI,EAC3C,OAAIgB,IAAQ,KACVA,EAAMD,EAAa,MAAMG,EAAUlB,CAAI,GAEjCgB,GAAO,EACjB,CACF,CACAH,EAAK,SAAWK,CAClB,CACA,GAAIN,EAAK,UAAW,CAClB,IAAMU,EAAY,KAAK,SAAS,WAAa,IAAIxB,EAAyC,KAAK,QAAQ,EACvG,QAAWqB,KAAQP,EAAK,UAAW,CACjC,GAAI,EAAEO,KAAQG,GACZ,MAAM,IAAI,MAAM,cAAcH,CAAI,kBAAkB,EAEtD,GAAI,CAAC,UAAW,QAAS,OAAO,EAAE,SAASA,CAAI,EAE7C,SAEF,IAAMI,EAAgBJ,EAChBK,EAAgBZ,EAAK,UAAUW,CAAa,EAC5CE,EAAgBH,EAAUC,CAAa,EAG7CD,EAAUC,CAAa,EAAI,IAAIvB,IAAoB,CACjD,IAAIgB,EAAMQ,EAAc,MAAMF,EAAWtB,CAAI,EAC7C,OAAIgB,IAAQ,KACVA,EAAMS,EAAc,MAAMH,EAAWtB,CAAI,GAEpCgB,CACT,CACF,CACAH,EAAK,UAAYS,CACnB,CAGA,GAAIV,EAAK,MAAO,CACd,IAAMc,EAAQ,KAAK,SAAS,OAAS,IAAI3B,EACzC,QAAWoB,KAAQP,EAAK,MAAO,CAC7B,GAAI,EAAEO,KAAQO,GACZ,MAAM,IAAI,MAAM,SAASP,CAAI,kBAAkB,EAEjD,GAAI,CAAC,UAAW,OAAO,EAAE,SAASA,CAAI,EAEpC,SAEF,IAAMQ,EAAYR,EACZS,EAAYhB,EAAK,MAAMe,CAAS,EAChCE,EAAWH,EAAMC,CAAS,EAC5B5B,EAAO,iBAAiB,IAAIoB,CAAI,EAElCO,EAAMC,CAAS,EAAKG,GAAiB,CACnC,GAAI,KAAK,SAAS,MAChB,OAAO,QAAQ,QAAQF,EAAU,KAAKF,EAAOI,CAAG,CAAC,EAAE,KAAKd,GAC/Ca,EAAS,KAAKH,EAAOV,CAAG,CAChC,EAGH,IAAMA,EAAMY,EAAU,KAAKF,EAAOI,CAAG,EACrC,OAAOD,EAAS,KAAKH,EAAOV,CAAG,CACjC,EAGAU,EAAMC,CAAS,EAAI,IAAI3B,IAAoB,CACzC,IAAIgB,EAAMY,EAAU,MAAMF,EAAO1B,CAAI,EACrC,OAAIgB,IAAQ,KACVA,EAAMa,EAAS,MAAMH,EAAO1B,CAAI,GAE3BgB,CACT,CAEJ,CACAH,EAAK,MAAQa,CACf,CAGA,GAAId,EAAK,WAAY,CACnB,IAAMmB,EAAa,KAAK,SAAS,WAC3BC,EAAiBpB,EAAK,WAC5BC,EAAK,WAAa,SAAST,EAAO,CAChC,IAAID,EAAyB,CAAC,EAC9B,OAAAA,EAAO,KAAK6B,EAAe,KAAK,KAAM5B,CAAK,CAAC,EACxC2B,IACF5B,EAASA,EAAO,OAAO4B,EAAW,KAAK,KAAM3B,CAAK,CAAC,GAE9CD,CACT,CACF,CAEA,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGU,CAAK,CAC9C,CAAC,EAEM,IACT,CAEA,WAAWoB,EAAkD,CAC3D,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGA,CAAI,EACpC,IACT,CAEA,MAAMC,EAAaC,EAAuD,CACxE,OAAOtC,EAAO,IAAIqC,EAAKC,GAAW,KAAK,QAAQ,CACjD,CAEA,OAAOlC,EAAiBkC,EAAuD,CAC7E,OAAOzC,EAAQ,MAAoCO,EAAQkC,GAAW,KAAK,QAAQ,CACrF,CAEQ,cAAcC,EAAoB,CAmExC,MA3D+B,CAACF,EAAaC,IAAsE,CACjH,IAAME,EAAU,CAAE,GAAGF,CAAQ,EACvBF,EAAM,CAAE,GAAG,KAAK,SAAU,GAAGI,CAAQ,EAErCC,EAAa,KAAK,QAAQ,CAAC,CAACL,EAAI,OAAQ,CAAC,CAACA,EAAI,KAAK,EAGzD,GAAI,KAAK,SAAS,QAAU,IAAQI,EAAQ,QAAU,GACpD,OAAOC,EAAW,IAAI,MAAM,oIAAoI,CAAC,EAInK,GAAI,OAAOJ,EAAQ,KAAeA,IAAQ,KACxC,OAAOI,EAAW,IAAI,MAAM,gDAAgD,CAAC,EAE/E,GAAI,OAAOJ,GAAQ,SACjB,OAAOI,EAAW,IAAI,MAAM,wCACxB,OAAO,UAAU,SAAS,KAAKJ,CAAG,EAAI,mBAAmB,CAAC,EAG5DD,EAAI,QACNA,EAAI,MAAM,QAAUA,EACpBA,EAAI,MAAM,MAAQG,GAGpB,IAAMG,EAAQN,EAAI,MAAQA,EAAI,MAAM,aAAa,EAAKG,EAAYvC,EAAO,IAAMA,EAAO,UAChF2C,EAASP,EAAI,MAAQA,EAAI,MAAM,cAAc,EAAKG,EAAY1C,EAAQ,MAAQA,EAAQ,YAE5F,GAAIuC,EAAI,MACN,OAAO,QAAQ,QAAQA,EAAI,MAAQA,EAAI,MAAM,WAAWC,CAAG,EAAIA,CAAG,EAC/D,KAAKA,GAAOK,EAAML,EAAKD,CAAG,CAAC,EAC3B,KAAKhC,GAAUgC,EAAI,MAAQA,EAAI,MAAM,iBAAiBhC,CAAM,EAAIA,CAAM,EACtE,KAAKA,GAAUgC,EAAI,WAAa,QAAQ,IAAI,KAAK,WAAWhC,EAAQgC,EAAI,UAAU,CAAC,EAAE,KAAK,IAAMhC,CAAM,EAAIA,CAAM,EAChH,KAAKA,GAAUuC,EAAOvC,EAAQgC,CAAG,CAAC,EAClC,KAAKQ,GAAQR,EAAI,MAAQA,EAAI,MAAM,YAAYQ,CAAI,EAAIA,CAAI,EAC3D,MAAMH,CAAU,EAGrB,GAAI,CACEL,EAAI,QACNC,EAAMD,EAAI,MAAM,WAAWC,CAAG,GAEhC,IAAIjC,EAASsC,EAAML,EAAKD,CAAG,EACvBA,EAAI,QACNhC,EAASgC,EAAI,MAAM,iBAAiBhC,CAAM,GAExCgC,EAAI,YACN,KAAK,WAAWhC,EAAQgC,EAAI,UAAU,EAExC,IAAIQ,EAAOD,EAAOvC,EAAQgC,CAAG,EAC7B,OAAIA,EAAI,QACNQ,EAAOR,EAAI,MAAM,YAAYQ,CAAI,GAE5BA,CACT,OAAQC,EAAG,CACT,OAAOJ,EAAWI,CAAU,CAC9B,CACF,CAGF,CAEQ,QAAQC,EAAiBC,EAAgB,CAC/C,OAAQF,GAAuC,CAG7C,GAFAA,EAAE,SAAW;AAAA,2DAETC,EAAQ,CACV,IAAME,EAAM,iCACRC,EAAOJ,EAAE,QAAU,GAAI,EAAI,EAC3B,SACJ,OAAIE,EACK,QAAQ,QAAQC,CAAG,EAErBA,CACT,CAEA,GAAID,EACF,OAAO,QAAQ,OAAOF,CAAC,EAEzB,MAAMA,CACR,CACF,CACF,EVjVA,IAAMK,EAAiB,IAAIC,EAqBpB,SAASC,EAAOC,EAAaC,EAAsD,CACxF,OAAOJ,EAAe,MAAMG,EAAKC,CAAG,CACtC,CAOAF,EAAO,QACPA,EAAO,WAAa,SAASG,EAAwB,CACnD,OAAAL,EAAe,WAAWK,CAAO,EACjCH,EAAO,SAAWF,EAAe,SACjCM,EAAeJ,EAAO,QAAQ,EACvBA,CACT,EAKAA,EAAO,YAAcK,EAErBL,EAAO,SAAWM,EAMlBN,EAAO,IAAM,YAAYO,EAAyB,CAChD,OAAAT,EAAe,IAAI,GAAGS,CAAI,EAC1BP,EAAO,SAAWF,EAAe,SACjCM,EAAeJ,EAAO,QAAQ,EACvBA,CACT,EAMAA,EAAO,WAAa,SAASQ,EAA8BC,EAA2D,CACpH,OAAOX,EAAe,WAAWU,EAAQC,CAAQ,CACnD,EASAT,EAAO,YAAcF,EAAe,YAKpCE,EAAO,OAASU,EAChBV,EAAO,OAASU,EAAQ,MACxBV,EAAO,SAAWW,EAClBX,EAAO,aAAeY,EACtBZ,EAAO,MAAQa,EACfb,EAAO,MAAQa,EAAO,IACtBb,EAAO,UAAYc,EACnBd,EAAO,MAAQe,EACff,EAAO,MAAQA,EAER,IAAMG,GAAUH,EAAO,QACjBgB,GAAahB,EAAO,WACpBiB,GAAMjB,EAAO,IACbkB,GAAalB,EAAO,WACpBmB,GAAcnB,EAAO,YACrBoB,GAAQpB,EACRqB,GAASX,EAAQ,MACjBY,GAAQT,EAAO", "names": ["marked_exports", "__export", "_Hooks", "_<PERSON>er", "Marked", "_<PERSON><PERSON>r", "_Renderer", "_<PERSON><PERSON><PERSON><PERSON>", "_Tokenizer", "_defaults", "_getDefaults", "lexer", "marked", "options", "parse", "parseInline", "parser", "setOptions", "use", "walkTokens", "__toCommonJS", "_getDefaults", "_defaults", "changeDefaults", "newDefaults", "noopTest", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "other", "bull", "indent", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheadingCore", "lheading", "lheadingGfm", "_paragraph", "blockText", "_blockLabel", "def", "list", "_tag", "_comment", "html", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "escape", "inlineCode", "br", "inlineText", "_punctuation", "_punctuationOrSpace", "_notPunctuationOrSpace", "punctuation", "_punctuationGfmStrongEm", "_punctuationOrSpaceGfmStrongEm", "_notPunctuationOrSpaceGfmStrongEm", "blockSkip", "emStrongLDelimCore", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongLDelimGfm", "emStrongRDelimAstCore", "emStrongRDelim<PERSON>t", "emStrongRDelimAstGfm", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "tag", "_inlineLabel", "link", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "escapeReplacements", "getEscapeReplacement", "ch", "escape", "html", "encode", "other", "cleanUrl", "href", "other", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "findClosingBracket", "b", "level", "outputLink", "cap", "link", "raw", "lexer", "rules", "href", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "_defaults", "src", "rtrim", "trimmed", "lines", "tokens", "inBlockquote", "currentLines", "i", "currentRaw", "currentText", "top", "lastToken", "oldToken", "newText", "newToken", "bull", "isordered", "list", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "nextLine", "blankLine", "indent", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "rawLine", "nextLineWithoutTabs", "istask", "ischecked", "lastItem", "spacers", "hasMultipleLineBreaks", "tag", "headers", "splitCells", "aligns", "rows", "item", "align", "row", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "findClosingBracket", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "match", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "escaped", "_<PERSON>er", "__<PERSON><PERSON>", "options", "_defaults", "_Tokenizer", "rules", "other", "block", "inline", "src", "i", "next", "tokens", "lastParagraphClipped", "token", "extTokenizer", "lastToken", "cutSrc", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "maskedSrc", "match", "links", "keepPrevChar", "prevChar", "_Renderer", "options", "_defaults", "token", "text", "lang", "escaped", "langString", "other", "code", "escape", "tokens", "depth", "ordered", "start", "body", "j", "item", "type", "startAttr", "itemBody", "checkbox", "checked", "header", "cell", "row", "k", "content", "href", "title", "cleanHref", "cleanUrl", "out", "_<PERSON><PERSON><PERSON><PERSON>", "text", "_<PERSON><PERSON>r", "__<PERSON><PERSON><PERSON>", "options", "_defaults", "_Renderer", "_<PERSON><PERSON><PERSON><PERSON>", "tokens", "top", "out", "i", "anyToken", "genericToken", "ret", "token", "textToken", "body", "errMsg", "renderer", "_Hooks", "options", "_defaults", "markdown", "html", "tokens", "_<PERSON>er", "_<PERSON><PERSON>r", "Marked", "_getDefaults", "_<PERSON><PERSON>r", "_Renderer", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON>er", "_Tokenizer", "_Hooks", "args", "tokens", "callback", "values", "token", "tableToken", "cell", "row", "listToken", "genericToken", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "ret", "extLevel", "renderer", "prop", "rendererProp", "rendererFunc", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "opt", "src", "options", "blockType", "origOpt", "throwError", "lexer", "parser", "html", "e", "silent", "async", "msg", "escape", "markedInstance", "Marked", "marked", "src", "opt", "options", "changeDefaults", "_getDefaults", "_defaults", "args", "tokens", "callback", "_<PERSON><PERSON>r", "_Renderer", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON>er", "_Tokenizer", "_Hooks", "setOptions", "use", "walkTokens", "parseInline", "parse", "parser", "lexer"]}