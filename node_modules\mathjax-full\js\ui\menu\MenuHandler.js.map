{"version": 3, "file": "MenuHandler.js", "sourceRoot": "", "sources": ["../../../ts/ui/menu/MenuHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,+CAAyC;AAEzC,sDAAuD;AAMvD,oDAAiD;AAEjD,qCAA+B;AA6B/B,IAAA,sBAAQ,EAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AA4B9B,SAAgB,iBAAiB,CAC/B,YAAe;IAGf;QAAqB,2BAAY;QAA1B;;QAqBP,CAAC;QAfQ,yBAAO,GAAd,UAAe,QAA0B,EAAE,KAAsB;YAAtB,sBAAA,EAAA,aAAsB;YAC/D,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,YAAY;gBAAE,OAAO;YAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,EAAE;gBAC7D,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC7B;YACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;QAKM,8BAAY,GAAnB,UAAoB,QAA0B;YAC5C,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC1B,CAAC;QAEH,cAAC;IAAD,CAAC,AArBM,CAAc,YAAY,GAqB/B;AAEJ,CAAC;AA3BD,8CA2BC;AAqCD,SAAgB,qBAAqB,CACnC,YAAe;;IAGf;YAAqB,2BAAY;YA2C/B;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAOd;gBANC,KAAI,CAAC,IAAI,GAAG,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAI,EAAE,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAM,WAAW,GAAI,KAAI,CAAC,WAAmC,CAAC,WAAW,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;oBACpC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;iBACtC;gBACD,KAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,iBAAiB,CAA0B,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;YAC5F,CAAC;YAOM,yBAAO,GAAd;;gBACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;;wBACzC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;4BAAzB,IAAM,IAAI,WAAA;4BACZ,IAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;yBACtC;;;;;;;;;oBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;iBACpC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAOM,8BAAY,GAAnB;gBACE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACvB,oBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAC,GAAG,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAhB,CAAgB,CAAC,CAAC,CAAC;iBAC/E;gBACD,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACpC,IAAI,QAAQ,CAAC,WAAW,EAAE;oBACxB,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;iBAC7C;gBACD,IAAI,QAAQ,CAAC,QAAQ,EAAE;oBACrB,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;oBACnC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;iBAC3C;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAKM,uBAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;gBAAxB,wBAAA,EAAA,eAAwB;gBAClD,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,GAAG,mBAAK,CAAC,YAAY,EAAE;oBAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;iBACtC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAKM,gCAAc,GAArB;gBACE,iBAAM,cAAc,WAAE,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAa,CAAC,IAAI,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAEH,cAAC;QAAD,CAAC,AA9GM,CAAc,YAAY;QAKjB,UAAO,uBAQnB,gBAAgB,EAAE,IAAI,EACtB,gBAAgB,EAAE,IAAI,EACtB,cAAc,EAAE,IAAI,EACpB,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,UAAC,IAAsB,EAAE,KAAmB,EAAE,GAAU;gBACnE,OAAA,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC;YAAtC,CAAsC,IACrC,YAAY,CAAC,OAAO,KACvB,SAAS,EAAE,cAAI,EACf,WAAW,EAAE,cAAI,CAAC,OAAO,EACzB,UAAU,EAAE,IAAI,EAChB,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,IAAA,uBAAU,EAAC,EAAE,CAAC,CAAC,EACjD,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI,IAAA,uBAAU,EAAC,EAAE,CAAC,CAAC,EACnD,aAAa,EAAE,IAAA,uBAAU,wBACpB,YAAY,CAAC,OAAO,CAAC,aAAa,KACrC,OAAO,EAAE,CAAC,mBAAK,CAAC,YAAY,CAAC,EAC7B,YAAY,EAAE,CAAC,mBAAK,CAAC,WAAW,GAAG,CAAC,CAAC,IACrC,GACF;WAgFF;AAEJ,CAAC;AApHD,sDAoHC;AAUD,SAAgB,WAAW,CAAC,OAA6C;IACvE,OAAO,CAAC,aAAa,GAAG,qBAAqB,CAA0B,OAAO,CAAC,aAAoB,CAAC,CAAC;IACrG,OAAO,OAAO,CAAC;AACjB,CAAC;AAHD,kCAGC"}