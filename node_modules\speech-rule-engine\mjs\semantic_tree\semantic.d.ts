import { SemanticFont, SemanticRole, SemanticType } from './semantic_meaning.js';
import { SemanticTree } from './semantic_tree.js';
export type Font = SemanticFont;
export type Role = SemanticRole;
export type Type = SemanticType;
type Attr = Font | Role | Type;
export { Attr };
export declare function xmlTree(mml: Element): Element;
export declare function getTree(mml: Element): SemanticTree;
export declare function getTreeFromString(expr: string): SemanticTree;
