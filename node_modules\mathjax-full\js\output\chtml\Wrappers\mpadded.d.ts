import { CHTMLConstructor } from '../Wrapper.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmpadded_base: import("../../common/Wrappers/mpadded.js").MpaddedConstructor & CHTMLConstructor<any, any, any>;
export declare class CHTMLmpadded<N, T, D> extends CHTMLmpadded_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
}
export {};
