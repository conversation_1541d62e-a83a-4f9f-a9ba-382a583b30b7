<script setup lang="ts">
import { computed, onMounted, nextTick, ref, watch } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

interface Props {
  content: string
  isStreaming?: boolean
}

const props = defineProps<Props>()
const markdownContainer = ref<HTMLElement>()

// 声明全局 MathJax 对象
declare global {
  interface Window {
    MathJax: any
  }
}

// 跟踪已处理的内容和渲染状态
const lastProcessedContent = ref('')
const isRendering = ref(false)
const renderedFormulas = new Set<string>()

// 生成公式的唯一标识
const getFormulaId = (element: Element): string => {
  const content = element.textContent?.trim() || ''
  const type = element.classList.contains('math-display') ? 'display' : 'inline'
  return `${type}-${btoa(content).slice(0, 10)}`
}

// 渲染数学公式的函数 - 优化版本
const renderMath = async () => {
  if (!window.MathJax || !markdownContainer.value || isRendering.value) {
    return
  }

  try {
    isRendering.value = true
    const currentContent = markdownContainer.value.innerHTML

    // 如果内容没有变化，跳过渲染
    if (currentContent === lastProcessedContent.value) {
      return
    }

    // 查找所有数学公式元素
    const formulaElements = markdownContainer.value.querySelectorAll('script[type*="math"], .math-inline, .math-display')
    const newFormulas: Element[] = []

    // 识别新的公式
    formulaElements.forEach(el => {
      const formulaId = getFormulaId(el)
      if (!renderedFormulas.has(formulaId)) {
        newFormulas.push(el)
        renderedFormulas.add(formulaId)

        // 为新公式添加占位符样式，防止布局跳动
        if (el.classList.contains('math-display')) {
          el.classList.add('math-placeholder-display')
        } else {
          el.classList.add('math-placeholder-inline')
        }
      }
    })

    // 只渲染新的公式
    if (newFormulas.length > 0) {
      if (typeof window.MathJax.typesetPromise === 'function') {
        // MathJax 3.x - 只渲染新公式
        await window.MathJax.typesetPromise(newFormulas)
      } else if (typeof window.MathJax.typeset === 'function') {
        window.MathJax.typeset(newFormulas)
      } else if (window.MathJax.Hub) {
        // MathJax 2.x
        window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, newFormulas])
      }

      // 渲染完成后移除占位符样式
      newFormulas.forEach(el => {
        el.classList.remove('math-placeholder-display', 'math-placeholder-inline')
      })
    }

    // 更新最后处理的内容
    lastProcessedContent.value = currentContent

  } catch (error) {
    console.error('MathJax 渲染错误:', error)
  } finally {
    isRendering.value = false
  }
}

// 高亮代码块的函数
const highlightCodeBlocks = () => {
  if (markdownContainer.value) {
    const codeBlocks = markdownContainer.value.querySelectorAll('pre code:not(.hljs)')
    codeBlocks.forEach((block) => {
      hljs.highlightElement(block as HTMLElement)
    })
  }
}

// 防抖渲染函数
let renderTimeout: number | null = null
const debouncedRender = () => {
  if (renderTimeout) {
    clearTimeout(renderTimeout)
  }
  renderTimeout = setTimeout(async () => {
    highlightCodeBlocks()
    await renderMath()
  }, props.isStreaming ? 300 : 100) // 流式渲染时延迟更长
}

// 计算渲染后的 HTML
const renderedHtml = computed(() => {
  try {
    if (!props.content) {
      // 清空已渲染公式记录
      renderedFormulas.clear()
      lastProcessedContent.value = ''
      return ''
    }

    // 使用 marked 解析 Markdown（保留原始的数学公式语法）
    const result = marked.parse(props.content, {
      breaks: true,
      gfm: true
    })

    // 延迟渲染，避免在computed中直接执行异步操作
    nextTick(() => {
      debouncedRender()
    })

    return result
  } catch (error) {
    console.error('Markdown 渲染错误:', error)
    return `<p>${props.content}</p>`
  }
})

// 监听内容变化 - 优化版本
watch(() => props.content, (newContent, oldContent) => {
  // 只有在内容真正变化时才重新渲染
  if (newContent !== oldContent) {
    nextTick(() => {
      debouncedRender()
    })
  }
}, { flush: 'post' })

// 在组件挂载后初始化渲染
onMounted(() => {
  nextTick(() => {
    setTimeout(async () => {
      highlightCodeBlocks()
      await renderMath()
    }, 200)
  })
})
</script>

<template>
  <div
    ref="markdownContainer"
    class="markdown-content"
    v-html="renderedHtml"
  ></div>
</template>

<style scoped>
.markdown-content {
  line-height: 1.6;
  color: inherit;
}

/* 全局样式，不使用 scoped */
</style>

<style>
/* Markdown 基础样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 1em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5em;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.3em;
}

.markdown-content h2 {
  font-size: 1.3em;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.3em;
}

.markdown-content h3 {
  font-size: 1.2em;
}

.markdown-content h4 {
  font-size: 1.1em;
}

.markdown-content h5,
.markdown-content h6 {
  font-size: 1em;
}

.markdown-content p {
  margin: 0.8em 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0.8em 0;
  padding-left: 2em;
}

.markdown-content li {
  margin: 0.2em 0;
}

.markdown-content blockquote {
  margin: 1em 0;
  padding: 0 1em;
  border-left: 4px solid #d1d5db;
  background-color: #f9fafb;
  color: #6b7280;
}

.markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
  border: 1px solid #e1e4e8;
}

.markdown-content code.inline-code {
  background-color: #f6f8fa;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
  border: 1px solid #e1e4e8;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  border: none;
  border-radius: 0;
}

/* 表格样式 */
.table-wrapper {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #e1e4e8;
}

.markdown-table th,
.markdown-table td {
  border: 1px solid #e1e4e8;
  padding: 8px 12px;
  text-align: left;
}

.markdown-table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-table tr:nth-child(even) {
  background-color: #f9fafb;
}

/* 链接样式 */
.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* 分隔线样式 */
.markdown-content hr {
  border: none;
  border-top: 1px solid #e1e4e8;
  margin: 2em 0;
}

/* 强调样式 */
.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

/* 删除线样式 */
.markdown-content del {
  text-decoration: line-through;
}

/* 代码高亮主题调整 */
.hljs {
  background: #f6f8fa !important;
  color: #24292e !important;
}

/* 数学公式样式 - 模仿 Deepseek 风格 */
.math-inline {
  display: inline-block;
  margin: 0 3px;
  vertical-align: middle;
  font-size: 1.05em;
  min-height: 1.2em; /* 为行内公式预留高度 */
}

.math-display {
  display: block;
  margin: 1.2em 0;
  text-align: center;
  overflow-x: auto;
  padding: 0.5em 0;
  min-height: 2.5em; /* 为块级公式预留足够的高度 */
}

.math-inline svg,
.math-display svg {
  max-width: 100%;
  height: auto;
}

/* MathJax 公式样式优化 */
.MathJax {
  outline: none;
  border-radius: 4px;
}

/* 行内公式特殊样式 */
.mjx-math {
  font-size: 1.05em !important;
}

/* 块级公式特殊样式 */
.math-display .MathJax {
  padding: 0.5em;
  background-color: rgba(250, 250, 250, 0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
}

/* 暗色模式下公式样式 */
@media (prefers-color-scheme: dark) {
  .math-display .MathJax {
    background-color: rgba(30, 30, 30, 0.5);
  }
}

/* 数学公式在流式渲染时的样式 */
.math-inline:empty::before {
  content: "...";
  color: #9ca3af;
  font-style: italic;
}

.math-display:empty::before {
  content: "正在渲染数学公式...";
  color: #9ca3af;
  font-style: italic;
  display: block;
  text-align: center;
  padding: 1em;
  background-color: rgba(250, 250, 250, 0.5);
  border-radius: 5px;
}

/* 公式渲染中的占位符样式 - 防止页面跳动 */
.math-placeholder {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

/* 确保MathJax渲染容器不会导致布局变化 */
.MathJax_Display {
  margin: 1.2em 0 !important;
}

/* 在公式渲染过程中保持页面稳定 */
.MathJax_Processing {
  visibility: hidden;
}
.MathJax_Processed {
  transition: opacity 0.2s ease-in;
}
</style>
