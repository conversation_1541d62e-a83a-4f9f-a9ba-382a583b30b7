{"version": 3, "sources": ["../../mathjax-full/ts/core/InputJax.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for the InputJax\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {MathDocument} from './MathDocument.js';\nimport {MathItem, ProtoItem} from './MathItem.js';\nimport {MmlNode} from './MmlTree/MmlNode.js';\nimport {MmlFactory} from './MmlTree/MmlFactory.js';\nimport {userOptions, defaultOptions, OptionList} from '../util/Options.js';\nimport {FunctionList} from '../util/FunctionList.js';\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\n\n/*****************************************************************/\n/**\n *  The InputJax interface\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface InputJax<N, T, D> {\n  /**\n   * The name of the input jax subclass (e.g,. 'TeX')\n   */\n  name: string;\n\n  /**\n   * Whether this input jax processes string arrays or DOM nodes\n   * (TeX and AsciiMath process strings, MathML processes DOM nodes)\n   */\n  processStrings: boolean;\n\n  /**\n   * The options for this input jax instance\n   */\n  options: OptionList;\n\n  /**\n   * Lists of pre- and post-filters to call before and after processing the input\n   */\n  preFilters: FunctionList;\n  postFilters: FunctionList;\n\n  /**\n   * The DOM adaptor for managing HTML elements\n   */\n  adaptor: DOMAdaptor<N, T, D>;\n\n  /**\n   * The MmlFactory for this input jax\n   */\n  mmlFactory: MmlFactory;\n\n  /**\n   * @param {DOMAdaptor} adaptor The adaptor to use in this jax\n   */\n  setAdaptor(adaptor: DOMAdaptor<N, T, D>): void;\n\n  /**\n   * @param {MmlFactory} mmlFactory The MmlFactory to use in this jax\n   */\n  setMmlFactory(mmlFactory: MmlFactory): void;\n\n  /**\n   * Do any initialization that depends on the document being set up\n   */\n  initialize(): void;\n\n  /**\n   * Reset any needed features of the input jax\n   *\n   * @param {any[]} args   The arguments needed by the reset operation\n   */\n  reset(...args: any[]): void;\n\n  /**\n   * Finds the math within the DOM or the list of strings\n   *\n   * @param {N | string[]} which   The element or array of strings to be searched for math\n   * @param {OptionList} options   The options for the search, if any\n   * @return {ProtoItem[]}         Array of proto math items found (further processed by the\n   *                                handler to produce actual MathItem objects)\n   */\n  findMath(which: N | string[], options?: OptionList): ProtoItem<N, T>[];\n\n  /**\n   * Convert the math in a math item into the internal format\n   *\n   * @param {MathItem} math  The MathItem whose math content is to processed\n   * @param {MathDocument} document The MathDocument for this input jax.\n   * @return {MmlNode}       The resulting internal node tree for the math\n   */\n  compile(math: MathItem<N, T, D>, document: MathDocument<N, T, D>): MmlNode;\n}\n\n/*****************************************************************/\n/**\n *  The abstract InputJax class\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractInputJax<N, T, D> implements InputJax<N, T, D> {\n\n  /**\n   * The name of the input jax\n   */\n  public static NAME: string = 'generic';\n\n  /**\n   * The default options for the input jax\n   */\n  public static OPTIONS: OptionList = {};\n\n  /**\n   * The actual options supplied to the input jax\n   */\n  public options: OptionList;\n\n  /**\n   * Filters to run on the TeX string before it is processed\n   */\n  public preFilters: FunctionList;\n\n  /**\n   * Filters to run on the generated MathML after the TeX string is processed\n   */\n  public postFilters: FunctionList;\n\n  /**\n   * The DOMAdaptor for the MathDocument for this input jax\n   */\n  public adaptor: DOMAdaptor<N, T, D> = null;  // set by the handler\n  /**\n   * The MathML node factory\n   */\n  public mmlFactory: MmlFactory = null;        // set by the handler\n\n  /**\n   * @param {OptionList} options  The options to apply to this input jax\n   *\n   * @constructor\n   */\n  constructor(options: OptionList = {}) {\n    let CLASS = this.constructor as typeof AbstractInputJax;\n    this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), options);\n    this.preFilters = new FunctionList();\n    this.postFilters = new FunctionList();\n  }\n\n  /**\n   * @return {string}  The name of this input jax class\n   */\n  public get name(): string {\n    return (this.constructor as typeof AbstractInputJax).NAME;\n  }\n\n  /**\n   * @override\n   */\n  public setAdaptor(adaptor: DOMAdaptor<N, T, D>) {\n    this.adaptor = adaptor;\n  }\n\n  /**\n   * @override\n   */\n  public setMmlFactory(mmlFactory: MmlFactory) {\n    this.mmlFactory = mmlFactory;\n  }\n\n  /**\n   * @override\n   */\n  public initialize() {\n  }\n\n  /**\n   * @override\n   */\n  public reset(..._args: any[]) {\n  }\n\n  /**\n   * @return {boolean}  True means find math in string array, false means in DOM element\n   */\n  public get processStrings(): boolean {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public findMath(_node: N | string[], _options?: OptionList) {\n    return [] as ProtoItem<N, T>[];\n  }\n\n  /**\n   * @override\n   */\n  public abstract compile(math: MathItem<N, T, D>, document: MathDocument<N, T, D>): MmlNode;\n\n  /**\n   * Execute a set of filters, passing them the MathItem and any needed data,\n   *  and return the (possibly modified) data\n   *\n   * @param {FunctionList} filters   The list of functions to be performed\n   * @param {MathItem} math          The math item that is being processed\n   * @param {MathDocument} document  The math document containg the math item\n   * @param {any} data               Whatever other data is needed\n   * @return {any}                   The (possibly modified) data\n   */\n  protected executeFilters(\n    filters: FunctionList, math: MathItem<N, T, D>,\n    document: MathDocument<N, T, D>, data: any\n  ): any {\n    let args = {math: math, document: document, data: data};\n    filters.execute(args);\n    return args.data;\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AA2BA,QAAA,eAAA;AACA,QAAA,oBAAA;AA8FA,QAAA,mBAAA,WAAA;AAyCE,eAAAA,kBAAY,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AAX7B,aAAA,UAA+B;AAI/B,aAAA,aAAyB;AAQ9B,YAAI,QAAQ,KAAK;AACjB,aAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,OAAO;AACrE,aAAK,aAAa,IAAI,kBAAA,aAAY;AAClC,aAAK,cAAc,IAAI,kBAAA,aAAY;MACrC;AAKA,aAAA,eAAWA,kBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAQ,KAAK,YAAwC;QACvD;;;;AAKO,MAAAA,kBAAA,UAAA,aAAP,SAAkB,SAA4B;AAC5C,aAAK,UAAU;MACjB;AAKO,MAAAA,kBAAA,UAAA,gBAAP,SAAqB,YAAsB;AACzC,aAAK,aAAa;MACpB;AAKO,MAAAA,kBAAA,UAAA,aAAP,WAAA;MACA;AAKO,MAAAA,kBAAA,UAAA,QAAP,WAAA;AAAa,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAe;AAAf,gBAAA,EAAA,IAAA,UAAA,EAAA;;MACb;AAKA,aAAA,eAAWA,kBAAA,WAAA,kBAAc;aAAzB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,kBAAA,UAAA,WAAP,SAAgB,OAAqB,UAAqB;AACxD,eAAO,CAAA;MACT;AAiBU,MAAAA,kBAAA,UAAA,iBAAV,SACE,SAAuB,MACvB,UAAiC,MAAS;AAE1C,YAAI,OAAO,EAAC,MAAY,UAAoB,KAAU;AACtD,gBAAQ,QAAQ,IAAI;AACpB,eAAO,KAAK;MACd;AAhHc,MAAAA,kBAAA,OAAe;AAKf,MAAAA,kBAAA,UAAsB,CAAA;AA6GtC,aAAAA;MAvHA;AAAsB,YAAA,mBAAA;;;", "names": ["AbstractInputJax"]}