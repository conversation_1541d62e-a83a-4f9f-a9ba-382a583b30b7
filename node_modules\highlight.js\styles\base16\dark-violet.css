pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Dark Violet
  Author: ruler501 (https://github.com/ruler501/base16-darkviolet)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme dark-violet
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #231a40  Lighter Background (Used for status bars, line number and folding marks)
base02  #432d59  Selection Background
base03  #593380  Comments, Invisibles, Line Highlighting
base04  #00ff00  Dark Foreground (Used for status bars)
base05  #b08ae6  Default Foreground, Caret, Delimiters, Operators
base06  #9045e6  Light Foreground (Not often used)
base07  #a366ff  Light Background (Not often used)
base08  #a82ee6  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #bb66cc  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f29df2  Classes, Markup Bold, Search Text Background
base0B  #4595e6  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #40dfff  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #4136d9  Functions, Methods, Attribute IDs, Headings
base0E  #7e5ce6  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #a886bf  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b08ae6;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #432d59;
  color: #b08ae6
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #593380 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #593380
}
/* base04 - #00ff00 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #00ff00
}
/* base05 - #b08ae6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b08ae6
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #a82ee6
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #bb66cc
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f29df2
}
.hljs-strong {
  font-weight: bold;
  color: #f29df2
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #4595e6
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #40dfff
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #4136d9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #7e5ce6
}
.hljs-emphasis {
  color: #7e5ce6;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #a886bf
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}