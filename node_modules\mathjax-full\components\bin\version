#! /usr/bin/env node

/*************************************************************
 *
 *  Copyright (c) 2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  Creates the version.ts file from the package version number
 *
 * <AUTHOR> (<PERSON><PERSON>)
 */


const fs = require('fs');
const path = require('path');

const package = path.resolve(__dirname, '..', '..', 'package.json');
const version = require(package).version;

const lines = `/*************************************************************
 *
 *  Copyright (c) 2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  The version of MathJax (used to tell what version a component
 *                was compiled against).
 *
 * <AUTHOR> (Davide Cervone)
 */

export const VERSION = '${version}';
`;

fs.writeFileSync(path.resolve(__dirname, '..', '..', 'ts', 'components', 'version.ts'), lines);
