{"version": 3, "file": "FindAsciiMath.js", "sourceRoot": "", "sources": ["../../../ts/input/asciimath/FindAsciiMath.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,sDAAwD;AAExD,kDAAkD;AAClD,sDAA4D;AAkB5D;IAA4C,iCAAyB;IA2BnE,uBAAY,OAAmB;QAA/B,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,WAAW,EAAE,CAAC;;IACrB,CAAC;IAMS,mCAAW,GAArB;QAAA,iBAOC;QANC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAC,MAAc,IAAK,OAAA,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAtC,CAAsC,CAAC,CAAC;QAC1F,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,CAAC;IASS,kCAAU,GAApB,UAAqB,MAAgB,EAAE,MAAc,EAAE,OAAgB;QACjE,IAAA,KAAA,OAAgB,MAAM,IAAA,EAArB,IAAI,QAAA,EAAE,KAAK,QAAU,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,MAAM,CAAC,IAAA,wBAAY,EAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAWS,+BAAO,GAAjB,UAAkB,IAAY,EAAE,CAAS,EAAE,KAAsB,EAAE,GAAY;QACzE,IAAA,KAAA,OAAwB,GAAG,IAAA,EAAvB,OAAO,QAAA,EAAE,OAAO,QAAO,CAAC;QAChC,IAAI,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC1D,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,uBAAS,EAAO,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACnD,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IACnG,CAAC;IASS,wCAAgB,GAA1B,UAA2B,IAAuB,EAAE,CAAS,EAAE,IAAY;QACzE,IAAI,KAAK,EAAE,KAAK,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QACzB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACtC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aACpC;SACF;IACH,CAAC;IAOM,gCAAQ,GAAf,UAAgB,OAAiB;QAC/B,IAAI,IAAI,GAAsB,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC9C,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5C;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAtGa,qBAAO,GAAe;QAClC,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KACzB,CAAC;IAsGJ,oBAAC;CAAA,AA7GD,CAA4C,8BAAgB,GA6G3D;AA7GY,sCAAa"}