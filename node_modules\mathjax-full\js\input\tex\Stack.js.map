{"version": 3, "file": "Stack.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/Stack.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,8DAAqC;AAMrC;IAmBE,eAAoB,QAA0B,EAC1B,IAAa,EAAE,KAAc;QAD7B,aAAQ,GAAR,QAAQ,CAAkB;QAC1B,SAAI,GAAJ,IAAI,CAAS;QAf1B,WAAM,GAAY,EAAE,CAAC;QAMpB,UAAK,GAAgB,EAAE,CAAC;QAU9B,IAAI,CAAC,MAAM,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,CAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAE,CAAC;QAC5D,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/B,CAAC;IAOD,sBAAW,sBAAG;aASd;YACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;aAXD,UAAe,GAAY;YACzB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAClB,CAAC;;;OAAA;IAgBM,oBAAI,GAAX;;QAAY,cAAgC;aAAhC,UAAgC,EAAhC,qBAAgC,EAAhC,IAAgC;YAAhC,yBAAgC;;;YAC1C,KAAmB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAApB,IAAM,IAAI,iBAAA;gBACb,IAAI,CAAC,IAAI,EAAE;oBACT,SAAS;iBACV;gBACD,IAAM,IAAI,GAAG,qBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAiB,CAAC;gBACxD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBACpB,IAAA,KAAA,OACJ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAA,EADxD,KAAG,QAAA,EAAE,OAAO,QAC4C,CAAC;gBAChE,IAAI,CAAC,OAAO,EAAE;oBACZ,SAAS;iBACV;gBACD,IAAI,KAAG,EAAE;oBACP,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,IAAI,CAAC,IAAI,OAAT,IAAI,2BAAS,KAAG,WAAE;oBAClB,SAAS;iBACV;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,IAAI,CAAC,GAAG,EAAE;oBACZ,IAAI,IAAI,CAAC,OAAO,EAAE;wBAChB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;qBACnC;oBACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;iBACrB;qBAAM;oBACL,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;iBACrB;aACF;;;;;;;;;IACH,CAAC;IAOM,mBAAG,GAAV;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,CAAC,GAAG,CAAC;SACjB;QACD,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAQM,mBAAG,GAAV,UAAW,CAAa;QAAb,kBAAA,EAAA,KAAa;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IASM,oBAAI,GAAX,UAAY,KAAe;QACzB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACvC,CAAC;IAMM,wBAAQ,GAAf;QACE,OAAO,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IACxD,CAAC;IAEH,YAAC;AAAD,CAAC,AA9HD,IA8HC"}