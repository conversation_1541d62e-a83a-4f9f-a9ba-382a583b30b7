{"version": 3, "file": "mathml.js", "sourceRoot": "", "sources": ["../../ts/input/mathml.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,mDAAqD;AACrD,iDAA+E;AAC/E,2DAAqD;AAMrD,wDAAkD;AAClD,8DAAwD;AAUxD;IAAqC,0BAAyB;IAyC5D,gBAAY,OAAwB;QAAxB,wBAAA,EAAA,YAAwB;QAApC,iBAMC;QALK,IAAA,KAAA,OAAuB,IAAA,4BAAe,EAAC,OAAO,EAAE,0BAAU,CAAC,OAAO,EAAE,gCAAa,CAAC,OAAO,CAAC,IAAA,EAAzF,GAAG,QAAA,EAAE,IAAI,QAAA,EAAE,OAAO,QAAuE,CAAC;gBAC/F,kBAAM,GAAG,CAAC;QACV,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,0BAAU,CAAU,IAAI,CAAC,CAAC;QAC9E,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,gCAAa,CAAU,OAAO,CAAC,CAAC;QACnF,KAAI,CAAC,UAAU,GAAG,IAAI,8BAAY,EAAE,CAAC;;IACvC,CAAC;IAOM,2BAAU,GAAjB,UAAkB,OAA4B;QAC5C,iBAAM,UAAU,YAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,CAAC;IAKM,8BAAa,GAApB,UAAqB,UAAsB;QACzC,iBAAM,aAAa,YAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAOD,sBAAW,kCAAc;aAAzB;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAkBM,wBAAO,GAAd,UAAe,IAAuB,EAAE,QAA+B;QACrE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,EAAE;YAChG,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,eAAe,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACzG,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7F,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9C,IAAI,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;aACvD;YACD,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAM,CAAC;YAC9D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,MAAM,EAAE;gBAC7D,IAAI,CAAC,KAAK,CAAC,kDAAkD,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;aAC/F;SACF;QACD,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAQ,CAAC,CAAC,CAAC;IAC9F,CAAC;IAQS,+BAAc,GAAxB,UAAyB,GAAM;QAC7B,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,IAAI,GAAG,EAAE;YACP,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE;gBACxC,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;aACvC;YACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC5C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAOS,sBAAK,GAAf,UAAgB,OAAe;QAC7B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAKM,yBAAQ,GAAf,UAAgB,IAAO;QACrB,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAzIa,WAAI,GAAW,QAAQ,CAAC;IAKxB,cAAO,GAAe,IAAA,2BAAc,EAAC;QACjD,OAAO,EAAE,MAAM;QACf,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,IAAI;QAInB,UAAU,EAAE,UAAU,IAAU;YAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC;KACF,EAAE,8BAAgB,CAAC,OAAO,CAAC,CAAC;IA2H/B,aAAC;CAAA,AAhJD,CAAqC,8BAAgB,GAgJpD;AAhJY,wBAAM"}