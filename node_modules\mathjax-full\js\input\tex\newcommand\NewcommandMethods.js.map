{"version": 3, "file": "NewcommandMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/newcommand/NewcommandMethods.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,+DAAsC;AAEtC,kDAAsC;AAEtC,0EAAiD;AACjD,iEAAwC;AAExC,0EAAiD;AAIjD,IAAI,iBAAiB,GAAgC,EAAE,CAAC;AAOxD,iBAAiB,CAAC,UAAU,GAAG,UAAS,MAAiB,EAAE,IAAY;IAErE,IAAI,EAAE,GAAG,2BAAc,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACxD,IAAI,CAAC,GAAG,2BAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACjD,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC;AAQF,iBAAiB,CAAC,cAAc,GAAG,UAAS,MAAiB,EAAE,IAAY;IAEzE,IAAI,GAAG,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,IAAI,CAAC,GAAG,2BAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACjD,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpC,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpC,2BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,iBAAiB,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACrG,CAAC,CAAC;AAQF,iBAAiB,CAAC,QAAQ,GAAG,UAAS,MAAiB,EAAE,IAAY;IAEnE,IAAI,EAAE,GAAG,2BAAc,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAChD,IAAI,MAAM,GAAG,2BAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;IACjE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC;QAE1B,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7E,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,iBAAiB,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AACnG,CAAC,CAAC;AAmBF,iBAAiB,CAAC,GAAG,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC9D,IAAM,EAAE,GAAG,2BAAc,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAClD,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAEzB,IAAI,CAAC,KAAK,GAAG,EAAE;QAEb,MAAM,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;KACtB;IACD,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAC,KAAK,IAAI,EAAE;QAEd,IAAI,GAAG,2BAAc,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,OAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAW,CAAC;QACpE,IAAI,OAAK,EAAE;YAET,2BAAc,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,OAAK,CAAC,IAAI,EAAE,OAAK,CAAC,UAAU,CAAC,CAAC;YAC7E,OAAO;SACR;QACD,IAAM,KAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,KAAG,EAAE;YAER,OAAO;SACR;QACD,IAAI,KAAG,YAAY,EAAE,CAAC,QAAQ,EAAE;YAE9B,IAAM,OAAK,GAAI,KAAqB,CAAC,MAAM,CAAC,IAAI,CAAU,CAAC;YAC3D,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,OAAK,CAAC,IAAI,EAAE,OAAK,CAAC,IAAI,EAAE,OAAK,CAAC,MAAM,CAAC,CAAC;YAC1E,OAAO;SACR;QACD,OAAK,GAAI,KAAuB,CAAC,MAAM,CAAC,IAAI,CAAW,CAAC;QACxD,IAAM,OAAO,GAAG,2BAAc,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAK,CAAC,CAAC;QAC5D,IAAM,MAAM,GAAG,UAAC,CAAY,EAAE,GAAW;YAAE,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,6BAAc;;YAEvD,IAAM,IAAI,GAAG,2BAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACjD,OAAO,KAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC;QACF,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QACrD,OAAO;KACR;IAED,MAAM,CAAC,CAAC,EAAE,CAAC;IACX,IAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAW,CAAC;IAC5D,IAAI,KAAK,EAAE;QAET,2BAAc,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC7E,OAAO;KACR;IAED,2BAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC,CAAC;AAYF,iBAAiB,CAAC,iBAAiB,GAAG,UAAU,MAAiB,EAAE,IAAY,EAC/B,IAAY,EAAE,CAAS;IACvB,gBAAmB;SAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;QAAnB,+BAAmB;;IACjE,IAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEjC,IAAI,QAAQ,EAAE;QAEZ,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,CAAC,OAAO,EAAE,CAAC;QACjB,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,2BAAc,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YAE9D,MAAM,IAAI,qBAAQ,CAAC,gBAAgB,EACf,yCAAyC,EAAE,IAAI,CAAC,CAAC;SACtE;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;YAEjC,IAAI,CAAC,IAAI,CAAC,2BAAc,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACrE;QACD,IAAI,GAAG,sBAAS,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACrD;IACD,MAAM,CAAC,MAAM,GAAG,sBAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,sBAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC;AAYF,iBAAiB,CAAC,QAAQ,GAAG,UAAS,MAAiB,EAAE,KAAgB,EACnC,IAAY,EAAE,IAAY,EAAE,CAAS,EAAE,GAAW;IAGtF,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;QAE/E,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEnC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,MAAM,CAAC,KAAK,EAAE,CAAC;QAEf,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAEb,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KAC9E;IACD,IAAI,CAAC,EAAE;QAEL,IAAI,IAAI,GAAa,EAAE,CAAC;QACxB,IAAI,GAAG,IAAI,IAAI,EAAE;YAEf,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;SAC9C;QACD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;SACnE;QACD,IAAI,GAAG,sBAAS,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACpD,IAAI,GAAG,sBAAS,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;KACnD;IACD,MAAM,CAAC,MAAM,GAAG,sBAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AACpF,CAAC,CAAC;AAEF,iBAAiB,CAAC,KAAK,GAAG,wBAAW,CAAC,KAAK,CAAC;AAE5C,kBAAe,iBAAiB,CAAC"}