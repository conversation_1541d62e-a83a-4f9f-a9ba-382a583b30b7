{"version": 3, "sources": ["../../mathjax-full/ts/core/FindMath.ts", "../../mathjax-full/ts/input/tex/FindTeX.ts", "../../mathjax-full/ts/input/tex/FilterUtil.ts", "../../mathjax-full/ts/input/tex.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Interfaces and abstract classes for FindMath objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {userOptions, defaultOptions, OptionList} from '../util/Options.js';\nimport {ProtoItem} from './MathItem.js';\n\n/*****************************************************************/\n/**\n *  The FindMath interface\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template _D  The Document class\n */\nexport interface FindMath<N, T, _D> {\n  /**\n   * One of two possibilities:  Look through a DOM element,\n   *   or look through an array of strings for delimited math.\n   *\n   * @param {N} node   The node to search for math\n   * @return {ProtoItem<N, T>[]}\n   */\n  findMath(node: N): ProtoItem<N, T>[];\n  /**\n   *\n   * @param {string[]} strings    The strings to search for math\n   * @return {ProtoItem<N, T>[]}\n   */\n  findMath(strings: string[]): ProtoItem<N, T>[];\n}\n\n/*****************************************************************/\n/**\n *  The FindMath abstract class\n */\n\n/**\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractFindMath<N, T, D> implements FindMath<N, T, D> {\n\n  /**\n   * The default options for FindMath\n   */\n  public static OPTIONS: OptionList = {};\n\n  /**\n   * The actual options for this instance\n   */\n  protected options: OptionList;\n\n  /**\n   * @param {OptionList} options  The user options for this instance\n   */\n  constructor(options: OptionList) {\n    let CLASS = this.constructor as typeof AbstractFindMath;\n    this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), options);\n  }\n\n  /**\n   * Locate math in an Element or a string array;\n   *\n   * @param {Element | string[]} where  The node or string array to search for math\n   * @return {ProtoItem[]}              The array of proto math items found\n   */\n  public abstract findMath(where: N | string[]): ProtoItem<N, T>[];\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the TeX version of the FindMath object\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractFindMath} from '../../core/FindMath.js';\nimport {OptionList} from '../../util/Options.js';\nimport {sortLength, quotePattern} from '../../util/string.js';\nimport {ProtoItem, protoItem} from '../../core/MathItem.js';\n\n/**\n * Shorthand types for data about end delimiters and delimiter pairs\n */\nexport type EndItem = [string, boolean, RegExp];\nexport type Delims = [string, string];\n\n/*****************************************************************/\n/*\n *  Implements the FindTeX class (extends AbstractFindMath)\n *\n *  Locates TeX expressions within strings\n */\n\n/*\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class FindTeX<N, T, D> extends AbstractFindMath<N, T, D> {\n\n  /**\n   * @type {OptionList}\n   */\n  public static OPTIONS: OptionList = {\n    inlineMath: [              // The start/end delimiter pairs for in-line math\n      //  ['$', '$'],              //  (comment out any you don't want, or add your own, but\n      ['\\\\(', '\\\\)']           //  be sure that you don't have an extra comma at the end)\n    ],\n\n    displayMath: [             // The start/end delimiter pairs for display math\n      ['$$', '$$'],            //  (comment out any you don't want, or add your own, but\n      ['\\\\[', '\\\\]']           //  be sure that you don't have an extra comma at the end)\n    ],\n\n    processEscapes: true,      // set to true to allow \\$ to produce a dollar without\n    //   starting in-line math mode\n    processEnvironments: true, // set to true to process \\begin{xxx}...\\end{xxx} outside\n    //   of math mode, false to prevent that\n    processRefs: true,         // set to true to process \\ref{...} outside of math mode\n  };\n\n  /**\n   * The regular expression for any starting delimiter\n   */\n  protected start: RegExp;\n\n  /**\n   * The end-delimiter data keyed to the opening delimiter string\n   */\n  protected end: {[name: string]: EndItem};\n\n  /**\n   * False if the configuration has no delimiters (so search can be skipped), true otherwise\n   */\n  protected hasPatterns: boolean;\n\n  /**\n   * The index of the \\begin...\\end pattern in the regex match array\n   */\n  protected env: number;\n\n  /**\n   * The index of the \\ref and escaped character patters in the regex match array\n   */\n  protected sub: number;\n\n  /**\n   * @override\n   */\n  constructor(options: OptionList) {\n    super(options);\n    this.getPatterns();\n  }\n\n  /**\n   * Create the patterns needed for searching the strings for TeX\n   *   based on the configuration options\n   */\n  protected getPatterns() {\n    let options = this.options;\n    let starts: string[] = [], parts: string[] = [], subparts: string[] = [];\n    this.end = {};\n    this.env = this.sub = 0;\n    let i = 1;\n    options['inlineMath'].forEach((delims: Delims) => this.addPattern(starts, delims, false));\n    options['displayMath'].forEach((delims: Delims) => this.addPattern(starts, delims, true));\n    if (starts.length) {\n      parts.push(starts.sort(sortLength).join('|'));\n    }\n    if (options['processEnvironments']) {\n      parts.push('\\\\\\\\begin\\\\s*\\\\{([^}]*)\\\\}');\n      this.env = i;\n      i++;\n    }\n    if (options['processEscapes']) {\n      subparts.push('\\\\\\\\([\\\\\\\\$])');\n    }\n    if (options['processRefs']) {\n      subparts.push('(\\\\\\\\(?:eq)?ref\\\\s*\\\\{[^}]*\\\\})');\n    }\n    if (subparts.length) {\n      parts.push('(' + subparts.join('|') + ')');\n      this.sub = i;\n    }\n    this.start = new RegExp(parts.join('|'), 'g');\n    this.hasPatterns = (parts.length > 0);\n  }\n\n  /**\n   * Add the needed patterns for a pair of delimiters\n   *\n   * @param {string[]} starts  Array of starting delimiter strings\n   * @param {Delims} delims    Array of delimiter strings, as [start, end]\n   * @param {boolean} display  True if the delimiters are for display mode\n   */\n  protected addPattern(starts: string[], delims: Delims, display: boolean) {\n    let [open, close] = delims;\n    starts.push(quotePattern(open));\n    this.end[open] = [close, display, this.endPattern(close)];\n  }\n\n  /**\n   * Create the pattern for a close delimiter\n   *\n   * @param {string} end   The end delimiter text\n   * @param {string} endp  The end delimiter pattern (overrides the literal end pattern)\n   * @return {RegExp}      The regular expression for the end delimiter\n   */\n  protected endPattern(end: string, endp?: string): RegExp {\n    return new RegExp((endp || quotePattern(end)) + '|\\\\\\\\(?:[a-zA-Z]|.)|[{}]', 'g');\n  }\n\n  /**\n   * Search for the end delimiter given the start delimiter,\n   *   skipping braced groups, and control sequences that aren't\n   *   the close delimiter.\n   *\n   * @param {string} text            The string being searched for the end delimiter\n   * @param {number} n               The index of the string being searched\n   * @param {RegExpExecArray} start  The result array from the start-delimiter search\n   * @param {EndItem} end            The end-delimiter data corresponding to the start delimiter\n   * @return {ProtoItem<N,T>}        The proto math item for the math, if found\n   */\n  protected findEnd(text: string, n: number, start: RegExpExecArray, end: EndItem): ProtoItem<N, T> {\n    let [close, display, pattern] = end;\n    let i = pattern.lastIndex = start.index + start[0].length;\n    let match: RegExpExecArray, braces: number = 0;\n    while ((match = pattern.exec(text))) {\n      if ((match[1] || match[0]) === close && braces === 0) {\n        return protoItem<N, T>(start[0], text.substr(i, match.index - i), match[0],\n                               n, start.index, match.index + match[0].length, display);\n      } else if (match[0] === '{') {\n        braces++;\n      } else if (match[0] === '}' && braces) {\n        braces--;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Search a string for math delimited by one of the delimiter pairs,\n   *   or by \\begin{env}...\\end{env}, or \\eqref{...}, \\ref{...}, \\\\, or \\$.\n   *\n   * @param {ProtoItem[]} math  The array of proto math items located so far\n   * @param {number} n          The index of the string being searched\n   * @param {string} text       The string being searched\n   */\n  protected findMathInString(math: ProtoItem<N, T>[], n: number, text: string) {\n    let start, match;\n    this.start.lastIndex = 0;\n    while ((start = this.start.exec(text))) {\n      if (start[this.env] !== undefined && this.env) {\n        let end = '\\\\\\\\end\\\\s*(\\\\{' + quotePattern(start[this.env]) + '\\\\})';\n        match = this.findEnd(text, n, start, ['{' + start[this.env] + '}', true, this.endPattern(null, end)]);\n        if (match) {\n          match.math = match.open + match.math + match.close;\n          match.open = match.close = '';\n        }\n      } else if (start[this.sub] !== undefined && this.sub) {\n        let math = start[this.sub];\n        let end = start.index + start[this.sub].length;\n        if (math.length === 2) {\n          match = protoItem<N, T>('', math.substr(1), '', n, start.index, end);\n        } else {\n          match = protoItem<N, T>('', math, '', n, start.index, end, false);\n        }\n      } else {\n        match = this.findEnd(text, n, start, this.end[start[0]]);\n      }\n      if (match) {\n        math.push(match);\n        this.start.lastIndex = match.end.n;\n      }\n    }\n  }\n\n  /**\n   * Search for math in an array of strings and return an array of matches.\n   *\n   * @override\n   */\n  public findMath(strings: string[]) {\n    let math: ProtoItem<N, T>[] = [];\n    if (this.hasPatterns) {\n      for (let i = 0, m = strings.length; i < m; i++) {\n        this.findMathInString(math, i, strings[i]);\n      }\n    }\n    return math;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Utility functions for standard pre and post filters.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\n\nimport {TEXCLASS, MMLNODE, MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport NodeUtil from './NodeUtil.js';\nimport ParseOptions from './ParseOptions.js';\nimport {MmlMo} from '../../core/MmlTree/MmlNodes/mo.js';\nimport {Attributes} from '../../core/MmlTree/Attributes.js';\n\n\nnamespace FilterUtil {\n\n  /**\n   * Visitor to set stretchy attributes to false on <mo> elements, if they are\n   * not used as delimiters. Also wraps non-stretchy infix delimiters into a\n   * TeXAtom.\n   * @param {MmlNode} math The node to rewrite.\n   * @param {ParseOptions} data The parse options.\n   */\n  export let cleanStretchy = function(arg: {math: any, data: ParseOptions}) {\n    let options = arg.data;\n    for (let mo of options.getList('fixStretchy')) {\n      if (NodeUtil.getProperty(mo, 'fixStretchy')) {\n        let symbol = NodeUtil.getForm(mo);\n        if (symbol && symbol[3] && symbol[3]['stretchy']) {\n          NodeUtil.setAttribute(mo, 'stretchy', false);\n        }\n        const parent = mo.parent;\n        if (!NodeUtil.getTexClass(mo) && (!symbol || !symbol[2])) {\n          const texAtom = options.nodeFactory.create('node', 'TeXAtom', [mo]);\n          parent.replaceChild(texAtom, mo);\n          texAtom.inheritAttributesFrom(mo);\n        }\n        NodeUtil.removeProperties(mo, 'fixStretchy');\n      }\n    }\n  };\n\n\n  /**\n   * Visitor that removes superfluous attributes from nodes. I.e., if a node has\n   * an attribute, which is also an inherited attribute it will be removed. This\n   * is necessary as attributes are set bottom up in the parser.\n   * @param {ParseOptions} data The parse options.\n   */\n  export let cleanAttributes = function(arg: {data: ParseOptions}) {\n    let node = arg.data.root as MmlNode;\n    node.walkTree((mml: MmlNode, _d: any) => {\n      let attribs = mml.attributes as any;\n      if (!attribs) {\n        return;\n      }\n      const keep = new Set((attribs.get('mjx-keep-attrs') || '').split(/ /));\n      delete (attribs.getAllAttributes())['mjx-keep-attrs'];\n      for (const key of attribs.getExplicitNames()) {\n        if (!keep.has(key) && attribs.attributes[key] === mml.attributes.getInherited(key)) {\n          delete attribs.attributes[key];\n        }\n      }\n    }, {});\n  };\n\n\n  /**\n   * Combine adjacent <mo> elements that are relations (since MathML treats the\n   * spacing very differently)\n   * @param {ParseOptions} data The parse options.\n   */\n  export let combineRelations = function(arg: {data: ParseOptions}) {\n    const remove: MmlNode[] = [];\n    for (let mo of arg.data.getList('mo')) {\n      if (mo.getProperty('relationsCombined') || !mo.parent ||\n          (mo.parent && !NodeUtil.isType(mo.parent, 'mrow')) ||\n          NodeUtil.getTexClass(mo) !== TEXCLASS.REL) {\n        // @test Prime, PrimeSup, Named Function\n        continue;\n      }\n      let mml = mo.parent;\n      let m2: MmlNode;\n      let children = mml.childNodes as MMLNODE[];\n      let next = children.indexOf(mo) + 1;\n      let variantForm = NodeUtil.getProperty(mo, 'variantForm');\n      while (next < children.length && (m2 = children[next]) &&\n             NodeUtil.isType(m2, 'mo') &&\n             NodeUtil.getTexClass(m2) === TEXCLASS.REL) {\n        if (variantForm === NodeUtil.getProperty(m2, 'variantForm') &&\n            _compareExplicit(mo, m2)) {\n          // @test Shift Left, Less Equal,\n          //       Multirel Font X, Multirel Mathvariant X\n          NodeUtil.appendChildren(mo, NodeUtil.getChildren(m2));\n          // This treatment means we might loose some inheritance structure, but\n          // no properties.\n          _copyExplicit(['stretchy', 'rspace'], mo, m2);\n          for (const name of m2.getPropertyNames()) {\n            mo.setProperty(name, m2.getProperty(name));\n          }\n          children.splice(next, 1);\n          remove.push(m2);\n          m2.parent = null;\n          m2.setProperty('relationsCombined', true);\n        } else {\n          // @test Preset Rspace Lspace\n          if (mo.attributes.getExplicit('rspace') == null) {\n            // @test Mulitrel Mathvariant 3, Mulitrel Mathvariant 4\n            NodeUtil.setAttribute(mo, 'rspace', '0pt');\n          }\n          if (m2.attributes.getExplicit('lspace') == null) {\n            // @test Mulitrel Mathvariant 3, Mulitrel Mathvariant 4\n            NodeUtil.setAttribute(m2, 'lspace', '0pt');\n          }\n          break;\n        }\n      }\n      mo.attributes.setInherited('form', (mo as MmlMo).getForms()[0]);\n    }\n    arg.data.removeFromList('mo', remove);\n  };\n\n\n  /**\n   * Copies the specified explicit attributes from node2 to node1.\n   * @param {string[]} attrs List of explicit attribute names.\n   * @param {MmlNode} node1 The goal node.\n   * @param {MmlNode} node2 The source node.\n   */\n  let _copyExplicit = function(attrs: string[],\n                               node1: MmlNode, node2: MmlNode) {\n    let attr1 = node1.attributes;\n    let attr2 = node2.attributes;\n    attrs.forEach(x => {\n      let attr = attr2.getExplicit(x);\n      if (attr != null) {\n        // @test Infix Stretchy Right, Preset Lspace Rspace\n        attr1.set(x, attr);\n      }\n    });\n  };\n\n\n  /**\n   * Compares the explicit attributes of two nodes. Returns true if they\n   * coincide, with the following exceptions:\n   *   - lspace attribute of node1 is ignored.\n   *   - rspace attribute of node2 is ignored.\n   *   - stretchy=false attributes are ignored.\n   * @param {MmlNode} node1 The first node.\n   * @param {MmlNode} node2 Its next sibling.\n   */\n  let _compareExplicit = function(node1: MmlNode, node2: MmlNode) {\n    let filter = (attr: Attributes, space: string): string[] => {\n      let exp = attr.getExplicitNames();\n      return exp.filter(x => {\n        return x !== space &&\n          (x !== 'stretchy' ||\n           attr.getExplicit('stretchy'));\n      });\n    };\n    let attr1 = node1.attributes;\n    let attr2 = node2.attributes;\n    let exp1 = filter(attr1, 'lspace');\n    let exp2 = filter(attr2, 'rspace');\n    if (exp1.length !== exp2.length) {\n      return false;\n    }\n    for (let name of exp1) {\n      if (attr1.getExplicit(name) !== attr2.getExplicit(name)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * Cleans msubsup and munderover elements.\n   * @param {ParseOptions} options The parse options.\n   * @param {string} low String representing the lower part of the expression.\n   * @param {string} up String representing the upper part.\n   */\n  let _cleanSubSup = function(options: ParseOptions, low: string, up: string) {\n    const remove: MmlNode[] = [];\n    for (let mml of options.getList('m' + low + up) as any[]) {\n      const children = mml.childNodes;\n      if (children[mml[low]] && children[mml[up]]) {\n        continue;\n      }\n      const parent = mml.parent;\n      let newNode = (children[mml[low]] ?\n                 options.nodeFactory.create('node', 'm' + low, [children[mml.base], children[mml[low]]]) :\n                 options.nodeFactory.create('node', 'm' + up, [children[mml.base], children[mml[up]]]));\n      NodeUtil.copyAttributes(mml, newNode);\n      if (parent) {\n        parent.replaceChild(newNode, mml);\n      } else {\n        options.root = newNode;\n      }\n      remove.push(mml);\n    }\n    options.removeFromList('m' + low + up, remove);\n  };\n\n\n  /**\n   * Visitor that rewrites incomplete msubsup/munderover elements in the given\n   * node into corresponding msub/sup/under/over nodes.\n   * @param {MmlNode} math The node to rewrite.\n   * @param {ParseOptions} data The parse options.\n   */\n  export let cleanSubSup = function(arg: {math: any, data: ParseOptions}) {\n    let options = arg.data;\n    if (options.error) {\n      return;\n    }\n    _cleanSubSup(options, 'sub', 'sup');\n    _cleanSubSup(options, 'under', 'over');\n  };\n\n\n  /**\n   * Looks through the list of munderover elements for ones that have\n   * movablelimits and bases that are not mo's, and creates new msubsup\n   * elements to replace them if they aren't in displaystyle.\n   *\n   * @param {MmlNode} ath The node to rewrite.\n   * @param {ParseOptions} data The parse options.\n   */\n  let _moveLimits = function (options: ParseOptions, underover: string, subsup: string) {\n    const remove: MmlNode[] = [];\n    for (const mml of options.getList(underover)) {\n      if (mml.attributes.get('displaystyle')) {\n        continue;\n      }\n      const base = mml.childNodes[(mml as any).base] as MmlNode;\n      const mo = base.coreMO();\n      if (base.getProperty('movablelimits') && !mo.attributes.getExplicit('movablelimits')) {\n        let node = options.nodeFactory.create('node', subsup, mml.childNodes);\n        NodeUtil.copyAttributes(mml, node);\n        if (mml.parent) {\n          mml.parent.replaceChild(node, mml);\n        } else {\n          options.root = node;\n        }\n        remove.push(mml);\n      }\n    }\n    options.removeFromList(underover, remove);\n  };\n\n  /**\n   * Visitor that rewrites in-line munderover elements with movablelimits but bases\n   * that are not mo's into explicit msubsup elements.\n   *\n   * @param {ParseOptions} data  The parse options to use\n   */\n  export let moveLimits = function (arg: {data: ParseOptions}) {\n    const options = arg.data;\n    _moveLimits(options, 'munderover', 'msubsup');\n    _moveLimits(options, 'munder', 'msub');\n    _moveLimits(options, 'mover', 'msup');\n  };\n\n\n  /**\n   * Recursively sets the inherited attributes on the math tree.\n   * @param {MmlNode} math The node to rewrite.\n   * @param {ParseOptions} data The parse options.\n   */\n  export let setInherited = function(arg: {math: any, data: ParseOptions}) {\n    arg.data.root.setInheritedAttributes({}, arg.math['display'], 0, false);\n  };\n\n}\n\n\nexport default FilterUtil;\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the TeX InputJax object\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractInputJax} from '../core/InputJax.js';\nimport {userOptions, separateOptions, OptionList} from '../util/Options.js';\nimport {MathDocument} from '../core/MathDocument.js';\nimport {MathItem} from '../core/MathItem.js';\nimport {MmlNode} from '../core/MmlTree/MmlNode.js';\nimport {MmlFactory} from '../core/MmlTree/MmlFactory.js';\n\nimport {FindTeX} from './tex/FindTeX.js';\n\nimport FilterUtil from './tex/FilterUtil.js';\nimport NodeUtil from './tex/NodeUtil.js';\nimport TexParser from './tex/TexParser.js';\nimport TexError from './tex/TexError.js';\nimport ParseOptions from './tex/ParseOptions.js';\nimport {TagsFactory} from './tex/Tags.js';\nimport {ParserConfiguration} from './tex/Configuration.js';\n// Import base as it is the default package loaded.\nimport './tex/base/BaseConfiguration.js';\n\n\n/*****************************************************************/\n/*\n *  Implements the TeX class (extends AbstractInputJax)\n */\n\n/**\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport class TeX<N, T, D> extends AbstractInputJax<N, T, D> {\n\n  /**\n   * Name of input jax.\n   * @type {string}\n   */\n  public static NAME: string = 'TeX';\n\n  /**\n   * Default options for the jax.\n   * @type {OptionList}\n   */\n  public static OPTIONS: OptionList = {\n    ...AbstractInputJax.OPTIONS,\n    FindTeX: null,\n    packages: ['base'],\n    // Digit pattern to match numbers.\n    digits: /^(?:[0-9]+(?:\\{,\\}[0-9]{3})*(?:\\.[0-9]*)?|\\.[0-9]+)/,\n    // Maximum size of TeX string to process.\n    maxBuffer: 5 * 1024,\n    formatError: (jax: TeX<any, any, any>, err: TexError) => jax.formatError(err)\n  };\n\n  /**\n   * The FindTeX instance used for locating TeX in strings\n   */\n  protected findTeX: FindTeX<N, T, D>;\n\n  /**\n   * The configuration of the TeX jax.\n   * @type {ParserConfiguration}\n   */\n  protected configuration: ParserConfiguration;\n\n  /**\n   * The LaTeX code that is parsed.\n   * @type {string}\n   */\n  protected latex: string;\n\n  /**\n   * The Math node that results from parsing.\n   * @type {MmlNode}\n   */\n  protected mathNode: MmlNode;\n\n  private _parseOptions: ParseOptions;\n\n  /**\n   * Initialises the configurations.\n   * @param {string[]} packages Names of packages.\n   * @return {Configuration} The configuration object.\n   */\n  protected static configure(packages: (string | [string, number])[]): ParserConfiguration {\n    let configuration = new ParserConfiguration(packages, ['tex']);\n    configuration.init();\n    return configuration;\n  }\n\n\n  /**\n   * Initialises the Tags factory. Add tagging structures from packages and set\n   * tagging to given default.\n   * @param {ParseOptions} options The parse options.\n   * @param {Configuration} configuration The configuration.\n   */\n  protected static tags(options: ParseOptions, configuration: ParserConfiguration) {\n    TagsFactory.addTags(configuration.tags);\n    TagsFactory.setDefault(options.options.tags);\n    options.tags = TagsFactory.getDefault();\n    options.tags.configuration = options;\n  }\n\n\n  /**\n   * @override\n   */\n  constructor(options: OptionList = {}) {\n    const [rest, tex, find] = separateOptions(options, TeX.OPTIONS, FindTeX.OPTIONS);\n    super(tex);\n    this.findTeX = this.options['FindTeX'] || new FindTeX(find);\n    const packages = this.options.packages;\n    const configuration = this.configuration = TeX.configure(packages);\n    const parseOptions = this._parseOptions =\n      new ParseOptions(configuration, [this.options, TagsFactory.OPTIONS]);\n    userOptions(parseOptions.options, rest);\n    configuration.config(this);\n    TeX.tags(parseOptions, configuration);\n    this.postFilters.add(FilterUtil.cleanSubSup, -6);\n    this.postFilters.add(FilterUtil.setInherited, -5);\n    this.postFilters.add(FilterUtil.moveLimits, -4);\n    this.postFilters.add(FilterUtil.cleanStretchy, -3);\n    this.postFilters.add(FilterUtil.cleanAttributes, -2);\n    this.postFilters.add(FilterUtil.combineRelations, -1);\n  }\n\n  /**\n   * @override\n   */\n  public setMmlFactory(mmlFactory: MmlFactory) {\n    super.setMmlFactory(mmlFactory);\n    this._parseOptions.nodeFactory.setMmlFactory(mmlFactory);\n  }\n\n\n  /**\n   * @return {ParseOptions} The parse options that configure this JaX instance.\n   */\n  public get parseOptions(): ParseOptions {\n    return this._parseOptions;\n  }\n\n  /**\n   * @override\n   */\n  public reset(tag: number = 0) {\n    this.parseOptions.tags.reset(tag);\n  }\n\n\n  /**\n   * @override\n   */\n  public compile(math: MathItem<N, T, D>, document: MathDocument<N, T, D>): MmlNode {\n    this.parseOptions.clear();\n    this.executeFilters(this.preFilters, math, document, this.parseOptions);\n    let display = math.display;\n    this.latex = math.math;\n    let node: MmlNode;\n    this.parseOptions.tags.startEquation(math);\n    let globalEnv;\n    try {\n      let parser = new TexParser(this.latex,\n                                 {display: display, isInner: false},\n                                 this.parseOptions);\n      node = parser.mml();\n      globalEnv = parser.stack.global;\n    } catch (err) {\n      if (!(err instanceof TexError)) {\n        throw err;\n      }\n      this.parseOptions.error = true;\n      node = this.options.formatError(this, err);\n    }\n    node = this.parseOptions.nodeFactory.create('node', 'math', [node]);\n    if (globalEnv?.indentalign) {\n      NodeUtil.setAttribute(node, 'indentalign', globalEnv.indentalign);\n    }\n    if (display) {\n      NodeUtil.setAttribute(node, 'display', 'block');\n    }\n    this.parseOptions.tags.finishEquation(math);\n    this.parseOptions.root = node;\n    this.executeFilters(this.postFilters, math, document, this.parseOptions);\n    this.mathNode = this.parseOptions.root;\n    return this.mathNode;\n  }\n\n\n  /**\n   * @override\n   */\n  public findMath(strings: string[]) {\n    return this.findTeX.findMath(strings);\n  }\n\n  /**\n   * Default formatter for error messages:\n   *   wrap an error into a node for output.\n   * @param {TeXError} err The TexError.\n   * @return {Node} The merror node.\n   */\n  public formatError(err: TexError): MmlNode {\n    let message = err.message.replace(/\\n.*/, '');\n    return this.parseOptions.nodeFactory.create(\n      'error', message, err.id, this.latex);\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,QAAA,eAAA;AAsCA,QAAA,mBAAA,WAAA;AAeE,eAAAA,kBAAY,SAAmB;AAC7B,YAAI,QAAQ,KAAK;AACjB,aAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,OAAO;MACvE;AAbc,MAAAA,kBAAA,UAAsB,CAAA;AAuBtC,aAAAA;MA5BA;AAAsB,YAAA,mBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCtB,QAAA,gBAAA;AAEA,QAAA,cAAA;AACA,QAAA,gBAAA;AAoBA,QAAA,UAAA,SAAA,QAAA;AAAsC,gBAAAC,UAAA,MAAA;AAmDpC,eAAAA,SAAY,SAAmB;AAA/B,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,YAAW;;MAClB;AAMU,MAAAA,SAAA,UAAA,cAAV,WAAA;AAAA,YAAA,QAAA;AACE,YAAI,UAAU,KAAK;AACnB,YAAI,SAAmB,CAAA,GAAI,QAAkB,CAAA,GAAI,WAAqB,CAAA;AACtE,aAAK,MAAM,CAAA;AACX,aAAK,MAAM,KAAK,MAAM;AACtB,YAAI,IAAI;AACR,gBAAQ,YAAY,EAAE,QAAQ,SAAC,QAAc;AAAK,iBAAA,MAAK,WAAW,QAAQ,QAAQ,KAAK;QAArC,CAAsC;AACxF,gBAAQ,aAAa,EAAE,QAAQ,SAAC,QAAc;AAAK,iBAAA,MAAK,WAAW,QAAQ,QAAQ,IAAI;QAApC,CAAqC;AACxF,YAAI,OAAO,QAAQ;AACjB,gBAAM,KAAK,OAAO,KAAK,YAAA,UAAU,EAAE,KAAK,GAAG,CAAC;;AAE9C,YAAI,QAAQ,qBAAqB,GAAG;AAClC,gBAAM,KAAK,4BAA4B;AACvC,eAAK,MAAM;AACX;;AAEF,YAAI,QAAQ,gBAAgB,GAAG;AAC7B,mBAAS,KAAK,eAAe;;AAE/B,YAAI,QAAQ,aAAa,GAAG;AAC1B,mBAAS,KAAK,iCAAiC;;AAEjD,YAAI,SAAS,QAAQ;AACnB,gBAAM,KAAK,MAAM,SAAS,KAAK,GAAG,IAAI,GAAG;AACzC,eAAK,MAAM;;AAEb,aAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,GAAG,GAAG,GAAG;AAC5C,aAAK,cAAe,MAAM,SAAS;MACrC;AASU,MAAAA,SAAA,UAAA,aAAV,SAAqB,QAAkB,QAAgB,SAAgB;AACjE,YAAA,KAAA,OAAgB,QAAM,CAAA,GAArB,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAChB,eAAO,MAAK,GAAA,YAAA,cAAa,IAAI,CAAC;AAC9B,aAAK,IAAI,IAAI,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,KAAK,CAAC;MAC1D;AASU,MAAAA,SAAA,UAAA,aAAV,SAAqB,KAAa,MAAa;AAC7C,eAAO,IAAI,QAAQ,SAAQ,GAAA,YAAA,cAAa,GAAG,KAAK,4BAA4B,GAAG;MACjF;AAaU,MAAAA,SAAA,UAAA,UAAV,SAAkB,MAAc,GAAW,OAAwB,KAAY;AACzE,YAAA,KAAA,OAA4B,KAAG,CAAA,GAA9B,QAAK,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AAC5B,YAAI,IAAI,QAAQ,YAAY,MAAM,QAAQ,MAAM,CAAC,EAAE;AACnD,YAAI,OAAwB,SAAiB;AAC7C,eAAQ,QAAQ,QAAQ,KAAK,IAAI,GAAI;AACnC,eAAK,MAAM,CAAC,KAAK,MAAM,CAAC,OAAO,SAAS,WAAW,GAAG;AACpD,oBAAO,GAAA,cAAA,WAAgB,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC,GAAG,MAAM,CAAC,GAClD,GAAG,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,QAAQ,OAAO;qBACpE,MAAM,CAAC,MAAM,KAAK;AAC3B;qBACS,MAAM,CAAC,MAAM,OAAO,QAAQ;AACrC;;;AAGJ,eAAO;MACT;AAUU,MAAAA,SAAA,UAAA,mBAAV,SAA2B,MAAyB,GAAW,MAAY;AACzE,YAAI,OAAO;AACX,aAAK,MAAM,YAAY;AACvB,eAAQ,QAAQ,KAAK,MAAM,KAAK,IAAI,GAAI;AACtC,cAAI,MAAM,KAAK,GAAG,MAAM,UAAa,KAAK,KAAK;AAC7C,gBAAI,MAAM,qBAAoB,GAAA,YAAA,cAAa,MAAM,KAAK,GAAG,CAAC,IAAI;AAC9D,oBAAQ,KAAK,QAAQ,MAAM,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM,KAAK,WAAW,MAAM,GAAG,CAAC,CAAC;AACpG,gBAAI,OAAO;AACT,oBAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM;AAC7C,oBAAM,OAAO,MAAM,QAAQ;;qBAEpB,MAAM,KAAK,GAAG,MAAM,UAAa,KAAK,KAAK;AACpD,gBAAI,SAAO,MAAM,KAAK,GAAG;AACzB,gBAAI,MAAM,MAAM,QAAQ,MAAM,KAAK,GAAG,EAAE;AACxC,gBAAI,OAAK,WAAW,GAAG;AACrB,uBAAQ,GAAA,cAAA,WAAgB,IAAI,OAAK,OAAO,CAAC,GAAG,IAAI,GAAG,MAAM,OAAO,GAAG;mBAC9D;AACL,uBAAQ,GAAA,cAAA,WAAgB,IAAI,QAAM,IAAI,GAAG,MAAM,OAAO,KAAK,KAAK;;iBAE7D;AACL,oBAAQ,KAAK,QAAQ,MAAM,GAAG,OAAO,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC;;AAEzD,cAAI,OAAO;AACT,iBAAK,KAAK,KAAK;AACf,iBAAK,MAAM,YAAY,MAAM,IAAI;;;MAGvC;AAOO,MAAAA,SAAA,UAAA,WAAP,SAAgB,SAAiB;AAC/B,YAAI,OAA0B,CAAA;AAC9B,YAAI,KAAK,aAAa;AACpB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,iBAAK,iBAAiB,MAAM,GAAG,QAAQ,CAAC,CAAC;;;AAG7C,eAAO;MACT;AA3Lc,MAAAA,SAAA,UAAsB;QAClC,YAAY;UAEV,CAAC,OAAO,KAAK;;QAGf,aAAa;UACX,CAAC,MAAM,IAAI;UACX,CAAC,OAAO,KAAK;;QAGf,gBAAgB;QAEhB,qBAAqB;QAErB,aAAa;;AA8KjB,aAAAA;MAlMsC,cAAA,gBAAgB;AAAzC,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;ACtBb,QAAA,eAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AAMA,QAAU;AAAV,KAAA,SAAUC,aAAU;AASP,MAAAA,YAAA,gBAAgB,SAAS,KAAoC;;AACtE,YAAI,UAAU,IAAI;;AAClB,mBAAe,KAAA,SAAA,QAAQ,QAAQ,aAAa,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA1C,gBAAI,KAAE,GAAA;AACT,gBAAI,cAAA,QAAS,YAAY,IAAI,aAAa,GAAG;AAC3C,kBAAI,SAAS,cAAA,QAAS,QAAQ,EAAE;AAChC,kBAAI,UAAU,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,UAAU,GAAG;AAChD,8BAAA,QAAS,aAAa,IAAI,YAAY,KAAK;;AAE7C,kBAAM,WAAS,GAAG;AAClB,kBAAI,CAAC,cAAA,QAAS,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI;AACxD,oBAAM,UAAU,QAAQ,YAAY,OAAO,QAAQ,WAAW,CAAC,EAAE,CAAC;AAClE,yBAAO,aAAa,SAAS,EAAE;AAC/B,wBAAQ,sBAAsB,EAAE;;AAElC,4BAAA,QAAS,iBAAiB,IAAI,aAAa;;;;;;;;;;;;MAGjD;AASW,MAAAA,YAAA,kBAAkB,SAAS,KAAyB;AAC7D,YAAI,OAAO,IAAI,KAAK;AACpB,aAAK,SAAS,SAAC,KAAc,IAAO;;AAClC,cAAI,UAAU,IAAI;AAClB,cAAI,CAAC,SAAS;AACZ;;AAEF,cAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,gBAAgB,KAAK,IAAI,MAAM,GAAG,CAAC;AACrE,iBAAQ,QAAQ,iBAAgB,EAAI,gBAAgB;;AACpD,qBAAkB,KAAA,SAAA,QAAQ,iBAAgB,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,kBAAM,MAAG,GAAA;AACZ,kBAAI,CAAC,KAAK,IAAI,GAAG,KAAK,QAAQ,WAAW,GAAG,MAAM,IAAI,WAAW,aAAa,GAAG,GAAG;AAClF,uBAAO,QAAQ,WAAW,GAAG;;;;;;;;;;;;QAGnC,GAAG,CAAA,CAAE;MACP;AAQW,MAAAA,YAAA,mBAAmB,SAAS,KAAyB;;AAC9D,YAAM,SAAoB,CAAA;;AAC1B,mBAAe,KAAA,SAAA,IAAI,KAAK,QAAQ,IAAI,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAlC,gBAAI,KAAE,GAAA;AACT,gBAAI,GAAG,YAAY,mBAAmB,KAAK,CAAC,GAAG,UAC1C,GAAG,UAAU,CAAC,cAAA,QAAS,OAAO,GAAG,QAAQ,MAAM,KAChD,cAAA,QAAS,YAAY,EAAE,MAAM,aAAA,SAAS,KAAK;AAE7C;;AAEF,gBAAI,MAAM,GAAG;AACb,gBAAI,KAAE;AACN,gBAAI,WAAW,IAAI;AACnB,gBAAI,OAAO,SAAS,QAAQ,EAAE,IAAI;AAClC,gBAAI,cAAc,cAAA,QAAS,YAAY,IAAI,aAAa;AACxD,mBAAO,OAAO,SAAS,WAAW,KAAK,SAAS,IAAI,MAC7C,cAAA,QAAS,OAAO,IAAI,IAAI,KACxB,cAAA,QAAS,YAAY,EAAE,MAAM,aAAA,SAAS,KAAK;AAChD,kBAAI,gBAAgB,cAAA,QAAS,YAAY,IAAI,aAAa,KACtD,iBAAiB,IAAI,EAAE,GAAG;AAG5B,8BAAA,QAAS,eAAe,IAAI,cAAA,QAAS,YAAY,EAAE,CAAC;AAGpD,8BAAc,CAAC,YAAY,QAAQ,GAAG,IAAI,EAAE;;AAC5C,2BAAmB,MAAA,MAAA,QAAA,SAAA,GAAG,iBAAgB,CAAE,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAArC,wBAAM,SAAI,GAAA;AACb,uBAAG,YAAY,QAAM,GAAG,YAAY,MAAI,CAAC;;;;;;;;;;;AAE3C,yBAAS,OAAO,MAAM,CAAC;AACvB,uBAAO,KAAK,EAAE;AACd,mBAAG,SAAS;AACZ,mBAAG,YAAY,qBAAqB,IAAI;qBACnC;AAEL,oBAAI,GAAG,WAAW,YAAY,QAAQ,KAAK,MAAM;AAE/C,gCAAA,QAAS,aAAa,IAAI,UAAU,KAAK;;AAE3C,oBAAI,GAAG,WAAW,YAAY,QAAQ,KAAK,MAAM;AAE/C,gCAAA,QAAS,aAAa,IAAI,UAAU,KAAK;;AAE3C;;;AAGJ,eAAG,WAAW,aAAa,QAAS,GAAa,SAAQ,EAAG,CAAC,CAAC;;;;;;;;;;;AAEhE,YAAI,KAAK,eAAe,MAAM,MAAM;MACtC;AASA,UAAI,gBAAgB,SAAS,OACA,OAAgB,OAAc;AACzD,YAAI,QAAQ,MAAM;AAClB,YAAI,QAAQ,MAAM;AAClB,cAAM,QAAQ,SAAA,GAAC;AACb,cAAI,OAAO,MAAM,YAAY,CAAC;AAC9B,cAAI,QAAQ,MAAM;AAEhB,kBAAM,IAAI,GAAG,IAAI;;QAErB,CAAC;MACH;AAYA,UAAI,mBAAmB,SAAS,OAAgB,OAAc;;AAC5D,YAAI,SAAS,SAAC,MAAkB,OAAa;AAC3C,cAAI,MAAM,KAAK,iBAAgB;AAC/B,iBAAO,IAAI,OAAO,SAAA,GAAC;AACjB,mBAAO,MAAM,UACV,MAAM,cACN,KAAK,YAAY,UAAU;UAChC,CAAC;QACH;AACA,YAAI,QAAQ,MAAM;AAClB,YAAI,QAAQ,MAAM;AAClB,YAAI,OAAO,OAAO,OAAO,QAAQ;AACjC,YAAI,OAAO,OAAO,OAAO,QAAQ;AACjC,YAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,iBAAO;;;AAET,mBAAiB,SAAA,SAAA,IAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAAlB,gBAAI,SAAI,SAAA;AACX,gBAAI,MAAM,YAAY,MAAI,MAAM,MAAM,YAAY,MAAI,GAAG;AACvD,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAQA,UAAI,eAAe,SAAS,SAAuB,KAAa,IAAU;;AACxE,YAAM,SAAoB,CAAA;;AAC1B,mBAAgB,KAAA,SAAA,QAAQ,QAAQ,MAAM,MAAM,EAAE,CAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAArD,gBAAI,MAAG,GAAA;AACV,gBAAM,WAAW,IAAI;AACrB,gBAAI,SAAS,IAAI,GAAG,CAAC,KAAK,SAAS,IAAI,EAAE,CAAC,GAAG;AAC3C;;AAEF,gBAAM,WAAS,IAAI;AACnB,gBAAI,UAAW,SAAS,IAAI,GAAG,CAAC,IACrB,QAAQ,YAAY,OAAO,QAAQ,MAAM,KAAK,CAAC,SAAS,IAAI,IAAI,GAAG,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,IACtF,QAAQ,YAAY,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/F,0BAAA,QAAS,eAAe,KAAK,OAAO;AACpC,gBAAI,UAAQ;AACV,uBAAO,aAAa,SAAS,GAAG;mBAC3B;AACL,sBAAQ,OAAO;;AAEjB,mBAAO,KAAK,GAAG;;;;;;;;;;;AAEjB,gBAAQ,eAAe,MAAM,MAAM,IAAI,MAAM;MAC/C;AASW,MAAAA,YAAA,cAAc,SAAS,KAAoC;AACpE,YAAI,UAAU,IAAI;AAClB,YAAI,QAAQ,OAAO;AACjB;;AAEF,qBAAa,SAAS,OAAO,KAAK;AAClC,qBAAa,SAAS,SAAS,MAAM;MACvC;AAWA,UAAI,cAAc,SAAU,SAAuB,WAAmB,QAAc;;AAClF,YAAM,SAAoB,CAAA;;AAC1B,mBAAkB,KAAA,SAAA,QAAQ,QAAQ,SAAS,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAM,MAAG,GAAA;AACZ,gBAAI,IAAI,WAAW,IAAI,cAAc,GAAG;AACtC;;AAEF,gBAAM,OAAO,IAAI,WAAY,IAAY,IAAI;AAC7C,gBAAM,KAAK,KAAK,OAAM;AACtB,gBAAI,KAAK,YAAY,eAAe,KAAK,CAAC,GAAG,WAAW,YAAY,eAAe,GAAG;AACpF,kBAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,QAAQ,IAAI,UAAU;AACpE,4BAAA,QAAS,eAAe,KAAK,IAAI;AACjC,kBAAI,IAAI,QAAQ;AACd,oBAAI,OAAO,aAAa,MAAM,GAAG;qBAC5B;AACL,wBAAQ,OAAO;;AAEjB,qBAAO,KAAK,GAAG;;;;;;;;;;;;AAGnB,gBAAQ,eAAe,WAAW,MAAM;MAC1C;AAQW,MAAAA,YAAA,aAAa,SAAU,KAAyB;AACzD,YAAM,UAAU,IAAI;AACpB,oBAAY,SAAS,cAAc,SAAS;AAC5C,oBAAY,SAAS,UAAU,MAAM;AACrC,oBAAY,SAAS,SAAS,MAAM;MACtC;AAQW,MAAAA,YAAA,eAAe,SAAS,KAAoC;AACrE,YAAI,KAAK,KAAK,uBAAuB,CAAA,GAAI,IAAI,KAAK,SAAS,GAAG,GAAG,KAAK;MACxE;IAEF,GApQU,eAAA,aAAU,CAAA,EAAA;AAuQpB,YAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Qf,QAAA,gBAAA;AACA,QAAA,eAAA;AAMA,QAAA,eAAA;AAEA,QAAA,kBAAA,gBAAA,oBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,oBAAA,gBAAA,sBAAA;AACA,QAAA,YAAA;AACA,QAAA,qBAAA;AAEA;AAaA,QAAA,MAAA,SAAA,QAAA;AAAkC,gBAAAC,MAAA,MAAA;AA6EhC,eAAAA,KAAY,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AAApC,YAAA,QAAA;AACQ,YAAA,KAAA,QAAoB,GAAA,aAAA,iBAAgB,SAASA,KAAI,SAAS,aAAA,QAAQ,OAAO,GAAC,CAAA,GAAzE,OAAI,GAAA,CAAA,GAAE,MAAG,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;gBACtB,OAAA,KAAA,MAAM,GAAG,KAAC;AACV,cAAK,UAAU,MAAK,QAAQ,SAAS,KAAK,IAAI,aAAA,QAAQ,IAAI;AAC1D,YAAM,WAAW,MAAK,QAAQ;AAC9B,YAAM,gBAAgB,MAAK,gBAAgBA,KAAI,UAAU,QAAQ;AACjE,YAAM,eAAe,MAAK,gBACxB,IAAI,kBAAA,QAAa,eAAe,CAAC,MAAK,SAAS,UAAA,YAAY,OAAO,CAAC;AACrE,SAAA,GAAA,aAAA,aAAY,aAAa,SAAS,IAAI;AACtC,sBAAc,OAAO,KAAI;AACzB,QAAAA,KAAI,KAAK,cAAc,aAAa;AACpC,cAAK,YAAY,IAAI,gBAAA,QAAW,aAAa,EAAE;AAC/C,cAAK,YAAY,IAAI,gBAAA,QAAW,cAAc,EAAE;AAChD,cAAK,YAAY,IAAI,gBAAA,QAAW,YAAY,EAAE;AAC9C,cAAK,YAAY,IAAI,gBAAA,QAAW,eAAe,EAAE;AACjD,cAAK,YAAY,IAAI,gBAAA,QAAW,iBAAiB,EAAE;AACnD,cAAK,YAAY,IAAI,gBAAA,QAAW,kBAAkB,EAAE;;MACtD;AAzCiB,MAAAA,KAAA,YAAjB,SAA2B,UAAuC;AAChE,YAAI,gBAAgB,IAAI,mBAAA,oBAAoB,UAAU,CAAC,KAAK,CAAC;AAC7D,sBAAc,KAAI;AAClB,eAAO;MACT;AASiB,MAAAA,KAAA,OAAjB,SAAsB,SAAuB,eAAkC;AAC7E,kBAAA,YAAY,QAAQ,cAAc,IAAI;AACtC,kBAAA,YAAY,WAAW,QAAQ,QAAQ,IAAI;AAC3C,gBAAQ,OAAO,UAAA,YAAY,WAAU;AACrC,gBAAQ,KAAK,gBAAgB;MAC/B;AA4BO,MAAAA,KAAA,UAAA,gBAAP,SAAqB,YAAsB;AACzC,eAAA,UAAM,cAAa,KAAA,MAAC,UAAU;AAC9B,aAAK,cAAc,YAAY,cAAc,UAAU;MACzD;AAMA,aAAA,eAAWA,KAAA,WAAA,gBAAY;aAAvB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAKO,MAAAA,KAAA,UAAA,QAAP,SAAa,KAAe;AAAf,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAe;AAC1B,aAAK,aAAa,KAAK,MAAM,GAAG;MAClC;AAMO,MAAAA,KAAA,UAAA,UAAP,SAAe,MAAyB,UAA+B;AACrE,aAAK,aAAa,MAAK;AACvB,aAAK,eAAe,KAAK,YAAY,MAAM,UAAU,KAAK,YAAY;AACtE,YAAI,UAAU,KAAK;AACnB,aAAK,QAAQ,KAAK;AAClB,YAAI;AACJ,aAAK,aAAa,KAAK,cAAc,IAAI;AACzC,YAAI;AACJ,YAAI;AACF,cAAI,SAAS,IAAI,eAAA,QAAU,KAAK,OACL,EAAC,SAAkB,SAAS,MAAK,GACjC,KAAK,YAAY;AAC5C,iBAAO,OAAO,IAAG;AACjB,sBAAY,OAAO,MAAM;iBAClB,KAAK;AACZ,cAAI,EAAE,eAAe,cAAA,UAAW;AAC9B,kBAAM;;AAER,eAAK,aAAa,QAAQ;AAC1B,iBAAO,KAAK,QAAQ,YAAY,MAAM,GAAG;;AAE3C,eAAO,KAAK,aAAa,YAAY,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC;AAClE,YAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,aAAa;AAC1B,wBAAA,QAAS,aAAa,MAAM,eAAe,UAAU,WAAW;;AAElE,YAAI,SAAS;AACX,wBAAA,QAAS,aAAa,MAAM,WAAW,OAAO;;AAEhD,aAAK,aAAa,KAAK,eAAe,IAAI;AAC1C,aAAK,aAAa,OAAO;AACzB,aAAK,eAAe,KAAK,aAAa,MAAM,UAAU,KAAK,YAAY;AACvE,aAAK,WAAW,KAAK,aAAa;AAClC,eAAO,KAAK;MACd;AAMO,MAAAA,KAAA,UAAA,WAAP,SAAgB,SAAiB;AAC/B,eAAO,KAAK,QAAQ,SAAS,OAAO;MACtC;AAQO,MAAAA,KAAA,UAAA,cAAP,SAAmB,KAAa;AAC9B,YAAI,UAAU,IAAI,QAAQ,QAAQ,QAAQ,EAAE;AAC5C,eAAO,KAAK,aAAa,YAAY,OACnC,SAAS,SAAS,IAAI,IAAI,KAAK,KAAK;MACxC;AA1Kc,MAAAA,KAAA,OAAe;AAMf,MAAAA,KAAA,UAAO,SAAA,SAAA,CAAA,GAChB,cAAA,iBAAiB,OAAO,GAAA,EAC3B,SAAS,MACT,UAAU,CAAC,MAAM,GAEjB,QAAQ,uDAER,WAAW,IAAI,MACf,aAAa,SAAC,KAAyB,KAAa;AAAK,eAAA,IAAI,YAAY,GAAG;MAAnB,EAAoB,CAAA;AA8JjF,aAAAA;MAlLkC,cAAA,gBAAgB;AAArC,YAAA,MAAA;;;", "names": ["AbstractFindMath", "FindTeX", "<PERSON><PERSON><PERSON><PERSON>", "TeX"]}