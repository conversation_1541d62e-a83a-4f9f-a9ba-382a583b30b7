pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Harmonic16 Dark
  Author: <PERSON><PERSON> (https://github.com/janniks)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme harmonic16-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #0b1c2c  Default Background
base01  #223b54  Lighter Background (Used for status bars, line number and folding marks)
base02  #405c79  Selection Background
base03  #627e99  Comments, Invisibles, Line Highlighting
base04  #aabcce  Dark Foreground (Used for status bars)
base05  #cbd6e2  Default Foreground, Caret, Delimiters, Operators
base06  #e5ebf1  Light Foreground (Not often used)
base07  #f7f9fb  Light Background (Not often used)
base08  #bf8b56  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #bfbf56  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #8bbf56  Classes, Markup Bold, Search Text Background
base0B  #56bf8b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #568bbf  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #8b56bf  Functions, Methods, Attribute IDs, Headings
base0E  #bf568b  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #bf5656  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #cbd6e2;
  background: #0b1c2c
}
.hljs::selection,
.hljs ::selection {
  background-color: #405c79;
  color: #cbd6e2
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #627e99 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #627e99
}
/* base04 - #aabcce -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #aabcce
}
/* base05 - #cbd6e2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #cbd6e2
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #bf8b56
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #bfbf56
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #8bbf56
}
.hljs-strong {
  font-weight: bold;
  color: #8bbf56
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #56bf8b
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #568bbf
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #8b56bf
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #bf568b
}
.hljs-emphasis {
  color: #bf568b;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #bf5656
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}