import { CHTMLWrapper, Constructor } from '../Wrapper.js';
import { CHTMLmsubsup } from './msubsup.js';
import { BBox } from '../../../util/BBox.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmmultiscripts_base: import("../../common/Wrappers/mmultiscripts.js").MmultiscriptsConstructor<CHTMLWrapper<any, any, any>> & Constructor<CHTMLmsubsup<any, any, any>>;
export declare class CHTMLmmultiscripts<N, T, D> extends CHTMLmmultiscripts_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
    protected addScripts(u: number, v: number, isPre: boolean, sub: BBox, sup: BBox, i: number, n: number): N;
}
export {};
