pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atlas
  Author: <PERSON> (https://ajlende.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atlas
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #002635  Default Background
base01  #00384d  Lighter Background (Used for status bars, line number and folding marks)
base02  #517F8D  Selection Background
base03  #6C8B91  Comments, Invisibles, Line Highlighting
base04  #869696  Dark Foreground (Used for status bars)
base05  #a1a19a  Default Foreground, Caret, Delimiters, Operators
base06  #e6e6dc  Light Foreground (Not often used)
base07  #fafaf8  Light Background (Not often used)
base08  #ff5a67  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f08e48  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffcc1b  Classes, Markup Bold, Search Text Background
base0B  #7fc06e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #14747e  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #5dd7b9  Functions, Methods, Attribute IDs, Headings
base0E  #9a70a4  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #c43060  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a1a19a;
  background: #002635
}
.hljs::selection,
.hljs ::selection {
  background-color: #517F8D;
  color: #a1a19a
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6C8B91 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6C8B91
}
/* base04 - #869696 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #869696
}
/* base05 - #a1a19a -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a1a19a
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff5a67
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f08e48
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffcc1b
}
.hljs-strong {
  font-weight: bold;
  color: #ffcc1b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7fc06e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #14747e
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #5dd7b9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9a70a4
}
.hljs-emphasis {
  color: #9a70a4;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #c43060
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}