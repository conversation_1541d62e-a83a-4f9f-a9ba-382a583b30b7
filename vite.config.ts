import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    proxy: {
      // 音乐服务API代理 - 优先级最高，放在最前面
      '/api/music': {
        target: 'http://192.168.1.115:8000',
        changeOrigin: true,
        secure: false
      },
      // 旅行服务API代理
      '/api/travel': {
        target: 'http://192.168.1.200:8000',
        changeOrigin: true,
        secure: false
      },
      // 将其他/api开头的请求转发到默认服务器
      '/api': {
        target: 'http://192.168.1.169:5678',
        changeOrigin: true,
        secure: false
      },
      // 将所有/upload开头的请求转发到目标服务器
      '/upload': {
        target: 'http://192.168.1.169:5678',
        changeOrigin: true,
        secure: false
      }
    }
  }
})