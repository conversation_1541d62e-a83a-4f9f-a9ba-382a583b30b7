{"version": 3, "file": "liteAdaptor.js", "sourceRoot": "", "sources": ["../../ts/adaptors/liteAdaptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,uDAAyD;AACzD,+CAAsD;AACtD,kDAAgD;AAChD,gDAAwD;AACxD,0CAAqD;AAErD,8CAA4C;AAC5C,8CAA4C;AAC5C,+CAAyC;AASzC;IAA8B,4BAAuD;IAoBnF;QAAA,YACE,iBAAO,SAGR;QAFC,KAAI,CAAC,MAAM,GAAG,IAAI,sBAAU,EAAE,CAAC;QAC/B,KAAI,CAAC,MAAM,GAAG,IAAI,sBAAU,EAAE,CAAC;;IACjC,CAAC;IAKM,wBAAK,GAAZ,UAAa,IAAY,EAAE,MAAe;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAKS,yBAAM,GAAhB,UAAiB,IAAY,EAAE,GAAkB;QAAlB,oBAAA,EAAA,UAAkB;QAC/C,OAAO,IAAI,wBAAW,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,uBAAI,GAAX,UAAY,IAAY;QACtB,OAAO,IAAI,kBAAQ,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAMM,0BAAO,GAAd,UAAe,IAAY;QACzB,OAAO,IAAI,qBAAW,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,iCAAc,GAArB;QACE,OAAO,IAAI,0BAAY,EAAE,CAAC;IAC5B,CAAC;IAKM,uBAAI,GAAX,UAAY,GAAiB;QAC3B,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAKM,uBAAI,GAAX,UAAY,GAAiB;QAC3B,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAKM,uBAAI,GAAX,UAAY,GAAiB;QAC3B,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAKM,0BAAO,GAAd,UAAe,GAAiB;QAC9B,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAKM,uBAAI,GAAX,UAAY,IAAiB,EAAE,IAAY,EAAE,EAAiB;QAAjB,mBAAA,EAAA,SAAiB;QAC5D,IAAI,KAAK,GAAG,EAAgB,CAAC;QAC7B,IAAI,IAAI,GAAG,EAAmB,CAAC;QAC/B,IAAI,EAAE,EAAE;YACN,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,GAAa,IAAI,CAAC;QACvB,OAAO,CAAC,EAAE;YACR,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YAClB,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,UAAU,EAAE;gBAC3C,CAAC,GAAG,CAAgB,CAAC;gBACrB,IAAI,IAAI,KAAK,IAAI,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACd;gBACD,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACrB,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBAClC;aACF;YACD,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,8BAAW,GAAlB,UAAmB,IAAiB,EAAE,EAAU;QAC9C,IAAI,KAAK,GAAG,EAAgB,CAAC;QAC7B,IAAI,CAAC,GAAG,IAAgB,CAAC;QACzB,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;gBAC/C,CAAC,GAAG,CAAgB,CAAC;gBACrB,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;oBAC7B,OAAO,CAAC,CAAC;iBACV;gBACD,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACrB,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBAClC;aACF;YACD,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,kCAAe,GAAtB,UAAuB,IAAiB,EAAE,IAAY;QACpD,IAAI,KAAK,GAAG,EAAgB,CAAC;QAC7B,IAAI,IAAI,GAAG,EAAmB,CAAC;QAC/B,IAAI,CAAC,GAAa,IAAI,CAAC;QACvB,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;gBAC/C,CAAC,GAAG,CAAgB,CAAC;gBACrB,IAAM,OAAO,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjE,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACd;gBACD,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACrB,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBAClC;aACF;YACD,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,8BAAW,GAAlB,UAAmB,KAA+C,EAAE,QAAsB;;QACxF,IAAI,UAAU,GAAG,EAAmB,CAAC;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;YACjC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,IAAI,kBAAA;gBACb,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBAC1B,IAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChD,IAAI,CAAC,EAAE;4BACL,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACpB;qBACF;yBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBACjC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3E;yBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;wBAC5C,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;qBACvD;iBACF;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACtC;qBAAM,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;oBAC7F,UAAU,GAAG,UAAU,CAAC,MAAM,CAAE,IAA8B,CAAC,KAAK,CAAC,CAAC;iBACvE;qBAAM;oBACL,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACvB;aACF;;;;;;;;;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAKM,2BAAQ,GAAf,UAAgB,SAAmB,EAAE,IAAyB;QAC5D,OAAO,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;YACjC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC1B;QACD,OAAO,CAAC,CAAC,IAAI,CAAC;IAChB,CAAC;IAKM,yBAAM,GAAb,UAAc,IAAc;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAMM,6BAAU,GAAjB,UAAkB,IAAc;QAC9B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,EAAV,CAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAKM,yBAAM,GAAb,UAAc,IAAiB,EAAE,KAAe;QAC9C,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACpB;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,yBAAM,GAAb,UAAc,MAAgB,EAAE,MAAgB;QAC9C,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACrB;QACD,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC3B,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAC/B;IACH,CAAC;IAKM,yBAAM,GAAb,UAAc,KAAe;QAC3B,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,EAAE;YACV,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACpC;QACD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,0BAAO,GAAd,UAAe,KAAe,EAAE,KAAe;QAC7C,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,EAAE;YACV,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACjC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC5B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;SACrB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,wBAAK,GAAZ,UAAa,IAAiB;QAA9B,iBAeC;QAdC,IAAM,KAAK,GAAG,IAAI,wBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,KAAK,CAAC,UAAU,gBAAO,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC;YAClC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;gBACtB,OAAO,IAAI,kBAAQ,CAAE,CAAc,CAAC,KAAK,CAAC,CAAC;aAC5C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;gBAChC,OAAO,IAAI,qBAAW,CAAE,CAAiB,CAAC,KAAK,CAAC,CAAC;aAClD;iBAAM;gBACL,IAAM,CAAC,GAAG,KAAI,CAAC,KAAK,CAAC,CAAgB,CAAC,CAAC;gBACvC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;gBACjB,OAAO,CAAC,CAAC;aACV;QACH,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,wBAAK,GAAZ,UAAa,IAAc,EAAE,CAAS;QACpC,IAAM,IAAI,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,uBAAI,GAAX,UAAY,IAAc;QACxB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC5E,CAAC;IAKM,2BAAQ,GAAf,UAAgB,IAAc;QAC5B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAKM,6BAAU,GAAjB,UAAkB,IAAiB;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAKM,4BAAS,GAAhB,UAAiB,IAAiB;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,CAAC;IAKM,6BAAU,GAAjB,UAAkB,IAAiB;QACjC,gCAAW,IAAI,CAAC,QAAQ,UAAE;IAC5B,CAAC;IAKM,4BAAS,GAAhB,UAAiB,IAAiB,EAAE,CAAS;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAKM,uBAAI,GAAX,UAAY,IAAc;QACxB,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAKM,wBAAK,GAAZ,UAAa,IAAyB;QACpC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAE,IAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAE,IAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChH,CAAC;IAKM,8BAAW,GAAlB,UAAmB,IAAiB;QAApC,iBAKC;QAJC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAC,CAAS,EAAE,CAAW;YACjD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAE,CAAc,CAAC,KAAK,CAAC,CAAC;gBAC5C,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,WAAW,CAAC,CAAgB,CAAC,CAAC,CAAC;QAC/E,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAKM,4BAAS,GAAhB,UAAiB,IAAiB;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAKM,4BAAS,GAAhB,UAAiB,IAAiB;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAKM,+BAAY,GAAnB,UAAoB,IAAiB;QACnC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAKM,+BAAY,GAAnB,UAAoB,IAAiB,EAAE,IAAY,EAAE,KAAsB,EAAE,EAAiB;QAAjB,mBAAA,EAAA,SAAiB;QAC5F,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SACvB;QACD,IAAI,EAAE,EAAE;YACN,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SAChE;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9B,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;IACH,CAAC;IAKM,+BAAY,GAAnB,UAAoB,IAAiB,EAAE,IAAY;QACjD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,kCAAe,GAAtB,UAAuB,IAAiB,EAAE,IAAY;QACpD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,+BAAY,GAAnB,UAAoB,IAAiB,EAAE,IAAY;QACjD,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAKM,gCAAa,GAApB,UAAqB,IAAiB;;QACpC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,IAAI,GAAG,EAAE,CAAC;;YAChB,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,MAAI,WAAA;gBACb,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,MAAI,EAAE,KAAK,EAAE,UAAU,CAAC,MAAI,CAAW,EAAC,CAAC,CAAC;aAC5D;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,2BAAQ,GAAf,UAAgB,IAAiB,EAAE,IAAY;QAC7C,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,EAAV,CAAU,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC;IAKM,8BAAW,GAAlB,UAAmB,IAAiB,EAAE,IAAY;QAChD,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtE,IAAM,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,EAAV,CAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,EAAE;YACV,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9C;IACH,CAAC;IAKM,2BAAQ,GAAf,UAAgB,IAAiB,EAAE,IAAY;QAC7C,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,EAAV,CAAU,CAAC,CAAC;IACzC,CAAC;IAKM,2BAAQ,GAAf,UAAgB,IAAiB,EAAE,IAAY,EAAE,KAAa;QAC5D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IACjD,CAAC;IAKM,2BAAQ,GAAf,UAAgB,IAAiB,EAAE,IAAY;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO,EAAE,CAAC;aACX;YACD,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAM,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,4BAAS,GAAhB,UAAiB,IAAiB;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAKM,8BAAW,GAAlB,UAAmB,IAAiB,EAAE,KAAe;QACnD,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpF,CAAC;IAUM,2BAAQ,GAAf,UAAgB,KAAkB;QAChC,OAAO,CAAC,CAAC;IACX,CAAC;IAKM,6BAAU,GAAjB,UAAkB,KAAkB;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;IAKM,2BAAQ,GAAf,UAAgB,KAAkB,EAAE,GAAe,EAAE,MAAsB;QAAvC,oBAAA,EAAA,OAAe;QAAE,uBAAA,EAAA,aAAsB;QACzE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAqB,CAAC;IACpC,CAAC;IAKM,2BAAQ,GAAf,UAAgB,KAAkB;QAChC,OAAO,EAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC;IAChD,CAAC;IAEH,eAAC;AAAD,CAAC,AAliBD,CAA8B,kCAAkB,GAkiB/C;AAliBY,4BAAQ;AAuiBrB;IAAiC,+BAA+E;IAAhH;;IAAkH,CAAC;IAAD,kBAAC;AAAD,CAAC,AAAnH,CAAiC,IAAA,wBAAS,EAA6D,QAAQ,CAAC,GAAG;AAAtG,kCAAW;AASxB,SAAgB,WAAW,CAAC,OAA0B;IAA1B,wBAAA,EAAA,cAA0B;IACpD,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAFD,kCAEC"}