{"version": 3, "file": "BussproofsMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/bussproofs/BussproofsMethods.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,+DAAsC;AACtC,iEAAwC;AACxC,iEAAwC;AAGxC,kEAAsD;AAItD,IAAI,iBAAiB,GAAgC,EAAE,CAAC;AAUxD,iBAAiB,CAAC,SAAS,GAAG,UAAS,MAAiB,EAAE,KAAgB;IACxE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnB,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;QAClD,aAAa,CAAC,EAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE;QACrB,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAC,CAAC,CAAC;IAEzE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAQF,iBAAiB,CAAC,KAAK,GAAG,UAAS,MAAiB,EAAE,IAAY;IAChE,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,IAAI,OAAO,GAAG,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACnD,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpB,CAAC,CAAC;AASF,IAAM,aAAa,GAAG,UAAS,MAAiB,EAAE,OAAe;IAE/D,IAAI,KAAK,GAAG,sBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,sBAAS,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7E,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE;QAC7C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;KAC1C;IACD,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;IAChE,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;IAChE,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,+BAAG,IAAI,UAAK,KAAK,YAAE,IAAI,UAAE,CAAC;AAC/D,CAAC,CAAC;AASF,iBAAiB,CAAC,SAAS,GAAG,UAAS,MAAiB,EAAE,IAAY,EAAE,CAAS;IAC/E,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;QAClB,MAAM,IAAI,qBAAQ,CAAC,cAAc,EAAE,6BAA6B,CAAC,CAAC;KACnE;IACD,IAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,CAAY,CAAC;IAC1D,IAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,QAAQ,GAAc,EAAE,CAAC;IAC7B,GAAG;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACxD;QACD,QAAQ,CAAC,OAAO,CACd,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAC1B,EAAC,UAAU,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,EAAE,CAAC;KACL,QAAQ,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;IACrD,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,EAAC,YAAY,EAAE,KAAK,EAAC,CAAC,CAAC;IAC1E,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,IAAI,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,aAAa,CAAW,CAAC;IACrD,IAAI,KAAK,KAAK,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;QACrC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;KACzD;IACD,IAAI,IAAI,GAAG,UAAU,CACnB,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAY,EAC/D,GAAG,CAAC,WAAW,CAAC,OAAO,CAAY,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACzD,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9B,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC/B,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAC1D,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAChD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAC;AAaF,SAAS,UAAU,CAAC,MAAiB,EAAE,OAAgB,EACnC,WAAsB,EAAE,IAAoB,EAC5C,KAAqB,EAAE,KAAa,EACpC,SAAkB;IACpC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,MAAM,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACpE,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,MAAM,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACtE,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAC7D,EAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAC,CAAC,CAAC;IACjF,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC7E,IAAI,SAAS,EAAE,UAAU,CAAC;IAC1B,IAAI,IAAI,EAAE;QACR,SAAS,GAAG,MAAM,CAAC,MAAM,CACvB,MAAM,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EACzB,EAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC,CAAC;QACxD,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;KAC7D;IACD,IAAI,KAAK,EAAE;QACT,UAAU,GAAG,MAAM,CAAC,MAAM,CACxB,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAC1B,EAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC,CAAC;QACxD,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;KAC/D;IACD,IAAI,QAAQ,EAAE,KAAK,CAAC;IACpB,IAAI,IAAI,IAAI,KAAK,EAAE;QACjB,QAAQ,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACzC,KAAK,GAAG,MAAM,CAAC;KAChB;SAAM,IAAI,IAAI,EAAE;QACf,QAAQ,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,KAAK,GAAG,MAAM,CAAC;KAChB;SAAM,IAAI,KAAK,EAAE;QAChB,QAAQ,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC9B,KAAK,GAAG,OAAO,CAAC;KACjB;SAAM;QACL,OAAO,IAAI,CAAC;KACb;IACD,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC/C,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AACd,CAAC;AASD,iBAAiB,CAAC,KAAK,GAAG,UAAS,MAAiB,EAAE,IAAY,EAAE,IAAY;IAC9E,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,IAAI,OAAO,GAAG,sBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1E,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1D,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/B,CAAC,CAAC;AAUF,iBAAiB,CAAC,OAAO,GAAG,UAAS,MAAiB,EAAE,KAAa,EAAE,KAAa,EAAE,MAAe;IACnG,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IACtC,IAAI,MAAM,EAAE;QACV,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChC;AACH,CAAC,CAAC;AASF,iBAAiB,CAAC,SAAS,GAAG,UAAS,MAAiB,EAAE,KAAa,EAAE,KAAc;IACrF,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AACtC,CAAC,CAAC;AAQF,iBAAiB,CAAC,MAAM,GAAG,UAAS,MAAiB,EAAE,IAAY;IACjE,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,IAAI,IAAI,GAAG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC1C,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAChD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAC;AASF,SAAS,gBAAgB,CAAC,MAAiB,EAAE,IAAY;IACvD,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAC9B,IAAI,MAAM,KAAK,GAAG,EAAE;QAClB,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,4CAA4C,EAAE,IAAI,CAAC,CAAC;KACxE;IACD,MAAM,CAAC,CAAC,EAAE,CAAC;IACX,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACtC,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QACrC,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,0BAA0B,EAAE,IAAI,CAAC,CAAC;KACtD;IAEG,IAAA,KAAA,OAAe,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,IAAA,EAAtC,IAAI,QAAA,EAAE,IAAI,QAA4B,CAAC;IAC5C,IAAI,OAAO,GAAG,CAAC,IAAI,sBAAS,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAClF,IAAI,UAAU,GAAG,CAAC,IAAI,sBAAS,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IACrF,IAAI,OAAO,GAAG,CAAC,IAAI,sBAAS,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IACzF,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IACzD,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3D,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7D,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IACpE,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAC,CAAC,CAAC;IACvG,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACnD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC7C,OAAO,KAAK,CAAC;AACf,CAAC;AAQD,iBAAiB,CAAC,OAAO,GAAG,UAAS,OAAkB,EAAE,KAAa,IAAI,CAAC,CAAC;AAS5E,iBAAiB,CAAC,UAAU,GAAG,UAAS,MAAiB,EAAE,IAAY,EAAE,CAAS;IAChF,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;QAC5B,MAAM,IAAI,qBAAQ,CAAC,qBAAqB,EACrB,uDAAuD,CAAC,CAAC;KAC7E;IACD,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;QAClB,MAAM,IAAI,qBAAQ,CAAC,cAAc,EAAE,6BAA6B,CAAC,CAAC;KACnE;IACD,IAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,CAAY,CAAC;IAC1D,IAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,QAAQ,GAAc,EAAE,CAAC;IAC7B,GAAG;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACxD;QACD,QAAQ,CAAC,OAAO,CACd,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAC1B,EAAC,UAAU,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,EAAE,CAAC;KACL,QAAQ,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;IACrD,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,EAAC,YAAY,EAAE,KAAK,EAAC,CAAC,CAAC;IAE1E,IAAI,UAAU,GAAG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAChD,IAAI,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,aAAa,CAAW,CAAC;IACrD,IAAI,KAAK,KAAK,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;QACrC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;KACzD;IACD,IAAI,IAAI,GAAG,UAAU,CACnB,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAY,EAC/D,GAAG,CAAC,WAAW,CAAC,OAAO,CAAY,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACzD,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9B,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC/B,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAC1D,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAChD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAC;AAEF,kBAAe,iBAAiB,CAAC"}