{"version": 3, "sources": ["../../mathjax-full/ts/util/string.ts", "../../mathjax-full/ts/util/FunctionList.ts", "../../mathjax-full/ts/core/MmlTree/Attributes.ts", "../../mathjax-full/ts/core/Tree/Node.ts", "../../mathjax-full/ts/core/MmlTree/MmlNode.ts", "../../mathjax-full/ts/core/MmlTree/OperatorDictionary.ts", "../../mathjax-full/ts/core/MmlTree/MmlNodes/mo.ts", "../../mathjax-full/ts/core/Tree/Factory.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements some string utility functions\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n\n/**\n * Sort strings by length\n *\n * @param {string} a  First string to be compared\n * @param {string} b  Second string to be compared\n * @return {number}  -1 id a < b, 0 of a === b, 1 if a > b\n */\nexport function sortLength(a: string, b: string): number {\n  return a.length !== b.length ? b.length - a.length : a === b ? 0 : a < b ? -1 : 1;\n}\n\n/**\n * Quote a string for use in regular expressions\n *\n * @param {string} text  The text whose regex characters are to be quoted\n * @return {string}  The quoted string\n */\nexport function quotePattern(text: string): string {\n  return text.replace(/([\\^$(){}+*?\\-|\\[\\]\\:\\\\])/g, '\\\\$1');\n}\n\n/**\n * Convert a UTF-8 string to an array of unicode code points\n *\n * @param {string} text  The string to be turned into unicode positions\n * @return {number[]}  Array of numbers representing the string's unicode character positions\n */\nexport function unicodeChars(text: string): number[] {\n  return Array.from(text).map((c) => c.codePointAt(0));\n}\n\n/**\n * Convert an array of unicode code points to a string\n *\n * @param {number[]} data   The array of unicode code points\n * @return {string}         The string consisting of the characters at those points\n */\nexport function unicodeString(data: number[]): string {\n  return String.fromCodePoint(...data);\n}\n\n/**\n * Test if a value is a percentage\n *\n * @param {string} x   The string to test\n * @return {boolean}   True if the string ends with a percent sign\n */\nexport function isPercent(x: string): boolean {\n  return !!x.match(/%\\s*$/);\n}\n\n/**\n * Split a space-separated string of values\n *\n * @param {string} x   The string to be split\n * @return {string[]}  The list of white-space-separated \"words\" in the string\n */\nexport function split(x: string): string[] {\n  return x.trim().split(/\\s+/);\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implement FunctionList object\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PrioritizedList, PrioritizedListItem} from './PrioritizedList.js';\n\n/*****************************************************************/\n/**\n *  The FunctionListItem interface (extends PrioritizedListItem<Function>)\n */\n\nexport interface FunctionListItem extends PrioritizedListItem<Function> {}\n\n/*****************************************************************/\n/**\n *  Implements the FunctionList class (extends PrioritizedList<Function>)\n */\n\nexport class FunctionList extends PrioritizedList<Function> {\n\n  /**\n   * Executes the functions in the list (in prioritized order),\n   *   passing the given data to the functions.  If any return\n   *   false, the list is terminated.\n   *\n   * @param {any[]} data  The array of arguments to pass to the functions\n   * @return {boolean}    False if any function stopped the list by\n   *                       returning false, true otherwise\n   */\n  public execute(...data: any[]): boolean {\n    for (const item of this) {\n      let result = item.item(...data);\n      if (result === false) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Executes the functions in the list (in prioritized order) asynchronously,\n   *   passing the given data to the functions, and doing the next function\n   *   only when the previous one completes.  If the function returns a\n   *   Promise, then use that to control the flow.  Otherwise, if the\n   *   function returns false, the list is terminated.\n   * This function returns a Promise.  If any function in the list fails,\n   *   the promise fails.  If any function returns false, the promise\n   *   succeeds, but passes false as its argument.  Otherwise it succeeds\n   *   and passes true.\n   *\n   * @param {any[]} data  The array of arguments to pass to the functions\n   * @return {Promise}    The promise that is satisfied when the function\n   *                       list completes (with argument true or false\n   *                       depending on whether some function returned\n   *                       false or not).\n   */\n  public asyncExecute(...data: any[]): Promise<void> {\n    let i = -1;\n    let items = this.items;\n    return new Promise((ok: Function, fail: Function) => {\n      (function execute() {\n        while (++i < items.length) {\n          let result = items[i].item(...data);\n          if (result instanceof Promise) {\n            result.then(execute).catch(err => fail(err));\n            return;\n          }\n          if (result === false) {\n            ok(false);\n            return;\n          }\n        }\n        ok(true);\n      })();\n    });\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview Implements Attribute class for MmlNodes\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList, Property} from '../Tree/Node.js';\n\n/**\n * A constant for when a property should be inherited from the global defaults lists\n */\nexport const INHERIT = '_inherit_';\n\n/******************************************************************/\n/**\n * Implements the Attributes class for MmlNodes\n *  (These can be set explicitly, inherited from parent nodes,\n *   taken from a default list of values, or taken from global\n *   defaults.)\n */\n\nexport class Attributes {\n  /**\n   * The attributes explicitly set on a node\n   */\n  protected attributes: PropertyList;\n  /**\n   * The attributes inherited from parent nodes\n   */\n  protected inherited: PropertyList;\n  /**\n   * The default attributes for the node type\n   */\n  protected defaults: PropertyList;\n  /**\n   * Global attributes from the math node itself\n   */\n  protected global: PropertyList;\n\n  /**\n   * @param {PropertyList} defaults  The defaults for this node type\n   * @param {PropertyList} global    The global properties (from the math node)\n   *\n   * @constructor\n   */\n  constructor(defaults: PropertyList, global: PropertyList) {\n    this.global = global;\n    this.defaults = Object.create(global);\n    this.inherited = Object.create(this.defaults);\n    this.attributes = Object.create(this.inherited);\n    Object.assign(this.defaults, defaults);\n  }\n\n  /**\n   * @param {string} name     The name of the attribute to set\n   * @param {Property} value  The value to give the named attribute\n   */\n  public set(name: string, value: Property) {\n    this.attributes[name] = value;\n  }\n\n  /**\n   * @param {PropertyList} list  An object containing the properties to set\n   */\n  public setList(list: PropertyList) {\n    Object.assign(this.attributes, list);\n  }\n\n  /**\n   * @param {string} name  The name of the attribute whose value is to be returned\n   * @return {Property}    The value of the named attribute (including inheritance and defaults)\n   */\n  public get(name: string): Property {\n    let value = this.attributes[name];\n    if (value === INHERIT) {\n      value = this.global[name];\n    }\n    return value;\n  }\n\n  /**\n   * @param {string} name  The value of the attribute whose value is to be returned\n   * @return {Property}    The attribute whose name was given if it is explicit on the\n   *                       node (not inherited or defaulted), null otherwise\n   */\n  public getExplicit(name: string): Property {\n    if (!this.attributes.hasOwnProperty(name)) {\n      return undefined;\n    }\n    return this.attributes[name];\n  }\n\n  /**\n   * @param {string[]} names  The names of attributes whose values are to be returned\n   * @return {PropertyList}   An object containing the attributes and their values\n   */\n  public getList(...names: string[]): PropertyList {\n    let values: PropertyList = {};\n    for (const name of names) {\n      values[name] = this.get(name);\n    }\n    return values;\n  }\n\n  /**\n   * @param {string} name  The name of an inherited attribute to be set\n   * @param {Property} value  The value to assign to the named attribute\n   */\n  public setInherited(name: string, value: Property) {\n    this.inherited[name] = value;\n  }\n\n  /**\n   * @param {string} name  The name of an inherited attribute whose value is to be returned\n   * @return {Property}    The value of the named attribute if it is inherited, null otherwise\n   */\n  public getInherited(name: string): Property {\n    return this.inherited[name];\n  }\n\n  /**\n   * @param {string} name  The name of a default attribute whose value is to be returned\n   * @return {Property}    The value of the named attribute if a default exists for it, null otherwise\n   */\n  public getDefault(name: string): Property {\n    return this.defaults[name];\n  }\n\n  /**\n   * @param {string} name  The name of a attribute to check\n   * @return {boolean}     True if attribute is set explicitly or inherited\n   *                         from an explicit mstyle or math attribute\n   */\n  public isSet(name: string): boolean {\n    return this.attributes.hasOwnProperty(name) || this.inherited.hasOwnProperty(name);\n  }\n\n  /**\n   * @param {string} name  The name of an attribute to test for the existence of a default\n   * @return {boolean}     True of there is a default for the named attribute, false otherwise\n   */\n  public hasDefault(name: string): boolean {\n    return (name in this.defaults);\n  }\n\n  /**\n   * @return {string[]}  The names of all the attributes explicitly set on the node\n   */\n  public getExplicitNames(): string[] {\n    return Object.keys(this.attributes);\n  }\n\n  /**\n   * @return {string[]}  The names of all the inherited attributes for the node\n   */\n  public getInheritedNames(): string[] {\n    return Object.keys(this.inherited);\n  }\n\n  /**\n   * @return {string[]}  The names of all the default attributes for the node\n   */\n  public getDefaultNames(): string[] {\n    return Object.keys(this.defaults);\n  }\n\n  /**\n   * @return {string[]}  The names of all the global attributes\n   */\n  public getGlobalNames(): string[] {\n    return Object.keys(this.global);\n  }\n\n  /**\n   * @return {PropertyList}  The attribute object\n   */\n  public getAllAttributes(): PropertyList {\n    return this.attributes;\n  }\n\n  /**\n   * @return {PropertyList}  The inherited object\n   */\n  public getAllInherited(): PropertyList {\n    return this.inherited;\n  }\n\n  /**\n   * @return {PropertyList}  The defaults object\n   */\n  public getAllDefaults(): PropertyList {\n    return this.defaults;\n  }\n\n  /**\n   * @return {PropertyList}  The global object\n   */\n  public getAllGlobals(): PropertyList {\n    return this.global;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview Generic Node classes for node trees\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {NodeFactory} from './NodeFactory.js';\n\n/**\n *  PropertyList and Property are for string data like\n *  attributes and other properties\n */\nexport type Property = string | number | boolean;\nexport type PropertyList = {[key: string]: Property};\n\n/*********************************************************/\n/**\n *  The generic Node interface\n */\n\nexport interface Node {\n  readonly kind: string;\n  /**\n   * The NodeFactory to use to create additional nodes, as needed\n   */\n  readonly factory: NodeFactory<Node, NodeClass>;\n  parent: Node;\n  childNodes: Node[];\n\n  /**\n   * @param {string} name     The name of the property to set\n   * @param {Property} value  The value to which the property will be set\n   */\n  setProperty(name: string, value: Property): void;\n\n  /**\n   * @param {string} name  The name of the property to get\n   * @return {Property}   The value of the named property\n   */\n  getProperty(name: string): Property;\n\n  /**\n   * @return {string[]}  An array of the names of every property currently defined\n   */\n  getPropertyNames(): string[];\n\n  /**\n   * @return {PropertyList}  The propery list containing all the properties of the node\n   */\n  getAllProperties(): PropertyList;\n\n  /**\n   * @param {string[]} names  The names of the properties to be removed\n   */\n  removeProperty(...names: string[]): void;\n\n\n  /**\n   * @param {string} kind  The type of node to test for\n   * @return {boolean}     True when the node is of the given type\n   */\n  isKind(kind: string): boolean;\n\n  /**\n   * @param {Node[]} children  The child nodes to add to this node\n   */\n  setChildren(children: Node[]): void;\n\n  /**\n   * @param {Node} child  A node to add to this node's children\n   * @return {Node}       The child node that was added\n   */\n  appendChild(child: Node): Node;\n\n  /**\n   * @param {Node} newChild  A child node to be inserted\n   * @param {Node} oldChild  A child node to be replaced\n   * @return {Node}          The old child node that was removed\n   */\n  replaceChild(newChild: Node, oldChild: Node): Node;\n\n  /**\n   * @param {Node} child   Child node to be removed\n   * @return {Node}        The old child node that was removed\n   */\n  removeChild(child: Node): Node;\n\n  /**\n   * @param {Node} child  A child node whose index in childNodes is desired\n   * @return {number}     The index of the child in childNodes, or null if not found\n   */\n  childIndex(child: Node): number;\n\n  /**\n   * Make a deep copy of the node (but with no parent).\n   */\n  copy(): Node;\n\n  /**\n   * @param {string} kind  The kind of nodes to be located in the tree\n   * @return {Node[]}      An array of nodes that are children (at any depth) of the given kind\n   */\n  findNodes(kind: string): Node[];\n\n  /**\n   * @param {Function} func  A function to apply to each node in the tree rooted at this node\n   * @param {any} data       Data to pass to the function (as state information)\n   */\n  walkTree(func: (node: Node, data?: any) => void, data?: any): void;\n}\n\n/*********************************************************/\n/**\n *  The generic Node class interface\n */\n\nexport interface NodeClass {\n  /**\n   * @param {NodeFactory} factory  The NodeFactory to use to create new nodes when needed\n   * @param {PropertyList} properties  Any properties to be added to the node, if any\n   * @param {Node[]} children  The initial child nodes, if any\n   * @return {Node}  The newly created node\n   */\n  new (factory: NodeFactory<Node, NodeClass>, properties?: PropertyList, children?: Node[]): Node;\n}\n\n/*********************************************************/\n/**\n *  The abstract Node class\n */\n\nexport abstract class AbstractNode implements Node {\n\n  /**\n   * The parent node for this one\n   */\n  public parent: Node = null;\n\n  /**\n   * The properties for this node\n   */\n  protected properties: PropertyList = {};\n\n  /**\n   * The children for this node\n   */\n  public childNodes: Node[] = [];\n\n  /**\n   * @param {NodeFactory} factory  The NodeFactory to use to create new nodes when needed\n   * @param {PropertyList} properties  Any properties to be added to the node, if any\n   * @param {Node[]} children  The initial child nodes, if any\n   *\n   * @constructor\n   * @implements {Node}\n   */\n  constructor(readonly factory: NodeFactory<Node, NodeClass>, properties: PropertyList = {}, children: Node[] = []) {\n    for (const name of Object.keys(properties)) {\n      this.setProperty(name, properties[name]);\n    }\n    if (children.length) {\n      this.setChildren(children);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'unknown';\n  }\n\n  /**\n   * @override\n   */\n  public setProperty(name: string, value: Property) {\n    this.properties[name] = value;\n  }\n\n  /**\n   * @override\n   */\n  public getProperty(name: string) {\n    return this.properties[name];\n  }\n\n  /**\n   * @override\n   */\n  public getPropertyNames() {\n    return Object.keys(this.properties);\n  }\n\n  /**\n   * @override\n   */\n  public getAllProperties() {\n    return this.properties;\n  }\n\n  /**\n   * @override\n   */\n  public removeProperty(...names: string[]) {\n    for (const name of names) {\n      delete this.properties[name];\n    }\n  }\n\n\n  /**\n   * @override\n   */\n  public isKind(kind: string): boolean {\n    return this.factory.nodeIsKind(this, kind);\n  }\n\n\n  /**\n   * @override\n   */\n  public setChildren(children: Node[]) {\n    this.childNodes = [];\n    for (let child of children) {\n      this.appendChild(child);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public appendChild(child: Node) {\n    this.childNodes.push(child);\n    child.parent = this;\n    return child;\n  }\n\n  /**\n   * @override\n   */\n  public replaceChild(newChild: Node, oldChild: Node) {\n    let i = this.childIndex(oldChild);\n    // If i === null should we error?  return null?  silently fail?\n    if (i !== null) {\n      this.childNodes[i] = newChild;\n      newChild.parent = this;\n      oldChild.parent = null;\n    }\n    return newChild;\n  }\n\n  /**\n   * @override\n   */\n  public removeChild(child: Node) {\n    const i = this.childIndex(child);\n    if (i !== null) {\n      this.childNodes.splice(i, 1);\n      child.parent = null;\n    }\n    return child;\n  }\n\n\n  /**\n   * @override\n   */\n  public childIndex(node: Node) {\n    let i = this.childNodes.indexOf(node);\n    return (i === -1 ? null : i);\n  }\n\n\n  /**\n   * @override\n   */\n  public copy() {\n    const node = (this as AbstractNode).factory.create(this.kind) as AbstractNode;\n    node.properties = {...this.properties};\n    for (const child of this.childNodes || []) {\n      if (child) {\n        node.appendChild(child.copy());\n      }\n    }\n    return node;\n  }\n\n  /**\n   * @override\n   */\n  public findNodes(kind: string) {\n    let nodes: Node[] = [];\n    this.walkTree((node: Node) => {\n      if (node.isKind(kind)) {\n        nodes.push(node);\n      }\n    });\n    return nodes;\n  }\n\n\n  /**\n   * @override\n   */\n  public walkTree(func: (node: Node, data?: any) => void, data?: any) {\n    func(this, data);\n    for (const child of this.childNodes) {\n      if (child) {\n        child.walkTree(func, data);\n      }\n    }\n    return data;\n  }\n\n  /**\n   * Simple string version for debugging, just to get the structure.\n   */\n  public toString() {\n    return this.kind + '(' + this.childNodes.join(',') + ')';\n  }\n\n}\n\n/*********************************************************/\n/**\n *  The abstract EmptyNode class\n */\n\nexport abstract class AbstractEmptyNode extends AbstractNode {\n  /**\n   *  We don't have children, so ignore these methods\n   */\n\n  /**\n   * @override\n   */\n  public setChildren(_children: Node[]) {\n  }\n\n  /**\n   * @override\n   */\n  public appendChild(child: Node) {\n    return child;\n  }\n\n  /**\n   * @override\n   */\n  public replaceChild(_newChild: Node, oldChild: Node) {\n    return oldChild;\n  }\n\n  /**\n   * @override\n   */\n  public childIndex(_node: Node) {\n    return null as number;\n  }\n\n  /**\n   * Don't step into children (there aren't any)\n   *\n   * @override\n   */\n  public walkTree(func: (node: Node, data?: any) => void, data?: any) {\n    func(this, data);\n    return data;\n  }\n\n  /**\n   * Simple string version for debugging, just to get the structure.\n   */\n  public toString() {\n    return this.kind;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Interfaces and abstract classes for MmlNode objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {Attributes, INHERIT} from './Attributes.js';\nimport {Property, PropertyList, Node, AbstractNode, AbstractEmptyNode, NodeClass} from '../Tree/Node.js';\nimport {MmlFactory} from './MmlFactory.js';\nimport {DOMAdaptor} from '../DOMAdaptor.js';\n\n/**\n *  Used in setInheritedAttributes() to pass originating node kind as well as property value\n */\nexport type AttributeList = {[attribute: string]: [string, Property]};\n\n/**\n *  These are the TeX classes for spacing computations\n */\nexport const TEXCLASS = {\n  ORD:   0,\n  OP:    1,\n  BIN:   2,\n  REL:   3,\n  OPEN:  4,\n  CLOSE: 5,\n  PUNCT: 6,\n  INNER: 7,\n  VCENTER: 8,  // Used in TeXAtom, but not for spacing\n  NONE:   -1\n};\n\nexport const TEXCLASSNAMES = ['ORD', 'OP', 'BIN', 'REL', 'OPEN', 'CLOSE', 'PUNCT', 'INNER', 'VCENTER'];\n\n/**\n *  The spacing sizes used by the TeX spacing table below.\n */\nconst TEXSPACELENGTH = ['', 'thinmathspace', 'mediummathspace', 'thickmathspace'];\n\n/**\n * See TeXBook Chapter 18 (p. 170)\n */\nconst TEXSPACE = [\n  [ 0, -1,  2,  3,  0,  0,  0,  1], // ORD\n  [-1, -1,  0,  3,  0,  0,  0,  1], // OP\n  [ 2,  2,  0,  0,  2,  0,  0,  2], // BIN\n  [ 3,  3,  0,  0,  3,  0,  0,  3], // REL\n  [ 0,  0,  0,  0,  0,  0,  0,  0], // OPEN\n  [ 0, -1,  2,  3,  0,  0,  0,  1], // CLOSE\n  [ 1,  1,  0,  1,  1,  1,  1,  1], // PUNCT\n  [ 1, -1,  2,  3,  1,  0,  1,  1]  // INNER\n];\n\n/**\n * Attributes used to determine indentation and shifting\n */\nexport const indentAttributes = [\n  'indentalign', 'indentalignfirst',\n  'indentshift', 'indentshiftfirst'\n];\n\n/**\n * The nodes that can be in the internal MathML tree\n */\nexport type MMLNODE = MmlNode | TextNode | XMLNode;\n\n/*****************************************************************/\n/**\n *  The MmlNode interface (extends Node interface)\n */\n\nexport interface MmlNode extends Node {\n\n  /**\n   * Test various properties of MathML nodes\n   */\n  readonly isToken: boolean;\n  readonly isEmbellished: boolean;\n  readonly isSpacelike: boolean;\n  readonly linebreakContainer: boolean;\n  readonly hasNewLine: boolean;\n\n  /**\n   *  The expected number of children (-1 means use inferred mrow)\n   */\n  readonly arity: number;\n  readonly isInferred: boolean;\n\n  /**\n   *  Get the parent node (skipping inferred mrows and\n   *    other nodes marked as notParent)\n   */\n  readonly Parent: MmlNode;\n  readonly notParent: boolean;\n\n  /**\n   * The actual parent in the tree\n   */\n  parent: MmlNode;\n\n  /**\n   *  values needed for TeX spacing computations\n   */\n  texClass: number;\n  prevClass: number;\n  prevLevel: number;\n\n  /**\n   *  The attributes (explicit and inherited) for this node\n   */\n  attributes: Attributes;\n\n  /**\n   * @return {MmlNode}  For embellished operators, the child node that contains the\n   *                    core <mo> node.  For non-embellished nodes, the original node.\n   */\n  core(): MmlNode;\n\n  /**\n   * @return {MmlNode}  For embellished operators, the core <mo> element (at whatever\n   *                    depth).  For non-embellished nodes, the original node itself.\n   */\n  coreMO(): MmlNode;\n\n  /**\n   * @return {number}   For embellished operators, the index of the child node containing\n   *                    the core <mo>.  For non-embellished nodes, 0.\n   */\n  coreIndex(): number;\n\n  /**\n   * @return {number}  The index of this node in its parent's childNodes array.\n   */\n  childPosition(): number;\n\n  /**\n   * @param {MmlNode} prev  The node that is before this one for TeX spacing purposes\n   *                        (not all nodes count in TeX measurements)\n   * @return {MmlNode}  The node that should be the previous node for the next one\n   *                    in the tree (usually, either the last child, or the node itself)\n   */\n  setTeXclass(prev: MmlNode): MmlNode;\n\n  /**\n   * @return {string}  The spacing to use before this element (one of TEXSPACELENGTH array above)\n   */\n  texSpacing(): string;\n\n  /**\n   * @return {boolean}  The core mo element has an explicit 'form', 'lspace', or 'rspace' attribute\n   */\n  hasSpacingAttributes(): boolean;\n\n  /**\n   * Sets the nodes inherited attributes, and pushes them to the nodes children.\n   *\n   * @param {AttributeList} attributes  The list of inheritable attributes (with the node kinds\n   *                                    from which they came)\n   * @param {boolean} display           The displaystyle to inherit\n   * @param {number} level              The scriptlevel to inherit\n   * @param {boolean} prime             The TeX prime style to inherit (T vs. T', etc).\n   */\n  setInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean): void;\n\n  /**\n   * Set the nodes inherited attributes based on the attributes of the given node\n   *   (used for creating extra nodes in the tree after setInheritedAttributes has already run)\n   *\n   * @param {MmlNode} node   The node whose attributes are to be used as a template\n   */\n  inheritAttributesFrom(node: MmlNode): void;\n\n  /**\n   * Replace the current node with an error message (or the name of the node)\n   *\n   * @param {string} message         The error message to use\n   * @param {PropertyList} options   The options telling how much to verify\n   * @param {boolean} short          True means use just the kind if not using full errors\n   * @return {MmlNode}               The construted merror\n   */\n  mError(message: string, options: PropertyList, short?: boolean): MmlNode;\n\n  /**\n   * Check integrity of MathML structure\n   *\n   * @param {PropertyList} options  The options controlling the check\n   */\n  verifyTree(options?: PropertyList): void;\n}\n\n\n/*****************************************************************/\n/**\n *  The MmlNode class interface (extends the NodeClass)\n */\n\nexport interface MmlNodeClass extends NodeClass {\n\n  /**\n   *  The list of default attribute values for nodes of this class\n   */\n  defaults?: PropertyList;\n\n  /**\n   * An MmlNode takes a NodeFactory (so it can create additional nodes as needed), a list\n   *   of attributes, and an array of children and returns the desired MmlNode with\n   *   those attributes and children\n   *\n   * @constructor\n   * @param {MmlFactory} factory       The MathML node factory to use to create additional nodes\n   * @param {PropertyList} attributes  The list of initial attributes for the node\n   * @param {MmlNode[]} children       The initial child nodes (more can be added later)\n   */\n  new (factory: MmlFactory, attributes?: PropertyList, children?: MmlNode[]): MmlNode;\n\n}\n\n\n/*****************************************************************/\n/**\n *  The abstract MmlNode class (extends the AbstractNode class and implements\n *  the IMmlNode interface)\n */\n\nexport abstract class AbstractMmlNode extends AbstractNode implements MmlNode {\n\n  /**\n   * The properties common to all MathML nodes\n   */\n  public static defaults: PropertyList = {\n    mathbackground: INHERIT,\n    mathcolor: INHERIT,\n    mathsize: INHERIT,  // technically only for token elements, but <mstyle mathsize=\"...\"> should\n    //    scale all spaces, fractions, etc.\n    dir: INHERIT\n  };\n\n  /**\n   *  This lists properties that do NOT get inherited between specific kinds\n   *  of nodes.  The outer keys are the node kinds that are being inherited FROM,\n   *  while the second level of keys are the nodes that INHERIT the values.  Any\n   *  property appearing in the innermost list is NOT inherited by the pair.\n   *\n   *  For example, an mpadded element will not inherit a width attribute from an mstyle node.\n   */\n  public static noInherit: {[node1: string]: {[node2: string]: {[attribute: string]: boolean}}} = {\n    mstyle: {\n      mpadded: {width: true, height: true, depth: true, lspace: true, voffset: true},\n      mtable:  {width: true, height: true, depth: true, align: true}\n    },\n    maligngroup: {\n      mrow: {groupalign: true},\n      mtable: {groupalign: true}\n    }\n  };\n\n  /**\n   * This lists the attributes that should always be inherited,\n   *   even when there is no default value for the attribute.\n   */\n  public static alwaysInherit: {[name: string]: boolean} = {\n    scriptminsize: true,\n    scriptsizemultiplier: true\n  };\n\n  /**\n   * This is the list of options for the verifyTree() method\n   */\n  public static verifyDefaults: PropertyList = {\n    checkArity: true,\n    checkAttributes: false,\n    fullErrors: false,\n    fixMmultiscripts: true,\n    fixMtables: true\n  };\n\n  /*\n   * These default to being unset (the node doesn't participate in spacing calculations).\n   * The correct values are produced when the setTeXclass() method is called on the tree.\n   */\n\n  /**\n   * The TeX class for the preceding node\n   */\n  public prevClass: number = null;\n\n  /**\n   * The scriptlevel of the preceding node\n   */\n  public prevLevel: number = null;\n\n  /**\n   * This node's attributes\n   */\n  public attributes: Attributes;\n\n  /**\n   *  Child nodes are MmlNodes (special case of Nodes).\n   */\n  public childNodes: MmlNode[];\n\n  /**\n   * The parent is an MmlNode\n   */\n  public parent: MmlNode;\n\n  /**\n   * The node factory is an MmlFactory\n   */\n  public readonly factory: MmlFactory;\n\n  /**\n   * The TeX class of this node (obtained via texClass below)\n   */\n  protected texclass: number = null;\n\n  /**\n   *  Create an MmlNode:\n   *    If the arity is -1, add the inferred row (created by the factory)\n   *    Add the children, if any\n   *    Create the Attribute object from the class defaults and the global defaults (the math node defaults)\n   *\n   *  @override\n   */\n  constructor(factory: MmlFactory, attributes: PropertyList = {}, children: MmlNode[] = []) {\n    super(factory);\n    if (this.arity < 0) {\n      this.childNodes = [factory.create('inferredMrow')];\n      this.childNodes[0].parent = this;\n    }\n    this.setChildren(children);\n    this.attributes = new Attributes(\n      factory.getNodeClass(this.kind).defaults,\n      factory.getNodeClass('math').defaults\n    );\n    this.attributes.setList(attributes);\n  }\n\n  /**\n   * @override\n   *\n   * @param {boolean} keepIds   True to copy id attributes, false to skip them.\n   *                              (May cause error in the future, since not part of the interface.)\n   * @return {AbstractMmlNode}  The copied node tree.\n   */\n  public copy(keepIds: boolean = false): AbstractMmlNode {\n    const node = this.factory.create(this.kind) as AbstractMmlNode;\n    node.properties = {...this.properties};\n    if (this.attributes) {\n      const attributes = this.attributes.getAllAttributes();\n      for (const name of Object.keys(attributes)) {\n        if (name !== 'id' || keepIds) {\n          node.attributes.set(name, attributes[name]);\n        }\n      }\n    }\n    if (this.childNodes && this.childNodes.length) {\n      let children = this.childNodes as MmlNode[];\n      if (children.length === 1 && children[0].isInferred) {\n        children = children[0].childNodes as MmlNode[];\n      }\n      for (const child of children) {\n        if (child) {\n          node.appendChild(child.copy() as MmlNode);\n        } else {\n          node.childNodes.push(null);\n        }\n      }\n    }\n    return node;\n  }\n\n  /**\n   * The TeX class for this node\n   */\n  public get texClass(): number {\n    return this.texclass;\n  }\n\n  /**\n   * The TeX class for this node\n   */\n  public set texClass(texClass: number) {\n    this.texclass = texClass;\n  }\n\n  /**\n   * @return {boolean}  true if this is a token node\n   */\n  public get isToken(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  true if this is an embellished operator\n   */\n  public get isEmbellished(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  true if this is a space-like node\n   */\n  public get isSpacelike(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  true if this is a node that supports linebreaks in its children\n   */\n  public get linebreakContainer(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  true if this node contains a line break\n   */\n  public get hasNewLine(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {number}  The number of children allowed, or Infinity for any number,\n   *                   or -1 for when an inferred row is needed for the children.\n   *                   Special case is 1, meaning at least one (other numbers\n   *                   mean exactly that many).\n   */\n  public get arity(): number {\n    return Infinity;\n  }\n\n  /**\n   * @return {boolean}  true if this is an inferred mrow\n   */\n  public get isInferred(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {MmlNode}  The logical parent of this node (skipping over inferred rows\n   *                      some other node types)\n   */\n  public get Parent(): MmlNode {\n    let parent = this.parent;\n    while (parent && parent.notParent) {\n      parent = parent.Parent;\n    }\n    return parent;\n  }\n\n  /**\n   * @return {boolean}  true if this is a node that doesn't count as a parent node in Parent()\n   */\n  public get notParent(): boolean {\n    return false;\n  }\n\n  /**\n   * If there is an inferred row, the the children of that instead\n   *\n   * @override\n   */\n  public setChildren(children: MmlNode[]) {\n    if (this.arity < 0) {\n      return this.childNodes[0].setChildren(children);\n    }\n    return super.setChildren(children);\n  }\n  /**\n   * If there is an inferred row, append to that instead.\n   * If a child is inferred, append its children instead.\n   *\n   * @override\n   */\n  public appendChild(child: MmlNode) {\n    if (this.arity < 0) {\n      this.childNodes[0].appendChild(child);\n      return child;\n    }\n    if (child.isInferred) {\n      //\n      //  If we can have arbitrary children, remove the inferred mrow\n      //  (just add its children).\n      //\n      if (this.arity === Infinity) {\n        child.childNodes.forEach((node) => super.appendChild(node));\n        return child;\n      }\n      //\n      //  Otherwise, convert the inferred mrow to an explicit mrow\n      //\n      const original = child;\n      child = this.factory.create('mrow');\n      child.setChildren(original.childNodes);\n      child.attributes = original.attributes;\n      for (const name of original.getPropertyNames()) {\n        child.setProperty(name, original.getProperty(name));\n      }\n    }\n    return super.appendChild(child);\n  }\n  /**\n   * If there is an inferred row, remove the child from there\n   *\n   * @override\n   */\n  public replaceChild(newChild: MmlNode, oldChild: MmlNode) {\n    if (this.arity < 0) {\n      this.childNodes[0].replaceChild(newChild, oldChild);\n      return newChild;\n    }\n    return super.replaceChild(newChild, oldChild);\n  }\n\n  /**\n   * @override\n   */\n  public core(): MmlNode {\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public coreMO(): MmlNode {\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public coreIndex() {\n    return 0;\n  }\n\n  /**\n   * @override\n   */\n  public childPosition() {\n    let child: MmlNode = this;\n    let parent = child.parent;\n    while (parent && parent.notParent) {\n      child = parent;\n      parent = parent.parent;\n    }\n    if (parent) {\n      let i = 0;\n      for (const node of parent.childNodes) {\n        if (node === child) {\n          return i;\n        }\n        i++;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode): MmlNode {\n    this.getPrevClass(prev);\n    return (this.texClass != null ? this : prev);\n  }\n  /**\n   * For embellished operators, get the data from the core and clear the core\n   *\n   * @param {MmlNode} core  The core <mo> for this node\n   */\n  protected updateTeXclass(core: MmlNode) {\n    if (core) {\n      this.prevClass = core.prevClass;\n      this.prevLevel = core.prevLevel;\n      core.prevClass = core.prevLevel = null;\n      this.texClass = core.texClass;\n    }\n  }\n  /**\n   * Get the previous element's texClass and scriptlevel\n   *\n   * @param {MmlNode} prev  The previous node to this one\n   */\n  protected getPrevClass(prev: MmlNode) {\n    if (prev) {\n      this.prevClass = prev.texClass;\n      this.prevLevel = prev.attributes.get('scriptlevel') as number;\n    }\n  }\n\n  /**\n   * @return {string}  returns the spacing to use before this node\n   */\n  public texSpacing(): string {\n    let prevClass = (this.prevClass != null ? this.prevClass : TEXCLASS.NONE);\n    let texClass = this.texClass || TEXCLASS.ORD;\n    if (prevClass === TEXCLASS.NONE || texClass === TEXCLASS.NONE) {\n      return '';\n    }\n    if (prevClass === TEXCLASS.VCENTER) {\n      prevClass = TEXCLASS.ORD;\n    }\n    if (texClass === TEXCLASS.VCENTER) {\n      texClass = TEXCLASS.ORD;\n    }\n    let space = TEXSPACE[prevClass][texClass];\n    if ((this.prevLevel > 0 || this.attributes.get('scriptlevel') > 0) && space >= 0) {\n      return '';\n    }\n    return TEXSPACELENGTH[Math.abs(space)];\n  }\n\n  /**\n   * @return {boolean}  The core mo element has an explicit 'form' attribute\n   */\n  public hasSpacingAttributes(): boolean {\n    return this.isEmbellished && this.coreMO().hasSpacingAttributes();\n  }\n\n  /**\n   * Sets the inherited propertis for this node, and pushes inherited properties to the children\n   *\n   *   For each inheritable attribute:\n   *     If the node has a default for this attribute, try to inherit it\n   *       but check if the noInherit object prevents that.\n   *   If the node doesn't have an explicit displaystyle, inherit it\n   *   If the node doesn't have an explicit scriptstyle, inherit it\n   *   If the prime style is true, set it as a property (it is not a MathML attribute)\n   *   Check that the number of children is correct\n   *   Finally, push any inherited attributes to teh children.\n   *\n   * @override\n   */\n  public setInheritedAttributes(attributes: AttributeList = {},\n                                display: boolean = false, level: number = 0, prime: boolean = false) {\n    let defaults = this.attributes.getAllDefaults();\n    for (const key of Object.keys(attributes)) {\n      if (defaults.hasOwnProperty(key) || AbstractMmlNode.alwaysInherit.hasOwnProperty(key)) {\n        let [node, value] = attributes[key];\n        let noinherit = (AbstractMmlNode.noInherit[node] || {})[this.kind] || {};\n        if (!noinherit[key]) {\n          this.attributes.setInherited(key, value);\n        }\n      }\n    }\n    let displaystyle = this.attributes.getExplicit('displaystyle');\n    if (displaystyle === undefined) {\n      this.attributes.setInherited('displaystyle', display);\n    }\n    let scriptlevel = this.attributes.getExplicit('scriptlevel');\n    if (scriptlevel === undefined) {\n      this.attributes.setInherited('scriptlevel', level);\n    }\n    if (prime) {\n      this.setProperty('texprimestyle', prime);\n    }\n    let arity = this.arity;\n    if (arity >= 0 && arity !== Infinity && ((arity === 1 && this.childNodes.length === 0) ||\n                                             (arity !== 1 && this.childNodes.length !== arity))) {\n      //\n      //  Make sure there are the right number of child nodes\n      //  (trim them or add empty mrows)\n      //\n      if (arity < this.childNodes.length) {\n        this.childNodes = this.childNodes.slice(0, arity);\n      } else {\n        while (this.childNodes.length < arity) {\n          this.appendChild(this.factory.create('mrow'));\n        }\n      }\n    }\n    this.setChildInheritedAttributes(attributes, display, level, prime);\n  }\n  /**\n   * Apply inherited attributes to all children\n   * (Some classes override this to handle changes in displaystyle and scriptlevel)\n   *\n   * @param {AttributeList} attributes  The list of inheritable attributes (with the node kinds\n   *                                    from which they came)\n   * @param {boolean} display           The displaystyle to inherit\n   * @param {number} level              The scriptlevel to inherit\n   * @param {boolean} prime             The TeX prime style to inherit (T vs. T', etc).\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    for (const child of this.childNodes) {\n      child.setInheritedAttributes(attributes, display, level, prime);\n    }\n  }\n  /**\n   * Used by subclasses to add their own attributes to the inherited list\n   * (e.g., mstyle uses this to augment the inherited attibutes)\n   *\n   * @param {AttributeList} current    The current list of inherited attributes\n   * @param {PropertyList} attributes  The new attributes to add into the list\n   */\n  protected addInheritedAttributes(current: AttributeList, attributes: PropertyList) {\n    let updated: AttributeList = {...current};\n    for (const name of Object.keys(attributes)) {\n      if (name !== 'displaystyle' && name !== 'scriptlevel' && name !== 'style') {\n        updated[name] = [this.kind, attributes[name]];\n      }\n    }\n    return updated;\n  }\n\n  /**\n   * Set the nodes inherited attributes based on the attributes of the given node\n   *   (used for creating extra nodes in the tree after setInheritedAttributes has already run)\n   *\n   * @param {MmlNode} node   The node whose attributes are to be used as a template\n   */\n  public inheritAttributesFrom(node: MmlNode) {\n    const attributes = node.attributes;\n    const display = attributes.get('displaystyle') as boolean;\n    const scriptlevel = attributes.get('scriptlevel') as number;\n    const defaults: AttributeList = (!attributes.isSet('mathsize') ? {} : {\n      mathsize: ['math', attributes.get('mathsize')]\n    });\n    const prime = node.getProperty('texprimestyle') as boolean || false;\n    this.setInheritedAttributes(defaults, display, scriptlevel, prime);\n  }\n\n  /**\n   * Verify the attributes, and that there are the right number of children.\n   * Then verify the children.\n   *\n   * @param {PropertyList} options   The options telling how much to verify\n   */\n  public verifyTree(options: PropertyList = null) {\n    if (options === null) {\n      return;\n    }\n    this.verifyAttributes(options);\n    let arity = this.arity;\n    if (options['checkArity']) {\n      if (arity >= 0 && arity !== Infinity &&\n          ((arity === 1 && this.childNodes.length === 0) ||\n           (arity !== 1 && this.childNodes.length !== arity))) {\n        this.mError('Wrong number of children for \"' + this.kind + '\" node', options, true);\n      }\n    }\n    this.verifyChildren(options);\n  }\n\n  /**\n   * Verify that all the attributes are valid (i.e., have defaults)\n   *\n   * @param {PropertyList} options   The options telling how much to verify\n   */\n  protected verifyAttributes(options: PropertyList) {\n    if (options['checkAttributes']) {\n      const attributes = this.attributes;\n      const bad = [];\n      for (const name of attributes.getExplicitNames()) {\n        if (name.substr(0, 5) !== 'data-' && attributes.getDefault(name) === undefined &&\n            !name.match(/^(?:class|style|id|(?:xlink:)?href)$/)) {\n          // FIXME: provide a configurable checker for names that are OK\n          bad.push(name);\n        }\n        // FIXME: add ability to check attribute values?\n      }\n      if (bad.length) {\n        this.mError('Unknown attributes for ' + this.kind + ' node: ' + bad.join(', '), options);\n      }\n    }\n  }\n\n  /**\n   * Verify the children.\n   *\n   * @param {PropertyList} options   The options telling how much to verify\n   */\n  protected verifyChildren(options: PropertyList) {\n    for (const child of this.childNodes) {\n      child.verifyTree(options);\n    }\n  }\n\n  /**\n   * Replace the current node with an error message (or the name of the node)\n   *\n   * @param {string} message         The error message to use\n   * @param {PropertyList} options   The options telling how much to verify\n   * @param {boolean} short          True means use just the kind if not using full errors\n   * @return {MmlNode}               The constructed merror\n   */\n  public mError(message: string, options: PropertyList, short: boolean = false): MmlNode {\n    if (this.parent && this.parent.isKind('merror')) {\n      return null;\n    }\n    let merror = this.factory.create('merror');\n    merror.attributes.set('data-mjx-message', message);\n    if (options['fullErrors'] || short) {\n      let mtext = this.factory.create('mtext');\n      let text = this.factory.create('text') as TextNode;\n      text.setText(options['fullErrors'] ? message : this.kind);\n      mtext.appendChild(text);\n      merror.appendChild(mtext);\n      this.parent.replaceChild(merror, this);\n    } else {\n      this.parent.replaceChild(merror, this);\n      merror.appendChild(this);\n    }\n    return merror;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  The abstract MmlNode Token node class (extends the AbstractMmlNode)\n */\n\nexport abstract class AbstractMmlTokenNode extends AbstractMmlNode {\n\n  /**\n   * Add the attributes common to all token nodes\n   */\n  public static defaults: PropertyList = {\n      ...AbstractMmlNode.defaults,\n    mathvariant: 'normal',\n    mathsize: INHERIT\n  };\n\n  /**\n   * @override\n   */\n  public get isToken() {\n    return true;\n  }\n\n  /**\n   * Get the text of the token node (skipping mglyphs, and combining\n   *   multiple text nodes)\n   */\n  public getText() {\n    let text = '';\n    for (const child of this.childNodes) {\n      if (child instanceof TextNode) {\n        text += child.getText();\n      }\n    }\n    return text;\n  }\n\n  /**\n   * Only inherit to child nodes that are AbstractMmlNodes (not TextNodes)\n   *\n   * @override\n   */\n  protected setChildInheritedAttributes(attributes: AttributeList, display: boolean, level: number, prime: boolean) {\n    for (const child of this.childNodes) {\n      if (child instanceof AbstractMmlNode) {\n        child.setInheritedAttributes(attributes, display, level, prime);\n      }\n    }\n  }\n\n  /**\n   * Only step into children that are AbstractMmlNodes (not TextNodes)\n   * @override\n   */\n  public walkTree(func: (node: Node, data?: any) => void, data?: any) {\n    func(this, data);\n    for (const child of this.childNodes) {\n      if (child instanceof AbstractMmlNode) {\n        child.walkTree(func, data);\n      }\n    }\n    return data;\n  }\n\n}\n\n\n/*****************************************************************/\n/**\n *  The abstract MmlNode Layout class (extends the AbstractMmlNode)\n *\n *  These have inferred mrows (so only one child) and can be\n *  spacelike or embellished based on their contents.\n */\n\nexport abstract class AbstractMmlLayoutNode extends AbstractMmlNode {\n\n  /**\n   * Use the same defaults as AbstractMmlNodes\n   */\n  public static defaults: PropertyList = AbstractMmlNode.defaults;\n\n  /**\n   * @override\n   */\n  public get isSpacelike() {\n    return this.childNodes[0].isSpacelike;\n  }\n\n  /**\n   * @override\n   */\n  public get isEmbellished() {\n    return this.childNodes[0].isEmbellished;\n  }\n\n  /**\n   * @override\n   */\n  public get arity() {\n    return -1;\n  }\n\n  /**\n   * @override\n   */\n  public core() {\n    return this.childNodes[0];\n  }\n\n  /**\n   * @override\n   */\n  public coreMO() {\n    return this.childNodes[0].coreMO();\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    prev = this.childNodes[0].setTeXclass(prev);\n    this.updateTeXclass(this.childNodes[0]);\n    return prev;\n  }\n}\n\n/*****************************************************************/\n/**\n *  The abstract MmlNode-with-base-node Class (extends the AbstractMmlNode)\n *\n *  These have a base element and other elemetns, (e.g., script elements for msubsup).\n *  They can be embellished (if their base is), and get their TeX classes\n *    from their base with their scripts being handled as separate math lists.\n */\n\nexport abstract class AbstractMmlBaseNode extends AbstractMmlNode {\n\n  /**\n   * Use the same defaults as AbstractMmlNodes\n   */\n  public static defaults: PropertyList = AbstractMmlNode.defaults;\n\n  /**\n   * @override\n   */\n  public get isEmbellished() {\n    return this.childNodes[0].isEmbellished;\n  }\n\n  /**\n   * @override\n   */\n  public core() {\n    return this.childNodes[0];\n  }\n\n  /**\n   * @override\n   */\n  public coreMO() {\n    return this.childNodes[0].coreMO();\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    this.getPrevClass(prev);\n    this.texClass = TEXCLASS.ORD;\n    let base = this.childNodes[0];\n    if (base) {\n      if (this.isEmbellished || base.isKind('mi')) {\n        prev = base.setTeXclass(prev);\n        this.updateTeXclass(this.core());\n      } else {\n        base.setTeXclass(null);\n        prev = this;\n      }\n    } else {\n      prev = this;\n    }\n    for (const child of this.childNodes.slice(1)) {\n      if (child) {\n        child.setTeXclass(null);\n      }\n    }\n    return prev;\n  }\n}\n\n/*****************************************************************/\n/**\n *  The abstract MmlNode Empty Class (extends AbstractEmptyNode, implements MmlNode)\n *\n *  These have no children and no attributes (TextNode and XMLNode), so we\n *  override all the methods dealing with them, and with the data that usually\n *  goes with an MmlNode.\n */\n\nexport abstract class AbstractMmlEmptyNode extends AbstractEmptyNode implements MmlNode {\n\n  /**\n   *  Parent is an MmlNode\n   */\n  public parent: MmlNode;\n\n  /**\n   * @return {boolean}  Not a token element\n   */\n  public get isToken(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  Not embellished\n   */\n  public get isEmbellished(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  Not space-like\n   */\n  public get isSpacelike(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  Not a container of any kind\n   */\n  public get linebreakContainer(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  Does not contain new lines\n   */\n  public get hasNewLine(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {number}  No children\n   */\n  public get arity(): number {\n    return 0;\n  }\n\n  /**\n   * @return {boolean}  Is not an inferred row\n   */\n  public get isInferred(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean}  Is not a container element\n   */\n  public get notParent(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {MmlNode}  Parent is the actual parent\n   */\n  public get Parent(): MmlNode {\n    return this.parent;\n  }\n\n  /**\n   * @return {number}  No TeX class\n   */\n  public get texClass(): number {\n    return TEXCLASS.NONE;\n  }\n\n  /**\n   * @return {number}  No previous element\n   */\n  public get prevClass(): number {\n    return TEXCLASS.NONE;\n  }\n\n  /**\n   * @return {number}  No previous element\n   */\n  public get prevLevel(): number {\n    return 0;\n  }\n\n  /**\n   * @return {boolean}  The core mo element has an explicit 'form' attribute\n   */\n  public hasSpacingAttributes(): boolean {\n    return false;\n  }\n\n  /**\n   * return {Attributes}  No attributes, so don't store one\n   */\n  public get attributes(): Attributes {\n    return null;\n  }\n\n  /**\n   * @override\n   */\n  public core(): MmlNode {\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public coreMO(): MmlNode {\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public coreIndex() {\n    return 0;\n  }\n\n  /**\n   * @override\n   */\n  public childPosition() {\n    return 0;\n  }\n\n  /**\n   * @override\n   */\n  public setTeXclass(prev: MmlNode) {\n    return prev;\n  }\n  /**\n   * @override\n   */\n  public texSpacing() {\n    return '';\n  }\n\n  /**\n   * No children or attributes, so ignore this call.\n   *\n   * @override\n   */\n  public setInheritedAttributes(_attributes: AttributeList, _display: boolean, _level: number, _prime: boolean) {}\n\n  /**\n   * No children or attributes, so ignore this call.\n   *\n   * @override\n   */\n  public inheritAttributesFrom(_node: MmlNode) {}\n\n  /**\n   * No children or attributes, so ignore this call.\n   *\n   * @param {PropertyList} options  The options for the check\n   */\n  public verifyTree(_options: PropertyList) {}\n\n  /**\n   *  @override\n   */\n  public mError(_message: string, _options: PropertyList, _short: boolean = false) {\n    return null as MmlNode;\n  }\n\n}\n\n/*****************************************************************/\n/**\n *  The TextNode Class (extends AbstractMmlEmptyNode)\n */\n\nexport class TextNode extends AbstractMmlEmptyNode {\n  /**\n   * The text for this node\n   */\n  protected text: string = '';\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'text';\n  }\n\n  /**\n   * @return {string}  Return the node's text\n   */\n  public getText(): string {\n    return this.text;\n  }\n\n  /**\n   * @param {string} text  The text to use for the node\n   * @return {TextNode}  The text node (for chaining of method calls)\n   */\n  public setText(text: string): TextNode {\n    this.text = text;\n    return this;\n  }\n\n  /**\n   * @override\n   */\n  public copy() {\n    return (this.factory.create(this.kind) as TextNode).setText(this.getText());\n  }\n\n  /**\n   * Just use the text\n   */\n  public toString() {\n    return this.text;\n  }\n\n}\n\n\n/*****************************************************************/\n/**\n *  The XMLNode Class (extends AbstractMmlEmptyNode)\n */\n\nexport class XMLNode extends AbstractMmlEmptyNode {\n  /**\n   * The XML content for this node\n   */\n  protected xml: Object = null;\n\n  /**\n   * DOM adaptor for the content\n   */\n  protected adaptor: DOMAdaptor<any, any, any> = null;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'XML';\n  }\n\n  /**\n   * @return {Object}  Return the node's XML content\n   */\n  public getXML(): Object {\n    return this.xml;\n  }\n\n  /**\n   * @param {object} xml  The XML content to be saved\n   * @param {DOMAdaptor} adaptor DOM adaptor for the content\n   * @return {XMLNode}  The XML node (for chaining of method calls)\n   */\n  public setXML(xml: Object, adaptor: DOMAdaptor<any, any, any> = null): XMLNode {\n    this.xml = xml;\n    this.adaptor = adaptor;\n    return this;\n  }\n\n  /**\n   * @return {string}  The serialized XML content\n   */\n  public getSerializedXML(): string {\n    return this.adaptor.serializeXML(this.xml);\n  }\n\n  /**\n   * @override\n   */\n  public copy(): XMLNode {\n    return (this.factory.create(this.kind) as XMLNode).setXML(this.adaptor.clone(this.xml));\n  }\n\n  /**\n   * Just indicate that this is XML data\n   */\n  public toString() {\n    return 'XML data';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Defines the operator dictionary structure\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../Tree/Node.js';\nimport {TEXCLASS} from './MmlNode.js';\n\n/**\n * Types needed for the operator dictionary\n */\nexport type OperatorDef = [number, number, number, PropertyList];\nexport type OperatorList = {[name: string]: OperatorDef};\nexport type RangeDef = [number, number, number, string, string?];\n\n/**\n * @param {number} lspace            The operator's MathML left-hand spacing\n * @param {number} rspace            The operator's MathML right-hand spacing\n * @param {number} texClass          The default TeX class for the operator\n * @param {PropertyList} properties  Any default properties from the operator dictionary\n * @return {OperatorDef}             The operator definition array\n */\nexport function OPDEF(lspace: number, rspace: number, texClass: number = TEXCLASS.BIN,\n                      properties: PropertyList = null): OperatorDef {\n                        return [lspace, rspace, texClass, properties] as OperatorDef;\n                      }\n\n/**\n *  The various kinds of operators in the dictionary\n */\nexport const MO = {\n  ORD:        OPDEF(0, 0, TEXCLASS.ORD),\n  ORD11:      OPDEF(1, 1, TEXCLASS.ORD),\n  ORD21:      OPDEF(2, 1, TEXCLASS.ORD),\n  ORD02:      OPDEF(0, 2, TEXCLASS.ORD),\n  ORD55:      OPDEF(5, 5, TEXCLASS.ORD),\n  NONE:       OPDEF(0, 0, TEXCLASS.NONE),\n  OP:         OPDEF(1, 2, TEXCLASS.OP, {largeop: true, movablelimits: true, symmetric: true}),\n  OPFIXED:    OPDEF(1, 2, TEXCLASS.OP, {largeop: true, movablelimits: true}),\n  INTEGRAL:   OPDEF(0, 1, TEXCLASS.OP, {largeop: true, symmetric: true}),\n  INTEGRAL2:  OPDEF(1, 2, TEXCLASS.OP, {largeop: true, symmetric: true}),\n  BIN3:       OPDEF(3, 3, TEXCLASS.BIN),\n  BIN4:       OPDEF(4, 4, TEXCLASS.BIN),\n  BIN01:      OPDEF(0, 1, TEXCLASS.BIN),\n  BIN5:       OPDEF(5, 5, TEXCLASS.BIN),\n  TALLBIN:    OPDEF(4, 4, TEXCLASS.BIN, {stretchy: true}),\n  BINOP:      OPDEF(4, 4, TEXCLASS.BIN, {largeop: true, movablelimits: true}),\n  REL:        OPDEF(5, 5, TEXCLASS.REL),\n  REL1:       OPDEF(1, 1, TEXCLASS.REL, {stretchy: true}),\n  REL4:       OPDEF(4, 4, TEXCLASS.REL),\n  RELSTRETCH: OPDEF(5, 5, TEXCLASS.REL, {stretchy: true}),\n  RELACCENT:  OPDEF(5, 5, TEXCLASS.REL, {accent: true}),\n  WIDEREL:    OPDEF(5, 5, TEXCLASS.REL, {accent: true, stretchy: true}),\n  OPEN:       OPDEF(0, 0, TEXCLASS.OPEN, {fence: true, stretchy: true, symmetric: true}),\n  CLOSE:      OPDEF(0, 0, TEXCLASS.CLOSE, {fence: true, stretchy: true, symmetric: true}),\n  INNER:      OPDEF(0, 0, TEXCLASS.INNER),\n  PUNCT:      OPDEF(0, 3, TEXCLASS.PUNCT),\n  ACCENT:     OPDEF(0, 0, TEXCLASS.ORD, {accent: true}),\n  WIDEACCENT: OPDEF(0, 0, TEXCLASS.ORD, {accent: true, stretchy: true})\n};\n\n/**\n *  The default TeX classes for the various unicode blocks, and their names\n */\nexport const RANGES: RangeDef[] = [\n  [0x0020, 0x007F, TEXCLASS.REL, 'mo'], // Basic Latin\n  [0x00A0, 0x00BF, TEXCLASS.ORD, 'mo'], // Latin-1 Supplement symbols\n  [0x00C0, 0x024F, TEXCLASS.ORD, 'mi'], // Latin-1 Supplement, Latin Extended-A, Latin Extended-B\n  [0x02B0, 0x036F, TEXCLASS.ORD, 'mo'], // Spacing modifier letters, Combining Diacritical Marks\n  [0x0370, 0x1A20, TEXCLASS.ORD, 'mi'], // Greek and Coptic (through) Tai Tham\n  [0x1AB0, 0x1AFF, TEXCLASS.ORD, 'mo'], // Combining Diacritical Marks Extended\n  [0x1B00, 0x1DBF, TEXCLASS.ORD, 'mi'], // Balinese (through) Phonetic Extensions Supplement\n  [0x1DC0, 0x1DFF, TEXCLASS.ORD, 'mo'], // Combining Diacritical Marks Supplement\n  [0x1E00, 0x1FFF, TEXCLASS.ORD, 'mi'], // Latin Extended Additional, Greek Extended\n  [0x2000, 0x206F, TEXCLASS.ORD, 'mo'], // General Punctuation\n  [0x2070, 0x209F, TEXCLASS.ORD, 'mo'], // Superscript and Subscripts (through) Combining Diacritical Marks for Symbols\n  [0x2100, 0x214F, TEXCLASS.ORD, 'mi'], // Letterlike Symbols\n  [0x2150, 0x218F, TEXCLASS.ORD, 'mn'], // Number Forms\n  [0x2190, 0x21FF, TEXCLASS.REL, 'mo'], // Arrows\n  [0x2200, 0x22FF, TEXCLASS.BIN, 'mo'], // Mathematical Operators\n  [0x2300, 0x23FF, TEXCLASS.ORD, 'mo'], // Miscellaneous Technical\n  [0x2460, 0x24FF, TEXCLASS.ORD, 'mn'], // Enclosed Alphanumerics\n  [0x2500, 0x27EF, TEXCLASS.ORD, 'mo'], // Box Drawing (though) Miscellaneous Math Symbols-A\n  [0x27F0, 0x27FF, TEXCLASS.REL, 'mo'], // Supplemental Arrows-A\n  [0x2800, 0x28FF, TEXCLASS.ORD, 'mtext'], // Braille Patterns\n  [0x2900, 0x297F, TEXCLASS.REL, 'mo'], // Supplemental Arrows-B\n  [0x2980, 0x29FF, TEXCLASS.ORD, 'mo'], // Miscellaneous Math Symbols-B\n  [0x2A00, 0x2AFF, TEXCLASS.BIN, 'mo'], // Supplemental Math Operators\n  [0x2B00, 0x2B2F, TEXCLASS.ORD, 'mo'], // Miscellaneous Symbols and Arrows\n  [0x2B30, 0x2B4F, TEXCLASS.REL, 'mo'], //   Arrows from above\n  [0x2B50, 0x2BFF, TEXCLASS.ORD, 'mo'], //   Rest of above\n  [0x2C00, 0x2DE0, TEXCLASS.ORD, 'mi'], // Glagolitic (through) Ethipoc Extended\n  [0x2E00, 0x2E7F, TEXCLASS.ORD, 'mo'], // Supplemental Punctuation\n  [0x2E80, 0x2FDF, TEXCLASS.ORD, 'mi', 'normal'], // CJK Radicals Supplement (through) Kangxi Radicals\n  [0x2FF0, 0x303F, TEXCLASS.ORD, 'mo'], // Ideographic Desc. Characters, CJK Symbols and Punctuation\n  [0x3040, 0xA49F, TEXCLASS.ORD, 'mi', 'normal'], // Hiragana (through) Yi Radicals\n  [0xA4D0, 0xA82F, TEXCLASS.ORD, 'mi'], // Lisu (through) Syloti Nagri\n  [0xA830, 0xA83F, TEXCLASS.ORD, 'mn'], // Common Indic Number FormsArabic Presentation Forms-A\n  [0xA840, 0xD7FF, TEXCLASS.ORD, 'mi'], // Phags-pa (though) Hangul Jamo Extended-B\n  [0xF900, 0xFAFF, TEXCLASS.ORD, 'mi', 'normal'], // CJK Compatibility Ideographs\n  [0xFB00, 0xFDFF, TEXCLASS.ORD, 'mi'], // Alphabetic Presentation Forms (though) Arabic Presentation Forms-A\n  [0xFE00, 0xFE6F, TEXCLASS.ORD, 'mo'], // Variation Selector (through) Small Form Variants\n  [0xFE70, 0x100FF, TEXCLASS.ORD, 'mi'], // Arabic Presentation Forms-B (through) Linear B Ideograms\n  [0x10100, 0x1018F, TEXCLASS.ORD, 'mn'], // Aegean Numbers, Ancient Greek Numbers\n  [0x10190, 0x123FF, TEXCLASS.ORD, 'mi', 'normal'], // Ancient Symbols (through) Cuneiform\n  [0x12400, 0x1247F, TEXCLASS.ORD, 'mn'], // Cuneiform Numbers and Punctuation\n  [0x12480, 0x1BC9F, TEXCLASS.ORD, 'mi', 'normal'], // Early Dynastic Cuneiform (through) Duployan\n  [0x1BCA0, 0x1D25F, TEXCLASS.ORD, 'mo'], // Shorthand Format Controls (through) TaiXuan Jing Symbols\n  [0x1D360, 0x1D37F, TEXCLASS.ORD, 'mn'], // Counting Rod Numerals\n  [0x1D400, 0x1D7CD, TEXCLASS.ORD, 'mi'], // Math Alphanumeric Symbols\n  [0x1D7CE, 0x1D7FF, TEXCLASS.ORD, 'mn'], //   Numerals from above\n  [0x1DF00, 0x1F7FF, TEXCLASS.ORD, 'mo'], // Mahjong Tiles (through) Geometric Shapes Extended\n  [0x1F800, 0x1F8FF, TEXCLASS.REL, 'mo'], // Supplemental Arrows-C\n  [0x1F900, 0x1F9FF, TEXCLASS.ORD, 'mo'], // Supplemental Symbols and Pictographs\n  [0x20000, 0x2FA1F, TEXCLASS.ORD, 'mi', 'normnal'], // CJK Unified Ideographs Ext. B (through) CJK Sompatibility Ideographs Supp.\n];\n\n/**\n * Get the Unicode range for the first character of a string\n *\n * @param {string} text      The character to check\n * @return {RangeDef|null}   The range containing that character, or null\n */\nexport function getRange(text: string): RangeDef | null {\n  const n = text.codePointAt(0);\n  for (const range of RANGES) {\n    if (n <= range[1]) {\n      if (n >= range[0]) {\n        return range;\n      }\n      break;\n    }\n  }\n  return null;\n}\n\n/**\n * The default MathML spacing for the various TeX classes.\n */\nexport const MMLSPACING = [\n  [0, 0],  // ORD\n  [1, 2],  // OP\n  [3, 3],  // BIN\n  [4, 4],  // REL\n  [0, 0],  // OPEN\n  [0, 0],  // CLOSE\n  [0, 3]   // PUNCT\n];\n\n/**\n *  The operator dictionary, with sections for the three forms:  prefix, postfix, and infix\n */\nexport const OPTABLE: {[form: string]: OperatorList} = {\n  prefix: {\n    '(': MO.OPEN,            // left parenthesis\n    '+': MO.BIN01,           // plus sign\n    '-': MO.BIN01,           // hyphen-minus\n    '[': MO.OPEN,            // left square bracket\n    '{': MO.OPEN,            // left curly bracket\n    '|': MO.OPEN,            // vertical line\n    '||': [0, 0, TEXCLASS.BIN, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: ||\n    '|||': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: |||\n    '\\u00AC': MO.ORD21,      // not sign\n    '\\u00B1': MO.BIN01,      // plus-minus sign\n    '\\u2016': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true}], // double vertical line\n    '\\u2018': [0, 0, TEXCLASS.OPEN, {fence: true}], // left single quotation mark\n    '\\u201C': [0, 0, TEXCLASS.OPEN, {fence: true}], // left double quotation mark\n    '\\u2145': MO.ORD21,      // double-struck italic capital d\n    '\\u2146': OPDEF(2, 0, TEXCLASS.ORD),  // double-struck italic small d\n    '\\u2200': MO.ORD21,      // for all\n    '\\u2202': MO.ORD21,      // partial differential\n    '\\u2203': MO.ORD21,      // there exists\n    '\\u2204': MO.ORD21,      // there does not exist\n    '\\u2207': MO.ORD21,      // nabla\n    '\\u220F': MO.OP,         // n-ary product\n    '\\u2210': MO.OP,         // n-ary coproduct\n    '\\u2211': MO.OP,         // n-ary summation\n    '\\u2212': MO.BIN01,      // minus sign\n    '\\u2213': MO.BIN01,      // minus-or-plus sign\n    '\\u221A': [1, 1, TEXCLASS.ORD, {stretchy: true}], // square root\n    '\\u221B': MO.ORD11,      // cube root\n    '\\u221C': MO.ORD11,      // fourth root\n    '\\u2220': MO.ORD,        // angle\n    '\\u2221': MO.ORD,        // measured angle\n    '\\u2222': MO.ORD,        // spherical angle\n    '\\u222B': MO.INTEGRAL,   // integral\n    '\\u222C': MO.INTEGRAL,   // double integral\n    '\\u222D': MO.INTEGRAL,   // triple integral\n    '\\u222E': MO.INTEGRAL,   // contour integral\n    '\\u222F': MO.INTEGRAL,   // surface integral\n    '\\u2230': MO.INTEGRAL,   // volume integral\n    '\\u2231': MO.INTEGRAL,   // clockwise integral\n    '\\u2232': MO.INTEGRAL,   // clockwise contour integral\n    '\\u2233': MO.INTEGRAL,   // anticlockwise contour integral\n    '\\u22C0': MO.OP,         // n-ary logical and\n    '\\u22C1': MO.OP,         // n-ary logical or\n    '\\u22C2': MO.OP,         // n-ary intersection\n    '\\u22C3': MO.OP,         // n-ary union\n    '\\u2308': MO.OPEN,       // left ceiling\n    '\\u230A': MO.OPEN,       // left floor\n    '\\u2329': MO.OPEN,       // left-pointing angle bracket\n    '\\u2772': MO.OPEN,       // light left tortoise shell bracket ornament\n    '\\u27E6': MO.OPEN,       // mathematical left white square bracket\n    '\\u27E8': MO.OPEN,       // mathematical left angle bracket\n    '\\u27EA': MO.OPEN,       // mathematical left double angle bracket\n    '\\u27EC': MO.OPEN,       // mathematical left white tortoise shell bracket\n    '\\u27EE': MO.OPEN,       // mathematical left flattened parenthesis\n    '\\u2980': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true}], // triple vertical bar delimiter\n    '\\u2983': MO.OPEN,       // left white curly bracket\n    '\\u2985': MO.OPEN,       // left white parenthesis\n    '\\u2987': MO.OPEN,       // z notation left image bracket\n    '\\u2989': MO.OPEN,       // z notation left binding bracket\n    '\\u298B': MO.OPEN,       // left square bracket with underbar\n    '\\u298D': MO.OPEN,       // left square bracket with tick in top corner\n    '\\u298F': MO.OPEN,       // left square bracket with tick in bottom corner\n    '\\u2991': MO.OPEN,       // left angle bracket with dot\n    '\\u2993': MO.OPEN,       // left arc less-than bracket\n    '\\u2995': MO.OPEN,       // double left arc greater-than bracket\n    '\\u2997': MO.OPEN,       // left black tortoise shell bracket\n    '\\u29FC': MO.OPEN,       // left-pointing curved angle bracket\n    '\\u2A00': MO.OP,         // n-ary circled dot operator\n    '\\u2A01': MO.OP,         // n-ary circled plus operator\n    '\\u2A02': MO.OP,         // n-ary circled times operator\n    '\\u2A03': MO.OP,         // n-ary union operator with dot\n    '\\u2A04': MO.OP,         // n-ary union operator with plus\n    '\\u2A05': MO.OP,         // n-ary square intersection operator\n    '\\u2A06': MO.OP,         // n-ary square union operator\n    '\\u2A07': MO.OP,         // two logical and operator\n    '\\u2A08': MO.OP,         // two logical or operator\n    '\\u2A09': MO.OP,         // n-ary times operator\n    '\\u2A0A': MO.OP,         // modulo two sum\n    '\\u2A0B': MO.INTEGRAL2,  // summation with integral\n    '\\u2A0C': MO.INTEGRAL,   // quadruple integral operator\n    '\\u2A0D': MO.INTEGRAL2,  // finite part integral\n    '\\u2A0E': MO.INTEGRAL2,  // integral with double stroke\n    '\\u2A0F': MO.INTEGRAL2,  // integral average with slash\n    '\\u2A10': MO.OP,         // circulation function\n    '\\u2A11': MO.OP,         // anticlockwise integration\n    '\\u2A12': MO.OP,         // line integration with rectangular path around pole\n    '\\u2A13': MO.OP,         // line integration with semicircular path around pole\n    '\\u2A14': MO.OP,         // line integration not including the pole\n    '\\u2A15': MO.INTEGRAL2,  // integral around a point operator\n    '\\u2A16': MO.INTEGRAL2,  // quaternion integral operator\n    '\\u2A17': MO.INTEGRAL2,  // integral with leftwards arrow with hook\n    '\\u2A18': MO.INTEGRAL2,  // integral with times sign\n    '\\u2A19': MO.INTEGRAL2,  // integral with intersection\n    '\\u2A1A': MO.INTEGRAL2,  // integral with union\n    '\\u2A1B': MO.INTEGRAL2,  // integral with overbar\n    '\\u2A1C': MO.INTEGRAL2,  // integral with underbar\n    '\\u2AFC': MO.OP,         // large triple vertical bar operator\n    '\\u2AFF': MO.OP,         // n-ary white vertical bar\n  },\n  postfix: {\n    '!!': OPDEF(1, 0),       // multiple character operator: !!\n    '!': [1, 0, TEXCLASS.CLOSE, null], // exclamation mark\n    '\"': MO.ACCENT,          // quotation mark\n    '&': MO.ORD,             // ampersand\n    ')': MO.CLOSE,           // right parenthesis\n    '++': OPDEF(0, 0),       // multiple character operator: ++\n    '--': OPDEF(0, 0),       // multiple character operator: --\n    '..': OPDEF(0, 0),       // multiple character operator: ..\n    '...': MO.ORD,           // multiple character operator: ...\n    '\\'': MO.ACCENT,         // apostrophe\n    ']': MO.CLOSE,           // right square bracket\n    '^': MO.WIDEACCENT,      // circumflex accent\n    '_': MO.WIDEACCENT,      // low line\n    '`': MO.ACCENT,          // grave accent\n    '|': MO.CLOSE,           // vertical line\n    '}': MO.CLOSE,           // right curly bracket\n    '~': MO.WIDEACCENT,      // tilde\n    '||': [0, 0, TEXCLASS.BIN, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: ||\n    '|||': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: |||\n    '\\u00A8': MO.ACCENT,     // diaeresis\n    '\\u00AA': MO.ACCENT,     // feminie ordinal indicator\n    '\\u00AF': MO.WIDEACCENT, // macron\n    '\\u00B0': MO.ORD,        // degree sign\n    '\\u00B2': MO.ACCENT,     // superscript 2\n    '\\u00B3': MO.ACCENT,     // superscript 3\n    '\\u00B4': MO.ACCENT,     // acute accent\n    '\\u00B8': MO.ACCENT,     // cedilla\n    '\\u00B9': MO.ACCENT,     // superscript 1\n    '\\u00BA': MO.ACCENT,     // masculine ordinal indicator\n    '\\u02C6': MO.WIDEACCENT, // modifier letter circumflex accent\n    '\\u02C7': MO.WIDEACCENT, // caron\n    '\\u02C9': MO.WIDEACCENT, // modifier letter macron\n    '\\u02CA': MO.ACCENT,     // modifier letter acute accent\n    '\\u02CB': MO.ACCENT,     // modifier letter grave accent\n    '\\u02CD': MO.WIDEACCENT, // modifier letter low macron\n    '\\u02D8': MO.ACCENT,     // breve\n    '\\u02D9': MO.ACCENT,     // dot above\n    '\\u02DA': MO.ACCENT,     // ring above\n    '\\u02DC': MO.WIDEACCENT, // small tilde\n    '\\u02DD': MO.ACCENT,     // double acute accent\n    '\\u02F7': MO.WIDEACCENT, // modifier letter low tilde\n    '\\u0302': MO.WIDEACCENT, // combining circumflex accent\n    '\\u0311': MO.ACCENT,     // combining inverted breve\n    '\\u03F6': MO.REL,        // greek reversed lunate epsilon symbol\n    '\\u2016': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true}], // double vertical line\n    '\\u2019': [0, 0, TEXCLASS.CLOSE, {fence: true}], // right single quotation mark\n    '\\u201A': MO.ACCENT,     // single low-9 quotation mark\n    '\\u201B': MO.ACCENT,     // single high-reversed-9 quotation mark\n    '\\u201D': [0, 0, TEXCLASS.CLOSE, {fence: true}],  // right double quotation mark\n    '\\u201E': MO.ACCENT,     // double low-9 quotation mark\n    '\\u201F': MO.ACCENT,     // double high-reversed-9 quotation mark\n    '\\u2032': MO.ORD,        // prime\n    '\\u2033': MO.ACCENT,     // double prime\n    '\\u2034': MO.ACCENT,     // triple prime\n    '\\u2035': MO.ACCENT,     // reversed prime\n    '\\u2036': MO.ACCENT,     // reversed double prime\n    '\\u2037': MO.ACCENT,     // reversed triple prime\n    '\\u203E': MO.WIDEACCENT, // overline\n    '\\u2057': MO.ACCENT,     // quadruple prime\n    '\\u20DB': MO.ACCENT,     // combining three dots above\n    '\\u20DC': MO.ACCENT,     // combining four dots above\n    '\\u2309': MO.CLOSE,      // right ceiling\n    '\\u230B': MO.CLOSE,      // right floor\n    '\\u232A': MO.CLOSE,      // right-pointing angle bracket\n    '\\u23B4': MO.WIDEACCENT, // top square bracket\n    '\\u23B5': MO.WIDEACCENT, // bottom square bracket\n    '\\u23DC': MO.WIDEACCENT, // top parenthesis\n    '\\u23DD': MO.WIDEACCENT, // bottom parenthesis\n    '\\u23DE': MO.WIDEACCENT, // top curly bracket\n    '\\u23DF': MO.WIDEACCENT, // bottom curly bracket\n    '\\u23E0': MO.WIDEACCENT, // top tortoise shell bracket\n    '\\u23E1': MO.WIDEACCENT, // bottom tortoise shell bracket\n    '\\u25A0': MO.BIN3,       // black square\n    '\\u25A1': MO.BIN3,       // white square\n    '\\u25AA': MO.BIN3,       // black small square\n    '\\u25AB': MO.BIN3,       // white small square\n    '\\u25AD': MO.BIN3,       // white rectangle\n    '\\u25AE': MO.BIN3,       // black vertical rectangle\n    '\\u25AF': MO.BIN3,       // white vertical rectangle\n    '\\u25B0': MO.BIN3,       // black parallelogram\n    '\\u25B1': MO.BIN3,       // white parallelogram\n    '\\u25B2': MO.BIN4,       // black up-pointing triangle\n    '\\u25B4': MO.BIN4,       // black up-pointing small triangle\n    '\\u25B6': MO.BIN4,       // black right-pointing triangle\n    '\\u25B7': MO.BIN4,       // white right-pointing triangle\n    '\\u25B8': MO.BIN4,       // black right-pointing small triangle\n    '\\u25BC': MO.BIN4,       // black down-pointing triangle\n    '\\u25BE': MO.BIN4,       // black down-pointing small triangle\n    '\\u25C0': MO.BIN4,       // black left-pointing triangle\n    '\\u25C1': MO.BIN4,       // white left-pointing triangle\n    '\\u25C2': MO.BIN4,       // black left-pointing small triangle\n    '\\u25C4': MO.BIN4,       // black left-pointing pointer\n    '\\u25C5': MO.BIN4,       // white left-pointing pointer\n    '\\u25C6': MO.BIN4,       // black diamond\n    '\\u25C7': MO.BIN4,       // white diamond\n    '\\u25C8': MO.BIN4,       // white diamond containing black small diamond\n    '\\u25C9': MO.BIN4,       // fisheye\n    '\\u25CC': MO.BIN4,       // dotted circle\n    '\\u25CD': MO.BIN4,       // circle with vertical fill\n    '\\u25CE': MO.BIN4,       // bullseye\n    '\\u25CF': MO.BIN4,       // black circle\n    '\\u25D6': MO.BIN4,       // left half black circle\n    '\\u25D7': MO.BIN4,       // right half black circle\n    '\\u25E6': MO.BIN4,       // white bullet\n    '\\u266D': MO.ORD02,      // music flat sign\n    '\\u266E': MO.ORD02,      // music natural sign\n    '\\u266F': MO.ORD02,      // music sharp sign\n    '\\u2773': MO.CLOSE,      // light right tortoise shell bracket ornament\n    '\\u27E7': MO.CLOSE,      // mathematical right white square bracket\n    '\\u27E9': MO.CLOSE,      // mathematical right angle bracket\n    '\\u27EB': MO.CLOSE,      // mathematical right double angle bracket\n    '\\u27ED': MO.CLOSE,      // mathematical right white tortoise shell bracket\n    '\\u27EF': MO.CLOSE,      // mathematical right flattened parenthesis\n    '\\u2980': [0, 0, TEXCLASS.ORD, {fence: true, stretchy: true}], // triple vertical bar delimiter\n    '\\u2984': MO.CLOSE,      // right white curly bracket\n    '\\u2986': MO.CLOSE,      // right white parenthesis\n    '\\u2988': MO.CLOSE,      // z notation right image bracket\n    '\\u298A': MO.CLOSE,      // z notation right binding bracket\n    '\\u298C': MO.CLOSE,      // right square bracket with underbar\n    '\\u298E': MO.CLOSE,      // right square bracket with tick in bottom corner\n    '\\u2990': MO.CLOSE,      // right square bracket with tick in top corner\n    '\\u2992': MO.CLOSE,      // right angle bracket with dot\n    '\\u2994': MO.CLOSE,      // right arc greater-than bracket\n    '\\u2996': MO.CLOSE,      // double right arc less-than bracket\n    '\\u2998': MO.CLOSE,      // right black tortoise shell bracket\n    '\\u29FD': MO.CLOSE,      // right-pointing curved angle bracket\n  },\n  infix: {\n    '!=': MO.BIN4,           // multiple character operator: !=\n    '#': MO.ORD,             // #\n    '$': MO.ORD,             // $\n    '%': [3, 3, TEXCLASS.ORD, null], // percent sign\n    '&&': MO.BIN4,           // multiple character operator: &&\n    '': MO.ORD,              // empty <mo>\n    '*': MO.BIN3,            // asterisk\n    '**': OPDEF(1, 1),       // multiple character operator: **\n    '*=': MO.BIN4,           // multiple character operator: *=\n    '+': MO.BIN4,            // plus sign\n    '+=': MO.BIN4,           // multiple character operator: +=\n    ',': [0, 3, TEXCLASS.PUNCT, {linebreakstyle: 'after', separator: true}], // comma\n    '-': MO.BIN4,            // hyphen-minus\n    '-=': MO.BIN4,           // multiple character operator: -=\n    '->': MO.BIN5,           // multiple character operator: ->\n    '.': [0, 3, TEXCLASS.PUNCT, {separator: true}], // \\ldotp\n    '/': MO.ORD11,           // solidus\n    '//': OPDEF(1, 1),       // multiple character operator: //\n    '/=': MO.BIN4,           // multiple character operator: /=\n    ':': [1, 2, TEXCLASS.REL, null], // colon\n    ':=': MO.BIN4,           // multiple character operator: :=\n    ';': [0, 3, TEXCLASS.PUNCT, {linebreakstyle: 'after', separator: true}], // semicolon\n    '<': MO.REL,             // less-than sign\n    '<=': MO.BIN5,           // multiple character operator: <=\n    '<>': OPDEF(1, 1),       // multiple character operator: <>\n    '=': MO.REL,             // equals sign\n    '==': MO.BIN4,           // multiple character operator: ==\n    '>': MO.REL,             // greater-than sign\n    '>=': MO.BIN5,           // multiple character operator: >=\n    '?': [1, 1, TEXCLASS.CLOSE, null], // question mark\n    '@': MO.ORD11,           // commercial at\n    '\\\\': MO.ORD,            // reverse solidus\n    '^': MO.ORD11,           // circumflex accent\n    '_': MO.ORD11,           // low line\n    '|': [2, 2, TEXCLASS.ORD, {fence: true, stretchy: true, symmetric: true}], // vertical line\n    '||': [2, 2, TEXCLASS.BIN, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: ||\n    '|||': [2, 2, TEXCLASS.ORD, {fence: true, stretchy: true, symmetric: true}], // multiple character operator: |||\n    '\\u00B1': MO.BIN4,       // plus-minus sign\n    '\\u00B7': MO.BIN4,       // middle dot\n    '\\u00D7': MO.BIN4,       // multiplication sign\n    '\\u00F7': MO.BIN4,       // division sign\n    '\\u02B9': MO.ORD,        // prime\n    '\\u0300': MO.ACCENT,     // \\grave\n    '\\u0301': MO.ACCENT,     // \\acute\n    '\\u0303': MO.WIDEACCENT, // \\tilde\n    '\\u0304': MO.ACCENT,     // \\bar\n    '\\u0306': MO.ACCENT,     // \\breve\n    '\\u0307': MO.ACCENT,     // \\dot\n    '\\u0308': MO.ACCENT,     // \\ddot\n    '\\u030C': MO.ACCENT,     // \\check\n    '\\u0332': MO.WIDEACCENT, // horizontal line\n    '\\u0338': MO.REL4,       // \\not\n    '\\u2015': [0, 0, TEXCLASS.ORD, {stretchy: true}], // horizontal line\n    '\\u2017': [0, 0, TEXCLASS.ORD, {stretchy: true}], // horizontal line\n    '\\u2020': MO.BIN3,       // \\dagger\n    '\\u2021': MO.BIN3,       // \\ddagger\n    '\\u2022': MO.BIN4,       // bullet\n    '\\u2026': MO.INNER,      // horizontal ellipsis\n    '\\u2043': MO.BIN4,       // hyphen bullet\n    '\\u2044': MO.TALLBIN,    // fraction slash\n    '\\u2061': MO.NONE,       // function application\n    '\\u2062': MO.NONE,       // invisible times\n    '\\u2063': [0, 0, TEXCLASS.NONE, {linebreakstyle: 'after', separator: true}], // invisible separator\n    '\\u2064': MO.NONE,       // invisible plus\n    '\\u20D7': MO.ACCENT,     // \\vec\n    '\\u2111': MO.ORD,        // \\Im\n    '\\u2113': MO.ORD,        // \\ell\n    '\\u2118': MO.ORD,        // \\wp\n    '\\u211C': MO.ORD,        // \\Re\n    '\\u2190': MO.WIDEREL,    // leftwards arrow\n    '\\u2191': MO.RELSTRETCH, // upwards arrow\n    '\\u2192': MO.WIDEREL,    // rightwards arrow\n    '\\u2193': MO.RELSTRETCH, // downwards arrow\n    '\\u2194': MO.WIDEREL,    // left right arrow\n    '\\u2195': MO.RELSTRETCH, // up down arrow\n    '\\u2196': MO.RELSTRETCH, // north west arrow\n    '\\u2197': MO.RELSTRETCH, // north east arrow\n    '\\u2198': MO.RELSTRETCH, // south east arrow\n    '\\u2199': MO.RELSTRETCH, // south west arrow\n    '\\u219A': MO.RELACCENT,  // leftwards arrow with stroke\n    '\\u219B': MO.RELACCENT,  // rightwards arrow with stroke\n    '\\u219C': MO.WIDEREL,    // leftwards wave arrow\n    '\\u219D': MO.WIDEREL,    // rightwards wave arrow\n    '\\u219E': MO.WIDEREL,    // leftwards two headed arrow\n    '\\u219F': MO.WIDEREL,    // upwards two headed arrow\n    '\\u21A0': MO.WIDEREL,    // rightwards two headed arrow\n    '\\u21A1': MO.RELSTRETCH, // downwards two headed arrow\n    '\\u21A2': MO.WIDEREL,    // leftwards arrow with tail\n    '\\u21A3': MO.WIDEREL,    // rightwards arrow with tail\n    '\\u21A4': MO.WIDEREL,    // leftwards arrow from bar\n    '\\u21A5': MO.RELSTRETCH, // upwards arrow from bar\n    '\\u21A6': MO.WIDEREL,    // rightwards arrow from bar\n    '\\u21A7': MO.RELSTRETCH, // downwards arrow from bar\n    '\\u21A8': MO.RELSTRETCH, // up down arrow with base\n    '\\u21A9': MO.WIDEREL,    // leftwards arrow with hook\n    '\\u21AA': MO.WIDEREL,    // rightwards arrow with hook\n    '\\u21AB': MO.WIDEREL,    // leftwards arrow with loop\n    '\\u21AC': MO.WIDEREL,    // rightwards arrow with loop\n    '\\u21AD': MO.WIDEREL,    // left right wave arrow\n    '\\u21AE': MO.RELACCENT,  // left right arrow with stroke\n    '\\u21AF': MO.RELSTRETCH, // downwards zigzag arrow\n    '\\u21B0': MO.RELSTRETCH, // upwards arrow with tip leftwards\n    '\\u21B1': MO.RELSTRETCH, // upwards arrow with tip rightwards\n    '\\u21B2': MO.RELSTRETCH, // downwards arrow with tip leftwards\n    '\\u21B3': MO.RELSTRETCH, // downwards arrow with tip rightwards\n    '\\u21B4': MO.RELSTRETCH, // rightwards arrow with corner downwards\n    '\\u21B5': MO.RELSTRETCH, // downwards arrow with corner leftwards\n    '\\u21B6': MO.RELACCENT,  // anticlockwise top semicircle arrow\n    '\\u21B7': MO.RELACCENT,  // clockwise top semicircle arrow\n    '\\u21B8': MO.REL,        // north west arrow to long bar\n    '\\u21B9': MO.WIDEREL,    // leftwards arrow to bar over rightwards arrow to bar\n    '\\u21BA': MO.REL,        // anticlockwise open circle arrow\n    '\\u21BB': MO.REL,        // clockwise open circle arrow\n    '\\u21BC': MO.WIDEREL,    // leftwards harpoon with barb upwards\n    '\\u21BD': MO.WIDEREL,    // leftwards harpoon with barb downwards\n    '\\u21BE': MO.RELSTRETCH, // upwards harpoon with barb rightwards\n    '\\u21BF': MO.RELSTRETCH, // upwards harpoon with barb leftwards\n    '\\u21C0': MO.WIDEREL,    // rightwards harpoon with barb upwards\n    '\\u21C1': MO.WIDEREL,    // rightwards harpoon with barb downwards\n    '\\u21C2': MO.RELSTRETCH, // downwards harpoon with barb rightwards\n    '\\u21C3': MO.RELSTRETCH, // downwards harpoon with barb leftwards\n    '\\u21C4': MO.WIDEREL,    // rightwards arrow over leftwards arrow\n    '\\u21C5': MO.RELSTRETCH, // upwards arrow leftwards of downwards arrow\n    '\\u21C6': MO.WIDEREL,    // leftwards arrow over rightwards arrow\n    '\\u21C7': MO.WIDEREL,    // leftwards paired arrows\n    '\\u21C8': MO.RELSTRETCH, // upwards paired arrows\n    '\\u21C9': MO.WIDEREL,    // rightwards paired arrows\n    '\\u21CA': MO.RELSTRETCH, // downwards paired arrows\n    '\\u21CB': MO.WIDEREL,    // leftwards harpoon over rightwards harpoon\n    '\\u21CC': MO.WIDEREL,    // rightwards harpoon over leftwards harpoon\n    '\\u21CD': MO.RELACCENT,  // leftwards double arrow with stroke\n    '\\u21CE': MO.RELACCENT,  // left right double arrow with stroke\n    '\\u21CF': MO.RELACCENT,  // rightwards double arrow with stroke\n    '\\u21D0': MO.WIDEREL,    // leftwards double arrow\n    '\\u21D1': MO.RELSTRETCH, // upwards double arrow\n    '\\u21D2': MO.WIDEREL,    // rightwards double arrow\n    '\\u21D3': MO.RELSTRETCH, // downwards double arrow\n    '\\u21D4': MO.WIDEREL,    // left right double arrow\n    '\\u21D5': MO.RELSTRETCH, // up down double arrow\n    '\\u21D6': MO.RELSTRETCH, // north west double arrow\n    '\\u21D7': MO.RELSTRETCH, // north east double arrow\n    '\\u21D8': MO.RELSTRETCH, // south east double arrow\n    '\\u21D9': MO.RELSTRETCH, // south west double arrow\n    '\\u21DA': MO.WIDEREL,    // leftwards triple arrow\n    '\\u21DB': MO.WIDEREL,    // rightwards triple arrow\n    '\\u21DC': MO.WIDEREL,    // leftwards squiggle arrow\n    '\\u21DD': MO.WIDEREL,    // rightwards squiggle arrow\n    '\\u21DE': MO.REL,        // upwards arrow with double stroke\n    '\\u21DF': MO.REL,        // downwards arrow with double stroke\n    '\\u21E0': MO.WIDEREL,    // leftwards dashed arrow\n    '\\u21E1': MO.RELSTRETCH, // upwards dashed arrow\n    '\\u21E2': MO.WIDEREL,    // rightwards dashed arrow\n    '\\u21E3': MO.RELSTRETCH, // downwards dashed arrow\n    '\\u21E4': MO.WIDEREL,    // leftwards arrow to bar\n    '\\u21E5': MO.WIDEREL,    // rightwards arrow to bar\n    '\\u21E6': MO.WIDEREL,    // leftwards white arrow\n    '\\u21E7': MO.RELSTRETCH, // upwards white arrow\n    '\\u21E8': MO.WIDEREL,    // rightwards white arrow\n    '\\u21E9': MO.RELSTRETCH, // downwards white arrow\n    '\\u21EA': MO.RELSTRETCH, // upwards white arrow from bar\n    '\\u21EB': MO.RELSTRETCH, // upwards white arrow on pedestal\n    '\\u21EC': MO.RELSTRETCH, // upwards white arrow on pedestal with horizontal bar\n    '\\u21ED': MO.RELSTRETCH, // upwards white arrow on pedestal with vertical bar\n    '\\u21EE': MO.RELSTRETCH, // upwards white double arrow\n    '\\u21EF': MO.RELSTRETCH, // upwards white double arrow on pedestal\n    '\\u21F0': MO.WIDEREL,    // rightwards white arrow from wall\n    '\\u21F1': MO.REL,        // north west arrow to corner\n    '\\u21F2': MO.REL,        // south east arrow to corner\n    '\\u21F3': MO.RELSTRETCH, // up down white arrow\n    '\\u21F4': MO.RELACCENT,  // right arrow with small circle\n    '\\u21F5': MO.RELSTRETCH, // downwards arrow leftwards of upwards arrow\n    '\\u21F6': MO.WIDEREL,    // three rightwards arrows\n    '\\u21F7': MO.RELACCENT,  // leftwards arrow with vertical stroke\n    '\\u21F8': MO.RELACCENT,  // rightwards arrow with vertical stroke\n    '\\u21F9': MO.RELACCENT,  // left right arrow with vertical stroke\n    '\\u21FA': MO.RELACCENT,  // leftwards arrow with double vertical stroke\n    '\\u21FB': MO.RELACCENT,  // rightwards arrow with double vertical stroke\n    '\\u21FC': MO.RELACCENT,  // left right arrow with double vertical stroke\n    '\\u21FD': MO.WIDEREL,    // leftwards open-headed arrow\n    '\\u21FE': MO.WIDEREL,    // rightwards open-headed arrow\n    '\\u21FF': MO.WIDEREL,    // left right open-headed arrow\n    '\\u2201': OPDEF(1, 2, TEXCLASS.ORD), // complement\n    '\\u2205': MO.ORD,        // \\emptyset\n    '\\u2206': MO.BIN3,       // increment\n    '\\u2208': MO.REL,        // element of\n    '\\u2209': MO.REL,        // not an element of\n    '\\u220A': MO.REL,        // small element of\n    '\\u220B': MO.REL,        // contains as member\n    '\\u220C': MO.REL,        // does not contain as member\n    '\\u220D': MO.REL,        // small contains as member\n    '\\u220E': MO.BIN3,       // end of proof\n    '\\u2212': MO.BIN4,       // minus sign\n    '\\u2213': MO.BIN4,       // minus-or-plus sign\n    '\\u2214': MO.BIN4,       // dot plus\n    '\\u2215': MO.TALLBIN,    // division slash\n    '\\u2216': MO.BIN4,       // set minus\n    '\\u2217': MO.BIN4,       // asterisk operator\n    '\\u2218': MO.BIN4,       // ring operator\n    '\\u2219': MO.BIN4,       // bullet operator\n    '\\u221D': MO.REL,        // proportional to\n    '\\u221E': MO.ORD,        // \\infty\n    '\\u221F': MO.REL,        // right angle\n    '\\u2223': MO.REL,        // divides\n    '\\u2224': MO.REL,        // does not divide\n    '\\u2225': MO.REL,        // parallel to\n    '\\u2226': MO.REL,        // not parallel to\n    '\\u2227': MO.BIN4,       // logical and\n    '\\u2228': MO.BIN4,       // logical or\n    '\\u2229': MO.BIN4,       // intersection\n    '\\u222A': MO.BIN4,       // union\n    '\\u2234': MO.REL,        // therefore\n    '\\u2235': MO.REL,        // because\n    '\\u2236': MO.REL,        // ratio\n    '\\u2237': MO.REL,        // proportion\n    '\\u2238': MO.BIN4,       // dot minus\n    '\\u2239': MO.REL,        // excess\n    '\\u223A': MO.BIN4,       // geometric proportion\n    '\\u223B': MO.REL,        // homothetic\n    '\\u223C': MO.REL,        // tilde operator\n    '\\u223D': MO.REL,        // reversed tilde\n    '\\u223D\\u0331': MO.BIN3, // reversed tilde with underline\n    '\\u223E': MO.REL,        // inverted lazy s\n    '\\u223F': MO.BIN3,       // sine wave\n    '\\u2240': MO.BIN4,       // wreath product\n    '\\u2241': MO.REL,        // not tilde\n    '\\u2242': MO.REL,        // minus tilde\n    '\\u2242\\u0338': MO.REL,  // minus tilde with slash\n    '\\u2243': MO.REL,        // asymptotically equal to\n    '\\u2244': MO.REL,        // not asymptotically equal to\n    '\\u2245': MO.REL,        // approximately equal to\n    '\\u2246': MO.REL,        // approximately but not actually equal to\n    '\\u2247': MO.REL,        // neither approximately nor actually equal to\n    '\\u2248': MO.REL,        // almost equal to\n    '\\u2249': MO.REL,        // not almost equal to\n    '\\u224A': MO.REL,        // almost equal or equal to\n    '\\u224B': MO.REL,        // triple tilde\n    '\\u224C': MO.REL,        // all equal to\n    '\\u224D': MO.REL,        // equivalent to\n    '\\u224E': MO.REL,        // geometrically equivalent to\n    '\\u224E\\u0338': MO.REL,  // geometrically equivalent to with slash\n    '\\u224F': MO.REL,        // difference between\n    '\\u224F\\u0338': MO.REL,  // difference between with slash\n    '\\u2250': MO.REL,        // approaches the limit\n    '\\u2251': MO.REL,        // geometrically equal to\n    '\\u2252': MO.REL,        // approximately equal to or the image of\n    '\\u2253': MO.REL,        // image of or approximately equal to\n    '\\u2254': MO.REL,        // colon equals\n    '\\u2255': MO.REL,        // equals colon\n    '\\u2256': MO.REL,        // ring in equal to\n    '\\u2257': MO.REL,        // ring equal to\n    '\\u2258': MO.REL,        // corresponds to\n    '\\u2259': MO.REL,        // estimates\n    '\\u225A': MO.REL,        // equiangular to\n    '\\u225B': MO.REL,        // star equals\n    '\\u225C': MO.REL,        // delta equal to\n    '\\u225D': MO.REL,        // equal to by definition\n    '\\u225E': MO.REL,        // measured by\n    '\\u225F': MO.REL,        // questioned equal to\n    '\\u2260': MO.REL,        // not equal to\n    '\\u2261': MO.REL,        // identical to\n    '\\u2262': MO.REL,        // not identical to\n    '\\u2263': MO.REL,        // strictly equivalent to\n    '\\u2264': MO.REL,        // less-than or equal to\n    '\\u2265': MO.REL,        // greater-than or equal to\n    '\\u2266': MO.REL,        // less-than over equal to\n    '\\u2266\\u0338': MO.REL,  // less-than over equal to with slash\n    '\\u2267': MO.REL,        // greater-than over equal to\n    '\\u2268': MO.REL,        // less-than but not equal to\n    '\\u2269': MO.REL,        // greater-than but not equal to\n    '\\u226A': MO.REL,        // much less-than\n    '\\u226A\\u0338': MO.REL,  // much less than with slash\n    '\\u226B': MO.REL,        // much greater-than\n    '\\u226B\\u0338': MO.REL,  // much greater than with slash\n    '\\u226C': MO.REL,        // between\n    '\\u226D': MO.REL,        // not equivalent to\n    '\\u226E': MO.REL,        // not less-than\n    '\\u226F': MO.REL,        // not greater-than\n    '\\u2270': MO.REL,        // neither less-than nor equal to\n    '\\u2271': MO.REL,        // neither greater-than nor equal to\n    '\\u2272': MO.REL,        // less-than or equivalent to\n    '\\u2273': MO.REL,        // greater-than or equivalent to\n    '\\u2274': MO.REL,        // neither less-than nor equivalent to\n    '\\u2275': MO.REL,        // neither greater-than nor equivalent to\n    '\\u2276': MO.REL,        // less-than or greater-than\n    '\\u2277': MO.REL,        // greater-than or less-than\n    '\\u2278': MO.REL,        // neither less-than nor greater-than\n    '\\u2279': MO.REL,        // neither greater-than nor less-than\n    '\\u227A': MO.REL,        // precedes\n    '\\u227B': MO.REL,        // succeeds\n    '\\u227C': MO.REL,        // precedes or equal to\n    '\\u227D': MO.REL,        // succeeds or equal to\n    '\\u227E': MO.REL,        // precedes or equivalent to\n    '\\u227F': MO.REL,        // succeeds or equivalent to\n    '\\u227F\\u0338': MO.REL,  // succeeds or equivalent to with slash\n    '\\u2280': MO.REL,        // does not precede\n    '\\u2281': MO.REL,        // does not succeed\n    '\\u2282': MO.REL,        // subset of\n    '\\u2282\\u20D2': MO.REL,  // subset of with vertical line\n    '\\u2283': MO.REL,        // superset of\n    '\\u2283\\u20D2': MO.REL,  // superset of with vertical line\n    '\\u2284': MO.REL,        // not a subset of\n    '\\u2285': MO.REL,        // not a superset of\n    '\\u2286': MO.REL,        // subset of or equal to\n    '\\u2287': MO.REL,        // superset of or equal to\n    '\\u2288': MO.REL,        // neither a subset of nor equal to\n    '\\u2289': MO.REL,        // neither a superset of nor equal to\n    '\\u228A': MO.REL,        // subset of with not equal to\n    '\\u228B': MO.REL,        // superset of with not equal to\n    '\\u228C': MO.BIN4,       // multiset\n    '\\u228D': MO.BIN4,       // multiset multiplication\n    '\\u228E': MO.BIN4,       // multiset union\n    '\\u228F': MO.REL,        // square image of\n    '\\u228F\\u0338': MO.REL,  // square image of with slash\n    '\\u2290': MO.REL,        // square original of\n    '\\u2290\\u0338': MO.REL,  // square original of with slash\n    '\\u2291': MO.REL,        // square image of or equal to\n    '\\u2292': MO.REL,        // square original of or equal to\n    '\\u2293': MO.BIN4,       // square cap\n    '\\u2294': MO.BIN4,       // square cup\n    '\\u2295': MO.BIN4,       // circled plus\n    '\\u2296': MO.BIN4,       // circled minus\n    '\\u2297': MO.BIN4,       // circled times\n    '\\u2298': MO.BIN4,       // circled division slash\n    '\\u2299': MO.BIN4,       // circled dot operator\n    '\\u229A': MO.BIN4,       // circled ring operator\n    '\\u229B': MO.BIN4,       // circled asterisk operator\n    '\\u229C': MO.BIN4,       // circled equals\n    '\\u229D': MO.BIN4,       // circled dash\n    '\\u229E': MO.BIN4,       // squared plus\n    '\\u229F': MO.BIN4,       // squared minus\n    '\\u22A0': MO.BIN4,       // squared times\n    '\\u22A1': MO.BIN4,       // squared dot operator\n    '\\u22A2': MO.REL,        // right tack\n    '\\u22A3': MO.REL,        // left tack\n    '\\u22A4': MO.ORD55,      // down tack\n    '\\u22A5': MO.REL,        // up tack\n    '\\u22A6': MO.REL,        // assertion\n    '\\u22A7': MO.REL,        // models\n    '\\u22A8': MO.REL,        // true\n    '\\u22A9': MO.REL,        // forces\n    '\\u22AA': MO.REL,        // triple vertical bar right turnstile\n    '\\u22AB': MO.REL,        // double vertical bar double right turnstile\n    '\\u22AC': MO.REL,        // does not prove\n    '\\u22AD': MO.REL,        // not true\n    '\\u22AE': MO.REL,        // does not force\n    '\\u22AF': MO.REL,        // negated double vertical bar double right turnstile\n    '\\u22B0': MO.REL,        // precedes under relation\n    '\\u22B1': MO.REL,        // succeeds under relation\n    '\\u22B2': MO.REL,        // normal subgroup of\n    '\\u22B3': MO.REL,        // contains as normal subgroup\n    '\\u22B4': MO.REL,        // normal subgroup of or equal to\n    '\\u22B5': MO.REL,        // contains as normal subgroup or equal to\n    '\\u22B6': MO.REL,        // original of\n    '\\u22B7': MO.REL,        // image of\n    '\\u22B8': MO.REL,        // multimap\n    '\\u22B9': MO.REL,        // hermitian conjugate matrix\n    '\\u22BA': MO.BIN4,       // intercalate\n    '\\u22BB': MO.BIN4,       // xor\n    '\\u22BC': MO.BIN4,       // nand\n    '\\u22BD': MO.BIN4,       // nor\n    '\\u22BE': MO.BIN3,       // right angle with arc\n    '\\u22BF': MO.BIN3,       // right triangle\n    '\\u22C4': MO.BIN4,       // diamond operator\n    '\\u22C5': MO.BIN4,       // dot operator\n    '\\u22C6': MO.BIN4,       // star operator\n    '\\u22C7': MO.BIN4,       // division times\n    '\\u22C8': MO.REL,        // bowtie\n    '\\u22C9': MO.BIN4,       // left normal factor semidirect product\n    '\\u22CA': MO.BIN4,       // right normal factor semidirect product\n    '\\u22CB': MO.BIN4,       // left semidirect product\n    '\\u22CC': MO.BIN4,       // right semidirect product\n    '\\u22CD': MO.REL,        // reversed tilde equals\n    '\\u22CE': MO.BIN4,       // curly logical or\n    '\\u22CF': MO.BIN4,       // curly logical and\n    '\\u22D0': MO.REL,        // double subset\n    '\\u22D1': MO.REL,        // double superset\n    '\\u22D2': MO.BIN4,       // double intersection\n    '\\u22D3': MO.BIN4,       // double union\n    '\\u22D4': MO.REL,        // pitchfork\n    '\\u22D5': MO.REL,        // equal and parallel to\n    '\\u22D6': MO.REL,        // less-than with dot\n    '\\u22D7': MO.REL,        // greater-than with dot\n    '\\u22D8': MO.REL,        // very much less-than\n    '\\u22D9': MO.REL,        // very much greater-than\n    '\\u22DA': MO.REL,        // less-than equal to or greater-than\n    '\\u22DB': MO.REL,        // greater-than equal to or less-than\n    '\\u22DC': MO.REL,        // equal to or less-than\n    '\\u22DD': MO.REL,        // equal to or greater-than\n    '\\u22DE': MO.REL,        // equal to or precedes\n    '\\u22DF': MO.REL,        // equal to or succeeds\n    '\\u22E0': MO.REL,        // does not precede or equal\n    '\\u22E1': MO.REL,        // does not succeed or equal\n    '\\u22E2': MO.REL,        // not square image of or equal to\n    '\\u22E3': MO.REL,        // not square original of or equal to\n    '\\u22E4': MO.REL,        // square image of or not equal to\n    '\\u22E5': MO.REL,        // square original of or not equal to\n    '\\u22E6': MO.REL,        // less-than but not equivalent to\n    '\\u22E7': MO.REL,        // greater-than but not equivalent to\n    '\\u22E8': MO.REL,        // precedes but not equivalent to\n    '\\u22E9': MO.REL,        // succeeds but not equivalent to\n    '\\u22EA': MO.REL,        // not normal subgroup of\n    '\\u22EB': MO.REL,        // does not contain as normal subgroup\n    '\\u22EC': MO.REL,        // not normal subgroup of or equal to\n    '\\u22ED': MO.REL,        // does not contain as normal subgroup or equal\n    '\\u22EE': MO.ORD55,      // vertical ellipsis\n    '\\u22EF': MO.INNER,      // midline horizontal ellipsis\n    '\\u22F0': MO.REL,        // up right diagonal ellipsis\n    '\\u22F1': [5, 5, TEXCLASS.INNER, null], // down right diagonal ellipsis\n    '\\u22F2': MO.REL,        // element of with long horizontal stroke\n    '\\u22F3': MO.REL,        // element of with vertical bar at end of horizontal stroke\n    '\\u22F4': MO.REL,        // small element of with vertical bar at end of horizontal stroke\n    '\\u22F5': MO.REL,        // element of with dot above\n    '\\u22F6': MO.REL,        // element of with overbar\n    '\\u22F7': MO.REL,        // small element of with overbar\n    '\\u22F8': MO.REL,        // element of with underbar\n    '\\u22F9': MO.REL,        // element of with two horizontal strokes\n    '\\u22FA': MO.REL,        // contains with long horizontal stroke\n    '\\u22FB': MO.REL,        // contains with vertical bar at end of horizontal stroke\n    '\\u22FC': MO.REL,        // small contains with vertical bar at end of horizontal stroke\n    '\\u22FD': MO.REL,        // contains with overbar\n    '\\u22FE': MO.REL,        // small contains with overbar\n    '\\u22FF': MO.REL,        // z notation bag membership\n    '\\u2305': MO.BIN3,       // barwedge\n    '\\u2306': MO.BIN3,       // doublebarwedge\n    '\\u2322': MO.REL4,       // \\frown\n    '\\u2323': MO.REL4,       // \\smile\n    '\\u2329': MO.OPEN,       // langle\n    '\\u232A': MO.CLOSE,      // rangle\n    '\\u23AA': MO.ORD,        // \\bracevert\n    '\\u23AF': [0, 0, TEXCLASS.ORD, {stretchy: true}], // \\underline\n    '\\u23B0': MO.OPEN,       // \\lmoustache\n    '\\u23B1': MO.CLOSE,      // \\rmoustache\n    '\\u2500': MO.ORD,        // horizontal line\n    '\\u25B3': MO.BIN4,       // white up-pointing triangle\n    '\\u25B5': MO.BIN4,       // white up-pointing small triangle\n    '\\u25B9': MO.BIN4,       // white right-pointing small triangle\n    '\\u25BD': MO.BIN4,       // white down-pointing triangle\n    '\\u25BF': MO.BIN4,       // white down-pointing small triangle\n    '\\u25C3': MO.BIN4,       // white left-pointing small triangle\n    '\\u25EF': MO.BIN3,       // \\bigcirc\n    '\\u2660': MO.ORD,        // \\spadesuit\n    '\\u2661': MO.ORD,        // \\heartsuit\n    '\\u2662': MO.ORD,        // \\diamondsuit\n    '\\u2663': MO.ORD,        // \\clubsuit\n    '\\u2758': MO.REL,        // light vertical bar\n    '\\u27F0': MO.RELSTRETCH, // upwards quadruple arrow\n    '\\u27F1': MO.RELSTRETCH, // downwards quadruple arrow\n    '\\u27F5': MO.WIDEREL,    // long leftwards arrow\n    '\\u27F6': MO.WIDEREL,    // long rightwards arrow\n    '\\u27F7': MO.WIDEREL,    // long left right arrow\n    '\\u27F8': MO.WIDEREL,    // long leftwards double arrow\n    '\\u27F9': MO.WIDEREL,    // long rightwards double arrow\n    '\\u27FA': MO.WIDEREL,    // long left right double arrow\n    '\\u27FB': MO.WIDEREL,    // long leftwards arrow from bar\n    '\\u27FC': MO.WIDEREL,    // long rightwards arrow from bar\n    '\\u27FD': MO.WIDEREL,    // long leftwards double arrow from bar\n    '\\u27FE': MO.WIDEREL,    // long rightwards double arrow from bar\n    '\\u27FF': MO.WIDEREL,    // long rightwards squiggle arrow\n    '\\u2900': MO.RELACCENT,  // rightwards two-headed arrow with vertical stroke\n    '\\u2901': MO.RELACCENT,  // rightwards two-headed arrow with double vertical stroke\n    '\\u2902': MO.RELACCENT,  // leftwards double arrow with vertical stroke\n    '\\u2903': MO.RELACCENT,  // rightwards double arrow with vertical stroke\n    '\\u2904': MO.RELACCENT,  // left right double arrow with vertical stroke\n    '\\u2905': MO.RELACCENT,  // rightwards two-headed arrow from bar\n    '\\u2906': MO.RELACCENT,  // leftwards double arrow from bar\n    '\\u2907': MO.RELACCENT,  // rightwards double arrow from bar\n    '\\u2908': MO.REL,        // downwards arrow with horizontal stroke\n    '\\u2909': MO.REL,        // upwards arrow with horizontal stroke\n    '\\u290A': MO.RELSTRETCH, // upwards triple arrow\n    '\\u290B': MO.RELSTRETCH, // downwards triple arrow\n    '\\u290C': MO.WIDEREL,    // leftwards double dash arrow\n    '\\u290D': MO.WIDEREL,    // rightwards double dash arrow\n    '\\u290E': MO.WIDEREL,    // leftwards triple dash arrow\n    '\\u290F': MO.WIDEREL,    // rightwards triple dash arrow\n    '\\u2910': MO.WIDEREL,    // rightwards two-headed triple dash arrow\n    '\\u2911': MO.RELACCENT,  // rightwards arrow with dotted stem\n    '\\u2912': MO.RELSTRETCH, // upwards arrow to bar\n    '\\u2913': MO.RELSTRETCH, // downwards arrow to bar\n    '\\u2914': MO.RELACCENT,  // rightwards arrow with tail with vertical stroke\n    '\\u2915': MO.RELACCENT,  // rightwards arrow with tail with double vertical stroke\n    '\\u2916': MO.RELACCENT,  // rightwards two-headed arrow with tail\n    '\\u2917': MO.RELACCENT,  // rightwards two-headed arrow with tail with vertical stroke\n    '\\u2918': MO.RELACCENT,  // rightwards two-headed arrow with tail with double vertical stroke\n    '\\u2919': MO.RELACCENT,  // leftwards arrow-tail\n    '\\u291A': MO.RELACCENT,  // rightwards arrow-tail\n    '\\u291B': MO.RELACCENT,  // leftwards double arrow-tail\n    '\\u291C': MO.RELACCENT,  // rightwards double arrow-tail\n    '\\u291D': MO.RELACCENT,  // leftwards arrow to black diamond\n    '\\u291E': MO.RELACCENT,  // rightwards arrow to black diamond\n    '\\u291F': MO.RELACCENT,  // leftwards arrow from bar to black diamond\n    '\\u2920': MO.RELACCENT,  // rightwards arrow from bar to black diamond\n    '\\u2921': MO.RELSTRETCH, // north west and south east arrow\n    '\\u2922': MO.RELSTRETCH, // north east and south west arrow\n    '\\u2923': MO.REL,        // north west arrow with hook\n    '\\u2924': MO.REL,        // north east arrow with hook\n    '\\u2925': MO.REL,        // south east arrow with hook\n    '\\u2926': MO.REL,        // south west arrow with hook\n    '\\u2927': MO.REL,        // north west arrow and north east arrow\n    '\\u2928': MO.REL,        // north east arrow and south east arrow\n    '\\u2929': MO.REL,        // south east arrow and south west arrow\n    '\\u292A': MO.REL,        // south west arrow and north west arrow\n    '\\u292B': MO.REL,        // rising diagonal crossing falling diagonal\n    '\\u292C': MO.REL,        // falling diagonal crossing rising diagonal\n    '\\u292D': MO.REL,        // south east arrow crossing north east arrow\n    '\\u292E': MO.REL,        // north east arrow crossing south east arrow\n    '\\u292F': MO.REL,        // falling diagonal crossing north east arrow\n    '\\u2930': MO.REL,        // rising diagonal crossing south east arrow\n    '\\u2931': MO.REL,        // north east arrow crossing north west arrow\n    '\\u2932': MO.REL,        // north west arrow crossing north east arrow\n    '\\u2933': MO.RELACCENT,  // wave arrow pointing directly right\n    '\\u2934': MO.REL,        // arrow pointing rightwards then curving upwards\n    '\\u2935': MO.REL,        // arrow pointing rightwards then curving downwards\n    '\\u2936': MO.REL,        // arrow pointing downwards then curving leftwards\n    '\\u2937': MO.REL,        // arrow pointing downwards then curving rightwards\n    '\\u2938': MO.REL,        // right-side arc clockwise arrow\n    '\\u2939': MO.REL,        // left-side arc anticlockwise arrow\n    '\\u293A': MO.RELACCENT,  // top arc anticlockwise arrow\n    '\\u293B': MO.RELACCENT,  // bottom arc anticlockwise arrow\n    '\\u293C': MO.RELACCENT,  // top arc clockwise arrow with minus\n    '\\u293D': MO.RELACCENT,  // top arc anticlockwise arrow with plus\n    '\\u293E': MO.REL,        // lower right semicircular clockwise arrow\n    '\\u293F': MO.REL,        // lower left semicircular anticlockwise arrow\n    '\\u2940': MO.REL,        // anticlockwise closed circle arrow\n    '\\u2941': MO.REL,        // clockwise closed circle arrow\n    '\\u2942': MO.RELACCENT,  // rightwards arrow above short leftwards arrow\n    '\\u2943': MO.RELACCENT,  // leftwards arrow above short rightwards arrow\n    '\\u2944': MO.RELACCENT,  // short rightwards arrow above leftwards arrow\n    '\\u2945': MO.RELACCENT,  // rightwards arrow with plus below\n    '\\u2946': MO.RELACCENT,  // leftwards arrow with plus below\n    '\\u2947': MO.RELACCENT,  // rightwards arrow through x\n    '\\u2948': MO.RELACCENT,  // left right arrow through small circle\n    '\\u2949': MO.REL,        // upwards two-headed arrow from small circle\n    '\\u294A': MO.RELACCENT,  // left barb up right barb down harpoon\n    '\\u294B': MO.RELACCENT,  // left barb down right barb up harpoon\n    '\\u294C': MO.REL,        // up barb right down barb left harpoon\n    '\\u294D': MO.REL,        // up barb left down barb right harpoon\n    '\\u294E': MO.WIDEREL,    // left barb up right barb up harpoon\n    '\\u294F': MO.RELSTRETCH, // up barb right down barb right harpoon\n    '\\u2950': MO.WIDEREL,    // left barb down right barb down harpoon\n    '\\u2951': MO.RELSTRETCH, // up barb left down barb left harpoon\n    '\\u2952': MO.WIDEREL,    // leftwards harpoon with barb up to bar\n    '\\u2953': MO.WIDEREL,    // rightwards harpoon with barb up to bar\n    '\\u2954': MO.RELSTRETCH, // upwards harpoon with barb right to bar\n    '\\u2955': MO.RELSTRETCH, // downwards harpoon with barb right to bar\n    '\\u2956': MO.RELSTRETCH, // leftwards harpoon with barb down to bar\n    '\\u2957': MO.RELSTRETCH, // rightwards harpoon with barb down to bar\n    '\\u2958': MO.RELSTRETCH, // upwards harpoon with barb left to bar\n    '\\u2959': MO.RELSTRETCH, // downwards harpoon with barb left to bar\n    '\\u295A': MO.WIDEREL,    // leftwards harpoon with barb up from bar\n    '\\u295B': MO.WIDEREL,    // rightwards harpoon with barb up from bar\n    '\\u295C': MO.RELSTRETCH, // upwards harpoon with barb right from bar\n    '\\u295D': MO.RELSTRETCH, // downwards harpoon with barb right from bar\n    '\\u295E': MO.WIDEREL,    // leftwards harpoon with barb down from bar\n    '\\u295F': MO.WIDEREL,    // rightwards harpoon with barb down from bar\n    '\\u2960': MO.RELSTRETCH, // upwards harpoon with barb left from bar\n    '\\u2961': MO.RELSTRETCH, // downwards harpoon with barb left from bar\n    '\\u2962': MO.RELACCENT,  // leftwards harpoon with barb up above leftwards harpoon with barb down\n    '\\u2963': MO.REL,        // upwards harpoon with barb left beside upwards harpoon with barb right\n    '\\u2964': MO.RELACCENT,  // rightwards harpoon with barb up above rightwards harpoon with barb down\n    '\\u2965': MO.REL,        // downwards harpoon with barb left beside downwards harpoon with barb right\n    '\\u2966': MO.RELACCENT,  // leftwards harpoon with barb up above rightwards harpoon with barb up\n    '\\u2967': MO.RELACCENT,  // leftwards harpoon with barb down above rightwards harpoon with barb down\n    '\\u2968': MO.RELACCENT,  // rightwards harpoon with barb up above leftwards harpoon with barb up\n    '\\u2969': MO.RELACCENT,  // rightwards harpoon with barb down above leftwards harpoon with barb down\n    '\\u296A': MO.RELACCENT,  // leftwards harpoon with barb up above long dash\n    '\\u296B': MO.RELACCENT,  // leftwards harpoon with barb down below long dash\n    '\\u296C': MO.RELACCENT,  // rightwards harpoon with barb up above long dash\n    '\\u296D': MO.RELACCENT,  // rightwards harpoon with barb down below long dash\n    '\\u296E': MO.RELSTRETCH, // upwards harpoon with barb left beside downwards harpoon with barb right\n    '\\u296F': MO.RELSTRETCH, // downwards harpoon with barb left beside upwards harpoon with barb right\n    '\\u2970': MO.RELACCENT,  // right double arrow with rounded head\n    '\\u2971': MO.RELACCENT,  // equals sign above rightwards arrow\n    '\\u2972': MO.RELACCENT,  // tilde operator above rightwards arrow\n    '\\u2973': MO.RELACCENT,  // leftwards arrow above tilde operator\n    '\\u2974': MO.RELACCENT,  // rightwards arrow above tilde operator\n    '\\u2975': MO.RELACCENT,  // rightwards arrow above almost equal to\n    '\\u2976': MO.RELACCENT,  // less-than above leftwards arrow\n    '\\u2977': MO.RELACCENT,  // leftwards arrow through less-than\n    '\\u2978': MO.RELACCENT,  // greater-than above rightwards arrow\n    '\\u2979': MO.RELACCENT,  // subset above rightwards arrow\n    '\\u297A': MO.RELACCENT,  // leftwards arrow through subset\n    '\\u297B': MO.RELACCENT,  // superset above leftwards arrow\n    '\\u297C': MO.RELACCENT,  // left fish tail\n    '\\u297D': MO.RELACCENT,  // right fish tail\n    '\\u297E': MO.REL,        // up fish tail\n    '\\u297F': MO.REL,        // down fish tail\n    '\\u2981': MO.BIN3,       // z notation spot\n    '\\u2982': MO.BIN3,       // z notation type colon\n    '\\u2999': MO.BIN3,       // dotted fence\n    '\\u299A': MO.BIN3,       // vertical zigzag line\n    '\\u299B': MO.BIN3,       // measured angle opening left\n    '\\u299C': MO.BIN3,       // right angle variant with square\n    '\\u299D': MO.BIN3,       // measured right angle with dot\n    '\\u299E': MO.BIN3,       // angle with s inside\n    '\\u299F': MO.BIN3,       // acute angle\n    '\\u29A0': MO.BIN3,       // spherical angle opening left\n    '\\u29A1': MO.BIN3,       // spherical angle opening up\n    '\\u29A2': MO.BIN3,       // turned angle\n    '\\u29A3': MO.BIN3,       // reversed angle\n    '\\u29A4': MO.BIN3,       // angle with underbar\n    '\\u29A5': MO.BIN3,       // reversed angle with underbar\n    '\\u29A6': MO.BIN3,       // oblique angle opening up\n    '\\u29A7': MO.BIN3,       // oblique angle opening down\n    '\\u29A8': MO.BIN3,       // measured angle with open arm ending in arrow pointing up and right\n    '\\u29A9': MO.BIN3,       // measured angle with open arm ending in arrow pointing up and left\n    '\\u29AA': MO.BIN3,       // measured angle with open arm ending in arrow pointing down and right\n    '\\u29AB': MO.BIN3,       // measured angle with open arm ending in arrow pointing down and left\n    '\\u29AC': MO.BIN3,       // measured angle with open arm ending in arrow pointing right and up\n    '\\u29AD': MO.BIN3,       // measured angle with open arm ending in arrow pointing left and up\n    '\\u29AE': MO.BIN3,       // measured angle with open arm ending in arrow pointing right and down\n    '\\u29AF': MO.BIN3,       // measured angle with open arm ending in arrow pointing left and down\n    '\\u29B0': MO.BIN3,       // reversed empty set\n    '\\u29B1': MO.BIN3,       // empty set with overbar\n    '\\u29B2': MO.BIN3,       // empty set with small circle above\n    '\\u29B3': MO.BIN3,       // empty set with right arrow above\n    '\\u29B4': MO.BIN3,       // empty set with left arrow above\n    '\\u29B5': MO.BIN3,       // circle with horizontal bar\n    '\\u29B6': MO.BIN4,       // circled vertical bar\n    '\\u29B7': MO.BIN4,       // circled parallel\n    '\\u29B8': MO.BIN4,       // circled reverse solidus\n    '\\u29B9': MO.BIN4,       // circled perpendicular\n    '\\u29BA': MO.BIN4,       // circle divided by horizontal bar and top half divided by vertical bar\n    '\\u29BB': MO.BIN4,       // circle with superimposed x\n    '\\u29BC': MO.BIN4,       // circled anticlockwise-rotated division sign\n    '\\u29BD': MO.BIN4,       // up arrow through circle\n    '\\u29BE': MO.BIN4,       // circled white bullet\n    '\\u29BF': MO.BIN4,       // circled bullet\n    '\\u29C0': MO.REL,        // circled less-than\n    '\\u29C1': MO.REL,        // circled greater-than\n    '\\u29C2': MO.BIN3,       // circle with small circle to the right\n    '\\u29C3': MO.BIN3,       // circle with two horizontal strokes to the right\n    '\\u29C4': MO.BIN4,       // squared rising diagonal slash\n    '\\u29C5': MO.BIN4,       // squared falling diagonal slash\n    '\\u29C6': MO.BIN4,       // squared asterisk\n    '\\u29C7': MO.BIN4,       // squared small circle\n    '\\u29C8': MO.BIN4,       // squared square\n    '\\u29C9': MO.BIN3,       // two joined squares\n    '\\u29CA': MO.BIN3,       // triangle with dot above\n    '\\u29CB': MO.BIN3,       // triangle with underbar\n    '\\u29CC': MO.BIN3,       // s in triangle\n    '\\u29CD': MO.BIN3,       // triangle with serifs at bottom\n    '\\u29CE': MO.REL,        // right triangle above left triangle\n    '\\u29CF': MO.REL,        // left triangle beside vertical bar\n    '\\u29CF\\u0338': MO.REL,  // left triangle beside vertical bar with slash\n    '\\u29D0': MO.REL,        // vertical bar beside right triangle\n    '\\u29D0\\u0338': MO.REL,  // vertical bar beside right triangle with slash\n    '\\u29D1': MO.REL,        // bowtie with left half black\n    '\\u29D2': MO.REL,        // bowtie with right half black\n    '\\u29D3': MO.REL,        // black bowtie\n    '\\u29D4': MO.REL,        // times with left half black\n    '\\u29D5': MO.REL,        // times with right half black\n    '\\u29D6': MO.BIN4,       // white hourglass\n    '\\u29D7': MO.BIN4,       // black hourglass\n    '\\u29D8': MO.BIN3,       // left wiggly fence\n    '\\u29D9': MO.BIN3,       // right wiggly fence\n    '\\u29DB': MO.BIN3,       // right double wiggly fence\n    '\\u29DC': MO.BIN3,       // incomplete infinity\n    '\\u29DD': MO.BIN3,       // tie over infinity\n    '\\u29DE': MO.REL,        // infinity negated with vertical bar\n    '\\u29DF': MO.BIN3,       // double-ended multimap\n    '\\u29E0': MO.BIN3,       // square with contoured outline\n    '\\u29E1': MO.REL,        // increases as\n    '\\u29E2': MO.BIN4,       // shuffle product\n    '\\u29E3': MO.REL,        // equals sign and slanted parallel\n    '\\u29E4': MO.REL,        // equals sign and slanted parallel with tilde above\n    '\\u29E5': MO.REL,        // identical to and slanted parallel\n    '\\u29E6': MO.REL,        // gleich stark\n    '\\u29E7': MO.BIN3,       // thermodynamic\n    '\\u29E8': MO.BIN3,       // down-pointing triangle with left half black\n    '\\u29E9': MO.BIN3,       // down-pointing triangle with right half black\n    '\\u29EA': MO.BIN3,       // black diamond with down arrow\n    '\\u29EB': MO.BIN3,       // black lozenge\n    '\\u29EC': MO.BIN3,       // white circle with down arrow\n    '\\u29ED': MO.BIN3,       // black circle with down arrow\n    '\\u29EE': MO.BIN3,       // error-barred white square\n    '\\u29EF': MO.BIN3,       // error-barred black square\n    '\\u29F0': MO.BIN3,       // error-barred white diamond\n    '\\u29F1': MO.BIN3,       // error-barred black diamond\n    '\\u29F2': MO.BIN3,       // error-barred white circle\n    '\\u29F3': MO.BIN3,       // error-barred black circle\n    '\\u29F4': MO.REL,        // rule-delayed\n    '\\u29F5': MO.BIN4,       // reverse solidus operator\n    '\\u29F6': MO.BIN4,       // solidus with overbar\n    '\\u29F7': MO.BIN4,       // reverse solidus with horizontal stroke\n    '\\u29F8': MO.BIN3,       // big solidus\n    '\\u29F9': MO.BIN3,       // big reverse solidus\n    '\\u29FA': MO.BIN3,       // double plus\n    '\\u29FB': MO.BIN3,       // triple plus\n    '\\u29FE': MO.BIN4,       // tiny\n    '\\u29FF': MO.BIN4,       // miny\n    '\\u2A1D': MO.BIN3,       // join\n    '\\u2A1E': MO.BIN3,       // large left triangle operator\n    '\\u2A1F': MO.BIN3,       // z notation schema composition\n    '\\u2A20': MO.BIN3,       // z notation schema piping\n    '\\u2A21': MO.BIN3,       // z notation schema projection\n    '\\u2A22': MO.BIN4,       // plus sign with small circle above\n    '\\u2A23': MO.BIN4,       // plus sign with circumflex accent above\n    '\\u2A24': MO.BIN4,       // plus sign with tilde above\n    '\\u2A25': MO.BIN4,       // plus sign with dot below\n    '\\u2A26': MO.BIN4,       // plus sign with tilde below\n    '\\u2A27': MO.BIN4,       // plus sign with subscript two\n    '\\u2A28': MO.BIN4,       // plus sign with black triangle\n    '\\u2A29': MO.BIN4,       // minus sign with comma above\n    '\\u2A2A': MO.BIN4,       // minus sign with dot below\n    '\\u2A2B': MO.BIN4,       // minus sign with falling dots\n    '\\u2A2C': MO.BIN4,       // minus sign with rising dots\n    '\\u2A2D': MO.BIN4,       // plus sign in left half circle\n    '\\u2A2E': MO.BIN4,       // plus sign in right half circle\n    '\\u2A2F': MO.BIN4,       // vector or cross product\n    '\\u2A30': MO.BIN4,       // multiplication sign with dot above\n    '\\u2A31': MO.BIN4,       // multiplication sign with underbar\n    '\\u2A32': MO.BIN4,       // semidirect product with bottom closed\n    '\\u2A33': MO.BIN4,       // smash product\n    '\\u2A34': MO.BIN4,       // multiplication sign in left half circle\n    '\\u2A35': MO.BIN4,       // multiplication sign in right half circle\n    '\\u2A36': MO.BIN4,       // circled multiplication sign with circumflex accent\n    '\\u2A37': MO.BIN4,       // multiplication sign in double circle\n    '\\u2A38': MO.BIN4,       // circled division sign\n    '\\u2A39': MO.BIN4,       // plus sign in triangle\n    '\\u2A3A': MO.BIN4,       // minus sign in triangle\n    '\\u2A3B': MO.BIN4,       // multiplication sign in triangle\n    '\\u2A3C': MO.BIN4,       // interior product\n    '\\u2A3D': MO.BIN4,       // righthand interior product\n    '\\u2A3E': MO.BIN4,       // z notation relational composition\n    '\\u2A3F': MO.BIN4,       // amalgamation or coproduct\n    '\\u2A40': MO.BIN4,       // intersection with dot\n    '\\u2A41': MO.BIN4,       // union with minus sign\n    '\\u2A42': MO.BIN4,       // union with overbar\n    '\\u2A43': MO.BIN4,       // intersection with overbar\n    '\\u2A44': MO.BIN4,       // intersection with logical and\n    '\\u2A45': MO.BIN4,       // union with logical or\n    '\\u2A46': MO.BIN4,       // union above intersection\n    '\\u2A47': MO.BIN4,       // intersection above union\n    '\\u2A48': MO.BIN4,       // union above bar above intersection\n    '\\u2A49': MO.BIN4,       // intersection above bar above union\n    '\\u2A4A': MO.BIN4,       // union beside and joined with union\n    '\\u2A4B': MO.BIN4,       // intersection beside and joined with intersection\n    '\\u2A4C': MO.BIN4,       // closed union with serifs\n    '\\u2A4D': MO.BIN4,       // closed intersection with serifs\n    '\\u2A4E': MO.BIN4,       // double square intersection\n    '\\u2A4F': MO.BIN4,       // double square union\n    '\\u2A50': MO.BIN4,       // closed union with serifs and smash product\n    '\\u2A51': MO.BIN4,       // logical and with dot above\n    '\\u2A52': MO.BIN4,       // logical or with dot above\n    '\\u2A53': MO.BIN4,       // double logical and\n    '\\u2A54': MO.BIN4,       // double logical or\n    '\\u2A55': MO.BIN4,       // two intersecting logical and\n    '\\u2A56': MO.BIN4,       // two intersecting logical or\n    '\\u2A57': MO.BIN4,       // sloping large or\n    '\\u2A58': MO.BIN4,       // sloping large and\n    '\\u2A59': MO.REL,        // logical or overlapping logical and\n    '\\u2A5A': MO.BIN4,       // logical and with middle stem\n    '\\u2A5B': MO.BIN4,       // logical or with middle stem\n    '\\u2A5C': MO.BIN4,       // logical and with horizontal dash\n    '\\u2A5D': MO.BIN4,       // logical or with horizontal dash\n    '\\u2A5E': MO.BIN4,       // logical and with double overbar\n    '\\u2A5F': MO.BIN4,       // logical and with underbar\n    '\\u2A60': MO.BIN4,       // logical and with double underbar\n    '\\u2A61': MO.BIN4,       // small vee with underbar\n    '\\u2A62': MO.BIN4,       // logical or with double overbar\n    '\\u2A63': MO.BIN4,       // logical or with double underbar\n    '\\u2A64': MO.BIN4,       // z notation domain antirestriction\n    '\\u2A65': MO.BIN4,       // z notation range antirestriction\n    '\\u2A66': MO.REL,        // equals sign with dot below\n    '\\u2A67': MO.REL,        // identical with dot above\n    '\\u2A68': MO.REL,        // triple horizontal bar with double vertical stroke\n    '\\u2A69': MO.REL,        // triple horizontal bar with triple vertical stroke\n    '\\u2A6A': MO.REL,        // tilde operator with dot above\n    '\\u2A6B': MO.REL,        // tilde operator with rising dots\n    '\\u2A6C': MO.REL,        // similar minus similar\n    '\\u2A6D': MO.REL,        // congruent with dot above\n    '\\u2A6E': MO.REL,        // equals with asterisk\n    '\\u2A6F': MO.REL,        // almost equal to with circumflex accent\n    '\\u2A70': MO.REL,        // approximately equal or equal to\n    '\\u2A71': MO.BIN4,       // equals sign above plus sign\n    '\\u2A72': MO.BIN4,       // plus sign above equals sign\n    '\\u2A73': MO.REL,        // equals sign above tilde operator\n    '\\u2A74': MO.REL,        // double colon equal\n    '\\u2A75': MO.REL,        // two consecutive equals signs\n    '\\u2A76': MO.REL,        // three consecutive equals signs\n    '\\u2A77': MO.REL,        // equals sign with two dots above and two dots below\n    '\\u2A78': MO.REL,        // equivalent with four dots above\n    '\\u2A79': MO.REL,        // less-than with circle inside\n    '\\u2A7A': MO.REL,        // greater-than with circle inside\n    '\\u2A7B': MO.REL,        // less-than with question mark above\n    '\\u2A7C': MO.REL,        // greater-than with question mark above\n    '\\u2A7D': MO.REL,        // less-than or slanted equal to\n    '\\u2A7D\\u0338': MO.REL,  // less-than or slanted equal to with slash\n    '\\u2A7E': MO.REL,        // greater-than or slanted equal to\n    '\\u2A7E\\u0338': MO.REL,  // greater-than or slanted equal to with slash\n    '\\u2A7F': MO.REL,        // less-than or slanted equal to with dot inside\n    '\\u2A80': MO.REL,        // greater-than or slanted equal to with dot inside\n    '\\u2A81': MO.REL,        // less-than or slanted equal to with dot above\n    '\\u2A82': MO.REL,        // greater-than or slanted equal to with dot above\n    '\\u2A83': MO.REL,        // less-than or slanted equal to with dot above right\n    '\\u2A84': MO.REL,        // greater-than or slanted equal to with dot above left\n    '\\u2A85': MO.REL,        // less-than or approximate\n    '\\u2A86': MO.REL,        // greater-than or approximate\n    '\\u2A87': MO.REL,        // less-than and single-line not equal to\n    '\\u2A88': MO.REL,        // greater-than and single-line not equal to\n    '\\u2A89': MO.REL,        // less-than and not approximate\n    '\\u2A8A': MO.REL,        // greater-than and not approximate\n    '\\u2A8B': MO.REL,        // less-than above double-line equal above greater-than\n    '\\u2A8C': MO.REL,        // greater-than above double-line equal above less-than\n    '\\u2A8D': MO.REL,        // less-than above similar or equal\n    '\\u2A8E': MO.REL,        // greater-than above similar or equal\n    '\\u2A8F': MO.REL,        // less-than above similar above greater-than\n    '\\u2A90': MO.REL,        // greater-than above similar above less-than\n    '\\u2A91': MO.REL,        // less-than above greater-than above double-line equal\n    '\\u2A92': MO.REL,        // greater-than above less-than above double-line equal\n    '\\u2A93': MO.REL,        // less-than above slanted equal above greater-than above slanted equal\n    '\\u2A94': MO.REL,        // greater-than above slanted equal above less-than above slanted equal\n    '\\u2A95': MO.REL,        // slanted equal to or less-than\n    '\\u2A96': MO.REL,        // slanted equal to or greater-than\n    '\\u2A97': MO.REL,        // slanted equal to or less-than with dot inside\n    '\\u2A98': MO.REL,        // slanted equal to or greater-than with dot inside\n    '\\u2A99': MO.REL,        // double-line equal to or less-than\n    '\\u2A9A': MO.REL,        // double-line equal to or greater-than\n    '\\u2A9B': MO.REL,        // double-line slanted equal to or less-than\n    '\\u2A9C': MO.REL,        // double-line slanted equal to or greater-than\n    '\\u2A9D': MO.REL,        // similar or less-than\n    '\\u2A9E': MO.REL,        // similar or greater-than\n    '\\u2A9F': MO.REL,        // similar above less-than above equals sign\n    '\\u2AA0': MO.REL,        // similar above greater-than above equals sign\n    '\\u2AA1': MO.REL,        // double nested less-than\n    '\\u2AA1\\u0338': MO.REL,  // double nested less-than with slash\n    '\\u2AA2': MO.REL,        // double nested greater-than\n    '\\u2AA2\\u0338': MO.REL,  // double nested greater-than with slash\n    '\\u2AA3': MO.REL,        // double nested less-than with underbar\n    '\\u2AA4': MO.REL,        // greater-than overlapping less-than\n    '\\u2AA5': MO.REL,        // greater-than beside less-than\n    '\\u2AA6': MO.REL,        // less-than closed by curve\n    '\\u2AA7': MO.REL,        // greater-than closed by curve\n    '\\u2AA8': MO.REL,        // less-than closed by curve above slanted equal\n    '\\u2AA9': MO.REL,        // greater-than closed by curve above slanted equal\n    '\\u2AAA': MO.REL,        // smaller than\n    '\\u2AAB': MO.REL,        // larger than\n    '\\u2AAC': MO.REL,        // smaller than or equal to\n    '\\u2AAD': MO.REL,        // larger than or equal to\n    '\\u2AAE': MO.REL,        // equals sign with bumpy above\n    '\\u2AAF': MO.REL,        // precedes above single-line equals sign\n    '\\u2AAF\\u0338': MO.REL,  // precedes above single-line equals sign with slash\n    '\\u2AB0': MO.REL,        // succeeds above single-line equals sign\n    '\\u2AB0\\u0338': MO.REL,  // succeeds above single-line equals sign with slash\n    '\\u2AB1': MO.REL,        // precedes above single-line not equal to\n    '\\u2AB2': MO.REL,        // succeeds above single-line not equal to\n    '\\u2AB3': MO.REL,        // precedes above equals sign\n    '\\u2AB4': MO.REL,        // succeeds above equals sign\n    '\\u2AB5': MO.REL,        // precedes above not equal to\n    '\\u2AB6': MO.REL,        // succeeds above not equal to\n    '\\u2AB7': MO.REL,        // precedes above almost equal to\n    '\\u2AB8': MO.REL,        // succeeds above almost equal to\n    '\\u2AB9': MO.REL,        // precedes above not almost equal to\n    '\\u2ABA': MO.REL,        // succeeds above not almost equal to\n    '\\u2ABB': MO.REL,        // double precedes\n    '\\u2ABC': MO.REL,        // double succeeds\n    '\\u2ABD': MO.REL,        // subset with dot\n    '\\u2ABE': MO.REL,        // superset with dot\n    '\\u2ABF': MO.REL,        // subset with plus sign below\n    '\\u2AC0': MO.REL,        // superset with plus sign below\n    '\\u2AC1': MO.REL,        // subset with multiplication sign below\n    '\\u2AC2': MO.REL,        // superset with multiplication sign below\n    '\\u2AC3': MO.REL,        // subset of or equal to with dot above\n    '\\u2AC4': MO.REL,        // superset of or equal to with dot above\n    '\\u2AC5': MO.REL,        // subset of above equals sign\n    '\\u2AC6': MO.REL,        // superset of above equals sign\n    '\\u2AC7': MO.REL,        // subset of above tilde operator\n    '\\u2AC8': MO.REL,        // superset of above tilde operator\n    '\\u2AC9': MO.REL,        // subset of above almost equal to\n    '\\u2ACA': MO.REL,        // superset of above almost equal to\n    '\\u2ACB': MO.REL,        // subset of above not equal to\n    '\\u2ACC': MO.REL,        // superset of above not equal to\n    '\\u2ACD': MO.REL,        // square left open box operator\n    '\\u2ACE': MO.REL,        // square right open box operator\n    '\\u2ACF': MO.REL,        // closed subset\n    '\\u2AD0': MO.REL,        // closed superset\n    '\\u2AD1': MO.REL,        // closed subset or equal to\n    '\\u2AD2': MO.REL,        // closed superset or equal to\n    '\\u2AD3': MO.REL,        // subset above superset\n    '\\u2AD4': MO.REL,        // superset above subset\n    '\\u2AD5': MO.REL,        // subset above subset\n    '\\u2AD6': MO.REL,        // superset above superset\n    '\\u2AD7': MO.REL,        // superset beside subset\n    '\\u2AD8': MO.REL,        // superset beside and joined by dash with subset\n    '\\u2AD9': MO.REL,        // element of opening downwards\n    '\\u2ADA': MO.REL,        // pitchfork with tee top\n    '\\u2ADB': MO.REL,        // transversal intersection\n    '\\u2ADD': MO.REL,        // nonforking\n    '\\u2ADD\\u0338': MO.REL,  // nonforking with slash\n    '\\u2ADE': MO.REL,        // short left tack\n    '\\u2ADF': MO.REL,        // short down tack\n    '\\u2AE0': MO.REL,        // short up tack\n    '\\u2AE1': MO.REL,        // perpendicular with s\n    '\\u2AE2': MO.REL,        // vertical bar triple right turnstile\n    '\\u2AE3': MO.REL,        // double vertical bar left turnstile\n    '\\u2AE4': MO.REL,        // vertical bar double left turnstile\n    '\\u2AE5': MO.REL,        // double vertical bar double left turnstile\n    '\\u2AE6': MO.REL,        // long dash from left member of double vertical\n    '\\u2AE7': MO.REL,        // short down tack with overbar\n    '\\u2AE8': MO.REL,        // short up tack with underbar\n    '\\u2AE9': MO.REL,        // short up tack above short down tack\n    '\\u2AEA': MO.REL,        // double down tack\n    '\\u2AEB': MO.REL,        // double up tack\n    '\\u2AEC': MO.REL,        // double stroke not sign\n    '\\u2AED': MO.REL,        // reversed double stroke not sign\n    '\\u2AEE': MO.REL,        // does not divide with reversed negation slash\n    '\\u2AEF': MO.REL,        // vertical line with circle above\n    '\\u2AF0': MO.REL,        // vertical line with circle below\n    '\\u2AF1': MO.REL,        // down tack with circle below\n    '\\u2AF2': MO.REL,        // parallel with horizontal stroke\n    '\\u2AF3': MO.REL,        // parallel with tilde operator\n    '\\u2AF4': MO.BIN4,       // triple vertical bar binary relation\n    '\\u2AF5': MO.BIN4,       // triple vertical bar with horizontal stroke\n    '\\u2AF6': MO.BIN4,       // triple colon operator\n    '\\u2AF7': MO.REL,        // triple nested less-than\n    '\\u2AF8': MO.REL,        // triple nested greater-than\n    '\\u2AF9': MO.REL,        // double-line slanted less-than or equal to\n    '\\u2AFA': MO.REL,        // double-line slanted greater-than or equal to\n    '\\u2AFB': MO.BIN4,       // triple solidus binary relation\n    '\\u2AFD': MO.BIN4,       // double solidus operator\n    '\\u2AFE': MO.BIN3,       // white vertical bar\n    '\\u2B45': MO.RELSTRETCH, // leftwards quadruple arrow\n    '\\u2B46': MO.RELSTRETCH, // rightwards quadruple arrow\n    '\\u3008': MO.OPEN,       // langle\n    '\\u3009': MO.CLOSE,      // rangle\n    '\\uFE37': MO.WIDEACCENT, // horizontal brace down\n    '\\uFE38': MO.WIDEACCENT, // horizontal brace up\n  }\n};\n\n//\n//  These are not in the W3C table, but we need them for \\widehat and \\underline\n//\nOPTABLE.infix['^'] = MO.WIDEREL;\nOPTABLE.infix['_'] = MO.WIDEREL;\n\n//\n//  Remove from Appendix C, but perhaps that was a mistake?\n//\nOPTABLE.infix['\\u2ADC'] = MO.REL;\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the MmlMo node\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {PropertyList} from '../../Tree/Node.js';\nimport {AbstractMmlTokenNode, MmlNode, AttributeList, TEXCLASS} from '../MmlNode.js';\nimport {MmlMrow} from './mrow.js';\nimport {MmlMover, MmlMunder, MmlMunderover} from './munderover.js';\nimport {OperatorList, OPTABLE, getRange, MMLSPACING} from '../OperatorDictionary.js';\nimport {unicodeChars, unicodeString} from '../../../util/string.js';\n\n/*****************************************************************/\n/**\n *  Implements the MmlMo node class (subclass of AbstractMmlTokenNode)\n */\n\nexport class MmlMo extends AbstractMmlTokenNode {\n\n  /**\n   * @override\n   */\n  public static defaults: PropertyList = {\n    ...AbstractMmlTokenNode.defaults,\n    form: 'infix',\n    fence: false,\n    separator: false,\n    lspace: 'thickmathspace',\n    rspace: 'thickmathspace',\n    stretchy: false,\n    symmetric: false,\n    maxsize: 'infinity',\n    minsize: '0em', // MathML says '1em', but that is larger than some natural sizes\n    largeop: false,\n    movablelimits: false,\n    accent: false,\n    linebreak: 'auto',\n    lineleading: '1ex',\n    linebreakstyle: 'before',\n    indentalign: 'auto',\n    indentshift: '0',\n    indenttarget: '',\n    indentalignfirst: 'indentalign',\n    indentshiftfirst: 'indentshift',\n    indentalignlast: 'indentalign',\n    indentshiftlast: 'indentshift'\n  };\n\n  /**\n   * The MathML spacing values for the TeX classes\n   */\n  public static MMLSPACING = MMLSPACING;\n\n  /**\n   * The Operator Dictionary\n   */\n  public static OPTABLE: {[form: string]: OperatorList} = OPTABLE;\n\n  /**\n   * Pattern for matching when the contents is one ore more pseudoscripts\n   */\n  public static pseudoScripts = new RegExp([\n    '^[\"\\'*`',\n    '\\u00AA',               // FEMININE ORDINAL INDICATOR\n    '\\u00B0',               // DEGREE SIGN\n    '\\u00B2-\\u00B4',        // SUPERSCRIPT 2 and 3, ACUTE ACCENT\n    '\\u00B9',               // SUPERSCRIPT ONE\n    '\\u00BA',               // MASCULINE ORDINAL INDICATOR\n    '\\u2018-\\u201F',        // Various double and single quotation marks (up and down)\n    '\\u2032-\\u2037\\u2057',  // Primes and reversed primes (forward and reversed)\n    '\\u2070\\u2071',         // SUPERSCRIPT 0 and i\n    '\\u2074-\\u207F',        // SUPERCRIPT 4 through 9, -, =, (, ), and n\n    '\\u2080-\\u208E',        // SUBSCRIPT 0 through 9, -, =, (, ).\n    ']+$'\n  ].join(''));\n\n  /**\n   * Pattern for when contents is a collection of primes\n   */\n   protected static primes = new RegExp([\n     '^[\"\\'`',\n     '\\u2018-\\u201F',        // Various double and single quotation marks (up and down)\n     ']+$'\n   ].join(''));\n\n   /**\n    * Default map for remapping prime characters\n    */\n  protected static remapPrimes: {[n: number]: number} = {\n     0x0022: 0x2033,   // double quotes\n     0x0027: 0x2032,   // single quote\n     0x0060: 0x2035,   // back quote\n     0x2018: 0x2035,   // open single quote\n     0x2019: 0x2032,   // close single quote\n     0x201A: 0x2032,   // low open single quote\n     0x201B: 0x2035,   // reversed open single quote\n     0x201C: 0x2036,   // open double quote\n     0x201D: 0x2033,   // close double quote\n     0x201E: 0x2033,   // low open double quote\n     0x201F: 0x2036,   // reversed open double quote\n  };\n\n  /**\n   * Regular expression matching characters that are marked as math accents\n   */\n  protected static mathaccents = new RegExp([\n    '^[',\n    '\\u00B4\\u0301\\u02CA',  // acute\n    '\\u0060\\u0300\\u02CB',  // grave\n    '\\u00A8\\u0308',        // ddot\n    '\\u007E\\u0303\\u02DC',  // tilde\n    '\\u00AF\\u0304\\u02C9',  // bar\n    '\\u02D8\\u0306',        // breve\n    '\\u02C7\\u030C',        // check\n    '\\u005E\\u0302\\u02C6',  // hat\n    '\\u2192\\u20D7',        // vec\n    '\\u02D9\\u0307',        // dot\n    '\\u02DA\\u030A',        // mathring\n    '\\u20DB',              // dddot\n    '\\u20DC',              // ddddot\n    ']$'\n  ].join(''));\n\n  /**\n   * The internal TeX class of the node (for use with getter/setter below)\n   */\n  public _texClass: number = null;\n\n  /**\n   * Use a getter to look up the TeX class from the operator table if it hasn't\n   * been set yet (but don't save it in case the form changes when it is in its\n   * location).\n   */\n  public get texClass() {\n    if (this._texClass === null) {\n      let mo = this.getText();\n      let [form1, form2, form3] = this.handleExplicitForm(this.getForms());\n      let OPTABLE = (this.constructor as typeof MmlMo).OPTABLE;\n      let def = OPTABLE[form1][mo] || OPTABLE[form2][mo] || OPTABLE[form3][mo];\n      return def ? def[2] : TEXCLASS.REL;\n    }\n    return this._texClass;\n  }\n\n  /**\n   * Use a setter to store the actual value in _texClass;\n   */\n  public set texClass(value: number) {\n    this._texClass = value;\n  }\n\n  /**\n   * The default MathML spacing on the left\n   */\n  /* tslint:disable-next-line:whitespace */\n  public lspace = 5/18;\n\n  /**\n   * The default MathML spacing on the right\n   */\n  /* tslint:disable-next-line:whitespace */\n  public rspace = 5/18;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mo';\n  }\n\n  /**\n   * All <mo> are considered embellished\n   * @override\n   */\n  public get isEmbellished() {\n    return true;\n  }\n\n  /**\n   * @return {boolean}  Is <mo> marked as an explicit linebreak?\n   */\n  public get hasNewLine(): boolean {\n    return this.attributes.get('linebreak') === 'newline';\n  }\n\n  /**\n   * @return {MmlNode}  The node that is the outermost embellished operator\n   *                    with this node as its core\n   */\n  public coreParent(): MmlNode {\n    let embellished = this as MmlNode;\n    let parent = this as MmlNode;\n    let math = this.factory.getNodeClass('math');\n    while (parent && parent.isEmbellished && parent.coreMO() === this && !(parent instanceof math)) {\n      embellished = parent;\n      parent = (parent as MmlNode).parent;\n    }\n    return embellished;\n  }\n\n  /**\n   * @param {MmlNode} parent  The node whose core text is to be obtained\n   * @return {string}         The text of the core MO of the given parent element\n   */\n  public coreText(parent: MmlNode): string {\n    if (!parent) {\n      return '';\n    }\n    if (parent.isEmbellished) {\n      return (parent.coreMO() as MmlMo).getText();\n    }\n    while ((((parent.isKind('mrow') ||\n              (parent.isKind('TeXAtom') && parent.texClass !== TEXCLASS.VCENTER) ||\n              parent.isKind('mstyle') ||\n              parent.isKind('mphantom')) && parent.childNodes.length === 1) ||\n            parent.isKind('munderover')) && parent.childNodes[0]) {\n      parent = parent.childNodes[0] as MmlNode;\n    }\n    return (parent.isToken ? (parent as AbstractMmlTokenNode).getText() : '');\n  }\n\n  /**\n   * @override\n   */\n  public hasSpacingAttributes() {\n    return this.attributes.isSet('lspace') ||\n      this.attributes.isSet('rspace');\n  }\n\n  /**\n   * @return {boolean}  True is this mo is an accent in an munderover construction\n   */\n  get isAccent(): boolean {\n    let accent = false;\n    const node = this.coreParent().parent;\n    if (node) {\n      const key = (node.isKind('mover') ?\n                   ((node.childNodes[(node as MmlMover).over] as MmlNode).coreMO() ?\n                    'accent' : '') :\n                   node.isKind('munder') ?\n                   ((node.childNodes[(node as MmlMunder).under] as MmlNode).coreMO() ?\n                    'accentunder' : '') :\n                   node.isKind('munderover') ?\n                   (this === (node.childNodes[(node as MmlMunderover).over] as MmlNode).coreMO() ?\n                    'accent' :\n                    this === (node.childNodes[(node as MmlMunderover).under] as MmlNode).coreMO() ?\n                    'accentunder' : '') :\n                   '');\n      if (key) {\n        const value = node.attributes.getExplicit(key);\n        accent = (value !== undefined ? accent : this.attributes.get('accent')) as boolean;\n      }\n    }\n    return accent;\n  }\n\n  /**\n   * Produce the texClass based on the operator dictionary values\n   *\n   * @override\n   */\n  public setTeXclass(prev: MmlNode): MmlNode {\n    let {form, fence} = this.attributes.getList('form', 'fence') as {form: string, fence: string};\n    if (this.getProperty('texClass') === undefined &&\n        (this.attributes.isSet('lspace') || this.attributes.isSet('rspace'))) {\n      return null;\n    }\n    if (fence && this.texClass === TEXCLASS.REL) {\n      if (form === 'prefix') {\n        this.texClass = TEXCLASS.OPEN;\n      }\n      if (form === 'postfix') {\n        this.texClass = TEXCLASS.CLOSE;\n      }\n    }\n    return this.adjustTeXclass(prev);\n  }\n  /**\n   * Follow the TeXBook rules for adjusting the TeX class once its neighbors are known\n   *\n   * @param {MmlNode} prev  The node appearing before this one in the output\n   * @return {MmlNode}      The last node displayed (this node)\n   */\n  public adjustTeXclass(prev: MmlNode): MmlNode {\n    let texClass = this.texClass;\n    let prevClass = this.prevClass;\n    if (texClass === TEXCLASS.NONE) {\n      return prev;\n    }\n    if (prev) {\n      if (prev.getProperty('autoOP') && (texClass === TEXCLASS.BIN || texClass === TEXCLASS.REL)) {\n        prevClass = prev.texClass = TEXCLASS.ORD;\n      }\n      prevClass = this.prevClass = (prev.texClass || TEXCLASS.ORD);\n      this.prevLevel = this.attributes.getInherited('scriptlevel') as number;\n    } else {\n      prevClass = this.prevClass = TEXCLASS.NONE;\n    }\n    if (texClass === TEXCLASS.BIN &&\n        (prevClass === TEXCLASS.NONE || prevClass === TEXCLASS.BIN || prevClass === TEXCLASS.OP ||\n         prevClass === TEXCLASS.REL || prevClass === TEXCLASS.OPEN || prevClass === TEXCLASS.PUNCT)) {\n      this.texClass = TEXCLASS.ORD;\n    } else if (prevClass === TEXCLASS.BIN &&\n               (texClass === TEXCLASS.REL || texClass === TEXCLASS.CLOSE || texClass === TEXCLASS.PUNCT)) {\n      prev.texClass = this.prevClass = TEXCLASS.ORD;\n    } else if (texClass === TEXCLASS.BIN) {\n      //\n      // Check if node is the last one in its container since the rule\n      // above only takes effect if there is a node that follows.\n      //\n      let child: MmlNode = this;\n      let parent = this.parent;\n      while (parent && parent.parent && parent.isEmbellished &&\n             (parent.childNodes.length === 1 ||\n              (!parent.isKind('mrow') && parent.core() === child))) {\n        child = parent;\n        parent = parent.parent;\n      }\n      if (parent.childNodes[parent.childNodes.length - 1] === child) {\n        this.texClass = TEXCLASS.ORD;\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Do the normal inheritance, then look up the attributes from the operator dictionary.\n   * If there is no dictionary entry, get the TeX class from the Unicode range list.\n   *\n   * @override\n   */\n  public setInheritedAttributes(attributes: AttributeList = {},\n                                display: boolean = false, level: number = 0, prime: boolean = false) {\n    super.setInheritedAttributes(attributes, display, level, prime);\n    let mo = this.getText();\n    this.checkOperatorTable(mo);\n    this.checkPseudoScripts(mo);\n    this.checkPrimes(mo);\n    this.checkMathAccent(mo);\n  }\n\n  /**\n   * Set the attributes from the operator table\n   *\n   * @param {string} mo   The test of the mo element\n   */\n  protected checkOperatorTable(mo: string) {\n    let [form1, form2, form3] = this.handleExplicitForm(this.getForms());\n    this.attributes.setInherited('form', form1);\n    let OPTABLE = (this.constructor as typeof MmlMo).OPTABLE;\n    let def = OPTABLE[form1][mo] || OPTABLE[form2][mo] || OPTABLE[form3][mo];\n    if (def) {\n      if (this.getProperty('texClass') === undefined) {\n        this.texClass = def[2];\n      }\n      for (const name of Object.keys(def[3] || {})) {\n        this.attributes.setInherited(name, def[3][name]);\n      }\n      this.lspace = (def[0] + 1) / 18;\n      this.rspace = (def[1] + 1) / 18;\n    } else {\n      let range = getRange(mo);\n      if (range) {\n        if (this.getProperty('texClass') === undefined) {\n          this.texClass = range[2];\n        }\n        const spacing = (this.constructor as typeof MmlMo).MMLSPACING[range[2]];\n        this.lspace = (spacing[0] + 1) / 18;\n        this.rspace = (spacing[1] + 1) / 18;\n      }\n    }\n  }\n\n  /**\n   * @return {[string, string, string]}  The list of form attribute values in the\n   *                                     order they should be tested, based on the\n   *                                     position of the element in its parent.\n   */\n  public getForms(): [string, string, string] {\n    let core: MmlNode = this;\n    let parent = this.parent;\n    let Parent = this.Parent;\n    while (Parent && Parent.isEmbellished) {\n      core = parent;\n      parent = Parent.parent;\n      Parent = Parent.Parent;\n    }\n    if (parent && parent.isKind('mrow') && (parent as MmlMrow).nonSpaceLength() !== 1) {\n      if ((parent as MmlMrow).firstNonSpace() === core) {\n        return ['prefix', 'infix', 'postfix'];\n      }\n      if ((parent as MmlMrow).lastNonSpace() === core) {\n        return ['postfix', 'infix', 'prefix'];\n      }\n    }\n    return ['infix', 'prefix', 'postfix'];\n  }\n\n  /**\n   * @param {string[]} forms     The three forms in the default order they are to be tested\n   * @return {string[]}          The forms in the new order, if there is an explicit form attribute\n   */\n  protected handleExplicitForm(forms: string[]): string[] {\n    if (this.attributes.isSet('form')) {\n      const form = this.attributes.get('form') as string;\n      forms = [form].concat(forms.filter(name => (name !== form)));\n    }\n    return forms;\n  }\n\n  /**\n   * Mark the mo as a pseudoscript if it is one.  True means it is,\n   *   false means it is a pseudo-script character, but in an msup (so needs a variant form)\n   *\n   * @param {string} mo   The test of the mo element\n   */\n  protected checkPseudoScripts(mo: string) {\n    const PSEUDOSCRIPTS = (this.constructor as typeof MmlMo).pseudoScripts;\n    if (!mo.match(PSEUDOSCRIPTS)) return;\n    const parent = this.coreParent().Parent;\n    const isPseudo = !parent || !(parent.isKind('msubsup') && !parent.isKind('msub'));\n    this.setProperty('pseudoscript', isPseudo);\n    if (isPseudo) {\n      this.attributes.setInherited('lspace', 0);\n      this.attributes.setInherited('rspace', 0);\n    }\n  }\n\n  /**\n   * Determine whether the mo consists of primes, and remap them if so.\n   *\n   * @param {string} mo   The test of the mo element\n   */\n  protected checkPrimes(mo: string) {\n    const PRIMES = (this.constructor as typeof MmlMo).primes;\n    if (!mo.match(PRIMES)) return;\n    const REMAP = (this.constructor as typeof MmlMo).remapPrimes;\n    const primes = unicodeString(unicodeChars(mo).map(c => REMAP[c]));\n    this.setProperty('primes', primes);\n  }\n\n  /**\n   * Determine whether the mo is a mathaccent character\n   *\n   * @param {string} mo   The test of the mo element\n   */\n  protected checkMathAccent(mo: string) {\n    const parent = this.Parent;\n    if (this.getProperty('mathaccent') !== undefined || !parent || !parent.isKind('munderover')) return;\n    const base = parent.childNodes[0] as MmlNode;\n    if (base.isEmbellished && base.coreMO() === this) return;\n    const MATHACCENT = (this.constructor as typeof MmlMo).mathaccents;\n    if (mo.match(MATHACCENT)) {\n      this.setProperty('mathaccent', true);\n    }\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  The generic Factory class for creating arbitrary objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n/*****************************************************************/\n/**\n * The Factory node interfaces (one for the node instance, one for the node class)\n */\n\nexport interface FactoryNode {\n  readonly kind: string;\n}\n\n/**\n * @template N  The Node type being created by the factory\n */\nexport interface FactoryNodeClass<N extends FactoryNode> {\n  /**\n   * @param {Factory<N, FactoryNodeClass<N>>} factory  The factory for creating more nodes\n   * @param {any[]} args  Any additional arguments needed by the node\n   * @return {N}  The newly created node\n   */\n  new(factory: Factory<N, FactoryNodeClass<N>>, ...args: any[]): N;\n}\n\n/*****************************************************************/\n/**\n * The Factory interface\n *\n * Factory<N, C> takes a node type N and a node class C, which give\n * the interfaces for the node instance and the node constructors. We\n * need both for two reasons: first, you can't use typeof N to get C,\n * since N is a type not an object, and if N has static members, we\n * may want to access them from the results of getNodeClass(kind)\n * (this is done in MmlNodes, for example).\n *\n * @template N  The node type created by the factory\n * @template C  The class of the node being constructed (for access to static properties)\n */\nexport interface Factory<N extends FactoryNode, C extends FactoryNodeClass<N>> {\n  /**\n   * @param {string} kind  The kind of node to create\n   * @return {N}  The newly created node of the given kind\n   */\n  create(kind: string): N;\n\n  /**\n   * Defines a class for a given node kind\n   *\n   * @param {string} kind  The kind whose class is being defined\n   * @param {C} nodeClass  The class for the given kind\n   */\n  setNodeClass(kind: string, nodeClass: C): void;\n\n  /**\n   * @param {string} kind  The kind of node whose class is to be returned\n   * @return {C}  The class object for the given kind\n   */\n  getNodeClass(kind: string): C;\n\n  /**\n   * @param {string} kind  The kind whose definition is to be deleted\n   */\n  deleteNodeClass(kind: string): void;\n\n  /**\n   * @param {N} node  The node to test if it is of a given kind\n   * @param {string} kind  The kind to test for\n   * @return {boolean}  True if the node is of the given kind, false otherwise\n   */\n  nodeIsKind(node: N, kind: string): boolean;\n\n  /**\n   * @return {string[]}  The names of all the available kinds of nodes\n   */\n  getKinds(): string[];\n}\n\n\n/*****************************************************************/\n/**\n * The generic AbstractFactoryClass interface\n *   (needed for access to defaultNodes via the constructor)\n *\n * @template N  The node type created by the factory\n * @template C  The class of the node being constructed (for access to static properties)\n */\ninterface AbstractFactoryClass<N extends FactoryNode, C extends FactoryNodeClass<N>> extends Function {\n  defaultNodes: {[kind: string]: C};\n}\n\n\n/*****************************************************************/\n/**\n * The generic AbstractFactory class\n *\n * @template N  The node type created by the factory\n * @template C  The class of the node being constructed (for access to static properties)\n */\nexport abstract class AbstractFactory<N extends FactoryNode, C extends FactoryNodeClass<N>> implements Factory<N, C> {\n\n  /**\n   * The default collection of objects to use for the node map\n   */\n  public static defaultNodes = {};\n\n  /**\n   * The default kind\n   */\n  public defaultKind = 'unknown';\n\n  /**\n   * The map of node kinds to node classes\n   */\n  protected nodeMap: Map<string, C> = new Map();\n\n  /**\n   * An object containing functions for creating the various node kinds\n   */\n  protected node: {[kind: string]: (...args: any[]) => N} = {};\n\n  /**\n   * @override\n   */\n  constructor(nodes: {[kind: string]: C} = null) {\n    if (nodes === null) {\n      nodes = (this.constructor as AbstractFactoryClass<N, C>).defaultNodes;\n    }\n    for (const kind of Object.keys(nodes)) {\n      this.setNodeClass(kind, nodes[kind]);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public create(kind: string, ...args: any[]) {\n    return (this.node[kind] || this.node[this.defaultKind])(...args);\n  }\n\n  /**\n   * @override\n   */\n  public setNodeClass(kind: string, nodeClass: C) {\n    this.nodeMap.set(kind, nodeClass);\n    let THIS = this;\n    let KIND = this.nodeMap.get(kind);\n    this.node[kind] = (...args: any[]) => {\n      return new KIND(THIS, ...args);\n    };\n  }\n  /**\n   * @override\n   */\n  public getNodeClass(kind: string): C {\n    return this.nodeMap.get(kind);\n  }\n\n  /**\n   * @override\n   */\n  public deleteNodeClass(kind: string) {\n    this.nodeMap.delete(kind);\n    delete this.node[kind];\n  }\n\n  /**\n   * @override\n   */\n  public nodeIsKind(node: N, kind: string) {\n    return (node instanceof this.getNodeClass(kind));\n  }\n\n  /**\n   * @override\n   */\n  public getKinds() {\n    return Array.from(this.nodeMap.keys());\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,aAAgB,WAAW,GAAW,GAAS;AAC7C,aAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,MAAM,IAAI,IAAI,IAAI,IAAI,KAAK;IAClF;AAFA,YAAA,aAAA;AAUA,aAAgB,aAAa,MAAY;AACvC,aAAO,KAAK,QAAQ,8BAA8B,MAAM;IAC1D;AAFA,YAAA,eAAA;AAUA,aAAgB,aAAa,MAAY;AACvC,aAAO,MAAM,KAAK,IAAI,EAAE,IAAI,SAAC,GAAC;AAAK,eAAA,EAAE,YAAY,CAAC;MAAf,CAAgB;IACrD;AAFA,YAAA,eAAA;AAUA,aAAgB,cAAc,MAAc;AAC1C,aAAO,OAAO,cAAa,MAApB,QAAM,cAAA,CAAA,GAAA,OAAkB,IAAI,GAAA,KAAA,CAAA;IACrC;AAFA,YAAA,gBAAA;AAUA,aAAgB,UAAU,GAAS;AACjC,aAAO,CAAC,CAAC,EAAE,MAAM,OAAO;IAC1B;AAFA,YAAA,YAAA;AAUA,aAAgB,MAAM,GAAS;AAC7B,aAAO,EAAE,KAAI,EAAG,MAAM,KAAK;IAC7B;AAFA,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1DA,QAAA,uBAAA;AAcA,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAA,eAAA,MAAA;AAAlC,eAAAA,gBAAA;;MA2DA;AAhDS,MAAAA,cAAA,UAAA,UAAP,WAAA;;AAAe,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,EAAA,IAAA,UAAA,EAAA;;;AACb,mBAAmB,KAAA,SAAA,IAAI,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApB,gBAAM,OAAI,GAAA;AACb,gBAAI,SAAS,KAAK,KAAI,MAAT,MAAI,cAAA,CAAA,GAAA,OAAS,IAAI,GAAA,KAAA,CAAA;AAC9B,gBAAI,WAAW,OAAO;AACpB,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAmBO,MAAAA,cAAA,UAAA,eAAP,WAAA;AAAoB,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,EAAA,IAAA,UAAA,EAAA;;AAClB,YAAI,IAAI;AACR,YAAI,QAAQ,KAAK;AACjB,eAAO,IAAI,QAAQ,SAAC,IAAc,MAAc;AAC9C,WAAC,SAAS,UAAO;;AACf,mBAAO,EAAE,IAAI,MAAM,QAAQ;AACzB,kBAAI,UAAS,KAAA,MAAM,CAAC,GAAE,KAAI,MAAA,IAAA,cAAA,CAAA,GAAA,OAAI,IAAI,GAAA,KAAA,CAAA;AAClC,kBAAI,kBAAkB,SAAS;AAC7B,uBAAO,KAAK,OAAO,EAAE,MAAM,SAAA,KAAG;AAAI,yBAAA,KAAK,GAAG;gBAAR,CAAS;AAC3C;;AAEF,kBAAI,WAAW,OAAO;AACpB,mBAAG,KAAK;AACR;;;AAGJ,eAAG,IAAI;UACT,GAAE;QACJ,CAAC;MACH;AAEF,aAAAA;IAAA,EA3DkC,qBAAA,eAAe;AAApC,YAAA,eAAA;;;;;;;;;;;;;;;;;;;;;ACTA,YAAA,UAAU;AAUvB,QAAA,aAAA,WAAA;AAwBE,eAAAC,YAAY,UAAwB,QAAoB;AACtD,aAAK,SAAS;AACd,aAAK,WAAW,OAAO,OAAO,MAAM;AACpC,aAAK,YAAY,OAAO,OAAO,KAAK,QAAQ;AAC5C,aAAK,aAAa,OAAO,OAAO,KAAK,SAAS;AAC9C,eAAO,OAAO,KAAK,UAAU,QAAQ;MACvC;AAMO,MAAAA,YAAA,UAAA,MAAP,SAAW,MAAc,OAAe;AACtC,aAAK,WAAW,IAAI,IAAI;MAC1B;AAKO,MAAAA,YAAA,UAAA,UAAP,SAAe,MAAkB;AAC/B,eAAO,OAAO,KAAK,YAAY,IAAI;MACrC;AAMO,MAAAA,YAAA,UAAA,MAAP,SAAW,MAAY;AACrB,YAAI,QAAQ,KAAK,WAAW,IAAI;AAChC,YAAI,UAAU,QAAA,SAAS;AACrB,kBAAQ,KAAK,OAAO,IAAI;;AAE1B,eAAO;MACT;AAOO,MAAAA,YAAA,UAAA,cAAP,SAAmB,MAAY;AAC7B,YAAI,CAAC,KAAK,WAAW,eAAe,IAAI,GAAG;AACzC,iBAAO;;AAET,eAAO,KAAK,WAAW,IAAI;MAC7B;AAMO,MAAAA,YAAA,UAAA,UAAP,WAAA;;AAAe,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,gBAAA,EAAA,IAAA,UAAA,EAAA;;AACb,YAAI,SAAuB,CAAA;;AAC3B,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,SAAI,UAAA;AACb,mBAAO,MAAI,IAAI,KAAK,IAAI,MAAI;;;;;;;;;;;AAE9B,eAAO;MACT;AAMO,MAAAA,YAAA,UAAA,eAAP,SAAoB,MAAc,OAAe;AAC/C,aAAK,UAAU,IAAI,IAAI;MACzB;AAMO,MAAAA,YAAA,UAAA,eAAP,SAAoB,MAAY;AAC9B,eAAO,KAAK,UAAU,IAAI;MAC5B;AAMO,MAAAA,YAAA,UAAA,aAAP,SAAkB,MAAY;AAC5B,eAAO,KAAK,SAAS,IAAI;MAC3B;AAOO,MAAAA,YAAA,UAAA,QAAP,SAAa,MAAY;AACvB,eAAO,KAAK,WAAW,eAAe,IAAI,KAAK,KAAK,UAAU,eAAe,IAAI;MACnF;AAMO,MAAAA,YAAA,UAAA,aAAP,SAAkB,MAAY;AAC5B,eAAQ,QAAQ,KAAK;MACvB;AAKO,MAAAA,YAAA,UAAA,mBAAP,WAAA;AACE,eAAO,OAAO,KAAK,KAAK,UAAU;MACpC;AAKO,MAAAA,YAAA,UAAA,oBAAP,WAAA;AACE,eAAO,OAAO,KAAK,KAAK,SAAS;MACnC;AAKO,MAAAA,YAAA,UAAA,kBAAP,WAAA;AACE,eAAO,OAAO,KAAK,KAAK,QAAQ;MAClC;AAKO,MAAAA,YAAA,UAAA,iBAAP,WAAA;AACE,eAAO,OAAO,KAAK,KAAK,MAAM;MAChC;AAKO,MAAAA,YAAA,UAAA,mBAAP,WAAA;AACE,eAAO,KAAK;MACd;AAKO,MAAAA,YAAA,UAAA,kBAAP,WAAA;AACE,eAAO,KAAK;MACd;AAKO,MAAAA,YAAA,UAAA,iBAAP,WAAA;AACE,eAAO,KAAK;MACd;AAKO,MAAAA,YAAA,UAAA,gBAAP,WAAA;AACE,eAAO,KAAK;MACd;AAEF,aAAAA;IAAA,EApLA;AAAa,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8Gb,QAAA,eAAA,WAAA;AAyBE,eAAAC,cAAqB,SAAuC,YAA+B,UAAqB;;AAApD,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA6B;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAqB;AAA3F,aAAA,UAAA;AApBd,aAAA,SAAe;AAKZ,aAAA,aAA2B,CAAA;AAK9B,aAAA,aAAqB,CAAA;;AAW1B,mBAAmB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,gBAAM,SAAI,GAAA;AACb,iBAAK,YAAY,QAAM,WAAW,MAAI,CAAC;;;;;;;;;;;AAEzC,YAAI,SAAS,QAAQ;AACnB,eAAK,YAAY,QAAQ;;MAE7B;AAKA,aAAA,eAAWA,cAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,cAAA,UAAA,cAAP,SAAmB,MAAc,OAAe;AAC9C,aAAK,WAAW,IAAI,IAAI;MAC1B;AAKO,MAAAA,cAAA,UAAA,cAAP,SAAmB,MAAY;AAC7B,eAAO,KAAK,WAAW,IAAI;MAC7B;AAKO,MAAAA,cAAA,UAAA,mBAAP,WAAA;AACE,eAAO,OAAO,KAAK,KAAK,UAAU;MACpC;AAKO,MAAAA,cAAA,UAAA,mBAAP,WAAA;AACE,eAAO,KAAK;MACd;AAKO,MAAAA,cAAA,UAAA,iBAAP,WAAA;;AAAsB,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,gBAAA,EAAA,IAAA,UAAA,EAAA;;;AACpB,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,SAAI,UAAA;AACb,mBAAO,KAAK,WAAW,MAAI;;;;;;;;;;;MAE/B;AAMO,MAAAA,cAAA,UAAA,SAAP,SAAc,MAAY;AACxB,eAAO,KAAK,QAAQ,WAAW,MAAM,IAAI;MAC3C;AAMO,MAAAA,cAAA,UAAA,cAAP,SAAmB,UAAgB;;AACjC,aAAK,aAAa,CAAA;;AAClB,mBAAkB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAvB,gBAAI,QAAK,aAAA;AACZ,iBAAK,YAAY,KAAK;;;;;;;;;;;MAE1B;AAKO,MAAAA,cAAA,UAAA,cAAP,SAAmB,OAAW;AAC5B,aAAK,WAAW,KAAK,KAAK;AAC1B,cAAM,SAAS;AACf,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,eAAP,SAAoB,UAAgB,UAAc;AAChD,YAAI,IAAI,KAAK,WAAW,QAAQ;AAEhC,YAAI,MAAM,MAAM;AACd,eAAK,WAAW,CAAC,IAAI;AACrB,mBAAS,SAAS;AAClB,mBAAS,SAAS;;AAEpB,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,cAAP,SAAmB,OAAW;AAC5B,YAAM,IAAI,KAAK,WAAW,KAAK;AAC/B,YAAI,MAAM,MAAM;AACd,eAAK,WAAW,OAAO,GAAG,CAAC;AAC3B,gBAAM,SAAS;;AAEjB,eAAO;MACT;AAMO,MAAAA,cAAA,UAAA,aAAP,SAAkB,MAAU;AAC1B,YAAI,IAAI,KAAK,WAAW,QAAQ,IAAI;AACpC,eAAQ,MAAM,KAAK,OAAO;MAC5B;AAMO,MAAAA,cAAA,UAAA,OAAP,WAAA;;AACE,YAAM,OAAQ,KAAsB,QAAQ,OAAO,KAAK,IAAI;AAC5D,aAAK,aAAU,SAAA,CAAA,GAAO,KAAK,UAAU;;AACrC,mBAAoB,KAAA,SAAA,KAAK,cAAc,CAAA,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAtC,gBAAM,QAAK,GAAA;AACd,gBAAI,OAAO;AACT,mBAAK,YAAY,MAAM,KAAI,CAAE;;;;;;;;;;;;AAGjC,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,YAAP,SAAiB,MAAY;AAC3B,YAAI,QAAgB,CAAA;AACpB,aAAK,SAAS,SAAC,MAAU;AACvB,cAAI,KAAK,OAAO,IAAI,GAAG;AACrB,kBAAM,KAAK,IAAI;;QAEnB,CAAC;AACD,eAAO;MACT;AAMO,MAAAA,cAAA,UAAA,WAAP,SAAgB,MAAwC,MAAU;;AAChE,aAAK,MAAM,IAAI;;AACf,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,OAAO;AACT,oBAAM,SAAS,MAAM,IAAI;;;;;;;;;;;;AAG7B,eAAO;MACT;AAKO,MAAAA,cAAA,UAAA,WAAP,WAAA;AACE,eAAO,KAAK,OAAO,MAAM,KAAK,WAAW,KAAK,GAAG,IAAI;MACvD;AAEF,aAAAA;IAAA,EA9LA;AAAsB,YAAA,eAAA;AAqMtB,QAAA,oBAAA,SAAA,QAAA;AAAgD,gBAAAC,oBAAA,MAAA;AAAhD,eAAAA,qBAAA;;MAiDA;AAzCS,MAAAA,mBAAA,UAAA,cAAP,SAAmB,WAAiB;MACpC;AAKO,MAAAA,mBAAA,UAAA,cAAP,SAAmB,OAAW;AAC5B,eAAO;MACT;AAKO,MAAAA,mBAAA,UAAA,eAAP,SAAoB,WAAiB,UAAc;AACjD,eAAO;MACT;AAKO,MAAAA,mBAAA,UAAA,aAAP,SAAkB,OAAW;AAC3B,eAAO;MACT;AAOO,MAAAA,mBAAA,UAAA,WAAP,SAAgB,MAAwC,MAAU;AAChE,aAAK,MAAM,IAAI;AACf,eAAO;MACT;AAKO,MAAAA,mBAAA,UAAA,WAAP,WAAA;AACE,eAAO,KAAK;MACd;AAEF,aAAAA;IAAA,EAjDgD,YAAY;AAAtC,YAAA,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClUtB,QAAA,kBAAA;AACA,QAAA,YAAA;AAYa,YAAA,WAAW;MACtB,KAAO;MACP,IAAO;MACP,KAAO;MACP,KAAO;MACP,MAAO;MACP,OAAO;MACP,OAAO;MACP,OAAO;MACP,SAAS;MACT,MAAQ;;AAGG,YAAA,gBAAgB,CAAC,OAAO,MAAM,OAAO,OAAO,QAAQ,SAAS,SAAS,SAAS,SAAS;AAKrG,QAAM,iBAAiB,CAAC,IAAI,iBAAiB,mBAAmB,gBAAgB;AAKhF,QAAM,WAAW;MACf,CAAE,GAAG,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAC,IAAI,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAG,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;MAC/B,CAAE,GAAG,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,CAAC;;AAMpB,YAAA,mBAAmB;MAC9B;MAAe;MACf;MAAe;;AAsKjB,QAAA,kBAAA,SAAA,QAAA;AAA8C,gBAAAC,kBAAA,MAAA;AAoG5C,eAAAA,iBAAY,SAAqB,YAA+B,UAAwB;AAAvD,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA6B;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAwB;AAAxF,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AAzCT,cAAA,YAAoB;AAKpB,cAAA,YAAoB;AAyBjB,cAAA,WAAmB;AAY3B,YAAI,MAAK,QAAQ,GAAG;AAClB,gBAAK,aAAa,CAAC,QAAQ,OAAO,cAAc,CAAC;AACjD,gBAAK,WAAW,CAAC,EAAE,SAAS;;AAE9B,cAAK,YAAY,QAAQ;AACzB,cAAK,aAAa,IAAI,gBAAA,WACpB,QAAQ,aAAa,MAAK,IAAI,EAAE,UAChC,QAAQ,aAAa,MAAM,EAAE,QAAQ;AAEvC,cAAK,WAAW,QAAQ,UAAU;;MACpC;AASO,MAAAA,iBAAA,UAAA,OAAP,SAAY,SAAwB;;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAClC,YAAM,OAAO,KAAK,QAAQ,OAAO,KAAK,IAAI;AAC1C,aAAK,aAAU,SAAA,CAAA,GAAO,KAAK,UAAU;AACrC,YAAI,KAAK,YAAY;AACnB,cAAM,aAAa,KAAK,WAAW,iBAAgB;;AACnD,qBAAmB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,kBAAM,SAAI,GAAA;AACb,kBAAI,WAAS,QAAQ,SAAS;AAC5B,qBAAK,WAAW,IAAI,QAAM,WAAW,MAAI,CAAC;;;;;;;;;;;;;AAIhD,YAAI,KAAK,cAAc,KAAK,WAAW,QAAQ;AAC7C,cAAI,WAAW,KAAK;AACpB,cAAI,SAAS,WAAW,KAAK,SAAS,CAAC,EAAE,YAAY;AACnD,uBAAW,SAAS,CAAC,EAAE;;;AAEzB,qBAAoB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAzB,kBAAM,QAAK,aAAA;AACd,kBAAI,OAAO;AACT,qBAAK,YAAY,MAAM,KAAI,CAAa;qBACnC;AACL,qBAAK,WAAW,KAAK,IAAI;;;;;;;;;;;;;AAI/B,eAAO;MACT;AAKA,aAAA,eAAWA,iBAAA,WAAA,YAAQ;aAAnB,WAAA;AACE,iBAAO,KAAK;QACd;aAKA,SAAoB,UAAgB;AAClC,eAAK,WAAW;QAClB;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,WAAO;aAAlB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAQA,aAAA,eAAWA,iBAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,iBAAA,WAAA,UAAM;aAAjB,WAAA;AACE,cAAI,SAAS,KAAK;AAClB,iBAAO,UAAU,OAAO,WAAW;AACjC,qBAAS,OAAO;;AAElB,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,iBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AAOO,MAAAA,iBAAA,UAAA,cAAP,SAAmB,UAAmB;AACpC,YAAI,KAAK,QAAQ,GAAG;AAClB,iBAAO,KAAK,WAAW,CAAC,EAAE,YAAY,QAAQ;;AAEhD,eAAO,OAAA,UAAM,YAAW,KAAA,MAAC,QAAQ;MACnC;AAOO,MAAAA,iBAAA,UAAA,cAAP,SAAmB,OAAc;;AAAjC,YAAA,QAAA;AACE,YAAI,KAAK,QAAQ,GAAG;AAClB,eAAK,WAAW,CAAC,EAAE,YAAY,KAAK;AACpC,iBAAO;;AAET,YAAI,MAAM,YAAY;AAKpB,cAAI,KAAK,UAAU,UAAU;AAC3B,kBAAM,WAAW,QAAQ,SAAC,MAAI;AAAK,qBAAA,OAAA,UAAM,YAAW,KAAA,OAAC,IAAI;YAAtB,CAAuB;AAC1D,mBAAO;;AAKT,cAAM,WAAW;AACjB,kBAAQ,KAAK,QAAQ,OAAO,MAAM;AAClC,gBAAM,YAAY,SAAS,UAAU;AACrC,gBAAM,aAAa,SAAS;;AAC5B,qBAAmB,KAAA,SAAA,SAAS,iBAAgB,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA3C,kBAAM,SAAI,GAAA;AACb,oBAAM,YAAY,QAAM,SAAS,YAAY,MAAI,CAAC;;;;;;;;;;;;AAGtD,eAAO,OAAA,UAAM,YAAW,KAAA,MAAC,KAAK;MAChC;AAMO,MAAAA,iBAAA,UAAA,eAAP,SAAoB,UAAmB,UAAiB;AACtD,YAAI,KAAK,QAAQ,GAAG;AAClB,eAAK,WAAW,CAAC,EAAE,aAAa,UAAU,QAAQ;AAClD,iBAAO;;AAET,eAAO,OAAA,UAAM,aAAY,KAAA,MAAC,UAAU,QAAQ;MAC9C;AAKO,MAAAA,iBAAA,UAAA,OAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,iBAAA,UAAA,SAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,iBAAA,UAAA,YAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,iBAAA,UAAA,gBAAP,WAAA;;AACE,YAAI,QAAiB;AACrB,YAAI,SAAS,MAAM;AACnB,eAAO,UAAU,OAAO,WAAW;AACjC,kBAAQ;AACR,mBAAS,OAAO;;AAElB,YAAI,QAAQ;AACV,cAAI,IAAI;;AACR,qBAAmB,KAAA,SAAA,OAAO,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAjC,kBAAM,OAAI,GAAA;AACb,kBAAI,SAAS,OAAO;AAClB,uBAAO;;AAET;;;;;;;;;;;;AAGJ,eAAO;MACT;AAKO,MAAAA,iBAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,aAAK,aAAa,IAAI;AACtB,eAAQ,KAAK,YAAY,OAAO,OAAO;MACzC;AAMU,MAAAA,iBAAA,UAAA,iBAAV,SAAyB,MAAa;AACpC,YAAI,MAAM;AACR,eAAK,YAAY,KAAK;AACtB,eAAK,YAAY,KAAK;AACtB,eAAK,YAAY,KAAK,YAAY;AAClC,eAAK,WAAW,KAAK;;MAEzB;AAMU,MAAAA,iBAAA,UAAA,eAAV,SAAuB,MAAa;AAClC,YAAI,MAAM;AACR,eAAK,YAAY,KAAK;AACtB,eAAK,YAAY,KAAK,WAAW,IAAI,aAAa;;MAEtD;AAKO,MAAAA,iBAAA,UAAA,aAAP,WAAA;AACE,YAAI,YAAa,KAAK,aAAa,OAAO,KAAK,YAAY,QAAA,SAAS;AACpE,YAAI,WAAW,KAAK,YAAY,QAAA,SAAS;AACzC,YAAI,cAAc,QAAA,SAAS,QAAQ,aAAa,QAAA,SAAS,MAAM;AAC7D,iBAAO;;AAET,YAAI,cAAc,QAAA,SAAS,SAAS;AAClC,sBAAY,QAAA,SAAS;;AAEvB,YAAI,aAAa,QAAA,SAAS,SAAS;AACjC,qBAAW,QAAA,SAAS;;AAEtB,YAAI,QAAQ,SAAS,SAAS,EAAE,QAAQ;AACxC,aAAK,KAAK,YAAY,KAAK,KAAK,WAAW,IAAI,aAAa,IAAI,MAAM,SAAS,GAAG;AAChF,iBAAO;;AAET,eAAO,eAAe,KAAK,IAAI,KAAK,CAAC;MACvC;AAKO,MAAAA,iBAAA,UAAA,uBAAP,WAAA;AACE,eAAO,KAAK,iBAAiB,KAAK,OAAM,EAAG,qBAAoB;MACjE;AAgBO,MAAAA,iBAAA,UAAA,yBAAP,SAA8B,YACA,SAA0B,OAAmB,OAAsB;;AADnE,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA8B;AAC9B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAiB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAC/F,YAAI,WAAW,KAAK,WAAW,eAAc;;AAC7C,mBAAkB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAtC,gBAAM,MAAG,GAAA;AACZ,gBAAI,SAAS,eAAe,GAAG,KAAKA,iBAAgB,cAAc,eAAe,GAAG,GAAG;AACjF,kBAAA,KAAA,OAAgB,WAAW,GAAG,GAAC,CAAA,GAA9B,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAChB,kBAAI,aAAaA,iBAAgB,UAAU,IAAI,KAAK,CAAA,GAAI,KAAK,IAAI,KAAK,CAAA;AACtE,kBAAI,CAAC,UAAU,GAAG,GAAG;AACnB,qBAAK,WAAW,aAAa,KAAK,KAAK;;;;;;;;;;;;;AAI7C,YAAI,eAAe,KAAK,WAAW,YAAY,cAAc;AAC7D,YAAI,iBAAiB,QAAW;AAC9B,eAAK,WAAW,aAAa,gBAAgB,OAAO;;AAEtD,YAAI,cAAc,KAAK,WAAW,YAAY,aAAa;AAC3D,YAAI,gBAAgB,QAAW;AAC7B,eAAK,WAAW,aAAa,eAAe,KAAK;;AAEnD,YAAI,OAAO;AACT,eAAK,YAAY,iBAAiB,KAAK;;AAEzC,YAAI,QAAQ,KAAK;AACjB,YAAI,SAAS,KAAK,UAAU,aAAc,UAAU,KAAK,KAAK,WAAW,WAAW,KAC1C,UAAU,KAAK,KAAK,WAAW,WAAW,QAAS;AAK3F,cAAI,QAAQ,KAAK,WAAW,QAAQ;AAClC,iBAAK,aAAa,KAAK,WAAW,MAAM,GAAG,KAAK;iBAC3C;AACL,mBAAO,KAAK,WAAW,SAAS,OAAO;AACrC,mBAAK,YAAY,KAAK,QAAQ,OAAO,MAAM,CAAC;;;;AAIlD,aAAK,4BAA4B,YAAY,SAAS,OAAO,KAAK;MACpE;AAWU,MAAAA,iBAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;;;AAC9G,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,kBAAM,uBAAuB,YAAY,SAAS,OAAO,KAAK;;;;;;;;;;;MAElE;AAQU,MAAAA,iBAAA,UAAA,yBAAV,SAAiC,SAAwB,YAAwB;;AAC/E,YAAI,UAAO,SAAA,CAAA,GAAsB,OAAO;;AACxC,mBAAmB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,gBAAM,SAAI,GAAA;AACb,gBAAI,WAAS,kBAAkB,WAAS,iBAAiB,WAAS,SAAS;AACzE,sBAAQ,MAAI,IAAI,CAAC,KAAK,MAAM,WAAW,MAAI,CAAC;;;;;;;;;;;;AAGhD,eAAO;MACT;AAQO,MAAAA,iBAAA,UAAA,wBAAP,SAA6B,MAAa;AACxC,YAAM,aAAa,KAAK;AACxB,YAAM,UAAU,WAAW,IAAI,cAAc;AAC7C,YAAM,cAAc,WAAW,IAAI,aAAa;AAChD,YAAM,WAA2B,CAAC,WAAW,MAAM,UAAU,IAAI,CAAA,IAAK;UACpE,UAAU,CAAC,QAAQ,WAAW,IAAI,UAAU,CAAC;;AAE/C,YAAM,QAAQ,KAAK,YAAY,eAAe,KAAgB;AAC9D,aAAK,uBAAuB,UAAU,SAAS,aAAa,KAAK;MACnE;AAQO,MAAAA,iBAAA,UAAA,aAAP,SAAkB,SAA4B;AAA5B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAA4B;AAC5C,YAAI,YAAY,MAAM;AACpB;;AAEF,aAAK,iBAAiB,OAAO;AAC7B,YAAI,QAAQ,KAAK;AACjB,YAAI,QAAQ,YAAY,GAAG;AACzB,cAAI,SAAS,KAAK,UAAU,aACtB,UAAU,KAAK,KAAK,WAAW,WAAW,KAC1C,UAAU,KAAK,KAAK,WAAW,WAAW,QAAS;AACvD,iBAAK,OAAO,mCAAmC,KAAK,OAAO,UAAU,SAAS,IAAI;;;AAGtF,aAAK,eAAe,OAAO;MAC7B;AAOU,MAAAA,iBAAA,UAAA,mBAAV,SAA2B,SAAqB;;AAC9C,YAAI,QAAQ,iBAAiB,GAAG;AAC9B,cAAM,aAAa,KAAK;AACxB,cAAM,MAAM,CAAA;;AACZ,qBAAmB,KAAA,SAAA,WAAW,iBAAgB,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA7C,kBAAM,SAAI,GAAA;AACb,kBAAI,OAAK,OAAO,GAAG,CAAC,MAAM,WAAW,WAAW,WAAW,MAAI,MAAM,UACjE,CAAC,OAAK,MAAM,sCAAsC,GAAG;AAEvD,oBAAI,KAAK,MAAI;;;;;;;;;;;;AAIjB,cAAI,IAAI,QAAQ;AACd,iBAAK,OAAO,4BAA4B,KAAK,OAAO,YAAY,IAAI,KAAK,IAAI,GAAG,OAAO;;;MAG7F;AAOU,MAAAA,iBAAA,UAAA,iBAAV,SAAyB,SAAqB;;;AAC5C,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,kBAAM,WAAW,OAAO;;;;;;;;;;;MAE5B;AAUO,MAAAA,iBAAA,UAAA,SAAP,SAAc,SAAiB,SAAuB,OAAsB;AAAtB,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAC1E,YAAI,KAAK,UAAU,KAAK,OAAO,OAAO,QAAQ,GAAG;AAC/C,iBAAO;;AAET,YAAI,SAAS,KAAK,QAAQ,OAAO,QAAQ;AACzC,eAAO,WAAW,IAAI,oBAAoB,OAAO;AACjD,YAAI,QAAQ,YAAY,KAAK,OAAO;AAClC,cAAI,QAAQ,KAAK,QAAQ,OAAO,OAAO;AACvC,cAAI,OAAO,KAAK,QAAQ,OAAO,MAAM;AACrC,eAAK,QAAQ,QAAQ,YAAY,IAAI,UAAU,KAAK,IAAI;AACxD,gBAAM,YAAY,IAAI;AACtB,iBAAO,YAAY,KAAK;AACxB,eAAK,OAAO,aAAa,QAAQ,IAAI;eAChC;AACL,eAAK,OAAO,aAAa,QAAQ,IAAI;AACrC,iBAAO,YAAY,IAAI;;AAEzB,eAAO;MACT;AA9jBc,MAAAA,iBAAA,WAAyB;QACrC,gBAAgB,gBAAA;QAChB,WAAW,gBAAA;QACX,UAAU,gBAAA;QAEV,KAAK,gBAAA;;AAWO,MAAAA,iBAAA,YAAkF;QAC9F,QAAQ;UACN,SAAS,EAAC,OAAO,MAAM,QAAQ,MAAM,OAAO,MAAM,QAAQ,MAAM,SAAS,KAAI;UAC7E,QAAS,EAAC,OAAO,MAAM,QAAQ,MAAM,OAAO,MAAM,OAAO,KAAI;;QAE/D,aAAa;UACX,MAAM,EAAC,YAAY,KAAI;UACvB,QAAQ,EAAC,YAAY,KAAI;;;AAQf,MAAAA,iBAAA,gBAA2C;QACvD,eAAe;QACf,sBAAsB;;AAMV,MAAAA,iBAAA,iBAA+B;QAC3C,YAAY;QACZ,iBAAiB;QACjB,YAAY;QACZ,kBAAkB;QAClB,YAAY;;AAohBhB,aAAAA;MArkB8C,UAAA,YAAY;AAApC,YAAA,kBAAA;AA4kBtB,QAAA,uBAAA,SAAA,QAAA;AAAmD,gBAAAC,uBAAA,MAAA;AAAnD,eAAAA,wBAAA;;MA2DA;AA7CE,aAAA,eAAWA,sBAAA,WAAA,WAAO;aAAlB,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,sBAAA,UAAA,UAAP,WAAA;;AACE,YAAI,OAAO;;AACX,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,iBAAiB,UAAU;AAC7B,sBAAQ,MAAM,QAAO;;;;;;;;;;;;AAGzB,eAAO;MACT;AAOU,MAAAA,sBAAA,UAAA,8BAAV,SAAsC,YAA2B,SAAkB,OAAe,OAAc;;;AAC9G,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,iBAAiB,iBAAiB;AACpC,oBAAM,uBAAuB,YAAY,SAAS,OAAO,KAAK;;;;;;;;;;;;MAGpE;AAMO,MAAAA,sBAAA,UAAA,WAAP,SAAgB,MAAwC,MAAU;;AAChE,aAAK,MAAM,IAAI;;AACf,mBAAoB,KAAA,SAAA,KAAK,UAAU,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,QAAK,GAAA;AACd,gBAAI,iBAAiB,iBAAiB;AACpC,oBAAM,SAAS,MAAM,IAAI;;;;;;;;;;;;AAG7B,eAAO;MACT;AApDc,MAAAA,sBAAA,WAAQ,SAAA,SAAA,CAAA,GACf,gBAAgB,QAAQ,GAAA,EAC7B,aAAa,UACb,UAAU,gBAAA,QAAO,CAAA;AAmDrB,aAAAA;MA3DmD,eAAe;AAA5C,YAAA,uBAAA;AAsEtB,QAAA,wBAAA,SAAA,QAAA;AAAoD,gBAAAC,wBAAA,MAAA;AAApD,eAAAA,yBAAA;;MAkDA;AAxCE,aAAA,eAAWA,uBAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO,KAAK,WAAW,CAAC,EAAE;QAC5B;;;;AAKA,aAAA,eAAWA,uBAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO,KAAK,WAAW,CAAC,EAAE;QAC5B;;;;AAKA,aAAA,eAAWA,uBAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,uBAAA,UAAA,OAAP,WAAA;AACE,eAAO,KAAK,WAAW,CAAC;MAC1B;AAKO,MAAAA,uBAAA,UAAA,SAAP,WAAA;AACE,eAAO,KAAK,WAAW,CAAC,EAAE,OAAM;MAClC;AAKO,MAAAA,uBAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,eAAO,KAAK,WAAW,CAAC,EAAE,YAAY,IAAI;AAC1C,aAAK,eAAe,KAAK,WAAW,CAAC,CAAC;AACtC,eAAO;MACT;AA5Cc,MAAAA,uBAAA,WAAyB,gBAAgB;AA6CzD,aAAAA;MAlDoD,eAAe;AAA7C,YAAA,wBAAA;AA6DtB,QAAA,sBAAA,SAAA,QAAA;AAAkD,gBAAAC,sBAAA,MAAA;AAAlD,eAAAA,uBAAA;;MAqDA;AA3CE,aAAA,eAAWA,qBAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO,KAAK,WAAW,CAAC,EAAE;QAC5B;;;;AAKO,MAAAA,qBAAA,UAAA,OAAP,WAAA;AACE,eAAO,KAAK,WAAW,CAAC;MAC1B;AAKO,MAAAA,qBAAA,UAAA,SAAP,WAAA;AACE,eAAO,KAAK,WAAW,CAAC,EAAE,OAAM;MAClC;AAKO,MAAAA,qBAAA,UAAA,cAAP,SAAmB,MAAa;;AAC9B,aAAK,aAAa,IAAI;AACtB,aAAK,WAAW,QAAA,SAAS;AACzB,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,MAAM;AACR,cAAI,KAAK,iBAAiB,KAAK,OAAO,IAAI,GAAG;AAC3C,mBAAO,KAAK,YAAY,IAAI;AAC5B,iBAAK,eAAe,KAAK,KAAI,CAAE;iBAC1B;AACL,iBAAK,YAAY,IAAI;AACrB,mBAAO;;eAEJ;AACL,iBAAO;;;AAET,mBAAoB,KAAA,SAAA,KAAK,WAAW,MAAM,CAAC,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAM,QAAK,GAAA;AACd,gBAAI,OAAO;AACT,oBAAM,YAAY,IAAI;;;;;;;;;;;;AAG1B,eAAO;MACT;AA/Cc,MAAAA,qBAAA,WAAyB,gBAAgB;AAgDzD,aAAAA;MArDkD,eAAe;AAA3C,YAAA,sBAAA;AAgEtB,QAAA,uBAAA,SAAA,QAAA;AAAmD,gBAAAC,uBAAA,MAAA;AAAnD,eAAAA,wBAAA;;MA8KA;AApKE,aAAA,eAAWA,sBAAA,WAAA,WAAO;aAAlB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,eAAW;aAAtB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,sBAAkB;aAA7B,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,UAAM;aAAjB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,YAAQ;aAAnB,WAAA;AACE,iBAAO,QAAA,SAAS;QAClB;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO,QAAA,SAAS;QAClB;;;;AAKA,aAAA,eAAWA,sBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,sBAAA,UAAA,uBAAP,WAAA;AACE,eAAO;MACT;AAKA,aAAA,eAAWA,sBAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,sBAAA,UAAA,OAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,SAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,YAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,gBAAP,WAAA;AACE,eAAO;MACT;AAKO,MAAAA,sBAAA,UAAA,cAAP,SAAmB,MAAa;AAC9B,eAAO;MACT;AAIO,MAAAA,sBAAA,UAAA,aAAP,WAAA;AACE,eAAO;MACT;AAOO,MAAAA,sBAAA,UAAA,yBAAP,SAA8B,aAA4B,UAAmB,QAAgB,QAAe;MAAG;AAOxG,MAAAA,sBAAA,UAAA,wBAAP,SAA6B,OAAc;MAAG;AAOvC,MAAAA,sBAAA,UAAA,aAAP,SAAkB,UAAsB;MAAG;AAKpC,MAAAA,sBAAA,UAAA,SAAP,SAAc,UAAkB,UAAwB,QAAuB;AAAvB,YAAA,WAAA,QAAA;AAAA,mBAAA;QAAuB;AAC7E,eAAO;MACT;AAEF,aAAAA;IAAA,EA9KmD,UAAA,iBAAiB;AAA9C,YAAA,uBAAA;AAqLtB,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAIY,cAAA,OAAe;;MAuC3B;AAlCE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,UAAA,UAAA,UAAP,WAAA;AACE,eAAO,KAAK;MACd;AAMO,MAAAA,UAAA,UAAA,UAAP,SAAe,MAAY;AACzB,aAAK,OAAO;AACZ,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,OAAP,WAAA;AACE,eAAQ,KAAK,QAAQ,OAAO,KAAK,IAAI,EAAe,QAAQ,KAAK,QAAO,CAAE;MAC5E;AAKO,MAAAA,UAAA,UAAA,WAAP,WAAA;AACE,eAAO,KAAK;MACd;AAEF,aAAAA;IAAA,EA3C8B,oBAAoB;AAArC,YAAA,WAAA;AAmDb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAIY,cAAA,MAAc;AAKd,cAAA,UAAqC;;MAgDjD;AA3CE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,SAAA,UAAA,SAAP,WAAA;AACE,eAAO,KAAK;MACd;AAOO,MAAAA,SAAA,UAAA,SAAP,SAAc,KAAa,SAAyC;AAAzC,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAyC;AAClE,aAAK,MAAM;AACX,aAAK,UAAU;AACf,eAAO;MACT;AAKO,MAAAA,SAAA,UAAA,mBAAP,WAAA;AACE,eAAO,KAAK,QAAQ,aAAa,KAAK,GAAG;MAC3C;AAKO,MAAAA,SAAA,UAAA,OAAP,WAAA;AACE,eAAQ,KAAK,QAAQ,OAAO,KAAK,IAAI,EAAc,OAAO,KAAK,QAAQ,MAAM,KAAK,GAAG,CAAC;MACxF;AAKO,MAAAA,SAAA,UAAA,WAAP,WAAA;AACE,eAAO;MACT;AAEF,aAAAA;IAAA,EAzD6B,oBAAoB;AAApC,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;AChtCb,QAAA,eAAA;AAgBA,aAAgB,MAAM,QAAgB,QAAgB,UAChC,YAA+B;AADC,UAAA,aAAA,QAAA;AAAA,mBAAmB,aAAA,SAAS;MAAG;AAC/D,UAAA,eAAA,QAAA;AAAA,qBAAA;MAA+B;AAC7B,aAAO,CAAC,QAAQ,QAAQ,UAAU,UAAU;IAC9C;AAHtB,YAAA,QAAA;AAQa,YAAA,KAAK;MAChB,KAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,IAAI;MACrC,IAAY,MAAM,GAAG,GAAG,aAAA,SAAS,IAAI,EAAC,SAAS,MAAM,eAAe,MAAM,WAAW,KAAI,CAAC;MAC1F,SAAY,MAAM,GAAG,GAAG,aAAA,SAAS,IAAI,EAAC,SAAS,MAAM,eAAe,KAAI,CAAC;MACzE,UAAY,MAAM,GAAG,GAAG,aAAA,SAAS,IAAI,EAAC,SAAS,MAAM,WAAW,KAAI,CAAC;MACrE,WAAY,MAAM,GAAG,GAAG,aAAA,SAAS,IAAI,EAAC,SAAS,MAAM,WAAW,KAAI,CAAC;MACrE,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,SAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;MACtD,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,SAAS,MAAM,eAAe,KAAI,CAAC;MAC1E,KAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;MACtD,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;MACpC,YAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;MACtD,WAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,QAAQ,KAAI,CAAC;MACpD,SAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,QAAQ,MAAM,UAAU,KAAI,CAAC;MACpE,MAAY,MAAM,GAAG,GAAG,aAAA,SAAS,MAAM,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;MACrF,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;MACtF,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK;MACtC,OAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK;MACtC,QAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,QAAQ,KAAI,CAAC;MACpD,YAAY,MAAM,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,QAAQ,MAAM,UAAU,KAAI,CAAC;;AAMzD,YAAA,SAAqB;MAChC,CAAC,IAAQ,KAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,KAAQ,KAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,KAAQ,KAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,KAAQ,KAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,KAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,MAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,MAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,OAAO;MACtC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,MAAM,QAAQ;MAC7C,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,MAAM,QAAQ;MAC7C,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,MAAM,QAAQ;MAC7C,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAQ,aAAA,SAAS,KAAK,IAAI;MACnC,CAAC,OAAQ,OAAS,aAAA,SAAS,KAAK,IAAI;MACpC,CAAC,OAAS,OAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,OAAS,OAAS,aAAA,SAAS,KAAK,MAAM,QAAQ;MAC/C,CAAC,OAAS,OAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,OAAS,QAAS,aAAA,SAAS,KAAK,MAAM,QAAQ;MAC/C,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,IAAI;MACrC,CAAC,QAAS,QAAS,aAAA,SAAS,KAAK,MAAM,SAAS;;AASlD,aAAgB,SAAS,MAAY;;AACnC,UAAM,IAAI,KAAK,YAAY,CAAC;;AAC5B,iBAAoB,WAAA,SAAA,QAAA,MAAM,GAAA,aAAA,SAAA,KAAA,GAAA,CAAA,WAAA,MAAA,aAAA,SAAA,KAAA,GAAE;AAAvB,cAAM,QAAK,WAAA;AACd,cAAI,KAAK,MAAM,CAAC,GAAG;AACjB,gBAAI,KAAK,MAAM,CAAC,GAAG;AACjB,qBAAO;;AAET;;;;;;;;;;;;AAGJ,aAAO;IACT;AAXA,YAAA,WAAA;AAgBa,YAAA,aAAa;MACxB,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;MACL,CAAC,GAAG,CAAC;;AAMM,YAAA,UAA0C;MACrD,QAAQ;QACN,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,MAAM,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QACzE,OAAO,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QAC1E,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,KAAI,CAAC;QAC5D,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,MAAM,EAAC,OAAO,KAAI,CAAC;QAC7C,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,MAAM,EAAC,OAAO,KAAI,CAAC;QAC7C,KAAU,QAAA,GAAG;QACb,KAAU,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;QAClC,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;QAC/C,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,KAAI,CAAC;QAC5D,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;;MAEf,SAAS;QACP,MAAM,MAAM,GAAG,CAAC;QAChB,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,IAAI;QAChC,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,MAAM,MAAM,GAAG,CAAC;QAChB,MAAM,MAAM,GAAG,CAAC;QAChB,MAAM,MAAM,GAAG,CAAC;QAChB,OAAO,QAAA,GAAG;QACV,KAAM,QAAA,GAAG;QACT,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,MAAM,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QACzE,OAAO,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QAC1E,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,KAAI,CAAC;QAC5D,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,OAAO,KAAI,CAAC;QAC9C,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,OAAO,KAAI,CAAC;QAC9C,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,KAAI,CAAC;QAC5D,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;;MAEf,OAAO;QACL,MAAM,QAAA,GAAG;QACT,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,IAAI;QAC9B,MAAM,QAAA,GAAG;QACT,IAAI,QAAA,GAAG;QACP,KAAK,QAAA,GAAG;QACR,MAAM,MAAM,GAAG,CAAC;QAChB,MAAM,QAAA,GAAG;QACT,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,gBAAgB,SAAS,WAAW,KAAI,CAAC;QACtE,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,MAAM,QAAA,GAAG;QACT,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,WAAW,KAAI,CAAC;QAC7C,KAAK,QAAA,GAAG;QACR,MAAM,MAAM,GAAG,CAAC;QAChB,MAAM,QAAA,GAAG;QACT,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,IAAI;QAC9B,MAAM,QAAA,GAAG;QACT,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,EAAC,gBAAgB,SAAS,WAAW,KAAI,CAAC;QACtE,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,MAAM,MAAM,GAAG,CAAC;QAChB,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,IAAI;QAChC,KAAK,QAAA,GAAG;QACR,MAAM,QAAA,GAAG;QACT,KAAK,QAAA,GAAG;QACR,KAAK,QAAA,GAAG;QACR,KAAK,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QACxE,MAAM,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QACzE,OAAO,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,KAAI,CAAC;QAC1E,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;QAC/C,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;QAC/C,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,MAAM,EAAC,gBAAgB,SAAS,WAAW,KAAI,CAAC;QAC1E,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,MAAM,GAAG,GAAG,aAAA,SAAS,GAAG;QAClC,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,OAAO,IAAI;QACrC,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,CAAC,GAAG,GAAG,aAAA,SAAS,KAAK,EAAC,UAAU,KAAI,CAAC;QAC/C,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,MAAgB,QAAA,GAAG;QACnB,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;QACb,KAAU,QAAA,GAAG;;;AAOjB,YAAA,QAAQ,MAAM,GAAG,IAAI,QAAA,GAAG;AACxB,YAAA,QAAQ,MAAM,GAAG,IAAI,QAAA,GAAG;AAKxB,YAAA,QAAQ,MAAM,GAAQ,IAAI,QAAA,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClyC7B,QAAA,eAAA;AAGA,QAAA,0BAAA;AACA,QAAA,cAAA;AAOA,QAAA,QAAA,SAAA,QAAA;AAA2B,gBAAAC,QAAA,MAAA;AAA3B,eAAAA,SAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AA6GS,cAAA,YAAoB;AA6BpB,cAAA,SAAS,IAAE;AAMX,cAAA,SAAS,IAAE;;MAwSpB;AApUE,aAAA,eAAWA,OAAA,WAAA,YAAQ;aAAnB,WAAA;AACE,cAAI,KAAK,cAAc,MAAM;AAC3B,gBAAI,KAAK,KAAK,QAAO;AACjB,gBAAA,KAAA,OAAwB,KAAK,mBAAmB,KAAK,SAAQ,CAAE,GAAC,CAAA,GAA/D,QAAK,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AACxB,gBAAI,YAAW,KAAK,YAA6B;AACjD,gBAAI,MAAM,UAAQ,KAAK,EAAE,EAAE,KAAK,UAAQ,KAAK,EAAE,EAAE,KAAK,UAAQ,KAAK,EAAE,EAAE;AACvE,mBAAO,MAAM,IAAI,CAAC,IAAI,aAAA,SAAS;;AAEjC,iBAAO,KAAK;QACd;aAKA,SAAoB,OAAa;AAC/B,eAAK,YAAY;QACnB;;;;AAiBA,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAWA,OAAA,WAAA,iBAAa;aAAxB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,OAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO,KAAK,WAAW,IAAI,WAAW,MAAM;QAC9C;;;;AAMO,MAAAA,OAAA,UAAA,aAAP,WAAA;AACE,YAAI,cAAc;AAClB,YAAI,SAAS;AACb,YAAI,OAAO,KAAK,QAAQ,aAAa,MAAM;AAC3C,eAAO,UAAU,OAAO,iBAAiB,OAAO,OAAM,MAAO,QAAQ,EAAE,kBAAkB,OAAO;AAC9F,wBAAc;AACd,mBAAU,OAAmB;;AAE/B,eAAO;MACT;AAMO,MAAAA,OAAA,UAAA,WAAP,SAAgB,QAAe;AAC7B,YAAI,CAAC,QAAQ;AACX,iBAAO;;AAET,YAAI,OAAO,eAAe;AACxB,iBAAQ,OAAO,OAAM,EAAa,QAAO;;AAE3C,iBAAU,OAAO,OAAO,MAAM,KACnB,OAAO,OAAO,SAAS,KAAK,OAAO,aAAa,aAAA,SAAS,WAC1D,OAAO,OAAO,QAAQ,KACtB,OAAO,OAAO,UAAU,MAAM,OAAO,WAAW,WAAW,KAC7D,OAAO,OAAO,YAAY,MAAM,OAAO,WAAW,CAAC,GAAG;AAC5D,mBAAS,OAAO,WAAW,CAAC;;AAE9B,eAAQ,OAAO,UAAW,OAAgC,QAAO,IAAK;MACxE;AAKO,MAAAA,OAAA,UAAA,uBAAP,WAAA;AACE,eAAO,KAAK,WAAW,MAAM,QAAQ,KACnC,KAAK,WAAW,MAAM,QAAQ;MAClC;AAKA,aAAA,eAAIA,OAAA,WAAA,YAAQ;aAAZ,WAAA;AACE,cAAI,SAAS;AACb,cAAM,OAAO,KAAK,WAAU,EAAG;AAC/B,cAAI,MAAM;AACR,gBAAM,MAAO,KAAK,OAAO,OAAO,IACjB,KAAK,WAAY,KAAkB,IAAI,EAAc,OAAM,IAC5D,WAAW,KACZ,KAAK,OAAO,QAAQ,IAClB,KAAK,WAAY,KAAmB,KAAK,EAAc,OAAM,IAC9D,gBAAgB,KACjB,KAAK,OAAO,YAAY,IACvB,SAAU,KAAK,WAAY,KAAuB,IAAI,EAAc,OAAM,IAC1E,WACA,SAAU,KAAK,WAAY,KAAuB,KAAK,EAAc,OAAM,IAC3E,gBAAgB,KACjB;AACb,gBAAI,KAAK;AACP,kBAAM,QAAQ,KAAK,WAAW,YAAY,GAAG;AAC7C,uBAAU,UAAU,SAAY,SAAS,KAAK,WAAW,IAAI,QAAQ;;;AAGzE,iBAAO;QACT;;;;AAOO,MAAAA,OAAA,UAAA,cAAP,SAAmB,MAAa;AAC1B,YAAA,KAAgB,KAAK,WAAW,QAAQ,QAAQ,OAAO,GAAtD,OAAI,GAAA,MAAE,QAAK,GAAA;AAChB,YAAI,KAAK,YAAY,UAAU,MAAM,WAChC,KAAK,WAAW,MAAM,QAAQ,KAAK,KAAK,WAAW,MAAM,QAAQ,IAAI;AACxE,iBAAO;;AAET,YAAI,SAAS,KAAK,aAAa,aAAA,SAAS,KAAK;AAC3C,cAAI,SAAS,UAAU;AACrB,iBAAK,WAAW,aAAA,SAAS;;AAE3B,cAAI,SAAS,WAAW;AACtB,iBAAK,WAAW,aAAA,SAAS;;;AAG7B,eAAO,KAAK,eAAe,IAAI;MACjC;AAOO,MAAAA,OAAA,UAAA,iBAAP,SAAsB,MAAa;AACjC,YAAI,WAAW,KAAK;AACpB,YAAI,YAAY,KAAK;AACrB,YAAI,aAAa,aAAA,SAAS,MAAM;AAC9B,iBAAO;;AAET,YAAI,MAAM;AACR,cAAI,KAAK,YAAY,QAAQ,MAAM,aAAa,aAAA,SAAS,OAAO,aAAa,aAAA,SAAS,MAAM;AAC1F,wBAAY,KAAK,WAAW,aAAA,SAAS;;AAEvC,sBAAY,KAAK,YAAa,KAAK,YAAY,aAAA,SAAS;AACxD,eAAK,YAAY,KAAK,WAAW,aAAa,aAAa;eACtD;AACL,sBAAY,KAAK,YAAY,aAAA,SAAS;;AAExC,YAAI,aAAa,aAAA,SAAS,QACrB,cAAc,aAAA,SAAS,QAAQ,cAAc,aAAA,SAAS,OAAO,cAAc,aAAA,SAAS,MACpF,cAAc,aAAA,SAAS,OAAO,cAAc,aAAA,SAAS,QAAQ,cAAc,aAAA,SAAS,QAAQ;AAC/F,eAAK,WAAW,aAAA,SAAS;mBAChB,cAAc,aAAA,SAAS,QACtB,aAAa,aAAA,SAAS,OAAO,aAAa,aAAA,SAAS,SAAS,aAAa,aAAA,SAAS,QAAQ;AACpG,eAAK,WAAW,KAAK,YAAY,aAAA,SAAS;mBACjC,aAAa,aAAA,SAAS,KAAK;AAKpC,cAAI,QAAiB;AACrB,cAAI,WAAS,KAAK;AAClB,iBAAO,YAAU,SAAO,UAAU,SAAO,kBACjC,SAAO,WAAW,WAAW,KAC5B,CAAC,SAAO,OAAO,MAAM,KAAK,SAAO,KAAI,MAAO,QAAS;AAC5D,oBAAQ;AACR,uBAAS,SAAO;;AAElB,cAAI,SAAO,WAAW,SAAO,WAAW,SAAS,CAAC,MAAM,OAAO;AAC7D,iBAAK,WAAW,aAAA,SAAS;;;AAG7B,eAAO;MACT;AAQO,MAAAA,OAAA,UAAA,yBAAP,SAA8B,YACA,SAA0B,OAAmB,OAAsB;AADnE,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAA8B;AAC9B,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAiB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAC/F,eAAA,UAAM,uBAAsB,KAAA,MAAC,YAAY,SAAS,OAAO,KAAK;AAC9D,YAAI,KAAK,KAAK,QAAO;AACrB,aAAK,mBAAmB,EAAE;AAC1B,aAAK,mBAAmB,EAAE;AAC1B,aAAK,YAAY,EAAE;AACnB,aAAK,gBAAgB,EAAE;MACzB;AAOU,MAAAA,OAAA,UAAA,qBAAV,SAA6B,IAAU;;AACjC,YAAA,KAAA,OAAwB,KAAK,mBAAmB,KAAK,SAAQ,CAAE,GAAC,CAAA,GAA/D,QAAK,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AACxB,aAAK,WAAW,aAAa,QAAQ,KAAK;AAC1C,YAAI,UAAW,KAAK,YAA6B;AACjD,YAAI,MAAM,QAAQ,KAAK,EAAE,EAAE,KAAK,QAAQ,KAAK,EAAE,EAAE,KAAK,QAAQ,KAAK,EAAE,EAAE;AACvE,YAAI,KAAK;AACP,cAAI,KAAK,YAAY,UAAU,MAAM,QAAW;AAC9C,iBAAK,WAAW,IAAI,CAAC;;;AAEvB,qBAAmB,KAAA,SAAA,OAAO,KAAK,IAAI,CAAC,KAAK,CAAA,CAAE,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,kBAAM,SAAI,GAAA;AACb,mBAAK,WAAW,aAAa,QAAM,IAAI,CAAC,EAAE,MAAI,CAAC;;;;;;;;;;;AAEjD,eAAK,UAAU,IAAI,CAAC,IAAI,KAAK;AAC7B,eAAK,UAAU,IAAI,CAAC,IAAI,KAAK;eACxB;AACL,cAAI,SAAQ,GAAA,wBAAA,UAAS,EAAE;AACvB,cAAI,OAAO;AACT,gBAAI,KAAK,YAAY,UAAU,MAAM,QAAW;AAC9C,mBAAK,WAAW,MAAM,CAAC;;AAEzB,gBAAM,UAAW,KAAK,YAA6B,WAAW,MAAM,CAAC,CAAC;AACtE,iBAAK,UAAU,QAAQ,CAAC,IAAI,KAAK;AACjC,iBAAK,UAAU,QAAQ,CAAC,IAAI,KAAK;;;MAGvC;AAOO,MAAAA,OAAA,UAAA,WAAP,WAAA;AACE,YAAI,OAAgB;AACpB,YAAI,SAAS,KAAK;AAClB,YAAI,SAAS,KAAK;AAClB,eAAO,UAAU,OAAO,eAAe;AACrC,iBAAO;AACP,mBAAS,OAAO;AAChB,mBAAS,OAAO;;AAElB,YAAI,UAAU,OAAO,OAAO,MAAM,KAAM,OAAmB,eAAc,MAAO,GAAG;AACjF,cAAK,OAAmB,cAAa,MAAO,MAAM;AAChD,mBAAO,CAAC,UAAU,SAAS,SAAS;;AAEtC,cAAK,OAAmB,aAAY,MAAO,MAAM;AAC/C,mBAAO,CAAC,WAAW,SAAS,QAAQ;;;AAGxC,eAAO,CAAC,SAAS,UAAU,SAAS;MACtC;AAMU,MAAAA,OAAA,UAAA,qBAAV,SAA6B,OAAe;AAC1C,YAAI,KAAK,WAAW,MAAM,MAAM,GAAG;AACjC,cAAM,SAAO,KAAK,WAAW,IAAI,MAAM;AACvC,kBAAQ,CAAC,MAAI,EAAE,OAAO,MAAM,OAAO,SAAA,MAAI;AAAI,mBAAC,SAAS;UAAV,CAAe,CAAC;;AAE7D,eAAO;MACT;AAQU,MAAAA,OAAA,UAAA,qBAAV,SAA6B,IAAU;AACrC,YAAM,gBAAiB,KAAK,YAA6B;AACzD,YAAI,CAAC,GAAG,MAAM,aAAa;AAAG;AAC9B,YAAM,SAAS,KAAK,WAAU,EAAG;AACjC,YAAM,WAAW,CAAC,UAAU,EAAE,OAAO,OAAO,SAAS,KAAK,CAAC,OAAO,OAAO,MAAM;AAC/E,aAAK,YAAY,gBAAgB,QAAQ;AACzC,YAAI,UAAU;AACZ,eAAK,WAAW,aAAa,UAAU,CAAC;AACxC,eAAK,WAAW,aAAa,UAAU,CAAC;;MAE5C;AAOU,MAAAA,OAAA,UAAA,cAAV,SAAsB,IAAU;AAC9B,YAAM,SAAU,KAAK,YAA6B;AAClD,YAAI,CAAC,GAAG,MAAM,MAAM;AAAG;AACvB,YAAM,QAAS,KAAK,YAA6B;AACjD,YAAM,UAAS,GAAA,YAAA,gBAAc,GAAA,YAAA,cAAa,EAAE,EAAE,IAAI,SAAA,GAAC;AAAI,iBAAA,MAAM,CAAC;QAAP,CAAQ,CAAC;AAChE,aAAK,YAAY,UAAU,MAAM;MACnC;AAOU,MAAAA,OAAA,UAAA,kBAAV,SAA0B,IAAU;AAClC,YAAM,SAAS,KAAK;AACpB,YAAI,KAAK,YAAY,YAAY,MAAM,UAAa,CAAC,UAAU,CAAC,OAAO,OAAO,YAAY;AAAG;AAC7F,YAAM,OAAO,OAAO,WAAW,CAAC;AAChC,YAAI,KAAK,iBAAiB,KAAK,OAAM,MAAO;AAAM;AAClD,YAAM,aAAc,KAAK,YAA6B;AACtD,YAAI,GAAG,MAAM,UAAU,GAAG;AACxB,eAAK,YAAY,cAAc,IAAI;;MAEvC;AAjbc,MAAAA,OAAA,WAAQ,SAAA,SAAA,CAAA,GACjB,aAAA,qBAAqB,QAAQ,GAAA,EAChC,MAAM,SACN,OAAO,OACP,WAAW,OACX,QAAQ,kBACR,QAAQ,kBACR,UAAU,OACV,WAAW,OACX,SAAS,YACT,SAAS,OACT,SAAS,OACT,eAAe,OACf,QAAQ,OACR,WAAW,QACX,aAAa,OACb,gBAAgB,UAChB,aAAa,QACb,aAAa,KACb,cAAc,IACd,kBAAkB,eAClB,kBAAkB,eAClB,iBAAiB,eACjB,iBAAiB,cAAa,CAAA;AAMlB,MAAAA,OAAA,aAAa,wBAAA;AAKb,MAAAA,OAAA,UAA0C,wBAAA;AAK1C,MAAAA,OAAA,gBAAgB,IAAI,OAAO;QACvC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK,EAAE,CAAC;AAKQ,MAAAA,OAAA,SAAS,IAAI,OAAO;QACnC;QACA;QACA;QACA,KAAK,EAAE,CAAC;AAKM,MAAAA,OAAA,cAAqC;QACnD,IAAQ;QACR,IAAQ;QACR,IAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;QACR,MAAQ;;AAMM,MAAAA,OAAA,cAAc,IAAI,OAAO;QACxC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK,EAAE,CAAC;AAgVZ,aAAAA;MAxb2B,aAAA,oBAAoB;AAAlC,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmFb,QAAA,kBAAA,WAAA;AAyBE,eAAAC,iBAAY,OAAiC;;AAAjC,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAiC;AAftC,aAAA,cAAc;AAKX,aAAA,UAA0B,oBAAI,IAAG;AAKjC,aAAA,OAAgD,CAAA;AAMxD,YAAI,UAAU,MAAM;AAClB,kBAAS,KAAK,YAA2C;;;AAE3D,mBAAmB,KAAA,SAAA,OAAO,KAAK,KAAK,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAlC,gBAAM,OAAI,GAAA;AACb,iBAAK,aAAa,MAAM,MAAM,IAAI,CAAC;;;;;;;;;;;MAEvC;AAKO,MAAAA,iBAAA,UAAA,SAAP,SAAc,MAAY;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC1B,gBAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,WAAW,GAAE,MAAA,QAAA,cAAA,CAAA,GAAA,OAAI,IAAI,GAAA,KAAA,CAAA;MACjE;AAKO,MAAAA,iBAAA,UAAA,eAAP,SAAoB,MAAc,WAAY;AAC5C,aAAK,QAAQ,IAAI,MAAM,SAAS;AAChC,YAAI,OAAO;AACX,YAAI,OAAO,KAAK,QAAQ,IAAI,IAAI;AAChC,aAAK,KAAK,IAAI,IAAI,WAAA;AAAC,cAAA,OAAA,CAAA;mBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,iBAAA,EAAA,IAAA,UAAA,EAAA;;AACjB,iBAAA,KAAW,KAAI,KAAA,MAAJ,MAAI,cAAA,CAAA,QAAC,IAAI,GAAA,OAAK,IAAI,GAAA,KAAA,CAAA,GAAA;QAC/B;MACF;AAIO,MAAAA,iBAAA,UAAA,eAAP,SAAoB,MAAY;AAC9B,eAAO,KAAK,QAAQ,IAAI,IAAI;MAC9B;AAKO,MAAAA,iBAAA,UAAA,kBAAP,SAAuB,MAAY;AACjC,aAAK,QAAQ,OAAO,IAAI;AACxB,eAAO,KAAK,KAAK,IAAI;MACvB;AAKO,MAAAA,iBAAA,UAAA,aAAP,SAAkB,MAAS,MAAY;AACrC,eAAQ,gBAAgB,KAAK,aAAa,IAAI;MAChD;AAKO,MAAAA,iBAAA,UAAA,WAAP,WAAA;AACE,eAAO,MAAM,KAAK,KAAK,QAAQ,KAAI,CAAE;MACvC;AA1Ec,MAAAA,iBAAA,eAAe,CAAA;AA4E/B,aAAAA;MAjFA;AAAsB,YAAA,kBAAA;;;", "names": ["FunctionList", "Attributes", "AbstractNode", "AbstractEmptyNode", "AbstractMmlNode", "AbstractMmlTokenNode", "AbstractMmlLayoutNode", "AbstractMmlBaseNode", "AbstractMmlEmptyNode", "TextNode", "XMLNode", "MmlMo", "AbstractFactory"]}