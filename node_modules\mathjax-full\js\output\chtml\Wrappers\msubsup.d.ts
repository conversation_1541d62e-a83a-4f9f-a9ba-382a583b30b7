import { CHTMLWrapper, Constructor } from '../Wrapper.js';
import { CHTMLscriptbase } from './scriptbase.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmsub_base: import("../../common/Wrappers/msubsup.js").MsubConstructor<CHTMLWrapper<any, any, any>> & Constructor<CHTMLscriptbase<any, any, any>>;
export declare class CHTMLmsub<N, T, D> extends CHTMLmsub_base {
    static kind: string;
}
declare const CHTMLmsup_base: import("../../common/Wrappers/msubsup.js").MsupConstructor<CHTMLWrapper<any, any, any>> & Constructor<CHTMLscriptbase<any, any, any>>;
export declare class CHTMLmsup<N, T, D> extends CHTMLmsup_base {
    static kind: string;
}
declare const CHTMLmsubsup_base: import("../../common/Wrappers/msubsup.js").MsubsupConstructor<CHTMLWrapper<any, any, any>> & Constructor<CHTMLscriptbase<any, any, any>>;
export declare class CHTMLmsubsup<N, T, D> extends CHTMLmsubsup_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
}
export {};
