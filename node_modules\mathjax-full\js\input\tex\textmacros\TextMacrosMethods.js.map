{"version": 3, "file": "TextMacrosMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/textmacros/TextMacrosMethods.ts"], "names": [], "mappings": ";;;;;;AAwBA,iEAAwC;AACxC,uDAAoD;AAEpD,0EAAiD;AAKpC,QAAA,iBAAiB,GAAG;IAM/B,OAAO,EAAP,UAAQ,MAAkB,EAAE,EAAU;QACpC,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACjF,MAAM,CAAC,CAAC,EAAE,CAAC;SACZ;QACD,MAAM,CAAC,CAAC,EAAE,CAAC;IACb,CAAC;IAMD,IAAI,EAAJ,UAAK,MAAkB,EAAE,IAAY;QACnC,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,CAAC,CAAC;QACT,IAAI,MAAM,GAAG,CAAC,CAAC;QAIf,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;YAC7B,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;YACf,QAAQ,CAAC,EAAE;gBAEX,KAAK,IAAI;oBACP,IAAM,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC1B,IAAI,EAAE,KAAK,GAAG;wBAAE,CAAC,GAAG,KAAK,CAAC;gBAC5B,KAAK,GAAG;oBAKN,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;wBAC9B,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC;wBAC9C,IAAM,GAAG,GAAG,CAAC,IAAI,sBAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAC5F,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;wBACrB,OAAO;qBACR;oBACD,MAAM;gBAER,KAAK,GAAG;oBACN,MAAM,EAAE,CAAC;oBACT,MAAM;gBAER,KAAK,GAAG;oBACN,IAAI,MAAM,KAAK,CAAC,EAAE;wBAChB,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,yCAAyC,CAAC,CAAC;qBAClF;oBACD,MAAM,EAAE,CAAC;oBACT,MAAM;aACP;SACF;QACD,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,sCAAsC,CAAC,CAAC;IAC5E,CAAC;IAMD,YAAY,EAAZ,UAAa,MAAkB,EAAE,CAAS;QACxC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,kCAAkC,EAAE,CAAC,CAAC,CAAC;IACtE,CAAC;IAMD,SAAS,EAAT,UAAU,MAAkB,EAAE,CAAS;QACrC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,6BAA6B,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IAMD,SAAS,EAAT,UAAU,MAAkB,EAAE,EAAU;QAKtC,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAMD,UAAU,EAAV,UAAW,MAAkB,EAAE,EAAU;QAIvC,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC1B,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAC1C;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,yCAAyC,CAAC,CAAC;SAClF;IACH,CAAC;IAMD,SAAS,EAAT,UAAU,MAAkB,EAAE,CAAS;QAIrC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC;YACxB,MAAM,CAAC,CAAC,EAAE,CAAC;SACZ;aAAM;YACL,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC;SACzB;IACH,CAAC;IAMD,UAAU,EAAV,UAAW,MAAkB,EAAE,CAAS;QAItC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC;YACxB,MAAM,CAAC,CAAC,EAAE,CAAC;SACZ;aAAM;YACL,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC;SACzB;IACH,CAAC;IAMD,KAAK,EAAL,UAAM,MAAkB,EAAE,EAAU;QAClC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC;IAC1B,CAAC;IAMD,KAAK,EAAL,UAAM,MAAkB,EAAE,EAAU;QAClC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC;QACnB,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,CAAC,EAAE,CAAC;IAClD,CAAC;IAMD,SAAS,EAAT,UAAU,MAAkB,EAAE,IAAY;QACxC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IAOD,MAAM,EAAN,UAAO,MAAkB,EAAE,KAAa,EAAE,CAAS;QACjD,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC;IACnB,CAAC;IAOD,MAAM,EAAN,UAAO,MAAkB,EAAE,IAAY,EAAE,CAAS;QAIhD,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnC,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACnD,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAMD,IAAI,EAAJ,UAAK,MAAkB,EAAE,IAAY;QAInC,IAAM,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,KAAK,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QAC5F,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,EAAC,WAAW,EAAE,OAAO,EAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAOD,OAAO,EAAP,UAAQ,MAAkB,EAAE,KAAa,EAAE,OAAe;QACxD,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC;IACzC,CAAC;IAOD,OAAO,EAAP,UAAQ,MAAkB,EAAE,KAAa,EAAE,IAAY;QACrD,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;IACnC,CAAC;IAMD,aAAa,EAAb,UAAc,MAAkB,EAAE,IAAY;QAC5C,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAClE,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAQrB,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC7D,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK;gBAAE,OAAO;YACnB,IAAA,uBAAU,EAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SAC/B;QACD,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAKD,KAAK,EAAE,wBAAW,CAAC,KAAK;IACxB,MAAM,EAAE,wBAAW,CAAC,MAAM;IAC1B,KAAK,EAAE,wBAAW,CAAC,KAAK;IACxB,IAAI,EAAE,wBAAW,CAAC,IAAI;IACtB,IAAI,EAAE,wBAAW,CAAC,IAAI;IACtB,SAAS,EAAE,wBAAW,CAAC,SAAS;CAEjC,CAAC"}