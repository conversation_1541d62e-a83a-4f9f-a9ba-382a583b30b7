export declare enum KeyCode {
    ENTER = 13,
    ESC = 27,
    SPACE = 32,
    PAGE_UP = 33,
    PAGE_DOWN = 34,
    END = 35,
    HOME = 36,
    LEFT = 37,
    UP = 38,
    RIGHT = 39,
    DOWN = 40,
    TAB = 9,
    LESS = 188,
    GREATER = 190,
    DASH = 189,
    ZERO = 48,
    ONE = 49,
    TWO = 50,
    THREE = 51,
    FOUR = 52,
    FIVE = 53,
    SIX = 54,
    SEVEN = 55,
    EIGHT = 56,
    NINE = 57,
    A = 65,
    B = 66,
    C = 67,
    D = 68,
    E = 69,
    F = 70,
    G = 71,
    H = 72,
    I = 73,
    J = 74,
    K = 75,
    <PERSON> = 76,
    <PERSON> = 77,
    <PERSON> = 78,
    <PERSON> = 79,
    P = 80,
    Q = 81,
    R = 82,
    S = 83,
    T = 84,
    U = 85,
    V = 86,
    W = 87,
    X = 88,
    Y = 89,
    Z = 90
}
export declare const Move: Map<number, string>;
declare enum EventType {
    CLICK = "click",
    DBLCLICK = "dblclick",
    MOUSEDOWN = "mousedown",
    MOUSEUP = "mouseup",
    MOUSEOVER = "mouseover",
    MOUSEOUT = "mouseout",
    MOUSEMOVE = "mousemove",
    SELECTSTART = "selectstart",
    KEYPRESS = "keypress",
    KEYDOWN = "keydown",
    KEYUP = "keyup",
    TOUCHSTART = "touchstart",
    TOUCHMOVE = "touchmove",
    TOUCHEND = "touchend",
    TOUCHCANCEL = "touchcancel"
}
export declare class Event {
    src: Node;
    type: EventType;
    callback: EventListener;
    constructor(src: Node, type: EventType, callback: EventListener);
    add(): void;
    remove(): void;
}
export {};
