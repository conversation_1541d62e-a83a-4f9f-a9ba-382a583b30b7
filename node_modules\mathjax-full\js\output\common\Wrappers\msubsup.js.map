{"version": 3, "file": "msubsup.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/msubsup.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,SAAgB,eAAe,CAG7B,IAAO;;IAEP;YAAqB,2BAAI;YAAlB;;YAuBP,CAAC;YAbC,sBAAW,gCAAW;qBAAtB;oBACE,OAAO,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,IAAgB,CAAC,GAAG,CAAC,CAAC;gBACrD,CAAC;;;eAAA;YAOM,2BAAS,GAAhB;gBACE,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3B,CAAC;YAEH,cAAC;QAAD,CAAC,AAvBM,CAAc,IAAI;QAKT,QAAK,GAAY,KAAM;WAkBrC;AAEJ,CAAC;AA9BD,0CA8BC;AAyBD,SAAgB,eAAe,CAG7B,IAAO;IAEP;QAAqB,2BAAI;QAAlB;;QAmBP,CAAC;QAdC,sBAAW,gCAAW;iBAAtB;gBACE,OAAO,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,IAAgB,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC;;;WAAA;QAOM,2BAAS,GAAhB;YACE,IAAM,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1B,CAAC;QAEH,cAAC;IAAD,CAAC,AAnBM,CAAc,IAAI,GAmBvB;AAEJ,CAAC;AA1BD,0CA0BC;AAkDD,SAAgB,kBAAkB,CAGhC,IAAO;;IAEP;YAAqB,2BAAI;YAAlB;gBAAA,qEA+FN;gBApFQ,SAAG,GAAa,IAAI,CAAC;;YAoF9B,CAAC;YA/EC,sBAAW,6BAAQ;qBAAnB;oBACE,OAAO,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,IAAmB,CAAC,GAAG,CAAC,CAAC;gBACxD,CAAC;;;eAAA;YAKD,sBAAW,6BAAQ;qBAAnB;oBACE,OAAO,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,IAAmB,CAAC,GAAG,CAAC,CAAC;gBACxD,CAAC;;;eAAA;YAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;gBAA1B,0BAAA,EAAA,iBAA0B;gBACvD,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;gBACxC,IAAA,KAAA,OAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,IAAA,EAA9E,MAAM,QAAA,EAAE,MAAM,QAAgE,CAAC;gBACtF,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrB,IAAM,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC9B,IAAM,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACzB,IAAA,KAAA,OAAS,IAAI,CAAC,MAAM,EAAE,IAAA,EAArB,CAAC,QAAA,EAAE,CAAC,QAAiB,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACvC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC;YASM,wBAAM,GAAb,UACE,MAA2C,EAC3C,MAA2C;gBAD3C,uBAAA,EAAA,SAAe,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBAC3C,uBAAA,EAAA,SAAe,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBAE3C,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC;gBAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC7B,IAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC;gBACjC,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5F,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;gBAItF,IAAA,KAAA,OAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,IAAA,EAArD,CAAC,QAAA,EAAE,CAAC,QAAiD,CAAC;gBAU3D,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxE,IAAI,CAAC,GAAG,CAAC,EAAE;oBACT,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACX,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;oBAClE,IAAI,CAAC,GAAG,CAAC,EAAE;wBACT,CAAC,IAAI,CAAC,CAAC;wBACP,CAAC,IAAI,CAAC,CAAC;qBACR;iBACF;gBAKD,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjF,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/E,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,OAAO,IAAI,CAAC,GAAG,CAAC;YAClB,CAAC;YAEH,cAAC;QAAD,CAAC,AA/FM,CAAc,IAAI;QAKT,QAAK,GAAY,KAAM;WA0FrC;AAEJ,CAAC;AAtGD,gDAsGC"}