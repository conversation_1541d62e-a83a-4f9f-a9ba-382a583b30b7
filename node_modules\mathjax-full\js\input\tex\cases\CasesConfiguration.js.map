{"version": 3, "file": "CasesConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/cases/CasesConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAkD;AAClD,gDAAyD;AACzD,iEAAwC;AACxC,0EAAiD;AAEjD,+DAAsC;AACtC,qDAA6D;AAC7D,kEAAmD;AAGnD,yDAAmD;AAKnD;IAAoC,kCAAS;IAA7C;;IAsBA,CAAC;IAjBC,sBAAI,gCAAI;aAAR;YACE,OAAO,aAAa,CAAC;QACvB,CAAC;;;OAAA;IAKM,kCAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC/B,OAAO,CAAC,EAAE,EAAE,IAAI,CAAc,CAAC;aAChC;SACF;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,qBAAC;AAAD,CAAC,AAtBD,CAAoC,wBAAS,GAsB5C;AAtBY,wCAAc;AA2B3B;IAA+B,6BAAO;IAAtC;QAAA,qEAqCC;QAhCW,gBAAU,GAAG,CAAC,CAAC;;IAgC3B,CAAC;IA3BQ,yBAAK,GAAZ,UAAa,GAAW,EAAE,QAAiB,EAAE,WAAoB;QAC/D,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,iBAAM,KAAK,YAAC,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC1C,CAAC;IAKM,2BAAO,GAAd;QACE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,IAAI;YAAE,OAAO;QACxC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,aAAa,EAAE;YACzC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;gBAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;SACnE;aAAM;YACL,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,eAAe;gBAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YACrF,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;SAClD;IACH,CAAC;IAKM,gCAAY,GAAnB,UAAoB,CAAS,EAAE,CAAgB;QAAhB,kBAAA,EAAA,QAAgB;QAC7C,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEH,gBAAC;AAAD,CAAC,AArCD,CAA+B,6BAAO,GAqCrC;AArCY,8BAAS;AAuCT,QAAA,YAAY,GAAG;IAQ1B,QAAQ,EAAR,UAAS,MAAiB,EAAE,KAAqB;QAC/C,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;YAChD,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACnF,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YACjC,IAAM,KAAK,GAAG,KAAK,CAAC,IAAiB,CAAC;YACtC,IAAM,QAAQ,GAAG,sBAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAc,CAAC;YAChE,IAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACvC,0BAAU,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,GAAG,mBAAmB,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACnF,OAAO,IAAI,CAAC;SACb;aAAM;YACL,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;YACpE,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAChC,IAAM,KAAK,GAAG,wBAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAmB,CAAC;YACtF,KAAK,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;YACpC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,MAAM,CAAC;YACnC,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAKD,KAAK,EAAL,UAAM,MAAiB,EAAE,IAAY;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YAC/C,OAAO,wBAAW,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACxC;QACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;QAI1F,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QAI7C,OAAO,CAAC,GAAG,CAAC,EAAE;YACZ,IAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,KAAK,GAAG,EAAE;gBAIb,MAAM,EAAE,CAAC;gBACT,CAAC,EAAE,CAAC;aACL;iBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;gBAQpB,IAAI,MAAM,KAAK,CAAC,EAAE;oBAChB,MAAM;iBACP;qBAAM;oBACL,MAAM,EAAE,CAAC;oBACT,CAAC,EAAE,CAAC;iBACL;aACF;iBAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE;gBAIpC,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EAAE,qDAAqD,CAAC,CAAC;aACjG;iBAAM,IAAI,CAAC,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,EAAE;gBAMrC,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,OAAO,EAAE;oBAChE,MAAM;iBACP;qBAAM;oBACL,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;iBAChB;aACF;iBAAM;gBAIL,CAAC,EAAE,CAAC;aACL;SACF;QAID,IAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACpE,MAAM,CAAC,OAAO,CAAC,sBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;CAEF,CAAC;AAKF,IAAI,6BAAc,CAAC,WAAW,EAAE,0BAAU,CAAC,WAAW,EAAE;IACtD,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;IAC/B,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;CACnC,EAAE,oBAAY,CAAC,CAAC;AAKjB,IAAI,uBAAQ,CAAC,cAAc,EAAE;IAC3B,GAAG,EAAE,OAAO;CACb,EAAE,oBAAY,CAAC,CAAC;AAKJ,QAAA,kBAAkB,GAAG,gCAAa,CAAC,MAAM,CAAC,OAAO,EAAE;IAC9D,OAAO,EAAE;QACP,WAAW,EAAE,CAAC,WAAW,CAAC;QAC1B,SAAS,EAAE,CAAC,cAAc,CAAC;KAC5B;IACD,KAAK;QACH,GAAC,cAAc,CAAC,SAAS,CAAC,IAAI,IAAG,cAAc;WAChD;IACD,IAAI,EAAE,EAAC,OAAO,EAAE,SAAS,EAAC;CAC3B,CAAC,CAAC"}