import { LiteElement } from './Element.js';
import { LiteDocument } from './Document.js';
import { LiteList } from './List.js';
import { LiteParser } from './Parser.js';
export declare class LiteWindow {
    document: LiteDocument;
    DOMParser: typeof LiteParser;
    NodeList: typeof LiteList;
    HTMLCollection: typeof LiteList;
    HTMLElement: typeof LiteElement;
    DocumentFragment: typeof LiteList;
    Document: typeof LiteDocument;
    constructor();
}
