import { <PERSON><PERSON><PERSON>rapper, SVGConstructor } from '../Wrapper.js';
import { SVGWrapperFactory } from '../WrapperFactory.js';
import { SVGmtr } from './mtr.js';
import { SVGmtd } from './mtd.js';
import { MmlNode } from '../../../core/MmlTree/MmlNode.js';
import { OptionList } from '../../../util/Options.js';
import { StyleList } from '../../../util/StyleList.js';
declare const SVGmtable_base: import("../../common/Wrappers/mtable.js").MtableConstructor<SVGmtd<any, any, any>, SVGmtr<any, any, any>> & SVGConstructor<any, any, any>;
export declare class SVGmtable<N, T, D> extends SVGmtable_base {
    static kind: string;
    static styles: StyleList;
    labels: N;
    constructor(factory: SVGWrapperFactory<N, T, D>, node: MmlNode, parent?: SVGWrapper<N, T, D>);
    toSVG(parent: N): void;
    protected placeRows(svg: N): void;
    protected getRowHD(equal: boolean, HD: number, H: number, D: number): [number, number];
    handleColor(): void;
    protected handleColumnLines(svg: N): void;
    protected handleRowLines(svg: N): void;
    protected handleFrame(svg: N): void;
    protected handlePWidth(svg: N): number;
    protected lineClass(style: string): string;
    protected makeFrame(w: number, h: number, d: number, style: string): N;
    protected makeVLine(x: number, style: string, t: number): N;
    protected makeHLine(y: number, style: string, t: number): N;
    protected setLineThickness(t: number, style: string, properties: OptionList): OptionList;
    protected handleLabels(svg: N, _parent: N, dx: number): void;
    protected spaceLabels(): void;
    protected topTable(svg: N, labels: N, side: string): void;
    protected subTable(svg: N, labels: N, side: string, dx: number): void;
}
export {};
