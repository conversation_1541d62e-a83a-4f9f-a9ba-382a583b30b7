"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sansSerifBoldItalic = void 0;
var FontData_js_1 = require("../../FontData.js");
var sans_serif_bold_italic_js_1 = require("../../../common/fonts/tex/sans-serif-bold-italic.js");
exports.sansSerifBoldItalic = (0, FontData_js_1.AddPaths)(sans_serif_bold_italic_js_1.sansSerifBoldItalic, {
    0x131: '54 431Q63 458 102 458H127H149Q192 458 199 433Q201 427 201 229T199 25Q190 0 149 0H125L81 1Q61 7 54 28V431',
    0x237: '-38 -84Q-36 -84 -14 -95T33 -106H38Q70 -103 78 -86Q83 -78 83 -49T84 180Q84 427 86 433Q93 458 136 458H158H180Q201 458 209 456T225 443Q230 436 231 418Q232 397 232 313V183V124V40Q232 -55 228 -87T203 -147Q166 -205 78 -205Q31 -205 -20 -189T-71 -159Q-71 -156 -59 -123Q-50 -96 -47 -91T-38 -84',
}, {});
//# sourceMappingURL=sans-serif-bold-italic.js.map