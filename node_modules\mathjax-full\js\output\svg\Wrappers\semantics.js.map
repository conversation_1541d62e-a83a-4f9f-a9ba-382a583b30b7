{"version": 3, "file": "semantics.js", "sourceRoot": "", "sources": ["../../../../ts/output/svg/Wrappers/semantics.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAwBA,4CAAyD;AACzD,mEAAwE;AAExE,4EAA0G;AAC1G,+DAAyD;AAYzD;IACA,gCAA+D;IAD/D;;IAkBA,CAAC;IAPQ,4BAAK,GAAZ,UAAa,MAAS;QACpB,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC/B;IACH,CAAC;IAVa,iBAAI,GAAG,2BAAY,CAAC,SAAS,CAAC,IAAI,CAAC;IAYnD,mBAAC;CAAA,AAlBD,CACA,IAAA,mCAAoB,EAAgC,uBAAU,CAAC,GAiB9D;AAlBY,oCAAY;AA6BzB;IAA4C,iCAAmB;IAA/D;;IAuBA,CAAC;IAbQ,6BAAK,GAAZ,UAAa,MAAS;QAEpB,iBAAM,KAAK,YAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAKM,mCAAW,GAAlB;QAEE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAhBa,kBAAI,GAAG,4BAAa,CAAC,SAAS,CAAC,IAAI,CAAC;IAkBpD,oBAAC;CAAA,AAvBD,CAA4C,uBAAU,GAuBrD;AAvBY,sCAAa;AAiC1B;IAA+C,oCAAmB;IAAlE;;IAkBA,CAAC;IAbe,qBAAI,GAAG,+BAAgB,CAAC,SAAS,CAAC,IAAI,CAAC;IAKvC,uBAAM,GAAc;QAChC,6BAA6B,EAAE;YAC7B,aAAa,EAAE,SAAS;YACxB,aAAa,EAAE,QAAQ;YACvB,QAAQ,EAAE,SAAS;SACpB;KACF,CAAC;IAEJ,uBAAC;CAAA,AAlBD,CAA+C,uBAAU,GAkBxD;AAlBY,4CAAgB;AA4B7B;IAAqC,0BAAmB;IAAxD;;IAqDA,CAAC;IAtCQ,sBAAK,GAAZ,UAAa,MAAS;QACpB,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAE,IAAI,CAAC,IAAgB,CAAC,MAAM,EAAO,CAAC,CAAC;QACrE,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAClE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAA,KAAY,IAAI,CAAC,OAAO,EAAE,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE;YACnE,cAAc,EAAE,IAAI;YACpB,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI;YACjC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI;YACpC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI;YAC3C,SAAS,EAAE,gBAAS,KAAK,2BAAwB;SAClD,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IAKM,4BAAW,GAAlB,UAAmB,IAAU,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAClD,IAAA,KAAY,IAAI,CAAC,GAAG,CAAC,cAAc,CAAE,IAAI,CAAC,IAAgB,CAAC,MAAM,EAAO,CAAC,EAAxE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAiE,CAAC;QAChF,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;IAKS,0BAAS,GAAnB,cAAuB,CAAC;IAKd,yBAAQ,GAAlB,cAAsB,CAAC;IAKb,2BAAU,GAApB,cAAwB,CAAC;IA/CX,WAAI,GAAG,oBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAK9B,gBAAS,GAAG,KAAK,CAAC;IA2ClC,aAAC;CAAA,AArDD,CAAqC,uBAAU,GAqD9C;AArDY,wBAAM"}