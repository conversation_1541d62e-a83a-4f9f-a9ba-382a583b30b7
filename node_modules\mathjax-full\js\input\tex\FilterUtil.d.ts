import ParseOptions from './ParseOptions.js';
declare namespace FilterUtil {
    let cleanStretchy: (arg: {
        math: any;
        data: ParseOptions;
    }) => void;
    let cleanAttributes: (arg: {
        data: ParseOptions;
    }) => void;
    let combineRelations: (arg: {
        data: ParseOptions;
    }) => void;
    let cleanSubSup: (arg: {
        math: any;
        data: ParseOptions;
    }) => void;
    let moveLimits: (arg: {
        data: ParseOptions;
    }) => void;
    let setInherited: (arg: {
        math: any;
        data: ParseOptions;
    }) => void;
}
export default FilterUtil;
