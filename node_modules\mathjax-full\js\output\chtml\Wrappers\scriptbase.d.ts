import { CHTMLWrapper, CHTMLConstructor } from '../Wrapper.js';
import { BBox } from '../../../util/BBox.js';
declare const CHTMLscriptbase_base: import("../../common/Wrappers/scriptbase.js").ScriptbaseConstructor<CHTMLWrapper<any, any, any>> & CHTMLConstructor<any, any, any>;
export declare class CHTMLscriptbase<N, T, D> extends CHTMLscriptbase_base {
    static kind: string;
    toCHTML(parent: N): void;
    protected setDeltaW(nodes: N[], dx: number[]): void;
    protected adjustOverDepth(over: N, overbox: BBox): void;
    protected adjustUnderDepth(under: N, underbox: BBox): void;
    protected adjustBaseHeight(base: N, basebox: BBox): void;
}
export {};
