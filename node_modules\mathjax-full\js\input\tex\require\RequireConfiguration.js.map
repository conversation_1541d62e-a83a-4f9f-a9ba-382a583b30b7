{"version": 3, "file": "RequireConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/require/RequireConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,wDAA6F;AAE7F,gDAA2C;AAE3C,+DAAsC;AAGtC,2DAAsD;AACtD,6DAAuD;AACvD,2DAA6E;AAC7E,kDAA4C;AAC5C,uDAAoD;AAKpD,IAAM,QAAQ,GAAG,mBAAO,CAAC,MAAM,CAAC;AAQhC,SAAS,iBAAiB,CAAC,GAAuB,EAAE,IAAY;;IAC9D,IAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;IACjD,IAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAoB,CAAC;IAClF,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACrD,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;QACnC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAIzB,oBAAoB,CAAC,GAAG,EAAE,kBAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QAI3D,IAAM,OAAO,GAAG,uCAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,OAAO,EAAE;YAKX,IAAI,SAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,OAAO,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC9F,SAAO,aAAI,GAAC,SAAS,IAAG,SAAO,KAAC,CAAC;aAClC;YAIA,GAAW,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,EAAE,SAAO,CAAC,CAAC;YAMxD,IAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC;YAC1E,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC9D,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAChC,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;aACvC;SACF;KACF;AACH,CAAC;AAQD,SAAS,oBAAoB,CAAC,GAAuB,EAAE,KAAoB;;IAApB,sBAAA,EAAA,UAAoB;IACzE,IAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;;QACvD,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;YAArB,IAAM,MAAI,kBAAA;YACb,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;gBAC5C,iBAAiB,CAAC,GAAG,EAAE,MAAI,CAAC,CAAC;aAC9B;SACF;;;;;;;;;AACH,CAAC;AAQD,SAAgB,WAAW,CAAC,MAAiB,EAAE,IAAY;IACzD,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;IACvC,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IAC3E,IAAM,OAAO,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACpD,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAClF,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,qBAAQ,CAAC,YAAY,EAAE,4CAA4C,EAAE,SAAS,CAAC,CAAC;KAC3F;IACD,IAAI,oBAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QACnC,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KACnF;SAAM;QACL,oBAAO,CAAC,UAAU,CAAC,kBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;KAC5C;AACH,CAAC;AAdD,kCAcC;AAKD,SAAS,MAAM,CAAC,OAA4B,EAAE,GAAuB;IACnE,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE;QAC1C,GAAG,EAAE,GAAG;QACR,QAAQ,2BAAM,GAAG,CAAC,OAAO,CAAC,QAAQ,SAAC;QACnC,UAAU,EAAE,IAAI,GAAG,EAAE;KACtB,CAAC,CAAC;IACH,IAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;IACjD,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACjC,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;KAC5D;IACD,IAAI,CAAC,kBAAY,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAC/B,kBAAY,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,gCAAgC,CAAC;KAC/D;IACD,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;AACvC,CAAC;AAMY,QAAA,cAAc,GAAgC;IAQzD,OAAO,EAAP,UAAQ,MAAiB,EAAE,IAAY;QACrC,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,QAAQ,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,qBAAQ,CAAC,gBAAgB,EAAE,6CAA6C,EAAE,IAAI,CAAC,CAAC;SAC3F;QACD,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAChC,CAAC;CAEF,CAAC;AAKW,QAAA,OAAO,GAAG;IACrB,OAAO,EAAE;QAMP,KAAK,EAAE,IAAA,uBAAU,EAAC;YAChB,IAAI,EAAE,KAAK;YACX,cAAc,EAAE,KAAK;YACrB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,KAAK;YACnB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,KAAK;SAClB,CAAC;QAIF,YAAY,EAAE,IAAI;QAKlB,MAAM,EAAE,KAAK;KACd;CACF,CAAC;AAKF,IAAI,yBAAU,CAAC,SAAS,EAAE,EAAC,OAAO,EAAE,SAAS,EAAC,EAAE,sBAAc,CAAC,CAAC;AAKnD,QAAA,oBAAoB,GAAG,gCAAa,CAAC,MAAM,CACtD,SAAS,EAAE,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAC,EAAE,MAAM,QAAA,EAAE,OAAO,iBAAA,EAAC,CAC5D,CAAC"}