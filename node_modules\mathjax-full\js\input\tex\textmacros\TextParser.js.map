{"version": 3, "file": "TextParser.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/textmacros/TextParser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,iEAAwC;AACxC,+DAAsC;AAEtC,iEAAwC;AAExC,+DAA0E;AAE1E,+DAAsC;AACtC,qDAAyD;AAKzD;IAAgC,8BAAS;IAwCvC,oBAAY,IAAY,EAAE,GAAY,EAAE,aAA2B,EAAE,KAAuB;QAA5F,YACE,kBAAM,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,SAEhC;QADC,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;IACrB,CAAC;IAlBD,sBAAW,iCAAS;aAApB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC;QACpE,CAAC;;;OAAA;IAKD,sBAAW,4BAAI;aAAf;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B,CAAC;;;OAAA;IAgBM,wBAAG,GAAV;QACE,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAC,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC,CAAC,CAAC;YAC3F,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7F,CAAC;IAKM,0BAAK,GAAZ;QACE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,iBAAM,KAAK,WAAE,CAAC;IAChB,CAAC;IAKM,6BAAQ,GAAf;QACE,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC;YAC/C,IAAM,IAAI,GAAG,sBAAS,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAC,WAAW,aAAA,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACvF,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;IACH,CAAC;IAKM,yBAAI,GAAX,UAAY,GAAwB;QAClC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;QACD,IAAI,GAAG,YAAY,uBAAQ,EAAE;YAC3B,OAAO,iBAAM,IAAI,YAAC,GAAG,CAAC,CAAC;SACxB;QACD,IAAI,GAAG,YAAY,wBAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;YAChD,OAAO;SACR;QACD,IAAI,GAAG,YAAY,4BAAe,EAAE;YAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACtB;IACH,CAAC;IAQM,6BAAQ,GAAf,UAAgB,GAAY;;QAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAC1B,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7C;;YACD,KAAmB,IAAA,KAAA,SAAA,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,MAAI,WAAA;gBACb,IAAI,GAAG,CAAC,MAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,MAAI,CAAC,EAAE;oBAClD,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;wBACzC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC5C;oBACD,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,MAAI,EAAE,GAAG,CAAC,MAAI,CAAC,CAAC,CAAC;iBAC7C;aACF;;;;;;;;;QACD,IAAI,GAAG,CAAC,UAAU,EAAE;YAClB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAQM,kCAAa,GAApB,UAAqB,GAAY;;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,OAAO;YAAE,OAAO;;YACzB,KAAmB,IAAA,KAAA,SAAA,CAAC,UAAU,EAAE,WAAW,EAAE,aAAa,CAAC,CAAA,gBAAA,4BAAE;gBAAxD,IAAM,MAAI,WAAA;gBACb,IAAI,GAAG,CAAC,MAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,MAAI,CAAC,EAAE;oBAClD,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,MAAI,EAAE,GAAG,CAAC,MAAI,CAAC,CAAC,CAAC;iBAC7C;aACF;;;;;;;;;IACH,CAAC;IAQM,iCAAY,GAAnB,UAAoB,IAAY,EAAE,GAAY;QAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACpC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5D,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAC/D,CAAC;IAOM,6BAAQ,GAAf,UAAgB,IAAY;QAC1B,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IAC5F,CAAC;IASM,0BAAK,GAAZ,UAAa,EAAU,EAAE,OAAe;QAAE,cAAiB;aAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;YAAjB,6BAAiB;;QACzD,WAAU,qBAAQ,YAAR,qBAAQ,yBAAC,EAAE,EAAE,OAAO,UAAK,IAAI,cAAE;IAC3C,CAAC;IAEH,iBAAC;AAAD,CAAC,AA3KD,CAAgC,sBAAS,GA2KxC;AA3KY,gCAAU"}