{"version": 3, "file": "EncloseConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/enclose/EncloseConfiguration.ts"], "names": [], "mappings": ";;;;;;AAwBA,wDAAkD;AAElD,gDAA2C;AAE3C,iEAAwC;AAO3B,QAAA,eAAe,GAA4B;IACtD,gBAAgB,EAAE,CAAC;IACnB,KAAK,EAAE,CAAC;IACR,SAAS,EAAE,CAAC;IACZ,UAAU,EAAE,CAAC;IACb,cAAc,EAAE,CAAC;IACjB,cAAc,EAAE,CAAC;IACjB,gBAAgB,EAAE,CAAC;CACpB,CAAC;AAIS,QAAA,cAAc,GAAgC,EAAE,CAAC;AAS5D,sBAAc,CAAC,OAAO,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC/D,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC3D,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,sBAAS,CAAC,aAAa,CAAC,IAAI,EAAE,uBAAe,CAAC,CAAC;IAC3D,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACxB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AAGF,IAAI,yBAAU,CAAC,SAAS,EAAE,EAAC,OAAO,EAAE,SAAS,EAAC,EAAE,sBAAc,CAAC,CAAC;AAGnD,QAAA,oBAAoB,GAAG,gCAAa,CAAC,MAAM,CACtD,SAAS,EAAE,EAAC,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAC,EAAC,CAC3C,CAAC"}