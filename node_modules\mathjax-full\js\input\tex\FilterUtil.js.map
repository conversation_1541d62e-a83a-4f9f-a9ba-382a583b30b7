{"version": 3, "file": "FilterUtil.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/FilterUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAwBA,4DAAyE;AACzE,8DAAqC;AAMrC,IAAU,UAAU,CAoQnB;AApQD,WAAU,UAAU;IASP,wBAAa,GAAG,UAAS,GAAoC;;QACtE,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;;YACvB,KAAe,IAAA,KAAA,SAAA,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA,gBAAA,4BAAE;gBAA1C,IAAI,EAAE,WAAA;gBACT,IAAI,qBAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE;oBAC3C,IAAI,MAAM,GAAG,qBAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBAClC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE;wBAChD,qBAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;qBAC9C;oBACD,IAAM,QAAM,GAAG,EAAE,CAAC,MAAM,CAAC;oBACzB,IAAI,CAAC,qBAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;wBACxD,IAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBACpE,QAAM,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBACjC,OAAO,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;qBACnC;oBACD,qBAAQ,CAAC,gBAAgB,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;iBAC9C;aACF;;;;;;;;;IACH,CAAC,CAAC;IASS,0BAAe,GAAG,UAAS,GAAyB;QAC7D,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAe,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,UAAC,GAAY,EAAE,EAAO;;YAClC,IAAI,OAAO,GAAG,GAAG,CAAC,UAAiB,CAAC;YACpC,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO;aACR;YACD,IAAM,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACvE,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC;;gBACtD,KAAkB,IAAA,KAAA,SAAA,OAAO,CAAC,gBAAgB,EAAE,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,GAAG,WAAA;oBACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;wBAClF,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;qBAChC;iBACF;;;;;;;;;QACH,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC,CAAC;IAQS,2BAAgB,GAAG,UAAS,GAAyB;;QAC9D,IAAM,MAAM,GAAc,EAAE,CAAC;;YAC7B,KAAe,IAAA,KAAA,SAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAlC,IAAI,EAAE,WAAA;gBACT,IAAI,EAAE,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM;oBACjD,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,qBAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClD,qBAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,qBAAQ,CAAC,GAAG,EAAE;oBAE7C,SAAS;iBACV;gBACD,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;gBACpB,IAAI,EAAE,SAAS,CAAC;gBAChB,IAAI,QAAQ,GAAG,GAAG,CAAC,UAAuB,CAAC;gBAC3C,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,WAAW,GAAG,qBAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;gBAC1D,OAAO,IAAI,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC/C,qBAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC;oBACzB,qBAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,qBAAQ,CAAC,GAAG,EAAE;oBAChD,IAAI,WAAW,KAAK,qBAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC;wBACvD,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;wBAG5B,qBAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,qBAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;wBAGtD,aAAa,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;;4BAC9C,KAAmB,IAAA,oBAAA,SAAA,EAAE,CAAC,gBAAgB,EAAE,CAAA,CAAA,gBAAA,4BAAE;gCAArC,IAAM,MAAI,WAAA;gCACb,EAAE,CAAC,WAAW,CAAC,MAAI,EAAE,EAAE,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC,CAAC;6BAC5C;;;;;;;;;wBACD,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBACzB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAChB,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC;wBACjB,EAAE,CAAC,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;qBAC3C;yBAAM;wBAEL,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;4BAE/C,qBAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;yBAC5C;wBACD,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;4BAE/C,qBAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;yBAC5C;wBACD,MAAM;qBACP;iBACF;gBACD,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,EAAG,EAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACjE;;;;;;;;;QACD,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;IASF,IAAI,aAAa,GAAG,UAAS,KAAe,EACf,KAAc,EAAE,KAAc;QACzD,IAAI,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC7B,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YACb,IAAI,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,IAAI,IAAI,IAAI,EAAE;gBAEhB,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAYF,IAAI,gBAAgB,GAAG,UAAS,KAAc,EAAE,KAAc;;QAC5D,IAAI,MAAM,GAAG,UAAC,IAAgB,EAAE,KAAa;YAC3C,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAClC,OAAO,GAAG,CAAC,MAAM,CAAC,UAAA,CAAC;gBACjB,OAAO,CAAC,KAAK,KAAK;oBAChB,CAAC,CAAC,KAAK,UAAU;wBAChB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QACF,IAAI,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC7B,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnC,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;YAC/B,OAAO,KAAK,CAAC;SACd;;YACD,KAAiB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAAlB,IAAI,MAAI,iBAAA;gBACX,IAAI,KAAK,CAAC,WAAW,CAAC,MAAI,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,MAAI,CAAC,EAAE;oBACvD,OAAO,KAAK,CAAC;iBACd;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAQF,IAAI,YAAY,GAAG,UAAS,OAAqB,EAAE,GAAW,EAAE,EAAU;;QACxE,IAAM,MAAM,GAAc,EAAE,CAAC;;YAC7B,KAAgB,IAAA,KAAA,SAAA,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAU,CAAA,gBAAA,4BAAE;gBAArD,IAAI,GAAG,WAAA;gBACV,IAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC;gBAChC,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC3C,SAAS;iBACV;gBACD,IAAM,QAAM,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC1B,IAAI,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACxB,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClG,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBACtC,IAAI,QAAM,EAAE;oBACV,QAAM,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;iBACnC;qBAAM;oBACL,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;iBACxB;gBACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;;;;;;;;;QACD,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC,CAAC;IASS,sBAAW,GAAG,UAAS,GAAoC;QACpE,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,OAAO;SACR;QACD,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACpC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,CAAC,CAAC;IAWF,IAAI,WAAW,GAAG,UAAU,OAAqB,EAAE,SAAiB,EAAE,MAAc;;QAClF,IAAM,MAAM,GAAc,EAAE,CAAC;;YAC7B,KAAkB,IAAA,KAAA,SAAA,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,GAAG,WAAA;gBACZ,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;oBACtC,SAAS;iBACV;gBACD,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAE,GAAW,CAAC,IAAI,CAAY,CAAC;gBAC1D,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE;oBACpF,IAAI,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;oBACtE,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACnC,IAAI,GAAG,CAAC,MAAM,EAAE;wBACd,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;qBACpC;yBAAM;wBACL,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;qBACrB;oBACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAClB;aACF;;;;;;;;;QACD,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC,CAAC;IAQS,qBAAU,GAAG,UAAU,GAAyB;QACzD,IAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;QACzB,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAC9C,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACvC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;IAQS,uBAAY,GAAG,UAAS,GAAoC;QACrE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC,CAAC;AAEJ,CAAC,EApQS,UAAU,KAAV,UAAU,QAoQnB;AAGD,kBAAe,UAAU,CAAC"}