import { OptionList } from '../util/Options.js';
export declare type AttributeData = {
    name: string;
    value: string;
};
export declare type PageBBox = {
    left: number;
    right: number;
    top: number;
    bottom: number;
};
export interface DOMAdaptor<N, T, D> {
    document: D;
    parse(text: string, format?: string): D;
    node(kind: string, def?: OptionList, children?: (N | T)[], ns?: string): N;
    text(text: string): T;
    head(doc: D): N;
    body(doc: D): N;
    root(doc: D): N;
    doctype(doc: D): string;
    tags(node: N, name: string, ns?: string): N[];
    getElements(nodes: (string | N | N[])[], document: D): N[];
    contains(container: N, node: N | T): boolean;
    parent(node: N | T): N;
    append(node: N, child: N | T): N | T;
    insert(nchild: N | T, ochild: N | T): void;
    remove(child: N | T): N | T;
    replace(nnode: N | T, onode: N | T): N | T;
    clone(node: N): N;
    split(node: T, n: number): T;
    next(node: N | T): N | T;
    previous(node: N | T): N | T;
    firstChild(node: N): N | T;
    lastChild(node: N): N | T;
    childNodes(node: N): (N | T)[];
    childNode(node: N, i: number): N | T;
    kind(node: N | T): string;
    value(node: N | T): string;
    textContent(node: N): string;
    innerHTML(node: N): string;
    outerHTML(node: N): string;
    serializeXML(node: N): string;
    setAttribute(node: N, name: string, value: string | number, ns?: string): void;
    setAttributes(node: N, def: OptionList): void;
    getAttribute(node: N, name: string): string;
    removeAttribute(node: N, name: string): void;
    hasAttribute(node: N, name: string): boolean;
    allAttributes(node: N): AttributeData[];
    addClass(node: N, name: string): void;
    removeClass(node: N, name: string): void;
    hasClass(node: N, name: string): boolean;
    allClasses(node: N): string[];
    setStyle(node: N, name: string, value: string): void;
    getStyle(node: N, name: string): string;
    allStyles(node: N): string;
    insertRules(node: N, rules: string[]): void;
    fontSize(node: N): number;
    fontFamily(node: N): string;
    nodeSize(node: N, em?: number, local?: boolean): [number, number];
    nodeBBox(node: N): PageBBox;
}
export declare abstract class AbstractDOMAdaptor<N, T, D> implements DOMAdaptor<N, T, D> {
    document: D;
    constructor(document?: D);
    abstract parse(text: string, format?: string): D;
    node(kind: string, def?: OptionList, children?: (N | T)[], ns?: string): N;
    protected abstract create(kind: string, ns?: string): N;
    abstract text(text: string): T;
    setAttributes(node: N, def: OptionList): void;
    abstract head(doc: D): N;
    abstract body(doc: D): N;
    abstract root(doc: D): N;
    abstract doctype(doc: D): string;
    abstract tags(node: N, name: string, ns?: string): N[];
    abstract getElements(nodes: (string | N | N[])[], document: D): N[];
    abstract contains(container: N, node: N | T): boolean;
    abstract parent(node: N | T): N;
    abstract append(node: N, child: N | T): N | T;
    abstract insert(nchild: N | T, ochild: N | T): void;
    abstract remove(child: N | T): N | T;
    replace(nnode: N | T, onode: N | T): N | T;
    abstract clone(node: N): N;
    abstract split(node: T, n: number): T;
    abstract next(node: N | T): N | T;
    abstract previous(node: N | T): N | T;
    abstract firstChild(node: N): N | T;
    abstract lastChild(node: N): N | T;
    abstract childNodes(node: N): (N | T)[];
    childNode(node: N, i: number): N | T;
    abstract kind(node: N | T): string;
    abstract value(node: N | T): string;
    abstract textContent(node: N): string;
    abstract innerHTML(node: N): string;
    abstract outerHTML(node: N): string;
    abstract serializeXML(node: N): string;
    abstract setAttribute(node: N, name: string, value: string, ns?: string): void;
    abstract getAttribute(node: N, name: string): string;
    abstract removeAttribute(node: N, name: string): void;
    abstract hasAttribute(node: N, name: string): boolean;
    abstract allAttributes(node: N): AttributeData[];
    abstract addClass(node: N, name: string): void;
    abstract removeClass(node: N, name: string): void;
    abstract hasClass(node: N, name: string): boolean;
    allClasses(node: N): string[];
    abstract setStyle(node: N, name: string, value: string): void;
    abstract getStyle(node: N, name: string): string;
    abstract allStyles(node: N): string;
    abstract insertRules(node: N, rules: string[]): void;
    abstract fontSize(node: N): number;
    abstract fontFamily(node: N): string;
    abstract nodeSize(node: N, em?: number, local?: boolean): [number, number];
    abstract nodeBBox(node: N): PageBBox;
}
