{"version": 3, "file": "asciimath.js", "sourceRoot": "", "sources": ["../../ts/input/asciimath.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,mDAAqD;AACrD,wEAAwE;AACxE,iDAA+D;AAI/D,iEAA2D;AAU3D;IAAwC,6BAAyB;IAuB/D,mBAAY,OAAmB;QAA/B,iBAIC;QAHK,IAAA,KAAA,OAAgB,IAAA,4BAAe,EAAC,OAAO,EAAE,gCAAa,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,IAAA,EAA9E,IAAI,QAAA,EAAE,EAAE,QAAsE,CAAC;gBACvF,kBAAM,EAAE,CAAC;QACT,KAAI,CAAC,aAAa,GAAG,KAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,gCAAa,CAAC,IAAI,CAAC,CAAC;;IAChF,CAAC;IAOM,2BAAO,GAAd,UAAe,IAAuB,EAAE,SAAgC;QACtE,OAAO,8BAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAKM,4BAAQ,GAAf,UAAgB,OAAiB;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAtCa,cAAI,GAAW,WAAW,CAAC;IAK3B,iBAAO,yBAChB,8BAAgB,CAAC,OAAO,KAC3B,aAAa,EAAE,IAAI,IACnB;IAgCJ,gBAAC;CAAA,AA7CD,CAAwC,8BAAgB,GA6CvD;AA7CY,8BAAS"}