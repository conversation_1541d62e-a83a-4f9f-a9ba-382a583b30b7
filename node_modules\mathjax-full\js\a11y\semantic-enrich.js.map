{"version": 3, "file": "semantic-enrich.js", "sourceRoot": "", "sources": ["../../ts/a11y/semantic-enrich.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAAsC;AAGtC,mDAAgF;AAGhF,mFAA6E;AAC7E,iDAA0D;AAC1D,oDAA2B;AAO3B,IAAI,aAAa,GAAG,MAAM,CAAC;AAY3B,IAAA,sBAAQ,EAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAKzB,IAAA,sBAAQ,EAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AAqC9B,SAAgB,qBAAqB,CACnC,YAAe,EACf,MAAuB,EACvB,QAAmC;IAGnC;QAAqB,2BAAY;QAA1B;;QA4FP,CAAC;QAtFW,8BAAY,GAAtB,UAAuB,IAAS;YAC9B,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,OAAO,IAAI,CAAC,SAAS,CAAC;aACvB;YAID,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,IAAI,YAAY,OAAO,EAAE;gBAC9F,IAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACjD,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtB,OAAO,GAAG,CAAC,SAAS,CAAC;aACtB;YAID,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,CAAC;QAMM,wBAAM,GAAb,UAAc,QAA+B,EAAE,KAAsB;YAAtB,sBAAA,EAAA,aAAsB;YACnE,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,QAAQ;gBAAE,OAAO;YAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,EAAE;gBACnE,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,aAAa,EAAE;oBACjD,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC5C,oBAAO,CAAC,UAAU,CAChB,gBAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CACxC,cAAM,OAAA,gBAAG,CAAC,QAAQ,EAAE,EAAd,CAAc,CAAC,CAAC,CAAC;iBAC5B;gBACD,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;gBACvD,IAAI;oBACF,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC7D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;oBACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC5B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBACtB,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;iBACxC;gBAAC,OAAO,GAAG,EAAE;oBACZ,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;iBACnD;aACF;YACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAKM,8BAAY,GAAnB,UAAoB,QAA+B;;YACjD,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,YAAY;gBAAE,OAAO;YAC/C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,IAAM,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAW,CAAC;YACrD,IAAI,MAAM,EAAE;gBACV,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACjC,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC9B,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;;oBACjD,KAAoB,IAAA,KAAA,SAAA,OAAO,CAAC,UAAU,CAAC,IAAI,CAAQ,CAAA,gBAAA,4BAAE;wBAAhD,IAAM,KAAK,WAAA;wBACd,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;qBACpD;;;;;;;;;aACF;YACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;QAOO,2BAAS,GAAjB,UAAkB,IAAa;;YAC7B,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACnC,IAAI,CAAC,UAAU;gBAAE,OAAO,EAAE,CAAC;YAC3B,IAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,sBAAsB,CAAW,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,sBAAsB,CAAC,IAAI,MAAM,EAAE;gBAC7D,OAAO,MAAM,CAAC;aACf;;gBACD,KAAkB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAA9B,IAAI,KAAK,WAAA;oBACZ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAgB,CAAC,CAAC;oBAC7C,IAAI,KAAK,IAAI,IAAI,EAAE;wBACjB,OAAO,KAAK,CAAC;qBACd;iBACF;;;;;;;;;YACD,OAAO,EAAE,CAAC;QACZ,CAAC;QAEH,cAAC;IAAD,CAAC,AA5FM,CAAc,YAAY,GA4F/B;AAEJ,CAAC;AApGD,sDAoGC;AA+CD,SAAgB,yBAAyB,CACvC,YAAe,EACf,MAAuB;;IAGvB;YAAqB,2BAAY;YA+B/B;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAad;gBAZC,MAAM,CAAC,aAAa,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC;gBACtC,IAAM,WAAW,GAAI,KAAI,CAAC,WAA2C,CAAC,WAAW,CAAC;gBAClF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;oBAChC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACjC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;iBACvC;gBACD,IAAM,OAAO,GAAG,IAAI,8CAAoB,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAM,QAAQ,GAAG,CAAC,UAAC,IAAa,IAAK,OAAA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAvB,CAAuB,CAAC,CAAC;gBAC9D,KAAI,CAAC,OAAO,CAAC,QAAQ;oBACnB,qBAAqB,CACnB,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CACxC,CAAC;;YACN,CAAC;YAKM,8BAAY,GAAnB;;gBACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;;wBAC1C,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;4BAAzB,IAAM,IAAI,WAAA;4BACZ,IAAkC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;yBACxD;;;;;;;;;oBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;iBACrC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAKM,wBAAM,GAAb;;gBACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;oBACrC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;;4BACjC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;gCAAzB,IAAM,IAAI,WAAA;gCACZ,IAAkC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;6BAClD;;;;;;;;;qBACF;oBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAChC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAIM,6BAAW,GAAlB,UAAmB,IAAmC,EAAE,KAAgC,EAAE,GAAU;gBAClG,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;YACzC,CAAC;YAKM,uBAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;gBAAxB,wBAAA,EAAA,eAAwB;gBAClD,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,GAAG,mBAAK,CAAC,QAAQ,EAAE;oBAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;iBAClC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAEH,cAAC;QAAD,CAAC,AA5FM,CAAc,YAAY;QAKjB,UAAO,yBAChB,YAAY,CAAC,OAAO,KACvB,gBAAgB,EAAE,IAAI,EACtB,WAAW,EAAE,UAAC,GAAkC,EAClC,IAA+B,EAC/B,GAAU,IAAK,OAAA,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAA/B,CAA+B,EAC5D,aAAa,EAAE,IAAA,uBAAU,wBACpB,YAAY,CAAC,OAAO,CAAC,aAAa,KACrC,MAAM,EAAQ,CAAC,mBAAK,CAAC,QAAQ,CAAC,EAC9B,YAAY,EAAE,CAAC,mBAAK,CAAC,YAAY,CAAC,IAClC,EACF,GAAG,EAAE,IAAA,uBAAU,EAAC;gBACd,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,IAAI;aACb,CAAC,GACF;WAsEF;AAEJ,CAAC;AAnGD,8DAmGC;AAeD,SAAgB,aAAa,CAAU,OAAyB,EAAE,MAAuB;IACvF,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACnC,OAAO,CAAC,aAAa;QACnB,yBAAyB,CACvB,OAAO,CAAC,aAAa,EAAE,MAAM,CAC9B,CAAC;IACJ,OAAO,OAAO,CAAC;AACjB,CAAC;AAPD,sCAOC"}