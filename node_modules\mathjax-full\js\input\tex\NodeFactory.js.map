{"version": 3, "file": "NodeFactory.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/NodeFactory.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,8DAAqC;AAKrC;IAAA;QAaY,eAAU,GAAe,IAAI,CAAC;QAMhC,YAAO,GACb,EAAC,MAAM,EAAE,WAAW,CAAC,UAAU;YAC9B,OAAO,EAAE,WAAW,CAAC,WAAW;YAChC,MAAM,EAAE,WAAW,CAAC,UAAU;YAC9B,OAAO,EAAE,WAAW,CAAC,WAAW;SAChC,CAAC;IAsHN,CAAC;IA3Ge,sBAAU,GAAxB,UAAyB,OAAoB,EAAE,IAAY,EAClC,QAAwB,EAAE,GAAa,EACvC,IAAe;QADf,yBAAA,EAAA,aAAwB;QAAE,oBAAA,EAAA,QAAa;QAE9D,IAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACxB;QACD,qBAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAWa,uBAAW,GAAzB,UAA0B,OAAoB,EAAE,IAAY,EAClC,GAAa,EAAE,IAAiB;QAAhC,oBAAA,EAAA,QAAa;QAAE,qBAAA,EAAA,SAAiB;QACxD,IAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IACzD,CAAC;IASa,sBAAU,GAAxB,UAAyB,OAAoB,EAAE,IAAY;QACzD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QACD,OAAQ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IASa,uBAAW,GAAzB,UAA0B,OAAoB,EAAE,OAAe;QAC7D,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC3C,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,EAAC,gBAAgB,EAAE,OAAO,EAAC,CAAC,CAAC;QACnF,OAAO,KAAK,CAAC;IACf,CAAC;IAMM,mCAAa,GAApB,UAAqB,UAAsB;QACzC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAOM,yBAAG,GAAV,UAAW,IAAY,EAAE,IAAuB;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAC5B,CAAC;IAOM,iCAAW,GAAlB,UAAmB,IAAyC;QAC1D,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;YACrB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5B;IACH,CAAC;IASM,4BAAM,GAAb,UAAc,IAAY;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxD,IAAM,IAAI,GAAG,IAAI,8BAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,UAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAC,CAAC;QACnD,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAMM,yBAAG,GAAV,UAAW,IAAY;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEH,kBAAC;AAAD,CAAC,AA9ID,IA8IC;AA9IY,kCAAW"}