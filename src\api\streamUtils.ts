/**
 * 流式响应处理工具
 */

/**
 * 处理流式响应数据
 * @param response Fetch API响应对象
 * @param onChunk 每收到一块数据时的回调函数
 * @param onDone 数据接收完成时的回调函数
 */
export async function handleStreamResponse(
  response: Response,
  onChunk: (chunk: string) => void,
  onDone: () => void
): Promise<void> {
  if (!response.body) {
    throw new Error('响应体为空')
  }

  const reader = response.body.getReader()
  const decoder = new TextDecoder()
  let done = false
  let buffer = '' // 用于存储不完整的JSON字符串

  try {
    while (!done) {
      const { value, done: readerDone } = await reader.read()
      done = readerDone
      
      if (value) {
        const chunk = decoder.decode(value, { stream: !done })
        buffer += chunk
        
        // 处理接收到的数据块
        try {
          // 检查是否包含JSON对象
          if (buffer.includes('{"content":')) {
            // 尝试提取完整的JSON对象
            let startIndex = 0
            let processedIndex = 0
            
            while ((startIndex = buffer.indexOf('{"content":', processedIndex)) !== -1) {
              try {
                // 寻找JSON对象的结束位置
                let endIndex = startIndex
                let braceCount = 0
                let inString = false
                let escapeNext = false
                
                for (let i = startIndex; i < buffer.length; i++) {
                  const char = buffer[i]
                  
                  if (escapeNext) {
                    escapeNext = false
                    continue
                  }
                  
                  if (char === '\\') {
                    escapeNext = true
                    continue
                  }
                  
                  if (char === '"' && !escapeNext) {
                    inString = !inString
                    continue
                  }
                  
                  if (inString) continue
                  
                  if (char === '{') {
                    braceCount++
                  } else if (char === '}') {
                    braceCount--
                    if (braceCount === 0) {
                      endIndex = i + 1
                      break
                    }
                  }
                }
                
                if (endIndex > startIndex && braceCount === 0) {
                  // 提取完整的JSON字符串
                  const jsonStr = buffer.substring(startIndex, endIndex)
                  processedIndex = endIndex
                  
                  try {
                    const data = JSON.parse(jsonStr)
                    if (data.content !== undefined) {
                      // 处理内容，保留原始格式
                      onChunk(data.content)
                    }
                  } catch (e) {
                    console.error('解析JSON失败:', e, jsonStr)
                    // 尝试提取content字段的值
                    const contentMatch = jsonStr.match(/"content"\s*:\s*"([^"]*)"/);
                    if (contentMatch && contentMatch[1]) {
                      // 处理转义字符
                      const content = contentMatch[1].replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                      onChunk(content);
                    } else {
                      // 如果无法提取，则发送原始JSON字符串
                      onChunk(jsonStr);
                    }
                  }
                } else {
                  // JSON不完整，等待更多数据
                  break
                }
              } catch (e) {
                console.error('处理JSON对象失败:', e)
                processedIndex = startIndex + 1 // 跳过这个位置，继续处理
              }
            }
            
            // 移除已处理的部分
            if (processedIndex > 0) {
              buffer = buffer.substring(processedIndex)
            }
          } else {
            // 非JSON格式，直接处理为文本
            if (buffer.trim()) {
              onChunk(buffer)
              buffer = ''
            }
          }
        } catch (e) {
          console.error('处理数据块失败:', e)
          // 降级处理，直接显示缓冲区内容
          if (buffer.trim()) {
            onChunk(buffer)
            buffer = ''
          }
        }
      }
    }
    
    // 处理剩余的缓冲区内容
    if (buffer.trim()) {
      onChunk(buffer)
    }
    
    onDone()
  } catch (error) {
    console.error('读取流失败:', error)
    throw error
  }
} 