import { MmlNode } from '../../../core/MmlTree/MmlNode.js';
import { Configuration } from '../Configuration.js';
import { ParseMethod } from '../Types.js';
import { NodeFactory } from '../NodeFactory.js';
import ParseOptions from '../ParseOptions.js';
export declare let BoldsymbolMethods: Record<string, ParseMethod>;
export declare function createBoldToken(factory: NodeFactory, kind: string, def: any, text: string): MmlNode;
export declare function rewriteBoldTokens(arg: {
    data: ParseOptions;
}): void;
export declare const BoldsymbolConfiguration: Configuration;
