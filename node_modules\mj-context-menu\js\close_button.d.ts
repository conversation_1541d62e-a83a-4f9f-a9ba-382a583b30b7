import { AbstractPostable } from './abstract_postable.js';
import { Postable } from './postable.js';
export declare class CloseButton extends AbstractPostable {
    private element;
    protected className: import("./html_classes.js").HtmlClass;
    protected role: string;
    constructor(element: Postable);
    generateHtml(): void;
    protected display(): void;
    unpost(): void;
    keydown(event: KeyboardEvent): void;
    space(event: KeyboardEvent): void;
    mousedown(event: MouseEvent): void;
}
