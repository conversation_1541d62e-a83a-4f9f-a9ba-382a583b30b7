import { Span } from '../audio/span.js';
import { SpeechRuleStore } from '../rule_engine/speech_rule_store.js';
export declare function spaceoutText(node: Element): Span[];
export declare function spaceoutNumber(node: Element): Element[];
export declare function spaceoutIdentifier(node: Element): Element[];
export declare function resetNestingDepth(node: Element): Element[];
export declare function fractionNestingDepth(node: Element): number;
export declare function openingFractionVerbose(node: Element): Span[];
export declare function closingFractionVerbose(node: Element): Span[];
export declare function overFractionVerbose(node: Element): Span[];
export declare function openingFractionBrief(node: Element): Span[];
export declare function closingFractionBrief(node: Element): Span[];
export declare function openingFractionSbrief(node: Element): Span[];
export declare function closingFractionSbrief(node: Element): Span[];
export declare function overFractionSbrief(node: Element): Span[];
export declare function isSmallVulgarFraction(node: Element): Element[];
export declare function nestedSubSuper(node: Element, init: string, replace: {
    sup: string;
    sub: string;
}): string;
export declare function subscriptVerbose(node: Element): Span[];
export declare function subscriptBrief(node: Element): Span[];
export declare function superscriptVerbose(node: Element): Span[];
export declare function superscriptBrief(node: Element): Span[];
export declare function baselineVerbose(node: Element): Span[];
export declare function baselineBrief(node: Element): Span[];
export declare function radicalNestingDepth(node: Element): number;
export declare function openingRadicalVerbose(node: Element): Span[];
export declare function closingRadicalVerbose(node: Element): Span[];
export declare function indexRadicalVerbose(node: Element): Span[];
export declare function openingRadicalBrief(node: Element): Span[];
export declare function closingRadicalBrief(node: Element): Span[];
export declare function indexRadicalBrief(node: Element): Span[];
export declare function openingRadicalSbrief(node: Element): Span[];
export declare function indexRadicalSbrief(node: Element): Span[];
export declare function nestedUnderscript(node: Element): Span[];
export declare function endscripts(_node: Element): Span[];
export declare function nestedOverscript(node: Element): Span[];
export declare function determinantIsSimple(node: Element): Element[];
export declare function generateBaselineConstraint(): string[];
export declare function removeParens(node: Element): Span[];
export declare function generateTensorRules(store: SpeechRuleStore, brief?: boolean): void;
export declare function smallRoot(node: Element): Element[];
