{"version": 3, "sources": ["../../mathjax-full/ts/util/Styles.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lite CssStyleDeclaration replacement\n *                (very limited in scope)\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n/**\n * An object contining name: value pairs\n */\nexport type StyleList = {[name: string]: string};\n\n/**\n * Data for how to map a combined style (like border) to its children\n */\nexport type connection = {\n  children: string[],               // suffix names to add to the base name\n  split: (name: string) => void,    // function to split the value for the children\n  combine: (name: string) => void   // function to combine the child values when one changes\n};\n\n/**\n * A collection of connections\n */\nexport type connections = {[name: string]: connection};\n\n/*********************************************************/\n/**\n * Some common children arrays\n */\nconst TRBL = ['top', 'right', 'bottom', 'left'];\nconst WSC = ['width', 'style', 'color'];\n\n/**\n * Split a style at spaces (taking quotation marks and commas into account)\n *\n * @param {string} text  The combined styles to be split at spaces\n * @return {string[]}    Array of parts of the style (separated by spaces)\n */\nfunction splitSpaces(text: string): string[] {\n  const parts = text.split(/((?:'[^']*'|\"[^\"]*\"|,[\\s\\n]|[^\\s\\n])*)/g);\n  const split = [] as string[];\n  while (parts.length > 1) {\n    parts.shift();\n    split.push(parts.shift());\n  }\n  return split;\n}\n\n/*********************************************************/\n/**\n * Split a top-right-bottom-left group into its parts\n * Format:\n *    x           all are the same value\n *    x y         same as x y x y\n *    x y z       same as x y z y\n *    x y z w     each specified\n *\n * @param {string} name   The style to be processed\n */\n\nfunction splitTRBL(name: string) {\n  const parts = splitSpaces(this.styles[name]);\n  if (parts.length === 0) {\n    parts.push('');\n  }\n  if (parts.length === 1) {\n    parts.push(parts[0]);\n  }\n  if (parts.length === 2) {\n    parts.push(parts[0]);\n  }\n  if (parts.length === 3) {\n    parts.push(parts[1]);\n  }\n  for (const child of Styles.connect[name].children) {\n    this.setStyle(this.childName(name, child), parts.shift());\n  }\n}\n\n/**\n * Combine top-right-bottom-left into one entry\n * (removing unneeded values)\n *\n * @param {string} name   The style to be processed\n */\nfunction combineTRBL(name: string) {\n  const children = Styles.connect[name].children;\n  const parts = [] as string[];\n  for (const child of children) {\n    const part = this.styles[name + '-' + child];\n    if (!part) {\n      delete this.styles[name];\n      return;\n    }\n    parts.push(part);\n  }\n  if (parts[3] === parts[1]) {\n    parts.pop();\n    if (parts[2] === parts[0]) {\n      parts.pop();\n      if (parts[1] === parts[0]) {\n        parts.pop();\n      }\n    }\n  }\n  this.styles[name] = parts.join(' ');\n}\n\n/*********************************************************/\n/**\n * Use the same value for all children\n *\n * @param {string} name   The style to be processed\n */\nfunction splitSame(name: string) {\n  for (const child of Styles.connect[name].children) {\n    this.setStyle(this.childName(name, child), this.styles[name]);\n  }\n}\n\n/**\n * Check that all children have the same values and\n * if so, set the parent to that value\n *\n * @param {string} name   The style to be processed\n */\nfunction combineSame(name: string) {\n  const children = [...Styles.connect[name].children];\n  const value = this.styles[this.childName(name, children.shift())];\n  for (const child of children) {\n    if (this.styles[this.childName(name, child)] !== value) {\n      delete this.styles[name];\n      return;\n    }\n  }\n  this.styles[name] = value;\n}\n\n/*********************************************************/\n/**\n * Patterns for the parts of a boarder\n */\nconst BORDER: {[name: string]: RegExp} = {\n  width: /^(?:[\\d.]+(?:[a-z]+)|thin|medium|thick|inherit|initial|unset)$/,\n  style: /^(?:none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset|inherit|initial|unset)$/\n};\n\n/**\n * Split a width-style-color border definition\n *\n * @param {string} name   The style to be processed\n */\nfunction splitWSC(name: string) {\n  let parts = {width: '', style: '', color: ''} as StyleList;\n  for (const part of splitSpaces(this.styles[name])) {\n    if (part.match(BORDER.width) && parts.width === '') {\n      parts.width = part;\n    } else if (part.match(BORDER.style) && parts.style === '') {\n      parts.style = part;\n    } else {\n      parts.color = part;\n    }\n  }\n  for (const child of Styles.connect[name].children) {\n    this.setStyle(this.childName(name, child), parts[child]);\n  }\n}\n\n/**\n * Combine with-style-color border definition from children\n *\n * @param {string} name   The style to be processed\n */\nfunction combineWSC(name: string) {\n  const parts = [] as string[];\n  for (const child of Styles.connect[name].children) {\n    const value = this.styles[this.childName(name, child)];\n    if (value) {\n      parts.push(value);\n    }\n  }\n  if (parts.length) {\n    this.styles[name] = parts.join(' ');\n  } else {\n    delete this.styles[name];\n  }\n}\n\n/*********************************************************/\n/**\n * Patterns for the parts of a font declaration\n */\nconst FONT: {[name: string]: RegExp} = {\n  style: /^(?:normal|italic|oblique|inherit|initial|unset)$/,\n  variant: new RegExp('^(?:' +\n                      ['normal|none',\n                       'inherit|initial|unset',\n                       'common-ligatures|no-common-ligatures',\n                       'discretionary-ligatures|no-discretionary-ligatures',\n                       'historical-ligatures|no-historical-ligatures',\n                       'contextual|no-contextual',\n                       '(?:stylistic|character-variant|swash|ornaments|annotation)\\\\([^)]*\\\\)',\n                       'small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps',\n                       'lining-nums|oldstyle-nums|proportional-nums|tabular-nums',\n                       'diagonal-fractions|stacked-fractions',\n                       'ordinal|slashed-zero',\n                       'jis78|jis83|jis90|jis04|simplified|traditional',\n                       'full-width|proportional-width',\n                       'ruby'].join('|') + ')$'),\n  weight: /^(?:normal|bold|bolder|lighter|[1-9]00|inherit|initial|unset)$/,\n  stretch: new RegExp('^(?:' +\n                      ['normal',\n                       '(?:(?:ultra|extra|semi)-)?condensed',\n                       '(?:(?:semi|extra|ulta)-)?expanded',\n                       'inherit|initial|unset']. join('|') + ')$'),\n  size: new RegExp('^(?:' +\n                   ['xx-small|x-small|small|medium|large|x-large|xx-large|larger|smaller',\n                    '[\\d.]+%|[\\d.]+[a-z]+',\n                    'inherit|initial|unset'].join('|') + ')' +\n                   '(?:\\/(?:normal|[\\d.\\+](?:%|[a-z]+)?))?$')\n};\n\n/**\n * Split a font declaration into is parts (not perfect but good enough for now)\n *\n * @param {string} name   The style to be processed\n */\nfunction splitFont(name: string) {\n  const parts = splitSpaces(this.styles[name]);\n  //\n  //  The parts found (array means can be more than one word)\n  //\n  const value = {\n    style: '', variant: [], weight: '', stretch: '',\n    size: '', family: '', 'line-height': ''\n  } as {[name: string]: string | string[]};\n  for (const part of parts) {\n    value.family = part; // assume it is family unless otherwise (family must be present)\n    for (const name of Object.keys(FONT)) {\n      if ((Array.isArray(value[name]) || value[name] === '') && part.match(FONT[name])) {\n        if (name === 'size') {\n          //\n          // Handle size/line-height\n          //\n          const [size, height] = part.split(/\\//);\n          value[name] = size;\n          if (height) {\n            value['line-height'] = height;\n          }\n        } else if (value.size === '') {\n          //\n          // style, weight, variant, stretch must appear before size\n          //\n          if (Array.isArray(value[name])) {\n            (value[name] as string[]).push(part);\n          } else {\n            value[name] = part;\n          }\n        }\n      }\n    }\n  }\n  saveFontParts(name, value);\n  delete this.styles[name]; // only use the parts, not the font declaration itself\n}\n\n/**\n * @param {string} name   The style to be processed\n * @param {{[name: string]: string | string[]}} value  The list of parts detected above\n */\nfunction saveFontParts(name: string, value: {[name: string]: string | string[]}) {\n  for (const child of Styles.connect[name].children) {\n    const cname = this.childName(name, child);\n    if (Array.isArray(value[child])) {\n      const values = value[child] as string[];\n      if (values.length) {\n        this.styles[cname] = values.join(' ');\n      }\n    } else  if (value[child] !== '') {\n      this.styles[cname] = value[child];\n    }\n  }\n}\n\n/**\n * Combine font parts into one (we don't actually do that)\n */\nfunction combineFont(_name: string) {}\n\n/*********************************************************/\n/**\n * Implements the Styles object (lite version of CssStyleDeclaration)\n */\nexport class Styles {\n\n  /**\n   * Patterns for style values and comments\n   */\n  public static pattern: {[name: string]: RegExp} = {\n    style: /([-a-z]+)[\\s\\n]*:[\\s\\n]*((?:'[^']*'|\"[^\"]*\"|\\n|.)*?)[\\s\\n]*(?:;|$)/g,\n    comment: /\\/\\*[^]*?\\*\\//g\n  };\n\n  /**\n   * The mapping of parents to children, and how to split and combine them\n   */\n  public static connect: connections = {\n    padding: {\n      children: TRBL,\n      split: splitTRBL,\n      combine: combineTRBL\n    },\n\n    border: {\n      children: TRBL,\n      split: splitSame,\n      combine: combineSame\n    },\n    'border-top': {\n      children: WSC,\n      split: splitWSC,\n      combine: combineWSC\n    },\n    'border-right': {\n      children: WSC,\n      split: splitWSC,\n      combine: combineWSC\n    },\n    'border-bottom': {\n      children: WSC,\n      split: splitWSC,\n      combine: combineWSC\n    },\n    'border-left': {\n      children: WSC,\n      split: splitWSC,\n      combine: combineWSC\n    },\n    'border-width': {\n      children: TRBL,\n      split: splitTRBL,\n      combine: null      // means its children combine to a different parent\n    },\n    'border-style': {\n      children: TRBL,\n      split: splitTRBL,\n      combine: null      // means its children combine to a different parent\n    },\n    'border-color': {\n      children: TRBL,\n      split: splitTRBL,\n      combine: null      // means its children combine to a different parent\n    },\n\n    font: {\n      children: ['style', 'variant', 'weight', 'stretch', 'line-height', 'size', 'family'],\n      split: splitFont,\n      combine: combineFont\n    }\n  };\n\n  /**\n   * The list of styles defined for this declaration\n   */\n  protected styles: StyleList;\n\n  /**\n   * @param {string} cssText  The initial definition for the style\n   * @constructor\n   */\n  constructor(cssText: string = '') {\n    this.parse(cssText);\n  }\n\n  /**\n   * @return {string}  The CSS string for the styles currently defined\n   */\n  public get cssText(): string {\n    const styles = [] as string[];\n    for (const name of Object.keys(this.styles)) {\n      const parent = this.parentName(name);\n      if (!this.styles[parent]) {\n        styles.push(name + ': ' + this.styles[name] + ';');\n      }\n    }\n    return styles.join(' ');\n  }\n\n  /**\n   * @param {string} name   The name of the style to set\n   * @param {string|number|boolean} value The value to set it to\n   */\n  public set(name: string, value: string | number | boolean) {\n    name = this.normalizeName(name);\n    this.setStyle(name, value as string);\n    //\n    // If there is no combine function ,the children combine to\n    // a separate parent (e.g., border-width sets border-top-width, etc.\n    // and combines to border-top)\n    //\n    if (Styles.connect[name] && !Styles.connect[name].combine) {\n      this.combineChildren(name);\n      delete this.styles[name];\n    }\n    //\n    // If we just changed a child, we need to try to combine\n    // it with its parent's other children\n    //\n    while (name.match(/-/)) {\n      name = this.parentName(name);\n      if (!Styles.connect[name]) break;\n      Styles.connect[name].combine.call(this, name);\n    }\n  }\n\n  /**\n   * @param {string} name  The name of the style to get\n   * @return {string}      The value of the style (or empty string if not defined)\n   */\n  public get(name: string): string {\n    name = this.normalizeName(name);\n    return (this.styles.hasOwnProperty(name) ? this.styles[name] : '');\n  }\n\n  /**\n   * @param {string} name   The name of the style to set (without causing parent updates)\n   * @param {string} value  The value to set it to\n   */\n  protected setStyle(name: string, value: string) {\n    this.styles[name] = value;\n    if (Styles.connect[name] && Styles.connect[name].children) {\n      Styles.connect[name].split.call(this, name);\n    }\n    if (value === '') {\n      delete this.styles[name];\n    }\n  }\n\n  /**\n   * @param {string} name   The name of the style whose parent is to be combined\n   */\n  protected combineChildren(name: string) {\n    const parent = this.parentName(name);\n    for (const child of Styles.connect[name].children) {\n      const cname = this.childName(parent, child);\n      Styles.connect[cname].combine.call(this, cname);\n    }\n  }\n\n  /**\n   * @param {string} name   The name of the style whose parent style is to be found\n   * @return {string}       The name of the parent, or '' if none\n   */\n  protected parentName(name: string): string {\n    const parent = name.replace(/-[^-]*$/, '');\n    return (name === parent ? '' : parent);\n  }\n\n  /**\n   * @param {string} name   The name of the parent style\n   * @param {string} child  The suffix to be added to the parent\n   * @preturn {string}      The combined name\n   */\n  protected childName(name: string, child: string) {\n    //\n    // If the child contains a dash, it is already the fill name\n    //\n    if (child.match(/-/)) {\n      return child;\n    }\n    //\n    // For non-combining styles, like border-width, insert\n    //   the child name before the find word, e.g., border-top-width\n    //\n    if (Styles.connect[name] && !Styles.connect[name].combine) {\n      child += name.replace(/.*-/, '-');\n      name = this.parentName(name);\n    }\n    return name + '-' + child;\n  }\n\n  /**\n   * @param {string} name  The name of a style to normalize\n   * @return {string}      The name converted from CamelCase to lowercase with dashes\n   */\n  protected normalizeName(name: string): string {\n    return name.replace(/[A-Z]/g, c => '-' + c.toLowerCase());\n  }\n\n  /**\n   * @param {string} cssText  A style text string to be parsed into separate styles\n   *                          (by using this.set(), we get all the sub-styles created\n   *                           as well as the merged style shorthands)\n   */\n  protected parse(cssText: string = '') {\n    let PATTERN = (this.constructor as typeof Styles).pattern;\n    this.styles = {};\n    const parts = cssText.replace(PATTERN.comment, '').split(PATTERN.style);\n    while (parts.length > 1) {\n      let [space, name, value] = parts.splice(0, 3);\n      if (space.match(/[^\\s\\n]/)) return;\n      this.set(name, value);\n    }\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,QAAM,OAAO,CAAC,OAAO,SAAS,UAAU,MAAM;AAC9C,QAAM,MAAM,CAAC,SAAS,SAAS,OAAO;AAQtC,aAAS,YAAY,MAAY;AAC/B,UAAM,QAAQ,KAAK,MAAM,yCAAyC;AAClE,UAAM,QAAQ,CAAA;AACd,aAAO,MAAM,SAAS,GAAG;AACvB,cAAM,MAAK;AACX,cAAM,KAAK,MAAM,MAAK,CAAE;;AAE1B,aAAO;IACT;AAcA,aAAS,UAAU,MAAY;;AAC7B,UAAM,QAAQ,YAAY,KAAK,OAAO,IAAI,CAAC;AAC3C,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM,KAAK,EAAE;;AAEf,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM,KAAK,MAAM,CAAC,CAAC;;AAErB,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM,KAAK,MAAM,CAAC,CAAC;;AAErB,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM,KAAK,MAAM,CAAC,CAAC;;;AAErB,iBAAoB,KAAA,SAAA,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,QAAK,GAAA;AACd,eAAK,SAAS,KAAK,UAAU,MAAM,KAAK,GAAG,MAAM,MAAK,CAAE;;;;;;;;;;;IAE5D;AAQA,aAAS,YAAY,MAAY;;AAC/B,UAAM,WAAW,OAAO,QAAQ,IAAI,EAAE;AACtC,UAAM,QAAQ,CAAA;;AACd,iBAAoB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAzB,cAAM,QAAK,aAAA;AACd,cAAM,OAAO,KAAK,OAAO,OAAO,MAAM,KAAK;AAC3C,cAAI,CAAC,MAAM;AACT,mBAAO,KAAK,OAAO,IAAI;AACvB;;AAEF,gBAAM,KAAK,IAAI;;;;;;;;;;;AAEjB,UAAI,MAAM,CAAC,MAAM,MAAM,CAAC,GAAG;AACzB,cAAM,IAAG;AACT,YAAI,MAAM,CAAC,MAAM,MAAM,CAAC,GAAG;AACzB,gBAAM,IAAG;AACT,cAAI,MAAM,CAAC,MAAM,MAAM,CAAC,GAAG;AACzB,kBAAM,IAAG;;;;AAIf,WAAK,OAAO,IAAI,IAAI,MAAM,KAAK,GAAG;IACpC;AAQA,aAAS,UAAU,MAAY;;;AAC7B,iBAAoB,KAAA,SAAA,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,QAAK,GAAA;AACd,eAAK,SAAS,KAAK,UAAU,MAAM,KAAK,GAAG,KAAK,OAAO,IAAI,CAAC;;;;;;;;;;;IAEhE;AAQA,aAAS,YAAY,MAAY;;AAC/B,UAAM,WAAQ,cAAA,CAAA,GAAA,OAAO,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA;AAClD,UAAM,QAAQ,KAAK,OAAO,KAAK,UAAU,MAAM,SAAS,MAAK,CAAE,CAAC;;AAChE,iBAAoB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAzB,cAAM,QAAK,aAAA;AACd,cAAI,KAAK,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,MAAM,OAAO;AACtD,mBAAO,KAAK,OAAO,IAAI;AACvB;;;;;;;;;;;;AAGJ,WAAK,OAAO,IAAI,IAAI;IACtB;AAMA,QAAM,SAAmC;MACvC,OAAO;MACP,OAAO;;AAQT,aAAS,SAAS,MAAY;;AAC5B,UAAI,QAAQ,EAAC,OAAO,IAAI,OAAO,IAAI,OAAO,GAAE;;AAC5C,iBAAmB,KAAA,SAAA,YAAY,KAAK,OAAO,IAAI,CAAC,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,OAAI,GAAA;AACb,cAAI,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,UAAU,IAAI;AAClD,kBAAM,QAAQ;qBACL,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,UAAU,IAAI;AACzD,kBAAM,QAAQ;iBACT;AACL,kBAAM,QAAQ;;;;;;;;;;;;;AAGlB,iBAAoB,KAAA,SAAA,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,QAAK,GAAA;AACd,eAAK,SAAS,KAAK,UAAU,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;;;;;;;;;;;IAE3D;AAOA,aAAS,WAAW,MAAY;;AAC9B,UAAM,QAAQ,CAAA;;AACd,iBAAoB,KAAA,SAAA,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,QAAK,GAAA;AACd,cAAM,QAAQ,KAAK,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AACrD,cAAI,OAAO;AACT,kBAAM,KAAK,KAAK;;;;;;;;;;;;AAGpB,UAAI,MAAM,QAAQ;AAChB,aAAK,OAAO,IAAI,IAAI,MAAM,KAAK,GAAG;aAC7B;AACL,eAAO,KAAK,OAAO,IAAI;;IAE3B;AAMA,QAAM,OAAiC;MACrC,OAAO;MACP,SAAS,IAAI,OAAO,SACA;QAAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAM,EAAE,KAAK,GAAG,IAAI,IAAI;MAC7C,QAAQ;MACR,SAAS,IAAI,OAAO,SACA;QAAC;QACA;QACA;QACA;MAAuB,EAAG,KAAK,GAAG,IAAI,IAAI;MAC/D,MAAM,IAAI,OAAO,SACA;QAAC;QACA;QACA;MAAuB,EAAE,KAAK,GAAG,IAAI,uCACG;;AAQ5D,aAAS,UAAU,MAAY;;AAC7B,UAAM,QAAQ,YAAY,KAAK,OAAO,IAAI,CAAC;AAI3C,UAAM,QAAQ;QACZ,OAAO;QAAI,SAAS,CAAA;QAAI,QAAQ;QAAI,SAAS;QAC7C,MAAM;QAAI,QAAQ;QAAI,eAAe;;;AAEvC,iBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,cAAM,OAAI,UAAA;AACb,gBAAM,SAAS;;AACf,qBAAmB,MAAA,MAAA,QAAA,SAAA,OAAO,KAAK,IAAI,CAAC,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAjC,kBAAM,SAAI,GAAA;AACb,mBAAK,MAAM,QAAQ,MAAM,MAAI,CAAC,KAAK,MAAM,MAAI,MAAM,OAAO,KAAK,MAAM,KAAK,MAAI,CAAC,GAAG;AAChF,oBAAI,WAAS,QAAQ;AAIb,sBAAA,KAAA,OAAiB,KAAK,MAAM,IAAI,GAAC,CAAA,GAAhC,OAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACnB,wBAAM,MAAI,IAAI;AACd,sBAAI,QAAQ;AACV,0BAAM,aAAa,IAAI;;2BAEhB,MAAM,SAAS,IAAI;AAI5B,sBAAI,MAAM,QAAQ,MAAM,MAAI,CAAC,GAAG;AAC7B,0BAAM,MAAI,EAAe,KAAK,IAAI;yBAC9B;AACL,0BAAM,MAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;;AAMxB,oBAAc,MAAM,KAAK;AACzB,aAAO,KAAK,OAAO,IAAI;IACzB;AAMA,aAAS,cAAc,MAAc,OAA0C;;;AAC7E,iBAAoB,KAAA,SAAA,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,cAAM,QAAK,GAAA;AACd,cAAM,QAAQ,KAAK,UAAU,MAAM,KAAK;AACxC,cAAI,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG;AAC/B,gBAAM,SAAS,MAAM,KAAK;AAC1B,gBAAI,OAAO,QAAQ;AACjB,mBAAK,OAAO,KAAK,IAAI,OAAO,KAAK,GAAG;;qBAE5B,MAAM,KAAK,MAAM,IAAI;AAC/B,iBAAK,OAAO,KAAK,IAAI,MAAM,KAAK;;;;;;;;;;;;IAGtC;AAKA,aAAS,YAAY,OAAa;IAAG;AAMrC,QAAA,SAAA,WAAA;AA6EE,eAAAA,QAAY,SAAoB;AAApB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAoB;AAC9B,aAAK,MAAM,OAAO;MACpB;AAKA,aAAA,eAAWA,QAAA,WAAA,WAAO;aAAlB,WAAA;;AACE,cAAM,SAAS,CAAA;;AACf,qBAAmB,KAAA,SAAA,OAAO,KAAK,KAAK,MAAM,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAxC,kBAAM,SAAI,GAAA;AACb,kBAAM,WAAS,KAAK,WAAW,MAAI;AACnC,kBAAI,CAAC,KAAK,OAAO,QAAM,GAAG;AACxB,uBAAO,KAAK,SAAO,OAAO,KAAK,OAAO,MAAI,IAAI,GAAG;;;;;;;;;;;;AAGrD,iBAAO,OAAO,KAAK,GAAG;QACxB;;;;AAMO,MAAAA,QAAA,UAAA,MAAP,SAAW,MAAc,OAAgC;AACvD,eAAO,KAAK,cAAc,IAAI;AAC9B,aAAK,SAAS,MAAM,KAAe;AAMnC,YAAIA,QAAO,QAAQ,IAAI,KAAK,CAACA,QAAO,QAAQ,IAAI,EAAE,SAAS;AACzD,eAAK,gBAAgB,IAAI;AACzB,iBAAO,KAAK,OAAO,IAAI;;AAMzB,eAAO,KAAK,MAAM,GAAG,GAAG;AACtB,iBAAO,KAAK,WAAW,IAAI;AAC3B,cAAI,CAACA,QAAO,QAAQ,IAAI;AAAG;AAC3B,UAAAA,QAAO,QAAQ,IAAI,EAAE,QAAQ,KAAK,MAAM,IAAI;;MAEhD;AAMO,MAAAA,QAAA,UAAA,MAAP,SAAW,MAAY;AACrB,eAAO,KAAK,cAAc,IAAI;AAC9B,eAAQ,KAAK,OAAO,eAAe,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI;MACjE;AAMU,MAAAA,QAAA,UAAA,WAAV,SAAmB,MAAc,OAAa;AAC5C,aAAK,OAAO,IAAI,IAAI;AACpB,YAAIA,QAAO,QAAQ,IAAI,KAAKA,QAAO,QAAQ,IAAI,EAAE,UAAU;AACzD,UAAAA,QAAO,QAAQ,IAAI,EAAE,MAAM,KAAK,MAAM,IAAI;;AAE5C,YAAI,UAAU,IAAI;AAChB,iBAAO,KAAK,OAAO,IAAI;;MAE3B;AAKU,MAAAA,QAAA,UAAA,kBAAV,SAA0B,MAAY;;AACpC,YAAM,SAAS,KAAK,WAAW,IAAI;;AACnC,mBAAoB,KAAA,SAAAA,QAAO,QAAQ,IAAI,EAAE,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9C,gBAAM,QAAK,GAAA;AACd,gBAAM,QAAQ,KAAK,UAAU,QAAQ,KAAK;AAC1C,YAAAA,QAAO,QAAQ,KAAK,EAAE,QAAQ,KAAK,MAAM,KAAK;;;;;;;;;;;MAElD;AAMU,MAAAA,QAAA,UAAA,aAAV,SAAqB,MAAY;AAC/B,YAAM,SAAS,KAAK,QAAQ,WAAW,EAAE;AACzC,eAAQ,SAAS,SAAS,KAAK;MACjC;AAOU,MAAAA,QAAA,UAAA,YAAV,SAAoB,MAAc,OAAa;AAI7C,YAAI,MAAM,MAAM,GAAG,GAAG;AACpB,iBAAO;;AAMT,YAAIA,QAAO,QAAQ,IAAI,KAAK,CAACA,QAAO,QAAQ,IAAI,EAAE,SAAS;AACzD,mBAAS,KAAK,QAAQ,OAAO,GAAG;AAChC,iBAAO,KAAK,WAAW,IAAI;;AAE7B,eAAO,OAAO,MAAM;MACtB;AAMU,MAAAA,QAAA,UAAA,gBAAV,SAAwB,MAAY;AAClC,eAAO,KAAK,QAAQ,UAAU,SAAA,GAAC;AAAI,iBAAA,MAAM,EAAE,YAAW;QAAnB,CAAqB;MAC1D;AAOU,MAAAA,QAAA,UAAA,QAAV,SAAgB,SAAoB;AAApB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAoB;AAClC,YAAI,UAAW,KAAK,YAA8B;AAClD,aAAK,SAAS,CAAA;AACd,YAAM,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,EAAE,EAAE,MAAM,QAAQ,KAAK;AACtE,eAAO,MAAM,SAAS,GAAG;AACnB,cAAA,KAAA,OAAuB,MAAM,OAAO,GAAG,CAAC,GAAC,CAAA,GAAxC,QAAK,GAAA,CAAA,GAAE,SAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AACvB,cAAI,MAAM,MAAM,SAAS;AAAG;AAC5B,eAAK,IAAI,QAAM,KAAK;;MAExB;AA7Mc,MAAAA,QAAA,UAAoC;QAChD,OAAO;QACP,SAAS;;AAMG,MAAAA,QAAA,UAAuB;QACnC,SAAS;UACP,UAAU;UACV,OAAO;UACP,SAAS;;QAGX,QAAQ;UACN,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,cAAc;UACZ,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,gBAAgB;UACd,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,iBAAiB;UACf,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,eAAe;UACb,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,gBAAgB;UACd,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,gBAAgB;UACd,UAAU;UACV,OAAO;UACP,SAAS;;QAEX,gBAAgB;UACd,UAAU;UACV,OAAO;UACP,SAAS;;QAGX,MAAM;UACJ,UAAU,CAAC,SAAS,WAAW,UAAU,WAAW,eAAe,QAAQ,QAAQ;UACnF,OAAO;UACP,SAAS;;;AAoJf,aAAAA;MApNA;AAAa,YAAA,SAAA;;;", "names": ["Styles"]}