/*!
  Theme: Silk Light
  Author: <PERSON> (https://github.com/Misterio77)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#385156;background:#e9f1ef}.hljs ::selection,.hljs::selection{background-color:#90b7b6;color:#385156}.hljs-comment{color:#5c787b}.hljs-tag{color:#4b5b5f}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#385156}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#cf432e}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#d27f46}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#cfad25}.hljs-strong{font-weight:700;color:#cfad25}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#6ca38c}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#329ca2}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#39aac9}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#6e6582}.hljs-emphasis{color:#6e6582;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#865369}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}