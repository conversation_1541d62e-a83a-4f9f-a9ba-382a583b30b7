{"version": 3, "file": "mtext.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mtext.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AA2CA,SAAgB,gBAAgB,CAA+B,IAAO;;IAEpE;YAAqB,2BAAI;YAAlB;;YAsCP,CAAC;YApBW,4BAAU,GAApB;gBACE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBACjC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;gBAMtC,IAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpG,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,MAAM,EAAE;oBACvD,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAW,CAAC;oBAClE,IAAM,IAAI,GAAI,IAAI,CAAC,WAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAClG,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;wBACzC,IAAI,CAAC,WAAW,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC3E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC5F,OAAO;iBACR;gBACD,iBAAM,UAAU,WAAE,CAAC;YACrB,CAAC;YAEH,cAAC;QAAD,CAAC,AAtCM,CAAc,IAAI;QAQT,eAAY,GAAG;YAC3B,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;YAC1B,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC;YACvB,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;YACzB,aAAa,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;SAC/B;WAyBF;AAEJ,CAAC;AA1CD,4CA0CC"}