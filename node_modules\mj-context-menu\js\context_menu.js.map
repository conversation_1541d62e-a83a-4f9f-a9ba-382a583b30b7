{"version": 3, "file": "context_menu.js", "sourceRoot": "", "sources": ["../ts/context_menu.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAwBA,uDAAgD;AAChD,qDAA8C;AAC9C,iDAA0C;AAE1C,uDAAgD;AAIhD;IAAiC,+BAAY;IA8D3C,qBAAmB,OAAsB;QAAzC,YACE,iBAAO,SAER;QAHkB,aAAO,GAAP,OAAO,CAAe;QAxDlC,QAAE,GAAW,EAAE,CAAC;QAMf,YAAM,GAAY,KAAK,CAAC;QAYxB,YAAM,GAAc,IAAI,yBAAS,CAAC,KAAI,CAAC,CAAC;QAYxC,aAAO,GAAe,EAAE,CAAC;QA4B/B,KAAI,CAAC,YAAY,GAAG,IAAI,+BAAY,EAAoB,CAAC;;IAC3D,CAAC;IAtBa,oBAAQ,GAAtB,UACE,OAAsB,EACtB,EAEqD;YAF9C,IAAI,UAAA,EAAS,KAAK,WAAA,EAAE,UAAW,EAAP,EAAE,mBAAG,EAAE,KAAA;QAItC,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC;QACjB,IAAI,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,SAAS,CAAC,OAAO,EAAE,CAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,EAA3C,CAA2C,CAAC,CAAC;QAC/D,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;QAC1B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAcM,kCAAY,GAAnB;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;QACD,iBAAM,YAAY,WAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,6BAAW,CAAC,WAAW,CAAC,CAAC,CAAC;QAEpD,IAAI,WAAW,GAAG,kDAAkD;YAClE,uDAAuD,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,sBAAsB,GAAG,WAAW,CAAC,CAAC;QACxE,IAAI,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,mBAAmB,GAAG,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAClC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EACX,UAAS,KAAY;YACnB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC;IAKS,6BAAO,GAAjB;QACE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAKM,4BAAM,GAAb,UAAc,MAAqB;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAKM,4BAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,OAAO;SACR;QACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;QACD,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAKM,0BAAI,GAAX,UAAY,MAAqB;QAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpC,CAAC;IAKM,2BAAK,GAAZ,UAAa,MAAqB;QAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAChC,CAAC;IAMD,sBAAW,8BAAK;aAAhB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAKD,sBAAW,8BAAK;aAAhB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAuBM,0BAAI,GAAX,UAAY,aAAmB,EAAE,GAAY;QAC3C,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;aAC7B;YACD,iBAAM,IAAI,YAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YAC/B,OAAO;SACR;QACD,IAAI,KAAK,GAAG,aAAa,CAAC;QAC1B,IAAI,IAAI,CAAC;QACT,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,GAAG,KAAK,CAAC;SACd;QACD,IAAI,CAAS,CAAC;QACd,IAAI,CAAS,CAAC;QACd,IAAI,KAAK,YAAY,UAAU,EAAE;YAC/B,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE;gBAC7B,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU;oBAC1C,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC;gBACtC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS;oBACzC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;aACtC;SACF;QACD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE;YACpB,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC;YACxE,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;YACvE,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACxC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;YAC3C,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;SAC5C;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAChC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,MAAM,EAAE;YAC7D,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;SAC3D;QAQD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC;IAMM,oCAAc,GAArB,UAAsB,MAAgB;QACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAMM,sCAAgB,GAAvB,UAAwB,MAAgB;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IAKM,mCAAa,GAApB;QACE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAE,EAAV,CAAU,CAAC,CAAC;IACxC,CAAC;IAKM,4BAAM,GAAb;QACE,OAAO,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC;IACpB,CAAC;IAOO,2BAAK,GAAb,UAAc,IAAiB;QAC7B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;YACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACrB;IACH,CAAC;IAEH,kBAAC;AAAD,CAAC,AApRD,CAAiC,+BAAY,GAoR5C;AApRY,kCAAW"}