{"version": 3, "file": "monospace.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/monospace.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,SAAS,GAAyB;IAC3C,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAClB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACvB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;IACpB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC1B,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAClB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC3B,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IACvB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACxB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACtB,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;IAC5B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACvB,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC;IACxB,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC;IACtB,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;CAC1B,CAAC"}