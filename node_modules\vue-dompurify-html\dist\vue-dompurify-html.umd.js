(function(e,u){typeof exports=="object"&&typeof module<"u"?u(exports,require("dompurify")):typeof define=="function"&&define.amd?define(["exports","dompurify"],u):(e=typeof globalThis<"u"?globalThis:e||self,u(e.VueDOMPurifyHTML={},e.DOMPurify))})(this,function(e,u){"use strict";function M(n,r){const t=n.hooks??{};let o;for(o in t){const d=t[o];d!==void 0&&r.addHook(o,d)}}function a(){return u()}function c(n={},r=a){const t=r();M(n,t);const o=function(i){const s=i.value;if(i.oldValue===s)return;const f=`${s}`,m=i.arg,v=n.namedConfigurations,y=n.default??{};return v&&m!==void 0?t.sanitize(f,v[m]??y):t.sanitize(f,y)},d=function(i,s){const f=o(s);f!==void 0&&(i.innerHTML=f)},p={mounted:d,updated:d};return n.enableSSRPropsSupport?{...p,getSSRProps(i){return{innerHTML:o(i)}}}:p}const l={install(n,r={},t=a){n.directive("dompurify-html",c(r,t))}};e.buildVueDompurifyHTMLDirective=c,e.default=l,e.vueDompurifyHTMLPlugin=l,Object.defineProperties(e,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
