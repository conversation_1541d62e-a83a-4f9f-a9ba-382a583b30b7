import {combineWithMathJax} from '../../../../../js/components/global.js';
import {VERSION} from '../../../../../js/components/version.js';

import * as module1 from '../../../../../js/output/common/FontData.js';
import * as module2 from '../../../../../js/output/common/Notation.js';
import * as module3 from '../../../../../js/output/common/OutputJax.js';
import * as module4 from '../../../../../js/output/common/Wrapper.js';
import * as module5 from '../../../../../js/output/common/WrapperFactory.js';
import * as module6 from '../../../../../js/output/common/Wrappers/TeXAtom.js';
import * as module7 from '../../../../../js/output/common/Wrappers/TextNode.js';
import * as module8 from '../../../../../js/output/common/Wrappers/maction.js';
import * as module9 from '../../../../../js/output/common/Wrappers/math.js';
import * as module10 from '../../../../../js/output/common/Wrappers/menclose.js';
import * as module11 from '../../../../../js/output/common/Wrappers/mfenced.js';
import * as module12 from '../../../../../js/output/common/Wrappers/mfrac.js';
import * as module13 from '../../../../../js/output/common/Wrappers/mglyph.js';
import * as module14 from '../../../../../js/output/common/Wrappers/mi.js';
import * as module15 from '../../../../../js/output/common/Wrappers/mmultiscripts.js';
import * as module16 from '../../../../../js/output/common/Wrappers/mn.js';
import * as module17 from '../../../../../js/output/common/Wrappers/mo.js';
import * as module18 from '../../../../../js/output/common/Wrappers/mpadded.js';
import * as module19 from '../../../../../js/output/common/Wrappers/mroot.js';
import * as module20 from '../../../../../js/output/common/Wrappers/mrow.js';
import * as module21 from '../../../../../js/output/common/Wrappers/ms.js';
import * as module22 from '../../../../../js/output/common/Wrappers/mspace.js';
import * as module23 from '../../../../../js/output/common/Wrappers/msqrt.js';
import * as module24 from '../../../../../js/output/common/Wrappers/msubsup.js';
import * as module25 from '../../../../../js/output/common/Wrappers/mtable.js';
import * as module26 from '../../../../../js/output/common/Wrappers/mtd.js';
import * as module27 from '../../../../../js/output/common/Wrappers/mtext.js';
import * as module28 from '../../../../../js/output/common/Wrappers/mtr.js';
import * as module29 from '../../../../../js/output/common/Wrappers/munderover.js';
import * as module30 from '../../../../../js/output/common/Wrappers/scriptbase.js';
import * as module31 from '../../../../../js/output/common/Wrappers/semantics.js';
import * as module32 from '../../../../../js/output/svg.js';
import * as module33 from '../../../../../js/output/svg/FontCache.js';
import * as module34 from '../../../../../js/output/svg/FontData.js';
import * as module35 from '../../../../../js/output/svg/Notation.js';
import * as module36 from '../../../../../js/output/svg/Wrapper.js';
import * as module37 from '../../../../../js/output/svg/WrapperFactory.js';
import * as module38 from '../../../../../js/output/svg/Wrappers.js';
import * as module39 from '../../../../../js/output/svg/Wrappers/TeXAtom.js';
import * as module40 from '../../../../../js/output/svg/Wrappers/TextNode.js';
import * as module41 from '../../../../../js/output/svg/Wrappers/maction.js';
import * as module42 from '../../../../../js/output/svg/Wrappers/math.js';
import * as module43 from '../../../../../js/output/svg/Wrappers/menclose.js';
import * as module44 from '../../../../../js/output/svg/Wrappers/merror.js';
import * as module45 from '../../../../../js/output/svg/Wrappers/mfenced.js';
import * as module46 from '../../../../../js/output/svg/Wrappers/mfrac.js';
import * as module47 from '../../../../../js/output/svg/Wrappers/mglyph.js';
import * as module48 from '../../../../../js/output/svg/Wrappers/mi.js';
import * as module49 from '../../../../../js/output/svg/Wrappers/mmultiscripts.js';
import * as module50 from '../../../../../js/output/svg/Wrappers/mn.js';
import * as module51 from '../../../../../js/output/svg/Wrappers/mo.js';
import * as module52 from '../../../../../js/output/svg/Wrappers/mpadded.js';
import * as module53 from '../../../../../js/output/svg/Wrappers/mphantom.js';
import * as module54 from '../../../../../js/output/svg/Wrappers/mroot.js';
import * as module55 from '../../../../../js/output/svg/Wrappers/mrow.js';
import * as module56 from '../../../../../js/output/svg/Wrappers/ms.js';
import * as module57 from '../../../../../js/output/svg/Wrappers/mspace.js';
import * as module58 from '../../../../../js/output/svg/Wrappers/msqrt.js';
import * as module59 from '../../../../../js/output/svg/Wrappers/msubsup.js';
import * as module60 from '../../../../../js/output/svg/Wrappers/mtable.js';
import * as module61 from '../../../../../js/output/svg/Wrappers/mtd.js';
import * as module62 from '../../../../../js/output/svg/Wrappers/mtext.js';
import * as module63 from '../../../../../js/output/svg/Wrappers/mtr.js';
import * as module64 from '../../../../../js/output/svg/Wrappers/munderover.js';
import * as module65 from '../../../../../js/output/svg/Wrappers/scriptbase.js';
import * as module66 from '../../../../../js/output/svg/Wrappers/semantics.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('output/svg', VERSION, 'output');
}

combineWithMathJax({_: {
  output: {
    common: {
      FontData: module1,
      Notation: module2,
      OutputJax: module3,
      Wrapper: module4,
      WrapperFactory: module5,
      Wrappers: {
        TeXAtom: module6,
        TextNode: module7,
        maction: module8,
        math: module9,
        menclose: module10,
        mfenced: module11,
        mfrac: module12,
        mglyph: module13,
        mi: module14,
        mmultiscripts: module15,
        mn: module16,
        mo: module17,
        mpadded: module18,
        mroot: module19,
        mrow: module20,
        ms: module21,
        mspace: module22,
        msqrt: module23,
        msubsup: module24,
        mtable: module25,
        mtd: module26,
        mtext: module27,
        mtr: module28,
        munderover: module29,
        scriptbase: module30,
        semantics: module31
      }
    },
    svg_ts: module32,
    svg: {
      FontCache: module33,
      FontData: module34,
      Notation: module35,
      Wrapper: module36,
      WrapperFactory: module37,
      Wrappers_ts: module38,
      Wrappers: {
        TeXAtom: module39,
        TextNode: module40,
        maction: module41,
        math: module42,
        menclose: module43,
        merror: module44,
        mfenced: module45,
        mfrac: module46,
        mglyph: module47,
        mi: module48,
        mmultiscripts: module49,
        mn: module50,
        mo: module51,
        mpadded: module52,
        mphantom: module53,
        mroot: module54,
        mrow: module55,
        ms: module56,
        mspace: module57,
        msqrt: module58,
        msubsup: module59,
        mtable: module60,
        mtd: module61,
        mtext: module62,
        mtr: module63,
        munderover: module64,
        scriptbase: module65,
        semantics: module66
      }
    }
  }
}});
