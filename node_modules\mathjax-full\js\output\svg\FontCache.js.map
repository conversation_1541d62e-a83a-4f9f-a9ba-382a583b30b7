{"version": 3, "file": "FontCache.js", "sourceRoot": "", "sources": ["../../../ts/output/svg/FontCache.ts"], "names": [], "mappings": ";;;AAyBA;IA8BE,mBAAY,GAAiB;QApBnB,UAAK,GAAwB,IAAI,GAAG,EAAE,CAAC;QAKvC,SAAI,GAAM,IAAI,CAAC;QAKf,YAAO,GAAW,EAAE,CAAC;QAKrB,WAAM,GAAW,CAAC,CAAC;QAM3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAUM,6BAAS,GAAhB,UAAiB,OAAe,EAAE,CAAS,EAAE,IAAY;QACvD,IAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAC/F,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,EAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;SAC7E;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAKM,gCAAY,GAAnB;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAMM,8BAAU,GAAjB,UAAkB,EAAiB;QAAjB,mBAAA,EAAA,SAAiB;QACjC,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5E,CAAC;IAKM,8BAAU,GAAjB;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAKM,4BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEH,gBAAC;AAAD,CAAC,AAjFD,IAiFC;AAjFY,8BAAS"}