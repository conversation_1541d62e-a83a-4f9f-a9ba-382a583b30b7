{"name": "wicked-good-xpath", "description": "Pure JS implementation of the DOM Level 3 XPath specification", "version": "1.3.0", "repository": {"type": "git", "url": "https://github.com/google/wicked-good-xpath.git"}, "main": "dist/wgxpath.install-node.js", "files": ["dist/*.js", "package.json", "README.md"], "keywords": ["closure", "google", "html", "javascript", "library", "parse", "wgxpath", "wicked-good-xpath", "xpath"], "author": {"name": "Google Inc."}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/google/wicked-good-xpath/issues"}, "homepage": "https://github.com/google/wicked-good-xpath", "devDependencies": {"google-closure-compiler": "^20160315.2.0", "google-closure-library": "^20160315.0.0", "gulp": "^3.9.1"}}