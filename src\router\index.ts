import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomePage.vue')
  },
  {
    path: '/chat/:id',
    name: 'Chat',
    component: () => import('@/views/ChatPage.vue'),
    props: true
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: () => import('@/views/ApiTestPage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 