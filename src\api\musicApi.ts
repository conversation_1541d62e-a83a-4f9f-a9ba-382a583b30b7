import { streamChat } from './streamChat'
import { MusicRecommendRequest, MusicRecommendResponse } from './types'

/**
 * 音乐API服务
 */
export const musicApi = {
  /**
   * 获取音乐推荐
   * @param data 推荐请求参数
   * @param onChunk 每收到一块数据时的回调函数
   * @returns 推荐响应
   */
  getRecommendation: async (
    data: MusicRecommendRequest,
    onChunk?: (chunk: string) => void
  ): Promise<MusicRecommendResponse> => {
    try {
      // 如果提供了onChunk回调，使用流式处理
      if (onChunk) {
        // 创建一个Promise，在流处理完成后解析
        return new Promise((resolve) => {
          // 存储完整的响应内容
          let fullContent = ''
          
          console.log('开始处理音乐推荐流式响应')
          
          // 使用streamChat函数处理流式响应
          const controller = streamChat({
            baseUrl: '/api',
            userId: 'user123', // 替换为实际的用户ID
            conversationId: '',
            query: data.query,
            onPing: () => {
              console.log('收到ping信号');
            },
            onText: (text) => {
              console.log('音乐API接收到数据块:', text);
              fullContent += text;
              if (onChunk) {
                onChunk(text);
              }
            },
            onDone: () => {
              console.log('文本内容输出完毕');
            },
            onCost: (cost) => {
              console.log(`本次对话消耗token数: ${cost}`);
            },
            onAllDone: () => {
              // 流处理完成，返回完整响应
              console.log('音乐推荐流式响应完成，完整内容:', fullContent);
              resolve({
                success: true,
                content: fullContent,
                message: '推荐成功'
              });
            },
            onError: (error) => {
              console.error('流处理错误:', error);
              // 出错时也要解析Promise，避免卡住UI
              resolve({
                success: false,
                content: fullContent || '抱歉，处理响应时出错。',
                message: '推荐失败'
              });
            }
          });
        });
      } else {
        // 不使用流式处理，直接发送请求
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-User-Id': 'user123' // 替换为实际的用户ID
          },
          body: JSON.stringify({
            conversationId: '',
            query: data.query,
            streaming: false
          })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const responseData = await response.json();
        
        return {
          success: true,
          content: responseData.text || '',
          message: '推荐成功'
        };
      }
    } catch (error) {
      console.error('获取音乐推荐失败:', error);
      return {
        success: false,
        content: '抱歉，获取推荐失败，请稍后再试。',
        message: '推荐失败'
      };
    }
  },
  
  /**
   * 检查是否已登录（始终返回true，因为不再需要登录）
   * @returns 是否已登录
   */
  isLoggedIn: (): boolean => {
    return true;
  }
} 