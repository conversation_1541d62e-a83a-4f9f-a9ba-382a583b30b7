import { fetchEventSource } from '@microsoft/fetch-event-source'
import { MusicRecommendRequest, MusicRecommendResponse } from './types'

/**
 * 音乐专用流式聊天函数
 */
function streamMusicChat({
  baseUrl,
  userId,
  conversationId,
  query,
  onPing,
  onText,
  onDone,
  onCost,
  onAllDone,
  onError
}: {
  baseUrl: string;
  userId: string;
  conversationId: string;
  query: string;
  onPing?: () => void;
  onText?: (text: string) => void;
  onDone?: () => void;
  onCost?: (cost: string) => void;
  onAllDone?: () => void;
  onError?: (error: Error) => void;
}) {
  const controller = new AbortController();
  let hasErrorOccurred = false;

  // 构建音乐API的URL
  const url = `${baseUrl}/music-api/api/music/messages`;

  // 构建请求体
  const body = JSON.stringify({
    conversationId,
    query,
    streaming: true
  });

  fetchEventSource(url, {
    method: 'POST',
    headers: {
      'X-User-Id': userId,
      'Content-Type': 'application/json'
    },
    body,
    signal: controller.signal,
    openWhenHidden: true,
    async onopen(response) {
      if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
        console.log('音乐流式连接已建立');
        return;
      } else {
        hasErrorOccurred = true;
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    },
    onmessage(event) {
      try {
        const eventData = JSON.parse(event.data);

        switch (eventData.event) {
          case 'ping':
            onPing?.();
            break;
          case 'text':
            onText?.(eventData.data);
            break;
          case 'done':
            onDone?.();
            break;
          case 'cost':
            onCost?.(eventData.data);
            break;
          case 'all_done':
            onAllDone?.();
            break;
          default:
            console.warn('未知事件类型:', eventData);
        }
      } catch (error) {
        console.error('处理音乐消息时出错:', error);
        hasErrorOccurred = true;
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    },
    onerror(error) {
      console.error('音乐流式请求出错:', error);
      hasErrorOccurred = true;
      onError?.(error);
      // 不重试，直接抛出错误停止连接
      throw error;
    }
  }).catch(error => {
    if (!hasErrorOccurred) {
      console.error('音乐 fetchEventSource 错误:', error);
      onError?.(error);
    }
  });

  return controller;
}

/**
 * 音乐API服务
 */
export const musicApi = {
  /**
   * 获取音乐推荐
   * @param data 推荐请求参数
   * @param onChunk 每收到一块数据时的回调函数
   * @returns 推荐响应
   */
  getRecommendation: async (
    data: MusicRecommendRequest,
    onChunk?: (chunk: string) => void
  ): Promise<MusicRecommendResponse> => {
    try {
      // 如果提供了onChunk回调，使用流式处理
      if (onChunk) {
        // 创建一个Promise，在流处理完成后解析
        return new Promise((resolve) => {
          // 存储完整的响应内容
          let fullContent = ''
          
          console.log('开始处理音乐推荐流式响应')
          
          // 使用音乐专用流式聊天函数
          streamMusicChat({
            baseUrl: '/api',
            userId: 'user123', // 替换为实际的用户ID
            conversationId: data.conversation_id || 'default_conversation',
            query: data.query,
            onPing: () => {
              console.log('收到ping信号');
            },
            onText: (text) => {
              console.log('音乐API接收到数据块:', text);
              fullContent += text;
              if (onChunk) {
                onChunk(text);
              }
            },
            onDone: () => {
              console.log('文本内容输出完毕');
            },
            onCost: (cost) => {
              console.log(`本次对话消耗token数: ${cost}`);
            },
            onAllDone: () => {
              // 流处理完成，返回完整响应
              console.log('音乐推荐流式响应完成，完整内容:', fullContent);
              resolve({
                success: true,
                content: fullContent,
                message: '推荐成功'
              });
            },
            onError: (error) => {
              console.error('音乐流处理错误:', error);
              // 出错时也要解析Promise，避免卡住UI
              resolve({
                success: false,
                content: fullContent || '抱歉，音乐推荐服务暂时不可用。',
                message: '推荐失败'
              });
            }
          });
        });
      } else {
        // 不使用流式处理，直接发送请求
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-User-Id': 'user123' // 替换为实际的用户ID
          },
          body: JSON.stringify({
            conversationId: data.conversation_id || 'default_conversation',
            query: data.query,
            streaming: false
          })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const responseData = await response.json();
        
        return {
          success: true,
          content: responseData.text || '',
          message: '推荐成功'
        };
      }
    } catch (error) {
      console.error('获取音乐推荐失败:', error);
      return {
        success: false,
        content: '抱歉，获取推荐失败，请稍后再试。',
        message: '推荐失败'
      };
    }
  },
  
  /**
   * 检查是否已登录（始终返回true，因为不再需要登录）
   * @returns 是否已登录
   */
  isLoggedIn: (): boolean => {
    return true;
  }
} 