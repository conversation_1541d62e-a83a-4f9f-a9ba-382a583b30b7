import { HTMLAdaptor } from './HTMLAdaptor.js';
import { Constructor } from './NodeMixin.js';
import { OptionList } from '../util/Options.js';
export declare type HTMLAdaptorConstructor = Constructor<HTMLAdaptor<HTMLElement, Text, Document>>;
declare const JsdomAdaptor_base: HTMLAdaptorConstructor;
export declare class JsdomAdaptor extends JsdomAdaptor_base {
}
export declare function jsdomAdaptor(JSDOM: any, options?: OptionList): JsdomAdaptor;
export {};
