import { ArrayItem } from '../base/BaseItems.js';
import { Configuration } from '../Configuration.js';
import { MmlNode } from '../../../core/MmlTree/MmlNode.js';
export interface ColorData {
    cell: string;
    row: string;
    col: string[];
}
export declare class ColorArrayItem extends ArrayItem {
    color: ColorData;
    hasColor: boolean;
    EndEntry(): void;
    EndRow(): void;
    createMml(): MmlNode;
}
export declare const ColortblConfiguration: Configuration;
