{"version": 3, "file": "EmpheqConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/empheq/EmpheqConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,wDAAkD;AAClD,gDAA2D;AAC3D,iEAAwC;AAExC,+DAAsC;AACtC,qDAA+C;AAE/C,iDAA2C;AAK3C;IAAqC,mCAAS;IAA9C;;IAmBA,CAAC;IAdC,sBAAW,iCAAI;aAAf;YACE,OAAO,cAAc,CAAC;QACxB,CAAC;;;OAAA;IAKM,mCAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,EAAE;YAC3D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChC;QACD,OAAO,iBAAM,SAAS,YAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEH,sBAAC;AAAD,CAAC,AAnBD,CAAqC,wBAAS,GAmB7C;AAnBY,0CAAe;AAwBf,QAAA,aAAa,GAAG;IAQ3B,MAAM,EAAN,UAAO,MAAiB,EAAE,KAAsB;QAC9C,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;YAChD,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9F,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;YAChC,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAqB,CAAC;YACrD,0BAAU,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC7E;aAAM;YACL,sBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9B,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;YAClC,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACpE,IAAA,KAAA,OAAW,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAnF,GAAG,QAAA,EAAE,CAAC,QAA6E,CAAC;YAC3F,IAAI,CAAC,0BAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,IAAI,qBAAQ,CAAC,YAAY,EAAE,0BAA0B,EAAE,GAAG,CAAC,CAAC;aACnE;YACD,IAAI,IAAI,EAAE;gBACR,KAAK,CAAC,aAAa,CAAC,0BAAU,CAAC,YAAY,CAAC,IAAI,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC;aACzE;YACD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;YACjC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpB;IACH,CAAC;IASD,QAAQ,EAAR,UAAS,MAAiB,EAAE,KAAa,EAAE,CAAS;QAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAQD,WAAW,EAAX,UAAY,MAAiB,EAAE,IAAY;QACzC,IAAM,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;CAEF,CAAC;AAKF,IAAI,6BAAc,CAAC,YAAY,EAAE,0BAAU,CAAC,WAAW,EAAE;IACvD,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;CAC7B,EAAE,qBAAa,CAAC,CAAC;AAKlB,IAAI,yBAAU,CAAC,eAAe,EAAE;IAC9B,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,YAAY,EAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,YAAY,EAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,YAAY,EAAK,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,WAAW,EAAM,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,WAAW,EAAM,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,WAAW,EAAM,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,WAAW,EAAM,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,YAAY,EAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,YAAY,EAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,WAAW,EAAM,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,WAAW,EAAM,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,eAAe,EAAE,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,cAAc,EAAG,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,cAAc,EAAG,CAAC,UAAU,EAAE,GAAG,CAAC;IAClC,cAAc,EAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,cAAc,EAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,cAAc,EAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,cAAc,EAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;IACvC,OAAO,EAAW,aAAa;IAC/B,OAAO,EAAW,aAAa;IAC/B,UAAU,EAAQ,aAAa;IAC/B,UAAU,EAAQ,aAAa;CAChC,EAAE,qBAAa,CAAC,CAAC;AAKL,QAAA,mBAAmB,GAAG,gCAAa,CAAC,MAAM,CAAC,QAAQ,EAAE;IAChE,OAAO,EAAE;QACP,KAAK,EAAE,CAAC,eAAe,CAAC;QACxB,WAAW,EAAE,CAAC,YAAY,CAAC;KAC5B;IACD,KAAK;QACH,GAAC,eAAe,CAAC,SAAS,CAAC,IAAI,IAAG,eAAe;WAClD;CACF,CAAC,CAAC"}