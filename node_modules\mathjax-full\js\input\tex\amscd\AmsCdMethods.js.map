{"version": 3, "file": "AmsCdMethods.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/amscd/AmsCdMethods.ts"], "names": [], "mappings": ";;;;;AAyBA,iEAAwC;AAIxC,qEAAmD;AAEnD,+DAA0D;AAC1D,+DAAsC;AAItC,IAAI,YAAY,GAAgC,EAAE,CAAC;AAQnD,YAAY,CAAC,EAAE,GAAG,UAAS,MAAiB,EAAE,KAAgB;IAC5D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAc,CAAC;IAC3D,IAAI,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC;IACjD,IAAI,CAAC,aAAa,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU;QACpD,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU;KACrD,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,GAAG;QACZ,WAAW,EAAE,QAAQ;QACrB,aAAa,EAAE,OAAO,CAAC,QAAQ;QAC/B,UAAU,EAAE,OAAO,CAAC,QAAQ;QAC5B,YAAY,EAAE,IAAI;KACrB,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAQF,YAAY,CAAC,KAAK,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC3D,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QACzB,OAAO,IAAA,4BAAK,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAC5B;SAAM;QACL,MAAM,CAAC,CAAC,EAAE,CAAC;KACZ;IACD,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE;QAC1C,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAChC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;KAC5B;IACD,IAAI,GAAG,GAAG,KAAkB,CAAC;IAI7B,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClD,OAAO,CAAC,EAAE;QACR,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAChC,CAAC,EAAE,CAAC;KACL;IAED,IAAI,GAAG,CAAC;IACR,IAAI,IAAI,GAAG,EAAC,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAC,EAC7D,IAAI,GAAG,EAAC,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC;QAChC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC;IAE/D,IAAI,CAAC,KAAK,GAAG,EAAE;KACd;SAAM,IAAI,CAAC,KAAK,GAAG,EAAE;QACpB,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAG,IAAI,EAAE,QAAQ,CAAC,CAAC;KACrD;SAAM,IAAI,CAAC,KAAK,GAAG,EAAE;QACpB,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;KAC/C;SAAM;QAKL,IAAI,KAAK,GAAY;YACnB,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ;SAA6B,CAAE,CAAC,CAAC,CAAC;QAC9F,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;YAI1B,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,CAAC,EAAE;gBACN,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aACzC;YACD,IAAI,CAAC,IAAI,CAAC,EAAE;gBACV,IAAI,GAAG,GAAY,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAC,CAAC;gBACtD,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,CAAkB,CAAC;gBAClE,IAAI,CAAC,EAAE;oBACL,IAAI,KAAK,GAAG,IAAI,sBAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC3E,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC7D,qBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;oBAClD,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAC3C;gBACD,IAAI,CAAC,EAAE;oBACL,IAAI,KAAK,GAAG,IAAI,sBAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC3E,qBAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;iBACnF;gBACD,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBAC3D,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,EAAC,CAAC,CAAC;iBAC1E;aACF;SACF;aAAM;YAIL,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,GAAG,GAAG,SAAS,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,EAAE;gBACV,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACpC,IAAI,CAAC,EAAE;oBACL,qBAAQ,CAAC,cAAc,CACrB,GAAG,EAAE,CAAC,IAAI,sBAAS,CAAC,sBAAsB,GAAG,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;iBACzG;gBACD,SAAS,CAAC,QAAQ,GAAG,qBAAQ,CAAC,GAAG,CAAC;gBAClC,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC1C,IAAI,CAAC,EAAE;oBACL,qBAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,sBAAS,CAAC,sBAAsB,GAAG,CAAC,GAAG,GAAG,EAChC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;iBAC7F;aACF;SACF;KACF;IACD,IAAI,GAAG,EAAE;QACP,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;IACD,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClC,CAAC,CAAC;AAQF,YAAY,CAAC,IAAI,GAAG,UAAS,MAAiB,EAAE,IAAY;IAC1D,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,EAAe,CAAC;IAC1C,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAKtE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;KACpF;IACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;AAC5F,CAAC,CAAC;AAQF,YAAY,CAAC,eAAe,GAAG,UAAS,MAAiB,EAAE,IAAY;IACrE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AAQF,YAAY,CAAC,gBAAgB,GAAG,UAAS,MAAiB,EAAE,IAAY;IACtE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AAGF,kBAAe,YAAY,CAAC"}