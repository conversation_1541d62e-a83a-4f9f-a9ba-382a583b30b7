{"version": 3, "file": "tex-oldstyle-bold.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/tex-oldstyle-bold.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,eAAe,GAAyB;IACjD,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACvB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACrB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;IACpB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACvB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxB,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACvB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC5C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IACrC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAChD,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC;IAC1C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC;CAC7C,CAAC"}