{"version": 3, "file": "safe.js", "sourceRoot": "", "sources": ["../../../ts/ui/safe/safe.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,oDAA6D;AAE7D,mDAA6C;AAmB7C;IAsJE,cAAY,QAA+B,EAAE,OAAmB;QA5CzD,qBAAgB,GAAwB,IAAI,GAAG,CAAC;YAIrD,CAAC,MAAM,EAAE,WAAW,CAAC;YACrB,CAAC,KAAK,EAAG,WAAW,CAAC;YACrB,CAAC,QAAQ,EAAE,WAAW,CAAC;YACvB,CAAC,OAAO,EAAE,iBAAiB,CAAC;YAC5B,CAAC,OAAO,EAAE,cAAc,CAAC;YACzB,CAAC,IAAI,EAAE,UAAU,CAAC;YAClB,CAAC,UAAU,EAAE,gBAAgB,CAAC;YAC9B,CAAC,UAAU,EAAE,gBAAgB,CAAC;YAC9B,CAAC,eAAe,EAAE,gBAAgB,CAAC;YACnC,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;YAChD,CAAC,aAAa,EAAE,mBAAmB,CAAC;YACpC,CAAC,OAAO,EAAE,YAAY,CAAC;SACxB,CAAC,CAAC;QAoBI,kBAAa,gBACf,4BAAW,EACd;QAOA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,CAAC;IAQM,uBAAQ,GAAf,UAAgB,IAAuB,EAAE,QAA+B;QACtE,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAClD;QAAC,OAAO,GAAG,EAAE;YACZ,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;SACpD;IACH,CAAC;IAOS,2BAAY,GAAtB,UAAuB,IAAa;;QAClC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;;YACtD,KAAiB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAArC,IAAM,EAAE,WAAA;gBACX,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACV,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/D,IAAI,KAAK,EAAE;wBACT,IAAI,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;4BACjG,UAAU,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;yBACxB;qBACF;yBAAM;wBACL,OAAO,UAAU,CAAC,EAAE,CAAC,CAAC;qBACvB;iBACF;aACF;;;;;;;;;IACH,CAAC;IASM,2BAAY,GAAnB,UAAoB,EAAU,EAAE,KAAa;QAC3C,IAAI,EAAE,KAAK,OAAO;YAAE,OAAO,IAAI,CAAC;QAChC,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAM,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACrG,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,KAAK,CAAC;SACd;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC/F,CAAC;IAQM,2BAAY,GAAnB,UAAoB,IAAc;QAAlC,iBAGC;QAFC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAI,EAAE,IAAI,CAAW,EAApD,CAAoD,CAAC;aACnE,MAAM,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,KAAK,IAAI,EAAd,CAAc,CAAC,CAAC;IAChD,CAAC;IAtNa,YAAO,GAAe;QAClC,KAAK,EAAE;YAIL,IAAI,EAAK,MAAM;YACf,OAAO,EAAE,MAAM;YACf,MAAM,EAAG,MAAM;YACf,MAAM,EAAG,MAAM;SAChB;QAID,SAAS,EAAE,CAAC;QAIZ,yBAAyB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAIlC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAIzB,YAAY,EAAE,uBAAuB;QAIrC,SAAS,EAAE,uBAAuB;QAIlC,WAAW,EAAE,YAAY;QAIzB,aAAa,EAAE,IAAA,uBAAU,EAAC;YACxB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,KAAK;YACjB,IAAI,EAAE,KAAK;SACZ,CAAC;QAIF,UAAU,EAAE,IAAA,uBAAU,EAAC;YACrB,KAAK,EAAE,IAAI;YACX,eAAe,EAAE,IAAI;YACrB,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;SACd,CAAC;QAIF,UAAU,EAAE,IAAA,uBAAU,EAAC;YACrB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;SACd,CAAC;QAOF,YAAY,EAAE,IAAA,uBAAU,EAAC;YACvB,SAAS,EAAE,gBAAgB;YAC3B,WAAW,EAAE,kBAAkB;YAC/B,YAAY,EAAE,mBAAmB;YACjC,UAAU,EAAE,iBAAiB;YAC7B,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;SACvB,CAAC;KACH,CAAC;IAwHJ,WAAC;CAAA,AA7ND,IA6NC;AA7NY,oBAAI"}