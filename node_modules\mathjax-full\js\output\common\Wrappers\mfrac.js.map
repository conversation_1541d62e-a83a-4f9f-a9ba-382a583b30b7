{"version": 3, "file": "mfrac.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mfrac.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA,SAAgB,gBAAgB,CAA+B,IAAO;IAEpE;QAAqB,2BAAI;QAkBvB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAYd;YA1BM,WAAK,GAAa,IAAI,CAAC;YAe5B,KAAI,CAAC,GAAG,GAAG,CAAC,KAAI,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAItG,IAAI,KAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBACjC,IAAA,CAAC,GAAI,KAAI,CAAC,YAAY,CAAC,KAAI,CAAC,SAAS,EAAE,CAAC,EAAvC,CAAwC;gBAChD,IAAM,KAAK,GAAG,KAAI,CAAC,KAAK,GAAG,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAa,CAAC;gBAC1D,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC7C,KAAK,CAAC,UAAU,GAAoB,CAAC;gBACrC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACtC;;QACH,CAAC;QAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YACvD,IAAI,CAAC,KAAK,EAAE,CAAC;YACP,IAAA,KAA4B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,EAApF,aAAa,mBAAA,EAAE,QAAQ,cAA6D,CAAC;YAC5F,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,IAAuB,CAAC;YAChC,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACrC;iBAAM;gBACL,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC7D,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;gBAClB,IAAI,SAAS,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACjC;qBAAM;oBACL,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBAC/C,CAAC,IAAI,EAAE,CAAC;iBACT;gBACD,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;aACb;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QASM,iCAAe,GAAtB,UAAuB,IAAU,EAAE,OAAgB,EAAE,CAAS;YAC5D,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAM,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC;YACpB,IAAA,KAAY,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,EAAlC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAA2B,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QAC9B,CAAC;QAQM,wBAAM,GAAb,UAAc,OAAgB,EAAE,CAAS;YACvC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAM,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC;YAC1B,IAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACpC,OAAO,EAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;gBAC5B,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAC1C,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC,CAAC;QAC1D,CAAC;QAQM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,OAAgB;YACvC,IAAA,KAAqB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAxC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAAwB,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACzB,CAAC;QAQM,wBAAM,GAAb,UAAc,OAAgB;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAMzB,IAAA,KAAA,OAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,IAAA,EAAnE,CAAC,QAAA,EAAE,CAAC,QAA+D,CAAC;YACzE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAI9D,IAAI,CAAC,GAAG,CAAC,EAAE;gBACT,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjB,CAAC,GAAG,CAAC,CAAC;aACP;YACD,OAAO,EAAC,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAC,CAAC;QAC/B,CAAC;QAQM,iCAAe,GAAtB,UAAuB,IAAU,EAAE,OAAgB;YAC3C,IAAA,KAA4B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAArD,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAA8B,CAAC;YAC7D,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,CAAC;QAQM,8BAAY,GAAnB,UAAoB,OAAgB;YAGlC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAM,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACnC,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC/F,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACvC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACzD,OAAO,EAAC,CAAC,GAAA,EAAE,KAAK,OAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAC,CAAC;QACtC,CAAC;QAOM,4BAAU,GAAjB,UAAkB,UAAqB;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAKM,2BAAS,GAAhB;YACQ,IAAA,KAA8B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,aAAa,CAAC,EAAxF,YAAY,kBAAA,EAAE,WAAW,iBAA+D,CAAC;YAChG,OAAO,YAAY,IAAI,WAAW,KAAK,CAAC,CAAC;QAC3C,CAAC;QAKM,8BAAY,GAAnB,UAAoB,CAAS;YAC3B,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,IAAI,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;aAC5C;YACD,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC3B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QAClD,CAAC;QAKM,+BAAa,GAApB,UAAqB,CAAS;YAC5B,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAW,CAAC,CAAC;QACzG,CAAC;QAEH,cAAC;IAAD,CAAC,AA5MM,CAAc,IAAI,GA4MvB;AAEJ,CAAC;AAhND,4CAgNC"}