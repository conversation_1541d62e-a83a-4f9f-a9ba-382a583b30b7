{"version": 3, "file": "abstract_navigatable.js", "sourceRoot": "", "sources": ["../ts/abstract_navigatable.ts"], "names": [], "mappings": ";;;AAwBA,2DAAyD;AACzD,+DAA+D;AAG/D;IAAA;QAGU,WAAM,GAAG,KAAK,CAAC;IAoIzB,CAAC;IA/HQ,uCAAS,GAAhB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAKM,qCAAO,GAAd,UAAe,KAAoB;QACjC,QAAQ,KAAK,CAAC,OAAO,EAAE;YACvB,KAAK,wBAAG,CAAC,MAAM;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,MAAM;YACR,KAAK,wBAAG,CAAC,KAAK;gBACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClB,MAAM;YACR,KAAK,wBAAG,CAAC,IAAI;gBACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjB,MAAM;YACR,KAAK,wBAAG,CAAC,EAAE;gBACT,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBACf,MAAM;YACR,KAAK,wBAAG,CAAC,IAAI;gBACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjB,MAAM;YACR,KAAK,wBAAG,CAAC,MAAM,CAAC;YAChB,KAAK,wBAAG,CAAC,KAAK;gBACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClB,MAAM;YACR;gBACE,OAAO;SACR;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvD,CAAC;IAKM,oCAAM,GAAb,UAAc,MAAqB,IAAS,CAAC;IAKtC,mCAAK,GAAZ,UAAa,MAAqB,IAAS,CAAC;IAKrC,kCAAI,GAAX,UAAY,MAAqB,IAAS,CAAC;IAKpC,mCAAK,GAAZ,UAAa,MAAqB,IAAS,CAAC;IAKrC,gCAAE,GAAT,UAAU,MAAqB,IAAS,CAAC;IAKlC,kCAAI,GAAX,UAAY,MAAqB,IAAS,CAAC;IAMjC,kCAAI,GAAd,UAAe,KAAY;QACzB,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;SAC3B;IACH,CAAC;IAKM,uCAAS,GAAhB,UAAiB,KAAiB;QAChC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAKM,qCAAO,GAAd,UAAe,KAAiB;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAKM,uCAAS,GAAhB,UAAiB,KAAiB;QAChC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAKM,sCAAQ,GAAf,UAAgB,KAAiB;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAKM,mCAAK,GAAZ,UAAa,KAAiB;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAMM,uCAAS,GAAhB,UAAiB,OAAoB;QACnC,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,gBAAgB,CAAC,4BAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IACH,0BAAC;AAAD,CAAC,AAvID,IAuIC;AAvIqB,kDAAmB"}