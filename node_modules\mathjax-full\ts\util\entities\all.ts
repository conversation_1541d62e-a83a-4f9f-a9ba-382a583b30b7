/*************************************************************
 *
 *  Copyright (c) 2017-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/**
 * @fileoverview  Loads all the entity files (used to avoid dynamic loading)
 *
 * <AUTHOR> (<PERSON><PERSON>)
 */

import './a.js';
import './b.js';
import './c.js';
import './d.js';
import './e.js';
import './f.js';
import './g.js';
import './h.js';
import './i.js';
import './j.js';
import './k.js';
import './l.js';
import './m.js';
import './n.js';
import './o.js';
import './p.js';
import './q.js';
import './r.js';
import './s.js';
import './t.js';
import './u.js';
import './v.js';
import './w.js';
import './x.js';
import './y.js';
import './z.js';

import './fr.js';
import './opf.js';
import './scr.js';
