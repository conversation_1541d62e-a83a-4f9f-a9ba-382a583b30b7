{"version": 3, "file": "NewcommandUtil.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/newcommand/NewcommandUtil.ts"], "names": [], "mappings": ";;;;;AAyBA,iEAAwC;AACxC,+DAAsC;AAEtC,0CAA2C;AAK3C,IAAU,cAAc,CAkSvB;AAlSD,WAAU,cAAc;IAWtB,SAAgB,iBAAiB,CAAC,IAAY,EAAE,MAAc;QAC5D,IAAI,OAAO,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAW,CAAC;QAE5C,IAAI,MAAM,CAAC,UAAU,EAAE;YAErB,KAAK,IAAI,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE;gBACjC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAS,CAAC,CAAC;aAC9C;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAXe,gCAAiB,oBAWhC,CAAA;IAUD,SAAgB,cAAc,CAAC,IAAY;QAEzC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAW,CAAC;QAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAW,CAAC;QAC7B,IAAI,KAAK,GAAe,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAE1C,KAAK,CAAC,IAAI,CAAC,CAAC,CAAW,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACxC;QACD,OAAO,IAAI,kBAAM,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAVe,6BAAc,iBAU7B,CAAA;IAQD,SAAgB,SAAS,CAAC,MAAiB,EAAE,GAAW;QAEtD,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,IAAI,EAAE;YAEd,MAAM,IAAI,qBAAQ,CAAC,WAAW,EACV,2CAA2C,EAAE,GAAG,CAAC,CAAC;SACvE;QACD,IAAI,EAAE,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACvD,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IAVe,wBAAS,YAUxB,CAAA;IAQD,SAAgB,iBAAiB,CAAC,MAAiB,EAAE,IAAY;QAC/D,IAAI,EAAE,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACxD,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAEzB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;YAE9B,MAAM,IAAI,qBAAQ,CAAC,4BAA4B,EAC5B,sCAAsC,EAAE,IAAI,CAAC,CAAC;SAClE;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAZe,gCAAiB,oBAYhC,CAAA;IAQD,SAAgB,WAAW,CAAC,MAAiB,EAAE,IAAY;QACzD,IAAI,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE;YAGL,CAAC,GAAG,sBAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBAExB,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EACpB,8CAA8C,EAAE,IAAI,CAAC,CAAC;aAC1E;SACF;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAbe,0BAAW,cAa1B,CAAA;IAUD,SAAgB,WAAW,CAAC,MAAiB,EAAE,GAAW,EAAE,EAAU;QAEpE,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACtC,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,EAAE;gBAEb,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;oBAElB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACnD;gBACD,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;oBAEvB,MAAM,IAAI,qBAAQ,CAAC,cAAc,EACb,qCAAqC,EAAE,EAAE,CAAC,CAAC;iBAChE;gBACD,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;oBAEvB,MAAM,IAAI,qBAAQ,CAAC,iBAAiB,EAChB,iDAAiD,EAAE,EAAE,CAAC,CAAC;iBAC5E;gBACD,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;gBAEpB,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;oBAElB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACnD;gBACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBAErB,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBACtC;qBAAM;oBAEL,OAAO,CAAC,CAAC;iBACV;aACF;YACD,MAAM,CAAC,CAAC,EAAE,CAAC;SACZ;QAED,MAAM,IAAI,qBAAQ,CAAC,0BAA0B,EACzB,iDAAiD,EAAE,GAAG,CAAC,CAAC;IAC9E,CAAC;IA7Ce,0BAAW,cA6C1B,CAAA;IASD,SAAgB,YAAY,CAAC,MAAiB,EAAE,IAAY,EAAE,KAAa;QACzE,IAAI,KAAK,IAAI,IAAI,EAAE;YAEjB,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACtC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,CAAC,KAAK,GAAG,EAAE;gBAEb,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;oBAElB,SAAS,GAAG,CAAC,CAAC;iBACf;gBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBAEpC,IAAI,SAAS,EAAE;oBAEb,CAAC,EAAE,CAAC;oBACJ,CAAC,IAAI,CAAC,CAAC;iBACR;gBACD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACnC;iBAAM,IAAI,CAAC,KAAK,IAAI,EAAE;gBAErB,MAAM,CAAC,CAAC,EAAE,CAAC;gBACX,CAAC,EAAE,CAAC;gBACJ,SAAS,GAAG,CAAC,CAAC;gBACd,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC9D,IAAI,KAAK,EAAE;oBAET,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAC5B,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;iBAClB;aACF;iBAAM;gBAEL,MAAM,CAAC,CAAC,EAAE,CAAC;gBACX,CAAC,EAAE,CAAC;gBACJ,SAAS,GAAG,CAAC,CAAC;aACf;SACF;QAED,MAAM,IAAI,qBAAQ,CAAC,iBAAiB,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;IAC1E,CAAC;IA/Ce,2BAAY,eA+C3B,CAAA;IAWD,SAAgB,UAAU,CAAC,MAAiB,EAAE,KAAa;QAEzD,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE;YAE1D,OAAO,CAAC,CAAC;SACV;QACD,IAAI,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;YACzB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAEjE,OAAO,CAAC,CAAC;SACV;QAED,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;QACzB,OAAO,CAAC,CAAC;IACX,CAAC;IAde,yBAAU,aAczB,CAAA;IAUD,SAAgB,YAAY,CAAC,MAAiB,EAAE,EAAU,EAAE,IAAY,EAAE,IAAgB;QACxF,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAA,aAAa,CAAoB,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,kBAAM,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9C,CAAC;IAJe,2BAAY,eAI3B,CAAA;IAWD,SAAgB,QAAQ,CAAC,MAAiB,EAAE,EAAU,EAAE,IAAiB,EAAE,IAAY,EAC9D,MAAmB;QAAnB,uBAAA,EAAA,WAAmB;QAC1C,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAA,WAAW,CAAkB,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,iBAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IALe,uBAAQ,WAKvB,CAAA;IAUD,SAAgB,cAAc,CAAC,MAAiB,EAAE,GAAW,EAAE,IAAiB,EAAE,IAAY;QAC5F,IAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAA,eAAe,CAAsB,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,iBAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAJe,6BAAc,iBAI7B,CAAA;IAKY,4BAAa,GAAG,eAAe,CAAC;IAChC,0BAAW,GAAG,aAAa,CAAC;IAC5B,8BAAe,GAAG,iBAAiB,CAAC;AAEnD,CAAC,EAlSS,cAAc,KAAd,cAAc,QAkSvB;AAED,kBAAe,cAAc,CAAC"}