{"version": 3, "file": "selection_box.js", "sourceRoot": "", "sources": ["../ts/selection_box.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,+CAAwC;AACxC,qDAA8C;AAC9C,uDAAgD;AAChD,qCAA+B;AAO/B;IAAmC,iCAAY;IAiC7C,uBAAmB,MAAoB;QAAvC,YACE,iBAAO,SAGR;QAJkB,YAAM,GAAN,MAAM,CAAc;QA5B7B,eAAS,GAAG,6BAAW,CAAC,eAAe,CAAC,CAAC;QA8BjD,KAAI,CAAC,YAAY,GAAG,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC;;IACnC,CAAC;IAxBa,sBAAQ,GAAtB,UACE,OAAsB,EACtB,EAA6D,EAC7D,EAAgB;YADR,KAAK,WAAA,EAAU,MAAM,YAAA,EAAY,QAAQ,cAAA;QAEjD,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAC5B,OAAO,EAAE,EAAC,OAAO,EAAE,KAAK,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,IAAI,IAAI,EAAC,EAAE,SAAS,CAAC,CAAC;QACjE,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QACtD,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CACrB,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CACvB,OAAO,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAC,EAAE,SAAS,CAAC,EADzD,CACyD,CAAC,CAAC;QAClE,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAW,CAAC;QAChD,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;QACxB,OAAO,SAAS,CAAC;IACnB,CAAC;IAeM,oCAAY,GAAnB;QACE,iBAAM,YAAY,WAAE,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,6BAAW,CAAC,eAAe,CAAC,CAAC,EAArD,CAAqD,CAAC,CAAC;IACpF,CAAC;IAKS,+BAAO,GAAjB,cAAsB,CAAC;IAKhB,6BAAK,GAAZ,UAAa,KAAoB;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAKM,4BAAI,GAAX,UAAY,KAAoB;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IACH,oBAAC;AAAD,CAAC,AAjED,CAAmC,+BAAY,GAiE9C;AAjEY,sCAAa;AAiF1B;IAAkC,gCAAI;IAgCpC,sBAAY,KAAa,EAAE,SAAiB,EACzB,KAA2C,EAC3C,IAA4C;QAD5C,sBAAA,EAAA,cAA2C;QAC3C,qBAAA,EAAA,iBAA4C;QAF/D,YAGE,kBAAM,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,SAC9B;QAHkB,WAAK,GAAL,KAAK,CAAsC;QAC3C,UAAI,GAAJ,IAAI,CAAwC;QAhCvD,iBAAW,GAAoB,EAAE,CAAC;QAClC,YAAM,GAAW,gBAAgB,CAAC;QAClC,eAAS,GAAY,IAAI,CAAC;;IAgClC,CAAC;IAzBa,qBAAQ,GAAtB,UACE,OAAsB,EACtB,EAE8C,EAC9C,IAAiB;YAHT,KAAK,WAAA,EAAa,SAAS,eAAA,EAAc,UAAU,gBAAA,EAAS,KAAK,WAAA,EAAQ,IAAI,UAAA;QAInF,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACjD,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,CACvB,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAA5C,CAA4C,CAAC,CAAC;QACrD,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;QACrB,OAAO,EAAE,CAAC;IACZ,CAAC;IAmBI,iCAAU,GAAjB,UAAkB,IAAiB;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,sBAAW,oCAAU;aAArB;YACE,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;aAED,UAAsB,UAA2B;YAAjD,iBAGC;YAFC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAApB,CAAoB,CAAC,CAAC;QAChD,CAAC;;;OALA;IAOM,mCAAY,GAAnB,UAAoB,SAAwB;QAC1C,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAGO,6BAAM,GAAd,UAAe,IAAqB;QAApC,iBAgBC;QAfC,IAAI,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG;YACtB,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;gBAChB,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,KAAI,CAAC,MAAM,GAAG,uBAAQ,CAAC,OAAO,EAAE,CAAC;aAChD;YACD,OAAO,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QACH,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAArB,CAAqB,EAAE,CAAC,CAAC,CAAC;QAC9D,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,6BAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACnD,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACtC,CAAC;IAKS,8BAAO,GAAjB;QACE,iBAAM,OAAO,WAAE,CAAC;QAChB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3B,OAAO;SACR;QACD,IAAI,SAAS,GAAkB,EAAE,CAAC;QAClC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,cAAc,GAAa,EAAE,CAAC;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gCAC9C,CAAC;YACR,IAAI,IAAI,GAAG,OAAK,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;YAC5C,IAAA,KAAA,OAA+B,OAAK,MAAM,CAAC,IAAI,CAAC,IAAA,EAA/C,GAAG,QAAA,EAAE,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,MAAM,QAAqB,CAAC;YACrD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,EAArC,CAAqC,CAAC,CAAC;YAC3D,cAAc,GAAG,OAAK,aAAa,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;;;QAN9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM;oBAA9C,CAAC;SAOT;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAC9C,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,EAAE,EAAE,CAAC,CAAC;SACvD;QACD,SAAS,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,GAAG,IAAI,EAAjC,CAAiC,CAAC,CAAC;IAC9D,CAAC;IAEO,mCAAY,GAApB,UAAqB,IAAY;QAC/B,QAAQ,IAAI,CAAC,IAAI,EAAE;YACnB;gBACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACrC;gBACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;YACnD,gBAA4B;YAC5B;gBACE,OAAO,YAAY,CAAC,SAAS,CAAC;SAE/B;IACH,CAAC;IAEO,oCAAa,GAArB,UAAsB,IAAmB,EAAE,MAAgB;QACzD,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;YACd,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAkB,CAAC;YACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,SAAa,EAAE,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5D,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oCAAa,GAArB,UAAsB,IAAc,EAAE,IAAc;QAClD,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACZ,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,EAAE,CAAA;SACJ;QAAA,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAKM,2BAAI,GAAX,UAAY,KAAoB;QAAhC,iBAGC;QAFC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,KAAa;YACrB,OAAA,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAAlD,CAAkD,CAAC,CAAC;IAChE,CAAC;IAKM,4BAAK,GAAZ,UAAa,KAAoB;QAAjC,iBAGC;QAFC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,KAAa;YACrB,OAAA,KAAK,KAAK,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QAApD,CAAoD,CAAC,CAAC;IAClE,CAAC;IAKM,mCAAY,GAAnB;QACE,iBAAM,YAAY,WAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,6BAAW,CAAC,WAAW,CAAC,CAAC,CAAC;IACpD,CAAC;IAKS,sCAAe,GAAzB;QACE,IAAI,GAAG,GAAG,iBAAM,eAAe,WAAE,CAAC;QAClC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,6BAAW,CAAC,cAAc,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAChC,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,oCAAa,GAArB,UAAsB,KAAoB;QACxC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAqB,CAAC;QACzC,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,MAAM,CAAC,EAAE,EAAE;YACb,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAvB,CAAuB,CAAC,CAAC;SAChE;QACD,IAAI,CAAC,SAAS,EAAE;YACd,IAAI,IAAE,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,IAAE,EAAhB,CAAgB,CAAC,CAAC;SACzD;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,2BAAI,GAAZ,UAAa,KAAoB,EACpB,MAA6B;QACxC,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SAC7B;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAoBO,4BAAK,GAAb;QACE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC;IAKM,6BAAM,GAAb;QACE,OAAO,EAAC,IAAI,EAAE,EAAE;SACR,CAAC;IACX,CAAC;IApOa,sBAAS,GAAG,CAAC,CAAC;IAwMrB,wBAAW,GAAG,IAAI,GAAG,CAAiE;QAC3F,iBAA8B,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAApD,CAAoD,CAAC;QAC7F,SAAsB,UAAC,EAAE,EAAE,EAAE,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC;QACpC,eAA4B,UAAC,CAAC,EAAE,CAAC;gBAC/B,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC;QACF,eAA4B,UAAC,CAAC,EAAE,CAAC;gBAC/B,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC;KACH,CAAC,CAAC;IAiBL,mBAAC;CAAA,AA3OD,CAAkC,cAAI,GA2OrC;AA3OY,oCAAY"}