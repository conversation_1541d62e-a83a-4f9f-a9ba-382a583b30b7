import { CHTMLConstructor } from '../Wrapper.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLTextNode_base: import("../../common/Wrappers/TextNode.js").TextNodeConstructor & CHTMLConstructor<any, any, any>;
export declare class CHTMLTextNode<N, T, D> extends CHTMLTextNode_base {
    static kind: string;
    static autoStyle: boolean;
    static styles: StyleList;
    toCHTML(parent: N): void;
}
export {};
