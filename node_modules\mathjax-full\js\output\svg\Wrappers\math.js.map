{"version": 3, "file": "math.js", "sourceRoot": "", "sources": ["../../../../ts/output/svg/Wrappers/math.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAAyD;AACzD,yDAA8D;AAC9D,kEAA+D;AAE/D,iDAA2C;AAW3C;IACA,2BAA0D;IAD1D;;IA0GA,CAAC;IA3EQ,uBAAK,GAAZ,UAAa,MAAS;QACpB,iBAAM,KAAK,YAAC,MAAM,CAAC,CAAC;QACpB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,CAAC;QAClE,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;IACH,CAAC;IAMS,+BAAa,GAAvB;QACQ,IAAA,KAAA,OAAiB,IAAI,CAAC,aAAa,EAAE,IAAA,EAApC,KAAK,QAAA,EAAE,KAAK,QAAwB,CAAC;QAC5C,IAAI,KAAK,KAAK,QAAQ,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAI,CAAC,SAAS,EAAE;YACvC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC/D,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gBACd,IAAA,KAAY,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,EAAxC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAiC,CAAC;gBAC9C,IAAI,KAAK,KAAK,OAAO,EAAE;oBACrB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;iBACnC;qBAAM,IAAI,KAAK,KAAK,MAAM,EAAE;oBAC3B,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,CAAC;iBACjC;qBAAM,IAAI,KAAK,KAAK,QAAQ,EAAE;oBAC7B,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC1B;gBACD,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;SACxB;IACH,CAAC;IAKS,8BAAY,GAAtB;;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,IAAM,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAW,CAAC;QAClG,IAAI,MAAM,EAAE;YACV,IAAM,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,EAAC,EAAE,IAAA,EAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3D,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACxD,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;YACzD,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;;gBACpD,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAA9C,IAAM,KAAK,WAAA;oBACd,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;iBAC5D;;;;;;;;;SACF;IACH,CAAC;IAKS,4BAAU,GAApB;QACE,OAAO,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC;IAKM,iCAAe,GAAtB,UAAuB,SAAkB,EAAE,CAAgB,EAAE,MAAsB;QAAxC,kBAAA,EAAA,QAAgB;QAAE,uBAAA,EAAA,aAAsB;QACjF,OAAO,iBAAM,eAAe,YAAC,SAAS,EACT,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAChE,KAAK,CAAC,CAAC;IACtC,CAAC;IAlGa,YAAI,GAAG,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAK9B,cAAM,GAAc;QAChC,0CAA0C,EAAE;YAC1C,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,QAAQ;YACtB,MAAM,EAAE,OAAO;SAChB;QACD,wDAAwD,EAAE;YACxD,OAAO,EAAE,MAAM;SAChB;QACD,0CAA0C,EAAE;YAC1C,YAAY,EAAE,MAAM;SACrB;QACD,2CAA2C,EAAE;YAC3C,YAAY,EAAE,OAAO;SACtB;KACF,CAAC;IAgFJ,cAAC;CAAA,AA1GD,CACA,IAAA,yBAAe,EAAgC,uBAAU,CAAC,GAyGzD;AA1GY,0BAAO"}