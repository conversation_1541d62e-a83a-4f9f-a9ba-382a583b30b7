/*!
  Theme: <PERSON>i <PERSON>
  Author: 
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#d6d6d6;background:#151718}.hljs ::selection,.hljs::selection{background-color:#3b758c;color:#d6d6d6}.hljs-comment{color:#41535b}.hljs-tag{color:#43a5d5}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#d6d6d6}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#cd3f45}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#db7b55}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#e6cd69}.hljs-strong{font-weight:700;color:#e6cd69}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#9fca56}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#55dbbe}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#55b5db}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#a074c4}.hljs-emphasis{color:#a074c4;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#8a553f}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}