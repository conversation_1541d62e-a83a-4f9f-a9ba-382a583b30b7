{"version": 3, "file": "BussproofsUtil.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/bussproofs/BussproofsUtil.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,+DAAsC;AACtC,iEAAwC;AAiBxC,IAAI,GAAG,GAAiB,IAAI,CAAC;AAC7B,IAAI,IAAI,GAAa,IAAI,CAAC;AAO1B,IAAI,OAAO,GAAG,UAAS,IAAa;IAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACV,IAAG,KAAK,GAAK,GAAG,CAAC,SAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAA7C,CAA8C;IAC7D,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAUF,IAAI,OAAO,GAAG,UAAS,IAAa;IAClC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,IAAI,IAAI,CAAC,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;QAC/C,IAAI,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,qBAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YACjC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;YACrC,CAAC,GAAG,CAAC,CAAC;YACN,SAAS;SACV;QACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;QAC5C,CAAC,EAAE,CAAC;KACL;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAaF,IAAI,WAAW,GAAG,UAAS,IAAa,EAAE,SAAiB;IACzD,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9D,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;AACzD,CAAC,CAAC;AASF,IAAI,UAAU,GAAG,UAAS,QAAiB,EAAE,CAAS;IACpD,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;AACvE,CAAC,CAAC;AAQF,IAAI,YAAY,GAAG,UAAS,QAAiB;IAC3C,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAY,CAAC;AAC5C,CAAC,CAAC;AAQF,IAAI,WAAW,GAAG,UAAS,QAAiB;IAC1C,OAAO,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AASF,IAAI,aAAa,GAAG,UAAS,IAAa,EAAE,SAAiB;IAC3D,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;AAC1G,CAAC,CAAC;AASF,IAAI,SAAS,GAAG,UAAS,GAAY;IACnC,OAAO,GAAG,IAAI,CAAC,qBAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;QAC1C,GAAG,GAAG,GAAG,CAAC,MAAiB,CAAC;KAC7B;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAQF,IAAI,WAAW,GAAG,UAAS,GAAY;IACrC,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAY,CAAC;AAClF,CAAC,CAAC;AASF,IAAI,eAAe,GAAG,UAAS,GAAY;IACzC,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAY,CAAC;AAClF,CAAC,CAAC;AAQF,IAAI,YAAY,GAAG,UAAS,GAAY;IACtC,OAAO,GAAG,IAAI,IAAA,mBAAW,EAAC,GAAG,EAAE,WAAW,CAAC,IAAI,IAAI,EAAE;QACnD,GAAG,GAAG,GAAG,CAAC,MAAiB,CAAC;KAC7B;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAgBF,IAAI,SAAS,GAAG,UAAS,GAAY,EAAE,IAAa,EAAE,KAAsB;IAAtB,sBAAA,EAAA,aAAsB;IAC1E,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,MAAM,CAAC;KACf;IACD,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,EAAE;QACvB,IAAI,UAAQ,GAAG,GAAG,CAAC,UAAuB,CAAC;QAC3C,IAAI,OAAK,GAAG,KAAK,CAAC,CAAC,CAAC,UAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,qBAAQ,CAAC,MAAM,CAAC,UAAQ,CAAC,OAAK,CAAC,EAAE,QAAQ,CAAC,EAAE;YAC9C,MAAM,IAAI,OAAO,CAAC,UAAQ,CAAC,OAAK,CAAC,CAAC,CAAC;SACpC;QACD,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;KACnB;IACD,IAAI,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,MAAM,CAAC;KACf;IACD,IAAI,QAAQ,GAAG,GAAG,CAAC,UAAuB,CAAC;IAC3C,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;QAC5B,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;KACpC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAaF,IAAI,WAAW,GAAG,UAAS,GAAY,EAAE,KAAsB;IAAtB,sBAAA,EAAA,aAAsB;IAC7D,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IACxB,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,IAAA,mBAAW,EAAC,IAAI,EAAE,eAAe,CAAW,CAAC,CAAC;IAE7E,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC;AAUF,IAAI,QAAQ,GAAG,UAAS,MAAoB,EAAE,GAAY,EAClC,KAAa,EAAE,KAAsB;IAAtB,sBAAA,EAAA,aAAsB;IAC3D,IAAI,IAAA,mBAAW,EAAC,GAAG,EAAE,eAAe,CAAC;QACjC,IAAA,mBAAW,EAAC,GAAG,EAAE,cAAc,CAAC,EAAE;QACpC,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxB,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC1B,GAAG,GAAG,IAAI,CAAC;KACZ;IAED,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAY,CAAC;IAC9C,IAAI,qBAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;QACrC,qBAAQ,CAAC,YAAY,CACnB,MAAM,EAAE,OAAO,EACf,sBAAS,CAAC,EAAE,CAAC,sBAAS,CAAC,QAAQ,CAC7B,qBAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAW,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAChE,OAAO;KACR;IACD,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EACpB,EAAC,KAAK,EAAE,sBAAS,CAAC,EAAE,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC;IACjE,IAAI,KAAK,EAAE;QACT,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACxB,OAAO;KACR;IACD,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;IACpB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC,CAAC;AAQF,IAAI,cAAc,GAAG,UAAS,GAAY,EAAE,IAAa;IACvD,IAAI,KAAK,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;IAChE,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;QACb,IAAI,KAAK,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAChC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,IAAA,mBAAW,EAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5B,IAAA,sBAAc,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SACxB;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAeF,IAAI,cAAc,GAAG,UAAS,MAAoB;IAChD,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO;KACR;IACD,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,SAAA,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC7D,IAAI,IAAA,mBAAW,EAAC,GAAG,EAAE,kBAAkB,CAAC,EAAE;YACxC,IAAA,sBAAc,EAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;YACxC,SAAS;SACV;QACD,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,IAAA,mBAAW,EAAC,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE;YACvC,SAAS;SACV;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClB,OAAO,IAAA,mBAAW,EAAC,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE;YAE1C,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,EAAE,IAAA,mBAAW,EAAC,GAAG,EAAE,eAAe,CAAW,CAAC,CAAC,CAAC;YAC1F,IAAI,OAAO,GAAG,CAAC,IAAA,mBAAW,EAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;gBAErD,aAAa,CAAC,OAAO,EAAE,IAAA,mBAAW,EAAC,OAAO,EAAE,eAAe,CAAW,CAAC,CAAC,CAAC;gBAEzE,OAAO,CAAC;YACV,IAAI,IAAA,mBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,EAAE;gBACnC,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;gBACvC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClB,IAAA,mBAAW,EAAC,GAAG,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;aAC5C;YACD,GAAG,GAAG,OAAO,CAAC;SACf;QACD,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAWF,IAAM,eAAe,GAAG,UAAS,MAAoB,EAAE,OAAgB,EACtC,QAAgB,EAAE,SAAiB,EAAE,KAAa;IACjF,IAAI,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EACpB,EAAC,KAAK,EAAE,sBAAS,CAAC,EAAE,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC;IACrE,IAAI,SAAS,KAAK,MAAM,EAAE;QACxB,IAAI,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;QAChE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;QACpB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAChC;SAAM;QACL,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;KAClD;IACD,IAAA,mBAAW,EAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC,CAAC;AAmBF,IAAM,qBAAqB,GAAG,UAAS,MAAoB,EAAE,QAAmB;IAC9E,IAAI,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;IACzB,OAAO,QAAQ,CAAC,MAAM,EAAE;QACtB,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;QACxB,IAAA,KAAA,OAAgB,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,IAAA,EAA3C,IAAI,QAAA,EAAE,KAAK,QAAgC,CAAC;QACjD,IAAI,IAAA,mBAAW,EAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE;YACpC,eAAe,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5E,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;SAChF;QACD,GAAG,GAAG,MAAM,CAAC;KACd;AACH,CAAC,CAAC;AAaF,IAAM,eAAe,GAAG,UAAS,GAAY,EAAE,MAAe;IAC5D,IAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC;IACjD,IAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC;IACpD,IAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC;IACjD,IAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC;IAEpD,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClB,CAAC,CAAC;AAyDK,IAAI,YAAY,GAAG,UAAS,GAAe;;IAChD,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrE,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC;IACtB,cAAc,CAAC,MAAM,CAAC,CAAC;IACvB,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;;QACrD,KAAgB,IAAA,eAAA,SAAA,UAAU,CAAA,sCAAA,8DAAE;YAAvB,IAAI,GAAG,uBAAA;YACV,IAAI,OAAO,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAExC,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,IAAA,mBAAW,EAAC,IAAI,EAAE,eAAe,CAAW,CAAC,CAAC;YAC/E,IAAI,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,IAAA,mBAAW,EAAC,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACtC,IAAI,QAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,QAAM,EAAE;oBACV,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAM,CAAC,CAAC;oBACpC,IAAI,GAAC,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;oBACpC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,QAAM,GAAG,GAAC,CAAC,CAAC;iBACnC;aACF;YAED,IAAI,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,IAAA,mBAAW,EAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,IAAI,EAAE;gBAC9C,SAAS;aACV;YACD,IAAI,MAAM,GAAG,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACzC,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACnC,IAAI,SAAS,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE,WAAW,CAAW,CAAC;YACxD,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;aACtC;YACD,IAAI,MAAM,SAAS,CAAC;YACpB,IAAI,OAAO,IAAI,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;gBAIzC,QAAQ,CAAC,MAAM,EAEN,IAAA,mBAAW,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;gBACzE,SAAS;aACV;YACD,IAAI,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,OAAO,EAAE;gBAGX,IAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EACpB,EAAC,KAAK,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,EAAC,CAAC,CAAC;gBAClE,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACzB,GAAG,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBAChC,SAAS;aACV;YACD,IAAI,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,UAAU,EAAE;gBACf,SAAS;aACV;YAGD,MAAM,GAAG,IAAA,mBAAW,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAAC,GAAG,CAAC,IAAA,mBAAW,EAAC,UAAU,EAAE,WAAW,CAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC5E,IAAA,mBAAW,EAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;SAC9C;;;;;;;;;AACH,CAAC,CAAC;AA7DS,QAAA,YAAY,gBA6DrB;AAOF,IAAI,eAAe,GAAG,OAAO,CAAC;AAC9B,IAAI,qBAAqB;IACvB,GAAC,eAAe,GAAG,WAAW,IAAG,IAAI;OACtC,CAAC;AAUK,IAAI,WAAW,GAAG,UAAS,IAAa,EAAE,QAAgB,EAAE,KAAe;IAChF,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,GAAG,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChE,CAAC,CAAC;AAFS,QAAA,WAAW,eAEpB;AASK,IAAI,WAAW,GAAG,UAAS,IAAa,EAAE,QAAgB;IAC/D,OAAO,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,GAAG,QAAQ,CAAC,CAAC;AAChE,CAAC,CAAC;AAFS,QAAA,WAAW,eAEpB;AAQK,IAAI,cAAc,GAAG,UAAS,IAAa,EAAE,QAAgB;IAClE,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,QAAQ,CAAC,CAAC;AAClD,CAAC,CAAC;AAFS,QAAA,cAAc,kBAEvB;AAQK,IAAI,kBAAkB,GAAG,UAAS,GAAe;IACtD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAC,GAAY,EAAE,KAAW;QAC/C,IAAI,IAAI,GAAa,EAAE,CAAC;QACxB,GAAG,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;YAC9B,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,eAAe,CAAC,CAAC,EAAE;gBACvE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,qBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SACzD;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAZS,QAAA,kBAAkB,sBAY3B;AAMK,IAAI,YAAY,GAAG,UAAU,GAAe;IACjD,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC;IACnB,IAAI,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE;QACjC,MAAM,KAAK,CAAC,yEAAyE,CAAC,CAAC;KACxF;AACH,CAAC,CAAC;AALS,QAAA,YAAY,gBAKrB;AAMK,IAAI,aAAa,GAAG,UAAU,IAAgB;IACnD,GAAG,GAAG,IAAI,CAAC;AACb,CAAC,CAAC;AAFS,QAAA,aAAa,iBAEtB"}