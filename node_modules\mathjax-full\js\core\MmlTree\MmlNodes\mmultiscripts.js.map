{"version": 3, "file": "mmultiscripts.js", "sourceRoot": "", "sources": ["../../../../ts/core/MmlTree/MmlNodes/mmultiscripts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4CAA6D;AAC7D,2CAAwC;AAOxC;IAAsC,oCAAU;IAAhD;;IAsFA,CAAC;IA1EC,sBAAW,kCAAI;aAAf;YACE,OAAO,eAAe,CAAC;QACzB,CAAC;;;OAAA;IAMD,sBAAW,mCAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAUS,sDAA2B,GAArC,UAAsC,UAAyB,EAAE,OAAgB,EAAE,KAAa,EAAE,KAAc;QAC9G,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC7E,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtD,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;gBAC/B,IAAI,CAAC,UAAU,EAAE;oBACf,UAAU,GAAG,IAAI,CAAC;oBAClB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBACf,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;wBACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;wBACnB,CAAC,EAAE,CAAC;qBACL;iBACF;aACF;iBAAM;gBACL,IAAI,UAAU,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxC,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;gBACvE,CAAC,EAAE,CAAC;aACL;SACF;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;SACzG;IACH,CAAC;IAOS,yCAAc,GAAxB,UAAyB,OAAqB;QAC5C,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,GAAG,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;gBAC/B,IAAI,UAAU,EAAE;oBACd,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,2BAA2B,GAAG,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;iBACnF;qBAAM;oBACL,UAAU,GAAG,IAAI,CAAC;oBAClB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;wBACvB,IAAI,CAAC,MAAM,CAAC,0DAA0D,EAAE,OAAO,CAAC,CAAC;qBAClF;iBACF;aACF;SACF;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/D,IAAI,CAAC,MAAM,CAAC,uDAAuD,EAAE,OAAO,CAAC,CAAC;SAC/E;QACD,iBAAM,cAAc,YAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IA/Ea,yBAAQ,gBACjB,uBAAU,CAAC,QAAQ,EACtB;IA+EJ,uBAAC;CAAA,AAtFD,CAAsC,uBAAU,GAsF/C;AAtFY,4CAAgB;AA6F7B;IAAoC,kCAAe;IAAnD;;IAmCA,CAAC;IAvBC,sBAAW,gCAAI;aAAf;YACE,OAAO,aAAa,CAAC;QACvB,CAAC;;;OAAA;IAKD,sBAAW,iCAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAOM,mCAAU,GAAjB,UAAkB,OAAqB;QACrC,iBAAM,UAAU,YAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;YACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,mCAAmC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAC7E;IACH,CAAC;IA5Ba,uBAAQ,gBACjB,4BAAe,CAAC,QAAQ,EAC3B;IA4BJ,qBAAC;CAAA,AAnCD,CAAoC,4BAAe,GAmClD;AAnCY,wCAAc;AA0C3B;IAA6B,2BAAe;IAA5C;;IAmCA,CAAC;IAvBC,sBAAW,yBAAI;aAAf;YACE,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAKD,sBAAW,0BAAK;aAAhB;YACE,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAOM,4BAAU,GAAjB,UAAkB,OAAqB;QACrC,iBAAM,UAAU,YAAC,OAAO,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;YACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,mCAAmC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAC7E;IACH,CAAC;IA5Ba,gBAAQ,gBACjB,4BAAe,CAAC,QAAQ,EAC3B;IA4BJ,cAAC;CAAA,AAnCD,CAA6B,4BAAe,GAmC3C;AAnCY,0BAAO"}