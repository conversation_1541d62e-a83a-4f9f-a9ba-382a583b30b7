(function(L,h,v,n){"use strict";function O(...t){const u=typeof t[0]=="string"?t[0]:void 0,o=typeof u=="string"?1:0,m={immediate:!!o,shallow:!0,abortPrevious:!0};let E={},d=v,s=m;const w=e=>!!e?.request;t.length>0+o&&(w(t[0+o])?d=t[0+o]:E=t[0+o]),t.length>1+o&&w(t[1+o])&&(d=t[1+o]),(t.length===2+o&&!w(t[1+o])||t.length===3+o)&&(s=t[t.length-1]||m);const{shallow:U,onSuccess:j=h.noop,onError:q=h.noop,immediate:B,resetOnExecute:N=!1}=s,x=s.initialData,y=n.shallowRef(),p=(U?n.shallowRef:n.ref)(x),i=n.shallowRef(!1),c=n.shallowRef(!1),r=n.shallowRef(!1),a=n.shallowRef();let b=new AbortController;const A=e=>{i.value||!c.value||(b.abort(e),b=new AbortController,r.value=!0,c.value=!1,i.value=!1)},V=e=>{c.value=e,i.value=!e},S=()=>{N&&(p.value=x)},D=()=>new Promise((e,f)=>{h.until(i).toBe(!0).then(()=>a.value?f(a.value):e(_))}),R={then:(...e)=>D().then(...e),catch:(...e)=>D().catch(...e)};let C=0;const P=(e=u,f={})=>{a.value=void 0;const F=typeof e=="string"?e:u??f.url;if(F===void 0)return a.value=new v.AxiosError(v.AxiosError.ERR_INVALID_URL),i.value=!0,R;S(),s.abortPrevious!==!1&&A(),V(!0),C+=1;const k=C;return r.value=!1,d(F,{...E,...typeof e=="object"?e:f,signal:b.signal}).then(l=>{if(r.value)return;y.value=l;const I=l.data;p.value=I,j(I)}).catch(l=>{a.value=l,q(l)}).finally(()=>{var l;(l=s.onFinish)==null||l.call(s),k===C&&V(!1)}),R};B&&u&&P();const _={response:y,data:p,error:a,isFinished:i,isLoading:c,cancel:A,isAborted:r,isCanceled:r,abort:A,execute:P};return{..._,...R}}L.useAxios=O})(this.VueUse=this.VueUse||{},VueUse,axios,Vue);
