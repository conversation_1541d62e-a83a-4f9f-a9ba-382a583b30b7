import { KeyCode } from '../common/event_util.js';
import { <PERSON><PERSON><PERSON> } from '../highlighter/highlighter.js';
import { AxisMap } from '../rule_engine/dynamic_cstr.js';
import { SemanticRole, SemanticType } from '../semantic_tree/semantic_meaning.js';
import { SemanticNode } from '../semantic_tree/semantic_node.js';
import { SemanticSkeleton } from '../semantic_tree/semantic_skeleton.js';
import { SpeechGenerator } from '../speech_generator/speech_generator.js';
import { Focus } from './focus.js';
import { Levels } from './levels.js';
import { RebuildStree } from './rebuild_stree.js';
import { <PERSON>, <PERSON>ove<PERSON> } from './walker.js';
export declare abstract class AbstractWalker<T> implements Walker {
    node: Element;
    generator: SpeechGenerator;
    highlighter: Highlighter;
    static ID_COUNTER: number;
    static SRE_ID_ATTR: string;
    modifier: boolean;
    id: any;
    rootNode: Element;
    rootId: string;
    skeleton: SemanticSkeleton;
    keyMapping: Map<KeyCode, () => any>;
    moved: WalkerMoves;
    cursors: {
        focus: Focus;
        levels: Levels<T>;
        undo: boolean;
    }[];
    levels: any;
    private xmlString_;
    private xml_;
    private rebuilt_;
    private focus_;
    private active_;
    abstract findFocusOnLevel(id: number): Focus;
    abstract initLevels(): Levels<T>;
    abstract combineContentChildren(type: SemanticType, role: SemanticRole, content: string[], children: string[]): T[];
    constructor(node: Element, generator: SpeechGenerator, highlighter: Highlighter, xml: string);
    getXml(): Element;
    getRebuilt(): RebuildStree;
    isActive(): boolean;
    activate(): void;
    deactivate(): void;
    getFocus(update?: boolean): Focus;
    setFocus(focus: Focus): void;
    getDepth(): number;
    isSpeech(): boolean;
    focusDomNodes(): Element[];
    focusSemanticNodes(): SemanticNode[];
    speech(): string;
    move(key: KeyCode): boolean;
    protected up(): Focus | null;
    protected down(): Focus | null;
    protected left(): Focus | null;
    protected right(): Focus | null;
    protected repeat(): Focus | null;
    protected depth(): Focus | null;
    protected home(): Focus | null;
    getBySemanticId(id: string): Element;
    primaryId(): string;
    expand(): Focus;
    expandable(node: Element): boolean;
    collapsible(node: Element): boolean;
    restoreState(): void;
    updateFocus(): void;
    protected rebuildStree(): void;
    previousLevel(): string | null;
    nextLevel(): T[];
    singletonFocus(id: string): Focus;
    private retrieveVisuals;
    private subtreeIds;
    focusFromId(id: string, ids: string[]): Focus;
    protected summary(): Focus | null;
    protected detail(): Focus | null;
    protected specialMove(): string | null;
    virtualize(opt_undo?: boolean): Focus;
    previous(): Focus;
    undo(): Focus;
    update(options: AxisMap): void;
    nextRules(): Focus;
    previousRules(): Focus;
    refocus(): void;
    private toggleActive_;
    private mergePrefix_;
    private prefix_;
    private postfix_;
    private depth_;
    private actionable_;
    private summary_;
    private detail_;
}
