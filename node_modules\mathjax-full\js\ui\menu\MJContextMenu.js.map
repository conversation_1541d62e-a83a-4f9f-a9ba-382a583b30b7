{"version": 3, "file": "MJContextMenu.js", "sourceRoot": "", "sources": ["../../../ts/ui/menu/MJContextMenu.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,sEAA+D;AAE/D,sEAA2D;AAU3D;IAAmC,iCAAW;IAA9C;QAAA,qEAyMC;QA5LQ,cAAQ,GAA0C,IAAI,CAAC;QAKvD,gBAAU,GAAW,EAAE,CAAC;QAexB,qBAAe,GAA+B,EAAE,CAAC;;IAwK1D,CAAC;IA9JQ,4BAAI,GAAX,UAAY,CAAO,EAAE,CAAU;QAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,KAAK,SAAS,EAAE;gBAEnB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC1C,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACjD,QAAQ,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC;gBAClF,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAClD,SAAS,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACrC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;gBACvD,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC9D,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,eAAe,EAAE,CAAC;aACxB;YACD,iBAAM,IAAI,YAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAClB;IACH,CAAC;IAOM,8BAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAUM,8BAAM,GAAb;;QAAc,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;QAC9B,IAAI,IAAI,GAAG,IAAY,CAAC;QACxB,IAAI,IAAI,GAAG,IAAY,CAAC;;YACxB,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,IAAI,IAAI,EAAE;oBACR,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;oBACvB,IAAI,GAAG,CAAC,IAAI,YAAY,yBAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;iBACxD;qBAAM;oBACL,IAAI,GAAG,IAAI,CAAC;iBACb;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOS,yCAAiB,GAA3B;QAAA,iBAIC;QAHC,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QAChE,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,EAAE,cAAM,OAAA,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAA1B,CAA0B,CAAC,CAAC;QACjF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,EAAE,cAAM,OAAA,KAAI,CAAC,cAAc,EAAE,EAArB,CAAqB,CAAC,CAAC;IAC9E,CAAC;IAOS,uCAAe,GAAzB;QACE,IAAI,IAAI,GAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAG;YACzC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC9D,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAOS,sCAAc,GAAxB,UAAyB,IAAa;;QACpC,IAAM,WAAW,GAAG,EAAwB,CAAC;QAC7C,IAAI,CAAC,IAAI;YAAE,OAAO,WAAW,CAAC;;YAC9B,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAuB,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;oBAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC1C,IAAI,KAAK,EAAE;wBACT,IAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,EAAvB,CAAuB,EAAE,EAAE,CAAC,CAAC;wBACpF,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;qBAClC;iBACF;aACF;;;;;;;;;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAMS,uCAAe,GAAzB,UAA0B,KAAc;;QACtC,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC;;YAC5D,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA,gBAAA,4BAAE;gBAAjD,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACrD,OAAO,IAAI,CAAC;iBACb;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IASS,4CAAoB,GAA9B,UAA+B,EAAU,EAAE,WAA+B,EAAE,MAAkB;QAA9F,iBAqBC;QApBC,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAY,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE;YACvD,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,UAAC,EAAa;oBAAb,KAAA,aAAa,EAAZ,IAAI,QAAA,EAAE,KAAK,QAAA;gBAClC,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,EAAE,EAAE,IAAI;oBACR,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC;wBACxB,MAAM,EAAE,CAAC;oBACX,CAAC;iBACF,CAAC;YACJ,CAAC,CAAC;YACF,EAAE,EAAE,aAAa;SAClB,EAAE,IAAI,CAAC,CAAC;QACT,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;aAAM;YACL,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;IACH,CAAC;IAOM,uCAAe,GAAtB;;;YACE,KAA2B,IAAA,KAAA,SAAA,aAAa,CAAC,eAAe,CAAA,gBAAA,4BAAE;gBAA/C,IAAA,KAAA,mBAAY,EAAX,EAAE,QAAA,EAAE,MAAM,QAAA;gBACpB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAY,CAAC;gBACtC,IAAI,CAAC,IAAI;oBAAE,SAAS;gBACpB,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;gBACnB,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;oBACpB,IAAI,CAAC,MAAM,EAAE,CAAC;iBACf;qBAAM;oBACL,IAAI,CAAC,OAAO,EAAE,CAAC;iBAChB;aACF;;;;;;;;;IACH,CAAC;IAjMa,6BAAe,GAEhB,IAAI,GAAG,EAAE,CAAC;IAiMzB,oBAAC;CAAA,AAzMD,CAAmC,6BAAW,GAyM7C;AAzMY,sCAAa"}