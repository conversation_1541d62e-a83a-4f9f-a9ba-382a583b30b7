"use strict";
Object.defineProperty(exports, '__esModule', {value: true});
exports.ARROWX = MathJax._.output.common.Notation.ARROWX;
exports.THICKNESS = MathJax._.output.common.Notation.THICKNESS;
exports.PADDING = MathJax._.output.common.Notation.PADDING;
exports.SOLID = MathJax._.output.common.Notation.SOLID;
exports.sideIndex = MathJax._.output.common.Notation.sideIndex;
exports.sideNames = MathJax._.output.common.Notation.sideNames;
exports.fullBBox = MathJax._.output.common.Notation.fullBBox;
exports.fullPadding = MathJax._.output.common.Notation.fullPadding;
exports.fullBorder = MathJax._.output.common.Notation.fullBorder;
exports.arrowHead = MathJax._.output.common.Notation.arrowHead;
exports.arrowBBoxHD = MathJax._.output.common.Notation.arrowBBoxHD;
exports.arrowBBoxW = MathJax._.output.common.Notation.arrowBBoxW;
exports.arrowDef = MathJax._.output.common.Notation.arrowDef;
exports.diagonalArrowDef = MathJax._.output.common.Notation.diagonalArrowDef;
exports.arrowBBox = MathJax._.output.common.Notation.arrowBBox;
exports.CommonBorder = MathJax._.output.common.Notation.CommonBorder;
exports.CommonBorder2 = MathJax._.output.common.Notation.CommonBorder2;
exports.CommonDiagonalStrike = MathJax._.output.common.Notation.CommonDiagonalStrike;
exports.CommonDiagonalArrow = MathJax._.output.common.Notation.CommonDiagonalArrow;
exports.CommonArrow = MathJax._.output.common.Notation.CommonArrow;
