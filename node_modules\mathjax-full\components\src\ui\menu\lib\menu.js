import {combineWithMathJax} from '../../../../../js/components/global.js';
import {VERSION} from '../../../../../js/components/version.js';

import * as module1 from '../../../../../js/ui/menu/MJContextMenu.js';
import * as module2 from '../../../../../js/ui/menu/Menu.js';
import * as module3 from '../../../../../js/ui/menu/MenuHandler.js';
import * as module4 from '../../../../../js/ui/menu/MmlVisitor.js';
import * as module5 from '../../../../../js/ui/menu/SelectableInfo.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('ui/menu', VERSION, 'ui');
}

combineWithMathJax({_: {
  ui: {
    menu: {
      MJContextMenu: module1,
      Menu: module2,
      MenuHandler: module3,
      MmlVisitor: module4,
      SelectableInfo: module5
    }
  }
}});
