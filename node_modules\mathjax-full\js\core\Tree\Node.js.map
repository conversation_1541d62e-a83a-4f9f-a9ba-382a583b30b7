{"version": 3, "file": "Node.js", "sourceRoot": "", "sources": ["../../../ts/core/Tree/Node.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;IAyBE,sBAAqB,OAAqC,EAAE,UAA6B,EAAE,QAAqB;;QAApD,2BAAA,EAAA,eAA6B;QAAE,yBAAA,EAAA,aAAqB;QAA3F,YAAO,GAAP,OAAO,CAA8B;QApBnD,WAAM,GAAS,IAAI,CAAC;QAKjB,eAAU,GAAiB,EAAE,CAAC;QAKjC,eAAU,GAAW,EAAE,CAAC;;YAW7B,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,MAAI,WAAA;gBACb,IAAI,CAAC,WAAW,CAAC,MAAI,EAAE,UAAU,CAAC,MAAI,CAAC,CAAC,CAAC;aAC1C;;;;;;;;;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAC5B;IACH,CAAC;IAKD,sBAAW,8BAAI;aAAf;YACE,OAAO,SAAS,CAAC;QACnB,CAAC;;;OAAA;IAKM,kCAAW,GAAlB,UAAmB,IAAY,EAAE,KAAe;QAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAChC,CAAC;IAKM,kCAAW,GAAlB,UAAmB,IAAY;QAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAKM,uCAAgB,GAAvB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAKM,uCAAgB,GAAvB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAKM,qCAAc,GAArB;;QAAsB,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;;YACtC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,OAAO,IAAI,CAAC,UAAU,CAAC,MAAI,CAAC,CAAC;aAC9B;;;;;;;;;IACH,CAAC;IAMM,6BAAM,GAAb,UAAc,IAAY;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAMM,kCAAW,GAAlB,UAAmB,QAAgB;;QACjC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;;YACrB,KAAkB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;gBAAvB,IAAI,KAAK,qBAAA;gBACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACzB;;;;;;;;;IACH,CAAC;IAKM,kCAAW,GAAlB,UAAmB,KAAW;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,mCAAY,GAAnB,UAAoB,QAAc,EAAE,QAAc;QAChD,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;YAC9B,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;YACvB,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;SACxB;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKM,kCAAW,GAAlB,UAAmB,KAAW;QAC5B,IAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;SACrB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAMM,iCAAU,GAAjB,UAAkB,IAAU;QAC1B,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAMM,2BAAI,GAAX;;QACE,IAAM,IAAI,GAAI,IAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAiB,CAAC;QAC9E,IAAI,CAAC,UAAU,gBAAO,IAAI,CAAC,UAAU,CAAC,CAAC;;YACvC,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,IAAI,EAAE,CAAA,gBAAA,4BAAE;gBAAtC,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,EAAE;oBACT,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;iBAChC;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,gCAAS,GAAhB,UAAiB,IAAY;QAC3B,IAAI,KAAK,GAAW,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,UAAC,IAAU;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAMM,+BAAQ,GAAf,UAAgB,IAAsC,EAAE,IAAU;;QAChE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;YACjB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,EAAE;oBACT,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAC5B;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,+BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC3D,CAAC;IAEH,mBAAC;AAAD,CAAC,AA9LD,IA8LC;AA9LqB,oCAAY;AAqMlC;IAAgD,qCAAY;IAA5D;;IAiDA,CAAC;IAzCQ,uCAAW,GAAlB,UAAmB,SAAiB;IACpC,CAAC;IAKM,uCAAW,GAAlB,UAAmB,KAAW;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,wCAAY,GAAnB,UAAoB,SAAe,EAAE,QAAc;QACjD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKM,sCAAU,GAAjB,UAAkB,KAAW;QAC3B,OAAO,IAAc,CAAC;IACxB,CAAC;IAOM,oCAAQ,GAAf,UAAgB,IAAsC,EAAE,IAAU;QAChE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,oCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEH,wBAAC;AAAD,CAAC,AAjDD,CAAgD,YAAY,GAiD3D;AAjDqB,8CAAiB"}