pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Horizon Dark
  Author: <PERSON><PERSON><PERSON> (http://github.com/michael-ball/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme horizon-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1C1E26  Default Background
base01  #232530  Lighter Background (Used for status bars, line number and folding marks)
base02  #2E303E  Selection Background
base03  #6F6F70  Comments, Invisibles, Line Highlighting
base04  #9DA0A2  Dark Foreground (Used for status bars)
base05  #CBCED0  Default Foreground, Caret, Delimiters, Operators
base06  #DCDFE4  Light Foreground (Not often used)
base07  #E3E6EE  Light Background (Not often used)
base08  #E93C58  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #E58D7D  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #EFB993  Classes, Markup Bold, Search Text Background
base0B  #EFAF8E  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #24A8B4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #DF5273  Functions, Methods, Attribute IDs, Headings
base0E  #B072D1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #E4A382  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #CBCED0;
  background: #1C1E26
}
.hljs::selection,
.hljs ::selection {
  background-color: #2E303E;
  color: #CBCED0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6F6F70 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6F6F70
}
/* base04 - #9DA0A2 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9DA0A2
}
/* base05 - #CBCED0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #CBCED0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #E93C58
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #E58D7D
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #EFB993
}
.hljs-strong {
  font-weight: bold;
  color: #EFB993
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #EFAF8E
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #24A8B4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #DF5273
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #B072D1
}
.hljs-emphasis {
  color: #B072D1;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #E4A382
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}