import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/action/ActionConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/action', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      action: {
        ActionConfiguration: module1
      }
    }
  }
}});
