import { SVGConstructor, Constructor } from '../Wrapper.js';
declare const SVGmrow_base: import("../../common/Wrappers/mrow.js").MrowConstructor & SVGConstructor<any, any, any>;
export declare class SVGmrow<N, T, D> extends SVGmrow_base {
    static kind: string;
    toSVG(parent: N): void;
}
declare const SVGinferredMrow_base: import("../../common/Wrappers/mrow.js").InferredMrowConstructor & Constructor<SVGmrow<any, any, any>>;
export declare class SVGinferredMrow<N, T, D> extends SVGinferredMrow_base {
    static kind: string;
}
export {};
