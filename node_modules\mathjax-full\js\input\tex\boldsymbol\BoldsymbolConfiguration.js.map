{"version": 3, "file": "BoldsymbolConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/boldsymbol/BoldsymbolConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAyBA,wDAAkD;AAClD,+DAAsC;AAEtC,sDAA+C;AAC/C,gDAA2C;AAE3C,oDAA8C;AAG9C,IAAI,WAAW,GAA4B,EAAE,CAAC;AAC9C,WAAW,CAAC,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,6BAAW,CAAC,OAAO,CAAC,IAAI,CAAC;AACnE,WAAW,CAAC,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAM,6BAAW,CAAC,OAAO,CAAC,UAAU,CAAC;AAC5E,WAAW,CAAC,6BAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAK,6BAAW,CAAC,OAAO,CAAC,WAAW,CAAC;AAC7E,WAAW,CAAC,6BAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAM,6BAAW,CAAC,OAAO,CAAC,UAAU,CAAC;AAC5E,WAAW,CAAC,6BAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,6BAAW,CAAC,OAAO,CAAC,aAAa,CAAC;AAC/E,WAAW,CAAC,mBAAmB,CAAC,GAAK,wBAAwB,CAAC;AAC9D,WAAW,CAAC,eAAe,CAAC,GAAS,oBAAoB,CAAC;AAC1D,WAAW,CAAC,aAAa,CAAC,GAAW,6BAAW,CAAC,OAAO,CAAC,UAAU,CAAC;AAIzD,QAAA,iBAAiB,GAAgC,EAAE,CAAC;AAQ/D,yBAAiB,CAAC,UAAU,GAAG,UAAS,MAAiB,EAAE,IAAY;IACrE,IAAI,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAChD,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;IACtC,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;IAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC;AAGF,IAAI,yBAAU,CAAC,YAAY,EAAE,EAAC,UAAU,EAAE,YAAY,EAAC,EAAE,yBAAiB,CAAC,CAAC;AAW5E,SAAgB,eAAe,CAAC,OAAoB,EAAE,IAAY,EAClC,GAAQ,EAAE,IAAY;IACpD,IAAI,KAAK,GAAG,4BAAW,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,IAAI,KAAK,OAAO;QAChB,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;QACxD,qBAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7C,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KACjD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AATD,0CASC;AAOD,SAAgB,iBAAiB,CAAC,GAAyB;;;QACzD,KAAiB,IAAA,KAAA,SAAA,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA,gBAAA,4BAAE;YAAzC,IAAI,IAAI,WAAA;YACX,IAAI,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;gBACzC,IAAI,OAAO,GAAG,qBAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,CAAW,CAAC;gBACnE,IAAI,OAAO,IAAI,IAAI,EAAE;oBACnB,qBAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,EAAE,6BAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACtE;qBAAM;oBACL,qBAAQ,CAAC,YAAY,CAAC,IAAI,EACJ,aAAa,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;iBACvE;gBACD,qBAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aAC5C;SACF;;;;;;;;;AACH,CAAC;AAbD,8CAaC;AAGY,QAAA,uBAAuB,GAAG,gCAAa,CAAC,MAAM,CACvD,YAAY,EAAE;IACV,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,YAAY,CAAC,EAAC;IAChC,KAAK,EAAE,EAAC,OAAO,EAAE,eAAe,EAAC;IACjC,cAAc,EAAE,CAAC,iBAAiB,CAAC;CACtC,CACJ,CAAC"}