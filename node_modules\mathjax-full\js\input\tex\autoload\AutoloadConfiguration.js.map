{"version": 3, "file": "AutoloadConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/autoload/AutoloadConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,wDAAuE;AAEvE,gDAA2C;AAC3C,0CAAmC;AAGnC,8EAAqF;AACrF,6DAAuD;AACvD,uDAAoE;AAepE,SAAS,QAAQ,CAAC,MAAiB,EAAE,IAAY,EAAE,SAAiB,EAAE,OAAgB;;IACpF,IAAI,oBAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;QACnE,IAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACzC,IAAA,KAAA,OAAiB,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAA,EAA7E,MAAM,QAAA,EAAE,IAAI,QAAiE,CAAC;;YACrF,KAAoB,IAAA,WAAA,SAAA,MAAM,CAAA,8BAAA,kDAAE;gBAAvB,IAAM,KAAK,mBAAA;gBACd,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC9B;;;;;;;;;;YACD,KAAkB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAAnB,IAAM,GAAG,iBAAA;gBACZ,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAClC;;;;;;;;;QAID,MAAM,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAE,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3G,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;KACd;IACD,IAAA,qCAAW,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACjC,CAAC;AASD,SAAS,YAAY,CAAC,MAA2B;IAC/C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;QAC3B,IAAA,2BAAc,EAAC,MAAM,CAAC,OAAO,EAAE,8CAAoB,CAAC,OAAO,CAAC,CAAC;KAC9D;AACH,CAAC;AAOD,SAAS,cAAc,CAAC,MAA2B,EAAE,GAAuB;;IAC1E,IAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC;IAChC,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC5C,IAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACxD,IAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;IACzC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC,CAAC;;QAI/C,KAAwB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA,gBAAA,4BAAE;YAA1C,IAAM,SAAS,WAAA;YAClB,IAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC1B,IAAA,KAAA,OAAe,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAA,EAA3E,IAAI,QAAA,EAAE,IAAI,QAAiE,CAAC;;gBAInF,KAAmB,IAAA,wBAAA,SAAA,IAAI,CAAA,CAAA,0BAAA,4CAAE;oBAApB,IAAM,MAAI,iBAAA;oBACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAI,CAAC,IAAI,MAAI,KAAK,OAAO,EAAE;wBAC5C,cAAc,CAAC,GAAG,CAAC,MAAI,EAAE,IAAI,iBAAK,CAAC,MAAI,EAAE,QAAQ,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;qBACxE;iBACF;;;;;;;;;;gBAID,KAAmB,IAAA,wBAAA,SAAA,IAAI,CAAA,CAAA,0BAAA,4CAAE;oBAApB,IAAM,MAAI,iBAAA;oBACb,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAI,CAAC,EAAE;wBAC9B,oBAAoB,CAAC,GAAG,CAAC,MAAI,EAAE,IAAI,iBAAK,CAAC,MAAI,EAAE,QAAQ,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC/E;iBACF;;;;;;;;;SACF;;;;;;;;;IAID,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QACtC,8CAAoB,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAC1C;AACH,CAAC;AAKD,IAAM,cAAc,GAAG,IAAI,yBAAU,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjE,IAAM,oBAAoB,GAAG,IAAI,yBAAU,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAMhE,QAAA,qBAAqB,GAAG,gCAAa,CAAC,MAAM,CACvD,UAAU,EAAE;IACV,OAAO,EAAE;QACP,KAAK,EAAE,CAAC,iBAAiB,CAAC;QAC1B,WAAW,EAAE,CAAC,uBAAuB,CAAC;KACvC;IACD,OAAO,EAAE;QAQP,QAAQ,EAAE,IAAA,uBAAU,EAAC;YACnB,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;YACxC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,UAAU,EAAE,CAAC,YAAY,CAAC;YAC1B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC1F,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;YAC/B,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;YACpD,KAAK,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;YACrE,OAAO,EAAE,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,CAAC;YACxG,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;YACzC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YACpB,UAAU,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,KAAK,EAAE,KAAK,CAAC;YAC9F,OAAO,EAAE,CAAC,SAAS,CAAC;YACpB,IAAI,EAAE,CAAC,MAAM,CAAC;SACf,CAAC;KACH;IACD,MAAM,EAAE,cAAc;IACtB,IAAI,EAAE,YAAY;IAClB,QAAQ,EAAE,EAAE;CACb,CACF,CAAC"}