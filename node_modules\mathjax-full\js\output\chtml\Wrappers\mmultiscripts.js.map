{"version": 3, "file": "mmultiscripts.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mmultiscripts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,2CAA0C;AAC1C,2EAAgF;AAChF,oFAAiF;AAGjF,qDAA8C;AAW9C;IACA,sCAA6G;IAD7G;;IAkGA,CAAC;IA3DQ,oCAAO,GAAd,UAAe,MAAS;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAI7B,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC;QACnE,IAAA,KAAA,OAAwB,IAAA,iBAAK,EAAC,WAAW,GAAG,GAAG,GAAG,WAAW,CAAC,IAAA,EAA7D,QAAQ,QAAA,EAAE,SAAS,QAA0C,CAAC;QAKrE,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAA,KAAA,OAAS,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAA,EAA7B,CAAC,QAAA,EAAE,CAAC,QAAyB,CAAC;QAIrC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5G,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;SACtF;QACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtF,SAAS,KAAK,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;SACvF;IACH,CAAC;IAcS,uCAAU,GAApB,UAAqB,CAAS,EAAE,CAAS,EAAE,KAAc,EAAE,GAAS,EAAE,GAAS,EAAE,CAAS,EAAE,CAAS;QACnG,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpC,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAM,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5D,IAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,KAAK,EAAE,EAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpE,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC5C,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;QACvD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAM,CAAC,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAM,CAAC,CAAC;SAClF;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IA1Fa,uBAAI,GAAG,mCAAgB,CAAC,SAAS,CAAC,IAAI,CAAC;IAKvC,yBAAM,GAAc;QAChC,gBAAgB,EAAE;YAChB,OAAO,EAAE,cAAc;YACvB,cAAc,EAAE,OAAO;SACxB;QACD,aAAa,EAAE;YACb,OAAO,EAAE,cAAc;YACvB,eAAe,EAAE,OAAO;SACzB;QACD,qCAAqC,EAAE;YACrC,YAAY,EAAE,OAAO;SACtB;QACD,4CAA4C,EAAE;YAC5C,YAAY,EAAE,MAAM;SACrB;QACD,8CAA8C,EAAE;YAC9C,YAAY,EAAE,QAAQ;SACvB;QACD,6CAA6C,EAAE;YAC7C,YAAY,EAAE,OAAO;SACtB;KACF,CAAC;IAkEJ,yBAAC;CAAA,AAlGD,CACA,IAAA,2CAAwB,EAAwE,yBAAY,CAAC,GAiG5G;AAlGY,gDAAkB"}