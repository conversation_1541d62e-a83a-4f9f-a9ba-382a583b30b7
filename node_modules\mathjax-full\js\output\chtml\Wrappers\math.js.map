{"version": 3, "file": "math.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/math.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAA6D;AAC7D,yDAA8D;AAC9D,kEAA+D;AAE/D,iDAA2C;AAW3C;IACA,6BAA8D;IAD9D;;IAqHA,CAAC;IAnEQ,2BAAO,GAAd,UAAe,MAAS;QACtB,iBAAM,OAAO,YAAC,MAAM,CAAC,CAAC;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,CAAC;QAClE,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAC/C,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;SAC3B;QACD,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACrC,CAAC;IAKS,iCAAa,GAAvB,UAAwB,MAAS;QAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAA,KAAA,OAAiB,IAAI,CAAC,aAAa,EAAE,IAAA,EAApC,KAAK,QAAA,EAAE,KAAK,QAAwB,CAAC;QAC5C,IAAI,KAAK,KAAK,QAAQ,EAAE;YACtB,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAI,CAAC,SAAS,EAAE;YACvC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gBACd,IAAA,KAAY,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,EAAxC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAiC,CAAC;gBAC9C,IAAI,KAAK,KAAK,OAAO,EAAE;oBACrB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;iBACnC;qBAAM,IAAI,KAAK,KAAK,MAAM,EAAE;oBAC3B,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,CAAC;iBACjC;qBAAM,IAAI,KAAK,KAAK,QAAQ,EAAE;oBAC7B,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC1B;gBACD,IAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;gBACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;aACxD;SACF;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC1C;IACH,CAAC;IAKS,gCAAY,GAAtB,UAAuB,MAAS;QAI9B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAC5D,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;YACjD,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YACjD,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;SACxC;IACH,CAAC;IAKM,mCAAe,GAAtB,UAAuB,SAAkB,EAAE,CAAgB,EAAE,KAAqB;QAAvC,kBAAA,EAAA,QAAgB;QAAE,sBAAA,EAAA,YAAqB;QAChF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAM,eAAe,YAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC5E,CAAC;IA7Ga,cAAI,GAAG,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC;IAK9B,gBAAM,GAAc;QAChC,UAAU,EAAE;YACV,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,MAAM;YACpB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,QAAQ;YACtB,aAAa,EAAE,QAAQ;YACvB,WAAW,EAAE,MAAM;YACnB,kBAAkB,EAAE,MAAM;YAC1B,gBAAgB,EAAE,QAAQ;YAC1B,iBAAiB,EAAE,UAAU;YAC7B,WAAW,EAAE,QAAQ;YACrB,cAAc,EAAE,QAAQ;YACxB,aAAa,EAAE,QAAQ;YACvB,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,OAAO;SACnB;QACD,4CAA4C,EAAE;YAC5C,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,QAAQ;YACtB,MAAM,EAAE,OAAO;SAChB;QACD,0DAA0D,EAAE;YAC1D,OAAO,EAAE,MAAM;SAChB;QACD,qDAAqD,EAAE;YACrD,OAAO,EAAE,CAAC;SACX;QACD,4CAA4C,EAAE;YAC5C,YAAY,EAAE,MAAM;SACrB;QACD,6CAA6C,EAAE;YAC7C,YAAY,EAAE,OAAO;SACtB;KACF,CAAC;IAwEJ,gBAAC;CAAA,AArHD,CACA,IAAA,yBAAe,EAAkC,yBAAY,CAAC,GAoH7D;AArHY,8BAAS"}