(function(l,u,N,d){"use strict";function V(t,i,o={}){let e;const{document:s=u.defaultDocument,...r}=o,c={onUpdate:n=>{m(i,n.oldIndex,n.newIndex,n)}},p=()=>{const n=typeof t=="string"?s?.querySelector(t):u.unrefElement(t);!n||e!==void 0||(e=new N(n,{...c,...r}))},v=()=>{e?.destroy(),e=void 0},h=(n,y)=>{if(y!==void 0)e?.option(n,y);else return e?.option(n)};return u.tryOnMounted(p),u.tryOnScopeDispose(v),{stop:v,start:p,option:h}}function f(t,i,o){const e=t.children[o];t.insertBefore(i,e)}function a(t){t.parentNode&&t.parentNode.removeChild(t)}function m(t,i,o,e=null){e!=null&&(a(e.item),f(e.from,e.item,i));const s=d.isRef(t),r=s?[...d.toValue(t)]:d.toValue(t);if(o>=0&&o<r.length){const c=r.splice(i,1)[0];d.nextTick(()=>{r.splice(o,0,c),s&&(t.value=r)})}}l.insertNodeAt=f,l.moveArrayElement=m,l.removeNode=a,l.useSortable=V})(this.VueUse=this.VueUse||{},VueUse,Sortable,Vue);
