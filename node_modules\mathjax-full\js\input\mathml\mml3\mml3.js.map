{"version": 3, "file": "mml3.js", "sourceRoot": "", "sources": ["../../../../ts/input/mathml/mml3/mml3.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,+CAA+C;AAgB/C;IAiBE,cAAY,QAA+B;QACzC,IAAI,OAAO,aAAa,KAAK,WAAW,EAAE;YAIxC,IAAI,CAAC,SAAS,GAAG,IAAA,8BAAe,GAAE,CAAC;SACpC;aAAM;YAIL,IAAM,WAAS,GAAG,IAAI,aAAa,EAAE,CAAC;YACtC,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAgB,CAAC;YAC5E,WAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,UAAC,IAAO;gBACvB,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACjC,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3D,IAAM,GAAG,GAAG,WAAS,CAAC,mBAAmB,CAAC,GAAkB,CAAa,CAAC;gBAC1E,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC;SACH;IACH,CAAC;IAOM,wBAAS,GAAhB,UAAiB,IAAyB;QACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE;YACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtD;IACH,CAAC;IAEH,WAAC;AAAD,CAAC,AAlDD,IAkDC;AAlDY,oBAAI;AAuDjB,SAAgB,WAAW,CAAU,OAAyB;;IAC5D,OAAO,CAAC,aAAa;YAAiB,2BAAqB;YAgBzD;;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAWd;;oBAVC,KAAkB,IAAA,KAAA,SAAA,KAAI,CAAC,QAAQ,IAAI,EAAE,CAAA,gBAAA,4BAAE;wBAAlC,IAAM,GAAG,WAAA;wBACZ,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;4BACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE;gCACtB,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAI,CAAC,CAAC;gCAC3B,GAAuB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gCACnE,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;6BAC1B;4BACD,MAAM;yBACP;qBACF;;;;;;;;;;YACH,CAAC;YACH,cAAC;QAAD,CAAC,AA7BuB,CAAc,OAAO,CAAC,aAAa;QAK3C,UAAO,yBAChB,OAAO,CAAC,aAAa,CAAC,OAAO,KAChC,UAAU,EAAE,IAAI,GAChB;WAqBH,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AAhCD,kCAgCC;AAKD,IAAI,CAAC,IAAI,GAAG,gjxBA8pBX,CAAC"}