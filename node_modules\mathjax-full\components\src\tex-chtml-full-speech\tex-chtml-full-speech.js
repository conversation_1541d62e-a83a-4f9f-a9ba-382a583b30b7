import '../startup/init.js';
import './preload.js';
import '../core/core.js';
import '../input/tex-full/tex-full.js';
import '../output/chtml/chtml.js';
import '../output/chtml/fonts/tex/tex.js';
import '../ui/menu/menu.js';
import '../a11y/assistive-mml/assistive-mml.js';
import '../a11y/sre/sre.js';
import MathMaps from '../../../js/a11y/mathmaps.js';
import base from 'speech-rule-engine/lib/mathmaps/base.json';
import en from 'speech-rule-engine/lib/mathmaps/en.json';
import nemeth from 'speech-rule-engine/lib/mathmaps/nemeth.json';

MathMaps.set('base', base);
MathMaps.set('en', en);
MathMaps.set('nemeth', nemeth);

import '../startup/startup.js';
