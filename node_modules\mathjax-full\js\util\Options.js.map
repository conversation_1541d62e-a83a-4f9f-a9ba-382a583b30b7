{"version": 3, "file": "Options.js", "sourceRoot": "", "sources": ["../../ts/util/Options.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAM,MAAM,GAAG,EAAE,CAAC,WAAW,CAAC;AAK9B,SAAgB,QAAQ,CAAC,GAAQ;IAC/B,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI;QAC5C,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM,IAAI,GAAG,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC;AACnE,CAAC;AAHD,4BAGC;AAoBY,QAAA,MAAM,GAAG,KAAK,CAAC;AAcf,QAAA,MAAM,GAAG,KAAK,CAAC;AAMf,QAAA,OAAO,GAAG;IACrB,aAAa,EAAE,MAA4B;IAO3C,WAAW,EAAE,UAAC,OAAe,EAAE,IAAY;QACzC,IAAI,eAAO,CAAC,aAAa,KAAK,OAAO,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QACD,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC;IACtC,CAAC;CACF,CAAC;AAMF;IAAA;IAAyB,CAAC;IAAD,iBAAC;AAAD,CAAC,AAA1B,IAA0B;AAAb,gCAAU;AAuBvB,SAAgB,UAAU,CAAC,GAAe;IACxC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;AACjE,CAAC;AAFD,gCAEC;AAMD,SAAgB,SAAS,CAAC,CAAM;IAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;AAFD,8BAEC;AASD,SAAgB,IAAI,CAAC,GAAe;IAClC,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,EAAE,CAAC;KACX;IACD,OAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7F,CAAC;AALD,oBAKC;AASD,SAAgB,IAAI,CAAC,GAAe;;IAClC,IAAI,KAAK,GAAe,EAAE,CAAC;;QAC3B,KAAkB,IAAA,KAAA,SAAA,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;YAAxB,IAAM,GAAG,WAAA;YACZ,IAAI,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACrD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACvC;iBAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;aAC1B;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,KAAK,CAAC,GAAa,CAAC,GAAG,IAAI,CAAC;aAC7B;SACF;;;;;;;;;IACD,OAAO,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC9F,CAAC;AAfD,oBAeC;AAYD,SAAgB,MAAM,CAAC,GAAe,EAAE,GAAe,EAAE,IAAoB;;IAApB,qBAAA,EAAA,WAAoB;4BAClE,GAAG;QAIV,IAAI,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,GAAG,CAAC,WAAW,KAAK,UAAU,EAAE;YACpE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,GAAG,GAAI,GAAc,CAAC,QAAQ,EAAE,CAAC;aAClC;YACD,eAAO,CAAC,WAAW,CAAC,2BAAmB,GAAG,2BAAuB,EAAE,GAAG,CAAC,CAAC;;SAEzE;QAID,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAKrC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;YAC/B,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,UAAU,CAAC,EAAE;YAC5D,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAIvB,IAIE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;gBACjB,CAIE,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,cAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,cAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAK3F,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,cAAM,GAAG,GAAG,GAAG,cAAM;wBAClE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAM,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAM,CAAC,CAAC,CAAC,CAC/D,EACH;gBAIA,IAAI,IAAI,CAAC,cAAM,CAAC,EAAE;oBAChB,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,cAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAA3B,CAA2B,CAAC,CAAC;iBACjE;gBAID,IAAI,IAAI,CAAC,cAAM,CAAC,EAAE;oBAChB,GAAG,CAAC,GAAG,CAAC,0CAAO,IAAI,kBAAK,IAAI,CAAC,cAAM,CAAC,SAAC,CAAC;iBACvC;aACF;iBAAM;gBAIL,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC1B;SACF;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAK9B,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC/B;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAIzB,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;aAAM;YAIL,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;SACjB;;;QA9EH,KAAgB,IAAA,KAAA,SAAA,IAAI,CAAC,GAAG,CAAa,CAAA,gBAAA;YAAhC,IAAI,GAAG,WAAA;oBAAH,GAAG;SA+EX;;;;;;;;;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAlFD,wBAkFC;AAWD,SAAgB,cAAc,CAAC,OAAmB;IAAE,cAAqB;SAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;QAArB,6BAAqB;;IACvE,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,EAA3B,CAA2B,CAAC,CAAC;IACjD,OAAO,OAAO,CAAC;AACjB,CAAC;AAHD,wCAGC;AAWD,SAAgB,WAAW,CAAC,OAAmB;IAAE,cAAqB;SAArB,UAAqB,EAArB,qBAAqB,EAArB,IAAqB;QAArB,6BAAqB;;IACpE,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,EAA1B,CAA0B,CAAC,CAAC;IAChD,OAAO,OAAO,CAAC;AACjB,CAAC;AAHD,kCAGC;AAUD,SAAgB,aAAa,CAAC,OAAmB;;IAAE,cAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,6BAAiB;;IAClE,IAAI,MAAM,GAAe,EAAE,CAAC;;QAC5B,KAAkB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;YAAnB,IAAM,GAAG,iBAAA;YACZ,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;;;;;;;;;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AARD,sCAQC;AAWD,SAAgB,qBAAqB,CAAC,OAAmB,EAAE,MAAkB;IAC3E,OAAO,aAAa,8BAAC,OAAO,UAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAE;AACxD,CAAC;AAFD,sDAEC;AAiBD,SAAgB,eAAe,CAAC,OAAmB;;IAAE,iBAAwB;SAAxB,UAAwB,EAAxB,qBAAwB,EAAxB,IAAwB;QAAxB,gCAAwB;;IAC3E,IAAI,OAAO,GAAiB,EAAE,CAAC;;QAC/B,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;YAAzB,IAAM,MAAM,oBAAA;YACf,IAAI,MAAM,GAAe,EAAE,EAAE,OAAO,GAAe,EAAE,CAAC;;gBACtD,KAAkB,IAAA,oBAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA,CAAA,gBAAA,4BAAE;oBAAzC,IAAM,GAAG,WAAA;oBACZ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;iBACpE;;;;;;;;;YACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAO,GAAG,OAAO,CAAC;SACnB;;;;;;;;;IACD,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACzB,OAAO,OAAO,CAAC;AACjB,CAAC;AAZD,0CAYC;AAYD,SAAgB,MAAM,CAAC,IAAY,EAAE,MAAkB,EAAE,GAAe;IAAf,oBAAA,EAAA,UAAe;IACtE,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AAFD,wBAEC"}