import { SVGConstructor } from '../Wrapper.js';
import { StyleList } from '../../../util/StyleList.js';
declare const SVGmath_base: import("../../common/Wrappers/math.js").MathConstructor & SVGConstructor<any, any, any>;
export declare class SVGmath<N, T, D> extends SVGmath_base {
    static kind: string;
    static styles: StyleList;
    toSVG(parent: N): void;
    protected handleDisplay(): void;
    protected handleSpeech(): void;
    protected getTitleID(): string;
    setChildPWidths(recompute: boolean, w?: number, _clear?: boolean): boolean;
}
export {};
