{"version": 3, "file": "mpadded.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mpadded.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,4CAAwE;AACxE,+DAAoE;AACpE,wEAAqE;AAYrE;IACA,gCAAiE;IADjE;;IAiEA,CAAC;IAzCQ,8BAAO,GAAd,UAAe,MAAS;;QACtB,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,IAAM,KAAK,GAAc,EAAE,CAAC;QACtB,IAAA,KAAA,OAAiC,IAAI,CAAC,SAAS,EAAE,IAAA,EAA3C,CAAC,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAAoB,CAAC;QAIxD,IAAI,EAAE,EAAE;YACN,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SAC/B;QAID,IAAI,EAAE,IAAI,EAAE,EAAE;YACZ,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SAClD;QAKD,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACjC,KAAK,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC,KAAK,EAAC;aAC3E,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE;gBACjD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBACjD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;YACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpB;QAID,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC,KAAK,EAAE,KAAK,EAAC,EAAE,OAAO,CAAC,CAAM,CAAC;;YACzF,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;aACpC;;;;;;;;;IACH,CAAC;IAzDa,iBAAI,GAAG,uBAAU,CAAC,SAAS,CAAC,IAAI,CAAC;IAKjC,mBAAM,GAAc;QAChC,aAAa,EAAE;YACb,OAAO,EAAE,cAAc;SACxB;QACD,UAAU,EAAE;YACV,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,UAAU;SACrB;KACF,CAAC;IA8CJ,mBAAC;CAAA,AAjED,CACA,IAAA,+BAAkB,EAAkC,yBAAY,CAAC,GAgEhE;AAjEY,oCAAY"}