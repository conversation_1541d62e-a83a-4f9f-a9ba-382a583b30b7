{"version": 3, "file": "PhysicsConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/physics/PhysicsConfiguration.ts"], "names": [], "mappings": ";;;;AAwBA,wDAAkD;AAClD,qDAA2C;AAC3C,gCAA8B;AAGjB,QAAA,oBAAoB,GAAG,gCAAa,CAAC,MAAM,CACtD,SAAS,EAAE;IACT,OAAO,EAAE;QACP,KAAK,EAAE;YACL,kCAAkC;YAClC,uBAAuB;YACvB,mBAAmB;YACnB,mBAAmB;YACnB,2BAA2B;YAC3B,4BAA4B;YAC5B,2BAA2B;YAC3B,wBAAwB;YACxB,uBAAuB;SACxB;QACD,SAAS,EAAE,CAAC,oBAAoB,CAAC;QACjC,WAAW,EAAE,CAAC,kBAAkB,CAAC;KAClC;IACD,KAAK;QACH,GAAC,0BAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,0BAAQ;WACpC;IACD,OAAO,EAAE;QACP,OAAO,EAAE;YACP,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,KAAK;SAChB;KACF;CACF,CACF,CAAC"}