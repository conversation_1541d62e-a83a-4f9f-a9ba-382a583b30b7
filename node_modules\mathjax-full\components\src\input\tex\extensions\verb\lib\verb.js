import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/verb/VerbConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/verb', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      verb: {
        VerbConfiguration: module1
      }
    }
  }
}});
