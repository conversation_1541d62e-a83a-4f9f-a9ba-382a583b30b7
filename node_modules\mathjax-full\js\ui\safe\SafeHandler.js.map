{"version": 3, "file": "SafeHandler.js", "sourceRoot": "", "sources": ["../../../ts/ui/safe/SafeHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,qCAA+B;AA8B/B,SAAgB,qBAAqB,CACnC,YAAe;;IAGf;YAAqB,2BAAY;YAwB/B;;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAcd;gBAbC,KAAI,CAAC,IAAI,GAAG,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAI,EAAE,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACvE,IAAM,WAAW,GAAI,KAAI,CAAC,WAAmC,CAAC,WAAW,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBAC5B,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBAC9B;;oBACD,KAAkB,IAAA,KAAA,SAAA,KAAI,CAAC,QAAQ,CAAA,gBAAA,4BAAE;wBAA5B,IAAM,GAAG,WAAA;wBACZ,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;4BAC3B,GAAW,CAAC,MAAM,CAAC,eAAe,GAAG,KAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;4BAC5E,GAAW,CAAC,MAAM,CAAC,eAAe,GAAG,KAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;yBAC9E;6BAAM,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;4BAChC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;yBACpD;qBACF;;;;;;;;;;YACH,CAAC;YAMS,0BAAQ,GAAlB,UAAmB,IAAoE;gBACrF,IAAI,CAAC,IAAI,CAAC,IAAI,GAAI,IAAY,CAAC,YAAY,CAAC,IAAI,CAAC;gBACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,CAAC;YACH,cAAC;QAAD,CAAC,AAjDM,CAAc,YAAY;QAKjB,UAAO,yBAChB,YAAY,CAAC,OAAO,KACvB,WAAW,eACN,cAAI,CAAC,OAAO,GAEjB,SAAS,EAAE,cAAI,GACf;WAsCF;AAEJ,CAAC;AAvDD,sDAuDC;AAWD,SAAgB,WAAW,CAAU,OAAyB;IAC5D,OAAO,CAAC,aAAa,GAAG,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACrE,OAAO,OAAO,CAAC;AACjB,CAAC;AAHD,kCAGC"}