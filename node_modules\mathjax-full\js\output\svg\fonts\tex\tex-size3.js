"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.texSize3 = void 0;
var FontData_js_1 = require("../../FontData.js");
var tex_size3_js_1 = require("../../../common/fonts/tex/tex-size3.js");
exports.texSize3 = (0, FontData_js_1.AddPaths)(tex_size3_js_1.texSize3, {
    0x28: '701 -940Q701 -943 695 -949H664Q662 -947 636 -922T591 -879T537 -818T475 -737T412 -636T350 -511T295 -362T250 -186T221 17T209 251Q209 962 573 1361Q596 1386 616 1405T649 1437T664 1450H695Q701 1444 701 1441Q701 1436 681 1415T629 1356T557 1261T476 1118T400 927T340 675T308 359Q306 321 306 250Q306 -139 400 -430T690 -924Q701 -936 701 -940',
    0x29: '34 1438Q34 1446 37 1448T50 1450H56H71Q73 1448 99 1423T144 1380T198 1319T260 1238T323 1137T385 1013T440 864T485 688T514 485T526 251Q526 134 519 53Q472 -519 162 -860Q139 -885 119 -904T86 -936T71 -949H56Q43 -949 39 -947T34 -937Q88 -883 140 -813Q428 -430 428 251Q428 453 402 628T338 922T245 1146T145 1309T46 1425Q44 1427 42 1429T39 1433T36 1436L34 1438',
    0x2F: '81 -949Q71 -949 63 -941T55 -921Q55 -917 56 -915Q59 -906 498 264T939 1438Q945 1450 960 1450Q972 1450 980 1441T988 1421Q982 1403 839 1020L398 -155Q107 -934 103 -938Q96 -949 81 -949',
    0x5B: '247 -949V1450H516V1388H309V-887H516V-949H247',
    0x5C: '988 -922Q988 -933 980 -941T962 -949Q947 -949 940 -938Q936 -934 645 -155L204 1020Q56 1416 56 1424Q56 1433 62 1441T84 1450Q97 1448 103 1439Q107 1435 398 656L839 -519Q988 -918 988 -922',
    0x5D: '11 1388V1450H280V-949H11V-887H218V1388H11',
    0x7B: '618 -943L612 -949H582L568 -943Q472 -903 411 -841T332 -703Q327 -682 327 -653T325 -350Q324 -28 323 -18Q317 24 301 61T264 124T221 171T179 205T147 225T132 234Q130 238 130 250Q130 255 130 258T131 264T132 267T134 269T139 272T144 275Q207 308 256 367Q310 436 323 519Q324 529 325 851Q326 1124 326 1154T332 1205Q369 1358 566 1443L582 1450H612L618 1444V1429Q618 1413 616 1411L608 1406Q599 1402 585 1393T552 1372T515 1343T479 1305T449 1257T429 1200Q425 1180 425 1152T423 851Q422 579 422 549T416 498Q407 459 388 424T346 364T297 318T250 284T214 264T197 254L188 251L205 242Q290 200 345 138T416 3Q421 -18 421 -48T423 -349Q423 -397 423 -472Q424 -677 428 -694Q429 -697 429 -699Q434 -722 443 -743T465 -782T491 -816T519 -845T548 -868T574 -886T595 -899T610 -908L616 -910Q618 -912 618 -928V-943',
    0x7D: '131 1414T131 1429T133 1447T148 1450H153H167L182 1444Q276 1404 336 1343T415 1207Q421 1184 421 1154T423 851L424 531L426 517Q434 462 460 415T518 339T571 296T608 274Q615 270 616 267T618 251Q618 241 618 238T615 232T608 227Q542 194 491 132T426 -15L424 -29L423 -350Q422 -622 422 -652T415 -706Q397 -780 337 -841T182 -943L167 -949H153Q137 -949 134 -946T131 -928Q131 -914 132 -911T144 -904Q146 -903 148 -902Q299 -820 323 -680Q324 -663 325 -349T327 -19Q355 145 541 241L561 250L541 260Q356 355 327 520Q326 537 325 850T323 1181Q315 1227 293 1267T244 1332T193 1374T151 1401T132 1413Q131 1414 131 1429',
    0x2C6: '1439 564Q1434 564 1080 631T722 698Q719 698 362 631Q7 564 4 564L0 583Q-4 602 -4 603L720 772L1083 688Q1446 603 1447 603Q1447 602 1443 583L1439 564',
    0x2DC: '1 643Q1 646 76 671T271 722T476 749Q555 749 626 736T742 706T856 676T999 662Q1088 662 1192 684T1363 727T1432 749Q1432 745 1437 731T1442 716Q1442 714 1381 693T1212 645T1012 611Q1000 610 955 610Q851 610 701 653T444 697Q355 697 251 676T80 632T11 610Q11 614 6 628T1 643',
    0x302: '-5 564Q-9 564 -363 631T-722 698Q-725 698 -1082 631Q-1437 564 -1440 564L-1444 583Q-1448 602 -1448 603L-724 772L-361 688Q2 603 3 603Q3 602 -1 583L-5 564',
    0x303: '-1443 643Q-1443 646 -1368 671T-1173 722T-968 749Q-889 749 -818 736T-702 706T-588 676T-445 662Q-356 662 -252 684T-81 727T-12 749Q-12 745 -7 731T-2 716Q-2 714 -63 693T-232 645T-432 611Q-444 610 -489 610Q-593 610 -743 653T-1000 697Q-1089 697 -1193 676T-1364 632T-1433 610Q-1433 614 -1438 628T-1443 643',
    0x2044: '81 -949Q71 -949 63 -941T55 -921Q55 -917 56 -915Q59 -906 498 264T939 1438Q945 1450 960 1450Q972 1450 980 1441T988 1421Q982 1403 839 1020L398 -155Q107 -934 103 -938Q96 -949 81 -949',
    0x221A: '424 -948Q422 -947 313 -434T202 80L170 31Q165 24 157 10Q137 -21 137 -21Q131 -16 124 -8L111 5L264 248L473 -720Q473 -717 727 359T983 1440Q989 1450 1001 1450Q1007 1450 1013 1445T1020 1433Q1020 1425 742 244T460 -941Q458 -950 439 -950H436Q424 -950 424 -948',
    0x2308: '246 -949V1450H571V1388H308V-949H246',
    0x2309: '11 1388V1450H336V-949H274V1388H11',
    0x230A: '246 -949V1450H308V-887H571V-949H246',
    0x230B: '274 -887V1450H336V-949H11V-887H274',
    0x2329: '126 242V259L361 845Q595 1431 597 1435Q610 1450 624 1450Q634 1450 644 1443T654 1419V1411L422 831Q190 253 190 250T422 -331L654 -910V-919Q654 -936 644 -943T624 -950Q612 -950 597 -935Q595 -931 361 -345L126 242',
    0x232A: '94 1424Q94 1426 97 1432T107 1444T124 1450Q141 1450 152 1435Q154 1431 388 845L623 259V242L388 -345Q153 -933 152 -934Q142 -949 127 -949H125Q95 -949 95 -919V-910L327 -331Q559 247 559 250T327 831Q94 1411 94 1424',
    0x27E8: '126 242V259L361 845Q595 1431 597 1435Q610 1450 624 1450Q634 1450 644 1443T654 1419V1411L422 831Q190 253 190 250T422 -331L654 -910V-919Q654 -936 644 -943T624 -950Q612 -950 597 -935Q595 -931 361 -345L126 242',
    0x27E9: '94 1424Q94 1426 97 1432T107 1444T124 1450Q141 1450 152 1435Q154 1431 388 845L623 259V242L388 -345Q153 -933 152 -934Q142 -949 127 -949H125Q95 -949 95 -919V-910L327 -331Q559 247 559 250T327 831Q94 1411 94 1424',
    0x3008: '126 242V259L361 845Q595 1431 597 1435Q610 1450 624 1450Q634 1450 644 1443T654 1419V1411L422 831Q190 253 190 250T422 -331L654 -910V-919Q654 -936 644 -943T624 -950Q612 -950 597 -935Q595 -931 361 -345L126 242',
    0x3009: '94 1424Q94 1426 97 1432T107 1444T124 1450Q141 1450 152 1435Q154 1431 388 845L623 259V242L388 -345Q153 -933 152 -934Q142 -949 127 -949H125Q95 -949 95 -919V-910L327 -331Q559 247 559 250T327 831Q94 1411 94 1424',
}, {});
//# sourceMappingURL=tex-size3.js.map