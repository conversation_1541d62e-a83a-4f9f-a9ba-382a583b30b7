{"version": 3, "file": "bold.js", "sourceRoot": "", "sources": ["../../../../../ts/output/svg/fonts/tex/bold.ts"], "names": [], "mappings": ";;;AAgBA,iDAAuD;AACvD,6DAA+D;AAElD,QAAA,IAAI,GAAe,IAAA,sBAAQ,EAAC,cAAI,EAAE;IAC3C,IAAI,EAAE,kOAAkO;IACxO,IAAI,EAAE,ucAAuc;IAC7c,IAAI,EAAE,m8BAAm8B;IACz8B,IAAI,EAAE,ivBAAivB;IACvvB,IAAI,EAAE,2yBAA2yB;IACjzB,IAAI,EAAE,k1BAAk1B;IACx1B,IAAI,EAAE,iPAAiP;IACvP,IAAI,EAAE,wOAAwO;IAC9O,IAAI,EAAE,qRAAqR;IAC3R,IAAI,EAAE,4pBAA4pB;IAClqB,IAAI,EAAE,gRAAgR;IACtR,IAAI,EAAE,gQAAgQ;IACtQ,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE,0FAA0F;IAChG,IAAI,EAAE,2IAA2I;IACjJ,IAAI,EAAE,8LAA8L;IACpM,IAAI,EAAE,oUAAoU;IAC1U,IAAI,EAAE,wPAAwP;IAC9P,IAAI,EAAE,oPAAoP;IAC1P,IAAI,EAAE,qOAAqO;IAC3O,IAAI,EAAE,4dAA4d;IACle,IAAI,EAAE,uqBAAuqB;IAC7qB,IAAI,EAAE,4CAA4C;IAClD,IAAI,EAAE,4JAA4J;IAClK,IAAI,EAAE,wCAAwC;IAC9C,IAAI,EAAE,yIAAyI;IAC/I,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,yOAAyO;IAC/O,IAAI,EAAE,qrBAAqrB;IAC3rB,IAAI,EAAE,+FAA+F;IACrG,IAAI,EAAE,8qBAA8qB;IACprB,IAAI,EAAE,uQAAuQ;IAC7Q,IAAI,EAAE,+MAA+M;IACrN,IAAI,EAAE,6MAA6M;IACnN,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE,kSAAkS;IACxS,IAAI,EAAE,iSAAiS;IACvS,IAAI,EAAE,yMAAyM;IAC/M,IAAI,EAAE,4FAA4F;IAClG,IAAI,EAAE,oaAAoa;IAC1a,IAAI,EAAE,uTAAuT;IAC7T,KAAK,EAAE,yPAAyP;IAChQ,KAAK,EAAE,mUAAmU;IAC1U,KAAK,EAAE,sLAAsL;IAC7L,KAAK,EAAE,yIAAyI;IAChJ,KAAK,EAAE,iJAAiJ;IACxJ,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,yMAAyM;IAChN,KAAK,EAAE,yOAAyO;IAChP,KAAK,EAAE,yJAAyJ;IAChK,KAAK,EAAE,iGAAiG;IACxG,KAAK,EAAE,kSAAkS;IACzS,KAAK,EAAE,uQAAuQ;IAC9Q,KAAK,EAAE,sQAAsQ;IAC7Q,KAAK,EAAE,kOAAkO;IACzO,KAAK,EAAE,0JAA0J;IACjK,KAAK,EAAE,ySAAyS;IAChT,KAAK,EAAE,2BAA2B;IAClC,KAAK,EAAE,2KAA2K;IAClL,KAAK,EAAE,6GAA6G;IACpH,KAAK,EAAE,0OAA0O;IACjP,KAAK,EAAE,uUAAuU;IAC9U,KAAK,EAAE,4VAA4V;IACnW,KAAK,EAAE,mKAAmK;IAC1K,KAAK,EAAE,wMAAwM;IAC/M,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,qBAAqB;IAC7B,MAAM,EAAE,sBAAsB;IAC9B,MAAM,EAAE,sBAAsB;IAC9B,MAAM,EAAE,gWAAgW;IACxW,MAAM,EAAE,qBAAqB;IAC7B,MAAM,EAAE,2NAA2N;IACnO,MAAM,EAAE,iPAAiP;IACzP,MAAM,EAAE,kdAAkd;IAC1d,MAAM,EAAE,ucAAuc;IAC/c,MAAM,EAAE,2iBAA2iB;IACnjB,MAAM,EAAE,m9BAAm9B;IAC39B,MAAM,EAAE,0IAA0I;IAClJ,MAAM,EAAE,qSAAqS;IAC7S,MAAM,EAAE,sLAAsL;IAC9L,MAAM,EAAE,uBAAuB;IAC/B,MAAM,EAAE,2IAA2I;IACnJ,MAAM,EAAE,4QAA4Q;IACpR,MAAM,EAAE,upBAAupB;IAC/pB,MAAM,EAAE,gjBAAgjB;IACxjB,MAAM,EAAE,q0BAAq0B;IAC70B,MAAM,EAAE,iyBAAiyB;IACzyB,MAAM,EAAE,kXAAkX;IAC1X,MAAM,EAAE,4WAA4W;IACpX,MAAM,EAAE,yVAAyV;IACjW,MAAM,EAAE,uWAAuW;IAC/W,MAAM,EAAE,0mBAA0mB;IAClnB,MAAM,EAAE,wgBAAwgB;IAChhB,MAAM,EAAE,gdAAgd;IACxd,MAAM,EAAE,qgBAAqgB;IAC7gB,MAAM,EAAE,qfAAqf;IAC7f,MAAM,EAAE,mfAAmf;IAC3f,MAAM,EAAE,4ZAA4Z;IACpa,MAAM,EAAE,4eAA4e;IACpf,MAAM,EAAE,weAAwe;IAChf,MAAM,EAAE,iQAAiQ;IACzQ,MAAM,EAAE,4OAA4O;IACpP,MAAM,EAAE,2PAA2P;IACnQ,MAAM,EAAE,kOAAkO;IAC1O,MAAM,EAAE,ueAAue;IAC/e,MAAM,EAAE,2iBAA2iB;IACnjB,MAAM,EAAE,2nBAA2nB;IACnoB,MAAM,EAAE,8hBAA8hB;IACtiB,MAAM,EAAE,gnBAAgnB;IACxnB,MAAM,EAAE,ixBAAixB;IACzxB,MAAM,EAAE,2qBAA2qB;IACnrB,MAAM,EAAE,qSAAqS;IAC7S,MAAM,EAAE,6LAA6L;IACrM,MAAM,EAAE,ksBAAksB;IAC1sB,MAAM,EAAE,iMAAiM;IACzM,MAAM,EAAE,ugBAAugB;IAC/gB,MAAM,EAAE,o0BAAo0B;IAC50B,MAAM,EAAE,maAAma;IAC3a,MAAM,EAAE,uHAAuH;IAC/H,MAAM,EAAE,uVAAuV;IAC/V,MAAM,EAAE,2IAA2I;IACnJ,MAAM,EAAE,4JAA4J;IACpK,MAAM,EAAE,q0BAAq0B;IAC70B,MAAM,EAAE,iPAAiP;IACzP,MAAM,EAAE,0IAA0I;IAClJ,MAAM,EAAE,mUAAmU;IAC3U,MAAM,EAAE,ufAAuf;IAC/f,MAAM,EAAE,ycAAyc;IACjd,MAAM,EAAE,qTAAqT;IAC7T,MAAM,EAAE,+FAA+F;IACvG,MAAM,EAAE,gWAAgW;IACxW,MAAM,EAAE,oOAAoO;IAC5O,MAAM,EAAE,uQAAuQ;IAC/Q,MAAM,EAAE,sYAAsY;IAC9Y,MAAM,EAAE,gWAAgW;IACxW,MAAM,EAAE,kpBAAkpB;IAC1pB,MAAM,EAAE,6RAA6R;IACrS,MAAM,EAAE,2QAA2Q;IACnR,MAAM,EAAE,weAAwe;IAChf,MAAM,EAAE,ogBAAogB;IAC5gB,MAAM,EAAE,iiBAAiiB;IACziB,MAAM,EAAE,4eAA4e;IACpf,MAAM,EAAE,6VAA6V;IACrW,MAAM,EAAE,oeAAoe;IAC5e,MAAM,EAAE,uZAAuZ;IAC/Z,MAAM,EAAE,uZAAuZ;IAC/Z,MAAM,EAAE,6XAA6X;IACrY,MAAM,EAAE,4gBAA4gB;IACphB,MAAM,EAAE,8jBAA8jB;IACtkB,MAAM,EAAE,6WAA6W;IACrX,MAAM,EAAE,kVAAkV;IAC1V,MAAM,EAAE,uWAAuW;IAC/W,MAAM,EAAE,0RAA0R;IAClS,MAAM,EAAE,uiBAAuiB;IAC/iB,MAAM,EAAE,iXAAiX;IACzX,MAAM,EAAE,imBAAimB;IACzmB,MAAM,EAAE,geAAge;IACxe,MAAM,EAAE,oVAAoV;IAC5V,MAAM,EAAE,oJAAoJ;IAC5J,MAAM,EAAE,4HAA4H;IACpI,MAAM,EAAE,4gBAA4gB;IACphB,MAAM,EAAE,kWAAkW;IAC1W,MAAM,EAAE,ggBAAggB;IACxgB,MAAM,EAAE,maAAma;IAC3a,MAAM,EAAE,sXAAsX;IAC9X,MAAM,EAAE,sMAAsM;IAC9M,MAAM,EAAE,+HAA+H;IACvI,MAAM,EAAE,wPAAwP;IAChQ,MAAM,EAAE,4JAA4J;IACpK,MAAM,EAAE,uQAAuQ;IAC/Q,MAAM,EAAE,sSAAsS;IAC9S,MAAM,EAAE,4FAA4F;IACpG,MAAM,EAAE,gcAAgc;IACxc,MAAM,EAAE,iZAAiZ;IACzZ,MAAM,EAAE,qSAAqS;IAC7S,MAAM,EAAE,0SAA0S;IAClT,MAAM,EAAE,wUAAwU;IAChV,MAAM,EAAE,iMAAiM;IACzM,MAAM,EAAE,yIAAyI;IACjJ,MAAM,EAAE,2IAA2I;IACnJ,MAAM,EAAE,mKAAmK;IAC3K,MAAM,EAAE,8SAA8S;IACtT,MAAM,EAAE,6PAA6P;IACrQ,MAAM,EAAE,8NAA8N;IACtO,MAAM,EAAE,wKAAwK;IAChL,MAAM,EAAE,qPAAqP;IAC7P,MAAM,EAAE,qPAAqP;IAC7P,MAAM,EAAE,oRAAoR;IAC5R,MAAM,EAAE,6TAA6T;IACrU,MAAM,EAAE,6TAA6T;IACrU,MAAM,EAAE,2OAA2O;IACnP,MAAM,EAAE,wQAAwQ;IAChR,MAAM,EAAE,uaAAua;IAC/a,MAAM,EAAE,+hBAA+hB;IACviB,MAAM,EAAE,goBAAgoB;IACxoB,MAAM,EAAE,qjBAAqjB;IAC7jB,MAAM,EAAE,iVAAiV;IACzV,MAAM,EAAE,4ZAA4Z;IACpa,MAAM,EAAE,20BAA20B;IACn1B,MAAM,EAAE,+FAA+F;IACvG,MAAM,EAAE,8NAA8N;IACtO,MAAM,EAAE,wKAAwK;IAChL,MAAM,EAAE,2bAA2b;IACnc,MAAM,EAAE,2YAA2Y;IACnZ,MAAM,EAAE,kqBAAkqB;IAC1qB,MAAM,EAAE,uoBAAuoB;IAC/oB,MAAM,EAAE,knBAAknB;IAC1nB,MAAM,EAAE,m7BAAm7B;IAC37B,MAAM,EAAE,0dAA0d;IACle,MAAM,EAAE,mLAAmL;IAC3L,MAAM,EAAE,oaAAoa;IAC5a,MAAM,EAAE,sdAAsd;IAC9d,MAAM,EAAE,4eAA4e;IACpf,MAAM,EAAE,8dAA8d;IACte,MAAM,EAAE,8NAA8N;IACtO,MAAM,EAAE,wKAAwK;CACnL,EAAC;IACE,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,oBAAoB;IAC5B,MAAM,EAAE,0BAA0B;IAClC,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;IACtB,MAAM,EAAE,cAAc;CACzB,CAAC,CAAC"}