{"name": "@vueuse/integrations", "type": "module", "version": "13.5.0", "description": "Integration wrappers for utility libraries", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/integrations#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/integrations"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use", "utils"], "sideEffects": false, "exports": {".": "./index.mjs", "./*": "./*", "./useAsyncValidator": "./useAsyncValidator.mjs", "./useAxios": "./useAxios.mjs", "./useCookies": "./useCookies.mjs", "./useDrauu": "./useDrauu.mjs", "./useFocusTrap": "./useFocusTrap.mjs", "./useFocusTrap/component": "./useFocusTrap/component.mjs", "./useFuse": "./useFuse.mjs", "./useJwt": "./useJwt.mjs", "./useNProgress": "./useNProgress.mjs", "./useQRCode": "./useQRCode.mjs", "./useChangeCase": "./useChangeCase.mjs", "./useAsyncValidator/component": "./useAsyncValidator/component.mjs", "./useIDBKeyval": "./useIDBKeyval.mjs", "./useSortable": "./useSortable.mjs", "./useSortable/component": "./useSortable/component.mjs"}, "main": "./index.mjs", "module": "./index.mjs", "unpkg": "./index.iife.min.js", "jsdelivr": "./index.iife.min.js", "types": "./index.d.mts", "files": ["**/*.d.mts", "**/*.js", "**/*.mjs"], "peerDependencies": {"async-validator": "^4", "axios": "^1", "change-case": "^5", "drauu": "^0.4", "focus-trap": "^7", "fuse.js": "^7", "idb-keyval": "^6", "jwt-decode": "^4", "nprogress": "^0.2", "qrcode": "^1.5", "sortablejs": "^1", "universal-cookie": "^7 || ^8", "vue": "^3.5.0"}, "peerDependenciesMeta": {"async-validator": {"optional": true}, "axios": {"optional": true}, "change-case": {"optional": true}, "drauu": {"optional": true}, "focus-trap": {"optional": true}, "fuse.js": {"optional": true}, "idb-keyval": {"optional": true}, "jwt-decode": {"optional": true}, "nprogress": {"optional": true}, "qrcode": {"optional": true}, "sortablejs": {"optional": true}, "universal-cookie": {"optional": true}}, "dependencies": {"@vueuse/core": "13.5.0", "@vueuse/shared": "13.5.0"}, "devDependencies": {"@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/sortablejs": "^1.15.8", "async-validator": "^4.2.5", "axios": "^1.10.0", "change-case": "^5.4.4", "drauu": "^0.4.3", "focus-trap": "^7.6.5", "fuse.js": "^7.1.0", "idb-keyval": "^6.2.2", "jwt-decode": "^4.0.0", "nprogress": "^0.2.0", "qrcode": "^1.5.4", "sortablejs": "^1.15.6", "universal-cookie": "^8.0.1"}, "scripts": {"build": "rollup --config=rollup.config.ts --configPlugin=rollup-plugin-esbuild", "test:attw": "attw --pack --config-path ../../.attw.json ."}}