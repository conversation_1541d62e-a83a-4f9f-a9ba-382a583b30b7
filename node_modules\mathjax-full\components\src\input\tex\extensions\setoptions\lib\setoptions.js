import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/setoptions/SetOptionsConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/setoptions', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      setoptions: {
        SetOptionsConfiguration: module1
      }
    }
  }
}});
