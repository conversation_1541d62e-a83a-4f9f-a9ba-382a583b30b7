{"version": 3, "sources": ["../../mathjax-full/ts/core/DOMAdaptor.ts", "../../mathjax-full/ts/adaptors/NodeMixin.ts", "../../mathjax-full/ts/adaptors/lite/Element.ts", "../../mathjax-full/ts/adaptors/lite/Document.ts", "../../mathjax-full/ts/adaptors/lite/Text.ts", "../../mathjax-full/ts/adaptors/lite/List.ts", "../../mathjax-full/ts/adaptors/lite/Parser.ts", "../../mathjax-full/ts/adaptors/lite/Window.ts", "../../mathjax-full/ts/adaptors/liteAdaptor.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  The DOMAdaptor interface and abstract class\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {OptionList} from '../util/Options.js';\n\n/**\n * The data for an attribute\n */\nexport type AttributeData = {\n  name: string,\n  value: string\n};\n\n/**\n * The data for an elements page-based bounding box\n */\nexport type PageBBox = {\n  left: number,\n  right: number,\n  top: number,\n  bottom: number\n};\n\n\n/*****************************************************************/\n/**\n *  The interface for the DOMAdaptor\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface DOMAdaptor<N, T, D> {\n  /**\n   * Document in which the nodes are to be created\n   */\n  document: D;\n\n  /**\n   * @param {string} text    The serialized document to be parsed\n   * @param {string} format  The format (e.g., 'text/html' or 'text/xhtml')\n   * @return {D}             The parsed document\n   */\n  parse(text: string, format?: string): D;\n\n  /**\n   * @param {string} kind      The tag name of the HTML node to be created\n   * @param {OptionList} def   The properties to set for the created node\n   * @param {(N|T)[]} children The child nodes for the created HTML node\n   * @param {string} ns        The namespace in which to create the node\n   * @return {N}               The generated HTML tree\n   */\n  node(kind: string, def?: OptionList, children?: (N | T)[], ns?: string): N;\n\n  /**\n   * @param {string} text   The text from which to create an HTML text node\n   * @return {T}            The generated text node with the given text\n   */\n  text(text: string): T;\n\n  /**\n   * @param {D} doc   The document whose head is to be obtained\n   * @return {N}      The document.head element\n   */\n  head(doc: D): N;\n\n  /**\n   * @param {D} doc   The document whose body is to be obtained\n   * @return {N}      The document.body element\n   */\n  body(doc: D): N;\n\n  /**\n   * @param {D} doc   The document whose documentElement is to be obtained\n   * @return {N}      The documentElement\n   */\n  root(doc: D): N;\n\n  /**\n   * @param {D} doc     The document whose doctype is to be obtained\n   * @return {string}   The DOCTYPE comment\n   */\n  doctype(doc: D): string;\n\n  /**\n   * @param {N} node        The node to search for tags\n   * @param {string} name   The name of the tag to search for\n   * @param {string} ns     The namespace to search in (or null for no namespace)\n   * @return {N[]}          The list of tags found\n   */\n  tags(node: N, name: string, ns?: string): N[];\n\n  /**\n   * Get a list of containers (to be searched for math).  These can be\n   *  specified by CSS selector, or as actual DOM elements or arrays of such.\n   *\n   * @param {(string | N | N[])[]} nodes  The array of items to make into a container list\n   * @param {D} document                  The document in which to search\n   * @return {N[]}                        The array of containers to search\n   */\n  getElements(nodes: (string | N | N[])[], document: D): N[];\n\n  /**\n   * Determine if a container node contains a given node somewhere in its DOM tree\n   *\n   * @param {N} container  The container to search\n   * @param {N|T} node     The node to look for\n   * @return {boolean}     True if the node is in the container's DOM tree\n   */\n  contains(container: N, node: N | T): boolean;\n\n  /**\n   * @param {N|T} node  The HTML node whose parent is to be obtained\n   * @return {N}        The parent node of the given one\n   */\n  parent(node: N | T): N;\n\n  /**\n   * @param {N} node     The HTML node to be appended to\n   * @param {N|T} child  The node or text to be appended\n   * @return {N|T}       The appended node\n   */\n  append(node: N, child: N | T): N | T;\n\n  /**\n   * @param {N|T} nchild  The node or text to be inserted\n   * @param {N|T} ochild  The node or text where the new child is to be added before it\n   */\n  insert(nchild: N | T, ochild: N | T): void;\n\n  /**\n   * @param {N|T} child  The node or text to be removed from its parent\n   * @return {N|T}       The removed node\n   */\n  remove(child: N | T): N | T;\n\n  /**\n   * @param {N|T} nnode  The node to replace with\n   * @param {N|T} onode  The child to be replaced\n   * @return {N|T}       The removed node\n   */\n  replace(nnode: N | T, onode: N | T): N | T;\n\n  /**\n   * @param {N} node   The HTML node to be cloned\n   * @return {N}       The copied node\n   */\n  clone(node: N): N;\n\n  /**\n   * @param {T} node    The HTML text node to be split\n   * @param {number} n  The index of the character where the split will occur\n   */\n  split(node: T, n: number): T;\n\n  /**\n   * @param {N|T} node   The HTML node whose sibling is to be obtained\n   * @return {N|T}       The node following the given one (or null)\n   */\n  next(node: N | T): N | T;\n\n  /**\n   * @param {N|T} node   The HTML node whose sibling is to be obtained\n   * @return {N|T}       The node preceding the given one (or null)\n   */\n  previous(node: N | T): N | T;\n\n  /**\n   * @param {N} node   The HTML node whose child is to be obtained\n   * @return {N|T}     The first child of the given node (or null)\n   */\n  firstChild(node: N): N | T;\n\n  /**\n   * @param {N} node   The HTML node whose child is to be obtained\n   * @return {N}       The last child of the given node (or null)\n   */\n  lastChild(node: N): N | T;\n\n  /**\n   * @param {N} node    The HTML node whose children are to be obtained\n   * @return {(N|T)[]}  Array of children for the given node (not a live list)\n   */\n  childNodes(node: N): (N | T)[];\n\n  /**\n   * @param {N} node    The HTML node whose child is to be obtained\n   * @param {number} i  The index of the child to return\n   * @return {N|T}      The i-th child node of the given node (or null)\n   */\n  childNode(node: N, i: number): N | T;\n\n  /**\n   * @param {N | T} node   The HTML node whose tag or node name is to be obtained\n   * @return {string}      The tag or node name of the given node\n   */\n  kind(node: N | T): string;\n\n  /**\n   * @param {N|T} node  The HTML node whose value is to be obtained\n   * @return {string}   The value of the given node\n   */\n  value(node: N | T): string;\n\n  /**\n   * @param {N} node    The HTML node whose text content is to be obtained\n   * @return {string}   The text content of the given node\n   */\n  textContent(node: N): string;\n\n  /**\n   * @param {N} node   The HTML node whose inner HTML string is to be obtained\n   * @return {string}  The serialized content of the node\n   */\n  innerHTML(node: N): string;\n\n  /**\n   * @param {N} node   The HTML node whose outer HTML string is to be obtained\n   * @return {string}  The serialized node and its content\n   */\n  outerHTML(node: N): string;\n\n  /**\n   * @param {N} node   The HTML node whose serialized string is to be obtained\n   * @return {string}  The serialized node and its content\n   */\n  serializeXML(node: N): string;\n\n  /**\n   * @param {N} node               The HTML node whose attribute is to be set\n   * @param {string|number} name   The name of the attribute to set\n   * @param {string} value         The new value of the attribute\n   * @param {string=} ns           The namespace to use for the attribute\n   */\n  setAttribute(node: N, name: string, value: string | number, ns?: string): void;\n\n  /**\n   * @param {N} node           The HTML element whose attributes are to be set\n   * @param {OptionList} def   The attributes to set on that node\n   */\n  setAttributes(node: N, def: OptionList): void;\n\n  /**\n   * @param {N} node        The HTML node whose attribute is to be obtained\n   * @param {string} name   The name of the attribute to get\n   * @return {string}       The value of the given attribute of the given node\n   */\n  getAttribute(node: N, name: string): string;\n\n  /**\n   * @param {N} node        The HTML node whose attribute is to be removed\n   * @param {string} name   The name of the attribute to remove\n   */\n  removeAttribute(node: N, name: string): void;\n\n  /**\n   * @param {N} node        The HTML node whose attribute is to be tested\n   * @param {string} name   The name of the attribute to test\n   * @return {boolean}      True of the node has the given attribute defined\n   */\n  hasAttribute(node: N, name: string): boolean;\n\n  /**\n   * @param {N} node           The HTML node whose attributes are to be returned\n   * @return {AttributeData[]} The list of attributes\n   */\n  allAttributes(node: N): AttributeData[];\n\n  /**\n   * @param {N} node        The HTML node whose class is to be augmented\n   * @param {string} name   The class to be added\n   */\n  addClass(node: N, name: string): void;\n\n  /**\n   * @param {N} node        The HTML node whose class is to be changed\n   * @param {string} name   The class to be removed\n   */\n  removeClass(node: N, name: string): void;\n\n  /**\n   * @param {N} node        The HTML node whose class is to be tested\n   * @param {string} name   The class to test\n   * @return {boolean}      True if the node has the given class\n   */\n  hasClass(node: N, name: string): boolean;\n\n  /**\n   * @param {N} node        The HTML node whose class list is needed\n   * @return {string[]}     An array of the class names for this node\n   */\n  allClasses(node: N): string[];\n\n  /**\n   * @param {N} node        The HTML node whose style is to be changed\n   * @param {string} name   The style to be set\n   * @param {string} value  The new value of the style\n   */\n  setStyle(node: N, name: string, value: string): void;\n\n  /**\n   * @param {N} node        The HTML node whose style is to be obtained\n   * @param {string} name   The style to be obtained\n   * @return {string}       The value of the style\n   */\n  getStyle(node: N, name: string): string;\n\n  /**\n   * @param {N} node        The HTML node whose styles are to be returned\n   * @return {string}       The cssText for the styles\n   */\n  allStyles(node: N): string;\n\n  /**\n   * @param {N} node           The stylesheet node where the rule will be added\n   * @param {string[]} rules   The rule to add at the beginning of the stylesheet\n   */\n  insertRules(node: N, rules: string[]): void;\n\n  /**\n   * @param {N} node        The HTML node whose font size is to be determined\n   * @return {number}       The font size (in pixels) of the node\n   */\n  fontSize(node: N): number;\n\n  /**\n   * @param {N} node        The HTML node whose font family is to be determined\n   * @return {string}       The font family\n   */\n  fontFamily(node: N): string;\n\n  /**\n   * @param {N} node            The HTML node whose dimensions are to be determined\n   * @param {number} em         The number of pixels in an em\n   * @param {boolean} local     True if local coordinates are to be used in SVG elements\n   * @return {[number, number]} The width and height (in ems) of the element\n   */\n  nodeSize(node: N, em?: number, local?: boolean): [number, number];\n\n\n  /**\n   * @param {N} node            The HTML node whose BBox is to be determined\n   * @return {PageBBox}         BBox as {left, right, top, bottom} position on the page (in pixels)\n   */\n  nodeBBox(node: N): PageBBox;\n}\n\n/*****************************************************************/\n/**\n *  Abstract DOMAdaptor class for creating HTML elements\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractDOMAdaptor<N, T, D> implements DOMAdaptor<N, T, D> {\n\n  /**\n   * The document in which the HTML nodes will be created\n   */\n  public document: D;\n\n  /**\n   * @param {D} document  The document in which the nodes will be created\n   * @constructor\n   */\n  constructor(document: D = null) {\n    this.document = document;\n  }\n\n  /**\n   * @override\n   */\n  public abstract parse(text: string, format?: string): D;\n\n  /**\n   * @override\n   */\n  public node(kind: string, def: OptionList = {}, children: (N | T)[] = [], ns?: string) {\n    const node = this.create(kind, ns);\n    this.setAttributes(node, def);\n    for (const child of children) {\n      this.append(node, child);\n    }\n    return node as N;\n  }\n\n  /**\n   * @param {string} kind  The type of the node to create\n   * @param {string} ns    The optional namespace in which to create the node\n   * @return {N}           The created node\n   */\n  protected abstract create(kind: string, ns?: string): N;\n\n  /**\n   * @override\n   */\n  public abstract text(text: string): T;\n\n  /**\n   * @param {N} node           The HTML element whose attributes are to be set\n   * @param {OptionList} def   The attributes to set on that node\n   */\n  public setAttributes(node: N, def: OptionList) {\n    if (def.style && typeof(def.style) !== 'string') {\n      for (let key of Object.keys(def.style)) {\n        this.setStyle(node, key.replace(/-([a-z])/g, (_m, c) => c.toUpperCase()), def.style[key]);\n      }\n    }\n    if (def.properties) {\n      for (let key of Object.keys(def.properties)) {\n        (node as OptionList)[key] = def.properties[key];\n      }\n    }\n    for (let key of Object.keys(def)) {\n      if ((key !== 'style' || typeof(def.style) === 'string') && key !== 'properties') {\n        this.setAttribute(node, key, def[key]);\n      }\n    }\n  }\n\n  /**\n   * @override\n   */\n  public abstract head(doc: D): N;\n\n  /**\n   * @override\n   */\n  public abstract body(doc: D): N;\n\n  /**\n   * @override\n   */\n  public abstract root(doc: D): N;\n\n  /**\n   * @override\n   */\n  public abstract doctype(doc: D): string;\n\n  /**\n   * @override\n   */\n  public abstract tags(node: N, name: string, ns?: string): N[];\n\n  /**\n   * @override\n   */\n  public abstract getElements(nodes: (string | N | N[])[], document: D): N[];\n\n  /**\n   * @override\n   */\n  public abstract contains(container: N, node: N | T): boolean;\n\n  /**\n   * @override\n   */\n  public abstract parent(node: N | T): N;\n\n  /**\n   * @override\n   */\n  public abstract append(node: N, child: N | T): N | T;\n\n  /**\n   * @override\n   */\n  public abstract insert(nchild: N | T, ochild: N | T): void;\n\n  /**\n   * @override\n   */\n  public abstract remove(child: N | T): N | T;\n\n  /**\n   * @override\n   */\n  public replace(nnode: N | T, onode: N | T) {\n    this.insert(nnode, onode);\n    this.remove(onode);\n    return onode;\n  }\n\n  /**\n   * @override\n   */\n  public abstract clone(node: N):  N;\n\n  /**\n   * @override\n   */\n  public abstract split(node: T, n: number): T;\n\n  /**\n   * @override\n   */\n  public abstract next(node: N | T): N | T;\n\n  /**\n   * @override\n   */\n  public abstract previous(node: N | T): N | T;\n\n  /**\n   * @override\n   */\n  public abstract firstChild(node: N): N | T;\n\n  /**\n   * @override\n   */\n  public abstract lastChild(node: N): N | T;\n\n  /**\n   * @override\n   */\n  public abstract childNodes(node: N): (N | T)[];\n\n  /**\n   * @override\n   */\n  public childNode(node: N, i: number) {\n    return this.childNodes(node)[i];\n  }\n\n  /**\n   * @override\n   */\n  public abstract kind(node: N | T): string;\n\n  /**\n   * @override\n   */\n  public abstract value(node: N | T): string;\n\n  /**\n   * @override\n   */\n  public abstract textContent(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract innerHTML(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract outerHTML(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract serializeXML(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract setAttribute(node: N, name: string, value: string, ns?: string): void;\n\n  /**\n   * @override\n   */\n  public abstract getAttribute(node: N, name: string): string;\n\n  /**\n   * @override\n   */\n  public abstract removeAttribute(node: N, name: string): void;\n\n  /**\n   * @override\n   */\n  public abstract hasAttribute(node: N, name: string): boolean;\n\n\n  /**\n   * @override\n   */\n  public abstract allAttributes(node: N): AttributeData[];\n\n  /**\n   * @override\n   */\n  public abstract addClass(node: N, name: string): void;\n\n  /**\n   * @override\n   */\n  public abstract removeClass(node: N, name: string): void;\n\n  /**\n   * @override\n   */\n  public abstract hasClass(node: N, name: string): boolean;\n\n  /**\n   * @override\n   */\n  public allClasses(node: N) {\n    const classes = this.getAttribute(node, 'class');\n    return (!classes ? [] as string[] :\n            classes.replace(/  +/g, ' ').replace(/^ /, '').replace(/ $/, '').split(/ /));\n  }\n\n  /**\n   * @override\n   */\n  public abstract setStyle(node: N, name: string, value: string): void;\n\n  /**\n   * @override\n   */\n  public abstract getStyle(node: N, name: string): string;\n\n  /**\n   * @override\n   */\n  public abstract allStyles(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract insertRules(node: N, rules: string[]): void;\n\n  /**\n   * @override\n   */\n  public abstract fontSize(node: N): number;\n\n  /**\n   * @override\n   */\n  public abstract fontFamily(node: N): string;\n\n  /**\n   * @override\n   */\n  public abstract nodeSize(node: N, em?: number, local?: boolean): [number, number];\n\n  /**\n   * @override\n   */\n  public abstract nodeBBox(node: N): PageBBox;\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2022-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a mixin for node-based adaptors that overrides\n *                the methods that obtain DOM node sizes, when those aren't\n *                available from the DOM itself.\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {DOMAdaptor} from '../core/DOMAdaptor.js';\nimport {userOptions, defaultOptions, OptionList} from '../util/Options.js';\n\n/**\n * A constructor for a given class\n *\n * @template T   The class to construct\n */\nexport type Constructor<T> = (new(...args: any[]) => T);\n\n/**\n * The type of an Adaptor class\n */\nexport type AdaptorConstructor<N, T, D> = Constructor<DOMAdaptor<N, T, D>>;\n\n/**\n * The options to the NodeMixin\n */\nexport const NodeMixinOptions: OptionList = {\n  badCSS: true,     // getComputedStyles() is not implemented in the DOM\n  badSizes: true,   // element sizes (e.g., ClientWidth, etc.) are not implemented in the DOM\n};\n\n/**\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport function NodeMixin<N, T, D, A extends AdaptorConstructor<N, T, D>>(\n  Base: A,\n  options: typeof NodeMixinOptions = {}\n): A {\n\n  options = userOptions(defaultOptions({}, NodeMixinOptions), options);\n\n  return class NodeAdaptor extends Base {\n\n    /**\n     * The default options\n     */\n    public static OPTIONS: OptionList = {\n      ...(options.badCSS ? {\n        fontSize: 16,          // We can't compute the font size, so always use this\n        fontFamily: 'Times',   // We can't compute the font family, so always use this\n      } : {}),\n      ...(options.badSizes ? {\n        cjkCharWidth: 1,       // Width (in em units) of full width characters\n        unknownCharWidth: .6,  // Width (in em units) of unknown (non-full-width) characters\n        unknownCharHeight: .8, // Height (in em units) of unknown characters\n      } : {})\n    };\n\n    /**\n     * Pattern to identify CJK (i.e., full-width) characters\n     */\n    public static cjkPattern = new RegExp([\n      '[',\n      '\\u1100-\\u115F', // Hangul Jamo\n      '\\u2329\\u232A',  // LEFT-POINTING ANGLE BRACKET, RIGHT-POINTING ANGLE BRACKET\n      '\\u2E80-\\u303E', // CJK Radicals Supplement ... CJK Symbols and Punctuation\n      '\\u3040-\\u3247', // Hiragana ... Enclosed CJK Letters and Months\n      '\\u3250-\\u4DBF', // Enclosed CJK Letters and Months ... CJK Unified Ideographs Extension A\n      '\\u4E00-\\uA4C6', // CJK Unified Ideographs ... Yi Radicals\n      '\\uA960-\\uA97C', // Hangul Jamo Extended-A\n      '\\uAC00-\\uD7A3', // Hangul Syllables\n      '\\uF900-\\uFAFF', // CJK Compatibility Ideographs\n      '\\uFE10-\\uFE19', // Vertical Forms\n      '\\uFE30-\\uFE6B', // CJK Compatibility Forms ... Small Form Variants\n      '\\uFF01-\\uFF60\\uFFE0-\\uFFE6', // Halfwidth and Fullwidth Forms\n      '\\u{1B000}-\\u{1B001}', // Kana Supplement\n      '\\u{1F200}-\\u{1F251}', // Enclosed Ideographic Supplement\n      '\\u{20000}-\\u{3FFFD}', // CJK Unified Ideographs Extension B ... Tertiary Ideographic Plane\n      ']'\n    ].join(''), 'gu');\n\n    /**\n     * The options for the instance\n     */\n    public options: OptionList;\n\n    /**\n     * @param {any} window          The window to work with\n     * @param {OptionList} options  The options for the adaptor\n     * @constructor\n     */\n    constructor(...args: any[]) {\n      super(args[0]);\n      let CLASS = this.constructor as typeof NodeAdaptor;\n      this.options = userOptions(defaultOptions({}, CLASS.OPTIONS), args[1]);\n    }\n\n    /**\n     * For DOMs that don't handle CSS well, use the font size from the options\n     *\n     * @override\n     */\n    public fontSize(node: N) {\n      return (options.badCSS ? this.options.fontSize : super.fontSize(node));\n    }\n\n    /**\n     * For DOMs that don't handle CSS well, use the font family from the options\n     *\n     * @override\n     */\n    public fontFamily(node: N) {\n      return (options.badCSS ? this.options.fontFamily : super.fontFamily(node));\n    }\n\n    /**\n     * @override\n     */\n    public nodeSize(node: N, em: number = 1, local: boolean = null) {\n      if (!options.badSizes) {\n        return super.nodeSize(node, em, local);\n      }\n      const text = this.textContent(node);\n      const non = Array.from(text.replace(NodeAdaptor.cjkPattern, '')).length;  // # of non-CJK chars\n      const CJK = Array.from(text).length - non;                                // # of cjk chars\n      return [\n        CJK * this.options.cjkCharWidth + non * this.options.unknownCharWidth,\n        this.options.unknownCharHeight\n      ] as [number, number];\n    }\n\n    /**\n     * @override\n     */\n    public nodeBBox(node: N) {\n      return (options.badSizes ? {left: 0, right: 0, top: 0, bottom: 0} : super.nodeBBox(node));\n    }\n\n  };\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight HTML Element replacement\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {OptionList} from '../../util/Options.js';\nimport {Styles} from '../../util/Styles.js';\nimport {LiteText} from './Text.js';\n\n/**\n * Type for attribute lists\n */\nexport type LiteAttributeList = OptionList;\n\n/**\n * Type for generic nodes in LiteAdaptor\n */\nexport type LiteNode = LiteElement | LiteText;\n\n\n/************************************************************/\n/**\n * Implements a lightweight HTML element replacement\n */\nexport class LiteElement {\n  /**\n   * The type of element (tag name)\n   */\n  public kind: string;\n\n  /**\n   * The element's attribute list\n   */\n  public attributes: LiteAttributeList;\n\n  /**\n   * The element's children\n   */\n  public children: LiteNode[];\n\n  /**\n   * The element's parent\n   */\n  public parent: LiteElement;\n\n  /**\n   * The styles for the element\n   */\n  public styles: Styles;\n\n  /**\n   * @param {string} kind  The type of node to create\n   * @param {LiteAttributeList} attributes  The list of attributes to set (if any)\n   * @param {LiteNode[]} children  The children for the node (if any)\n   * @constructor\n   */\n  constructor(kind: string, attributes: LiteAttributeList = {}, children: LiteNode[] = []) {\n    this.kind = kind;\n    this.attributes = {...attributes};\n    this.children = [...children];\n    for (const child of this.children) {\n      child.parent = this;\n    }\n    this.styles = null;\n  }\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight DOM adaptor\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {LiteElement} from './Element.js';\n\n/************************************************************/\n/**\n * Implements a lightweight Document replacement\n */\nexport class LiteDocument {\n  /**\n   * The document's <html> element\n   */\n  public root: LiteElement;\n  /**\n   * The document's <head> element\n   */\n  public head: LiteElement;\n  /**\n   * The document's <body> element\n   */\n  public body: LiteElement;\n\n  /**\n   * the DOCTYPE comment\n   */\n  public type: string;\n\n  /**\n   * The kind is always #document\n   */\n  public get kind() {\n    return '#document';\n  }\n\n  /**\n   * @constructor\n   */\n  constructor() {\n    this.root = new LiteElement('html', {}, [\n      this.head = new LiteElement('head'),\n      this.body = new LiteElement('body')\n    ]);\n    this.type = '';\n  }\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight Text element replacement\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {LiteElement} from './Element.js';\n\n/************************************************************/\n/**\n * Implements a lightweight Text node replacement\n */\nexport class LiteText {\n  /**\n   * The text stored in the node\n   */\n  public value: string;\n\n  /**\n   * The parent holding this text\n   */\n  public parent: LiteElement;\n\n  /**\n   * The kind of node is #text\n   */\n  public get kind() {\n    return '#text';\n  }\n\n  /**\n   * @param {string} text  The text for the node\n   * @constructor\n   */\n  constructor(text: string = '') {\n    this.value = text;\n  }\n}\n\n/************************************************************/\n/**\n * Implements a lightweight Comment node replacement\n */\nexport class LiteComment extends LiteText {\n  public get kind() {\n    return '#comment';\n  }\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight DOM adaptor\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {LiteNode} from './Element.js';\n\n/************************************************************/\n/**\n * Implements a lightweight DocumentFragment or NodeList replacement\n *\n * @template N  The HTMLElement node class\n */\nexport class LiteList<N> {\n  /**\n   * The nodes held in the fragment\n   */\n  public nodes: N[] = [];\n\n  /**\n   * @param {N[]} children  The children for the fragment\n   * @constructor\n   */\n  constructor(children: N[]) {\n    this.nodes = [...children];\n  }\n\n  /**\n   * @param {N} node  A node to append to the fragment\n   */\n  public append(node: N) {\n    this.nodes.push(node);\n  }\n\n  /**\n   * Make this class iterable (so it can be used with Array.from())\n   */\n  public [Symbol.iterator](): Iterator<LiteNode> {\n    let i = 0;\n    return {\n      /**\n       * @return {IteratorResult<LiteNode>}\n       */\n      next(): IteratorResult<LiteNode> {\n        return (i === this.nodes.length ?\n                {value: null, done: true} :\n                {value: this.nodes[i++], done: false});\n      }\n    };\n  }\n\n}\n", "\n/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight DOM adaptor\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AttributeData} from '../../core/DOMAdaptor.js';\nimport {MinDOMParser} from '../HTMLAdaptor.js';\nimport * as Entities from '../../util/Entities.js';\nimport {LiteDocument} from './Document.js';\nimport {LiteElement} from './Element.js';\nimport {LiteText, LiteComment} from './Text.js';\nimport {LiteAdaptor} from '../liteAdaptor.js';\n\n/**\n * Patterns used in parsing serialized HTML\n */\nexport namespace PATTERNS {\n  export const TAGNAME = '[a-z][^\\\\s\\\\n>]*';\n  export const ATTNAME = '[a-z][^\\\\s\\\\n>=]*';\n  export const VALUE =  `(?:'[^']*'|\"[^\"]*\"|[^\\\\s\\\\n]+)`;\n  export const VALUESPLIT =  `(?:'([^']*)'|\"([^\"]*)\"|([^\\\\s\\\\n]+))`;\n  export const SPACE = '(?:\\\\s|\\\\n)+';\n  export const OPTIONALSPACE = '(?:\\\\s|\\\\n)*';\n  export const ATTRIBUTE = ATTNAME + '(?:' + OPTIONALSPACE + '=' + OPTIONALSPACE + VALUE + ')?';\n  export const ATTRIBUTESPLIT = '(' + ATTNAME + ')(?:' + OPTIONALSPACE + '=' + OPTIONALSPACE + VALUESPLIT + ')?';\n  export const TAG = '(<(?:' + TAGNAME + '(?:' + SPACE + ATTRIBUTE + ')*'\n                       + OPTIONALSPACE + '/?|/' + TAGNAME + '|!--[^]*?--|![^]*?)(?:>|$))';\n  export const tag = new RegExp(TAG, 'i');\n  export const attr = new RegExp(ATTRIBUTE, 'i');\n  export const attrsplit = new RegExp(ATTRIBUTESPLIT, 'i');\n}\n\n/************************************************************/\n/**\n * Implements a lightweight DOMParser replacement\n * (Not perfect, but handles most well-formed HTML)\n */\nexport class LiteParser implements MinDOMParser<LiteDocument> {\n\n  /**\n   * The list of self-closing tags\n   */\n  public static SELF_CLOSING: {[name: string]: boolean} = {\n    area: true,\n    base: true,\n    br: true,\n    col: true,\n    command: true,\n    embed: true,\n    hr: true,\n    img: true,\n    input: true,\n    keygen: true,\n    link: true,\n    menuitem: true,\n    meta: true,\n    param: true,\n    source: true,\n    track: true,\n    wbr: true\n  };\n\n  /**\n   * The list of tags chose content is not parsed (PCDATA)\n   */\n  public static PCDATA: {[name: string]: boolean} = {\n    option: true,\n    textarea: true,\n    fieldset: true,\n    title: true,\n    style: true,\n    script: true\n  };\n\n  /**\n   * The list of attributes that don't get entity translation\n   */\n  public static CDATA_ATTR: {[name: string]: boolean} = {\n    style: true,\n    datafld: true,\n    datasrc: true,\n    href: true,\n    src: true,\n    longdesc: true,\n    usemap: true,\n    cite: true,\n    datetime: true,\n    action: true,\n    axis: true,\n    profile: true,\n    content: true,\n    scheme: true\n  };\n\n  /**\n   * @override\n   */\n  public parseFromString(text: string, _format: string = 'text/html', adaptor: LiteAdaptor = null) {\n    const root = adaptor.createDocument();\n    let node = adaptor.body(root);\n    //\n    // Split the HTML into an array of text, tag, text, tag, ...\n    // Then loop through them and add text nodes and process tags.\n    //\n    let parts = text.replace(/<\\?.*?\\?>/g, '').split(PATTERNS.tag);\n    while (parts.length) {\n      const text = parts.shift();\n      const tag = parts.shift();\n      if (text) {\n        this.addText(adaptor, node, text);\n      }\n      if (tag && tag.charAt(tag.length - 1) === '>') {\n        if (tag.charAt(1) === '!') {\n          this.addComment(adaptor, node, tag);\n        } else if (tag.charAt(1) === '/') {\n          node = this.closeTag(adaptor, node, tag);\n        } else {\n          node = this.openTag(adaptor, node, tag, parts);\n        }\n      }\n    }\n    this.checkDocument(adaptor, root);\n    return root;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node to add a text element to\n   * @param {string} text          The text for the text node\n   * @return {LiteText}            The text element\n   */\n  protected addText(adaptor: LiteAdaptor, node: LiteElement, text: string): LiteText {\n    text = Entities.translate(text);\n    return adaptor.append(node, adaptor.text(text)) as LiteText;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node to add a comment to\n   * @param {string} comment       The text for the comment node\n   * @return {LiteComment}         The comment element\n   */\n  protected addComment(adaptor: LiteAdaptor, node: LiteElement, comment: string): LiteComment {\n    return adaptor.append(node, new LiteComment(comment)) as LiteComment;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node to close\n   * @param {string} tag           The close tag being processed\n   * @return {LiteElement}         The first unclosed parent node\n   */\n  protected closeTag(adaptor: LiteAdaptor, node: LiteElement, tag: string): LiteElement {\n    const kind = tag.slice(2, tag.length - 1).toLowerCase();\n    while (adaptor.parent(node) && adaptor.kind(node) !== kind) {\n      node = adaptor.parent(node);\n    }\n    return adaptor.parent(node);\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The parent node for the tag\n   * @param {string} tag           The tag being processed\n   * @param {string[]} parts       The rest of the text/tag array\n   * @return {LiteElement}         The node to which the next tag will be added\n   */\n  protected openTag(adaptor: LiteAdaptor, node: LiteElement, tag: string, parts: string[]): LiteElement {\n    const PCDATA = (this.constructor as typeof LiteParser).PCDATA;\n    const SELF_CLOSING = (this.constructor as typeof LiteParser).SELF_CLOSING;\n    //\n    // Get the child to be added to the node\n    //\n    const kind = tag.match(/<(.*?)[\\s\\n>\\/]/)[1].toLowerCase();\n    const child = adaptor.node(kind) as LiteElement;\n    //\n    // Split out the tag attributes as an array of space, name, value1, value3, value3,\n    //   where value1, value2, and value3 are the value of the node (only one is defined)\n    //   that come from matching quoted strings with ' (value1), \" (value2) or no quotes (value3).\n    //\n    const attributes = tag.replace(/^<.*?[\\s\\n>]/, '').split(PATTERNS.attrsplit);\n    //\n    // If the tag was complete (it ends with > or has no attributes)\n    //\n    if (attributes.pop().match(/>$/) || attributes.length < 5) {\n      this.addAttributes(adaptor, child, attributes);\n      adaptor.append(node, child);\n      //\n      // For non-self-closing tags,\n      //   For tags whose contents is PCDATA (like <script>), collect the\n      //     content up until the end tag, and continue adding nee tags\n      //     to the current parent node.\n      //   Otherwise, the child tag becames the parent node to which\n      //     new tags are added\n      //\n      if (!SELF_CLOSING[kind] && !tag.match(/\\/>$/)) {\n        if (PCDATA[kind]) {\n          this.handlePCDATA(adaptor, child, kind, parts);\n        } else {\n          node = child;\n        }\n      }\n    }\n    return node;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node getting the attributes\n   * @param {string[]} attributes  The array of space, name, value1, value2, value3\n   *                                as described above.\n   */\n  protected addAttributes(adaptor: LiteAdaptor, node: LiteElement, attributes: string[]) {\n    const CDATA_ATTR = (this.constructor as typeof LiteParser).CDATA_ATTR;\n    while (attributes.length) {\n      let [ , name, v1, v2, v3] = attributes.splice(0, 5);\n      let value = v1 || v2 || v3 || '';\n      if (!CDATA_ATTR[name]) {\n        value = Entities.translate(value);\n      }\n      adaptor.setAttribute(node, name, value);\n    }\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node whose PCDATA content is being collected\n   * @param {string} kind          The tag name being handled\n   * @param {string[]} parts       The array of text/tag data for the document\n   */\n  protected handlePCDATA(adaptor: LiteAdaptor, node: LiteElement, kind: string, parts: string[]) {\n    const pcdata = [] as string[];\n    const etag = '</' + kind + '>';\n    let ptag = '';\n    //\n    //  Look through the parts until the end tag is found\n    //    Add the unmatched tag and the following text\n    //    and try the next tag until we find the end tag.\n    //\n    while (parts.length && ptag !== etag) {\n      pcdata.push(ptag);\n      pcdata.push(parts.shift());\n      ptag = parts.shift();\n    }\n    //\n    //  Add the collected contents as a text node\n    //\n    adaptor.append(node, adaptor.text(pcdata.join('')));\n  }\n\n  /**\n   * Check the contents of the parsed document and move html, head, and body\n   * tags into the document structure.  That way, you can parse fragments or\n   * full documents and still get a valid document.\n   *\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteDocument} root    The document being checked\n   */\n  protected checkDocument(adaptor: LiteAdaptor, root: LiteDocument) {\n    let node = this.getOnlyChild(adaptor, adaptor.body(root));\n    if (!node) return;\n    for (const child of adaptor.childNodes(adaptor.body(root))) {\n      if (child === node) {\n        break;\n      }\n      if (child instanceof LiteComment && child.value.match(/^<!DOCTYPE/)) {\n        root.type = child.value;\n      }\n    }\n    switch (adaptor.kind(node)) {\n    case 'html':\n      //\n      //  Look through the children for the head and body\n      //\n      for (const child of node.children) {\n        switch (adaptor.kind(child)) {\n        case 'head':\n          root.head = child as LiteElement;\n          break;\n        case 'body':\n          root.body = child as LiteElement;\n          break;\n        }\n      }\n      //\n      //  Make sure the elements are linked in properly\n      //\n      root.root = node;\n      adaptor.remove(node);\n      if (adaptor.parent(root.body) !== node) {\n        adaptor.append(node, root.body);\n      }\n      if (adaptor.parent(root.head) !== node) {\n        adaptor.insert(root.head, root.body);\n      }\n      break;\n\n    case 'head':\n      root.head = adaptor.replace(node, root.head) as LiteElement;\n      break;\n\n    case 'body':\n      root.body = adaptor.replace(node, root.body) as LiteElement;\n      break;\n    }\n  }\n\n  /**\n   * Checks if the body has only one element child (as opposed to comments or text nodes)\n   * and returns that sole element (or null if none or more than one)\n   *\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} body     The body element being checked\n   * @return {LiteElement}         The sole LiteElement child of the body, or null if none or more than one\n   */\n  protected getOnlyChild(adaptor: LiteAdaptor, body: LiteElement): LiteElement {\n    let node: LiteElement = null;\n    for (const child of adaptor.childNodes(body)) {\n      if (child instanceof LiteElement) {\n        if (node) return null;\n        node = child;\n      }\n    }\n    return node;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node to serialize\n   * @param {boolean} xml          True when producing XML, false for HTML\n   * @return {string}              The serialized element (like outerHTML)\n   */\n  public serialize(adaptor: LiteAdaptor, node: LiteElement, xml: boolean = false): string {\n    const SELF_CLOSING = (this.constructor as typeof LiteParser).SELF_CLOSING;\n    const CDATA = (this.constructor as typeof LiteParser).CDATA_ATTR;\n    const tag = adaptor.kind(node);\n    const attributes = adaptor.allAttributes(node).map(\n      (x: AttributeData) => x.name + '=\"' + (CDATA[x.name] ? x.value : this.protectAttribute(x.value)) + '\"'\n    ).join(' ');\n    const content = this.serializeInner(adaptor, node, xml);\n    const html =\n      '<' + tag + (attributes ? ' ' + attributes : '')\n          + ((!xml || content) && !SELF_CLOSING[tag] ? `>${content}</${tag}>` : xml ? '/>' : '>');\n    return html;\n  }\n\n  /**\n   * @param {LiteAdaptor} adaptor  The adaptor for managing nodes\n   * @param {LiteElement} node     The node whose innerHTML is needed\n   * @return {string}              The serialized element (like innerHTML)\n   */\n  public serializeInner(adaptor: LiteAdaptor, node: LiteElement, xml: boolean = false): string {\n    const PCDATA = (this.constructor as typeof LiteParser).PCDATA;\n    if (PCDATA.hasOwnProperty(node.kind)) {\n      return adaptor.childNodes(node).map(x => adaptor.value(x)).join('');\n    }\n    return adaptor.childNodes(node).map(x => {\n      const kind = adaptor.kind(x);\n      return (kind === '#text' ? this.protectHTML(adaptor.value(x)) :\n              kind === '#comment' ? (x as LiteComment).value :\n              this.serialize(adaptor, x as LiteElement, xml));\n    }).join('');\n  }\n\n  /**\n   * @param {string} text  The attribute value to be HTML escaped\n   * @return {string}      The string with \" replaced by entities\n   */\n  public protectAttribute(text: string): string {\n    if (typeof text !== 'string') {\n      text = String(text);\n    }\n    return text.replace(/\"/g, '&quot;');\n  }\n\n  /**\n   * @param {string} text  The text to be HTML escaped\n   * @return {string}      The string with &, <, and > replaced by entities\n   */\n  public protectHTML(text: string): string {\n    return text.replace(/&/g, '&amp;')\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight DOM adaptor\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {LiteElement} from './Element.js';\nimport {LiteDocument} from './Document.js';\nimport {LiteList} from './List.js';\nimport {LiteParser} from './Parser.js';\n\n/************************************************************/\n/**\n * Implements a lightweight Window replacement\n */\nexport class LiteWindow {\n  /**\n   * The window's document instance\n   */\n  public document: LiteDocument;\n  /**\n   * The DOMParser class\n   */\n  public DOMParser: typeof LiteParser = LiteParser;\n  /**\n   * The NodeList class\n   */\n  public NodeList: typeof LiteList = LiteList;\n  /**\n   * The HTMLCollection class\n   */\n  public HTMLCollection: typeof LiteList = LiteList;\n  /**\n   * The HTMLElement class\n   */\n  public HTMLElement: typeof LiteElement = LiteElement;\n  /**\n   * The DocumentFragment class\n   */\n  public DocumentFragment: typeof LiteList  = LiteList;\n  /**\n   * The Document class\n   */\n  public Document: typeof LiteDocument  = LiteDocument;\n\n  /**\n   * Create the LiteWindow and its LiteDocument\n   */\n  constructor() {\n    this.document = new LiteDocument();\n  }\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements a lightweight DOM adaptor\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {AbstractDOMAdaptor} from '../core/DOMAdaptor.js';\nimport {NodeMixin, Constructor} from './NodeMixin.js';\nimport {LiteDocument} from './lite/Document.js';\nimport {LiteElement, LiteNode} from './lite/Element.js';\nimport {LiteText, LiteComment} from './lite/Text.js';\nimport {LiteList} from './lite/List.js';\nimport {LiteWindow} from './lite/Window.js';\nimport {LiteParser} from './lite/Parser.js';\nimport {Styles} from '../util/Styles.js';\nimport {OptionList} from '../util/Options.js';\n\n/************************************************************/\n\n\n/**\n * Implements a lightweight DOMAdaptor on liteweight HTML elements\n */\nexport class LiteBase extends AbstractDOMAdaptor<LiteElement, LiteText, LiteDocument> {\n  /**\n   * The document in which the HTML nodes will be created\n   */\n  public document: LiteDocument;\n\n  /**\n   * The window for the document\n   */\n  public window: LiteWindow;\n\n  /**\n   * The parser for serialized HTML\n   */\n  public parser: LiteParser;\n\n  /**\n   * @param {OptionList} options  The options for the lite adaptor (e.g., fontSize)\n   * @constructor\n   */\n  constructor() {\n    super();\n    this.parser = new LiteParser();\n    this.window = new LiteWindow();\n  }\n\n  /**\n   * @override\n   */\n  public parse(text: string, format?: string): LiteDocument {\n    return this.parser.parseFromString(text, format, this);\n  }\n\n  /**\n   * @override\n   */\n  protected create(kind: string, _ns: string = null) {\n    return new LiteElement(kind);\n  }\n\n  /**\n   * @override\n   */\n  public text(text: string) {\n    return new LiteText(text);\n  }\n\n  /**\n   * @param {string} text   The text of the comment\n   * @return {LiteComment}  The comment node\n   */\n  public comment(text: string): LiteComment {\n    return new LiteComment(text);\n  }\n\n  /**\n   * @return {LiteDocument}  A new document element\n   */\n  public createDocument(): LiteDocument {\n    return new LiteDocument();\n  }\n\n  /**\n   * @override\n   */\n  public head(doc: LiteDocument) {\n    return doc.head;\n  }\n\n  /**\n   * @override\n   */\n  public body(doc: LiteDocument) {\n    return doc.body;\n  }\n\n  /**\n   * @override\n   */\n  public root(doc: LiteDocument) {\n    return doc.root;\n  }\n\n  /**\n   * @override\n   */\n  public doctype(doc: LiteDocument) {\n    return doc.type;\n  }\n\n  /**\n   * @override\n   */\n  public tags(node: LiteElement, name: string, ns: string = null) {\n    let stack = [] as LiteNode[];\n    let tags = [] as LiteElement[];\n    if (ns) {\n      return tags;  // we don't have namespaces\n    }\n    let n: LiteNode = node;\n    while (n) {\n      let kind = n.kind;\n      if (kind !== '#text' && kind !== '#comment') {\n        n = n as LiteElement;\n        if (kind === name) {\n          tags.push(n);\n        }\n        if (n.children.length) {\n          stack = n.children.concat(stack);\n        }\n      }\n      n = stack.shift();\n    }\n    return tags;\n  }\n\n  /**\n   * @param {LiteElement} node   The node to be searched\n   * @param {string} id          The id of the node to look for\n   * @return {LiteElement}       The child node having the given id\n   */\n  public elementById(node: LiteElement, id: string): LiteElement {\n    let stack = [] as LiteNode[];\n    let n = node as LiteNode;\n    while (n) {\n      if (n.kind !== '#text' && n.kind !== '#comment') {\n        n = n as LiteElement;\n        if (n.attributes['id'] === id) {\n          return n;\n        }\n        if (n.children.length) {\n          stack = n.children.concat(stack);\n        }\n      }\n      n = stack.shift();\n    }\n    return null;\n  }\n\n  /**\n   * @param {LiteElement} node   The node to be searched\n   * @param {string} name        The name of the class to find\n   * @return {LiteElement[]}     The nodes with the given class\n   */\n  public elementsByClass(node: LiteElement, name: string): LiteElement[] {\n    let stack = [] as LiteNode[];\n    let tags = [] as LiteElement[];\n    let n: LiteNode = node;\n    while (n) {\n      if (n.kind !== '#text' && n.kind !== '#comment') {\n        n = n as LiteElement;\n        const classes = (n.attributes['class'] || '').trim().split(/ +/);\n        if (classes.includes(name)) {\n          tags.push(n);\n        }\n        if (n.children.length) {\n          stack = n.children.concat(stack);\n        }\n      }\n      n = stack.shift();\n    }\n    return tags;\n  }\n\n  /**\n   * @override\n   */\n  public getElements(nodes: (string | LiteElement | LiteElement[])[], document: LiteDocument) {\n    let containers = [] as LiteElement[];\n    const body = this.body(document);\n    for (const node of nodes) {\n      if (typeof(node) === 'string') {\n        if (node.charAt(0) === '#') {\n          const n = this.elementById(body, node.slice(1));\n          if (n) {\n            containers.push(n);\n          }\n        } else if (node.charAt(0) === '.') {\n          containers = containers.concat(this.elementsByClass(body, node.slice(1)));\n        } else if (node.match(/^[-a-z][-a-z0-9]*$/i)) {\n          containers = containers.concat(this.tags(body, node));\n        }\n      } else if (Array.isArray(node)) {\n        containers = containers.concat(node);\n      } else if (node instanceof this.window.NodeList || node instanceof this.window.HTMLCollection) {\n        containers = containers.concat((node as LiteList<LiteElement>).nodes);\n      } else {\n        containers.push(node);\n      }\n    }\n    return containers;\n  }\n\n  /**\n   * @override\n   */\n  public contains(container: LiteNode, node: LiteNode | LiteText) {\n    while (node && node !== container) {\n      node = this.parent(node);\n    }\n    return !!node;\n  }\n\n  /**\n   * @override\n   */\n  public parent(node: LiteNode) {\n    return node.parent;\n  }\n\n  /**\n   * @param {LiteNode} node  The node whose index is needed\n   * @return {number}        The index of the node it its parent's children array\n   */\n  public childIndex(node: LiteNode): number {\n    return (node.parent ? node.parent.children.findIndex(n => n === node) : -1);\n  }\n\n  /**\n   * @override\n   */\n  public append(node: LiteElement, child: LiteNode) {\n    if (child.parent) {\n      this.remove(child);\n    }\n    node.children.push(child);\n    child.parent = node;\n    return child;\n  }\n\n  /**\n   * @override\n   */\n  public insert(nchild: LiteNode, ochild: LiteNode) {\n    if (nchild.parent) {\n      this.remove(nchild);\n    }\n    if (ochild && ochild.parent) {\n      const i = this.childIndex(ochild);\n      ochild.parent.children.splice(i, 0, nchild);\n      nchild.parent = ochild.parent;\n    }\n  }\n\n  /**\n   * @override\n   */\n  public remove(child: LiteNode) {\n    const i = this.childIndex(child);\n    if (i >= 0) {\n      child.parent.children.splice(i, 1);\n    }\n    child.parent = null;\n    return child;\n  }\n\n  /**\n   * @override\n   */\n  public replace(nnode: LiteNode, onode: LiteNode) {\n    const i = this.childIndex(onode);\n    if (i >= 0) {\n      onode.parent.children[i] = nnode;\n      nnode.parent = onode.parent;\n      onode.parent = null;\n    }\n    return onode;\n  }\n\n  /**\n   * @override\n   */\n  public clone(node: LiteElement) {\n    const nnode = new LiteElement(node.kind);\n    nnode.attributes = {...node.attributes};\n    nnode.children = node.children.map(n => {\n      if (n.kind === '#text') {\n        return new LiteText((n as LiteText).value);\n      } else if (n.kind === '#comment') {\n        return new LiteComment((n as LiteComment).value);\n      } else {\n        const m = this.clone(n as LiteElement);\n        m.parent = nnode;\n        return m;\n      }\n    });\n    return nnode;\n  }\n\n  /**\n   * @override\n   */\n  public split(node: LiteText, n: number) {\n    const text = new LiteText(node.value.slice(n));\n    node.value = node.value.slice(0, n);\n    node.parent.children.splice(this.childIndex(node) + 1, 0, text);\n    text.parent = node.parent;\n    return text;\n  }\n\n  /**\n   * @override\n   */\n  public next(node: LiteNode) {\n    const parent = node.parent;\n    if (!parent) return null;\n    const i = this.childIndex(node) + 1;\n    return (i >= 0 && i < parent.children.length ? parent.children[i] : null);\n  }\n\n  /**\n   * @override\n   */\n  public previous(node: LiteNode) {\n    const parent = node.parent;\n    if (!parent) return null;\n    const i = this.childIndex(node) - 1;\n    return (i >= 0 ? parent.children[i] : null);\n  }\n\n  /**\n   * @override\n   */\n  public firstChild(node: LiteElement) {\n    return node.children[0];\n  }\n\n  /**\n   * @override\n   */\n  public lastChild(node: LiteElement) {\n    return node.children[node.children.length - 1];\n  }\n\n  /**\n   * @override\n   */\n  public childNodes(node: LiteElement) {\n    return [...node.children];\n  }\n\n  /**\n   * @override\n   */\n  public childNode(node: LiteElement, i: number) {\n    return node.children[i];\n  }\n\n  /**\n   * @override\n   */\n  public kind(node: LiteNode) {\n    return node.kind;\n  }\n\n  /**\n   * @override\n   */\n  public value(node: LiteNode | LiteText) {\n    return (node.kind === '#text' ? (node as LiteText).value :\n            node.kind === '#comment' ? (node as LiteComment).value.replace(/^<!(--)?((?:.|\\n)*)\\1>$/, '$2') : '');\n  }\n\n  /**\n   * @override\n   */\n  public textContent(node: LiteElement): string {\n    return node.children.reduce((s: string, n: LiteNode) => {\n      return s + (n.kind === '#text' ? (n as LiteText).value :\n                  n.kind === '#comment' ? '' : this.textContent(n as LiteElement));\n    }, '');\n  }\n\n  /**\n   * @override\n   */\n  public innerHTML(node: LiteElement): string {\n    return this.parser.serializeInner(this, node);\n  }\n\n  /**\n   * @override\n   */\n  public outerHTML(node: LiteElement): string {\n    return this.parser.serialize(this, node);\n  }\n\n  /**\n   * @override\n   */\n  public serializeXML(node: LiteElement): string {\n    return this.parser.serialize(this, node, true);\n  }\n\n  /**\n   * @override\n   */\n  public setAttribute(node: LiteElement, name: string, value: string | number, ns: string = null) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    if (ns) {\n      name = ns.replace(/.*\\//, '') + ':' + name.replace(/^.*:/, '');\n    }\n    node.attributes[name] = value;\n    if (name === 'style') {\n      node.styles = null;\n    }\n  }\n\n  /**\n   * @override\n   */\n  public getAttribute(node: LiteElement, name: string) {\n    return node.attributes[name];\n  }\n\n  /**\n   * @override\n   */\n  public removeAttribute(node: LiteElement, name: string) {\n    delete node.attributes[name];\n  }\n\n  /**\n   * @override\n   */\n  public hasAttribute(node: LiteElement, name: string) {\n    return node.attributes.hasOwnProperty(name);\n  }\n\n  /**\n   * @override\n   */\n  public allAttributes(node: LiteElement) {\n    const attributes = node.attributes;\n    const list = [];\n    for (const name of Object.keys(attributes)) {\n      list.push({name: name, value: attributes[name] as string});\n    }\n    return list;\n  }\n\n  /**\n   * @override\n   */\n  public addClass(node: LiteElement, name: string) {\n    const classes = (node.attributes['class'] as string || '').split(/ /);\n    if (!classes.find(n => n === name)) {\n      classes.push(name);\n      node.attributes['class'] = classes.join(' ');\n    }\n  }\n\n  /**\n   * @override\n   */\n  public removeClass(node: LiteElement, name: string) {\n    const classes = (node.attributes['class'] as string || '').split(/ /);\n    const i = classes.findIndex(n => n === name);\n    if (i >= 0) {\n      classes.splice(i, 1);\n      node.attributes['class'] = classes.join(' ');\n    }\n  }\n\n  /**\n   * @override\n   */\n  public hasClass(node: LiteElement, name: string) {\n    const classes = (node.attributes['class'] as string || '').split(/ /);\n    return !!classes.find(n => n === name);\n  }\n\n  /**\n   * @override\n   */\n  public setStyle(node: LiteElement, name: string, value: string) {\n    if (!node.styles) {\n      node.styles = new Styles(this.getAttribute(node, 'style'));\n    }\n    node.styles.set(name, value);\n    node.attributes['style'] = node.styles.cssText;\n  }\n\n  /**\n   * @override\n   */\n  public getStyle(node: LiteElement, name: string) {\n    if (!node.styles) {\n      const style = this.getAttribute(node, 'style');\n      if (!style) {\n        return '';\n      }\n      node.styles = new Styles(style);\n    }\n    return node.styles.get(name);\n  }\n\n  /**\n   * @override\n   */\n  public allStyles(node: LiteElement) {\n    return this.getAttribute(node, 'style');\n  }\n\n  /**\n   * @override\n   */\n  public insertRules(node: LiteElement, rules: string[]) {\n    node.children = [this.text(rules.join('\\n\\n') + '\\n\\n' + this.textContent(node))];\n  }\n\n  /*******************************************************************/\n  /*\n   *  The next four methods get overridden by the NodeMixin below\n   */\n\n  /**\n   * @override\n   */\n  public fontSize(_node: LiteElement) {\n    return 0;\n  }\n\n  /**\n   * @override\n   */\n  public fontFamily(_node: LiteElement) {\n    return '';\n  }\n\n  /**\n   * @override\n   */\n  public nodeSize(_node: LiteElement, _em: number = 1, _local: boolean = null) {\n    return [0, 0] as [number, number];\n  }\n\n  /**\n   * @override\n   */\n  public nodeBBox(_node: LiteElement) {\n    return {left: 0, right: 0, top: 0, bottom: 0};\n  }\n\n}\n\n/**\n * The LiteAdaptor class (add in the NodeMixin methods and options)\n */\nexport class LiteAdaptor extends NodeMixin<LiteElement, LiteText, LiteDocument, Constructor<LiteBase>>(LiteBase) {}\n\n/************************************************************/\n/**\n * The function to call to obtain a LiteAdaptor\n *\n * @param {OptionList} options  The options for the adaptor\n * @return {LiteAdaptor}        The newly created adaptor\n */\nexport function liteAdaptor(options: OptionList = null): LiteAdaptor {\n  return new LiteAdaptor(null, options);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuXA,QAAA,qBAAA,WAAA;AAWE,eAAAA,oBAAY,UAAkB;AAAlB,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAkB;AAC5B,aAAK,WAAW;MAClB;AAUO,MAAAA,oBAAA,UAAA,OAAP,SAAY,MAAc,KAAsB,UAA0B,IAAW;;AAA3D,YAAA,QAAA,QAAA;AAAA,gBAAA,CAAA;QAAoB;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAwB;AACtE,YAAM,OAAO,KAAK,OAAO,MAAM,EAAE;AACjC,aAAK,cAAc,MAAM,GAAG;;AAC5B,mBAAoB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAzB,gBAAM,QAAK,aAAA;AACd,iBAAK,OAAO,MAAM,KAAK;;;;;;;;;;;AAEzB,eAAO;MACT;AAkBO,MAAAA,oBAAA,UAAA,gBAAP,SAAqB,MAAS,KAAe;;AAC3C,YAAI,IAAI,SAAS,OAAO,IAAI,UAAW,UAAU;;AAC/C,qBAAgB,KAAA,SAAA,OAAO,KAAK,IAAI,KAAK,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,kBAAI,MAAG,GAAA;AACV,mBAAK,SAAS,MAAM,IAAI,QAAQ,aAAa,SAAC,IAAI,GAAC;AAAK,uBAAA,EAAE,YAAW;cAAb,CAAe,GAAG,IAAI,MAAM,GAAG,CAAC;;;;;;;;;;;;AAG5F,YAAI,IAAI,YAAY;;AAClB,qBAAgB,KAAA,SAAA,OAAO,KAAK,IAAI,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAxC,kBAAI,MAAG,GAAA;AACT,mBAAoB,GAAG,IAAI,IAAI,WAAW,GAAG;;;;;;;;;;;;;AAGlD,mBAAgB,KAAA,SAAA,OAAO,KAAK,GAAG,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA7B,gBAAI,MAAG,GAAA;AACV,iBAAK,QAAQ,WAAW,OAAO,IAAI,UAAW,aAAa,QAAQ,cAAc;AAC/E,mBAAK,aAAa,MAAM,KAAK,IAAI,GAAG,CAAC;;;;;;;;;;;;MAG3C;AA4DO,MAAAA,oBAAA,UAAA,UAAP,SAAe,OAAc,OAAY;AACvC,aAAK,OAAO,OAAO,KAAK;AACxB,aAAK,OAAO,KAAK;AACjB,eAAO;MACT;AAwCO,MAAAA,oBAAA,UAAA,YAAP,SAAiB,MAAS,GAAS;AACjC,eAAO,KAAK,WAAW,IAAI,EAAE,CAAC;MAChC;AA4EO,MAAAA,oBAAA,UAAA,aAAP,SAAkB,MAAO;AACvB,YAAM,UAAU,KAAK,aAAa,MAAM,OAAO;AAC/C,eAAQ,CAAC,UAAU,CAAA,IACX,QAAQ,QAAQ,QAAQ,GAAG,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,MAAM,EAAE,EAAE,MAAM,GAAG;MACpF;AA0CF,aAAAA;IAAA,EApSA;AAAsB,YAAA,qBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7VtB,QAAA,eAAA;AAiBa,YAAA,mBAA+B;MAC1C,QAAQ;MACR,UAAU;;AAQZ,aAAgB,UACd,MACA,SAAqC;;AAArC,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAAqC;AAGrC,iBAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,QAAA,gBAAgB,GAAG,OAAO;AAEnE,aAAA,KAAA,SAAA,QAAA;AAAiC,kBAAA,aAAA,MAAA;AAkD/B,iBAAA,cAAA;AAAY,cAAA,OAAA,CAAA;mBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,iBAAA,EAAA,IAAA,UAAA,EAAA;;AAAZ,cAAA,QACE,OAAA,KAAA,MAAM,KAAK,CAAC,CAAC,KAAC;AACd,cAAI,QAAQ,MAAK;AACjB,gBAAK,WAAU,GAAA,aAAA,cAAY,GAAA,aAAA,gBAAe,CAAA,GAAI,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC;;QACvE;AAOO,oBAAA,UAAA,WAAP,SAAgB,MAAO;AACrB,iBAAQ,QAAQ,SAAS,KAAK,QAAQ,WAAW,OAAA,UAAM,SAAQ,KAAA,MAAC,IAAI;QACtE;AAOO,oBAAA,UAAA,aAAP,SAAkB,MAAO;AACvB,iBAAQ,QAAQ,SAAS,KAAK,QAAQ,aAAa,OAAA,UAAM,WAAU,KAAA,MAAC,IAAI;QAC1E;AAKO,oBAAA,UAAA,WAAP,SAAgB,MAAS,IAAgB,OAAqB;AAArC,cAAA,OAAA,QAAA;AAAA,iBAAA;UAAc;AAAE,cAAA,UAAA,QAAA;AAAA,oBAAA;UAAqB;AAC5D,cAAI,CAAC,QAAQ,UAAU;AACrB,mBAAO,OAAA,UAAM,SAAQ,KAAA,MAAC,MAAM,IAAI,KAAK;;AAEvC,cAAM,OAAO,KAAK,YAAY,IAAI;AAClC,cAAM,MAAM,MAAM,KAAK,KAAK,QAAQ,YAAY,YAAY,EAAE,CAAC,EAAE;AACjE,cAAM,MAAM,MAAM,KAAK,IAAI,EAAE,SAAS;AACtC,iBAAO;YACL,MAAM,KAAK,QAAQ,eAAe,MAAM,KAAK,QAAQ;YACrD,KAAK,QAAQ;;QAEjB;AAKO,oBAAA,UAAA,WAAP,SAAgB,MAAO;AACrB,iBAAQ,QAAQ,WAAW,EAAC,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAC,IAAI,OAAA,UAAM,SAAQ,KAAA,MAAC,IAAI;QACzF;AAEF,eAAA;MAAA,EAjGiC,IAAI,GAKrB,GAAA,UAAO,SAAA,SAAA,CAAA,GACf,QAAQ,SAAS;QACnB,UAAU;QACV,YAAY;UACV,CAAA,CAAG,GACH,QAAQ,WAAW;QACrB,cAAc;QACd,kBAAkB;QAClB,mBAAmB;UACjB,CAAA,CAAG,GAMK,GAAA,aAAa,IAAI,OAAO;QACpC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAK,EAAE,GAAG,IAAI;IA6DpB;AA1GA,YAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXA,QAAA,cAAA,2BAAA;AAgCE,eAAAC,aAAY,MAAc,YAAoC,UAAyB;;AAA7D,YAAA,eAAA,QAAA;AAAA,uBAAA,CAAA;QAAkC;AAAE,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAyB;AACrF,aAAK,OAAO;AACZ,aAAK,aAAU,SAAA,CAAA,GAAO,UAAU;AAChC,aAAK,WAAQ,cAAA,CAAA,GAAA,OAAO,QAAQ,GAAA,KAAA;;AAC5B,mBAAoB,KAAA,SAAA,KAAK,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9B,gBAAM,QAAK,GAAA;AACd,kBAAM,SAAS;;;;;;;;;;;AAEjB,aAAK,SAAS;MAChB;AACF,aAAAA;IAAA,EAzCA;AAAa,YAAA,cAAA;;;;;;;;;;ACnBb,QAAA,eAAA;AAMA,QAAA,eAAA,WAAA;AA6BE,eAAAC,gBAAA;AACE,aAAK,OAAO,IAAI,aAAA,YAAY,QAAQ,CAAA,GAAI;UACtC,KAAK,OAAO,IAAI,aAAA,YAAY,MAAM;UAClC,KAAK,OAAO,IAAI,aAAA,YAAY,MAAM;SACnC;AACD,aAAK,OAAO;MACd;AAbA,aAAA,eAAWA,cAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAYF,aAAAA;IAAA,EApCA;AAAa,YAAA,eAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAb,QAAA,WAAA,WAAA;AAsBE,eAAAC,UAAY,MAAiB;AAAjB,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAiB;AAC3B,aAAK,QAAQ;MACf;AAVA,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AASF,aAAAA;IAAA,EAzBA;AAAa,YAAA,WAAA;AA+Bb,QAAA,cAAA,SAAA,QAAA;AAAiC,gBAAAC,cAAA,MAAA;AAAjC,eAAAA,eAAA;;MAIA;AAHE,aAAA,eAAWA,aAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AACF,aAAAA;IAAA,EAJiC,QAAQ;AAA5B,YAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Bb,QAAA,WAAA,WAAA;AAUE,eAAAC,UAAY,UAAa;AANlB,aAAA,QAAa,CAAA;AAOlB,aAAK,QAAK,cAAA,CAAA,GAAA,OAAO,QAAQ,GAAA,KAAA;MAC3B;AAKO,MAAAA,UAAA,UAAA,SAAP,SAAc,MAAO;AACnB,aAAK,MAAM,KAAK,IAAI;MACtB;AAKO,MAAAA,UAAA,UAAC,OAAO,QAAQ,IAAvB,WAAA;AACE,YAAI,IAAI;AACR,eAAO;UAIL,MAAA,WAAA;AACE,mBAAQ,MAAM,KAAK,MAAM,SACjB,EAAC,OAAO,MAAM,MAAM,KAAI,IACxB,EAAC,OAAO,KAAK,MAAM,GAAG,GAAG,MAAM,MAAK;UAC9C;;MAEJ;AAEF,aAAAA;IAAA,EAtCA;AAAa,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLb,QAAA,WAAA,aAAA,kBAAA;AAEA,QAAA,eAAA;AACA,QAAA,YAAA;AAMA,QAAiB;AAAjB,KAAA,SAAiBC,WAAQ;AACV,MAAAA,UAAA,UAAU;AACV,MAAAA,UAAA,UAAU;AACV,MAAAA,UAAA,QAAS;AACT,MAAAA,UAAA,aAAc;AACd,MAAAA,UAAA,QAAQ;AACR,MAAAA,UAAA,gBAAgB;AAChB,MAAAA,UAAA,YAAYA,UAAA,UAAU,QAAQA,UAAA,gBAAgB,MAAMA,UAAA,gBAAgBA,UAAA,QAAQ;AAC5E,MAAAA,UAAA,iBAAiB,MAAMA,UAAA,UAAU,SAASA,UAAA,gBAAgB,MAAMA,UAAA,gBAAgBA,UAAA,aAAa;AAC7F,MAAAA,UAAA,MAAM,UAAUA,UAAA,UAAU,QAAQA,UAAA,QAAQA,UAAA,YAAY,OAC5CA,UAAA,gBAAgB,SAASA,UAAA,UAAU;AAC7C,MAAAA,UAAA,MAAM,IAAI,OAAOA,UAAA,KAAK,GAAG;AACzB,MAAAA,UAAA,OAAO,IAAI,OAAOA,UAAA,WAAW,GAAG;AAChC,MAAAA,UAAA,YAAY,IAAI,OAAOA,UAAA,gBAAgB,GAAG;IACzD,GAdiB,WAAA,QAAA,aAAA,QAAA,WAAQ,CAAA,EAAA;AAqBzB,QAAA,aAAA,WAAA;AAAA,eAAAC,cAAA;MA4VA;AAhSS,MAAAA,YAAA,UAAA,kBAAP,SAAuB,MAAc,SAA+B,SAA2B;AAA1D,YAAA,YAAA,QAAA;AAAA,oBAAA;QAA6B;AAAE,YAAA,YAAA,QAAA;AAAA,oBAAA;QAA2B;AAC7F,YAAM,OAAO,QAAQ,eAAc;AACnC,YAAI,OAAO,QAAQ,KAAK,IAAI;AAK5B,YAAI,QAAQ,KAAK,QAAQ,cAAc,EAAE,EAAE,MAAM,SAAS,GAAG;AAC7D,eAAO,MAAM,QAAQ;AACnB,cAAM,SAAO,MAAM,MAAK;AACxB,cAAM,MAAM,MAAM,MAAK;AACvB,cAAI,QAAM;AACR,iBAAK,QAAQ,SAAS,MAAM,MAAI;;AAElC,cAAI,OAAO,IAAI,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7C,gBAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,mBAAK,WAAW,SAAS,MAAM,GAAG;uBACzB,IAAI,OAAO,CAAC,MAAM,KAAK;AAChC,qBAAO,KAAK,SAAS,SAAS,MAAM,GAAG;mBAClC;AACL,qBAAO,KAAK,QAAQ,SAAS,MAAM,KAAK,KAAK;;;;AAInD,aAAK,cAAc,SAAS,IAAI;AAChC,eAAO;MACT;AAQU,MAAAA,YAAA,UAAA,UAAV,SAAkB,SAAsB,MAAmB,MAAY;AACrE,eAAO,SAAS,UAAU,IAAI;AAC9B,eAAO,QAAQ,OAAO,MAAM,QAAQ,KAAK,IAAI,CAAC;MAChD;AAQU,MAAAA,YAAA,UAAA,aAAV,SAAqB,SAAsB,MAAmB,SAAe;AAC3E,eAAO,QAAQ,OAAO,MAAM,IAAI,UAAA,YAAY,OAAO,CAAC;MACtD;AAQU,MAAAA,YAAA,UAAA,WAAV,SAAmB,SAAsB,MAAmB,KAAW;AACrE,YAAM,OAAO,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,YAAW;AACrD,eAAO,QAAQ,OAAO,IAAI,KAAK,QAAQ,KAAK,IAAI,MAAM,MAAM;AAC1D,iBAAO,QAAQ,OAAO,IAAI;;AAE5B,eAAO,QAAQ,OAAO,IAAI;MAC5B;AASU,MAAAA,YAAA,UAAA,UAAV,SAAkB,SAAsB,MAAmB,KAAa,OAAe;AACrF,YAAM,SAAU,KAAK,YAAkC;AACvD,YAAM,eAAgB,KAAK,YAAkC;AAI7D,YAAM,OAAO,IAAI,MAAM,iBAAiB,EAAE,CAAC,EAAE,YAAW;AACxD,YAAM,QAAQ,QAAQ,KAAK,IAAI;AAM/B,YAAM,aAAa,IAAI,QAAQ,gBAAgB,EAAE,EAAE,MAAM,SAAS,SAAS;AAI3E,YAAI,WAAW,IAAG,EAAG,MAAM,IAAI,KAAK,WAAW,SAAS,GAAG;AACzD,eAAK,cAAc,SAAS,OAAO,UAAU;AAC7C,kBAAQ,OAAO,MAAM,KAAK;AAS1B,cAAI,CAAC,aAAa,IAAI,KAAK,CAAC,IAAI,MAAM,MAAM,GAAG;AAC7C,gBAAI,OAAO,IAAI,GAAG;AAChB,mBAAK,aAAa,SAAS,OAAO,MAAM,KAAK;mBACxC;AACL,qBAAO;;;;AAIb,eAAO;MACT;AAQU,MAAAA,YAAA,UAAA,gBAAV,SAAwB,SAAsB,MAAmB,YAAoB;AACnF,YAAM,aAAc,KAAK,YAAkC;AAC3D,eAAO,WAAW,QAAQ;AACpB,cAAA,KAAA,OAAwB,WAAW,OAAO,GAAG,CAAC,GAAC,CAAA,GAA3C,SAAI,GAAA,CAAA,GAAE,KAAE,GAAA,CAAA,GAAE,KAAE,GAAA,CAAA,GAAE,KAAE,GAAA,CAAA;AACxB,cAAI,QAAQ,MAAM,MAAM,MAAM;AAC9B,cAAI,CAAC,WAAW,MAAI,GAAG;AACrB,oBAAQ,SAAS,UAAU,KAAK;;AAElC,kBAAQ,aAAa,MAAM,QAAM,KAAK;;MAE1C;AAQU,MAAAA,YAAA,UAAA,eAAV,SAAuB,SAAsB,MAAmB,MAAc,OAAe;AAC3F,YAAM,SAAS,CAAA;AACf,YAAM,OAAO,OAAO,OAAO;AAC3B,YAAI,OAAO;AAMX,eAAO,MAAM,UAAU,SAAS,MAAM;AACpC,iBAAO,KAAK,IAAI;AAChB,iBAAO,KAAK,MAAM,MAAK,CAAE;AACzB,iBAAO,MAAM,MAAK;;AAKpB,gBAAQ,OAAO,MAAM,QAAQ,KAAK,OAAO,KAAK,EAAE,CAAC,CAAC;MACpD;AAUU,MAAAA,YAAA,UAAA,gBAAV,SAAwB,SAAsB,MAAkB;;AAC9D,YAAI,OAAO,KAAK,aAAa,SAAS,QAAQ,KAAK,IAAI,CAAC;AACxD,YAAI,CAAC;AAAM;;AACX,mBAAoB,KAAA,SAAA,QAAQ,WAAW,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvD,gBAAM,QAAK,GAAA;AACd,gBAAI,UAAU,MAAM;AAClB;;AAEF,gBAAI,iBAAiB,UAAA,eAAe,MAAM,MAAM,MAAM,YAAY,GAAG;AACnE,mBAAK,OAAO,MAAM;;;;;;;;;;;;AAGtB,gBAAQ,QAAQ,KAAK,IAAI,GAAG;UAC5B,KAAK;;AAIH,uBAAoB,KAAA,SAAA,KAAK,QAAQ,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9B,oBAAM,QAAK,GAAA;AACd,wBAAQ,QAAQ,KAAK,KAAK,GAAG;kBAC7B,KAAK;AACH,yBAAK,OAAO;AACZ;kBACF,KAAK;AACH,yBAAK,OAAO;AACZ;;;;;;;;;;;;AAMJ,iBAAK,OAAO;AACZ,oBAAQ,OAAO,IAAI;AACnB,gBAAI,QAAQ,OAAO,KAAK,IAAI,MAAM,MAAM;AACtC,sBAAQ,OAAO,MAAM,KAAK,IAAI;;AAEhC,gBAAI,QAAQ,OAAO,KAAK,IAAI,MAAM,MAAM;AACtC,sBAAQ,OAAO,KAAK,MAAM,KAAK,IAAI;;AAErC;UAEF,KAAK;AACH,iBAAK,OAAO,QAAQ,QAAQ,MAAM,KAAK,IAAI;AAC3C;UAEF,KAAK;AACH,iBAAK,OAAO,QAAQ,QAAQ,MAAM,KAAK,IAAI;AAC3C;;MAEJ;AAUU,MAAAA,YAAA,UAAA,eAAV,SAAuB,SAAsB,MAAiB;;AAC5D,YAAI,OAAoB;;AACxB,mBAAoB,KAAA,SAAA,QAAQ,WAAW,IAAI,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAM,QAAK,GAAA;AACd,gBAAI,iBAAiB,aAAA,aAAa;AAChC,kBAAI;AAAM,uBAAO;AACjB,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAQO,MAAAA,YAAA,UAAA,YAAP,SAAiB,SAAsB,MAAmB,KAAoB;AAA9E,YAAA,QAAA;AAA0D,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAoB;AAC5E,YAAM,eAAgB,KAAK,YAAkC;AAC7D,YAAM,QAAS,KAAK,YAAkC;AACtD,YAAM,MAAM,QAAQ,KAAK,IAAI;AAC7B,YAAM,aAAa,QAAQ,cAAc,IAAI,EAAE,IAC7C,SAAC,GAAgB;AAAK,iBAAA,EAAE,OAAO,QAAQ,MAAM,EAAE,IAAI,IAAI,EAAE,QAAQ,MAAK,iBAAiB,EAAE,KAAK,KAAK;QAA7E,CAAgF,EACtG,KAAK,GAAG;AACV,YAAM,UAAU,KAAK,eAAe,SAAS,MAAM,GAAG;AACtD,YAAM,OACJ,MAAM,OAAO,aAAa,MAAM,aAAa,QACrC,CAAC,OAAO,YAAY,CAAC,aAAa,GAAG,IAAI,IAAA,OAAI,SAAO,IAAA,EAAA,OAAK,KAAG,GAAA,IAAM,MAAM,OAAO;AACzF,eAAO;MACT;AAOO,MAAAA,YAAA,UAAA,iBAAP,SAAsB,SAAsB,MAAmB,KAAoB;AAAnF,YAAA,QAAA;AAA+D,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAoB;AACjF,YAAM,SAAU,KAAK,YAAkC;AACvD,YAAI,OAAO,eAAe,KAAK,IAAI,GAAG;AACpC,iBAAO,QAAQ,WAAW,IAAI,EAAE,IAAI,SAAA,GAAC;AAAI,mBAAA,QAAQ,MAAM,CAAC;UAAf,CAAgB,EAAE,KAAK,EAAE;;AAEpE,eAAO,QAAQ,WAAW,IAAI,EAAE,IAAI,SAAA,GAAC;AACnC,cAAM,OAAO,QAAQ,KAAK,CAAC;AAC3B,iBAAQ,SAAS,UAAU,MAAK,YAAY,QAAQ,MAAM,CAAC,CAAC,IACpD,SAAS,aAAc,EAAkB,QACzC,MAAK,UAAU,SAAS,GAAkB,GAAG;QACvD,CAAC,EAAE,KAAK,EAAE;MACZ;AAMO,MAAAA,YAAA,UAAA,mBAAP,SAAwB,MAAY;AAClC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,OAAO,IAAI;;AAEpB,eAAO,KAAK,QAAQ,MAAM,QAAQ;MACpC;AAMO,MAAAA,YAAA,UAAA,cAAP,SAAmB,MAAY;AAC7B,eAAO,KAAK,QAAQ,MAAM,OAAO,EAC9B,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;MACzB;AArVc,MAAAA,YAAA,eAA0C;QACtD,MAAM;QACN,MAAM;QACN,IAAI;QACJ,KAAK;QACL,SAAS;QACT,OAAO;QACP,IAAI;QACJ,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,KAAK;;AAMO,MAAAA,YAAA,SAAoC;QAChD,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;;AAMI,MAAAA,YAAA,aAAwC;QACpD,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;;AAsSZ,aAAAA;MA5VA;AAAa,YAAA,aAAA;;;;;;;;;;ACjCb,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,YAAA;AACA,QAAA,cAAA;AAMA,QAAA,aAAA,2BAAA;AAiCE,eAAAC,cAAA;AAzBO,aAAA,YAA+B,YAAA;AAI/B,aAAA,WAA4B,UAAA;AAI5B,aAAA,iBAAkC,UAAA;AAIlC,aAAA,cAAkC,aAAA;AAIlC,aAAA,mBAAqC,UAAA;AAIrC,aAAA,WAAiC,cAAA;AAMtC,aAAK,WAAW,IAAI,cAAA,aAAY;MAClC;AACF,aAAAA;IAAA,EApCA;AAAa,YAAA,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTb,QAAA,kBAAA;AACA,QAAA,iBAAA;AACA,QAAA,gBAAA;AACA,QAAA,eAAA;AACA,QAAA,YAAA;AAEA,QAAA,cAAA;AACA,QAAA,cAAA;AACA,QAAA,cAAA;AASA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAoB5B,eAAAA,YAAA;AAAA,YAAA,QACE,OAAA,KAAA,IAAA,KAAO;AACP,cAAK,SAAS,IAAI,YAAA,WAAU;AAC5B,cAAK,SAAS,IAAI,YAAA,WAAU;;MAC9B;AAKO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAc,QAAe;AACxC,eAAO,KAAK,OAAO,gBAAgB,MAAM,QAAQ,IAAI;MACvD;AAKU,MAAAA,UAAA,UAAA,SAAV,SAAiB,MAAc,KAAkB;AAAlB,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAkB;AAC/C,eAAO,IAAI,aAAA,YAAY,IAAI;MAC7B;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,MAAY;AACtB,eAAO,IAAI,UAAA,SAAS,IAAI;MAC1B;AAMO,MAAAA,UAAA,UAAA,UAAP,SAAe,MAAY;AACzB,eAAO,IAAI,UAAA,YAAY,IAAI;MAC7B;AAKO,MAAAA,UAAA,UAAA,iBAAP,WAAA;AACE,eAAO,IAAI,cAAA,aAAY;MACzB;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,KAAiB;AAC3B,eAAO,IAAI;MACb;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,KAAiB;AAC3B,eAAO,IAAI;MACb;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,KAAiB;AAC3B,eAAO,IAAI;MACb;AAKO,MAAAA,UAAA,UAAA,UAAP,SAAe,KAAiB;AAC9B,eAAO,IAAI;MACb;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,MAAmB,MAAc,IAAiB;AAAjB,YAAA,OAAA,QAAA;AAAA,eAAA;QAAiB;AAC5D,YAAI,QAAQ,CAAA;AACZ,YAAI,OAAO,CAAA;AACX,YAAI,IAAI;AACN,iBAAO;;AAET,YAAI,IAAc;AAClB,eAAO,GAAG;AACR,cAAI,OAAO,EAAE;AACb,cAAI,SAAS,WAAW,SAAS,YAAY;AAC3C,gBAAI;AACJ,gBAAI,SAAS,MAAM;AACjB,mBAAK,KAAK,CAAC;;AAEb,gBAAI,EAAE,SAAS,QAAQ;AACrB,sBAAQ,EAAE,SAAS,OAAO,KAAK;;;AAGnC,cAAI,MAAM,MAAK;;AAEjB,eAAO;MACT;AAOO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAmB,IAAU;AAC9C,YAAI,QAAQ,CAAA;AACZ,YAAI,IAAI;AACR,eAAO,GAAG;AACR,cAAI,EAAE,SAAS,WAAW,EAAE,SAAS,YAAY;AAC/C,gBAAI;AACJ,gBAAI,EAAE,WAAW,IAAI,MAAM,IAAI;AAC7B,qBAAO;;AAET,gBAAI,EAAE,SAAS,QAAQ;AACrB,sBAAQ,EAAE,SAAS,OAAO,KAAK;;;AAGnC,cAAI,MAAM,MAAK;;AAEjB,eAAO;MACT;AAOO,MAAAA,UAAA,UAAA,kBAAP,SAAuB,MAAmB,MAAY;AACpD,YAAI,QAAQ,CAAA;AACZ,YAAI,OAAO,CAAA;AACX,YAAI,IAAc;AAClB,eAAO,GAAG;AACR,cAAI,EAAE,SAAS,WAAW,EAAE,SAAS,YAAY;AAC/C,gBAAI;AACJ,gBAAM,WAAW,EAAE,WAAW,OAAO,KAAK,IAAI,KAAI,EAAG,MAAM,IAAI;AAC/D,gBAAI,QAAQ,SAAS,IAAI,GAAG;AAC1B,mBAAK,KAAK,CAAC;;AAEb,gBAAI,EAAE,SAAS,QAAQ;AACrB,sBAAQ,EAAE,SAAS,OAAO,KAAK;;;AAGnC,cAAI,MAAM,MAAK;;AAEjB,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,OAAiD,UAAsB;;AACxF,YAAI,aAAa,CAAA;AACjB,YAAM,OAAO,KAAK,KAAK,QAAQ;;AAC/B,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,OAAI,UAAA;AACb,gBAAI,OAAO,SAAU,UAAU;AAC7B,kBAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,oBAAM,IAAI,KAAK,YAAY,MAAM,KAAK,MAAM,CAAC,CAAC;AAC9C,oBAAI,GAAG;AACL,6BAAW,KAAK,CAAC;;yBAEV,KAAK,OAAO,CAAC,MAAM,KAAK;AACjC,6BAAa,WAAW,OAAO,KAAK,gBAAgB,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;yBAC/D,KAAK,MAAM,qBAAqB,GAAG;AAC5C,6BAAa,WAAW,OAAO,KAAK,KAAK,MAAM,IAAI,CAAC;;uBAE7C,MAAM,QAAQ,IAAI,GAAG;AAC9B,2BAAa,WAAW,OAAO,IAAI;uBAC1B,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,KAAK,OAAO,gBAAgB;AAC7F,2BAAa,WAAW,OAAQ,KAA+B,KAAK;mBAC/D;AACL,yBAAW,KAAK,IAAI;;;;;;;;;;;;AAGxB,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,WAAqB,MAAyB;AAC5D,eAAO,QAAQ,SAAS,WAAW;AACjC,iBAAO,KAAK,OAAO,IAAI;;AAEzB,eAAO,CAAC,CAAC;MACX;AAKO,MAAAA,UAAA,UAAA,SAAP,SAAc,MAAc;AAC1B,eAAO,KAAK;MACd;AAMO,MAAAA,UAAA,UAAA,aAAP,SAAkB,MAAc;AAC9B,eAAQ,KAAK,SAAS,KAAK,OAAO,SAAS,UAAU,SAAA,GAAC;AAAI,iBAAA,MAAM;QAAN,CAAU,IAAI;MAC1E;AAKO,MAAAA,UAAA,UAAA,SAAP,SAAc,MAAmB,OAAe;AAC9C,YAAI,MAAM,QAAQ;AAChB,eAAK,OAAO,KAAK;;AAEnB,aAAK,SAAS,KAAK,KAAK;AACxB,cAAM,SAAS;AACf,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,SAAP,SAAc,QAAkB,QAAgB;AAC9C,YAAI,OAAO,QAAQ;AACjB,eAAK,OAAO,MAAM;;AAEpB,YAAI,UAAU,OAAO,QAAQ;AAC3B,cAAM,IAAI,KAAK,WAAW,MAAM;AAChC,iBAAO,OAAO,SAAS,OAAO,GAAG,GAAG,MAAM;AAC1C,iBAAO,SAAS,OAAO;;MAE3B;AAKO,MAAAA,UAAA,UAAA,SAAP,SAAc,OAAe;AAC3B,YAAM,IAAI,KAAK,WAAW,KAAK;AAC/B,YAAI,KAAK,GAAG;AACV,gBAAM,OAAO,SAAS,OAAO,GAAG,CAAC;;AAEnC,cAAM,SAAS;AACf,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,UAAP,SAAe,OAAiB,OAAe;AAC7C,YAAM,IAAI,KAAK,WAAW,KAAK;AAC/B,YAAI,KAAK,GAAG;AACV,gBAAM,OAAO,SAAS,CAAC,IAAI;AAC3B,gBAAM,SAAS,MAAM;AACrB,gBAAM,SAAS;;AAEjB,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAiB;AAA9B,YAAA,QAAA;AACE,YAAM,QAAQ,IAAI,aAAA,YAAY,KAAK,IAAI;AACvC,cAAM,aAAU,SAAA,CAAA,GAAO,KAAK,UAAU;AACtC,cAAM,WAAW,KAAK,SAAS,IAAI,SAAA,GAAC;AAClC,cAAI,EAAE,SAAS,SAAS;AACtB,mBAAO,IAAI,UAAA,SAAU,EAAe,KAAK;qBAChC,EAAE,SAAS,YAAY;AAChC,mBAAO,IAAI,UAAA,YAAa,EAAkB,KAAK;iBAC1C;AACL,gBAAM,IAAI,MAAK,MAAM,CAAgB;AACrC,cAAE,SAAS;AACX,mBAAO;;QAEX,CAAC;AACD,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAgB,GAAS;AACpC,YAAM,OAAO,IAAI,UAAA,SAAS,KAAK,MAAM,MAAM,CAAC,CAAC;AAC7C,aAAK,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC;AAClC,aAAK,OAAO,SAAS,OAAO,KAAK,WAAW,IAAI,IAAI,GAAG,GAAG,IAAI;AAC9D,aAAK,SAAS,KAAK;AACnB,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,MAAc;AACxB,YAAM,SAAS,KAAK;AACpB,YAAI,CAAC;AAAQ,iBAAO;AACpB,YAAM,IAAI,KAAK,WAAW,IAAI,IAAI;AAClC,eAAQ,KAAK,KAAK,IAAI,OAAO,SAAS,SAAS,OAAO,SAAS,CAAC,IAAI;MACtE;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,MAAc;AAC5B,YAAM,SAAS,KAAK;AACpB,YAAI,CAAC;AAAQ,iBAAO;AACpB,YAAM,IAAI,KAAK,WAAW,IAAI,IAAI;AAClC,eAAQ,KAAK,IAAI,OAAO,SAAS,CAAC,IAAI;MACxC;AAKO,MAAAA,UAAA,UAAA,aAAP,SAAkB,MAAiB;AACjC,eAAO,KAAK,SAAS,CAAC;MACxB;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAiB;AAChC,eAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;MAC/C;AAKO,MAAAA,UAAA,UAAA,aAAP,SAAkB,MAAiB;AACjC,eAAA,cAAA,CAAA,GAAA,OAAW,KAAK,QAAQ,GAAA,KAAA;MAC1B;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAmB,GAAS;AAC3C,eAAO,KAAK,SAAS,CAAC;MACxB;AAKO,MAAAA,UAAA,UAAA,OAAP,SAAY,MAAc;AACxB,eAAO,KAAK;MACd;AAKO,MAAAA,UAAA,UAAA,QAAP,SAAa,MAAyB;AACpC,eAAQ,KAAK,SAAS,UAAW,KAAkB,QAC3C,KAAK,SAAS,aAAc,KAAqB,MAAM,QAAQ,2BAA2B,IAAI,IAAI;MAC5G;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAiB;AAApC,YAAA,QAAA;AACE,eAAO,KAAK,SAAS,OAAO,SAAC,GAAW,GAAW;AACjD,iBAAO,KAAK,EAAE,SAAS,UAAW,EAAe,QACrC,EAAE,SAAS,aAAa,KAAK,MAAK,YAAY,CAAgB;QAC5E,GAAG,EAAE;MACP;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAiB;AAChC,eAAO,KAAK,OAAO,eAAe,MAAM,IAAI;MAC9C;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAiB;AAChC,eAAO,KAAK,OAAO,UAAU,MAAM,IAAI;MACzC;AAKO,MAAAA,UAAA,UAAA,eAAP,SAAoB,MAAiB;AACnC,eAAO,KAAK,OAAO,UAAU,MAAM,MAAM,IAAI;MAC/C;AAKO,MAAAA,UAAA,UAAA,eAAP,SAAoB,MAAmB,MAAc,OAAwB,IAAiB;AAAjB,YAAA,OAAA,QAAA;AAAA,eAAA;QAAiB;AAC5F,YAAI,OAAO,UAAU,UAAU;AAC7B,kBAAQ,OAAO,KAAK;;AAEtB,YAAI,IAAI;AACN,iBAAO,GAAG,QAAQ,QAAQ,EAAE,IAAI,MAAM,KAAK,QAAQ,QAAQ,EAAE;;AAE/D,aAAK,WAAW,IAAI,IAAI;AACxB,YAAI,SAAS,SAAS;AACpB,eAAK,SAAS;;MAElB;AAKO,MAAAA,UAAA,UAAA,eAAP,SAAoB,MAAmB,MAAY;AACjD,eAAO,KAAK,WAAW,IAAI;MAC7B;AAKO,MAAAA,UAAA,UAAA,kBAAP,SAAuB,MAAmB,MAAY;AACpD,eAAO,KAAK,WAAW,IAAI;MAC7B;AAKO,MAAAA,UAAA,UAAA,eAAP,SAAoB,MAAmB,MAAY;AACjD,eAAO,KAAK,WAAW,eAAe,IAAI;MAC5C;AAKO,MAAAA,UAAA,UAAA,gBAAP,SAAqB,MAAiB;;AACpC,YAAM,aAAa,KAAK;AACxB,YAAM,OAAO,CAAA;;AACb,mBAAmB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,gBAAM,SAAI,GAAA;AACb,iBAAK,KAAK,EAAC,MAAM,QAAM,OAAO,WAAW,MAAI,EAAW,CAAC;;;;;;;;;;;AAE3D,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,MAAmB,MAAY;AAC7C,YAAM,WAAW,KAAK,WAAW,OAAO,KAAe,IAAI,MAAM,GAAG;AACpE,YAAI,CAAC,QAAQ,KAAK,SAAA,GAAC;AAAI,iBAAA,MAAM;QAAN,CAAU,GAAG;AAClC,kBAAQ,KAAK,IAAI;AACjB,eAAK,WAAW,OAAO,IAAI,QAAQ,KAAK,GAAG;;MAE/C;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAmB,MAAY;AAChD,YAAM,WAAW,KAAK,WAAW,OAAO,KAAe,IAAI,MAAM,GAAG;AACpE,YAAM,IAAI,QAAQ,UAAU,SAAA,GAAC;AAAI,iBAAA,MAAM;QAAN,CAAU;AAC3C,YAAI,KAAK,GAAG;AACV,kBAAQ,OAAO,GAAG,CAAC;AACnB,eAAK,WAAW,OAAO,IAAI,QAAQ,KAAK,GAAG;;MAE/C;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,MAAmB,MAAY;AAC7C,YAAM,WAAW,KAAK,WAAW,OAAO,KAAe,IAAI,MAAM,GAAG;AACpE,eAAO,CAAC,CAAC,QAAQ,KAAK,SAAA,GAAC;AAAI,iBAAA,MAAM;QAAN,CAAU;MACvC;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,MAAmB,MAAc,OAAa;AAC5D,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,IAAI,YAAA,OAAO,KAAK,aAAa,MAAM,OAAO,CAAC;;AAE3D,aAAK,OAAO,IAAI,MAAM,KAAK;AAC3B,aAAK,WAAW,OAAO,IAAI,KAAK,OAAO;MACzC;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,MAAmB,MAAY;AAC7C,YAAI,CAAC,KAAK,QAAQ;AAChB,cAAM,QAAQ,KAAK,aAAa,MAAM,OAAO;AAC7C,cAAI,CAAC,OAAO;AACV,mBAAO;;AAET,eAAK,SAAS,IAAI,YAAA,OAAO,KAAK;;AAEhC,eAAO,KAAK,OAAO,IAAI,IAAI;MAC7B;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAiB;AAChC,eAAO,KAAK,aAAa,MAAM,OAAO;MACxC;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,MAAmB,OAAe;AACnD,aAAK,WAAW,CAAC,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,SAAS,KAAK,YAAY,IAAI,CAAC,CAAC;MAClF;AAUO,MAAAA,UAAA,UAAA,WAAP,SAAgB,OAAkB;AAChC,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,aAAP,SAAkB,OAAkB;AAClC,eAAO;MACT;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,OAAoB,KAAiB,QAAsB;AAAvC,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAe;AAAE,YAAA,WAAA,QAAA;AAAA,mBAAA;QAAsB;AACzE,eAAO,CAAC,GAAG,CAAC;MACd;AAKO,MAAAA,UAAA,UAAA,WAAP,SAAgB,OAAkB;AAChC,eAAO,EAAC,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAC;MAC9C;AAEF,aAAAA;IAAA,EAliB8B,gBAAA,kBAAkB;AAAnC,YAAA,WAAA;AAuiBb,QAAA,cAAA,SAAA,QAAA;AAAiC,gBAAAC,cAAA,MAAA;AAAjC,eAAAA,eAAA;;MAAkH;AAAA,aAAAA;IAAA,GAAjF,GAAA,eAAA,WAAsE,QAAQ,CAAC;AAAnG,YAAA,cAAA;AASb,aAAgB,YAAY,SAA0B;AAA1B,UAAA,YAAA,QAAA;AAAA,kBAAA;MAA0B;AACpD,aAAO,IAAI,YAAY,MAAM,OAAO;IACtC;AAFA,YAAA,cAAA;;;", "names": ["AbstractDOMAdaptor", "LiteElement", "LiteDocument", "LiteText", "LiteComment", "LiteList", "PATTERNS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LiteWindow", "LiteBase", "LiteAdaptor"]}