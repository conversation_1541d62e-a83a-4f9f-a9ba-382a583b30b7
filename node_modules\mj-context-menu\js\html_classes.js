"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HtmlAttrs = exports.HtmlClasses = void 0;
var PREFIX = 'CtxtMenu';
function prefix_(name) {
    return PREFIX + '_' + name;
}
function prefixClass_(name) {
    return prefix_(name);
}
function prefixAttr_(name) {
    return prefix_(name);
}
exports.HtmlClasses = {
    ATTACHED: prefixClass_('Attached'),
    CONTEXTMENU: prefixClass_('ContextMenu'),
    MENU: prefixClass_('Menu'),
    MENUARROW: prefixClass_('MenuArrow'),
    MENUACTIVE: prefixClass_('MenuActive'),
    MENUCHECK: prefixClass_('MenuCheck'),
    MENUCLOSE: prefixClass_('MenuClose'),
    MENUCOMBOBOX: prefixClass_('MenuComboBox'),
    MENUDISABLED: prefixClass_('MenuDisabled'),
    MENUFRAME: prefixClass_('MenuFrame'),
    MENUITEM: prefixClass_('MenuItem'),
    MENULABEL: prefixClass_('MenuLabel'),
    MENURADIOCHECK: prefixClass_('MenuRadioCheck'),
    MENUINPUTBOX: prefixClass_('MenuInputBox'),
    MENURULE: prefixClass_('MenuRule'),
    MENUSLIDER: prefixClass_('MenuSlider'),
    MOUSEPOST: prefixClass_('MousePost'),
    RTL: prefixClass_('RTL'),
    INFO: prefixClass_('Info'),
    INFOCLOSE: prefixClass_('InfoClose'),
    INFOCONTENT: prefixClass_('InfoContent'),
    INFOSIGNATURE: prefixClass_('InfoSignature'),
    INFOTITLE: prefixClass_('InfoTitle'),
    SLIDERVALUE: prefixClass_('SliderValue'),
    SLIDERBAR: prefixClass_('SliderBar'),
    SELECTION: prefixClass_('Selection'),
    SELECTIONBOX: prefixClass_('SelectionBox'),
    SELECTIONMENU: prefixClass_('SelectionMenu'),
    SELECTIONDIVIDER: prefixClass_('SelectionDivider'),
    SELECTIONITEM: prefixClass_('SelectionItem')
};
exports.HtmlAttrs = {
    COUNTER: prefixAttr_('Counter'),
    KEYDOWNFUNC: prefixAttr_('keydownFunc'),
    CONTEXTMENUFUNC: prefixAttr_('contextmenuFunc'),
    OLDTAB: prefixAttr_('Oldtabindex'),
    TOUCHFUNC: prefixAttr_('TouchFunc'),
};
//# sourceMappingURL=html_classes.js.map