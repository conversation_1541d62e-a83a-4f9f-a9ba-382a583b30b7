import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/mathjax-full/js/util/Options.js
var require_Options = __commonJS({
  "node_modules/mathjax-full/js/util/Options.js"(exports) {
    "use strict";
    var __values = exports && exports.__values || function(o) {
      var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && typeof o.length === "number") return {
        next: function() {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    var __read = exports && exports.__read || function(o, n) {
      var m = typeof Symbol === "function" && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = { error };
      } finally {
        try {
          if (r && !r.done && (m = i["return"])) m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    var __spreadArray = exports && exports.__spreadArray || function(to, from, pack) {
      if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i);
          ar[i] = from[i];
        }
      }
      return to.concat(ar || Array.prototype.slice.call(from));
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.lookup = exports.separateOptions = exports.selectOptionsFromKeys = exports.selectOptions = exports.userOptions = exports.defaultOptions = exports.insert = exports.copy = exports.keys = exports.makeArray = exports.expandable = exports.Expandable = exports.OPTIONS = exports.REMOVE = exports.APPEND = exports.isObject = void 0;
    var OBJECT = {}.constructor;
    function isObject(obj) {
      return typeof obj === "object" && obj !== null && (obj.constructor === OBJECT || obj.constructor === Expandable);
    }
    exports.isObject = isObject;
    exports.APPEND = "[+]";
    exports.REMOVE = "[-]";
    exports.OPTIONS = {
      invalidOption: "warn",
      optionError: function(message, _key) {
        if (exports.OPTIONS.invalidOption === "fatal") {
          throw new Error(message);
        }
        console.warn("MathJax: " + message);
      }
    };
    var Expandable = /* @__PURE__ */ function() {
      function Expandable2() {
      }
      return Expandable2;
    }();
    exports.Expandable = Expandable;
    function expandable(def) {
      return Object.assign(Object.create(Expandable.prototype), def);
    }
    exports.expandable = expandable;
    function makeArray(x) {
      return Array.isArray(x) ? x : [x];
    }
    exports.makeArray = makeArray;
    function keys(def) {
      if (!def) {
        return [];
      }
      return Object.keys(def).concat(Object.getOwnPropertySymbols(def));
    }
    exports.keys = keys;
    function copy(def) {
      var e_1, _a;
      var props = {};
      try {
        for (var _b = __values(keys(def)), _c = _b.next(); !_c.done; _c = _b.next()) {
          var key = _c.value;
          var prop = Object.getOwnPropertyDescriptor(def, key);
          var value = prop.value;
          if (Array.isArray(value)) {
            prop.value = insert([], value, false);
          } else if (isObject(value)) {
            prop.value = copy(value);
          }
          if (prop.enumerable) {
            props[key] = prop;
          }
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
      return Object.defineProperties(def.constructor === Expandable ? expandable({}) : {}, props);
    }
    exports.copy = copy;
    function insert(dst, src, warn) {
      var e_2, _a;
      if (warn === void 0) {
        warn = true;
      }
      var _loop_1 = function(key2) {
        if (warn && dst[key2] === void 0 && dst.constructor !== Expandable) {
          if (typeof key2 === "symbol") {
            key2 = key2.toString();
          }
          exports.OPTIONS.optionError('Invalid option "'.concat(key2, '" (no default value).'), key2);
          return "continue";
        }
        var sval = src[key2], dval = dst[key2];
        if (isObject(sval) && dval !== null && (typeof dval === "object" || typeof dval === "function")) {
          var ids = keys(sval);
          if (Array.isArray(dval) && (ids.length === 1 && (ids[0] === exports.APPEND || ids[0] === exports.REMOVE) && Array.isArray(sval[ids[0]]) || ids.length === 2 && ids.sort().join(",") === exports.APPEND + "," + exports.REMOVE && Array.isArray(sval[exports.APPEND]) && Array.isArray(sval[exports.REMOVE]))) {
            if (sval[exports.REMOVE]) {
              dval = dst[key2] = dval.filter(function(x) {
                return sval[exports.REMOVE].indexOf(x) < 0;
              });
            }
            if (sval[exports.APPEND]) {
              dst[key2] = __spreadArray(__spreadArray([], __read(dval), false), __read(sval[exports.APPEND]), false);
            }
          } else {
            insert(dval, sval, warn);
          }
        } else if (Array.isArray(sval)) {
          dst[key2] = [];
          insert(dst[key2], sval, false);
        } else if (isObject(sval)) {
          dst[key2] = copy(sval);
        } else {
          dst[key2] = sval;
        }
      };
      try {
        for (var _b = __values(keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {
          var key = _c.value;
          _loop_1(key);
        }
      } catch (e_2_1) {
        e_2 = { error: e_2_1 };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
        } finally {
          if (e_2) throw e_2.error;
        }
      }
      return dst;
    }
    exports.insert = insert;
    function defaultOptions(options) {
      var defs = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        defs[_i - 1] = arguments[_i];
      }
      defs.forEach(function(def) {
        return insert(options, def, false);
      });
      return options;
    }
    exports.defaultOptions = defaultOptions;
    function userOptions(options) {
      var defs = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        defs[_i - 1] = arguments[_i];
      }
      defs.forEach(function(def) {
        return insert(options, def, true);
      });
      return options;
    }
    exports.userOptions = userOptions;
    function selectOptions(options) {
      var e_3, _a;
      var keys2 = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        keys2[_i - 1] = arguments[_i];
      }
      var subset = {};
      try {
        for (var keys_1 = __values(keys2), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {
          var key = keys_1_1.value;
          if (options.hasOwnProperty(key)) {
            subset[key] = options[key];
          }
        }
      } catch (e_3_1) {
        e_3 = { error: e_3_1 };
      } finally {
        try {
          if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);
        } finally {
          if (e_3) throw e_3.error;
        }
      }
      return subset;
    }
    exports.selectOptions = selectOptions;
    function selectOptionsFromKeys(options, object) {
      return selectOptions.apply(void 0, __spreadArray([options], __read(Object.keys(object)), false));
    }
    exports.selectOptionsFromKeys = selectOptionsFromKeys;
    function separateOptions(options) {
      var e_4, _a, e_5, _b;
      var objects = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        objects[_i - 1] = arguments[_i];
      }
      var results = [];
      try {
        for (var objects_1 = __values(objects), objects_1_1 = objects_1.next(); !objects_1_1.done; objects_1_1 = objects_1.next()) {
          var object = objects_1_1.value;
          var exists = {}, missing = {};
          try {
            for (var _c = (e_5 = void 0, __values(Object.keys(options || {}))), _d = _c.next(); !_d.done; _d = _c.next()) {
              var key = _d.value;
              (object[key] === void 0 ? missing : exists)[key] = options[key];
            }
          } catch (e_5_1) {
            e_5 = { error: e_5_1 };
          } finally {
            try {
              if (_d && !_d.done && (_b = _c.return)) _b.call(_c);
            } finally {
              if (e_5) throw e_5.error;
            }
          }
          results.push(exists);
          options = missing;
        }
      } catch (e_4_1) {
        e_4 = { error: e_4_1 };
      } finally {
        try {
          if (objects_1_1 && !objects_1_1.done && (_a = objects_1.return)) _a.call(objects_1);
        } finally {
          if (e_4) throw e_4.error;
        }
      }
      results.unshift(options);
      return results;
    }
    exports.separateOptions = separateOptions;
    function lookup(name, lookup2, def) {
      if (def === void 0) {
        def = null;
      }
      return lookup2.hasOwnProperty(name) ? lookup2[name] : def;
    }
    exports.lookup = lookup;
  }
});

export {
  require_Options
};
//# sourceMappingURL=chunk-FQVVVHII.js.map
