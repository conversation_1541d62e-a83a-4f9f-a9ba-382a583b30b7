{"version": 3, "file": "Notation.js", "sourceRoot": "", "sources": ["../../../ts/output/common/Notation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA4Ba,QAAA,MAAM,GAAG,CAAC,EAAE,QAAA,OAAO,GAAG,CAAC,EAAE,QAAA,MAAM,GAAG,CAAC,CAAC;AAEpC,QAAA,SAAS,GAAG,IAAI,CAAC;AACjB,QAAA,OAAO,GAAG,EAAE,CAAC;AAEb,QAAA,KAAK,GAAG,iBAAS,GAAG,UAAU,CAAC;AA+D/B,QAAA,SAAS,GAAG,EAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC,CAAC;AAEnD,QAAA,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAW,CAAC;AAK7C,QAAA,QAAQ,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,EAAhD,CAAgD,CAA2B,CAAC;AAClG,QAAA,WAAW,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAA/B,CAA+B,CAA2B,CAAC;AACpF,QAAA,UAAU,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAjC,CAAiC,CAAyB,CAAC;AAOzF,IAAM,SAAS,GAAG,UAAC,IAAc;IACtC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAKK,IAAM,WAAW,GAAG,UAAC,IAAc,EAAE,IAAiB;IAC3D,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;QAChB,IAAA,KAAS,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAApC,CAAC,OAAA,EAAE,CAAC,OAAgC,CAAC;QAC5C,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAClF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AANW,QAAA,WAAW,eAMtB;AAKK,IAAM,UAAU,GAAG,UAAC,IAAc,EAAE,IAAiB;IAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;QACf,IAAA,CAAC,GAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAhC,CAAiC;QACzC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5E;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AANW,QAAA,UAAU,cAMrB;AAMW,QAAA,QAAQ,GAAG;IACtB,EAAE,EAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAG,gBAAgB,CAAC;IACzD,IAAI,EAAO,CAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAG,gBAAgB,CAAC;IACzD,KAAK,EAAM,CAAE,CAAC,EAAY,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC;IAC3D,IAAI,EAAO,CAAE,IAAI,CAAC,EAAE,EAAM,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC;IAC3D,MAAM,EAAK,CAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,EAAG,IAAI,EAAG,kCAAkC,CAAC;IAC3E,SAAS,EAAE,CAAE,CAAC,EAAY,IAAI,EAAG,KAAK,EAAE,uCAAuC,CAAC;CACzB,CAAC;AAM7C,QAAA,gBAAgB,GAAG;IAC9B,UAAU,EAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAQ,KAAK,EAAE,iCAAiC,CAAC;IAC3E,SAAS,EAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAQ,KAAK,EAAE,kCAAkC,CAAC;IAC5E,SAAS,EAAW,CAAE,CAAC,EAAE,CAAC,EAAQ,KAAK,EAAE,oBAAoB,CAAC;IAC9D,SAAS,EAAW,CAAE,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,oBAAoB,CAAC;IAC9D,SAAS,EAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,kBAAkB,CAAC;IAC5D,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAQ,IAAI,EAAG,gEAAgE,CAAC;IAC1G,kBAAkB,EAAE,CAAE,CAAC,EAAE,CAAC,EAAQ,IAAI,EAAG,kDAAkD,CAAC;CACtC,CAAC;AAK5C,QAAA,SAAS,GAAG;IACvB,EAAE,EAAK,UAAC,IAAI,IAAK,OAAA,IAAA,kBAAU,EAAC,IAAI,EAAE,CAAC,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAvD,CAAuD;IACxE,IAAI,EAAG,UAAC,IAAI,IAAK,OAAA,IAAA,kBAAU,EAAC,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAvD,CAAuD;IACxE,KAAK,EAAE,UAAC,IAAI,IAAK,OAAA,IAAA,mBAAW,EAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAxD,CAAwD;IACzE,IAAI,EAAG,UAAC,IAAI,IAAK,OAAA,IAAA,mBAAW,EAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,CAAC,EAAxD,CAAwD;IACzE,MAAM,EAAK,UAAC,IAAI,IAAK,OAAA,IAAA,kBAAU,EAAC,IAAI,EAAE,CAAC,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAA1D,CAA0D;IAC/E,SAAS,EAAE,UAAC,IAAI,IAAK,OAAA,IAAA,mBAAW,EAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,CAAC,EAA3D,CAA2D;CACrC,CAAC;AASvC,IAAM,YAAY,GAAG,UAAgC,MAAsB;IAKhF,OAAO,UAAC,IAAU;QAChB,IAAM,CAAC,GAAG,iBAAS,CAAC,IAAI,CAAC,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE;gBAIZ,QAAQ,EAAE,MAAM;gBAIhB,IAAI,EAAE,UAAC,IAAI;oBACT,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAgB,CAAC;oBACzC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;oBACxC,OAAO,IAAI,CAAC;gBACd,CAAC;gBAID,MAAM,EAAE,UAAC,IAAI;oBACX,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAgB,CAAC;oBACzC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;oBACzB,OAAO,IAAI,CAAC;gBACd,CAAC;aACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AA9BW,QAAA,YAAY,gBA8BvB;AAOK,IAAM,aAAa,GAAG,UAAgC,MAAsB;IAQjF,OAAO,UAAC,IAAY,EAAE,KAAW,EAAE,KAAW;QAC5C,IAAM,EAAE,GAAG,iBAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAM,EAAE,GAAG,iBAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,OAAO,CAAC,IAAI,EAAE;gBAIZ,QAAQ,EAAE,MAAM;gBAIhB,IAAI,EAAE,UAAC,IAAI;oBACT,IAAM,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;oBACxC,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAgB,CAAC;oBACzC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,CAAC;gBAID,MAAM,EAAE,UAAC,IAAI;oBACX,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAgB,CAAC;oBACzC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;oBACrC,OAAO,IAAI,CAAC;gBACd,CAAC;gBAID,MAAM,EAAE,KAAK,GAAG,GAAG,GAAG,KAAK;aAC5B,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AAvCW,QAAA,aAAa,iBAuCxB;AAQK,IAAM,oBAAoB,GAAG,UAAgC,MAAyC;IAM3G,OAAO,UAAC,IAAY;QAClB,IAAM,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QACjD,OAAO,CAAC,IAAI,GAAG,gBAAgB,EAAE;gBAI/B,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC;gBAIvB,IAAI,EAAE,gBAAQ;aACf,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AAnBW,QAAA,oBAAoB,wBAmB/B;AAQK,IAAM,mBAAmB,GAAG,UAAgC,MAAsB;IAKvF,OAAO,UAAC,IAAY;QACZ,IAAA,KAAA,OAA0B,wBAAgB,CAAC,IAAI,CAAC,IAAA,EAA/C,CAAC,QAAA,EAAE,EAAE,QAAA,EAAE,MAAM,QAAA,EAAE,MAAM,QAA0B,CAAC;QACvD,OAAO,CAAC,IAAI,GAAG,OAAO,EAAE;gBAKtB,QAAQ,EAAE,UAAC,IAAI,EAAE,MAAM;oBACf,IAAA,KAAA,OAAS,IAAI,CAAC,OAAO,EAAE,IAAA,EAAtB,CAAC,QAAA,EAAE,CAAC,QAAkB,CAAC;oBAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;oBACjD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACtB,CAAC;gBAID,IAAI,EAAE,UAAC,IAAI;oBACH,IAAA,KAAY,IAAI,CAAC,SAAS,EAAE,EAA3B,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAoB,CAAC;oBAC7B,IAAA,KAAA,OAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAA,EAAtE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,GAAG,QAA2D,CAAC;oBACxE,IAAA,KAAA,OAAU,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC,IAAA,EAArC,CAAC,QAAA,EAAE,EAAE,QAAgC,CAAC;oBAC7C,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/F,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC1B,CAAC;gBAID,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,mBAAmB,uBAkC9B;AAMK,IAAM,WAAW,GAAG,UAAgC,MAAsB;IAK/E,OAAO,UAAC,IAAY;QACZ,IAAA,KAAA,OAAsC,gBAAQ,CAAC,IAAI,CAAC,IAAA,EAAnD,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,UAAU,QAAA,EAAE,MAAM,QAAkB,CAAC;QAC3D,OAAO,CAAC,IAAI,GAAG,OAAO,EAAE;gBAKtB,QAAQ,EAAE,UAAC,IAAI,EAAE,MAAM;oBACf,IAAA,KAAY,IAAI,CAAC,OAAO,EAAE,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;oBAC3B,IAAA,KAAA,OAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAA,EAAnD,CAAC,QAAA,EAAE,MAAM,QAA0C,CAAC;oBAC3D,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;oBACvD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACtB,CAAC;gBAID,IAAI,EAAE,iBAAS,CAAC,IAAI,CAAC;gBAIrB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AA7BW,QAAA,WAAW,eA6BtB"}