export declare const HtmlClasses: {
    [id: string]: HtmlClass;
};
export declare type HtmlClass = 'CtxtMenu_ContextMenu' | 'CtxtMenu_Menu' | 'CtxtMenu_MenuArrow' | 'CtxtMenu_MenuActive' | 'CtxtMenu_MenuCheck' | 'CtxtMenu_MenuClose' | 'CtxtMenu_MenuDisabled' | 'CtxtMenu_MenuItem' | 'CtxtMenu_MenuLabel' | 'CtxtMenu_MenuRadioCheck' | 'CtxtMenu_MenuRule' | 'CtxtMenu_MousePost' | 'CtxtMenu_RTL' | 'CtxtMenu_Attached' | 'CtxtMenu_Info' | 'CtxtMenu_InfoClose' | 'CtxtMenu_InfoContent' | 'CtxtMenu_InfoSignature' | 'CtxtMenu_InfoTitle' | 'CtxtMenu_MenuFrame' | 'CtxtMenu_MenuInputBox' | 'CtxtMenu_MenuSlider' | 'CtxtMenu_Selection' | 'CtxtMenu_SelectionBox' | 'CtxtMenu_SelectionItem' | 'CtxtMenu_SelectionDivider' | 'CtxtMenu_SelectionMenu';
export declare const HtmlAttrs: {
    [id: string]: HtmlAttr;
};
export declare type HtmlAttr = 'CtxtMenu_Counter' | 'CtxtMenu_keydownFunc' | 'CtxtMenu_contextmenuFunc' | 'CtxtMenu_touchstartFunc' | 'CtxtMenu_Oldtabindex';
