{"version": 3, "sources": ["../../mathjax-full/ts/util/Options.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements functions for handling option lists\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n\n/*****************************************************************/\n/* tslint:disable-next-line:jsdoc-require */\nconst OBJECT = {}.constructor;\n\n/**\n *  Check if an object is an object literal (as opposed to an instance of a class)\n */\nexport function isObject(obj: any) {\n  return typeof obj === 'object' && obj !== null &&\n    (obj.constructor === OBJECT || obj.constructor === Expandable);\n}\n\n/*****************************************************************/\n/**\n * Generic list of options\n */\nexport type OptionList = {[name: string]: any};\n\n/*****************************************************************/\n/**\n *  Used to append an array to an array in default options\n *  E.g., an option of the form\n *\n *    {\n *      name: {[APPEND]: [1, 2, 3]}\n *    }\n *\n *  where 'name' is an array in the default options would end up with name having its\n *  original value with 1, 2, and 3 appended.\n */\nexport const APPEND = '[+]';\n\n/**\n *  Used to remove elements from an array in default options\n *  E.g., an option of the form\n *\n *    {\n *      name: {[REMOVE]: [2]}\n *    }\n *\n *  where 'name' is an array in the default options would end up with name having its\n *  original value but with any entry of 2 removed  So if the original value was [1, 2, 3, 2],\n *  then the final value will be [1, 3] instead.\n */\nexport const REMOVE = '[-]';\n\n\n/**\n *  Provides options for the option utlities.\n */\nexport const OPTIONS = {\n  invalidOption: 'warn' as ('fatal' | 'warn'),\n  /**\n   * Function to report messages for invalid options\n   *\n   * @param {string} message   The message for the invalid parameter.\n   * @param {string} key       The invalid key itself.\n   */\n  optionError: (message: string, _key: string) => {\n    if (OPTIONS.invalidOption === 'fatal') {\n      throw new Error(message);\n    }\n    console.warn('MathJax: ' + message);\n  }\n};\n\n\n/**\n * A Class to use for options that should not produce warnings if an undefined key is used\n */\nexport class Expandable {}\n\n/**\n * Produces an instance of Expandable with the given values (to be used in defining options\n * that can use keys that don't have default values).  E.g., default options of the form:\n *\n *  OPTIONS = {\n *     types: expandable({\n *       a: 1,\n *       b: 2\n *     })\n *  }\n *\n *  would allow user options of\n *\n *  {\n *     types: {\n *       c: 3\n *     }\n *  }\n *\n *  without reporting an error.\n */\nexport function expandable(def: OptionList) {\n  return Object.assign(Object.create(Expandable.prototype), def);\n}\n\n/*****************************************************************/\n/**\n *  Make sure an option is an Array\n */\nexport function makeArray(x: any): any[] {\n  return Array.isArray(x) ? x : [x];\n}\n\n/*****************************************************************/\n/**\n * Get all keys and symbols from an object\n *\n * @param {Optionlist} def        The object whose keys are to be returned\n * @return {(string | symbol)[]}  The list of keys for the object\n */\nexport function keys(def: OptionList): (string | symbol)[] {\n  if (!def) {\n    return [];\n  }\n  return (Object.keys(def) as (string | symbol)[]).concat(Object.getOwnPropertySymbols(def));\n}\n\n/*****************************************************************/\n/**\n * Make a deep copy of an object\n *\n * @param {OptionList} def  The object to be copied\n * @return {OptionList}     The copy of the object\n */\nexport function copy(def: OptionList): OptionList {\n  let props: OptionList = {};\n  for (const key of keys(def)) {\n    let prop = Object.getOwnPropertyDescriptor(def, key);\n    let value = prop.value;\n    if (Array.isArray(value)) {\n      prop.value = insert([], value, false);\n    } else if (isObject(value)) {\n      prop.value = copy(value);\n    }\n    if (prop.enumerable) {\n      props[key as string] = prop;\n    }\n  }\n  return Object.defineProperties(def.constructor === Expandable ? expandable({}) : {}, props);\n}\n\n/*****************************************************************/\n/**\n * Insert one object into another (with optional warnings about\n * keys that aren't in the original)\n *\n * @param {OptionList} dst  The option list to merge into\n * @param {OptionList} src  The options to be merged\n * @param {boolean} warn    True if a warning should be issued for a src option that isn't already in dst\n * @return {OptionList}     The modified destination option list (dst)\n */\nexport function insert(dst: OptionList, src: OptionList, warn: boolean = true): OptionList {\n  for (let key of keys(src) as string[]) {\n    //\n    // Check if the key is valid (i.e., is in the defaults or in an expandable block)\n    //\n    if (warn && dst[key] === undefined && dst.constructor !== Expandable) {\n      if (typeof key === 'symbol') {\n        key = (key as symbol).toString();\n      }\n      OPTIONS.optionError(`Invalid option \"${key}\" (no default value).`, key);\n      continue;\n    }\n    //\n    // Shorthands for the source and destination values\n    //\n    let sval = src[key], dval = dst[key];\n    //\n    // If the source is an object literal and the destination exists and is either an\n    //   object or a function (so can have properties added to it)...\n    //\n    if (isObject(sval) && dval !== null &&\n        (typeof dval === 'object' || typeof dval === 'function')) {\n      const ids = keys(sval);\n      //\n      // Check for APPEND or REMOVE objects:\n      //\n      if (\n        //\n        // If the destination value is an array...\n        //\n        Array.isArray(dval) &&\n          (\n            //\n            // If there is only one key and it is APPEND or REMOVE and the keys value is an array...\n            //\n            (ids.length === 1 && (ids[0] === APPEND || ids[0] === REMOVE) && Array.isArray(sval[ids[0]])) ||\n              //\n              // Or if there are two keys and they are APPEND and REMOVE and both keys' values\n              //   are arrays...\n              //\n              (ids.length === 2 && ids.sort().join(',') === APPEND + ',' + REMOVE &&\n               Array.isArray(sval[APPEND]) && Array.isArray(sval[REMOVE]))\n          )\n      ) {\n        //\n        // Then remove any values to be removed\n        //\n        if (sval[REMOVE]) {\n          dval = dst[key] = dval.filter(x => sval[REMOVE].indexOf(x) < 0);\n        }\n        //\n        // And append any values to be added (make a copy so as not to modify the original)\n        //\n        if (sval[APPEND]) {\n          dst[key] = [...dval, ...sval[APPEND]];\n        }\n      } else {\n        //\n        // Otherwise insert the values of the source object into the destination object\n        //\n        insert(dval, sval, warn);\n      }\n    } else if (Array.isArray(sval)) {\n      //\n      // If the source is an array, replace the destination with an empty array\n      //   and copy the source values into it.\n      //\n      dst[key] = [];\n      insert(dst[key], sval, false);\n    } else if (isObject(sval)) {\n      //\n      // If the source is an object literal, set the destination to a copy of it\n      //\n      dst[key] = copy(sval);\n    } else {\n      //\n      // Otherwise set the destination to the source value\n      //\n      dst[key] = sval;\n    }\n  }\n  return dst;\n}\n\n/*****************************************************************/\n/**\n * Merge options without warnings (so we can add new default values into an\n * existing default list)\n *\n * @param {OptionList} options  The option list to be merged into\n * @param {OptionList[]} defs   The option lists to merge into the first one\n * @return {OptionList}         The modified options list\n */\nexport function defaultOptions(options: OptionList, ...defs: OptionList[]): OptionList {\n  defs.forEach(def => insert(options, def, false));\n  return options;\n}\n\n/*****************************************************************/\n/**\n * Merge options with warnings about undefined ones (so we can merge\n * user options into the default list)\n *\n * @param {OptionList} options  The option list to be merged into\n * @param {OptionList[]} defs   The option lists to merge into the first one\n * @return {OptionList}         The modified options list\n */\nexport function userOptions(options: OptionList, ...defs: OptionList[]): OptionList {\n  defs.forEach(def => insert(options, def, true));\n  return options;\n}\n\n/*****************************************************************/\n/**\n * Select a subset of options by key name\n *\n * @param {OptionList} options  The option list from which option values will be taken\n * @param {string[]} keys       The names of the options to extract\n * @return {OptionList}         The option list consisting of only the ones whose keys were given\n */\nexport function selectOptions(options: OptionList, ...keys: string[]): OptionList {\n  let subset: OptionList = {};\n  for (const key of keys) {\n    if (options.hasOwnProperty(key)) {\n      subset[key] = options[key];\n    }\n  }\n  return subset;\n}\n\n/*****************************************************************/\n/**\n * Select a subset of options by keys from an object\n *\n * @param {OptionList} options  The option list from which the option values will be taken\n * @param {OptionList} object   The option list whose keys will be used to select the options\n * @return {OptionList}         The option list consisting of the option values from the first\n *                               list whose keys are those from the second list.\n */\nexport function selectOptionsFromKeys(options: OptionList, object: OptionList): OptionList {\n  return selectOptions(options, ...Object.keys(object));\n}\n\n/*****************************************************************/\n/**\n *  Separate options into sets: the ones having the same keys\n *  as the second object, the third object, etc, and the ones that don't.\n *  (Used to separate an option list into the options needed for several\n *   subobjects.)\n *\n * @param {OptionList} options    The option list to be split into parts\n * @param {OptionList[]} objects  The list of option lists whose keys are used to break up\n *                                 the original options into separate pieces.\n * @return {OptionList[]}         The option lists taken from the original based on the\n *                                 keys of the other objects.  The first one in the list\n *                                 consists of the values not appearing in any of the others\n *                                 (i.e., whose keys were not in any of the others).\n */\nexport function separateOptions(options: OptionList, ...objects: OptionList[]): OptionList[] {\n  let results: OptionList[] = [];\n  for (const object of objects) {\n    let exists: OptionList = {}, missing: OptionList = {};\n    for (const key of Object.keys(options || {})) {\n      (object[key] === undefined ? missing : exists)[key] = options[key];\n    }\n    results.push(exists);\n    options = missing;\n  }\n  results.unshift(options);\n  return results;\n}\n\n\n/*****************************************************************/\n/**\n *  Look up a value from object literal, being sure it is an\n *  actual property (not inherited), with a default if not found.\n *\n * @param {string} name         The name of the key to look up.\n * @param {OptionList} lookup   The list of options to check.\n * @param {any} def             The default value if the key isn't found.\n */\nexport function lookup(name: string, lookup: OptionList, def: any = null) {\n  return (lookup.hasOwnProperty(name) ? lookup[name] : def);\n}\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,QAAM,SAAS,CAAA,EAAG;AAKlB,aAAgB,SAAS,KAAQ;AAC/B,aAAO,OAAO,QAAQ,YAAY,QAAQ,SACvC,IAAI,gBAAgB,UAAU,IAAI,gBAAgB;IACvD;AAHA,YAAA,WAAA;AAuBa,YAAA,SAAS;AAcT,YAAA,SAAS;AAMT,YAAA,UAAU;MACrB,eAAe;MAOf,aAAa,SAAC,SAAiB,MAAY;AACzC,YAAI,QAAA,QAAQ,kBAAkB,SAAS;AACrC,gBAAM,IAAI,MAAM,OAAO;;AAEzB,gBAAQ,KAAK,cAAc,OAAO;MACpC;;AAOF,QAAA,aAAA,2BAAA;AAAA,eAAAA,cAAA;MAAyB;AAAA,aAAAA;IAAA,EAAzB;AAAa,YAAA,aAAA;AAuBb,aAAgB,WAAW,KAAe;AACxC,aAAO,OAAO,OAAO,OAAO,OAAO,WAAW,SAAS,GAAG,GAAG;IAC/D;AAFA,YAAA,aAAA;AAQA,aAAgB,UAAU,GAAM;AAC9B,aAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;IAClC;AAFA,YAAA,YAAA;AAWA,aAAgB,KAAK,KAAe;AAClC,UAAI,CAAC,KAAK;AACR,eAAO,CAAA;;AAET,aAAQ,OAAO,KAAK,GAAG,EAA0B,OAAO,OAAO,sBAAsB,GAAG,CAAC;IAC3F;AALA,YAAA,OAAA;AAcA,aAAgB,KAAK,KAAe;;AAClC,UAAI,QAAoB,CAAA;;AACxB,iBAAkB,KAAA,SAAA,KAAK,GAAG,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAxB,cAAM,MAAG,GAAA;AACZ,cAAI,OAAO,OAAO,yBAAyB,KAAK,GAAG;AACnD,cAAI,QAAQ,KAAK;AACjB,cAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAK,QAAQ,OAAO,CAAA,GAAI,OAAO,KAAK;qBAC3B,SAAS,KAAK,GAAG;AAC1B,iBAAK,QAAQ,KAAK,KAAK;;AAEzB,cAAI,KAAK,YAAY;AACnB,kBAAM,GAAa,IAAI;;;;;;;;;;;;AAG3B,aAAO,OAAO,iBAAiB,IAAI,gBAAgB,aAAa,WAAW,CAAA,CAAE,IAAI,CAAA,GAAI,KAAK;IAC5F;AAfA,YAAA,OAAA;AA2BA,aAAgB,OAAO,KAAiB,KAAiB,MAAoB;;AAApB,UAAA,SAAA,QAAA;AAAA,eAAA;MAAoB;6BAClEC,MAAG;AAIV,YAAI,QAAQ,IAAIA,IAAG,MAAM,UAAa,IAAI,gBAAgB,YAAY;AACpE,cAAI,OAAOA,SAAQ,UAAU;AAC3B,YAAAA,OAAOA,KAAe,SAAQ;;AAEhC,kBAAA,QAAQ,YAAY,mBAAA,OAAmBA,MAAG,uBAAA,GAAyBA,IAAG;;;AAMxE,YAAI,OAAO,IAAIA,IAAG,GAAG,OAAO,IAAIA,IAAG;AAKnC,YAAI,SAAS,IAAI,KAAK,SAAS,SAC1B,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AAC5D,cAAM,MAAM,KAAK,IAAI;AAIrB,cAIE,MAAM,QAAQ,IAAI,MAKb,IAAI,WAAW,MAAM,IAAI,CAAC,MAAM,QAAA,UAAU,IAAI,CAAC,MAAM,QAAA,WAAW,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,KAKxF,IAAI,WAAW,KAAK,IAAI,KAAI,EAAG,KAAK,GAAG,MAAM,QAAA,SAAS,MAAM,QAAA,UAC5D,MAAM,QAAQ,KAAK,QAAA,MAAM,CAAC,KAAK,MAAM,QAAQ,KAAK,QAAA,MAAM,CAAC,IAEhE;AAIA,gBAAI,KAAK,QAAA,MAAM,GAAG;AAChB,qBAAO,IAAIA,IAAG,IAAI,KAAK,OAAO,SAAA,GAAC;AAAI,uBAAA,KAAK,QAAA,MAAM,EAAE,QAAQ,CAAC,IAAI;cAA1B,CAA2B;;AAKhE,gBAAI,KAAK,QAAA,MAAM,GAAG;AAChB,kBAAIA,IAAG,IAAC,cAAA,cAAA,CAAA,GAAA,OAAO,IAAI,GAAA,KAAA,GAAA,OAAK,KAAK,QAAA,MAAM,CAAC,GAAA,KAAA;;iBAEjC;AAIL,mBAAO,MAAM,MAAM,IAAI;;mBAEhB,MAAM,QAAQ,IAAI,GAAG;AAK9B,cAAIA,IAAG,IAAI,CAAA;AACX,iBAAO,IAAIA,IAAG,GAAG,MAAM,KAAK;mBACnB,SAAS,IAAI,GAAG;AAIzB,cAAIA,IAAG,IAAI,KAAK,IAAI;eACf;AAIL,cAAIA,IAAG,IAAI;;;;AA7Ef,iBAAgB,KAAA,SAAA,KAAK,GAAG,CAAa,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAA;AAAhC,cAAI,MAAG,GAAA;kBAAH,GAAG;;;;;;;;;;;AAgFZ,aAAO;IACT;AAlFA,YAAA,SAAA;AA6FA,aAAgB,eAAe,SAAmB;AAAE,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAqB;AAArB,aAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAClD,WAAK,QAAQ,SAAA,KAAG;AAAI,eAAA,OAAO,SAAS,KAAK,KAAK;MAA1B,CAA2B;AAC/C,aAAO;IACT;AAHA,YAAA,iBAAA;AAcA,aAAgB,YAAY,SAAmB;AAAE,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAqB;AAArB,aAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC/C,WAAK,QAAQ,SAAA,KAAG;AAAI,eAAA,OAAO,SAAS,KAAK,IAAI;MAAzB,CAA0B;AAC9C,aAAO;IACT;AAHA,YAAA,cAAA;AAaA,aAAgB,cAAc,SAAmB;;AAAE,UAAAC,QAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAiB;AAAjB,QAAAA,MAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AACjD,UAAI,SAAqB,CAAA;;AACzB,iBAAkB,SAAA,SAAAA,KAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAAnB,cAAM,MAAG,SAAA;AACZ,cAAI,QAAQ,eAAe,GAAG,GAAG;AAC/B,mBAAO,GAAG,IAAI,QAAQ,GAAG;;;;;;;;;;;;AAG7B,aAAO;IACT;AARA,YAAA,gBAAA;AAmBA,aAAgB,sBAAsB,SAAqB,QAAkB;AAC3E,aAAO,cAAa,MAAA,QAAA,cAAA,CAAC,OAAO,GAAA,OAAK,OAAO,KAAK,MAAM,CAAC,GAAA,KAAA,CAAA;IACtD;AAFA,YAAA,wBAAA;AAmBA,aAAgB,gBAAgB,SAAmB;;AAAE,UAAA,UAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAwB;AAAxB,gBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AACnD,UAAI,UAAwB,CAAA;;AAC5B,iBAAqB,YAAA,SAAA,OAAO,GAAA,cAAA,UAAA,KAAA,GAAA,CAAA,YAAA,MAAA,cAAA,UAAA,KAAA,GAAE;AAAzB,cAAM,SAAM,YAAA;AACf,cAAI,SAAqB,CAAA,GAAI,UAAsB,CAAA;;AACnD,qBAAkB,MAAA,MAAA,QAAA,SAAA,OAAO,KAAK,WAAW,CAAA,CAAE,CAAC,IAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,kBAAM,MAAG,GAAA;AACZ,eAAC,OAAO,GAAG,MAAM,SAAY,UAAU,QAAQ,GAAG,IAAI,QAAQ,GAAG;;;;;;;;;;;AAEnE,kBAAQ,KAAK,MAAM;AACnB,oBAAU;;;;;;;;;;;AAEZ,cAAQ,QAAQ,OAAO;AACvB,aAAO;IACT;AAZA,YAAA,kBAAA;AAwBA,aAAgB,OAAO,MAAcC,SAAoB,KAAe;AAAf,UAAA,QAAA,QAAA;AAAA,cAAA;MAAe;AACtE,aAAQA,QAAO,eAAe,IAAI,IAAIA,QAAO,IAAI,IAAI;IACvD;AAFA,YAAA,SAAA;;;", "names": ["Expandable", "key", "keys", "lookup"]}