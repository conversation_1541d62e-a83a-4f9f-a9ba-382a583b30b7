import { CHTMLConstructor, Constructor } from '../Wrapper.js';
import { CHTMLmtd } from './mtd.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmtr_base: import("../../common/Wrappers/mtr.js").MtrConstructor<CHTMLmtd<any, any, any>> & CHTMLConstructor<any, any, any>;
export declare class CHTMLmtr<N, T, D> extends CHTMLmtr_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
}
declare const CHTMLmlabeledtr_base: import("../../common/Wrappers/mtr.js").MlabeledtrConstructor<CHTMLmtd<any, any, any>> & Constructor<CHTMLmtr<any, any, any>>;
export declare class CHTMLmlabeledtr<N, T, D> extends CHTMLmlabeledtr_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
    markUsed(): void;
}
export {};
