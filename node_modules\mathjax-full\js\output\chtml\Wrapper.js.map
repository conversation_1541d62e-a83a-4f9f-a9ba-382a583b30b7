{"version": 3, "file": "Wrapper.js", "sourceRoot": "", "sources": ["../../../ts/output/chtml/Wrapper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,6DAAiD;AACjD,mDAA4F;AAG5F,8CAAwC;AAU3B,QAAA,QAAQ,GAAc;IACjC,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;CACb,CAAC;AAEW,QAAA,KAAK;IAEhB,GAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,IAAG,GAAG;IACvB,GAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,IAAG,GAAG;IACvB,GAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,IAAG,GAAG;IACvB,GAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,IAAG,GAAG;IACvB,GAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,IAAG,GAAG;QAEvB;AAiCF;IACA,gCAOC;IARD;QAAA,qEAkTC;QA5QQ,WAAK,GAAM,IAAI,CAAC;;IA4QzB,CAAC;IAnQQ,8BAAO,GAAd,UAAe,MAAS;;QACtB,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;;YAC7C,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACtB;;;;;;;;;IACH,CAAC;IAUS,wCAAiB,GAA3B,UAA4B,MAAS;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,+BAAQ,GAAf;QACE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAMS,sCAAe,GAAzB,UAA0B,MAAS;QACjC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,IAAI,EAAE;YACR,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAM,CAAC;SACzE;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAM,CAAC;QAClF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAKS,mCAAY,GAAtB;QACE,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QACzB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACvD,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC9C,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,GAAG,MAAM,CAAC,CAAC;aACxE;SACF;IACH,CAAC;IAKS,oCAAa,GAAvB;QACE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EACnB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SAC3G;IACH,CAAC;IAKS,kCAAW,GAArB;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAOS,+BAAQ,GAAlB,UAAmB,KAAQ,EAAE,MAAc;QACzC,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACxB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,gBAAQ,CAAC,IAAI,CAAC,EAAE;gBAClB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,gBAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;aAC1D;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;aAChD;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKS,kCAAW,GAArB;;;YACE,KAAmB,IAAA,KAAA,SAAA,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAG,YAAY,CAAC;gBACrC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBADxD,IAAM,IAAI,WAAA;gBAEP,IAAA,KAAA,OAAwB,IAAgC,IAAA,EAAvD,KAAK,QAAA,EAAE,MAAI,QAAA,EAAE,MAAM,QAAoC,CAAC;gBAC/D,IAAI,KAAK,EAAE;oBACT,IAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;oBAC7B,IAAI,aAAK,CAAC,KAAK,CAAC,EAAE;wBAChB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAI,EAAE,aAAK,CAAC,KAAK,CAAC,CAAC,CAAC;qBAC3D;yBAAM;wBACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;qBAClD;iBACF;aACF;;;;;;;;;IACH,CAAC;IAOS,kCAAW,GAArB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,IAAM,SAAS,GAAG,UAAU,CAAC,WAAW,CAAC,WAAW,CAAW,CAAC;QAChE,IAAM,KAAK,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,CAAW,CAAC;QACxD,IAAM,cAAc,GAAG,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAW,CAAC;QAC1E,IAAM,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,YAAY,CAAW,CAAC;QAClE,IAAI,SAAS,IAAI,KAAK,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,CAAC;SAChE;QACD,IAAI,cAAc,IAAI,UAAU,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,iBAAiB,EAAE,cAAc,IAAI,UAAU,CAAC,CAAC;SACpF;IACH,CAAC;IASS,uCAAgB,GAA1B;;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,IAAM,QAAQ,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;QAC7C,IAAM,IAAI,GAAG,YAAY,CAAC,cAAc,CAAC;;YACzC,KAAmB,IAAA,KAAA,SAAA,UAAU,CAAC,gBAAgB,EAAE,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,MAAI,WAAA;gBACb,IAAI,IAAI,CAAC,MAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,MAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAI,CAAC;oBAClC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAI,CAAC,CAAC,EAAE;oBAC1E,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAI,EAAE,UAAU,CAAC,WAAW,CAAC,MAAI,CAAW,CAAC,CAAC;iBACrF;aACF;;;;;;;;;QACD,IAAI,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC3B,IAAM,KAAK,GAAI,UAAU,CAAC,GAAG,CAAC,OAAO,CAAY,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;gBACrE,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;oBAArB,IAAM,MAAI,kBAAA;oBACb,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAI,CAAC,CAAC;iBACzC;;;;;;;;;SACF;IACH,CAAC;IAKS,mCAAY,GAAtB;QACE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,cAAI,CAAC,SAAS,EAAE;gBACvC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACxD;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9D;SACF;IACH,CAAC;IASS,gCAAS,GAAnB,UAAoB,KAAQ,EAAE,KAAa,EAAE,KAAa;QACxD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,MAAM,EAAE;YAC1C,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5D;QACD,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,OAAO,EAAE;YAC3C,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;SAC9D;IACH,CAAC;IAOM,+BAAQ,GAAf;QACM,IAAA,KAAgB,IAAI,CAAC,OAAO,EAAE,EAA7B,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAmB,CAAC;QACnC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAC,KAAK,EAAE;gBACvC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7C,EAAC,EAAE;YACF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAC,KAAK,EAAE;oBAC3B,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClB,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjB,kBAAkB,EAAE,KAAK;iBAC1B,EAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAC,KAAK,EAAE;oBAC3B,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClB,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjB,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC1B,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC7B,kBAAkB,EAAE,OAAO;iBAC5B,EAAC,CAAC;SACG,CAAC,CAAC;QACV,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC9C;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACzD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;IAC5D,CAAC;IAaM,2BAAI,GAAX,UAAY,IAAY,EAAE,GAAoB,EAAE,OAAuB;QAA7C,oBAAA,EAAA,QAAoB;QAAE,wBAAA,EAAA,YAAuB;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAMM,2BAAI,GAAX,UAAY,IAAY;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAMS,2BAAI,GAAd,UAAe,CAAS;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAnSa,iBAAI,GAAW,SAAS,CAAC;IAMzB,sBAAS,GAAG,IAAI,CAAC;IA+RjC,mBAAC;CAAA,AAlTD,CACA,0BAAa,GAiTZ;AAlTY,oCAAY"}