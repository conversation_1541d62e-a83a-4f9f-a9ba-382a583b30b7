import {combineWithMathJax} from '../../../../../js/components/global.js';
import {VERSION} from '../../../../../js/components/version.js';

import * as module1 from '../../../../../js/input/tex.js';
import * as module2 from '../../../../../js/input/tex/Configuration.js';
import * as module3 from '../../../../../js/input/tex/FilterUtil.js';
import * as module4 from '../../../../../js/input/tex/FindTeX.js';
import * as module5 from '../../../../../js/input/tex/MapHandler.js';
import * as module6 from '../../../../../js/input/tex/NodeFactory.js';
import * as module7 from '../../../../../js/input/tex/NodeUtil.js';
import * as module8 from '../../../../../js/input/tex/ParseMethods.js';
import * as module9 from '../../../../../js/input/tex/ParseOptions.js';
import * as module10 from '../../../../../js/input/tex/ParseUtil.js';
import * as module11 from '../../../../../js/input/tex/Stack.js';
import * as module12 from '../../../../../js/input/tex/StackItem.js';
import * as module13 from '../../../../../js/input/tex/StackItemFactory.js';
import * as module14 from '../../../../../js/input/tex/Symbol.js';
import * as module15 from '../../../../../js/input/tex/SymbolMap.js';
import * as module16 from '../../../../../js/input/tex/Tags.js';
import * as module17 from '../../../../../js/input/tex/TexConstants.js';
import * as module18 from '../../../../../js/input/tex/TexError.js';
import * as module19 from '../../../../../js/input/tex/TexParser.js';
import * as module20 from '../../../../../js/input/tex/ams/AmsConfiguration.js';
import * as module21 from '../../../../../js/input/tex/ams/AmsItems.js';
import * as module22 from '../../../../../js/input/tex/ams/AmsMethods.js';
import * as module23 from '../../../../../js/input/tex/autoload/AutoloadConfiguration.js';
import * as module24 from '../../../../../js/input/tex/base/BaseConfiguration.js';
import * as module25 from '../../../../../js/input/tex/base/BaseItems.js';
import * as module26 from '../../../../../js/input/tex/base/BaseMethods.js';
import * as module27 from '../../../../../js/input/tex/configmacros/ConfigMacrosConfiguration.js';
import * as module28 from '../../../../../js/input/tex/newcommand/NewcommandConfiguration.js';
import * as module29 from '../../../../../js/input/tex/newcommand/NewcommandItems.js';
import * as module30 from '../../../../../js/input/tex/newcommand/NewcommandMethods.js';
import * as module31 from '../../../../../js/input/tex/newcommand/NewcommandUtil.js';
import * as module32 from '../../../../../js/input/tex/noundefined/NoUndefinedConfiguration.js';
import * as module33 from '../../../../../js/input/tex/require/RequireConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('input/tex', VERSION, 'input');
}

combineWithMathJax({_: {
  input: {
    tex_ts: module1,
    tex: {
      Configuration: module2,
      FilterUtil: module3,
      FindTeX: module4,
      MapHandler: module5,
      NodeFactory: module6,
      NodeUtil: module7,
      ParseMethods: module8,
      ParseOptions: module9,
      ParseUtil: module10,
      Stack: module11,
      StackItem: module12,
      StackItemFactory: module13,
      Symbol: module14,
      SymbolMap: module15,
      Tags: module16,
      TexConstants: module17,
      TexError: module18,
      TexParser: module19,
      ams: {
        AmsConfiguration: module20,
        AmsItems: module21,
        AmsMethods: module22
      },
      autoload: {
        AutoloadConfiguration: module23
      },
      base: {
        BaseConfiguration: module24,
        BaseItems: module25,
        BaseMethods: module26
      },
      configmacros: {
        ConfigMacrosConfiguration: module27
      },
      newcommand: {
        NewcommandConfiguration: module28,
        NewcommandItems: module29,
        NewcommandMethods: module30,
        NewcommandUtil: module31
      },
      noundefined: {
        NoUndefinedConfiguration: module32
      },
      require: {
        RequireConfiguration: module33
      }
    }
  }
}});
