/**
 * 会话管理
 * 负责生成、存储和获取会话ID
 */

// 会话ID的本地存储键名
const SESSION_ID_KEY = 'chat_session_id'

/**
 * 生成UUID v4
 * @returns UUID字符串
 */
function generateUUID(): string {
  // 简单的UUID v4生成函数
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 会话管理器
 */
export const sessionManager = {
  /**
   * 获取当前会话ID
   * 如果不存在则创建新的
   */
  getSessionId(): string {
    let sessionId = localStorage.getItem(SESSION_ID_KEY)
    
    if (!sessionId) {
      sessionId = generateUUID()
      localStorage.setItem(SESSION_ID_KEY, sessionId)
    }
    
    return sessionId
  },
  
  /**
   * 创建新会话
   * 生成新的会话ID并存储
   */
  createNewSession(): string {
    const sessionId = generateUUID()
    localStorage.setItem(SESSION_ID_KEY, sessionId)
    return sessionId
  },
  
  /**
   * 清除会话
   */
  clearSession(): void {
    localStorage.removeItem(SESSION_ID_KEY)
  }
}