{"version": 3, "file": "FindMathML.js", "sourceRoot": "", "sources": ["../../../ts/input/mathml/FindMathML.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,sDAAwD;AAQxD,IAAM,SAAS,GAAG,oCAAoC,CAAC;AAWvD;IAAyC,8BAAyB;IAAlE;;IAuFA,CAAC;IApEQ,6BAAQ,GAAf,UAAgB,IAAO;QACrB,IAAI,GAAG,GAAG,IAAI,GAAG,EAAK,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACjC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,IAAK,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAQS,kCAAa,GAAvB,UAAwB,IAAO,EAAE,GAAW;;;YAC1C,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA,gBAAA,4BAAE;gBAA/C,IAAM,IAAI,WAAA;gBACb,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACf;;;;;;;;;IACH,CAAC;IAQS,qCAAgB,GAA1B,UAA2B,IAAO,EAAE,GAAW;;QAC7C,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;YACpD,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAhD,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;oBACnE,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;wBACjC,KAAmB,IAAA,oBAAA,SAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,CAAA,CAAA,gBAAA,4BAAE;4BAAzD,IAAM,IAAI,WAAA;4BACb,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;yBACf;;;;;;;;;iBACF;aACF;;;;;;;;;IACH,CAAC;IAQS,+BAAU,GAApB,UAAqB,IAAO,EAAE,GAAW;;;YACvC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA,gBAAA,4BAAE;gBAA1D,IAAM,IAAI,WAAA;gBACb,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACf;;;;;;;;;IACH,CAAC;IAKS,gCAAW,GAArB,UAAsB,GAAW;;QAC/B,IAAI,IAAI,GAAsB,EAAE,CAAC;;YACjC,KAAkB,IAAA,KAAA,SAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;gBAA9B,IAAM,GAAG,WAAA;gBACZ,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK,OAAO;oBACrD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,SAAS,CAAC,CAAC;gBACrE,IAAI,KAAK,GAAG,EAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC;gBACzC,IAAI,GAAG,GAAK,EAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAC,CAAC,CAAC;aACrE;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAhFa,kBAAO,GAAe,EAAE,CAAC;IAkFzC,iBAAC;CAAA,AAvFD,CAAyC,8BAAgB,GAuFxD;AAvFY,gCAAU"}