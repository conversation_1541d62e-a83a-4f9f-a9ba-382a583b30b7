import { Grammar } from '../../rule_engine/grammar.js';
import { createLocale } from '../locale.js';
import { nestingToString } from '../locale_util.js';
import { NUMBERS } from '../numbers/numbers_ko.js';
import * as tr from '../transformers.js';
let locale = null;
export function ko() {
    if (!locale) {
        locale = create();
    }
    return locale;
}
function create() {
    const loc = createLocale();
    loc.NUMBERS = NUMBERS;
    loc.FUNCTIONS.radicalNestDepth = nestingToString;
    loc.FUNCTIONS.plural = function (unit) {
        return unit;
    };
    loc.FUNCTIONS.si = (prefix, unit) => {
        return prefix + unit;
    };
    loc.FUNCTIONS.combineRootIndex = function (index, postfix) {
        return index + postfix;
    };
    loc.ALPHABETS.combiner = tr.Combiners.prefixCombiner;
    loc.ALPHABETS.digitTrans.default = NUMBERS.numberToWords;
    loc.CORRECTIONS.postposition = (name) => {
        if (['같다', '는', '와', '를', '로'].includes(name))
            return name;
        const char = name.slice(-1);
        const value = (char.charCodeAt(0) - 44032) % 28;
        let final = value > 0 ? true : false;
        if (char.match(/[r,l,n,m,1,3,6,7,8,0]/i))
            final = true;
        Grammar.getInstance().setParameter('final', final);
        return name;
    };
    loc.CORRECTIONS.article = (name) => {
        const final = Grammar.getInstance().getParameter('final');
        if (final)
            Grammar.getInstance().setParameter('final', false);
        if (name === '같다')
            name = '는';
        const temp = { 는: '은', 와: '과', 를: '을', 로: '으로' }[name];
        return temp !== undefined && final ? temp : name;
    };
    return loc;
}
