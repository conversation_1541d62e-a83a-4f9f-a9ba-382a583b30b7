{"version": 3, "file": "tex-size4.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/tex-size4.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,QAAQ,GAAyB;IAC1C,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1B,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACzB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACxC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IAC5B,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5B,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAClC,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;IACxB,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;IACxB,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACvB,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;IACvB,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACzB,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IAC3B,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACtC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACnC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IACjC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IAClC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAChC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;IAChC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAC,EAAE,EAAE,GAAG,EAAC,CAAC;CACrC,CAAC"}