import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/textcomp/TextcompConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/textcomp', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      textcomp: {
        TextcompConfiguration: module1
      }
    }
  }
}});
