{"version": 3, "file": "StackItem.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/StackItem.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,8DAAqC;AAuErC;IAOE,kBAAoB,MAAiB;QAAjB,WAAM,GAAN,MAAM,CAAW;IAAI,CAAC;IAK1C,sBAAc,2BAAK;aAAnB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAKM,uBAAI,GAAX;;QAAY,eAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,0BAAmB;;QAC7B,CAAA,KAAA,IAAI,CAAC,MAAM,CAAA,CAAC,IAAI,oCAAI,KAAK,WAAE;IAC7B,CAAC;IAMM,sBAAG,GAAV;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAC3B,CAAC;IAMD,sBAAW,2BAAK;aAAhB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;aAMD,UAAiB,IAAa;YAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACtC,CAAC;;;OARA;IAcD,sBAAW,0BAAI;aAAf;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;aAMD,UAAgB,IAAa;YAC3B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACxB,CAAC;;;OARA;IAcM,uBAAI,GAAX,UAAY,CAAU;QACpB,IAAI,CAAC,IAAI,IAAI,EAAE;YACb,CAAC,GAAG,CAAC,CAAC;SACP;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAMM,uBAAI,GAAX;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAMM,wBAAK,GAAZ;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAQM,wBAAK,GAAZ,UAAa,QAAwB,EAAE,QAAkB;QAA5C,yBAAA,EAAA,eAAwB;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;YACzC,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;QAED,OAAO,IAAI,CAAC,MAAM,CAChB,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;IASM,yBAAM,GAAb,UAAc,IAAY;;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QACxC,OAAO,CAAA,KAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAA,CAAC,MAAM,0BAAC,IAAI,UAAK,IAAI,WAAE;IACtE,CAAC;IAEH,eAAC;AAAD,CAAC,AApHD,IAoHC;AApHqB,4BAAQ;AA0O9B;IAAuC,4BAAQ;IA2C7C,kBAAsB,OAAyB;QAAE,eAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,8BAAmB;;QAApE,YACE,kBAAM,KAAK,CAAC,SAIb;QALqB,aAAO,GAAP,OAAO,CAAkB;QAXxC,YAAM,GAAY,EAAE,CAAC;QAIpB,iBAAW,GAAa,EAAE,CAAC;QASjC,IAAI,KAAI,CAAC,MAAM,EAAE;YACf,KAAI,CAAC,IAAI,GAAG,EAAE,CAAC;SAChB;;IACH,CAAC;IAKC,sBAAW,0BAAI;aAAf;YACA,OAAO,MAAM,CAAC;QAChB,CAAC;;;OAAA;IAKD,sBAAW,yBAAG;aAAd;YACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;aAMD,UAAe,KAAc;YAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QACpB,CAAC;;;OARA;IAaD,sBAAW,6BAAO;aAAlB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;;;OAAA;IAKM,8BAAW,GAAlB,UAAmB,GAAW;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAKM,8BAAW,GAAlB,UAAmB,GAAW,EAAE,KAAW;QACzC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,sBAAI,4BAAM;aAAV;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAMD,sBAAI,6BAAO;aAAX;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAOD,sBAAI,6BAAO;aAAX;YACE,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAMM,yBAAM,GAAb,UAAc,IAAY;QACxB,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC;IAMM,4BAAS,GAAhB,UAAiB,IAAe;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACtC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACtC,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;gBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC;aACtB;YAED,MAAM,IAAI,qBAAQ,CAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAGvC,IAAA,KAAA,OAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,EAAxC,EAAE,QAAA,EAAE,OAAO,QAA6B,CAAC;YAChD,MAAM,IAAI,qBAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,OAAO,QAAQ,CAAC,OAAO,CAAC;SACzB;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAMM,2BAAQ,GAAf;;;YACE,KAAiB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,EAAE,WAAA;gBACX,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aACrB;;;;;;;;;IACH,CAAC;IAMM,gCAAa,GAApB,UAAqB,GAAa;QAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAMM,0BAAO,GAAd;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAW,CAAC;IAC5C,CAAC;IAMM,2BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;IAUM,4BAAS,GAAhB,UAAiB,IAAY;QAC3B,IAAM,KAAK,GAAI,IAAI,CAAC,WAA+B,CAAC;QACpD,OAAO,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IArMgB,aAAI,GAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAMhC,gBAAO,GAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAMlC,eAAM,GAA8B;QAEnD,GAAG,EAAE,CAAC,sBAAsB,EAAE,wCAAwC,CAAC;QAEvE,KAAK,EAAE,CAAC,uBAAuB,EAAE,yCAAyC,CAAC;QAE3E,KAAK,EAAE,CAAC,uBAAuB,EAAE,iCAAiC,CAAC;QACnE,MAAM,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;KAC1C,CAAC;IAmLJ,eAAC;CAAA,AA7MD,CAAuC,QAAQ,GA6M9C;AA7MqB,4BAAQ"}