(function(l,u,f,p,a){"use strict";function h(v,s={}){let t;const{immediate:V,...d}=s,c=a.shallowRef(!1),n=a.shallowRef(!1),i=e=>t&&t.activate(e),r=e=>t&&t.deactivate(e),m=()=>{t&&(t.pause(),n.value=!0)},T=()=>{t&&(t.unpause(),n.value=!1)},g=a.computed(()=>{const e=a.toValue(v);return u.toArray(e).map(A=>{const o=a.toValue(A);return typeof o=="string"?o:u.unrefElement(o)}).filter(f.notNullish)});return a.watch(g,e=>{e.length&&(t=p.createFocusTrap(e,{...d,onActivate(){c.value=!0,s.onActivate&&s.onActivate()},onDeactivate(){c.value=!1,s.onDeactivate&&s.onDeactivate()}}),V&&i())},{flush:"post"}),u.tryOnScopeDispose(()=>r()),{hasFocus:c,isPaused:n,activate:i,deactivate:r,pause:m,unpause:T}}l.useFocusTrap=h})(this.VueUse=this.VueUse||{},VueUse,VueUse,focusTrap,Vue);
