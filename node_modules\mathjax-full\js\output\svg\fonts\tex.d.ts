import { SVGFontData, SVGCharOptions, SVGVariantData, SVGDelimiterData, DelimiterMap, CharMapMap } from '../FontData.js';
import { OptionList } from '../../../util/Options.js';
declare const TeXFont_base: import("../FontData.js").FontDataClass<SVGCharOptions, SVGVariantData, SVGDelimiterData> & typeof SVGFontData;
export declare class TeXFont extends TeXFont_base {
    protected static defaultDelimiters: DelimiterMap<SVGDelimiterData>;
    protected static defaultChars: CharMapMap<SVGCharOptions>;
    protected static variantCacheIds: {
        [name: string]: string;
    };
    constructor(options?: OptionList);
}
export {};
