{"version": 3, "file": "tex-oldstyle.js", "sourceRoot": "", "sources": ["../../../../../ts/output/common/fonts/tex/tex-oldstyle.ts"], "names": [], "mappings": ";;;AAmBa,QAAA,WAAW,GAAyB;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;IACnB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;IACnB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;IACtB,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IAClC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC5C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IACrC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACnC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC7C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAC,CAAC;IACpC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,KAAK,EAAC,CAAC;IACrC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC9C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;IAC3C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAC,CAAC;IAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;CAC9C,CAAC"}