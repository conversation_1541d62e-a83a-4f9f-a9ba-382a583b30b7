{"version": 3, "file": "MathMLCompile.js", "sourceRoot": "", "sources": ["../../../ts/input/mathml/MathMLCompile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,4DACqC;AACrC,oDAA8E;AAC9E,+DAAmD;AAYnD;IAoCE,uBAAY,OAAwB;QAAxB,wBAAA,EAAA,YAAwB;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,WAAmC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,IAAA,wBAAW,EAAC,IAAA,2BAAc,EAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAKM,qCAAa,GAApB,UAAqB,UAAsB;QACzC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;IAC5B,CAAC;IAQM,+BAAO,GAAd,UAAe,IAAO;QACpB,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,GAAG,CAAC,sBAAsB,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAChD,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,OAAO,GAAG,CAAC;IACb,CAAC;IAWM,gCAAQ,GAAf,UAAgB,IAAO;;QACrB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,mBAAmB,CAAC,IAAI,EAAE,CAAC;QACrE,IAAI,QAAQ,EAAE;YACZ,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtE;QACD,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;;YAC1D,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBAA9D,IAAM,MAAI,WAAA;gBACb,IAAI,MAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,IAAI,KAAK,MAAM,EAAE;oBAClD,QAAQ,GAAG,MAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAC3B,IAAI,GAAG,SAAS,CAAC;iBAClB;qBAAM,IAAI,MAAI,KAAK,iBAAiB,EAAE;oBACrC,MAAM,GAAG,IAAI,CAAC;iBACf;aACF;;;;;;;;;QACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;QAClF,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YACtD,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACpC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SACpD;QACD,IAAI,QAAQ,EAAE;YACZ,GAAG,CAAC,QAAQ,GAAI,qBAAqC,CAAC,QAAQ,CAAC,CAAC;YAChE,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAQS,qCAAa,GAAvB,UAAwB,GAAY,EAAE,IAAO;;QAC3C,IAAI,aAAa,GAAG,KAAK,CAAC;;YAC1B,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAhD,IAAM,IAAI,WAAA;gBACb,IAAI,MAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACrB,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,KAAK,KAAK,IAAI,IAAI,MAAI,KAAK,OAAO,EAAE;oBACtC,SAAS;iBACV;gBACD,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,WAAW,EAAE;oBACrC,QAAQ,MAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACxB,KAAK,WAAW;4BACd,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;4BACrC,MAAM;wBACR,KAAK,SAAS;4BACZ,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;4BACzC,aAAa,GAAG,IAAI,CAAC;4BACrB,MAAM;wBACR,KAAK,aAAa;4BAChB,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;4BAClC,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;4BACpC,MAAM;wBACR,KAAK,QAAQ;4BACX,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC;4BAChD,MAAM;wBACR,KAAK,SAAS;4BACZ,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC;4BAC5C,MAAM;wBACR,KAAK,cAAc;4BACjB,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;4BACtC,MAAM;qBACP;iBACF;qBAAM,IAAI,MAAI,KAAK,OAAO,EAAE;oBAC3B,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;oBAC9B,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,EAAE;wBACrC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAI,EAAE,GAAG,KAAK,MAAM,CAAC,CAAC;qBAC1C;yBAAM,IAAI,CAAC,aAAa,IAAI,MAAI,KAAK,aAAa,EAAE;wBACnD,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;qBACjC;iBACF;aACF;;;;;;;;;IACH,CAAC;IAQS,uCAAe,GAAzB,UAA0B,KAAa,EAAE,KAAa;QACpD,OAAO,KAAK,CAAC;IACf,CAAC;IAOS,uCAAe,GAAzB,UAA0B,IAAc;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAQS,mCAAW,GAArB,UAAsB,GAAY,EAAE,IAAO;;QACzC,IAAI,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;YACnB,OAAO;SACR;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;;YAC7B,KAAoB,IAAA,KAAA,SAAA,OAAO,CAAC,UAAU,CAAC,IAAI,CAAQ,CAAA,gBAAA,4BAAE;gBAAhD,IAAM,KAAK,WAAA;gBACd,IAAM,MAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjC,IAAI,MAAI,KAAK,UAAU,EAAE;oBACvB,SAAS;iBACV;gBACD,IAAI,MAAI,KAAK,OAAO,EAAE;oBACpB,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC1B;qBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE;oBACvC,GAAG,CAAC,WAAW,CAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAa,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;iBACjF;qBAAM;oBACL,IAAI,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAY,CAAC;oBAChE,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;wBAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;4BACxC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;yBAC9B;6BAAM;4BACL,QAAQ,CAAC,MAAM,CAAC,mCAAmC,GAAG,QAAQ,CAAC,IAAI,GAAG,QAAQ,EAC9D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;yBAC/C;qBACF;iBACF;aACF;;;;;;;;;IACH,CAAC;IAQS,+BAAO,GAAjB,UAAkB,GAAY,EAAE,KAAQ;QACtC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE;YAC5D,IAAI,GAAG,CAAC,OAAO,EAAE;gBACf,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aAC7B;YACD,GAAG,CAAC,WAAW,CAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC1E;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;SACnD;IACH,CAAC;IAQS,kCAAU,GAApB,UAAqB,GAAY,EAAE,IAAO;;QACxC,IAAI,SAAS,GAAG,EAAE,CAAC;;YACnB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBAAnE,IAAM,MAAI,WAAA;gBACb,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;oBAChC,IAAI,MAAI,KAAK,aAAa,EAAE;wBAC1B,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;qBACtC;yBAAM,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,aAAa,EAAE;wBAC/C,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,MAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBACzE;iBACF;qBAAM;oBACL,SAAS,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC;iBACtB;aACF;;;;;;;;;QACD,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;IACH,CAAC;IAQS,uCAAe,GAAzB,UAA0B,OAAe;QACvC,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IACxD,CAAC;IAOS,iCAAS,GAAnB,UAAoB,GAAY;QAC9B,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;YACvE,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;YACzC,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAY,CAAC;YAChE,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC;gBACvF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBACxF,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC3B,GAAG,CAAC,WAAW,CAAC,MAAM,EAAG,KAA8B,CAAC,OAAO,EAAE,CAAC,CAAC;iBACpE;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC1B,GAAG,CAAC,WAAW,CAAC,OAAO,EAAG,IAA6B,CAAC,OAAO,EAAE,CAAC,CAAC;iBACpE;aACF;SACF;IACH,CAAC;IAMS,iCAAS,GAAnB,UAAoB,IAAY;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;aACzB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;aAClB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;aAClB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAKS,6BAAK,GAAf,UAAgB,OAAe;QAC7B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAlSa,qBAAO,GAAe;QAClC,UAAU,EAAE,IAAI;QAChB,oBAAoB,EAAE,IAAI;QAG1B,MAAM,eACD,4BAAe,CAAC,cAAc,CAClC;QACD,iBAAiB,EAAE,IAAI;KACxB,CAAC;IA0RJ,oBAAC;CAAA,AAxSD,IAwSC;AAxSY,sCAAa"}