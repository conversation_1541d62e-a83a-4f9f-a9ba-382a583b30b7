{"version": 3, "file": "collapse.js", "sourceRoot": "", "sources": ["../../../ts/a11y/complexity/collapse.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAgDA;IAuOI,kBAAY,OAA0B;QAAtC,iBAEC;QAzNM,WAAM,GAAqB;YAC9B,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,MAAM,EAAE,CAAC;YACT,UAAU,EAAE;gBACR,QAAQ,EAAE,QAAQ,CAAC,UAAU;gBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,KAAK,EAAE,EAAE;aACZ;SACJ,CAAC;QAOK,WAAM,GAAqB;YAC9B,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,KAAK;YACX,IAAI,EAAE;gBACF,gBAAgB,EAAE,KAAK;gBACvB,KAAK,EAAE,KAAK;aACf;YACD,QAAQ,EAAE,GAAG;YACb,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,cAAc;YAC3B,SAAS,EAAE,SAAS;YACpB,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE;gBACJ,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,KAAK;gBAClB,KAAK,EAAE,eAAe;aACzB;YACD,MAAM,EAAE;gBACJ,YAAY,EAAE,MAAM;gBACpB,SAAS,EAAE,oBAAoB;gBAC/B,YAAY,EAAE,oBAAoB;gBAClC,WAAW,EAAE,MAAM;gBACnB,KAAK,EAAE,MAAM;aAChB;YACD,KAAK,EAAE,IAAI;YACX,OAAO,EAAE;gBACL,QAAQ,EAAE,GAAG;gBACb,WAAW,EAAE,QAAQ;gBACrB,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,GAAG;aACb;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,GAAG;aACb;SACJ,CAAC;QAKK,aAAQ,GAAwB,IAAI,GAAG,CAAC;YAM3C,CAAC,QAAQ,EAAE,UAAC,IAAI,EAAE,UAAU;oBACxB,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;oBACvD,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,WAAW,EAAE;wBAC9F,UAAU,GAAG,KAAI,CAAC,cAAc,CAC5B,IAAI,EAAE,UAAU,EAChB,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;4BACvC,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAY,CAAC,CAC3E,CAAC;qBACL;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAMF,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,UAAU;oBACtB,IAAI,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;wBAChC,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpD,IAAM,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC,IAAgC,CAAC;wBAC5D,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAW,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC;wBACzF,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;qBAC5D;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAMF,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,UAAU;oBACtB,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;oBACvD,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE;wBAC/B,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAI,CAAC,MAAM,CAAC,IAAc,CAAC,CAAC;qBAClF;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YACF,CAAC,MAAM,EAAE,UAAC,IAAI,EAAE,UAAU;oBACtB,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1D,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE;wBAC/B,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAI,CAAC,MAAM,CAAC,IAAc,CAAC,CAAC;qBAClF;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAKF,CAAC,SAAS,EAAE,UAAC,IAAI,EAAE,UAAU;oBACzB,IAAI,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;wBACpD,IAAM,KAAK,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBAC1C,IAAI,KAAK,EAAE;4BACP,IAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAW,CAAC;4BAC9D,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;4BAC7B,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;yBAC1F;qBACJ;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAKF,CAAC,OAAO,EAAE,UAAC,IAAI,EAAE,UAAU;oBACvB,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBACtD,IAAM,EAAE,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC;wBACtD,IAAM,EAAE,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBACxC,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;qBAC1D;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YACF,CAAC,UAAU,EAAE,UAAC,IAAI,EAAE,UAAU;oBAC1B,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBACzD,IAAM,EAAE,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC;wBACtD,IAAM,EAAE,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBACxC,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;qBAC1D;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAKF,CAAC,QAAQ,EAAE,UAAC,IAAI,EAAE,UAAU;oBACxB,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,EAAE;wBACjC,IAAM,EAAE,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnD,IAAM,IAAI,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBAC1C,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;qBAC5D;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YACF,CAAC,UAAU,EAAE,UAAC,IAAI,EAAE,UAAU;oBAC1B,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,EAAE;wBACjC,IAAM,EAAE,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnD,IAAM,IAAI,GAAG,KAAI,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC;wBACrD,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;qBAC5D;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YAKF,CAAC,aAAa,EAAE,UAAC,IAAI,EAAE,UAAU;oBAC7B,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1D,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,WAAW,EAAE;wBACtC,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAI,CAAC,MAAM,CAAC,WAAqB,CAAC,CAAC;qBACzF;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YACF,CAAC,WAAW,EAAE,UAAC,IAAI,EAAE,UAAU;oBAC3B,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1D,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,EAAE;wBACpC,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAI,CAAC,MAAM,CAAC,SAAmB,CAAC,CAAC;qBACvF;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;YACF,CAAC,QAAQ,EAAE,UAAC,IAAI,EAAE,UAAU;oBACxB,UAAU,GAAG,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1D,IAAI,UAAU,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,EAAE;wBACjC,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAI,CAAC,MAAM,CAAC,MAAgB,CAAC,CAAC;qBACpF;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;SAE2B,CAAC,CAAC;QAK3B,YAAO,GAAG,CAAC,CAAC;QAMhB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;IAC9B,CAAC;IAWM,wBAAK,GAAZ,UAAa,IAAa,EAAE,UAAkB;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAW,CAAC;QACjE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;SAC/D;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SACpD;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAUS,+BAAY,GAAtB,UAAuB,IAAa,EAAE,UAAkB,EAAE,IAAY;QAClE,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAW,CAAC;QACjE,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChC,IAAM,MAAM,GAAG,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QAChF,IAAI,UAAU,GAAG,MAAM,EAAE;YACrB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;YACzC,IAAM,IAAI,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;YAClF,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SAC5D;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAQS,iCAAc,GAAxB,UAAyB,IAAa,EAAE,UAAkB,EAAE,IAAY;QACpE,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,QAAQ,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;IACzD,CAAC;IAOS,mCAAgB,GAA1B,UAA2B,IAAa;QACpC,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAC3D,IAAI,UAAU,IAAI,IAAI,EAAE;YACpB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC;YAC5D,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAC3C,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1C;IACL,CAAC;IAQS,gCAAa,GAAvB,UAAwB,IAAa,EAAE,CAAS,EAAE,CAAa;QAAb,kBAAA,EAAA,KAAa;QAC3D,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;gBAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAa,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAChG,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC1B,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;gBAC3C,IAAI,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;oBACtC,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IASS,kCAAe,GAAzB,UAA0B,UAAkB,EAAE,IAAa,EAAE,CAAS,EAAE,CAAa;QAAb,kBAAA,EAAA,KAAa;QACjF,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;gBACvB,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;aACtE;YACD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAW,CAAC;SACjE;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAOS,iCAAc,GAAxB,UAAyB,IAAa,EAAE,EAAU;QAC9C,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,GAAG,EAAE,CAAW,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnF,CAAC;IAMS,0BAAO,GAAjB,UAAkB,IAAa;QAA/B,iBAGC;QAFG,IAAI,IAAI,CAAC,OAAO;YAAE,OAAQ,IAA6B,CAAC,OAAO,EAAE,CAAC;QAClE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAC,CAAU,IAAK,OAAA,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;IAOS,gCAAa,GAAvB,UAAwB,IAAa,EAAE,EAAU;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC;IACjD,CAAC;IAOS,4BAAS,GAAnB,UAAoB,IAAa,EAAE,EAAU;;QACzC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;gBACf,KAAkB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAA9B,IAAM,GAAG,WAAA;oBACV,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAc,EAAE,EAAE,CAAC,CAAC;oBACjD,IAAI,KAAK;wBAAE,OAAO,KAAK,CAAC;iBAC3B;;;;;;;;;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAOM,+BAAY,GAAnB,UAAoB,IAAa;QAC7B,IAAM,KAAK,GAAc,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,UAAC,KAAc;YACzB,IAAI,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;gBACtC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACrB;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAKM,8BAAW,GAAlB,UAAmB,KAAgB;;;YAC/B,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,IAAI,kBAAA;gBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACzB;;;;;;;;;IACL,CAAC;IAKO,yBAAM,GAAd;QACI,OAAO,eAAe,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5C,CAAC;IAKM,6BAAU,GAAjB,UAAkB,IAAa;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACrB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAW,CAAC;QAC7D,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;YACpC,UAAU,EAAE,QAAQ;YACpB,SAAS,EAAE,CAAC;YACZ,kBAAkB,EAAE,IAAI;YACxB,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE;YACjB,0BAA0B,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC;SAC9E,EAAE;YACC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAC,SAAS,EAAE,MAAM,EAAC,EAAE;gBACxC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,MAAM,CAAC;aACvD,CAAC;SACL,CAAC,CAAC;QACH,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAC3C,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IASM,0BAAO,GAAd,UAAe,IAAa;;QACxB,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAuB,CAAC,CAAC;QACtG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEvC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;;YACtD,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,MAAI,WAAA;gBACX,IAAI,MAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,gBAAgB,EAAE;oBACzC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAI,EAAE,UAAU,CAAC,MAAI,CAAC,CAAC,CAAC;oBAC5C,OAAO,UAAU,CAAC,MAAI,CAAC,CAAC;iBAC3B;aACJ;;;;;;;;;QAED,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAhda,mBAAU,GAAW,QAAQ,CAAC;IAidhD,eAAC;CAAA,AAtdD,IAsdC;AAtdY,4BAAQ"}