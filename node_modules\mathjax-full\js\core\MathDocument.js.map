{"version": 3, "file": "MathDocument.js", "sourceRoot": "", "sources": ["../../ts/core/MathDocument.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,iDAAuF;AACvF,6CAAyD;AACzD,+CAA4D;AAC5D,6CAAyD;AACzD,6CAAgE;AAEhE,+DAAyD;AAEzD,mDAA4D;AAE5D,iEAA2D;AAyE3D;IAAyC,8BAAoC;IAA7E;;IA4HA,CAAC;IApHe,iBAAM,GAApB,UAA8B,OAA+B;;QAC3D,IAAM,IAAI,GAAG,IAAI,IAAI,EAAW,CAAC;;YACjC,KAAiB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,gBAAA,4BAAE;gBAAlC,IAAM,EAAE,WAAA;gBACL,IAAA,KAAA,OAAqB,IAAI,CAAC,MAAM,CAAU,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,IAAA,EAAzD,MAAM,QAAA,EAAE,QAAQ,QAAyC,CAAC;gBACjE,IAAI,QAAQ,EAAE;oBACZ,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBAC5B;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAUa,iBAAM,GAApB,UAA8B,EAAU,EAAE,MAA6B;;QACrE,IAAI,SAAS,EAAE,UAAU,CAAC;QAC1B,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACzD,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAY,CAAC,CAAC;YACxD,KAAA,OAA0B,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAA,EAA/C,SAAS,QAAA,EAAE,UAAU,QAAA,CAA2B;SAClD;aAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACxC,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACjC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAY,CAAC,CAAC;gBAClD,IAAA,KAAA,OAAqB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAqB,IAAA,EAAvD,OAAO,QAAA,EAAE,OAAO,QAAuC,CAAC;gBAC/D,KAAA,OAA0B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,IAAA,EAA7D,SAAS,QAAA,EAAE,UAAU,QAAA,CAAyC;aAChE;iBAAM;gBACL,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAY,CAAC,CAAC;gBACxD,KAAA,OAA0B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAW,CAAC,IAAA,EAAhE,SAAS,QAAA,EAAE,UAAU,QAAA,CAA4C;aACnE;SACF;aAAM;YACL,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAY,CAAC,CAAC;YACxD,KAAA,OAA0B,MAAM,CAAC,KAAK,CAAC,CAAC,CAA8C,IAAA,EAArF,SAAS,QAAA,EAAE,UAAU,QAAA,CAAiE;SACxF;QACD,OAAO,CAAC,EAAC,EAAE,IAAA,EAAE,SAAS,WAAA,EAAE,UAAU,YAAA,EAAE,OAAO,SAAA,EAAwB,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IASgB,wBAAa,GAA9B,UAA+B,OAAe,EAAE,OAAyB;QAAzB,wBAAA,EAAA,iBAAyB;QACvE,OAAO;YACL,UAAC,QAAa,IAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC;YACnE,UAAC,IAAS,EAAE,QAAa,IAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAQM,8BAAS,GAAhB,UAAiB,QAA+B,EAAE,KAAiC;;QAAjC,sBAAA,EAAA,QAAgB,mBAAK,CAAC,WAAW;;YACjF,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;oBAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;wBAAE,OAAO;iBAC3C;aACF;;;;;;;;;IACH,CAAC;IASM,+BAAU,GAAjB,UAAkB,IAAuB,EAAE,QAA+B,EAAE,KAAiC;;QAAjC,sBAAA,EAAA,QAAgB,mBAAK,CAAC,WAAW;;YAC3G,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;oBAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;wBAAE,OAAO;iBAClD;aACF;;;;;;;;;IACH,CAAC;IASM,kCAAa,GAApB,UAAqB,IAAuB,EAAE,QAA+B,EAAE,GAAwB;;QAAxB,oBAAA,EAAA,MAAc,mBAAK,CAAC,IAAI;;YACrG,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,QAAQ,GAAG,GAAG;oBAAE,OAAO;gBAChC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACrB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;wBAAE,OAAO;iBAClD;aACF;;;;;;;;;IACH,CAAC;IAQM,2BAAM,GAAb,UAAc,EAAU;;;YACtB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;oBACvB,OAAO,IAAI,CAAC,IAAI,CAAC;iBAClB;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEH,iBAAC;AAAD,CAAC,AA5HD,CAAyC,oCAAe,GA4HvD;AA5HY,gCAAU;AAqJV,QAAA,YAAY,GAAc;IACrC,GAAG,EAAE,KAAK;IACV,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;CAChB,CAAC;AAKW,QAAA,eAAe,GAAc;IACxC,GAAG,EAAE,IAAI;IACT,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,EAAE;CACd,CAAC;AA8NF;IAAuC,mCAAyB;IAAhE;;IAOA,CAAC;IAHQ,iCAAO,GAAd,UAAe,KAAwB;QACrC,OAAO,IAAe,CAAC;IACzB,CAAC;IACH,sBAAC;AAAD,CAAC,AAPD,CAAuC,8BAAgB,GAOtD;AASD;IAAwC,oCAA0B;IAAlE;;IAaA,CAAC;IATQ,kCAAO,GAAd,UAAe,KAAwB,EAAE,SAAuC;QAAvC,0BAAA,EAAA,gBAAuC;QAC9E,OAAO,IAAS,CAAC;IACnB,CAAC;IAIM,kCAAO,GAAd,UAAe,KAAwB,EAAE,SAAiC;QACxE,OAAO,IAAS,CAAC;IACnB,CAAC;IACH,uBAAC;AAAD,CAAC,AAbD,CAAwC,gCAAiB,GAaxD;AASD;IAAuC,mCAAyB;IAAhE;;IAAkE,CAAC;IAAD,sBAAC;AAAD,CAAC,AAAnE,CAAuC,8BAAgB,GAAY;AASnE;IAAuC,mCAAyB;IAAhE;;IAAkE,CAAC;IAAD,sBAAC;AAAD,CAAC,AAAnE,CAAuC,8BAAgB,GAAY;AAUnE;IAuFE,8BAAa,QAAW,EAAE,OAA4B,EAAE,OAAmB;QAA3E,iBA6BC;QA5BC,IAAI,KAAK,GAAG,IAAI,CAAC,WAA0C,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,IAAA,wBAAW,EAAC,IAAA,2BAAc,EAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,eAAe,CAAC,EAAE,CAAC;QAChE,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAU,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC,WAAW,EAAE,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,gBAAgB,EAAW,CAAC;QAC9E,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,eAAe,EAAW,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5B,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;SACvB;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAIzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAvB,CAAuB,CAAC,CAAC;QAIlD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,0BAAU,EAAE,CAAC;QACjE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,aAAa,CAAC,KAAI,CAAC,UAAU,CAAC,EAAlC,CAAkC,CAAC,CAAC;QAI7D,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,UAAU,EAAE,EAAhB,CAAgB,CAAC,CAAC;IAC7C,CAAC;IAKD,sBAAW,sCAAI;aAAf;YACE,OAAQ,IAAI,CAAC,WAA2C,CAAC,IAAI,CAAC;QAChE,CAAC;;;OAAA;IAKM,8CAAe,GAAtB,UAAuB,EAAU;QAAE,gBAAgB;aAAhB,UAAgB,EAAhB,qBAAgB,EAAhB,IAAgB;YAAhB,+BAAgB;;QAC3C,IAAA,KAAA,OAAU,UAAU,CAAC,MAAM,CAAU,EAAE,EAAE,MAA+B,CAAC,IAAA,EAAxE,EAAE,QAAA,EAAE,CAAC,QAAmE,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAChC,CAAC;IAKM,iDAAkB,GAAzB,UAA0B,EAAU;QAClC,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACnC;IACH,CAAC;IAKM,qCAAM,GAAb;QACE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,uCAAQ,GAAf,UAAgB,KAA8B;QAA9B,sBAAA,EAAA,QAAgB,mBAAK,CAAC,QAAQ;QAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,sCAAO,GAAd,UAAe,IAAY,EAAE,OAAwB;QAAxB,wBAAA,EAAA,YAAwB;QAC/C,IAAA,KAA2E,IAAA,wBAAW,EAAC;YACzF,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,mBAAK,CAAC,IAAI;YAC7D,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE;SAC9E,EAAE,OAAO,CAAC,EAHN,MAAM,YAAA,EAAE,OAAO,aAAA,EAAE,GAAG,SAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,cAAc,oBAAA,EAAE,SAAS,eAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAGhE,CAAC;QACZ,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,cAAc,GAAG,EAAE,GAAG,EAAE,CAAC;SAC1B;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,OAAA,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAnC,CAAmC,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAM,KAAK,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC5D,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3C,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC;SACvC;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5C,KAAK,CAAC,UAAU,CAAC,YAAY,GAAG,MAAM,CAAC;SACxC;QACD,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAKM,uCAAQ,GAAf,UAAgB,QAA2B;QAA3B,yBAAA,EAAA,eAA2B;QACzC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,sCAAO,GAAd;;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YAIpC,IAAM,SAAS,GAAG,EAAE,CAAC;;gBACrB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;oBAAzB,IAAM,IAAI,WAAA;oBACb,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBACvB,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;wBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACtB;iBACF;;;;;;;;;;gBAKD,KAAmB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA,2DAAE;oBAAzB,IAAM,IAAI,sBAAA;oBACb,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;oBACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACvB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBACxB;;;;;;;;;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKS,0CAAW,GAArB,UAAsB,IAAuB;QAC3C,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpB;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE;gBAC5B,MAAM,GAAG,CAAC;aACX;YACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;SAC/B;IACH,CAAC;IAQM,2CAAY,GAAnB,UAAoB,IAAuB,EAAE,GAAU;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE;YAC/C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAC,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAC,EAAE;gBACpF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE;oBACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC;iBACzE,CAAC;aACH,CAAC;SACH,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC;IACrC,CAAC;IAKM,sCAAO,GAAd;;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;gBACpC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;oBAAzB,IAAM,IAAI,WAAA;oBACb,IAAI;wBACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;qBACpB;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE;4BAC5B,MAAM,GAAG,CAAC;yBACX;wBACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;wBAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;qBAChC;iBACF;;;;;;;;;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAQM,2CAAY,GAAnB,UAAoB,IAAuB,EAAE,GAAU;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YACpD,KAAK,EAAE,0BAA0B;YACjC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;SACzB,EAAE;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE;gBACxB,gBAAgB,EAAE,GAAG,CAAC,OAAO;gBAC7B,KAAK,EAAE,GAAG,CAAC,OAAO;gBAClB,KAAK,EAAE;oBACL,KAAK,EAAE,KAAK;oBACZ,kBAAkB,EAAE,QAAQ;oBAC5B,aAAa,EAAE,QAAQ;iBACxB;aACF,EAAE;gBACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC;aACvC,CAAC;SACH,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC3C,KAAK,EAAE;oBACL,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,OAAO;oBACf,YAAY,EAAE,QAAQ;iBACvB;aACF,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC;IACtC,CAAC;IAKM,yCAAU,GAAjB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YACvC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SAClC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,6CAAc,GAArB;;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;;gBAC3C,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,gBAAA,4BAAE;oBAApC,IAAM,IAAI,WAAA;oBACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;iBAC3B;;;;;;;;;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,iDAAkB,GAAzB,UAA0B,QAAyB;QAAzB,yBAAA,EAAA,gBAAyB;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,oCAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;;QAAxB,wBAAA,EAAA,eAAwB;;YAClD,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;gBAAzB,IAAM,IAAI,WAAA;gBACb,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aAC5B;;;;;;;;;QACD,IAAI,KAAK,GAAG,mBAAK,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;SACxC;QACD,IAAI,KAAK,GAAG,mBAAK,CAAC,OAAO,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,IAAI,KAAK,GAAG,mBAAK,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SACjC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,oCAAK,GAAZ,UAAa,OAAsC;;QAAtC,wBAAA,EAAA,YAAsB,SAAS,EAAE,IAAI,EAAC;QACjD,OAAO,GAAG,IAAA,wBAAW,EAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,oBAAY,CAAC,EAAE,OAAO,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,uBAAe,CAAC,CAAC;QACvD,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAC5C,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,KAAK,OAAT,GAAG,2BAAU,OAAO,CAAC,QAAQ,YAA7B,CAA8B,CAAC,CAAC;QACjF,OAAO,CAAC,SAAS,IAAI,CAAA,KAAA,IAAI,CAAC,SAAS,CAAA,CAAC,KAAK,oCAAI,OAAO,CAAC,SAAS,UAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,oCAAK,GAAZ;QACE,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,qCAAM,GAAb,UAAc,IAAuB;QACnC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAKM,mDAAoB,GAA3B,UAA4B,UAA4B;;QACtD,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAClD,CAAA,KAAA,IAAI,CAAC,IAAI,CAAA,CAAC,MAAM,oCAAI,KAAK,WAAE;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC;IAKM,iDAAkB,GAAzB,UAA0B,QAA0B;;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5B,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;SACvB;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,KAAK,GAAG,EAAyB,CAAC;QACxC,IAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;YAChE,KAAK,EACL,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;gBAAzB,IAAM,IAAI,WAAA;;oBACb,KAAwB,IAAA,+BAAA,SAAA,UAAU,CAAA,CAAA,sCAAA,8DAAE;wBAA/B,IAAM,SAAS,uBAAA;wBAClB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4BACnE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACjB,SAAS,KAAK,CAAC;yBAChB;qBACF;;;;;;;;;aACF;;;;;;;;;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAlaa,yBAAI,GAAW,cAAc,CAAC;IAK9B,4BAAO,GAAe;QAClC,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,eAAe;QACzB,QAAQ,EAAE,eAAe;QACzB,YAAY,EAAE,UAAC,GAAwC,EAAE,IAA6B,EAAE,GAAU;YAChG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,CAAC;QACD,YAAY,EAAE,UAAC,GAAwC,EAAE,IAA6B,EAAE,GAAU;YAChG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,CAAC;QACD,aAAa,EAAE,IAAA,uBAAU,EAAC;YACxB,IAAI,EAAK,CAAC,mBAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC;YAChD,OAAO,EAAE,CAAC,mBAAK,CAAC,QAAQ,CAAC;YACzB,OAAO,EAAE,CAAC,mBAAK,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE,KAAK,CAAC;YACjD,OAAO,EAAE,CAAC,mBAAK,CAAC,OAAO,CAAC;YACxB,MAAM,EAAG,CAAC,mBAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,KAAK,CAAC;SACnD,CAAiC;KACnC,CAAC;IAKY,gCAAW,GAAG,IAAA,2BAAa,EAAC,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAuY9G,2BAAC;CAAA,AAzaD,IAyaC;AAzaqB,oDAAoB"}