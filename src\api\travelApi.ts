import axios from './axios'
import { handleStreamResponse } from './streamUtils'
import { TravelLoginRequest, TravelLoginResponse, TravelQuestionRequest, TravelQuestionResponse } from './types'

// 定义接口响应类型
interface TokenResponse {
  token: string
  [key: string]: any
}

/**
 * 旅行API服务
 */
export const travelApi = {
  /**
   * 登录获取token
   * @param data 登录信息
   * @returns 登录响应
   */
  login: async (data: TravelLoginRequest): Promise<TravelLoginResponse> => {
    try {
      // 使用axios发送登录请求
      const response = await axios.post<TokenResponse>('/api/travel/token', data, {
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      // axios在这个项目中已经配置了响应拦截器，直接返回data
      const responseData = response as unknown as TokenResponse
      
      // 将token存储到localStorage
      if (responseData && responseData.token) {
        localStorage.setItem('travel_token', responseData.token)
      }
      
      return {
        success: true,
        token: responseData.token,
        message: '登录成功'
      }
    } catch (error) {
      console.error('旅行服务登录失败:', error)
      return {
        success: false,
        token: null,
        message: '登录失败，请稍后再试'
      }
    }
  },
  
  /**
   * 发送旅行相关问题
   * @param data 问题请求参数
   * @param onChunk 每收到一块数据时的回调函数
   * @returns 问题响应
   */
  askQuestion: async (
    data: TravelQuestionRequest,
    onChunk?: (chunk: string) => void
  ): Promise<TravelQuestionResponse> => {
    try {
      // 获取token
      const token = localStorage.getItem('travel_token')
      
      if (!token) {
        throw new Error('未登录，请先登录')
      }
      
      // 如果提供了onChunk回调，使用流式处理
      if (onChunk) {
        // 使用fetch API进行流式请求
        const response = await fetch('/api/travel/travel/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            user_question: data.user_question,
            follow_up: data.follow_up
          })
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        // 创建一个Promise，在流处理完成后解析
        return new Promise((resolve) => {
          // 存储完整的响应内容
          let fullContent = ''

          console.log('开始处理旅行问题流式响应')

          // 处理流式响应
          handleStreamResponse(
            response,
            (chunk) => {
            //   console.log('旅行API接收到数据块:', chunk)
              fullContent += chunk
              if (onChunk) {
                onChunk(chunk)
              }
            },
            () => {
              // 流处理完成，返回完整响应
              console.log('旅行问题流式响应完成，完整内容:', fullContent)
              resolve({
                success: true,
                content: fullContent,
                message: '回答成功'
              })
            }
          ).catch(error => {
            console.error('流处理错误:', error)
            // 出错时也要解析Promise，避免卡住UI
            resolve({
              success: false,
              content: fullContent || '抱歉，处理响应时出错。',
              message: '回答失败'
            })
          })
        })
      } else {
        // 不使用流式处理，直接发送请求
        const response = await axios.post('/api/travel/travel/stream', {
          user_question: data.user_question,
          follow_up: data.follow_up
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        
        // 处理响应数据
        const content = typeof response === 'object' && response !== null ? 
          JSON.stringify(response) : String(response)
        
        return {
          success: true,
          content: content,
          message: '回答成功'
        }
      }
    } catch (error) {
      console.error('获取旅行问题回答失败:', error)
      return {
        success: false,
        content: '抱歉，获取回答失败，请稍后再试。',
        message: '回答失败'
      }
    }
  },
  
  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isLoggedIn: (): boolean => {
    return !!localStorage.getItem('travel_token')
  },
  
  /**
   * 登出
   */
  logout: (): void => {
    localStorage.removeItem('travel_token')
  }
} 