{"version": 3, "file": "CenternotConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/centernot/CenternotConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwBA,wDAAkD;AAElD,iEAAwC;AACxC,+DAAsC;AACtC,gDAA2C;AAE3C,0EAAiD;AAEjD,IAAI,yBAAU,CAAC,WAAW,EAAE;IAC1B,UAAU,EAAE,YAAY;IACxB,SAAS,EAAE,CAAC,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;CACtD,EAAE;IAOD,UAAU,EAAV,UAAW,MAAiB,EAAE,IAAY;QACxC,IAAM,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;QACjD,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnC,IAAM,IAAI,GAAG,IAAI,sBAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9E,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE;YACzC,IAAI,sBAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE;YAChE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE;gBAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC;gBACxE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC;aAC1C,EAAE,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC;SACnC,CAAC,CAAC;QACH,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACjD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IACD,KAAK,EAAE,wBAAW,CAAC,KAAK;CACzB,CAAC,CAAC;AAQH,SAAgB,gBAAgB,CAAC,EAA4B;;QAA3B,IAAI,UAAA;;QACpC,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA,gBAAA,4BAAE;YAA1C,IAAM,IAAI,WAAA;YACb,IAAM,QAAQ,GAAG,qBAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC,CAAC;YACnF,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,qBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAC,QAAQ,UAAA,EAAC,CAAC,CAAC;aACpF;SACF;;;;;;;;;AACH,CAAC;AAPD,4CAOC;AAGY,QAAA,sBAAsB,GAAG,gCAAa,CAAC,MAAM,CACxD,WAAW,EAAE;IACX,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAC;IAC/B,cAAc,EAAE,CAAC,gBAAgB,CAAC;CACnC,CACF,CAAC"}