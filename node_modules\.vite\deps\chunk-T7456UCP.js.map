{"version": 3, "sources": ["../../mathjax-full/ts/util/lengths.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Utility functions for handling dimensions (lengths)\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\n/**\n *  A very large number\n */\nexport const BIGDIMEN = 1000000;\n\n/**\n *  Sizes of various units in pixels\n */\nexport const UNITS: {[unit: string]: number} = {\n  px: 1,\n  'in': 96,            // 96 px to an inch\n  cm: 96 / 2.54,       // 2.54 cm to an inch\n  mm: 96 / 25.4        // 10 mm to a cm\n};\n\n/**\n *  Sizes of various relative units in em's\n */\nexport const RELUNITS: {[unit: string]: number} = {\n  em: 1,\n  ex: .431,        // this.TEX.x_height;\n  pt: 1 / 10,      // 10 pt to an em\n  pc: 12 / 10,     // 12 pc to a pt\n  mu: 1 / 18       // 18mu to an em for the scriptlevel\n};\n\n/**\n *  The various named spaces\n */\nexport const MATHSPACE: {[name: string]: number} = {\n  /* tslint:disable:whitespace */\n  veryverythinmathspace:           1/18,\n  verythinmathspace:               2/18,\n  thinmathspace:                   3/18,\n  mediummathspace:                 4/18,\n  thickmathspace:                  5/18,\n  verythickmathspace:              6/18,\n  veryverythickmathspace:          7/18,\n  negativeveryverythinmathspace:  -1/18,\n  negativeverythinmathspace:      -2/18,\n  negativethinmathspace:          -3/18,\n  negativemediummathspace:        -4/18,\n  negativethickmathspace:         -5/18,\n  negativeverythickmathspace:     -6/18,\n  negativeveryverythickmathspace: -7/18,\n  /* tslint:enable */\n\n  thin:   .04,\n  medium: .06,\n  thick:  .1,\n\n  normal:  1,\n  big:     2,\n  small:   1 / Math.sqrt(2),\n\n  infinity:  BIGDIMEN\n};\n\n\n/**\n * @param {string|number} length  A dimension (giving number and units) to be converted to ems\n * @param {number} size           The default size of the dimension (for percentage values)\n * @param {number} scale          The current scaling factor (to handle absolute units)\n * @param {number} em             The size of an em in pixels\n * @return {number}               The dimension converted to ems\n */\nexport function length2em(length: string | number, size: number = 0, scale: number = 1, em: number = 16): number {\n  if (typeof length !== 'string') {\n    length = String(length);\n  }\n  if (length === '' || length == null) {\n    return size;\n  }\n  if (MATHSPACE[length]) {\n    return MATHSPACE[length];\n  }\n  let match = length.match(/^\\s*([-+]?(?:\\.\\d+|\\d+(?:\\.\\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/);\n  if (!match) {\n    return size;\n  }\n  let m = parseFloat(match[1] || '1'), unit = match[2];\n  if (UNITS.hasOwnProperty(unit)) {\n    return m * UNITS[unit] / em / scale;\n  }\n  if (RELUNITS.hasOwnProperty(unit)) {\n    return m * RELUNITS[unit];\n  }\n  if (unit === '%') {\n    return m / 100 * size;  // percentage of the size\n  }\n  return m * size;            // relative to size\n}\n\n/**\n * @param {number} m  A number to be shown as a percent\n * @return {string}   The number m as a percent\n */\nexport function percent(m: number): string {\n  return (100 * m).toFixed(1).replace(/\\.?0+$/, '') + '%';\n}\n\n/**\n * @param {number} m  A number to be shown in ems\n * @return {string}   The number with units of ems\n */\nexport function em(m: number): string {\n  if (Math.abs(m) < .001) return '0';\n  return (m.toFixed(3).replace(/\\.?0+$/, '')) + 'em';\n}\n\n/**\n * @param {number} m   A number to be shown in ems, but rounded to pixel boundaries\n * @param {number} em  The number of pixels in an em\n * @return {string}    The number with units of em\n */\nexport function emRounded(m: number, em: number = 16): string {\n  m = (Math.round(m * em) + .05) / em;\n  if (Math.abs(m) < .001) return '0em';\n  return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n}\n\n\n/**\n * @param {number} m   A number of em's to be shown as pixels\n * @param {number} M   The minimum number of pixels to allow\n * @param {number} em  The number of pixels in an em\n * @return {string}    The number with units of px\n */\nexport function px(m: number, M: number = -BIGDIMEN, em: number = 16): string {\n  m *= em;\n  if (M && m < M) m = M;\n  if (Math.abs(m) < .1) return '0';\n  return m.toFixed(1).replace(/\\.0$/, '') + 'px';\n}\n"], "mappings": ";;;;;;;;;;AA0Ba,YAAA,WAAW;AAKX,YAAA,QAAkC;MAC7C,IAAI;MACJ,MAAM;MACN,IAAI,KAAK;MACT,IAAI,KAAK;;AAME,YAAA,WAAqC;MAChD,IAAI;MACJ,IAAI;MACJ,IAAI,IAAI;MACR,IAAI,KAAK;MACT,IAAI,IAAI;;AAMG,YAAA,YAAsC;MAEjD,uBAAiC,IAAE;MACnC,mBAAiC,IAAE;MACnC,eAAiC,IAAE;MACnC,iBAAiC,IAAE;MACnC,gBAAiC,IAAE;MACnC,oBAAiC,IAAE;MACnC,wBAAiC,IAAE;MACnC,+BAAgC,KAAG;MACnC,2BAAgC,KAAG;MACnC,uBAAgC,KAAG;MACnC,yBAAgC,KAAG;MACnC,wBAAgC,KAAG;MACnC,4BAAgC,KAAG;MACnC,gCAAgC,KAAG;MAGnC,MAAQ;MACR,QAAQ;MACR,OAAQ;MAER,QAAS;MACT,KAAS;MACT,OAAS,IAAI,KAAK,KAAK,CAAC;MAExB,UAAW,QAAA;;AAWb,aAAgB,UAAU,QAAyB,MAAkB,OAAmBA,KAAe;AAApD,UAAA,SAAA,QAAA;AAAA,eAAA;MAAgB;AAAE,UAAA,UAAA,QAAA;AAAA,gBAAA;MAAiB;AAAE,UAAAA,QAAA,QAAA;AAAA,QAAAA,MAAA;MAAe;AACrG,UAAI,OAAO,WAAW,UAAU;AAC9B,iBAAS,OAAO,MAAM;;AAExB,UAAI,WAAW,MAAM,UAAU,MAAM;AACnC,eAAO;;AAET,UAAI,QAAA,UAAU,MAAM,GAAG;AACrB,eAAO,QAAA,UAAU,MAAM;;AAEzB,UAAI,QAAQ,OAAO,MAAM,oEAAoE;AAC7F,UAAI,CAAC,OAAO;AACV,eAAO;;AAET,UAAI,IAAI,WAAW,MAAM,CAAC,KAAK,GAAG,GAAG,OAAO,MAAM,CAAC;AACnD,UAAI,QAAA,MAAM,eAAe,IAAI,GAAG;AAC9B,eAAO,IAAI,QAAA,MAAM,IAAI,IAAIA,MAAK;;AAEhC,UAAI,QAAA,SAAS,eAAe,IAAI,GAAG;AACjC,eAAO,IAAI,QAAA,SAAS,IAAI;;AAE1B,UAAI,SAAS,KAAK;AAChB,eAAO,IAAI,MAAM;;AAEnB,aAAO,IAAI;IACb;AAzBA,YAAA,YAAA;AA+BA,aAAgB,QAAQ,GAAS;AAC/B,cAAQ,MAAM,GAAG,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAI;IACtD;AAFA,YAAA,UAAA;AAQA,aAAgB,GAAG,GAAS;AAC1B,UAAI,KAAK,IAAI,CAAC,IAAI;AAAM,eAAO;AAC/B,aAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAK;IAChD;AAHA,YAAA,KAAA;AAUA,aAAgB,UAAU,GAAWA,KAAe;AAAf,UAAAA,QAAA,QAAA;AAAA,QAAAA,MAAA;MAAe;AAClD,WAAK,KAAK,MAAM,IAAIA,GAAE,IAAI,QAAOA;AACjC,UAAI,KAAK,IAAI,CAAC,IAAI;AAAM,eAAO;AAC/B,aAAO,EAAE,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAI;IAC9C;AAJA,YAAA,YAAA;AAaA,aAAgB,GAAG,GAAW,GAAuBA,KAAe;AAAtC,UAAA,MAAA,QAAA;AAAA,YAAA,CAAa,QAAA;MAAQ;AAAE,UAAAA,QAAA,QAAA;AAAA,QAAAA,MAAA;MAAe;AAClE,WAAKA;AACL,UAAI,KAAK,IAAI;AAAG,YAAI;AACpB,UAAI,KAAK,IAAI,CAAC,IAAI;AAAI,eAAO;AAC7B,aAAO,EAAE,QAAQ,CAAC,EAAE,QAAQ,QAAQ,EAAE,IAAI;IAC5C;AALA,YAAA,KAAA;;;", "names": ["em"]}