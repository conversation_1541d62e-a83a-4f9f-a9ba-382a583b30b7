{"version": 3, "file": "ParseMethods.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/ParseMethods.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,8DAAqC;AACrC,qDAA8C;AAE9C,gEAAuC;AAGvC,IAAU,YAAY,CAkIrB;AAlID,WAAU,YAAY;IAOpB,SAAgB,QAAQ,CAAC,MAAiB,EAAE,CAAS;QAEnD,IAAM,GAAG,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACzC,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,GAAG,CAAC,sBAAsB,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE;YACjD,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,sBAAuC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACzB,IAAI,GAAG,CAAC,WAAW,KAAK,6BAAW,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClF,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;aACpB;SACF;QAED,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAde,qBAAQ,WAcvB,CAAA;IASD,SAAgB,KAAK,CAAC,MAAiB,EAAE,CAAS;QAChD,IAAI,GAAY,CAAC;QACjB,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAM,GAAG,GAAG,sBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,EAAE;YAEL,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM;YAEL,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SAC5C;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAfe,kBAAK,QAepB,CAAA;IAOD,SAAgB,eAAe,CAAC,MAAiB,EAAE,EAAU;QAC3D,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5B,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IAHe,4BAAe,kBAG9B,CAAA;IAQD,SAAgB,WAAW,CAAC,MAAiB,EAAE,KAAa;QAC1D,IAAM,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,EAAC,WAAW,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,EAAC,CAAC;QAE1E,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IALe,wBAAW,cAK1B,CAAA;IAOD,SAAgB,WAAW,CAAC,MAAiB,EAAE,KAAa;QAC1D,IAAM,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;QAExB,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAElD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IATe,wBAAW,cAS1B,CAAA;IAOD,SAAgB,SAAS,CAAC,MAAiB,EAAE,KAAa;QACxD,IAAM,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,EAAC,WAAW,EAAE,6BAAW,CAAC,OAAO,CAAC,MAAM,EAAC,CAAC;QAC1E,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAE5B,GAAG,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC/C;QAED,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IATe,sBAAS,YASxB,CAAA;IAOD,SAAgB,SAAS,CAAC,MAAiB,EAAE,KAAa;QACxD,IAAI,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;QAEjC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,EAAE,GAAG,CAAC,CAAC;QAC1D,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IANe,sBAAS,YAMxB,CAAA;IAUD,SAAgB,WAAW,CAAC,MAAiB,EAAE,GAAW,EAAE,IAAc,EAAE,IAAW;QACrF,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,EAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC,CAAC;QAClF,GAAG,GAAG,IAAI,8BAAC,MAAM,EAAE,GAAG,UAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,UAAC,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IALe,wBAAW,cAK1B,CAAA;AAEH,CAAC,EAlIS,YAAY,KAAZ,YAAY,QAkIrB;AAED,kBAAe,YAAY,CAAC"}