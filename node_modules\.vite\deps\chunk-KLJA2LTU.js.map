{"version": 3, "sources": ["../../mathjax-full/ts/input/tex/NodeUtil.ts", "../../mathjax-full/ts/input/tex/TexError.ts", "../../mathjax-full/ts/input/tex/ParseUtil.ts", "../../mathjax-full/ts/input/tex/Stack.ts", "../../mathjax-full/ts/input/tex/TexParser.ts", "../../mathjax-full/ts/input/tex/StackItem.ts", "../../mathjax-full/ts/input/tex/StackItemFactory.ts", "../../mathjax-full/ts/input/tex/NodeFactory.ts", "../../mathjax-full/ts/input/tex/ParseOptions.ts", "../../mathjax-full/ts/input/tex/Tags.ts", "../../mathjax-full/ts/input/tex/MapHandler.ts", "../../mathjax-full/ts/input/tex/Configuration.ts", "../../mathjax-full/ts/input/tex/Symbol.ts", "../../mathjax-full/ts/input/tex/SymbolMap.ts", "../../mathjax-full/ts/input/tex/base/BaseItems.ts", "../../mathjax-full/ts/input/tex/TexConstants.ts", "../../mathjax-full/ts/input/tex/base/BaseMethods.ts", "../../mathjax-full/ts/input/tex/ParseMethods.ts", "../../mathjax-full/ts/input/tex/base/BaseMappings.ts", "../../mathjax-full/ts/input/tex/base/BaseConfiguration.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Node utility methods.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {TextNode, MMLNODE, MmlNode, AbstractMmlNode, AbstractMmlEmptyNode} from '../../core/MmlTree/MmlNode.js';\nimport {MmlMo} from '../../core/MmlTree/MmlNodes/mo.js';\nimport {Property, PropertyList} from '../../core/Tree/Node.js';\nimport {Args} from './Types.js';\nimport {OperatorDef} from '../../core/MmlTree/OperatorDictionary.js';\n\n\nnamespace NodeUtil {\n\n  const attrs: Map<String, boolean> = new Map([\n    ['autoOP', true],\n    ['fnOP', true],\n    ['movesupsub', true],\n    ['subsupOK', true],\n    ['texprimestyle', true],\n    ['useHeight', true],\n    ['variantForm', true],\n    ['withDelims', true],\n    ['mathaccent', true],\n    ['open', true],\n    ['close', true]\n  ]);\n\n\n  /**\n   * Creates a single character from a unicode hex string.\n   * @param {string} code The code.\n   * @return {string} The newly created entity.\n   */\n  export function createEntity(code: string): string  {\n    return String.fromCodePoint(parseInt(code, 16));\n  }\n\n\n  /**\n   * Get the children of the a node.\n   * @param {MmlNode} node The node.\n   * @return {MMLNODE[]} Its children.\n   */\n  export function getChildren(node: MmlNode): MMLNODE[] {\n    return (node.childNodes as MMLNODE[]);\n  }\n\n\n  /**\n   * Get text content of a node.\n   * @param {TextNode} node The node.\n   * @return {string} Its text content.\n   */\n  export function getText(node: TextNode): string {\n    return node.getText();\n  }\n\n\n  /**\n   * Append children to a node.\n   * @param {MmlNode} node The node.\n   * @param {MMLNODE[]} children A list of new children.\n   */\n  export function appendChildren(node: MmlNode, children: MMLNODE[])  {\n    for (let child of children) {\n      node.appendChild(child);\n    }\n  }\n\n\n  /**\n   * Sets an attribute of a node.\n   * @param {MmlNode} node The node.\n   * @param {string} attribute An attribute.\n   * @param {Args} value The attribute value.\n   */\n  export function setAttribute(node: MmlNode, attribute: string, value: Args) {\n    node.attributes.set(attribute, value);\n  }\n\n\n  /**\n   * Sets a property of a node.\n   * @param {MmlNode} node The node.\n   * @param {string} property The property.\n   * @param {Args} value The property value.\n   */\n  export function setProperty(node: MmlNode, property: string, value: Args) {\n    node.setProperty(property, value);\n  }\n\n\n  /**\n   * Sets properties and attributes of a node.\n   * @param {MmlNode} node The node.\n   * @param {PropertyList} properties A list of property/attribute value pairs.\n   */\n  export function setProperties(node: MmlNode, properties: PropertyList) {\n    for (const name of Object.keys(properties)) {\n      let value = properties[name];\n      if (name === 'texClass') {\n        node.texClass = (value as number);\n        node.setProperty(name, value);\n      } else if (name === 'movablelimits') {\n        node.setProperty('movablelimits', value);\n        if (node.isKind('mo') || node.isKind('mstyle')) {\n          node.attributes.set('movablelimits', value);\n        }\n      } else if (name === 'inferred') {\n        // ignore\n      } else if (attrs.has(name)) {\n        node.setProperty(name, value);\n      } else {\n        node.attributes.set(name, value);\n      }\n    }\n  }\n\n\n  /**\n   * Returns the property of a node.\n   * @param {MmlNode} node The node.\n   * @param {string} property A property name.\n   * @return {Property} Value of the property.\n   */\n  export function getProperty(node: MmlNode, property: string): Property  {\n    return node.getProperty(property);\n  }\n\n\n  /**\n   * Returns the attribute of a node.\n   * @param {MmlNode} node The node.\n   * @param {string} attr A attribute name.\n   * @return {Property} Value of the attribute.\n   */\n  export function getAttribute(node: MmlNode, attr: string): Property  {\n    return node.attributes.get(attr);\n  }\n\n\n  /**\n   * Removes a set of properties from a node.\n   * @param {MmlNode} node The node.\n   * @param {string[]} ...properties  A list of properties.\n   */\n  export function removeProperties(node: MmlNode, ...properties: string[]) {\n    node.removeProperty(...properties);\n  }\n\n\n  /**\n   * Returns a child node at a given position.\n   * @param {MmlNode} node The node.\n   * @param {number} position The position of the child.\n   * @return {MMLNODE} The child node at position.\n   */\n  export function getChildAt(node: MmlNode, position: number): MMLNODE {\n    return (node.childNodes[position] as MMLNODE);\n  }\n\n\n  /**\n   * Set node child at position.\n   * @param {MmlNode} node The node.\n   * @param {number} position The position of the new child.\n   * @param {MmlNode} child The new child.\n   */\n  export function setChild(node: MmlNode, position: number, child: MmlNode) {\n    let children = node.childNodes;\n    children[position] = child;\n    if (child) {\n      child.parent = node;\n    }\n  }\n\n\n  /**\n   * Copies children between nodes.\n   * @param {MmlNode} oldNode The source node.\n   * @param {MmlNode} newNode The target node.\n   */\n  export function copyChildren(oldNode: MmlNode, newNode: MmlNode) {\n    let children = oldNode.childNodes as (TextNode | MmlNode)[];\n    for (let i = 0; i < children.length; i++) {\n      setChild(newNode, i, children[i]);\n    }\n  }\n\n\n  /**\n   * Copies attributes between nodes.\n   * @param {MmlNode} oldNode The source node.\n   * @param {MmlNode} newNode The target node.\n   */\n  export function copyAttributes(oldNode: MmlNode, newNode: MmlNode) {\n    newNode.attributes = oldNode.attributes;\n    setProperties(newNode, oldNode.getAllProperties());\n  }\n\n\n  /**\n   * Checks if node is of a particular type.\n   * @param {MmlNode} node The node.\n   * @param {string} kind The type to check.\n   * @return {boolean} True if node is of the given type.\n   */\n  export function isType(node: MmlNode, kind: string): boolean  {\n    return node.isKind(kind);\n  }\n\n\n  /**\n   * Checks if the node is embellished.\n   * @param {MmlNode} node The node.\n   * @return {boolean} True if node is embellished.\n   */\n  export function isEmbellished(node: MmlNode): boolean {\n    return node.isEmbellished;\n  }\n\n\n  /**\n   * Gets the texclass of a node.\n   * @param {MmlNode} node The node.\n   * @return {number} Its texclass.\n   */\n  export function getTexClass(node: MmlNode): number  {\n    return node.texClass;\n  }\n\n\n  /**\n   * Gets the mo element at the core of the node.\n   * @param {MmlNode} node The node.\n   * @return {MmlNode} The MO node at the core.\n   */\n  export function getCoreMO(node: MmlNode): MmlNode  {\n    return node.coreMO();\n  }\n\n\n  /**\n   * Checks if an object is a node.\n   * @param {any} item The object.\n   * @return {boolean} True if it is a node.\n   */\n  export function isNode(item: any): boolean  {\n    return item instanceof AbstractMmlNode || item instanceof AbstractMmlEmptyNode;\n  }\n\n\n  /**\n   * Checks if the node is an inferred mrow.\n   * @param {MmlNode} node The node.\n   * @return {boolean} True if the node is an inferred mrow.\n   */\n  export function isInferred(node: MmlNode): boolean {\n    return node.isInferred;\n  }\n\n\n  /**\n   * Gets the operator definition of a node.\n   * @param {MmlNode} node The node.\n   * @return {OperatorDef} If node is an MO returns the operator definition. O/w\n   *    null.\n   */\n  export function getForm(node: MmlNode): OperatorDef {\n    if (!isType(node, 'mo')) {\n      return null;\n    }\n    let mo = node as MmlMo;\n    let forms = mo.getForms();\n    for (let form of forms) {\n      let symbol = MmlMo.OPTABLE[form][mo.getText()];\n      if (symbol) {\n        return symbol;\n      }\n    }\n    return null;\n  }\n\n}\n\nexport default NodeUtil;\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Error class for the TeX parser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\n\nexport default class TexError {\n\n  private static pattern =\n    /%(\\d+|\\{\\d+\\}|\\{[a-z]+:\\%\\d+(?:\\|(?:%\\{\\d+\\}|%.|[^\\}])*)+\\}|.)/g;\n\n  /**\n   * Default error message.\n   * @type {string}\n   */\n  public message: string;\n\n  /**\n   * The old MathJax processing function.\n   * @param {string} str The basic error message.\n   * @param {string[]} args The arguments to be replaced in the error message.\n   * @return {string} The processed error string.\n   */\n  private static processString(str: string, args: string[]): string {\n    let parts = str.split(TexError.pattern);\n    for (let i = 1, m = parts.length; i < m; i += 2) {\n      let c = parts[i].charAt(0);  // first char will be { or \\d or a char to be\n                                   // kept literally\n      if (c >= '0' && c <= '9') {    // %n\n        parts[i] = args[parseInt(parts[i], 10) - 1];\n        if (typeof parts[i] === 'number') {\n          parts[i] = parts[i].toString();\n        }\n      } else if (c === '{') {        // %{n} or %{plural:%n|...}\n        c = parts[i].substr(1);\n        if (c >= '0' && c <= '9') {  // %{n}\n          parts[i] = args[parseInt(parts[i].substr(1, parts[i].length - 2), 10) - 1];\n          if (typeof parts[i] === 'number') {\n            parts[i] = parts[i].toString();\n          }\n        } else {                     // %{plural:%n|...}\n          let match = parts[i].match(/^\\{([a-z]+):%(\\d+)\\|(.*)\\}$/);\n          if (match) {\n            // Removed plural here.\n            parts[i] = '%' + parts[i];\n          }\n        }\n      }\n      if (parts[i] == null) {\n        parts[i] = '???';\n      }\n    }\n    return parts.join('');\n  }\n\n  /**\n   * @constructor\n   * @param{string} id        message id (for localization)\n   * @param{string} message   text of English message\n   * @param{string[]=} rest   any substitution arguments\n   */\n  constructor(public id: string, message: string, ...rest: string[]) {\n    this.message = TexError.processString(message, rest);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview A namespace for utility functions for the TeX Parser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {TEXCLASS, MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {EnvList} from './StackItem.js';\nimport {ArrayItem} from './base/BaseItems.js';\nimport ParseOptions from './ParseOptions.js';\nimport NodeUtil from './NodeUtil.js';\nimport TexParser from './TexParser.js';\nimport TexError from './TexError.js';\nimport {entities} from '../../util/Entities.js';\nimport {MmlMunderover} from '../../core/MmlTree/MmlNodes/munderover.js';\n\n\nnamespace ParseUtil {\n\n  // TODO (VS): Combine some of this with lengths in util.\n  const emPerInch = 7.2;\n  const pxPerInch = 72;\n  // Note, the following are TeX CM font values.\n  const UNIT_CASES: {[key: string]: ((m: number) => number)}  = {\n    'em': m => m,\n    'ex': m => m * .43,\n    'pt': m => m / 10,                    // 10 pt to an em\n    'pc': m => m * 1.2,                   // 12 pt to a pc\n    'px': m => m * emPerInch / pxPerInch,\n    'in': m => m * emPerInch,\n    'cm': m => m * emPerInch / 2.54, // 2.54 cm to an inch\n    'mm': m => m * emPerInch / 25.4, // 10 mm to a cm\n    'mu': m => m / 18,\n  };\n  const num = '([-+]?([.,]\\\\d+|\\\\d+([.,]\\\\d*)?))';\n  const unit = '(pt|em|ex|mu|px|mm|cm|in|pc)';\n  const dimenEnd = RegExp('^\\\\s*' + num + '\\\\s*' + unit + '\\\\s*$');\n  const dimenRest = RegExp('^\\\\s*' + num + '\\\\s*' + unit + ' ?');\n\n\n  /**\n   * Matches for a dimension argument.\n   * @param {string} dim The argument.\n   * @param {boolean} rest Allow for trailing garbage in the dimension string.\n   * @return {[string, string, number]} The match result as (Anglosaxon) value,\n   *     unit name, length of matched string. The latter is interesting in the\n   *     case of trailing garbage.\n   */\n  export function matchDimen(\n    dim: string, rest: boolean = false): [string, string, number] {\n      let match = dim.match(rest ? dimenRest : dimenEnd);\n      return match ?\n        muReplace([match[1].replace(/,/, '.'), match[4], match[0].length]) :\n        [null, null, 0];\n  }\n\n\n  /**\n   * Transforms mu dimension to em if necessary.\n   * @param {[string, string, number]} [value, unit, length] The dimension triple.\n   * @return {[string, string, number]} [value, unit, length] The transformed triple.\n   */\n  function muReplace([value, unit, length]: [string, string, number]): [string, string, number] {\n    if (unit !== 'mu') {\n      return [value, unit, length];\n    }\n    let em = Em(UNIT_CASES[unit](parseFloat(value || '1')));\n    return [em.slice(0, -2), 'em', length];\n  }\n\n\n  /**\n   * Convert a dimension string into standard em dimension.\n   * @param {string} dim The attribute string.\n   * @return {number} The numerical value.\n   */\n  export function dimen2em(dim: string): number {\n    let [value, unit] = matchDimen(dim);\n    let m = parseFloat(value || '1');\n    let func = UNIT_CASES[unit];\n    return func ? func(m) : 0;\n  }\n\n\n  /**\n   * Turns a number into an em value.\n   * @param {number} m The number.\n   * @return {string} The em dimension string.\n   */\n  export function Em(m: number): string {\n    if (Math.abs(m) < .0006) {\n      return '0em';\n    }\n    return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n  }\n\n\n  /**\n   * Takes an array of numbers and returns a space-separated string of em values.\n   * @param {number[]} W  The widths to be turned into em values\n   * @return {string}     The numbers with em units, separated by spaces.\n   */\n  export function cols(...W: number[]): string {\n    return W.map(n => Em(n)).join(' ');\n  }\n\n\n  /**\n   * Create an mrow that has stretchy delimiters at either end, as needed\n   * @param {ParseOptions} configuration Current parse options.\n   * @param {string} open The opening fence.\n   * @param {MmlNode} mml The enclosed node.\n   * @param {string} close The closing fence.\n   * @param {string=} big Bigg command.\n   */\n  export function fenced(configuration: ParseOptions, open: string, mml: MmlNode,\n                         close: string, big: string = '', color: string = '') {\n    // @test Fenced, Fenced3\n    let nf = configuration.nodeFactory;\n    let mrow = nf.create('node', 'mrow', [],\n                         {open: open, close: close, texClass: TEXCLASS.INNER});\n    let mo;\n    if (big) {\n      mo = new TexParser('\\\\' + big + 'l' + open, configuration.parser.stack.env, configuration).mml();\n    } else {\n      let openNode = nf.create('text', open);\n      mo = nf.create('node', 'mo', [],\n                     {fence: true, stretchy: true, symmetric: true, texClass: TEXCLASS.OPEN},\n                     openNode);\n    }\n    NodeUtil.appendChildren(mrow, [mo, mml]);\n    if (big) {\n      mo = new TexParser('\\\\' + big + 'r' + close, configuration.parser.stack.env, configuration).mml();\n    } else {\n      let closeNode = nf.create('text', close);\n      mo = nf.create('node', 'mo', [],\n                     {fence: true, stretchy: true, symmetric: true, texClass: TEXCLASS.CLOSE},\n                     closeNode);\n    }\n    color && mo.attributes.set('mathcolor', color);\n    NodeUtil.appendChildren(mrow, [mo]);\n    return mrow;\n  }\n\n\n  /**\n   *  Create an mrow that has \\\\mathchoice using \\\\bigg and \\\\big for the delimiters.\n   * @param {ParseOptions} configuration The current parse options.\n   * @param {string} open The opening fence.\n   * @param {MmlNode} mml The enclosed node.\n   * @param {string} close The closing fence.\n   * @return {MmlNode} The mrow node.\n   */\n  export function fixedFence(configuration: ParseOptions, open: string,\n                             mml: MmlNode, close: string): MmlNode {\n    // @test Choose, Over With Delims, Above with Delims\n    let mrow = configuration.nodeFactory.create('node',\n      'mrow', [], {open: open, close: close, texClass: TEXCLASS.ORD});\n    if (open) {\n      NodeUtil.appendChildren(mrow, [mathPalette(configuration, open, 'l')]);\n    }\n    if (NodeUtil.isType(mml, 'mrow')) {\n      NodeUtil.appendChildren(mrow, NodeUtil.getChildren(mml));\n    } else {\n      NodeUtil.appendChildren(mrow, [mml]);\n    }\n    if (close) {\n      NodeUtil.appendChildren(mrow, [mathPalette(configuration, close, 'r')]);\n    }\n    return mrow;\n  }\n\n\n  /**\n   * Generates a mathchoice element for fences. These will be resolved later,\n   * once the position, and therefore size, of the of the fenced expression is\n   * known.\n   * @param {ParseOptions} configuration The current parse otpions.\n   * @param {string} fence The fence.\n   * @param {string} side The side of the fence (l or r).\n   * @return {MmlNode} The mathchoice node.\n   */\n  export function mathPalette(configuration: ParseOptions, fence: string,\n                              side: string): MmlNode  {\n    if (fence === '{' || fence === '}') {\n      fence = '\\\\' + fence;\n    }\n    let D = '{\\\\bigg' + side + ' ' + fence + '}';\n    let T = '{\\\\big' + side + ' ' + fence + '}';\n    return new TexParser('\\\\mathchoice' + D + T + T + T, {}, configuration).mml();\n  }\n\n\n  /**\n   * If the initial child, skipping any initial space or\n   * empty braces (TeXAtom with child being an empty inferred row),\n   * is an <mo>, precede it by an empty <mi> to force the <mo> to\n   * be infix.\n   * @param {ParseOptions} configuration The current parse options.\n   * @param {MmlNode[]} nodes The row of nodes to scan for an initial <mo>\n   */\n  export function fixInitialMO(configuration: ParseOptions, nodes: MmlNode[]) {\n    for (let i = 0, m = nodes.length; i < m; i++) {\n      let child = nodes[i];\n      if (child && (!NodeUtil.isType(child, 'mspace') &&\n                    (!NodeUtil.isType(child, 'TeXAtom') ||\n                     (NodeUtil.getChildren(child)[0] &&\n                      NodeUtil.getChildren(NodeUtil.getChildren(child)[0]).length)))) {\n        if (NodeUtil.isEmbellished(child) ||\n            (NodeUtil.isType(child, 'TeXAtom') && NodeUtil.getTexClass(child) === TEXCLASS.REL)) {\n          let mi = configuration.nodeFactory.create('node', 'mi');\n          nodes.unshift(mi);\n        }\n        break;\n      }\n    }\n  }\n\n\n  /**\n   * Break up a string into text and math blocks.\n   * @param {TexParser} parser The calling parser.\n   * @param {string} text The text in the math expression to parse.\n   * @param {number|string=} level The scriptlevel.\n   * @param {string} font The mathvariant to use\n   * @return {MmlNode[]} The nodes corresponding to the internal math expression.\n   */\n  export function internalMath(parser: TexParser, text: string,\n                               level?: number | string, font?: string): MmlNode[] {\n    if (parser.configuration.options.internalMath) {\n      return parser.configuration.options.internalMath(parser, text, level, font);\n    }\n    let mathvariant = font || parser.stack.env.font;\n    let def = (mathvariant ? {mathvariant} : {});\n    let mml: MmlNode[] = [], i = 0, k = 0, c, node, match = '', braces = 0;\n    if (text.match(/\\\\?[${}\\\\]|\\\\\\(|\\\\(eq)?ref\\s*\\{/)) {\n      while (i < text.length) {\n        c = text.charAt(i++);\n        if (c === '$') {\n          if (match === '$' && braces === 0) {\n            // @test Interspersed Text\n            node = parser.create(\n              'node', 'TeXAtom',\n              [(new TexParser(text.slice(k, i - 1), {}, parser.configuration)).mml()]);\n            mml.push(node);\n            match = '';\n            k = i;\n          } else if (match === '') {\n            // @test Interspersed Text\n            if (k < i - 1) {\n              // @test Interspersed Text\n              mml.push(internalText(parser, text.slice(k, i - 1), def));\n            }\n            match = '$';\n            k = i;\n          }\n        } else if (c === '{' && match !== '') {\n          // @test Mbox Mbox, Mbox Math\n          braces++;\n        } else if (c === '}') {\n          // @test Mbox Mbox, Mbox Math\n          if (match === '}' && braces === 0) {\n            // @test Mbox Eqref, Mbox Math\n            let atom = (new TexParser(text.slice(k, i), {}, parser.configuration)).mml();\n            node = parser.create('node', 'TeXAtom', [atom], def);\n            mml.push(node);\n            match = '';\n            k = i;\n          } else if (match !== '') {\n            // @test Mbox Math, Mbox Mbox\n            if (braces) {\n              // @test Mbox Math, Mbox Mbox\n              braces--;\n            }\n          }\n        } else if (c === '\\\\') {\n          // @test Mbox Eqref, Mbox CR\n          if (match === '' && text.substr(i).match(/^(eq)?ref\\s*\\{/)) {\n            // @test Mbox Eqref\n            let len = ((RegExp as any)['$&'] as string).length;\n            if (k < i - 1) {\n              // @test Mbox Eqref\n              mml.push(internalText(parser, text.slice(k, i - 1), def));\n            }\n            match = '}';\n            k = i - 1;\n            i += len;\n          } else {\n            // @test Mbox CR, Mbox Mbox\n            c = text.charAt(i++);\n            if (c === '(' && match === '') {\n              // @test Mbox Internal Display\n              if (k < i - 2) {\n                // @test Mbox Internal Display\n                mml.push(internalText(parser, text.slice(k, i - 2), def));\n              }\n              match = ')'; k = i;\n            } else if (c === ')' && match === ')' && braces === 0) {\n              // @test Mbox Internal Display\n              node = parser.create(\n                'node', 'TeXAtom',\n                [(new TexParser(text.slice(k, i - 2), {}, parser.configuration)).mml()]);\n              mml.push(node);\n              match = '';\n              k = i;\n            } else if (c.match(/[${}\\\\]/) && match === '')  {\n              // @test Mbox CR\n              i--;\n              text = text.substr(0, i - 1) + text.substr(i); // remove \\ from \\$, \\{, \\}, or \\\\\n            }\n          }\n        }\n      }\n      if (match !== '') {\n        // @test Internal Math Error\n        throw new TexError('MathNotTerminated', 'Math not terminated in text box');\n      }\n    }\n    if (k < text.length) {\n      // @test Interspersed Text, Mbox Mbox\n      mml.push(internalText(parser, text.slice(k), def));\n    }\n    if (level != null) {\n      // @test Label, Fbox, Hbox\n      mml = [parser.create('node', 'mstyle', mml, {displaystyle: false, scriptlevel: level})];\n    } else if (mml.length > 1) {\n      // @test Interspersed Text\n      mml = [parser.create('node', 'mrow', mml)];\n    }\n    return mml;\n  }\n\n\n  /**\n   * Parses text internal to boxes or labels.\n   * @param {TexParser} parser The current tex parser.\n   * @param {string} text The text to parse.\n   * @param {EnvList} def The attributes of the text node.\n   * @return {MmlNode} The text node.\n   */\n  export function internalText(parser: TexParser, text: string, def: EnvList): MmlNode {\n    // @test Label, Fbox, Hbox\n    text = text.replace(/^\\s+/, entities.nbsp).replace(/\\s+$/, entities.nbsp);\n    let textNode = parser.create('text', text);\n    return parser.create('node', 'mtext', [], def, textNode);\n  }\n\n  /**\n   * Create an munderover node with the given script position.\n   * @param {TexParser} parser   The current TeX parser.\n   * @param {MmlNode} base       The base node.\n   * @param {MmlNode} script     The under- or over-script.\n   * @param {string} pos         Either 'over' or 'under'.\n   * @param {boolean} stack      True if super- or sub-scripts should stack.\n   * @return {MmlNode}           The generated node (MmlMunderover or TeXAtom)\n   */\n  export function underOver(parser: TexParser, base: MmlNode, script: MmlNode, pos: string, stack: boolean): MmlNode {\n    // @test Overline\n    ParseUtil.checkMovableLimits(base);\n    if (NodeUtil.isType(base, 'munderover') && NodeUtil.isEmbellished(base)) {\n      // @test Overline Limits\n      NodeUtil.setProperties(NodeUtil.getCoreMO(base), {lspace: 0, rspace: 0});\n      const mo = parser.create('node', 'mo', [], {rspace: 0});\n      base = parser.create('node', 'mrow', [mo, base]);\n      // TODO? add an empty <mi> so it's not embellished any more\n    }\n    const mml = parser.create('node', 'munderover', [base]) as MmlMunderover;\n    NodeUtil.setChild(mml, pos === 'over' ?  mml.over : mml.under, script);\n    let node: MmlNode = mml;\n    if (stack) {\n      // @test Overbrace 1 2 3, Underbrace, Overbrace Op 1 2\n      node = parser.create('node', 'TeXAtom', [mml], {texClass: TEXCLASS.OP, movesupsub: true});\n    }\n    NodeUtil.setProperty(node, 'subsupOK', true);\n    return node;\n  }\n\n  /**\n   * Set movablelimits to false if necessary.\n   * @param {MmlNode} base   The base node being tested.\n   */\n  export function checkMovableLimits(base: MmlNode) {\n    const symbol = (NodeUtil.isType(base, 'mo') ? NodeUtil.getForm(base) : null);\n    if (NodeUtil.getProperty(base, 'movablelimits') || (symbol && symbol[3] && symbol[3].movablelimits)) {\n      // @test Overline Sum\n      NodeUtil.setProperties(base, {movablelimits: false});\n    }\n  }\n\n  /**\n   * Trim spaces from a string.\n   * @param {string} text The string to clean.\n   * @return {string} The string with leading and trailing whitespace removed.\n   */\n  export function trimSpaces(text: string): string {\n    if (typeof(text) !== 'string') {\n      return text;\n    }\n    let TEXT = text.trim();\n    if (TEXT.match(/\\\\$/) && text.match(/ $/)) {\n      TEXT += ' ';\n    }\n    return TEXT;\n  }\n\n\n  /**\n   * Sets alignment in array definitions.\n   * @param {ArrayItem} array The array item.\n   * @param {string} align The alignment string.\n   * @return {ArrayItem} The altered array item.\n   */\n  export function setArrayAlign(array: ArrayItem, align: string): ArrayItem {\n    // @test Array1, Array2, Array Test\n    align = ParseUtil.trimSpaces(align || '');\n    if (align === 't') {\n      array.arraydef.align = 'baseline 1';\n    } else if (align === 'b') {\n      array.arraydef.align = 'baseline -1';\n    } else if (align === 'c') {\n      array.arraydef.align = 'axis';\n    } else if (align) {\n      array.arraydef.align = align;\n    } // FIXME: should be an error?\n    return array;\n  }\n\n\n  /**\n   * Replace macro parameters with their values.\n   * @param {TexParser} parser The current TeX parser.\n   * @param {string[]} args A list of arguments for macro parameters.\n   * @param {string} str The macro parameter string.\n   * @return {string} The string with all parameters replaced by arguments.\n   */\n  export function substituteArgs(parser: TexParser, args: string[],\n                                 str: string): string {\n    let text = '';\n    let newstring = '';\n    let i = 0;\n    while (i < str.length) {\n      let c = str.charAt(i++);\n      if (c === '\\\\') {\n        text += c + str.charAt(i++);\n      }\n      else if (c === '#') {\n        c = str.charAt(i++);\n        if (c === '#') {\n          text += c;\n        } else {\n          if (!c.match(/[1-9]/) || parseInt(c, 10) > args.length) {\n            throw new TexError('IllegalMacroParam',\n                                'Illegal macro parameter reference');\n          }\n          newstring = addArgs(parser, addArgs(parser, newstring, text),\n                              args[parseInt(c, 10) - 1]);\n          text = '';\n        }\n      } else {\n        text += c;\n      }\n    }\n    return addArgs(parser, newstring, text);\n  }\n\n\n  /**\n   * Adds a new expanded argument to an already macro parameter string.  Makes\n   * sure that macros are followed by a space if their names could accidentally\n   * be continued into the following text.\n   * @param {TexParser} parser The current TeX parser.\n   * @param {string} s1 The already expanded string.\n   * @param {string} s2 The string to add.\n   * @return {string} The combined string.\n   */\n  export function addArgs(parser: TexParser, s1: string, s2: string): string {\n    if (s2.match(/^[a-z]/i) && s1.match(/(^|[^\\\\])(\\\\\\\\)*\\\\[a-z]+$/i)) {\n      s1 += ' ';\n    }\n    if (s1.length + s2.length > parser.configuration.options['maxBuffer']) {\n      throw new TexError('MaxBufferSize',\n                          'MathJax internal buffer size exceeded; is there a' +\n                          ' recursive macro call?');\n    }\n    return s1 + s2;\n  }\n\n  /**\n   * Report an error if there are too many macro substitutions.\n   * @param {TexParser} parser The current TeX parser.\n   * @param {boolean} isMacro  True if we are substituting a macro, false for environment.\n   */\n  export function checkMaxMacros(parser: TexParser, isMacro: boolean = true) {\n    if (++parser.macroCount <= parser.configuration.options['maxMacros']) {\n      return;\n    }\n    if (isMacro) {\n      throw new TexError('MaxMacroSub1',\n                         'MathJax maximum macro substitution count exceeded; ' +\n                         'is here a recursive macro call?');\n    } else {\n      throw new TexError('MaxMacroSub2',\n                         'MathJax maximum substitution count exceeded; ' +\n                         'is there a recursive latex environment?');\n    }\n  }\n\n\n  /**\n   *  Check for bad nesting of equation environments\n   */\n  export function checkEqnEnv(parser: TexParser) {\n    if (parser.stack.global.eqnenv) {\n      // @test ErroneousNestingEq\n      throw new TexError('ErroneousNestingEq', 'Erroneous nesting of equation structures');\n    }\n    parser.stack.global.eqnenv = true;\n  }\n\n  /**\n   * Copy an MmlNode and add it (and its children) to the proper lists.\n   *\n   * @param {MmlNode} node       The MmlNode to copy\n   * @param {TexParser} parser   The active tex parser\n   * @return {MmlNode}           The duplicate tree\n   */\n  export function copyNode(node: MmlNode, parser: TexParser): MmlNode  {\n    const tree = node.copy() as MmlNode;\n    const options = parser.configuration;\n    tree.walkTree((n: MmlNode) => {\n      options.addNode(n.kind, n);\n      const lists = (n.getProperty('in-lists') as string || '').split(/,/);\n      for (const list of lists) {\n        list && options.addNode(list, n);\n      }\n    });\n    return tree;\n  }\n\n  /**\n   * This is a placeholder for future security filtering of attributes.\n   * @param {TexParser} parser The current parser.\n   * @param {string} name The attribute name.\n   * @param {string} value The attribute value to filter.\n   * @return {string} The filtered value.\n   */\n  export function MmlFilterAttribute(_parser: TexParser, _name: string, value: string): string {\n    // TODO: Implement in security package.\n    return value;\n  }\n\n\n  /**\n   * Initialises an stack environment with current font definition in the parser.\n   * @param {TexParser} parser The current tex parser.\n   * @return {EnvList} The initialised environment list.\n   */\n  export function getFontDef(parser: TexParser): EnvList {\n    const font = parser.stack.env['font'];\n    return (font ? {mathvariant: font} : {});\n  }\n\n\n  /**\n   * Splits a package option list of the form [x=y,z=1] into an attribute list\n   * of the form {x: y, z: 1}.\n   * @param {string} attrib The attributes of the package.\n   * @param {{[key: string]: number}?} allowed A list of allowed options. If\n   *     given only allowed arguments are returned.\n   * @param {boolean?} error If true, raises an exception if not allowed options\n   *     are found.\n   * @return {EnvList} The attribute list.\n   */\n  export function keyvalOptions(attrib: string,\n                                allowed: {[key: string]: number} = null,\n                                error: boolean = false): EnvList {\n    let def: EnvList = readKeyval(attrib);\n    if (allowed) {\n      for (let key of Object.keys(def)) {\n        if (!allowed.hasOwnProperty(key)) {\n          if (error) {\n            throw new TexError('InvalidOption', 'Invalid option: %1', key);\n          }\n          delete def[key];\n        }\n      }\n    }\n    return def;\n  }\n\n\n  /**\n   * Implementation of the keyval function from https://www.ctan.org/pkg/keyval\n   * @param {string} text The optional parameter string for a package or\n   *     command.\n   * @return {EnvList} Set of options as key/value pairs.\n   */\n  function readKeyval(text: string): EnvList {\n    let options: EnvList = {};\n    let rest = text;\n    let end, key, val;\n    while (rest) {\n      [key, end, rest] = readValue(rest, ['=', ',']);\n      if (end === '=') {\n        [val, end, rest] = readValue(rest, [',']);\n        val = (val === 'false' || val === 'true') ?\n            JSON.parse(val) : val;\n        options[key] = val;\n      } else if (key) {\n        options[key] = true;\n      }\n    }\n    return options;\n  }\n\n\n  /**\n   * Removes pairs of outer braces.\n   * @param {string} text The string to clean.\n   * @param {number} count The number of outer braces to slice off.\n   * @return {string} The cleaned string.\n   */\n  function removeBraces(text: string, count: number): string {\n    while (count > 0) {\n      text = text.trim().slice(1, -1);\n      count--;\n    }\n    return text.trim();\n  }\n\n\n  /**\n   * Read a value from the given string until an end parameter is reached or\n   * string is exhausted.\n   * @param {string} text The string to process.\n   * @param {string[]} end List of possible end characters.\n   * @return {[string, string, string]} The collected value, the actual end\n   *     character, and the rest of the string still to parse.\n   */\n  function readValue(text: string, end: string[]): [string, string, string] {\n    let length = text.length;\n    let braces = 0;\n    let value = '';\n    let index = 0;\n    let start = 0;             // Counter for the starting left braces.\n    let startCount = true;     // Flag for counting starting left braces.\n    let stopCount = false;     // If true right braces are found directly\n                               // after starting braces, but no other char yet.\n    while (index < length) {\n      let c = text[index++];\n      switch (c) {\n      case ' ':                // Ignore spaces.\n        break;\n      case '{':\n        if (startCount) {      // Count start left braces at start.\n          start++;\n        } else {\n          stopCount = false;\n          if (start > braces) {   // Some start left braces have been closed.\n            start = braces;\n          }\n        }\n        braces++;\n        break;\n      case '}':\n        if (braces) {          // Closing braces.\n          braces--;\n        }\n        if (startCount || stopCount) {  // Closing braces at the start.\n          start--;\n          stopCount = true;    // Continue to close braces.\n        }\n        startCount = false;    // Stop counting start left braces.\n        break;\n      default:\n        if (!braces && end.indexOf(c) !== -1) {   // End character reached.\n          return [stopCount ? 'true' :            // If Stop count is true we\n                                                  // have balanced braces, only.\n                  removeBraces(value, start), c, text.slice(index)];\n        }\n        startCount = false;\n        stopCount = false;\n      }\n      value += c;\n    }\n    if (braces) {\n      throw new TexError('ExtraOpenMissingClose',\n                         'Extra open brace or missing close brace');\n    }\n    return [stopCount ? 'true' : removeBraces(value, start), '', text.slice(index)];\n  }\n\n}\n\nexport default ParseUtil;\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview The Stack for the TeX parser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\n\nimport NodeUtil from './NodeUtil.js';\nimport {MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {StackItem, EnvList} from './StackItem.js';\nimport StackItemFactory from './StackItemFactory.js';\n\n\nexport default class Stack {\n\n  /**\n   * @type {EnvList}\n   */\n  public global: EnvList = {};\n\n  /**\n   * The actual stack, a list of stack items.\n   * @type {Array.<StackItem>}\n   */\n  private stack: StackItem[] = [];\n\n  /**\n   * @constructor\n   * @param {StackItemFactory} factory The stack item factory.\n   * @param {EnvList} env The environment.\n   * @param {boolean} inner True if parser has been called recursively.\n   */\n  constructor(private _factory: StackItemFactory,\n              private _env: EnvList, inner: boolean) {\n    this.global = {isInner: inner};\n    this.stack = [ this._factory.create('start', this.global) ];\n    if (_env) {\n      this.stack[0].env = _env;\n    }\n    this.env = this.stack[0].env;\n  }\n\n\n  /**\n   * Set the environment of the stack.\n   * @param {EnvList} env The new environment.\n   */\n  public set env(env: EnvList) {\n    this._env = env;\n  }\n\n\n  /**\n   * Retrieves the environment of that stack.\n   * @return {EnvList} The current environment.\n   */\n  public get env(): EnvList {\n    return this._env;\n  }\n\n\n  /**\n   * Pushes items or nodes onto stack.\n   * @param {...StackItem|MmlNode} args A list of items to push.\n   */\n  public Push(...args: (StackItem | MmlNode)[]) {\n    for (const node of args) {\n      if (!node) {\n        continue;\n      }\n      const item = NodeUtil.isNode(node) ?\n        this._factory.create('mml', node) : node as StackItem;\n      item.global = this.global;\n      const [top, success] =\n        this.stack.length ? this.Top().checkItem(item) : [null, true];\n      if (!success) {\n        continue;\n      }\n      if (top) {\n        this.Pop();\n        this.Push(...top);\n        continue;\n      }\n      this.stack.push(item);\n      if (item.env) {\n        if (item.copyEnv) {\n          Object.assign(item.env, this.env);\n        }\n        this.env = item.env;\n      } else {\n        item.env = this.env;\n      }\n    }\n  }\n\n\n  /**\n   * Pop the topmost elements off the stack.\n   * @return {StackItem} A stack item.\n   */\n  public Pop(): StackItem {\n    const item = this.stack.pop();\n    if (!item.isOpen) {\n      delete item.env;\n    }\n    this.env = (this.stack.length ? this.Top().env : {});\n    return item;\n  }\n\n\n  /**\n   * Lookup the nth elements on the stack without removing them.\n   * @param {number=} n Position of element that should be returned. Default 1.\n   * @return {StackItem} Nth item on the stack.\n   */\n  public Top(n: number = 1): StackItem {\n    return this.stack.length < n ? null : this.stack[this.stack.length - n];\n  }\n\n\n  /**\n   * Lookup the topmost element on the stack, returning the Mml node in that\n   * item. Optionally pops the Mml node from that stack item.\n   * @param {boolean=} noPop Pop top item if true.\n   * @return {MmlNode} The Mml node in the topmost stack item.\n   */\n  public Prev(noPop?: boolean): MmlNode | void {\n    const top = this.Top();\n    return noPop ? top.First : top.Pop();\n  }\n\n\n  /**\n   * @override\n   */\n  public toString() {\n    return 'stack[\\n  ' + this.stack.join('\\n  ') + '\\n]';\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview The TexParser. Implements the basic parsing functionality and\n *     administers the global stack and tree objects.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport ParseUtil from './ParseUtil.js';\nimport {HandlerType} from './MapHandler.js';\nimport Stack from './Stack.js';\nimport StackItemFactory from './StackItemFactory.js';\nimport {Tags} from './Tags.js';\nimport TexError from './TexError.js';\nimport {MmlNode, AbstractMmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {ParseInput, ParseResult} from './Types.js';\nimport ParseOptions from './ParseOptions.js';\nimport {StackItem, EnvList} from './StackItem.js';\nimport {Symbol} from './Symbol.js';\nimport {OptionList} from '../../util/Options.js';\n\n\n/**\n * The main Tex Parser class.\n */\nexport default class TexParser {\n\n  /**\n   * Counter for recursive macros.\n   * @type {number}\n   */\n  public macroCount: number = 0;\n\n  /**\n   * The stack for items and created nodes.\n   * @type {Stack}\n   */\n  public stack: Stack;\n\n  /**\n   * Current position in the string that is parsed.\n   * @type {number}\n   */\n  public i: number = 0;\n\n  /**\n   * The last command sequence\n   * @type {string}\n   */\n  public currentCS: string = '';\n\n  /**\n   * @constructor\n   * @param {string} string The string to parse.\n   * @param {EnvList} env The intial environment representing the current parse\n   *     state of the overall expression translation.\n   * @param {ParseOptions} configuration A parser configuration.\n   */\n  constructor(private _string: string, env: EnvList, public configuration: ParseOptions) {\n    const inner = env.hasOwnProperty('isInner');\n    const isInner = env['isInner'] as boolean;\n    delete env['isInner'];\n    let ENV: EnvList;\n    if (env) {\n      ENV = {};\n      for (const id of Object.keys(env)) {\n        ENV[id] = env[id];\n      }\n    }\n    this.configuration.pushParser(this);\n    this.stack = new Stack(this.itemFactory, ENV, inner ? isInner : true);\n    this.Parse();\n    this.Push(this.itemFactory.create('stop'));\n  }\n\n  /**\n   * @return {OptionList} The configuration options.\n   */\n  get options(): OptionList {\n    return this.configuration.options;\n  }\n\n  /**\n   * @return {StackItemFactory} The factory for stack items.\n   */\n  get itemFactory(): StackItemFactory {\n    return this.configuration.itemFactory;\n  }\n\n  /**\n   * @return {Tags} The tags style of this configuration.\n   */\n  get tags(): Tags {\n    return this.configuration.tags;\n  }\n\n  /**\n   * Sets the string that should be parsed.\n   * @param {string} str The new string to parse.\n   */\n  set string(str: string) {\n    this._string = str;\n  }\n\n  /**\n   * @return {string} The string that is currently parsed.\n   */\n  get string(): string {\n    return this._string;\n  }\n\n\n  /**\n   * Parses the input with the specified kind of map.\n   * @param {HandlerType} kind Configuration name.\n   * @param {ParseInput} input Input to be parsed.\n   * @return {ParseResult} The output of the parsing function.\n   */\n  public parse(kind: HandlerType, input: ParseInput): ParseResult {\n    return this.configuration.handlers.get(kind).parse(input);\n  }\n\n\n  /**\n   * Maps a symbol to its \"parse value\" if it exists.\n   * @param {HandlerType} kind Configuration name.\n   * @param {string} symbol The symbol to parse.\n   * @return {any} A boolean, Character, or Macro.\n   */\n  public lookup(kind: HandlerType, symbol: string): any {\n    return this.configuration.handlers.get(kind).lookup(symbol);\n  }\n\n\n  /**\n   * Checks if a symbol is contained in one of the symbol mappings of the\n   * specified kind.\n   * @param {HandlerType} kind Configuration name.\n   * @param {string} symbol The symbol to parse.\n   * @return {boolean} True if the symbol is contained in the given types of\n   *     symbol mapping.\n   */\n  public contains(kind: HandlerType, symbol: string): boolean {\n    return this.configuration.handlers.get(kind).contains(symbol);\n  }\n\n\n  /**\n   * @override\n   */\n  public toString(): string {\n    let str = '';\n    for (const config of Array.from(this.configuration.handlers.keys())) {\n      str += config + ': ' +\n        this.configuration.handlers.get(config as HandlerType) + '\\n';\n    }\n    return str;\n  }\n\n\n  /**\n   * Parses the current input string.\n   */\n  public Parse() {\n    let c: string;\n    while (this.i < this.string.length) {\n      c = this.getCodePoint();\n      this.i += c.length;\n      this.parse('character', [this, c]);\n    }\n  }\n\n\n  /**\n   * Pushes a new item onto the stack. The item can also be a Mml node,\n   *   but if the mml item is an inferred row, push its children instead.\n   * @param {StackItem|MmlNode} arg The new item.\n   */\n  public Push(arg: StackItem | MmlNode) {\n    if (arg instanceof AbstractMmlNode && arg.isInferred) {\n      this.PushAll(arg.childNodes);\n    } else {\n      this.stack.Push(arg);\n    }\n  }\n\n\n  /**\n   * Pushes a list of new items onto the stack.\n   * @param {StackItem|MmlNode[]} args The new items.\n   */\n  public PushAll(args: (StackItem | MmlNode)[]) {\n    for (const arg of args) {\n      this.stack.Push(arg);\n    }\n  }\n\n\n  /**\n   * @return {MmlNode} The internal Mathml structure.\n   */\n  public mml(): MmlNode {\n    if (!this.stack.Top().isKind('mml')) {\n      return null;\n    }\n    let node = this.stack.Top().First;\n    this.configuration.popParser();\n    return node;\n  }\n\n  /************************************************************************\n   *\n   *   String handling routines\n   */\n\n  /**\n   * Convert delimiter to character.\n   * @param {string} c The delimiter name.\n   * @return {string} The corresponding character.\n   */\n  public convertDelimiter(c: string): string {\n    const symbol = this.lookup('delimiter', c) as Symbol;\n    return symbol ? symbol.char : null;\n  }\n\n  /**\n   * @return {string}   Get the next unicode character in the string\n   */\n  public getCodePoint(): string {\n    const code = this.string.codePointAt(this.i);\n    return code === undefined ? '' : String.fromCodePoint(code);\n  }\n\n  /**\n   * @return {boolean} True if the next character to parse is a space.\n   */\n  public nextIsSpace(): boolean {\n    return !!this.string.charAt(this.i).match(/\\s/);\n  }\n\n  /**\n   * @return {string} Get the next non-space character.\n   */\n  public GetNext(): string {\n    while (this.nextIsSpace()) {\n      this.i++;\n    }\n    return this.getCodePoint();\n  }\n\n  /**\n   * @return {string} Get and return a control-sequence name\n   */\n  public GetCS(): string {\n    let CS = this.string.slice(this.i).match(/^(([a-z]+) ?|[\\uD800-\\uDBFF].|.)/i);\n    if (CS) {\n      this.i += CS[0].length;\n      return CS[2] || CS[1];\n    } else {\n      this.i++;\n      return ' ';\n    }\n  }\n\n  /**\n   * Get and return a TeX argument (either a single character or control\n   *     sequence, or the contents of the next set of braces).\n   * @param {string} name Name of the current control sequence.\n   * @param {boolean} noneOK? True if no argument is OK.\n   * @return {string} The next argument.\n   */\n  public GetArgument(_name: string, noneOK?: boolean): string {\n    switch (this.GetNext()) {\n    case '':\n      if (!noneOK) {\n        // @test MissingArgFor\n        throw new TexError('MissingArgFor', 'Missing argument for %1', this.currentCS);\n      }\n      return null;\n    case '}':\n      if (!noneOK) {\n        // @test ExtraCloseMissingOpen\n        throw new TexError('ExtraCloseMissingOpen',\n                            'Extra close brace or missing open brace');\n      }\n      return null;\n    case '\\\\':\n      this.i++;\n      return '\\\\' + this.GetCS();\n    case '{':\n      let j = ++this.i, parens = 1;\n      while (this.i < this.string.length) {\n        switch (this.string.charAt(this.i++)) {\n        case '\\\\':  this.i++; break;\n        case '{':   parens++; break;\n        case '}':\n          if (--parens === 0) {\n            return this.string.slice(j, this.i - 1);\n          }\n          break;\n        }\n      }\n      // @test MissingCloseBrace\n      throw new TexError('MissingCloseBrace', 'Missing close brace');\n    }\n    const c = this.getCodePoint();\n    this.i += c.length;\n    return c;\n  }\n\n\n  /**\n   * Get an optional LaTeX argument in brackets.\n   * @param {string} name Name of the current control sequence.\n   * @param {string} def? The default value for the optional argument.\n   * @return {string} The optional argument.\n   */\n  public GetBrackets(_name: string, def?: string): string {\n    if (this.GetNext() !== '[') {\n      return def;\n    }\n    let j = ++this.i, parens = 0;\n    while (this.i < this.string.length) {\n      switch (this.string.charAt(this.i++)) {\n      case '{':   parens++; break;\n      case '\\\\':  this.i++; break;\n      case '}':\n        if (parens-- <= 0) {\n          // @test ExtraCloseLooking1\n          throw new TexError('ExtraCloseLooking',\n                              'Extra close brace while looking for %1', '\\']\\'');\n        }\n        break;\n      case ']':\n        if (parens === 0) {\n          return this.string.slice(j, this.i - 1);\n        }\n        break;\n      }\n    }\n    // @test MissingCloseBracket\n    throw new TexError('MissingCloseBracket',\n                        'Could not find closing \\']\\' for argument to %1', this.currentCS);\n  }\n\n  /**\n   *  Get the name of a delimiter (check it in the delimiter list).\n   * @param {string} name Name of the current control sequence.\n   * @param {boolean} braceOK? Are braces around the delimiter OK.\n   * @return {string} The delimiter name.\n   */\n  public GetDelimiter(name: string, braceOK?: boolean): string {\n    let c = this.GetNext(); this.i += c.length;\n    if (this.i <= this.string.length) {\n      if (c === '\\\\') {\n        c += this.GetCS();\n      } else if (c === '{' && braceOK) {\n        this.i--;\n        c = this.GetArgument(name).trim();\n      }\n      if (this.contains('delimiter', c)) {\n        return this.convertDelimiter(c);\n      }\n    }\n    // @test MissingOrUnrecognizedDelim1, MissingOrUnrecognizedDelim2\n    throw new TexError('MissingOrUnrecognizedDelim',\n                        'Missing or unrecognized delimiter for %1', this.currentCS);\n  }\n\n  /**\n   * Get a dimension (including its units).\n   * @param {string} name Name of the current control sequence.\n   * @return {string} The dimension string.\n   */\n  public GetDimen(name: string): string {\n    if (this.GetNext() === '{') {\n      let dimen = this.GetArgument(name);\n      let [value, unit] = ParseUtil.matchDimen(dimen);\n      if (value) {\n        // @test Raise In Line, Lower 2, (Raise|Lower) Negative\n        return value + unit;\n      }\n    } else {\n      // @test Above, Raise, Lower, Modulo, Above With Delims\n      let dimen = this.string.slice(this.i);\n      let [value, unit, length] = ParseUtil.matchDimen(dimen, true);\n      if (value) {\n        this.i += length;\n        return value + unit;\n      }\n    }\n    // @test MissingDimOrUnits\n    throw new TexError('MissingDimOrUnits',\n                        'Missing dimension or its units for %1', this.currentCS);\n  }\n\n  /**\n   *  Get everything up to the given control sequence (token)\n   * @param {string} name Name of the current control sequence.\n   * @param {string} token The element until where to parse.\n   * @return {string} The text between the current position and the given token.\n   */\n  public GetUpTo(_name: string, token: string): string {\n    while (this.nextIsSpace()) {\n      this.i++;\n    }\n    let j = this.i;\n    let parens = 0;\n    while (this.i < this.string.length) {\n      let k = this.i;\n      let c = this.GetNext(); this.i += c.length;\n      switch (c) {\n      case '\\\\':  c += this.GetCS(); break;\n      case '{':   parens++; break;\n      case '}':\n        if (parens === 0) {\n          // @test ExtraCloseLooking2\n          throw new TexError('ExtraCloseLooking',\n                              'Extra close brace while looking for %1', token);\n        }\n        parens--;\n        break;\n      }\n      if (parens === 0 && c === token) {\n        return this.string.slice(j, k);\n      }\n    }\n    // @test TokenNotFoundForCommand\n    throw new TexError('TokenNotFoundForCommand',\n                        'Could not find %1 for %2', token, this.currentCS);\n  }\n\n  /**\n   * Parse the arguments of a control sequence in a new parser instance.\n   * @param {string} name Name of the current control sequence.\n   * @return {MmlNode} The parsed node.\n   */\n  public ParseArg(name: string): MmlNode  {\n    return new TexParser(this.GetArgument(name), this.stack.env,\n                         this.configuration).mml();\n  }\n\n  /**\n   * Parses a given string up to a given token in a new parser instance.\n   * @param {string} name Name of the current control sequence.\n   * @param {string} token A Token at which to end parsing.\n   * @return {MmlNode} The parsed node.\n   */\n  public ParseUpTo(name: string, token: string): MmlNode {\n    return new TexParser(this.GetUpTo(name, token), this.stack.env,\n                         this.configuration).mml();\n  }\n\n\n  /**\n   * Get a delimiter or empty argument\n   * @param {string} name Name of the current control sequence.\n   * @return {string} The delimiter.\n   */\n  public GetDelimiterArg(name: string): string {\n    let c = ParseUtil.trimSpaces(this.GetArgument(name));\n    if (c === '') {\n      return null;\n    }\n    if (this.contains('delimiter', c)) {\n      return c;\n    }\n    // @test MissingOrUnrecognizedDelim\n    throw new TexError('MissingOrUnrecognizedDelim',\n                        'Missing or unrecognized delimiter for %1', this.currentCS);\n  }\n\n  /**\n   * @return {boolean} True if a star follows the control sequence name.\n   */\n  public GetStar(): boolean {\n    let star = (this.GetNext() === '*');\n    if (star) {\n      this.i++;\n    }\n    return star;\n  }\n\n\n  /**\n   * Convenience method to create nodes with the node factory of the current\n   * configuration.\n   * @param {string} kind The kind of node to create.\n   * @param {any[]} ...rest The remaining arguments for the creation method.\n   * @return {MmlNode} The newly created node.\n   */\n  public create(kind: string, ...rest: any[]): MmlNode {\n    return this.configuration.nodeFactory.create(kind, ...rest);\n  }\n\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Stack items hold information on the TexParser stack.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {FactoryNodeClass} from '../../core/Tree/Factory.js';\nimport TexError from './TexError.js';\nimport StackItemFactory from './StackItemFactory.js';\n\n// Union types for abbreviation.\nexport type EnvProp = string | number | boolean;\n\nexport type EnvList = {[key: string]: EnvProp};\n\n// This is the type for all fields that used to be set with With.\nexport type Prop = string | number | boolean | MmlNode | PropList;\n\nexport type PropList = {[key: string]: Prop};\n\nexport type CheckType = [(MmlNode | StackItem)[], boolean];\n\n\nexport interface NodeStack {\n\n  /**\n   * Get or set the topmost element on the node stack without removing it.\n   * @return {MmlNode} The topmost node on the stack.\n   */\n  First: MmlNode;\n\n  /**\n   * Get or set the last element on the node stack without removing it.\n   * @return {MmlNode} The last node on the stack.\n   */\n  Last: MmlNode;\n\n  /**\n   * @return {MmlNode} The topmost node on the item's node stack.\n   */\n  Pop(): MmlNode | void;\n\n  /**\n   * Pushes new nodes onto the items node stack.\n   * @param {MmlNode[]} ...nodes A list of nodes.\n   */\n  Push(...nodes: MmlNode[]): void;\n\n  /**\n   * Get the top n elements on the node stack without removing them.\n   * @param {number=} n Number of elements that should be returned.\n   * @return {MmlNode[]} List of nodes on top of stack.\n   */\n  Peek(n?: number): MmlNode[];\n\n  /**\n   * @return {number} The size of the stack.\n   */\n  Size(): number;\n\n  /**\n   * Clears the stack.\n   */\n  Clear(): void;\n\n  /**\n   * Returns nodes on the stack item's node stack as an Mml node. I.e., in case\n   * the item contains more than one node, it creates an mrow.\n   * @param {boolean=} inferred If set the mrow will be an inferred mrow.\n   * @param {boolean=} forceRow If set an mrow will be created, regardless of\n   *     how many nodes the item contains.\n   * @return {MmlNode} The topmost Mml node.\n   */\n  toMml(inferred?: boolean, forceRow?: boolean): MmlNode;\n\n}\n\n\nexport abstract class MmlStack implements NodeStack {\n\n  /**\n   * @constructor\n   * @extends {NodeStack}\n   * @param {MmlNode[]} nodes An initial list of nodes to put on the stack.\n   */\n  constructor(private _nodes: MmlNode[]) { }\n\n  /**\n   * @return {MmlNode[]} The nodes on the stack.\n   */\n  protected get nodes(): MmlNode[] {\n    return this._nodes;\n  }\n\n  /**\n   * @override\n   */\n  public Push(...nodes: MmlNode[]) {\n    this._nodes.push(...nodes);\n  }\n\n\n  /**\n   * @override\n   */\n  public Pop(): MmlNode {\n    return this._nodes.pop();\n  }\n\n\n  /**\n   * @override\n   */\n  public get First(): MmlNode {\n    return this._nodes[this.Size() - 1];\n  }\n\n\n  /**\n   * @override\n   */\n  public set First(node: MmlNode) {\n    this._nodes[this.Size() - 1] = node;\n  }\n\n\n  /**\n   * @override\n   */\n  public get Last(): MmlNode {\n    return this._nodes[0];\n  }\n\n\n  /**\n   * @override\n   */\n  public set Last(node: MmlNode) {\n    this._nodes[0] = node;\n  }\n\n\n  /**\n   * @override\n   */\n  public Peek(n?: number): MmlNode[] {\n    if (n == null) {\n      n = 1;\n    }\n    return this._nodes.slice(this.Size() - n);\n  }\n\n\n  /**\n   * @override\n   */\n  public Size(): number {\n    return this._nodes.length;\n  }\n\n\n  /**\n   * @override\n   */\n  public Clear(): void {\n    this._nodes = [];\n  }\n\n\n  protected abstract get factory(): StackItemFactory;\n\n  /**\n   * @override\n   */\n  public toMml(inferred: boolean = true, forceRow?: boolean) {\n    if (this._nodes.length === 1 && !forceRow) {\n      return this.First;\n    }\n    // @test Two Identifiers\n    return this.create(\n      'node', inferred ? 'inferredMrow' : 'mrow', this._nodes, {});\n  }\n\n\n  /**\n   * Convenience method to create nodes with the node factory on this stack.\n   * @param {string} kind The kind of node to create.\n   * @param {any[]} ...rest The remaining arguments for the creation method.\n   * @return {MmlNode} The newly created node.\n   */\n  public create(kind: string, ...rest: any[]): MmlNode {\n    return this.factory.configuration.nodeFactory.create(kind, ...rest);\n  }\n\n}\n\nexport interface StackItem extends NodeStack {\n\n\n  /**\n   * Type of stack item.\n   * @type {string}\n   */\n  kind: string;\n\n  /**\n   * Is this a closing item, e.g., end.\n   * @type {boolean}\n   */\n  isClose: boolean;\n\n  /**\n   * Is this an opening item, e.g., begin.\n   * @type {boolean}\n   */\n  isOpen: boolean;\n\n  /**\n   * Is this a finalising item, i.e., one that only collects nodes.\n   * @type {boolean}\n   */\n  isFinal: boolean;\n\n  /**\n   * Global properties of the parser.\n   * @type {EnvList}\n   */\n   global: EnvList;\n\n  /**\n   * Local properties of the stack item.\n   * @type {EnvList}\n   */\n   env: EnvList;\n\n  /**\n   * Copy local properties when pushed to stack?\n   * @type {boolean}\n   */\n   copyEnv: boolean;\n\n  /**\n   * Tests if item is of the given type.\n   * @param {string} kind The type.\n   * @return {boolean} True if item is of that type.\n   */\n  isKind(kind: string): boolean;\n\n  /**\n   * Get a property of the item.\n   * @param {string} key Property name.\n   * @return {Prop} Property value if it exists.\n   */\n  getProperty(key: string): Prop;\n\n  /**\n   * Set a property.\n   * @param {string} key Property name.\n   * @param {Prop} value Property value.\n   * @return {StackItem} The item for pipelining.\n   */\n  setProperty(key: string, value: Prop): StackItem;\n\n  /**\n   * Sets a list of properties.\n   * @param {PropList} def The properties to set.\n   * @return {StackItem} Returns the stack item object for pipelining.\n   */\n  setProperties(def: PropList): StackItem;\n\n  /**\n   * Convenience method for returning the string property \"name\".\n   * @return {string} The value for the name property.\n   */\n  getName(): string;\n\n  /**\n   * TeX parsing in MathJax is essentially implemented via a nested stack\n   * automaton. That is the tex parser works on a stack, and each item on the\n   * stack can have a data stack of its own. Data on the stack is either a stack\n   * item or a node.\n   *\n   * The checkItem method effectively implements the recursive checking of\n   * input data from the parser against data recursively given on the stack.\n   *\n   * I.e., new input is parsed resulting in a new item. When pushed on the stack\n   * it is checked against the top most item on the stack. This either leads to\n   * the item being pushed onto the stack or combined with the top most\n   * element(s), pushing a new item, which is recursively checked, unless an\n   * error is thrown.\n   *\n   * A simple example: If \\\\end{foo} is parsed, an endItem is created, pushed on\n   * the stack. Nodes on the stack are collapsed into content of the 'foo'\n   * environment, until a beginItem for 'foo' is found.  If a beginItem is not\n   * for 'foo' or does not exist an error is thrown.\n   *\n   * @param {StackItem} item The pushed item.\n   * @return {CheckType} True/false or an item or node.\n   */\n  checkItem(item: StackItem): CheckType;\n\n}\n\nexport interface StackItemClass extends FactoryNodeClass<StackItem> {\n  // new (factory: StackItemFactory, ...args: any[]): StackItem;\n}\n\n\n/**\n * Abstract basic item class that implements most of the stack item\n * functionality. In particular, it contains the base method for checkItem.\n */\nexport abstract class BaseItem extends MmlStack implements StackItem {\n\n  /**\n   * The fail value.\n   * @type {CheckType}\n   */\n  protected static fail: CheckType = [null, false];\n\n  /**\n   * The success value.\n   * @type {CheckType}\n   */\n  protected static success: CheckType = [null, true];\n\n  /**\n   * A list of basic errors.\n   * @type {{[key: string]: string[]}}\n   */\n  protected static errors: {[key: string]: string[]} = {\n    // @test ExtraOpenMissingClose\n    end: ['MissingBeginExtraEnd', 'Missing \\\\begin{%1} or extra \\\\end{%1}'],\n    // @test ExtraCloseMissingOpen\n    close: ['ExtraCloseMissingOpen', 'Extra close brace or missing open brace'],\n    // @test MissingLeftExtraRight\n    right: ['MissingLeftExtraRight', 'Missing \\\\left or extra \\\\right'],\n    middle: ['ExtraMiddle', 'Extra \\\\middle']\n  };\n\n\n  /**\n   * @override\n   */\n  public global: EnvList = {};\n\n  private _env: EnvList;\n\n  private _properties: PropList = {};\n\n\n  /**\n   * @constructor\n   * @extends {MmlStack}\n   */\n  constructor(protected factory: StackItemFactory, ...nodes: MmlNode[]) {\n    super(nodes);\n    if (this.isOpen) {\n      this._env = {};\n    }\n  }\n\n  /**\n   * @return {string} The type of the stack item.\n   */\n    public get kind(): string {\n    return 'base';\n  }\n\n  /**\n   * @return {EnvList} Get the private environment\n   */\n  public get env(): EnvList {\n    return this._env;\n  }\n\n  /**\n   * Set the private environment\n   * @param {EnvList} value New private environemt.\n   */\n  public set env(value: EnvList) {\n    this._env = value;\n  }\n\n  /**\n   * Default is to copy local environment when pushed on stack\n   */\n  public get copyEnv() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public getProperty(key: string): Prop {\n    return this._properties[key];\n  }\n\n  /**\n   * @override\n   */\n  public setProperty(key: string, value: Prop) {\n    this._properties[key] = value;\n    return this;\n  }\n\n\n  /**\n   * @return {boolean} True if item is an opening entity, i.e., it expects a\n   *     closing counterpart on the stack later.\n   */\n  get isOpen(): boolean {\n    return false;\n  }\n\n  /**\n   * @return {boolean} True if item is an closing entity, i.e., it needs an\n   *     opening counterpart already on the stack.\n   */\n  get isClose(): boolean {\n    return false;\n  }\n\n\n  /**\n   * @return {boolean} True if item is final, i.e., it contains one or multiple\n   *      finished parsed nodes.\n   */\n  get isFinal(): boolean {\n    return false;\n  }\n\n\n  /**\n   * @override\n   */\n  public isKind(kind: string) {\n    return kind === this.kind;\n  }\n\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('over') && this.isOpen) {\n      item.setProperty('num', this.toMml(false));\n      this.Clear();\n    }\n    if (item.isKind('cell') && this.isOpen) {\n      if (item.getProperty('linebreak')) {\n        return BaseItem.fail;\n      }\n      // @test Ampersand-error\n      throw new TexError('Misplaced', 'Misplaced %1', item.getName());\n    }\n    if (item.isClose && this.getErrors(item.kind)) {\n      // @test ExtraOpenMissingClose, ExtraCloseMissingOpen,\n      //       MissingLeftExtraRight, MissingBeginExtraEnd\n      const [id, message] = this.getErrors(item.kind);\n      throw new TexError(id, message, item.getName());\n    }\n    if (!item.isFinal) {\n      return BaseItem.success;\n    }\n    this.Push(item.First);\n    return BaseItem.fail;\n  }\n\n\n  /**\n   * Clears the item's environment.\n   */\n  public clearEnv() {\n    for (const id of Object.keys(this.env)) {\n      delete this.env[id];\n    }\n  }\n\n\n  /**\n   * @override\n   */\n  public setProperties(def: PropList) {\n    Object.assign(this._properties, def);\n    return this;\n  }\n\n\n  /**\n   * @override\n   */\n  public getName() {\n    return this.getProperty('name') as string;\n  }\n\n\n  /**\n   * @override\n   */\n  public toString() {\n    return this.kind + '[' + this.nodes.join('; ') + ']';\n  }\n\n\n  /**\n   * Get error messages for a particular types of stack items. This reads error\n   * messages from the static errors object, which can be extended in\n   * subclasses.\n   * @param {string} kind The stack item type.\n   * @return {string[]} The list of arguments for the TeXError.\n   */\n  public getErrors(kind: string): string[] {\n    const CLASS = (this.constructor as typeof BaseItem);\n    return (CLASS.errors || {})[kind] || BaseItem.errors[kind];\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview A factory for stack items. This allows particular items to be\n *     overwritten later.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {StackItemClass, StackItem, BaseItem} from './StackItem.js';\nimport ParseOptions from './ParseOptions.js';\nimport {AbstractFactory} from '../../core/Tree/Factory.js';\n\n\nclass DummyItem extends BaseItem {}\n\n/**\n * The StackItemFactory is initially populated with the default stack item\n * classes. They can be changed, deleted or added to, if and when necessary.\n *\n * @constructor\n * @extends {AbstractFactory}\n */\nexport default class StackItemFactory extends AbstractFactory<StackItem, StackItemClass> {\n\n  /**\n   * @override\n   */\n  public static DefaultStackItems: {[kind: string]: StackItemClass} = {\n    [DummyItem.prototype.kind]: DummyItem\n  };\n\n\n  /**\n   * @override\n   */\n  public defaultKind = 'dummy';\n\n\n  /**\n   * The parser configuration.\n   * @type {ParseOptions}\n   */\n  public configuration: ParseOptions = null;\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Node factory for creating MmlNodes. This allows extension\n *     packages to add node constructors or overwrite existing ones.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {TextNode, MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {MmlFactory} from '../../core/MmlTree/MmlFactory.js';\nimport ParseOptions from './ParseOptions.js';\nimport NodeUtil from './NodeUtil.js';\n\n\nexport type NodeFactoryMethod = (factory: NodeFactory, kind: string, ...rest: any[]) => MmlNode;\n\nexport class NodeFactory {\n\n  /**\n   * Parser configuration that can be used to pass information between node methods.\n   * @type {ParseOption}\n   */\n  public configuration: ParseOptions;\n\n\n  /**\n   * The external node factory.\n   * @type {MmlFactory}\n   */\n  protected mmlFactory: MmlFactory = null;\n\n\n  /**\n   * The factory table populated with some default methods.\n   */\n  private factory: {[kind: string]: NodeFactoryMethod} =\n    {'node': NodeFactory.createNode,\n     'token': NodeFactory.createToken,\n     'text': NodeFactory.createText,\n     'error': NodeFactory.createError\n    };\n\n  /**\n   * Default node generation function.\n   * @param {NodeFactory} factory The current node factory.\n   * @param {string} kind The type of node to create.\n   * @param {MmlNode[]} children Its children.\n   * @param {any=} def Its properties.\n   * @param {TextNode=} text An optional text node if this is a token.\n   * @return {MmlNode} The newly created Mml node.\n   */\n  public static createNode(factory: NodeFactory, kind: string,\n                           children: MmlNode[] = [], def: any = {},\n                           text?: TextNode): MmlNode {\n    const node = factory.mmlFactory.create(kind);\n    node.setChildren(children);\n    if (text) {\n      node.appendChild(text);\n    }\n    NodeUtil.setProperties(node, def);\n    return node;\n  }\n\n\n  /**\n   * Default token generation function.\n   * @param {NodeFactory} factory The current node factory.\n   * @param {string} kind The type of node to create.\n   * @param {any} def Its properties.\n   * @param {string} text Text of the token.\n   * @return {MmlNode} The newly created token node.\n   */\n  public static createToken(factory: NodeFactory, kind: string,\n                            def: any = {}, text: string = ''): MmlNode  {\n    const textNode = factory.create('text', text);\n    return factory.create('node', kind, [], def, textNode);\n  }\n\n\n  /**\n   * Default text node generation function.\n   * @param {NodeFactory} factory The current node factory.\n   * @param {string} text The text for the new node.\n   * @return {TextNode} The newly created text node.\n   */\n  public static createText(factory: NodeFactory, text: string): TextNode  {\n    if (text == null) {\n      return null;\n    }\n    return (factory.mmlFactory.create('text') as TextNode).setText(text);\n  }\n\n\n  /**\n   * Default error node generation function.\n   * @param {NodeFactory} factory The current node factory.\n   * @param {string} message The error message.\n   * @return {MmlNode} The newly created error node.\n   */\n  public static createError(factory: NodeFactory, message: string): MmlNode  {\n    let text = factory.create('text', message);\n    let mtext = factory.create('node', 'mtext', [], {}, text);\n    let error = factory.create('node', 'merror', [mtext], {'data-mjx-error': message});\n    return error;\n  }\n\n\n  /**\n   * @param {MmlFactory} mmlFactory   The MmlFactory for the TeX jax to use\n   */\n  public setMmlFactory(mmlFactory: MmlFactory) {\n    this.mmlFactory = mmlFactory;\n  }\n\n  /**\n   * Adds a method to the factory.\n   * @param {string} kind The type of node the method creates.\n   * @param {NodeFactoryMethod} func The node creator.\n   */\n  public set(kind: string, func: NodeFactoryMethod) {\n    this.factory[kind] = func;\n  }\n\n\n  /**\n   * Adds a set of node creators to the factory.\n   * @param {Object.<NodeFactoryMethod>} maps The set of functions.\n   */\n  public setCreators(maps: {[kind: string]: NodeFactoryMethod}) {\n    for (let kind in maps) {\n      this.set(kind, maps[kind]);\n    }\n  }\n\n\n  /**\n   * Creates a node for the internal data structure from the factory.\n   * @param {string} kind The type of node to be created.\n   * @param {any[]} ...rest The arguments for the node.\n   * @return {MmlNode} The created node.\n   */\n  public create(kind: string, ...rest: any[]): MmlNode {\n    const func = this.factory[kind] || this.factory['node'];\n    const node = func(this, rest[0], ...rest.slice(1));\n    if (kind === 'node') {\n      this.configuration.addNode(rest[0], node);\n    }\n    return node;\n  }\n\n\n  /**\n   * @param {string} kind The method for generating a node of given kind.\n   */\n  public get(kind: string) {\n    return this.factory[kind];\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Factory generating maps to keep options for the TeX parser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport StackItemFactory from './StackItemFactory.js';\nimport {Tags} from './Tags.js';\nimport {SubHandlers} from './MapHandler.js';\nimport {NodeFactory} from './NodeFactory.js';\nimport NodeUtil from './NodeUtil.js';\nimport {MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport TexParser from './TexParser.js';\nimport {defaultOptions, OptionList} from '../../util/Options.js';\nimport {ParserConfiguration} from './Configuration.js';\n\n\n/**\n * @class\n */\nexport default class ParseOptions {\n\n  /**\n   * A set of sub handlers\n   * @type {SubHandlers}\n   */\n  public handlers: SubHandlers;\n\n  /**\n   * A set of options, mapping names to string or boolean values.\n   * @type {OptionList}\n   */\n  public options: OptionList = {};\n\n  /**\n   * The current item factory.\n   * @type {StackItemFactory}\n   */\n  public itemFactory: StackItemFactory;\n\n  /**\n   * The current node factory.\n   * @type {NodeFactory}\n   */\n  public nodeFactory: NodeFactory;\n\n  /**\n   * The current tagging object.\n   * @type {Tags}\n   */\n  public tags: Tags;\n\n  /**\n   * Storage area for parser-specific package data (indexed by package name)\n   * @type {Map<string, any>}\n   */\n  public packageData: Map<string, any> = new Map();\n\n  // Fields for ephemeral options, i.e., options that will be cleared for each\n  // run of the parser.\n  /**\n   * Stack of previous tex parsers. This is used to keep track of parser\n   * settings when expressions are recursively parsed.\n   * @type {TexParser[]}\n   */\n  public parsers: TexParser[] = [];\n\n\n  /**\n   * The current root node.\n   * @type {MmlNode}\n   */\n  public root: MmlNode = null;\n\n  /**\n   * List of node lists saved with respect to some property or their kind.\n   * @type {{[key: string]: MmlNode[]}}\n   */\n  public nodeLists: {[key: string]: MmlNode[]} = {};\n\n  /**\n   * Error state of the parser.\n   * @type {boolean}\n   */\n  public error: boolean = false;\n\n\n\n  /**\n   * @constructor\n   * @param {Configuration} configuration Configuration object of the current\n   *     TeX parser.\n   * @param {OptionList[]} options   [TeX options, Tag options, {packages}]\n   */\n  public constructor(configuration: ParserConfiguration, options: OptionList[] = []) {\n    this.handlers = configuration.handlers;\n    // Add node factory methods from packages.\n    this.nodeFactory = new NodeFactory();\n    this.nodeFactory.configuration = this;\n    this.nodeFactory.setCreators(configuration.nodes);\n    // Add stackitems from packages.\n    this.itemFactory = new StackItemFactory(configuration.items);\n    this.itemFactory.configuration = this;\n    // Set default options for parser from packages and for tags.\n    defaultOptions(this.options, ...options);\n    defaultOptions(this.options, configuration.options);\n  }\n\n\n  // Methods for dealing with ephemeral fields.\n  /**\n   * Pushes a new tex parser onto the stack.\n   * @param {TexParser} parser The new parser.\n   */\n  public pushParser(parser: TexParser) {\n    this.parsers.unshift(parser);\n  }\n\n\n  /**\n   * Pops a parser of the tex parser stack.\n   */\n  public popParser() {\n    this.parsers.shift();\n  }\n\n\n  /**\n   * @return {TexParser} The currently active tex parser.\n   */\n  public get parser(): TexParser {\n    return this.parsers[0];\n  }\n\n  /**\n   * Clears all the ephemeral options.\n   */\n  public clear() {\n    this.parsers = [];\n    this.root = null;\n    this.nodeLists = {};\n    this.error = false;\n    this.tags.resetTag();\n  }\n\n\n  /**\n   * Saves a tree node to a list of nodes for post processing.\n   * @param {string} property The property name that will be used for\n   *     postprocessing.\n   * @param {MmlNode} node The node to save.\n   */\n  public addNode(property: string, node: MmlNode) {\n    let list = this.nodeLists[property];\n    if (!list) {\n      list = this.nodeLists[property] = [];\n    }\n    list.push(node);\n    if (node.kind !== property) {\n      //\n      // If the list is not just for its kind, record that it is in this list\n      //   so that if it is copied, the copy can also be added to the list.\n      //\n      const inlists = (NodeUtil.getProperty(node, 'in-lists') as string || '');\n      const lists = (inlists ? inlists.split(/,/) : []).concat(property).join(',');\n      NodeUtil.setProperty(node, 'in-lists', lists);\n    }\n  }\n\n\n  /**\n   * Gets a saved node list with respect to a given property. It first ensures\n   * that all the nodes are \"live\", i.e., actually live in the current\n   * tree. Sometimes nodes are created, saved in the node list but discarded\n   * later in the parsing. These will be filtered out here.\n   *\n   * NB: Do not use this method before the root field of the options is\n   * set. Otherwise, your node list will always be empty!\n   * @param {string} property The property for which to retrieve the node list.\n   */\n  public getList(property: string) {\n    let list = this.nodeLists[property] || [];\n    let result = [];\n    for (let node of list) {\n      if (this.inTree(node)) {\n        result.push(node);\n      }\n    }\n    this.nodeLists[property] = result;\n    return result;\n  }\n\n\n  /**\n   * Remove a list of nodes from a saved list (e.g., when a filter removes the\n   * node from the DOM, like for munderover => munder).\n   *\n   * @param {string} property The property from which to remove nodes.\n   * @param {MmlNode[]} nodes The nodes to remove.\n   */\n  public removeFromList(property: string, nodes: MmlNode[]) {\n    const list = this.nodeLists[property] || [];\n    for (const node of nodes) {\n      const i = list.indexOf(node);\n      if (i >= 0) {\n        list.splice(i, 1);\n      }\n    }\n  }\n\n\n  /**\n   * Tests if the node is in the tree spanned by the current root node.\n   * @param {MmlNode} node The node to test.\n   */\n  private inTree(node: MmlNode) {\n    while (node && node !== this.root) {\n      node = node.parent;\n    }\n    return !!node;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Class for generating tags, references, etc.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport TexParser from './TexParser.js';\nimport {MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport {MathItem} from '../../core/MathItem.js';\nimport {EnvList} from './StackItem.js';\nimport ParseOptions from './ParseOptions.js';\nimport {OptionList} from '../../util/Options.js';\n\n\n/**\n *  Simple class for label objects.\n */\nexport class Label {\n\n  /**\n   * @constructor\n   * @param {string=} tag The tag that's displayed.\n   * @param {string=} id The id that serves as reference.\n   */\n  constructor(public tag: string = '???', public id: string = '') {}\n}\n\n\n/**\n * A simple class for keeping track of tag information.\n */\nexport class TagInfo {\n\n  /**\n   * @constructor\n   * @param {string} env The environment name (e.g., align).\n   * @param {boolean} taggable Environment supports tags (e.g., align* does, but\n   *     split does not.)\n   * @param {boolean} defaultTags Environment is tagged by default (e.g., align\n   *     is, but align* is not).\n   * @param {string} tag The tag name (e.g., 1).\n   * @param {string} tagId The unique id for that tag (e.g., mjx-eqn:1).\n   * @param {string} tagFormat The formatted tag (e.g., \"(1)\").\n   * @param {boolean} noTag A no tagging command has been set (e.g., \\notag,\n   *     \\nonumber).\n   * @param {string} labelId The label referring to the tag.\n   */\n  constructor(readonly env: string = '',\n              readonly taggable: boolean = false,\n              readonly defaultTags: boolean = false,\n              public tag: string = null,\n              public tagId: string = '',\n              public tagFormat: string = '',\n              public noTag: boolean = false,\n              public labelId: string = '') {}\n\n}\n\n\nexport interface Tags {\n\n  /**\n   * The global configurations in which the parsing takes place.\n   * @type {ParseOptions}\n   */\n  configuration: ParseOptions;\n\n  /**\n   * IDs used in this equation.\n   * @type {Object.<boolean>}\n   */\n  ids: {[key: string]: boolean};\n\n  /**\n   * IDs used in previous equations.\n   * @type {Object.<boolean>}\n   */\n  allIds: {[key: string]: boolean};\n\n  /**\n   * Labels in the current equation.\n   * @type {Object.<Label>}\n   */\n  labels: {[key: string]: Label};\n\n  /**\n   * Labels in previous equations.\n   * @type {Object.<Label>}\n   */\n  allLabels: {[key: string]: Label};\n\n  /**\n   * The label to use for the next tag.\n   * @type {string}\n   */\n  label: string;\n\n  /**\n   * True if the equation contains an undefined label and must be reprocessed later.\n   * @type {boolean}\n   */\n  redo: boolean;\n\n  /**\n   * True when recompiling to update undefined references\n   * @type {boolean}\n   */\n  refUpdate: boolean;\n\n  /**\n   * The environment that is currently tagged.\n   * @type {string}\n   */\n  env: string;\n\n  /**\n   * The currently active tag.\n   * @type {TagInfo}\n   */\n  currentTag: TagInfo;\n\n  /**\n   * How to format tags.\n   * @param {string} tag The tag string.\n   * @return {string} The formatted numbered tag.\n   */\n  formatTag(tag: string): string;\n\n  /**\n   * How to format URLs for references.\n   * @param {string} id The reference id.\n   * @param {string} base The base URL in the reference.\n   * @return {}\n   */\n  formatUrl(id: string, base: string): string;\n\n  /**\n   * Set the tag automatically, by incrementing equation number.\n   */\n  autoTag(): void;\n\n  /**\n   * @return {MmlNode|void} Generates and returns the tag node.\n   */\n  getTag(): MmlNode | void;\n\n  /**\n   * Clears tagging information.\n   */\n  clearTag(): void;\n\n  /**\n   * Resets the tag structure after an expression has been typeset.\n   */\n  resetTag(): void;\n\n  /**\n   * Fully resets the tag structure, in particular all the tagging and label\n   * history.\n   * @param {number} offset A new offset value to start counting ids from.\n   */\n  reset(offset?: number): void;\n\n  /**\n   * Initialise tagging for a MathItem\n   * (clear equation-specific labels and ids, set counter\n   * and check for recompile)\n   * @param {MathItem} math   The MathItem for the current equation\n   */\n    startEquation(math: MathItem<any, any, any>): void;\n\n  /**\n   * Move equation-specific labels and ids to global ones,\n   * save the counter, and mark the MathItem for redos\n   */\n    finishEquation(math: MathItem<any, any, any>): void;\n\n  /**\n   * Finalizes tag creation.\n   * @param {MmlNode} node\n   * @param {EnvList} env List of environment properties.\n   * @return {MmlNode} The newly created tag.\n   */\n  finalize(node: MmlNode, env: EnvList): MmlNode;\n\n  /**\n   * Starts tagging on a given environment.\n   * @param {string} env The name of the environment.\n   * @param {boolean} taggable True if taggable.\n   * @param {boolean} defaultTags True if tagged by default.\n   */\n  start(env: string, taggable: boolean, defaultTags: boolean): void;\n\n  /**\n   * End tagging.\n   */\n  end(): void;\n\n  /**\n   * Computes the next tag.\n   * @param {string} tag The tag content.\n   * @param {boolean} noFormat True if tag should not be formatted.\n   */\n  tag(tag: string, noFormat: boolean): void;\n\n  /**\n   * Call an explicit no tag.\n   */\n  notag(): void;\n\n  /**\n   * Entag an element by creating a table around it.\n   * @param {MmlNode} node The node to be tagged.\n   * @param {MmlNode} tag The tag node.\n   * @return {MmlNode} The table node containing the original node and tag.\n   */\n  enTag(node: MmlNode, tag: MmlNode): MmlNode;\n}\n\n\nexport class AbstractTags implements Tags {\n\n  /**\n   * Current equation number.\n   * @type {number}\n   */\n  protected counter: number = 0;\n\n  /**\n   * Equation number as equation begins.\n   * @type {number}\n   */\n  protected allCounter: number = 0;\n\n  /**\n   * @override\n   */\n  public configuration: ParseOptions = null;\n\n  /**\n   * @override\n   */\n  public ids: {[key: string]: boolean} = {};\n\n  /**\n   * @override\n   */\n  public allIds: {[key: string]: boolean} = {};\n\n  /**\n   * @override\n   */\n  public labels: {[key: string]: Label} = {};\n\n  /**\n   * @override\n   */\n  public allLabels: {[key: string]: Label} = {};\n\n  /**\n   * @override\n   */\n  public redo: boolean = false;\n\n  /**\n   * @override\n   */\n  public refUpdate: boolean = false;\n\n  /**\n   * @override\n   */\n  public currentTag: TagInfo = new TagInfo();\n\n\n  /**\n   * Chronology of all previous tags, in case we need to look something up in\n   * the finalize method.\n   * @type {TagInfo[]}\n   */\n  protected history: TagInfo[] = [];\n\n  private stack: TagInfo[] = [];\n\n  /**\n   * @override\n   */\n  public start(env: string, taggable: boolean, defaultTags: boolean) {\n    if (this.currentTag) {\n      this.stack.push(this.currentTag);\n    }\n    this.currentTag = new TagInfo(env, taggable, defaultTags);\n  }\n\n  public get env() {\n    return this.currentTag.env;\n  }\n\n\n  /**\n   * @override\n   */\n  public end() {\n    this.history.push(this.currentTag);\n    this.currentTag = this.stack.pop();\n  }\n\n\n  /**\n   * @override\n   */\n  public tag(tag: string, noFormat: boolean) {\n    this.currentTag.tag = tag;\n    this.currentTag.tagFormat = noFormat ? tag : this.formatTag(tag);\n    this.currentTag.noTag = false;\n  }\n\n\n  /**\n   * @override\n   */\n  public notag() {\n    this.tag('', true);\n    this.currentTag.noTag = true;\n  }\n\n  protected get noTag(): boolean {\n    return this.currentTag.noTag;\n  }\n\n  public set label(label: string) {\n    this.currentTag.labelId = label;\n  }\n\n  public get label() {\n    return this.currentTag.labelId;\n  }\n\n  /**\n   * @override\n   */\n  public formatUrl(id: string, base: string) {\n    return base + '#' + encodeURIComponent(id);\n  }\n\n  /**\n   * @override\n   */\n  public formatTag(tag: string) {\n    return '(' + tag + ')';\n  }\n\n  /**\n   * How to format ids for labelling equations.\n   * @param {string} id The unique part of the id (e.g., label or number).\n   * @return {string} The formatted id.\n   */\n  protected formatId(id: string): string {\n    return 'mjx-eqn:' + id.replace(/\\s/g, '_');\n  }\n\n  /**\n   * How to format numbers in tags.\n   * @param {number} n The tag number.\n   * @return {string} The formatted number.\n   */\n  protected formatNumber(n: number): string {\n    return n.toString();\n  }\n\n  // Tag handling functions.\n  /**\n   * @override\n   */\n  public autoTag() {\n    if (this.currentTag.tag == null) {\n      this.counter++;\n      this.tag(this.formatNumber(this.counter), false);\n    }\n  }\n\n\n  /**\n   * @override\n   */\n  public clearTag() {\n    this.label = '';\n    this.tag(null, true);\n    this.currentTag.tagId = '';\n  }\n\n\n  /**\n   * @override\n   */\n  public getTag(force: boolean = false) {\n    if (force) {\n      this.autoTag();\n      return this.makeTag();\n    }\n    const ct = this.currentTag;\n    if (ct.taggable && !ct.noTag) {\n      if (ct.defaultTags) {\n        this.autoTag();\n      }\n      if (ct.tag) {\n        return this.makeTag();\n      }\n    }\n    return null;\n  }\n\n\n  /**\n   * @override\n   */\n  public resetTag() {\n    this.history = [];\n    this.redo = false;\n    this.refUpdate = false;\n    this.clearTag();\n  }\n\n  /**\n   * @override\n   */\n  public reset(offset: number = 0) {\n    this.resetTag();\n    this.counter = this.allCounter = offset;\n    this.allLabels = {};\n    this.allIds = {};\n  }\n\n  /**\n   * @override\n   */\n  public startEquation(math: MathItem<any, any, any>) {\n    this.history = [];\n    this.stack = [];\n    this.clearTag();\n    this.currentTag = new TagInfo('', undefined, undefined);\n    this.labels = {};\n    this.ids = {};\n    this.counter = this.allCounter;\n    this.redo = false;\n    const recompile = math.inputData.recompile;\n    if (recompile) {\n      this.refUpdate = true;\n      this.counter = recompile.counter;\n    }\n  }\n\n  /**\n   * @override\n   */\n  public finishEquation(math: MathItem<any, any, any>) {\n    if (this.redo) {\n      math.inputData.recompile = {\n        state: math.state(),\n        counter: this.allCounter\n      };\n    }\n    if (!this.refUpdate) {\n      this.allCounter = this.counter;\n    }\n    Object.assign(this.allIds, this.ids);\n    Object.assign(this.allLabels, this.labels);\n  }\n\n  /**\n   * @override\n   */\n  public finalize(node: MmlNode, env: EnvList): MmlNode {\n    if (!env.display || this.currentTag.env ||\n        this.currentTag.tag == null) {\n      return node;\n    }\n    let tag = this.makeTag();\n    let table = this.enTag(node, tag);\n    return table;\n  }\n\n  /**\n   * @override\n   */\n  public enTag = function(node: MmlNode, tag: MmlNode): MmlNode {\n    let nf = this.configuration.nodeFactory;\n    let cell = nf.create('node', 'mtd', [node]);\n    let row = nf.create('node', 'mlabeledtr', [tag, cell]);\n    let table = nf.create('node', 'mtable', [row], {\n      side: this.configuration.options['tagSide'],\n      minlabelspacing: this.configuration.options['tagIndent'],\n      displaystyle: true\n    });\n    return table;\n  };\n\n\n  /**\n   * Sets the tag id.\n   */\n  private makeId() {\n    this.currentTag.tagId = this.formatId(\n      this.configuration.options['useLabelIds'] ?\n        (this.label || this.currentTag.tag) : this.currentTag.tag);\n  }\n\n\n  /**\n   * @return {MmlNode} The actual tag node as an mtd.\n   */\n  private makeTag(): MmlNode {\n    this.makeId();\n    if (this.label) {\n      this.labels[this.label] = new Label(this.currentTag.tag, this.currentTag.tagId);\n    }\n    let mml = new TexParser('\\\\text{' + this.currentTag.tagFormat + '}', {},\n                            this.configuration).mml();\n    return this.configuration.nodeFactory.create('node', 'mtd', [mml],\n                                                 {id: this.currentTag.tagId});\n  }\n\n}\n\n\n/**\n * No tags, except where explicitly set.\n * @constructor\n * @extends {AbstractTags}\n */\nexport class NoTags extends AbstractTags {\n\n  /**\n   * @override\n   */\n  public autoTag() {}\n\n  /**\n   * @override\n   */\n  public getTag() {\n    return !this.currentTag.tag ? null : super.getTag();\n  }\n\n}\n\n\n/**\n * Tags every display formula. Exceptions are: Environments that explicitly\n * disallow tags, e.g., equation*.\n * @constructor\n * @extends {AbstractTags}\n */\nexport class AllTags extends AbstractTags {\n\n  /**\n   * @override\n   */\n  public finalize(node: MmlNode, env: EnvList) {\n    if (!env.display || this.history.find(\n      function(x: TagInfo) { return x.taggable; })) {\n      return node;\n    }\n    let tag = this.getTag(true);\n    return this.enTag(node, tag);\n  }\n\n}\n\n\n/**\n * Class interface for factory.\n * @interface\n */\nexport interface TagsClass {\n  new (): Tags;\n}\n\n\nexport namespace TagsFactory {\n\n  let tagsMapping = new Map<string, TagsClass>([\n    ['none', NoTags],\n    ['all', AllTags]\n  ]);\n\n  let defaultTags = 'none';\n\n  /**\n   * The default options for tagging\n   * @type {OptionList}\n   */\n  export let OPTIONS: OptionList = {\n    // Tagging style, used to be autonumber in v2.\n    tags: defaultTags,\n    // This specifies the side on which \\tag{} macros will place the tags.\n    // Set to 'left' to place on the left-hand side.\n    tagSide: 'right',\n    // This is the amount of indentation (from right or left) for the tags.\n    tagIndent: '0.8em',\n    // make element ID's use \\label name rather than equation number\n    // MJ puts in an equation prefix: mjx-eqn\n    // When true it uses the label name XXX as mjx-eqn:XXX\n    // If false it uses the actual number N that is displayed: mjx-eqn:N\n    useLabelIds: true,\n    // Set to true in order to prevent error messages for duplicate label ids\n    ignoreDuplicateLabels: false\n  };\n\n\n  /**\n   * Add a tagging object.\n   * @param {string} name Name of the tagging object.\n   * @param {TagsClass} constr The class of the Tagging object.\n   */\n  export let add = function(name: string, constr: TagsClass) {\n    tagsMapping.set(name, constr);\n  };\n\n\n  /**\n   * Adds a list of tagging objects to the factory.\n   * @param {{[name: string]: TagsClass}} tags The list of tagging objects.\n   */\n  export let addTags = function(tags: {[name: string]: TagsClass}) {\n    for (const key of Object.keys(tags)) {\n      TagsFactory.add(key, tags[key]);\n    }\n  };\n\n\n  /**\n   * Creates a new tagging object.\n   * @param {string} name The name of the tagging object.\n   * @return {Tags} The newly created object.\n   */\n  export let create = function(name: string): Tags {\n    let constr = tagsMapping.get(name) || tagsMapping.get(defaultTags);\n    if (!constr) {\n        throw Error('Unknown tags class');\n    }\n    return new constr();\n  };\n\n\n  /**\n   * Set the name of the default tagging object.\n   * @param {string} name The default.\n   */\n  export let setDefault = function(name: string) {\n    defaultTags = name;\n  };\n\n\n  /**\n   * @return {Tags} The default tagging object.\n   */\n  export let getDefault = function(): Tags {\n    return TagsFactory.create(defaultTags);\n  };\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Singleton class for handling symbol maps.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {AbstractSymbolMap, SymbolMap} from './SymbolMap.js';\nimport {ParseInput, ParseResult, ParseMethod} from './Types.js';\n// import {ParserConfiguration} from './Configuration.js';\nimport {PrioritizedList} from '../../util/PrioritizedList.js';\nimport {FunctionList} from '../../util/FunctionList.js';\n\n\nexport type HandlerType = 'delimiter' | 'macro' | 'character' | 'environment';\n\nexport type HandlerConfig = {[P in HandlerType]?: string[]};\nexport type FallbackConfig = {[P in HandlerType]?: ParseMethod};\n\n\nexport namespace MapHandler {\n\n  let maps: Map<string, SymbolMap> = new Map();\n\n  /**\n   * Adds a new symbol map to the map handler. Might overwrite an existing\n   * symbol map of the same name.\n   *\n   * @param {SymbolMap} map Registers a new symbol map.\n   */\n  export let register = function(map: SymbolMap): void {\n    maps.set(map.name, map);\n  };\n\n\n  /**\n   * Looks up a symbol map if it exists.\n   *\n   * @param {string} name The name of the symbol map.\n   * @return {SymbolMap} The symbol map with the given name or null.\n   */\n  export let getMap = function(name: string): SymbolMap {\n    return maps.get(name);\n  };\n\n}\n\n\n/**\n * Class of symbol mappings that are active in a configuration.\n */\nexport class SubHandler {\n\n  private _configuration: PrioritizedList<SymbolMap> = new PrioritizedList<SymbolMap>();\n  private _fallback: FunctionList = new FunctionList();\n\n  /**\n   * Adds a list of symbol maps to the handler.\n   * @param {string[]} maps The names of the symbol maps to add.\n   * @param {ParseMethod} fallback A fallback method.\n   * @param {number} priority Optionally a priority.\n   */\n  public add(maps: string[], fallback: ParseMethod,\n             priority: number = PrioritizedList.DEFAULTPRIORITY) {\n    for (const name of maps.slice().reverse()) {\n      let map = MapHandler.getMap(name);\n      if (!map) {\n        this.warn('Configuration ' + name + ' not found! Omitted.');\n        return;\n      }\n      this._configuration.add(map, priority);\n    }\n    if (fallback) {\n      this._fallback.add(fallback, priority);\n    }\n  }\n\n  /**\n   * Parses the given input with the first applicable symbol map.\n   * @param {ParseInput} input The input for the parser.\n   * @return {ParseResult} The output of the parsing function.\n   */\n  public parse(input: ParseInput): ParseResult {\n    for (let {item: map} of this._configuration) {\n      const result = map.parse(input);\n      if (result) {\n        return result;\n      }\n    }\n    let [env, symbol] = input;\n    Array.from(this._fallback)[0].item(env, symbol);\n  }\n\n\n  /**\n   * Maps a symbol to its \"parse value\" if it exists.\n   *\n   * @param {string} symbol The symbol to parse.\n   * @return {T} A boolean, Character, or Macro.\n   */\n  public lookup<T>(symbol: string): T {\n    let map = this.applicable(symbol) as AbstractSymbolMap<T>;\n    return map ? map.lookup(symbol) : null;\n  }\n\n\n  /**\n   * Checks if a symbol is contained in one of the symbol mappings of this\n   * configuration.\n   *\n   * @param {string} symbol The symbol to parse.\n   * @return {boolean} True if the symbol is contained in the mapping.\n   */\n  public contains(symbol: string): boolean {\n    return this.applicable(symbol) ? true : false;\n  }\n\n\n  /**\n   * @override\n   */\n  public toString(): string {\n    let names = [];\n    for (let {item: map} of this._configuration) {\n      names.push(map.name);\n    }\n    return names.join(', ');\n  }\n\n\n  /**\n   * Retrieves the first applicable symbol map in the configuration.\n   * @param {string} symbol The symbol to parse.\n   * @return {SymbolMap} A map that can parse the symbol.\n   */\n  public applicable(symbol: string): SymbolMap {\n    for (let {item: map} of this._configuration) {\n      if (map.contains(symbol)) {\n        return map;\n      }\n    }\n    return null;\n  }\n\n\n  /**\n   * Retrieves the map of the given name.\n   * @param {string} name Name of the symbol map.\n   * @return {SymbolMap} The map if it exists.\n   */\n  public retrieve(name: string): SymbolMap {\n    for (let {item: map} of this._configuration) {\n      if (map.name === name) {\n        return map;\n      }\n    }\n    return null;\n  }\n\n\n  /**\n   * Prints a warning message.\n   * @param {string} message The warning.\n   */\n  private warn(message: string) {\n    console.log('TexParser Warning: ' + message);\n  }\n\n}\n\n\nexport class SubHandlers {\n\n  private map = new Map<HandlerType, SubHandler>();\n\n  /**\n   * Adds a symbol map to the configuration if it exists.\n   * @param {string} name of the symbol map.\n   */\n  public add(handlers: HandlerConfig, fallbacks: FallbackConfig,\n             priority: number = PrioritizedList.DEFAULTPRIORITY): void {\n    for (const key of Object.keys(handlers)) {\n      let name = key as HandlerType;\n      let subHandler = this.get(name);\n      if (!subHandler) {\n        subHandler = new SubHandler();\n        this.set(name, subHandler);\n      }\n      subHandler.add(handlers[name], fallbacks[name], priority);\n    }\n  }\n\n\n  /**\n   * Setter for subhandlers.\n   * @param {HandlerType} name The name of the subhandler.\n   * @param {SubHandler} subHandler The subhandler.\n   */\n  public set(name: HandlerType, subHandler: SubHandler) {\n    this.map.set(name, subHandler);\n  }\n\n\n  /**\n   * Getter for subhandler.\n   * @param {HandlerType} name Name of the subhandler.\n   * @return {SubHandler} The subhandler by that name if it exists.\n   */\n  public get(name: HandlerType): SubHandler {\n    return this.map.get(name);\n  }\n\n\n  /**\n   * Retrieves a symbol map of the given name.\n   * @param {string} name Name of the symbol map.\n   * @return {SymbolMap} The map if it exists. O/w null.\n   */\n  public retrieve(name: string): SymbolMap {\n    for (const handler of this.map.values()) {\n      let map = handler.retrieve(name);\n      if (map) {\n        return map;\n      }\n    }\n    return null;\n  }\n\n\n  /**\n   * All names of registered subhandlers.\n   * @return {IterableIterator<string>} Iterable list of keys.\n   */\n  public keys(): IterableIterator<string> {\n    return this.map.keys();\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Configuration options for the TexParser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {HandlerConfig, FallbackConfig} from './MapHandler.js';\nimport {StackItemClass} from './StackItem.js';\nimport {TagsClass} from './Tags.js';\nimport {userOptions, defaultOptions, OptionList} from '../../util/Options.js';\nimport {SubHandlers} from './MapHandler.js';\nimport {FunctionList} from '../../util/FunctionList.js';\nimport {TeX} from '../tex.js';\nimport {PrioritizedList} from '../../util/PrioritizedList.js';\nimport {TagsFactory} from './Tags.js';\n\n\nexport type StackItemConfig = {[kind: string]: StackItemClass};\nexport type TagsConfig = {[kind: string]: TagsClass};\nexport type Processor<T> = [T, number];\nexport type ProtoProcessor<T> = Processor<T> | T;\nexport type ProcessorList = Processor<Function>[];\nexport type ConfigMethod = (c: ParserConfiguration, j: TeX<any, any, any>) => void;\nexport type InitMethod = (c: ParserConfiguration) => void;\n\n\n\nexport class Configuration {\n\n  /**\n   * Creates a function priority pair.\n   * @param {ProtoProcessor<T>} func The function or processor.\n   * @param {number} priority The default priority.\n   * @return {Processor} The processor pair.\n   * @template T\n   */\n  private static makeProcessor<T>(func: ProtoProcessor<T>, priority: number): Processor<T> {\n    return Array.isArray(func) ? func : [func, priority];\n  }\n\n  /**\n   * Creates a configuration for a package.\n   * @param {string} name The package name or empty string.\n   * @param {Object} config See `create` method.\n   * @return {Configuration} The newly generated configuration.\n   */\n  private static _create(name: string,\n                         config: {handler?: HandlerConfig,\n                                  fallback?: FallbackConfig,\n                                  items?: StackItemConfig,\n                                  tags?: TagsConfig,\n                                  options?: OptionList,\n                                  nodes?: {[key: string]: any},\n                                  preprocessors?: ProtoProcessor<Function>[],\n                                  postprocessors?: ProtoProcessor<Function>[],\n                                  init?: ProtoProcessor<InitMethod>,\n                                  config?: ProtoProcessor<ConfigMethod>,\n                                  priority?: number,\n                                  parser?: string,\n                                 } = {}): Configuration {\n    let priority = config.priority || PrioritizedList.DEFAULTPRIORITY;\n    let init = config.init ? this.makeProcessor(config.init, priority) : null;\n    let conf = config.config ? this.makeProcessor(config.config, priority) : null;\n    let preprocessors = (config.preprocessors || []).map(\n      pre => this.makeProcessor(pre, priority));\n    let postprocessors = (config.postprocessors || []).map(\n      post => this.makeProcessor(post, priority));\n    let parser = config.parser || 'tex';\n    return new Configuration(\n      name,\n      config.handler || {},\n      config.fallback || {},\n      config.items || {},\n      config.tags || {},\n      config.options || {},\n      config.nodes || {},\n      preprocessors, postprocessors, init, conf, priority,\n      parser\n    );\n  }\n\n  /**\n   * Creator pattern for creating a named package configuration. This will be\n   * administered in the configuration handler and can be retrieved again.\n   * @param {string} name The package name.\n   * @param {Object} config The configuration parameters:\n   * Configuration for the TexParser consist of the following:\n   *  * _handler_  configuration mapping handler types to lists of symbol mappings.\n   *  * _fallback_ configuration mapping handler types to fallback methods.\n   *  * _items_ for the StackItem factory.\n   *  * _tags_ mapping tagging configurations to tagging objects.\n   *  * _options_ parse options for the packages.\n   *  * _nodes_ for the Node factory.\n   *  * _preprocessors_ list of functions for preprocessing the LaTeX\n   *      string wrt. to given parse options. Can contain a priority.\n   *  * _postprocessors_ list of functions for postprocessing the MmlNode\n   *      wrt. to given parse options. Can contain a priority.\n   *  * _init_ init method and optionally its priority.\n   *  * _config_ config method and optionally its priority.\n   *  * _priority_ default priority of the configuration.\n   *  * _parser_ the name of the parser that this configuration targets.\n   * @return {Configuration} The newly generated configuration.\n   */\n  public static create(name: string,\n                       config: {handler?: HandlerConfig,\n                                fallback?: FallbackConfig,\n                                items?: StackItemConfig,\n                                tags?: TagsConfig,\n                                options?: OptionList,\n                                nodes?: {[key: string]: any},\n                                preprocessors?: ProtoProcessor<Function>[],\n                                postprocessors?: ProtoProcessor<Function>[],\n                                init?: ProtoProcessor<InitMethod>,\n                                config?: ProtoProcessor<ConfigMethod>,\n                                priority?: number,\n                                parser?: string,\n                               } = {}): Configuration {\n    let configuration = Configuration._create(name, config);\n    ConfigurationHandler.set(name, configuration);\n    return configuration;\n  }\n\n  /**\n   * Creates an unnamed, ephemeral package configuration. It will not added to\n   * the configuration handler.\n   * @param {Object} config See `create` method.\n   * @return {Configuration} The ephemeral package configuration.\n   */\n  public static local(config: {handler?: HandlerConfig,\n                              fallback?: FallbackConfig,\n                              items?: StackItemConfig,\n                              tags?: TagsConfig,\n                              options?: OptionList,\n                              nodes?: {[key: string]: any},\n                              preprocessors?: ProtoProcessor<Function>[],\n                              postprocessors?: ProtoProcessor<Function>[],\n                              init?: ProtoProcessor<InitMethod>,\n                              config?: ProtoProcessor<ConfigMethod>,\n                              priority?: number,\n                              parser?: string,\n                             } = {}): Configuration {\n    return Configuration._create('', config);\n  }\n\n\n  /**\n   * @constructor\n   */\n  private constructor(readonly name: string,\n                      readonly handler: HandlerConfig = {},\n                      readonly fallback: FallbackConfig = {},\n                      readonly items: StackItemConfig = {},\n                      readonly tags: TagsConfig = {},\n                      readonly options: OptionList = {},\n                      readonly nodes: {[key: string]: any} = {},\n                      readonly preprocessors: ProcessorList = [],\n                      readonly postprocessors: ProcessorList = [],\n                      readonly initMethod: Processor<InitMethod> = null,\n                      readonly configMethod: Processor<ConfigMethod> = null,\n                      public priority: number,\n                      readonly parser: string\n                     ) {\n    this.handler = Object.assign(\n      {character: [], delimiter: [], macro: [], environment: []}, handler);\n  }\n\n  /**\n   * The init method.\n   * @type {Function}\n   */\n  public get init(): InitMethod {\n    return this.initMethod ? this.initMethod[0] : null;\n  }\n\n  /**\n   * The config method to call once jax is ready.\n   * @type {FunctionList}\n   */\n  public get config(): ConfigMethod {\n    return this.configMethod ? this.configMethod[0] : null;\n  }\n\n}\n\n\nexport namespace ConfigurationHandler {\n\n  let maps: Map<string, Configuration> = new Map();\n\n  /**\n   * Adds a new configuration to the handler overwriting old ones.\n   *\n   * @param {string} name The name of the configuration.\n   * @param {Configuration} map The configuration mapping.\n   */\n  export let set = function(name: string, map: Configuration): void {\n    maps.set(name, map);\n  };\n\n\n  /**\n   * Looks up a configuration.\n   *\n   * @param {string} name The name of the configuration.\n   * @return {Configuration} The configuration with the given name or null.\n   */\n  export let get = function(name: string): Configuration {\n    return maps.get(name);\n  };\n\n  /**\n   * @return {string[]} All configurations in the handler.\n   */\n  export let keys = function(): IterableIterator<string> {\n    return maps.keys();\n  };\n\n}\n\n\n/**\n * Parser configuration combines the configurations of the currently selected\n * packages.\n * @constructor\n */\nexport class ParserConfiguration {\n\n  /**\n   * Priority list of init methods.\n   * @type {FunctionList}\n   */\n  protected initMethod: FunctionList = new FunctionList();\n\n  /**\n   * Priority list of init methods to call once jax is ready.\n   * @type {FunctionList}\n   */\n  protected configMethod: FunctionList = new FunctionList();\n\n  /**\n   * An ordered list of cofigurations.\n   * @type {PrioritizedList<Configuration>}\n   */\n  protected configurations: PrioritizedList<Configuration> = new PrioritizedList();\n\n  /**\n   * The list of parsers this configuration targets\n   */\n  protected parsers: string[] = [];\n\n  /**\n   * The subhandlers for this configuration.\n   * @type {SubHandlers}\n   */\n  public handlers: SubHandlers = new SubHandlers();\n\n  /**\n   * The collated stack items.\n   * @type {StackItemConfig}\n   */\n  public items: StackItemConfig = {};\n\n  /**\n   * The collated tag configurations.\n   * @type {TagsConfig}\n   */\n  public tags: TagsConfig = {};\n\n  /**\n   * The collated options.\n   * @type {OptionList}\n   */\n  public options: OptionList = {};\n\n  /**\n   * The collated node creators.\n   * @type {{[key: string]: any}}\n   */\n  public nodes: {[key: string]: any}  = {};\n\n  /**\n   * @constructor\n   * @param {(string|[string,number])[]} packages A list of packages with\n   *     optional priorities.\n   * @parm {string[]} parsers   The names of the parsers this package targets\n   */\n  constructor(packages: (string | [string, number])[], parsers: string[] = ['tex']) {\n    this.parsers = parsers;\n    for (const pkg of packages.slice().reverse()) {\n      this.addPackage(pkg);\n    }\n    for (let {item: config, priority: priority} of this.configurations) {\n      this.append(config, priority);\n    }\n  }\n\n  /**\n   * Init method for the configuration;\n   */\n  public init() {\n    this.initMethod.execute(this);\n  }\n\n  /**\n   * Init method for when the jax is ready\n   * @param {TeX} jax The TeX jax for this configuration\n   */\n  public config(jax: TeX<any, any, any>) {\n    this.configMethod.execute(this, jax);\n    for (const config of this.configurations) {\n      this.addFilters(jax, config.item);\n    }\n  }\n\n  /**\n   * Retrieves and adds configuration for a package with priority.\n   * @param {(string | [string, number]} pkg Package with priority.\n   */\n  public addPackage(pkg: (string | [string, number])) {\n    const name = typeof pkg === 'string' ? pkg : pkg[0];\n    const conf = this.getPackage(name);\n    conf && this.configurations.add(conf, typeof pkg === 'string' ? conf.priority : pkg[1]);\n  }\n\n  /**\n   * Adds a configuration after the input jax is created.  (Used by \\require.)\n   * Sets items, nodes and runs configuration method explicitly.\n   *\n   * @param {string} name            The name of the package to add\n   * @param {TeX} jax                The TeX jax where it is being registered\n   * @param {OptionList=} options    The options for the configuration.\n   */\n  public add(name: string, jax: TeX<any, any, any>, options: OptionList = {}) {\n    const config = this.getPackage(name);\n    this.append(config);\n    this.configurations.add(config, config.priority);\n    this.init();\n    const parser = jax.parseOptions;\n    parser.nodeFactory.setCreators(config.nodes);\n    for (const kind of Object.keys(config.items)) {\n      parser.itemFactory.setNodeClass(kind, config.items[kind]);\n    }\n    TagsFactory.addTags(config.tags);\n    defaultOptions(parser.options, config.options);\n    userOptions(parser.options, options);\n    this.addFilters(jax, config);\n    if (config.config) {\n      config.config(this, jax);\n    }\n  }\n\n /**\n  * Find a package and check that it is for the targeted parser\n  *\n  * @param {string} name       The name of the package to check\n  * @return {Configuration}    The configuration for the package\n  */\n  protected getPackage(name: string): Configuration {\n    const config = ConfigurationHandler.get(name);\n    if (config && this.parsers.indexOf(config.parser) < 0) {\n      throw Error(`Package ${name} doesn't target the proper parser`);\n    }\n    return config;\n  }\n\n  /**\n   * Appends a configuration to the overall configuration object.\n   * @param {Configuration} config A configuration.\n   * @param {number} priority The configurations optional priority.\n   */\n  public append(config: Configuration, priority?: number) {\n    priority = priority || config.priority;\n    if (config.initMethod) {\n      this.initMethod.add(config.initMethod[0], config.initMethod[1]);\n    }\n    if (config.configMethod) {\n        this.configMethod.add(config.configMethod[0], config.configMethod[1]);\n      }\n    this.handlers.add(config.handler, config.fallback, priority);\n    Object.assign(this.items, config.items);\n    Object.assign(this.tags, config.tags);\n    defaultOptions(this.options, config.options);\n    Object.assign(this.nodes, config.nodes);\n  }\n\n  /**\n   * Adds pre- and postprocessor as filters to the jax.\n   * @param {TeX<any} jax The TeX Jax.\n   * @param {Configuration} config The configuration whose processors are added.\n   */\n  private addFilters(jax: TeX<any, any, any>, config: Configuration) {\n    for (const [pre, priority] of config.preprocessors) {\n      jax.preFilters.add(pre, priority);\n    }\n    for (const [post, priority] of config.postprocessors) {\n      jax.postFilters.add(post, priority);\n    }\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Symbol classes.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {Args, Attributes, ParseMethod} from './Types.js';\n\n\n/**\n * Symbol class\n */\nexport class Symbol {\n\n  /**\n   * @constructor\n   * @param {string} symbol The symbol parsed.\n   * @param {string} char The corresponding translation.\n   * @param {Attributes} attributes The attributes for the translation.\n   */\n  constructor(private _symbol: string, private _char: string,\n              private _attributes: Attributes) {\n  }\n\n  public get symbol(): string {\n    return this._symbol;\n  }\n\n  public get char(): string {\n    return this._char;\n  }\n\n  public get attributes(): Attributes {\n    return this._attributes;\n  }\n\n}\n\n\nexport class Macro {\n\n  /**\n   * @constructor\n   * @param {string} symbol The symbol parsed\n   * @param {ParseMethod} func The parsing function for that symbol.\n   * @param {Args[]} args Additional arguments for the function.\n   */\n  constructor(private _symbol: string, private _func: ParseMethod,\n              private _args: Args[] = []) {\n  }\n\n  public get symbol(): string {\n    return this._symbol;\n  }\n\n  public get func(): ParseMethod {\n    return this._func;\n  }\n\n  public get args(): Args[] {\n    return this._args;\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Symbol map classes.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {Attributes, Args, ParseMethod, ParseInput, ParseResult} from './Types.js';\nimport {Symbol, Macro} from './Symbol.js';\nimport {<PERSON><PERSON>andler} from './MapHandler.js';\n\n\n/**\n * SymbolMaps are the base components for the input parsers.\n *\n * They provide a contains method that checks if a map is applicable (contains)\n * a particular string. Implementing classes then perform the actual symbol\n * parsing, from simple regular expression test, straight forward symbol mapping\n * to transformational functionality on the parsed string.\n *\n * @interface\n */\nexport interface SymbolMap {\n\n  /**\n   * @return {string} The name of the map.\n   */\n  name: string;\n\n  /**\n   * @return {ParseMethod} The default parsing method.\n   */\n  parser: ParseMethod;\n\n  /**\n   * @param {string} symbol A symbol to parse.\n   * @return {boolean} True if the symbol map applies to the symbol.\n   */\n  contains(symbol: string): boolean;\n\n  /**\n   * @param {string} symbol A symbol to parse.\n   * @return {ParseMethod} A parse method for the symbol.\n   */\n  parserFor(symbol: string): ParseMethod;\n\n  /**\n   * @param {TexParser} env The current parser.\n   * @param {string} symbol A symbol to parse.\n   * @return {ParseResult} The parsed symbol and the rest of the string.\n   */\n  parse([env, symbol]: ParseInput): ParseResult;\n\n}\n\n/**\n * @param {ParseResult} result    The result to check\n * @return {ParseResult}          True if result was void, result otherwise\n */\nexport function parseResult(result: ParseResult): ParseResult {\n  return result === void 0 ? true : result;\n}\n\n/**\n * Abstract implementation of symbol maps.\n * @template T\n */\nexport abstract class AbstractSymbolMap<T> implements SymbolMap {\n\n  /**\n   * @constructor\n   * @implements {SymbolMap}\n   * @param {string} name Name of the mapping.\n   * @param {ParseMethod} parser The parser for the mappiong.\n   */\n  constructor(private _name: string, private _parser: ParseMethod) {\n    MapHandler.register(this);\n  }\n\n\n  /**\n   * @override\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n\n  /**\n   * @override\n   */\n  public abstract contains(symbol: string): boolean;\n\n\n  /**\n   * @override\n   */\n  public parserFor(symbol: string) {\n    return this.contains(symbol) ? this.parser : null;\n  }\n\n\n  /**\n   * @override\n   */\n  public parse([env, symbol]: ParseInput) {\n    let parser = this.parserFor(symbol);\n    let mapped = this.lookup(symbol);\n    return (parser && mapped) ? parseResult(parser(env, mapped as any)) : null;\n  }\n\n\n  public set parser(parser: ParseMethod) {\n    this._parser = parser;\n  }\n\n  public get parser(): ParseMethod {\n    return this._parser;\n  }\n\n\n  /**\n   * @param {string} symbol\n   * @return {T}\n   */\n  public abstract lookup(symbol: string): T;\n\n}\n\n\n\n/**\n * Regular expressions used for parsing strings.\n */\nexport class RegExpMap extends AbstractSymbolMap<string> {\n\n  /**\n   * @constructor\n   * @extends {AbstractSymbolMap}\n   * @param {string} name Name of the mapping.\n   * @param {ParseMethod} parser The parser for the mappiong.\n   * @param {RegExp} regexp The regular expression.\n   */\n  constructor(name: string, parser: ParseMethod, private _regExp: RegExp) {\n    super(name, parser);\n  }\n\n\n  /**\n   * @override\n   */\n  public contains(symbol: string) {\n    return this._regExp.test(symbol);\n  }\n\n\n  /**\n   * @override\n   */\n  public lookup(symbol: string): string {\n    return this.contains(symbol) ? symbol : null;\n  }\n\n}\n\n\n/**\n * Parse maps associate strings with parsing functionality.\n * @constructor\n * @extends {AbstractSymbolMap}\n * @template K\n */\nexport abstract class AbstractParseMap<K> extends AbstractSymbolMap<K> {\n\n  private map: Map<string, K> = new Map<string, K>();\n\n  /**\n   * @override\n   */\n  public lookup(symbol: string): K {\n    return this.map.get(symbol);\n  }\n\n  /**\n   * @override\n   */\n  public contains(symbol: string) {\n    return this.map.has(symbol);\n  }\n\n  /**\n   * Sets mapping for a symbol.\n   * @param {string} symbol The symbol to map.\n   * @param {K} object The symbols value in the mapping's codomain.\n   */\n  public add(symbol: string, object: K) {\n    this.map.set(symbol, object);\n  }\n\n  /**\n   * Removes a symbol from the map\n   * @param {string} symbol The symbol to remove\n   */\n  public remove(symbol: string) {\n    this.map.delete(symbol);\n  }\n\n}\n\n\n/**\n * Maps symbols that can all be parsed with the same method.\n *\n * @constructor\n * @extends {AbstractParseMap}\n */\nexport class CharacterMap extends AbstractParseMap<Symbol> {\n\n  /**\n   * @constructor\n   * @param {string} name Name of the mapping.\n   * @param {ParseMethod} parser The parser for the mapping.\n   * @param {JSON} json The JSON representation of the character mapping.\n   */\n  constructor(name: string, parser: ParseMethod,\n              json: {[index: string]: string | [string, Attributes]}) {\n    super(name, parser);\n    for (const key of Object.keys(json)) {\n      let value = json[key];\n      let [char, attrs] = (typeof(value) === 'string') ? [value, null] : value;\n      let character = new Symbol(key, char, attrs);\n      this.add(key, character);\n    }\n  }\n\n}\n\n\n/**\n * Maps symbols that are delimiters, that are all parsed with the same method.\n *\n * @constructor\n * @extends {CharacterMap}\n */\nexport class DelimiterMap extends CharacterMap {\n\n  /**\n   * @override\n   */\n  public parse([env, symbol]: ParseInput) {\n    return super.parse([env, '\\\\' + symbol]);\n  }\n\n}\n\n\n/**\n * Maps macros that all bring their own parsing method.\n *\n * @constructor\n * @extends {AbstractParseMap}\n */\nexport class MacroMap extends AbstractParseMap<Macro> {\n\n  /**\n   * @constructor\n   * @param {string} name Name of the mapping.\n   * @param {JSON} json The JSON representation of the macro map.\n   * @param {Record<string, ParseMethod>} functionMap Collection of parse\n   *     functions for the single macros.\n   */\n  constructor(name: string,\n              json: {[index: string]: string | Args[]},\n              functionMap: Record<string, ParseMethod>) {\n    super(name, null);\n    for (const key of Object.keys(json)) {\n      let value = json[key];\n      let [func, ...attrs] = (typeof(value) === 'string') ? [value] : value;\n      let character = new Macro(key, functionMap[func as string], attrs);\n      this.add(key, character);\n    }\n  }\n\n\n  /**\n   * @override\n   */\n  public parserFor(symbol: string) {\n    let macro = this.lookup(symbol);\n    return macro ? macro.func : null;\n  }\n\n\n  /**\n   * @override\n   */\n  public parse([env, symbol]: ParseInput) {\n    let macro = this.lookup(symbol);\n    let parser = this.parserFor(symbol);\n    if (!macro || !parser) {\n      return null;\n    }\n    return parseResult(parser(env, macro.symbol, ...macro.args));\n  }\n\n}\n\n\n/**\n * Maps macros that all bring their own parsing method.\n *\n * @constructor\n * @extends {MacroMap}\n */\nexport class CommandMap extends MacroMap {\n\n  /**\n   * @override\n   */\n  public parse([env, symbol]: ParseInput) {\n    let macro = this.lookup(symbol);\n    let parser = this.parserFor(symbol);\n    if (!macro || !parser) {\n      return null;\n    }\n    let saveCommand = env.currentCS;\n    env.currentCS = '\\\\' + symbol;\n    let result = parser(env, '\\\\' + macro.symbol, ...macro.args);\n    env.currentCS = saveCommand;\n    return parseResult(result);\n  }\n\n}\n\n\n/**\n * Maps macros for environments. It has a general parsing method for\n * environments, i.e., one that deals with begin/end, and each environment has\n * its own parsing method returning the content.\n *\n * @constructor\n * @extends {MacroMap}\n */\nexport class EnvironmentMap extends MacroMap {\n\n  /**\n   * @constructor\n   * @param {string} name Name of the mapping.\n   * @param {ParseMethod} parser The parser for the environments.\n   * @param {JSON} json The JSON representation of the macro map.\n   * @param {Record<string, ParseMethod>} functionMap Collection of parse\n   *     functions for the single macros.\n   */\n  constructor(name: string,\n              parser: ParseMethod,\n              json: {[index: string]: string | Args[]},\n              functionMap: Record<string, ParseMethod>) {\n    super(name, json, functionMap);\n    this.parser = parser;\n  }\n\n\n  /**\n   * @override\n   */\n  public parse([env, symbol]: ParseInput) {\n    let macro = this.lookup(symbol);\n    let envParser = this.parserFor(symbol);\n    if (!macro || !envParser) {\n      return null;\n    }\n    return parseResult(this.parser(env, macro.symbol, envParser, macro.args));\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2009-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Stack items for basic Tex parsing.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\n\nimport {MapHandler} from '../MapHandler.js';\nimport {CharacterMap} from '../SymbolMap.js';\nimport {entities} from '../../../util/Entities.js';\nimport {MmlNode, TextNode, TEXCLASS} from '../../../core/MmlTree/MmlNode.js';\nimport {MmlMsubsup} from '../../../core/MmlTree/MmlNodes/msubsup.js';\nimport TexError from '../TexError.js';\nimport ParseUtil from '../ParseUtil.js';\nimport NodeUtil from '../NodeUtil.js';\nimport {Property} from '../../../core/Tree/Node.js';\nimport StackItemFactory from '../StackItemFactory.js';\nimport {CheckType, BaseItem, StackItem, EnvList} from '../StackItem.js';\n\n\n/**\n * Initial item on the stack. It's pushed when parsing begins.\n */\nexport class StartItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  constructor(factory: StackItemFactory, public global: EnvList) {\n    super(factory);\n  }\n\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'start';\n  }\n\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('stop')) {\n      let node = this.toMml();\n      if (!this.global.isInner) {\n        node = this.factory.configuration.tags.finalize(node, this.env);\n      }\n      return [[this.factory.create('mml', node)], true];\n    }\n    return super.checkItem(item);\n  }\n\n}\n\n\n/**\n * Final item on the stack. Errors will be thrown if other items than the start\n * item are still on the stack.\n */\nexport class StopItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'stop';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n}\n\n\n/**\n * Item indicating an open brace.\n */\nexport class OpenItem extends BaseItem {\n\n\n  /**\n   * @override\n   */\n  protected static errors = Object.assign(Object.create(BaseItem.errors), {\n    // @test ExtraOpenMissingClose\n    'stop': ['ExtraOpenMissingClose',\n             'Extra open brace or missing close brace']\n  });\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'open';\n  }\n\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('close')) {\n      // @test PrimeSup\n      let mml = this.toMml();\n      const node = this.create('node', 'TeXAtom', [mml]);\n      return [[this.factory.create('mml', node)], true];\n    }\n    return super.checkItem(item);\n  }\n}\n\n\n/**\n * Item indicating a close brace. Collapses stack until an OpenItem is found.\n */\nexport class CloseItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'close';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n}\n\n\n/**\n * Item indicating an we are currently dealing with a prime mark.\n */\nexport class PrimeItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'prime';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    let [top0, top1] = this.Peek(2);\n    if (!NodeUtil.isType(top0, 'msubsup') || NodeUtil.isType(top0, 'msup')) {\n      // @test Prime, Double Prime\n      const node = this.create('node', 'msup', [top0, top1]);\n      return [[node, item], true];\n    }\n    NodeUtil.setChild(top0, (top0 as MmlMsubsup).sup, top1);\n    return [[top0, item], true];\n  }\n}\n\n\n/**\n * Item indicating an we are currently dealing with a sub/superscript\n * expression.\n */\nexport class SubsupItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  protected static errors = Object.assign(Object.create(BaseItem.errors), {\n    // @test MissingScript Sub, MissingScript Sup\n    'stop': ['MissingScript',\n             'Missing superscript or subscript argument'],\n    // @test MissingOpenForSup\n    'sup': ['MissingOpenForSup',\n            'Missing open brace for superscript'],\n    // @test MissingOpenForSub\n    'sub': ['MissingOpenForSub',\n            'Missing open brace for subscript']\n  });\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'subsup';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType | null {\n    if (item.isKind('open') || item.isKind('left')) {\n      return BaseItem.success;\n    }\n    const top = this.First;\n    const position = this.getProperty('position') as number;\n    if (item.isKind('mml')) {\n      if (this.getProperty('primes')) {\n        if (position !== 2) {\n          // @test Prime on Sub\n          NodeUtil.setChild(top, 2, this.getProperty('primes') as MmlNode);\n        } else {\n          // @test Prime on Prime\n          NodeUtil.setProperty(this.getProperty('primes') as MmlNode, 'variantForm', true);\n          const node = this.create('node', 'mrow', [this.getProperty('primes') as MmlNode, item.First]);\n          item.First = node;\n        }\n      }\n      NodeUtil.setChild(top, position, item.First);\n      if (this.getProperty('movesupsub') != null) {\n        // @test Limits Subsup (currently does not work! Check again!)\n        NodeUtil.setProperty(top, 'movesupsub', this.getProperty('movesupsub') as Property);\n      }\n      const result = this.factory.create('mml', top);\n      return [[result], true];\n    }\n    if (super.checkItem(item)[1]) {\n      // @test Brace Superscript Error, MissingOpenForSup, MissingOpenForSub\n      const error = this.getErrors(['', 'sub', 'sup'][position]);\n      throw new TexError(error[0], error[1], ...error.splice(2));\n    }\n    return null;\n  }\n\n}\n\n\n/**\n * Item indicating an we are currently dealing with an \\\\over command.\n */\nexport class OverItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  constructor(factory: StackItemFactory) {\n    super(factory);\n    this.setProperty('name', '\\\\over');\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'over';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('over')) {\n      // @test Double Over\n      throw new TexError(\n        'AmbiguousUseOf', 'Ambiguous use of %1', item.getName());\n    }\n    if (item.isClose) {\n      // @test Over\n      let mml = this.create('node',\n                            'mfrac', [this.getProperty('num') as MmlNode, this.toMml(false)]);\n      if (this.getProperty('thickness') != null) {\n        // @test Choose, Above, Above with Delims\n        NodeUtil.setAttribute(mml, 'linethickness',\n                              this.getProperty('thickness') as string);\n      }\n      if (this.getProperty('open') || this.getProperty('close')) {\n        // @test Choose\n        NodeUtil.setProperty(mml, 'withDelims', true);\n        mml = ParseUtil.fixedFence(this.factory.configuration,\n                                   this.getProperty('open') as string, mml,\n                                   this.getProperty('close') as string);\n      }\n      return [[this.factory.create('mml', mml), item], true];\n    }\n    return super.checkItem(item);\n  }\n\n\n  /**\n   * @override\n   */\n  public toString() {\n    return 'over[' + this.getProperty('num') +\n      ' / ' + this.nodes.join('; ') + ']';\n  }\n\n}\n\n\n/**\n * Item pushed when a \\\\left opening delimiter has been found.\n */\nexport class LeftItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  protected static errors = Object.assign(Object.create(BaseItem.errors), {\n    // @test ExtraLeftMissingRight\n    'stop': ['ExtraLeftMissingRight',\n             'Extra \\\\left or missing \\\\right']\n  });\n\n\n  /**\n   * @override\n   */\n  constructor(factory: StackItemFactory, delim: string) {\n    super(factory);\n    this.setProperty('delim', delim);\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'left';\n  }\n\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    // @test Missing Right\n    if (item.isKind('right')) {\n      //\n      //  Create the fenced structure as an mrow\n      //\n      return [[this.factory.create('mml', ParseUtil.fenced(\n        this.factory.configuration,\n        this.getProperty('delim') as string, this.toMml(),\n        item.getProperty('delim') as string, '', item.getProperty('color') as string))], true];\n    }\n    if (item.isKind('middle')) {\n      //\n      //  Add the middle delimiter, with empty open and close elements around it for spacing\n      //\n      const def = {stretchy: true} as any;\n      if (item.getProperty('color')) {\n        def.mathcolor = item.getProperty('color');\n      }\n      this.Push(\n        this.create('node', 'TeXAtom', [], {texClass: TEXCLASS.CLOSE}),\n        this.create('token', 'mo', def, item.getProperty('delim')),\n        this.create('node', 'TeXAtom', [], {texClass: TEXCLASS.OPEN})\n      );\n      this.env = {};         // Since \\middle closes the group, clear the environment\n      return [[this], true]; // this will reset the environment to its initial state\n    }\n    return super.checkItem(item);\n  }\n\n}\n\n/**\n * Item pushed when a \\\\middle delimiter has been found. Stack is\n * collapsed until a corresponding LeftItem is encountered.\n */\nexport class Middle extends BaseItem {\n\n  /**\n   * @override\n   */\n  constructor(factory: StackItemFactory, delim: string, color: string) {\n    super(factory);\n    this.setProperty('delim', delim);\n    color && this.setProperty('color', color);\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'middle';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n}\n\n/**\n * Item pushed when a \\\\right closing delimiter has been found. Stack is\n * collapsed until a corresponding LeftItem is encountered.\n */\nexport class RightItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  constructor(factory: StackItemFactory, delim: string, color: string) {\n    super(factory);\n    this.setProperty('delim', delim);\n    color && this.setProperty('color', color);\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'right';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n}\n\n\n/**\n * Item pushed for opening an environment with \\\\begin{env}.\n */\nexport class BeginItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'begin';\n  }\n\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('end')) {\n      if (item.getName() !== this.getName()) {\n        // @test EnvBadEnd\n        throw new TexError('EnvBadEnd', '\\\\begin{%1} ended with \\\\end{%2}',\n                           this.getName(), item.getName());\n      }\n      if (!this.getProperty('end')) {\n        // @test Hfill\n        return [[this.factory.create('mml', this.toMml())], true];\n      }\n      return BaseItem.fail;  // TODO: This case could probably go!\n    }\n    if (item.isKind('stop')) {\n      // @test EnvMissingEnd Array\n      throw new TexError('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n    }\n    return super.checkItem(item);\n  }\n\n}\n\n\n/**\n * Item pushed for closing an environment with \\\\end{env}. Stack is collapsed\n * until a corresponding BeginItem for 'env' is found. Error is thrown in case\n * other open environments interfere.\n */\nexport class EndItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'end';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n\n}\n\n\n/**\n * Item pushed for remembering styling information.\n */\nexport class StyleItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'style';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (!item.isClose) {\n      return super.checkItem(item);\n    }\n    // @test Style\n    const mml = this.create('node', 'mstyle', this.nodes, this.getProperty('styles'));\n    return [[this.factory.create('mml', mml), item], true];\n  }\n\n}\n\n\n/**\n * Item pushed for remembering positioning information.\n */\nexport class PositionItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'position';\n  }\n\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isClose) {\n      // @test MissingBoxFor\n      throw new TexError('MissingBoxFor', 'Missing box for %1', this.getName());\n    }\n    if (item.isFinal) {\n      let mml = item.toMml();\n      switch (this.getProperty('move')) {\n      case 'vertical':\n        // @test Raise, Lower, Raise Negative, Lower Negative\n        mml = this.create('node', 'mpadded', [mml],\n                          {height: this.getProperty('dh'),\n                           depth: this.getProperty('dd'),\n                           voffset: this.getProperty('dh')});\n        return [[this.factory.create('mml', mml)], true];\n      case 'horizontal':\n        // @test Move Left, Move Right, Move Left Negative, Move Right Negative\n        return [[this.factory.create('mml', this.getProperty('left') as MmlNode), item,\n                 this.factory.create('mml', this.getProperty('right') as MmlNode)], true];\n      }\n    }\n    return super.checkItem(item);\n  }\n}\n\n\n/**\n * Item indicating a table cell.\n */\nexport class CellItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'cell';\n  }\n\n\n  /**\n   * @override\n   */\n  get isClose() {\n    return true;\n  }\n}\n\n\n/**\n * Final item for collating Nodes.\n */\nexport class MmlItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get isFinal() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'mml';\n  }\n\n}\n\n\n/**\n * Item indicating a named function operator (e.g., \\\\sin) as been encountered.\n */\nexport class FnItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'fn';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    const top = this.First;\n    if (top) {\n      if (item.isOpen) {\n        // @test Fn Stretchy\n        return BaseItem.success;\n      }\n      if (!item.isKind('fn')) {\n        // @test Named Function\n        let mml = item.First;\n        if (!item.isKind('mml') || !mml) {\n          // @test Mathop Super\n          return [[top, item], true];\n        }\n        if ((NodeUtil.isType(mml, 'mstyle') && mml.childNodes.length &&\n             NodeUtil.isType(mml.childNodes[0].childNodes[0] as MmlNode, 'mspace')) ||\n             NodeUtil.isType(mml, 'mspace')) {\n          // @test Fn Pos Space, Fn Neg Space\n          return [[top, item], true];\n        }\n        if (NodeUtil.isEmbellished(mml)) {\n          // @test MultiInt with Limits\n          mml = NodeUtil.getCoreMO(mml);\n        }\n        const form = NodeUtil.getForm(mml);\n        if (form != null && [0, 0, 1, 1, 0, 1, 1, 0, 0, 0][form[2]]) {\n          // @test Fn Operator\n          return [[top, item], true];\n        }\n      }\n      // @test Named Function, Named Function Arg\n      const node = this.create('token', 'mo', {texClass: TEXCLASS.NONE},\n                               entities.ApplyFunction);\n      return [[top, node, item], true];\n    }\n    // @test Mathop Super, Mathop Sub\n    return super.checkItem.apply(this, arguments);\n  }\n}\n\n\n/**\n * Item indicating a \\\\not has been encountered and needs to be applied to the\n * next operator.\n */\nexport class NotItem extends BaseItem {\n\n  private remap = MapHandler.getMap('not_remap') as CharacterMap;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'not';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    let mml: TextNode | MmlNode;\n    let c: string;\n    let textNode: TextNode;\n    if (item.isKind('open') || item.isKind('left')) {\n      // @test Negation Left Paren\n      return BaseItem.success;\n    }\n    if (item.isKind('mml') &&\n        (NodeUtil.isType(item.First, 'mo') || NodeUtil.isType(item.First, 'mi') ||\n         NodeUtil.isType(item.First, 'mtext'))) {\n      mml = item.First;\n      c = NodeUtil.getText(mml as TextNode);\n      if (c.length === 1 && !NodeUtil.getProperty(mml, 'movesupsub') &&\n          NodeUtil.getChildren(mml).length === 1) {\n        if (this.remap.contains(c)) {\n          // @test Negation Simple, Negation Complex\n          textNode = this.create('text', this.remap.lookup(c).char) as TextNode;\n          NodeUtil.setChild(mml, 0, textNode);\n        } else {\n          // @test Negation Explicit\n          textNode = this.create('text', '\\u0338') as TextNode;\n          NodeUtil.appendChildren(mml, [textNode]);\n        }\n        return [[item], true];\n      }\n    }\n    // @test Negation Large\n    textNode = this.create('text', '\\u29F8') as TextNode;\n    const mtextNode = this.create('node', 'mtext', [], {}, textNode);\n    const paddedNode = this.create('node', 'mpadded', [mtextNode], {width: 0});\n    mml = this.create('node', 'TeXAtom', [paddedNode], {texClass: TEXCLASS.REL});\n    return [[mml, item], true];\n  }\n}\n\n/**\n * A StackItem that removes an mspace that follows it (for \\nonscript).\n */\nexport class NonscriptItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'nonscript';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    //\n    //  Check if the next item is an mspace (or an mspace in an mstyle) and remove it.\n    //\n    if (item.isKind('mml') && item.Size() === 1) {\n      let mml = item.First;\n      //\n      //  Space macros like \\, are wrapped with an mstyle to set scriptlevel=\"0\"\n      //    (so size is independent of level), we look at the contents of the mstyle for the mspace.\n      //\n      if (mml.isKind('mstyle') && mml.notParent) {\n        mml = NodeUtil.getChildren(NodeUtil.getChildren(mml)[0])[0];\n      }\n      if (mml.isKind('mspace')) {\n        //\n        //  If the space is in an mstyle, wrap it in an mrow so we can test its scriptlevel\n        //    in the post-filter (the mrow will be removed in the filter).  We can't test\n        //    the mstyle's scriptlevel, since it is ecxplicitly setting it to 0.\n        //\n        if (mml !== item.First) {\n          const mrow = this.create('node', 'mrow', [item.Pop()]);\n          item.Push(mrow);\n        }\n        //\n        //  Save the mspace for later post-processing.\n        //\n        this.factory.configuration.addNode('nonscript', item.First);\n      }\n    }\n    return [[item], true];\n  }\n}\n\n/**\n * Item indicating a dots command has been encountered.\n */\nexport class DotsItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'dots';\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('open') || item.isKind('left')) {\n      return BaseItem.success;\n    }\n    let dots = this.getProperty('ldots') as MmlNode;\n    let top = item.First;\n    // @test Operator Dots\n    if (item.isKind('mml') && NodeUtil.isEmbellished(top)) {\n      const tclass = NodeUtil.getTexClass(NodeUtil.getCoreMO(top));\n      if (tclass === TEXCLASS.BIN || tclass === TEXCLASS.REL) {\n        dots = this.getProperty('cdots') as MmlNode;\n      }\n    }\n    return [[dots, item], true];\n  }\n}\n\n\n/**\n * Item indicating an array is assembled. It collates cells, rows and\n * information about column/row separator and framing lines.\n */\nexport class ArrayItem extends BaseItem {\n\n  /**\n   * The table as a list of rows.\n   * @type {MmlNode[]}\n   */\n  public table: MmlNode[] = [];\n\n  /**\n   * The current row as a list of cells.\n   * @type {MmlNode[]}\n   */\n  public row: MmlNode[] = [];\n\n  /**\n   * Frame specification as a list of strings.\n   * @type {string[]}\n   */\n  public frame: string[] = [];\n\n  /**\n   * Hfill value.\n   * @type {number[]}\n   */\n  public hfill: number[] = [];\n\n  /**\n   * Properties for special array definitions.\n   * @type {{[key: string]: string|number|boolean}}\n   */\n  public arraydef: {[key: string]: string | number | boolean} = {};\n\n  /**\n   * True if separators are dashed.\n   * @type {boolean}\n   */\n  public dashed: boolean = false;\n\n  /**\n   * @override\n   */\n  public get kind() {\n    return 'array';\n  }\n\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  get copyEnv() {\n    return false;\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    // @test Array Single\n    if (item.isClose && !item.isKind('over')) {\n      // @test Array Single\n      if (item.getProperty('isEntry')) {\n        // @test Array dashed column, Array solid column\n        this.EndEntry();\n        this.clearEnv();\n        return BaseItem.fail;\n      }\n      if (item.getProperty('isCR')) {\n        // @test Enclosed bottom\n        this.EndEntry();\n        this.EndRow();\n        this.clearEnv();\n        return BaseItem.fail;\n      }\n      this.EndTable();\n      this.clearEnv();\n      let newItem = this.factory.create('mml', this.createMml());\n      if (this.getProperty('requireClose')) {\n        // @test: Label\n        if (item.isKind('close')) {\n          // @test: Label\n          return [[newItem], true];\n        }\n        // @test MissingCloseBrace2\n        throw new TexError('MissingCloseBrace', 'Missing close brace');\n      }\n      return [[newItem, item], true];\n    }\n    return super.checkItem(item);\n  }\n\n  /**\n   * Create the MathML representation of the table.\n   *\n   * @return {MmlNode}\n   */\n  public createMml(): MmlNode {\n    const scriptlevel = this.arraydef['scriptlevel'];\n    delete this.arraydef['scriptlevel'];\n    let mml = this.create('node', 'mtable', this.table, this.arraydef);\n    if (scriptlevel) {\n      mml.setProperty('scriptlevel', scriptlevel);\n    }\n    if (this.frame.length === 4) {\n      // @test Enclosed frame solid, Enclosed frame dashed\n      NodeUtil.setAttribute(mml, 'frame', this.dashed ? 'dashed' : 'solid');\n    } else if (this.frame.length) {\n      // @test Enclosed left right\n      if (this.arraydef['rowlines']) {\n        // @test Enclosed dashed row, Enclosed solid row,\n        this.arraydef['rowlines'] =\n          (this.arraydef['rowlines'] as string).replace(/none( none)+$/, 'none');\n      }\n      // @test Enclosed left right\n      NodeUtil.setAttribute(mml, 'frame', '');\n      mml = this.create('node', 'menclose', [mml], {notation: this.frame.join(' ')});\n      if ((this.arraydef['columnlines'] || 'none') !== 'none' ||\n          (this.arraydef['rowlines'] || 'none') !== 'none') {\n        // @test Enclosed dashed row, Enclosed solid row\n        // @test Enclosed dashed column, Enclosed solid column\n        NodeUtil.setAttribute(mml, 'data-padding', 0);\n      }\n    }\n    if (this.getProperty('open') || this.getProperty('close')) {\n      // @test Cross Product Formula\n      mml = ParseUtil.fenced(this.factory.configuration,\n                             this.getProperty('open') as string, mml,\n                             this.getProperty('close') as string);\n    }\n    return mml;\n  }\n\n  /**\n   * Finishes a single cell of the array.\n   */\n  public EndEntry() {\n    // @test Array1, Array2\n    const mtd = this.create('node', 'mtd', this.nodes);\n    if (this.hfill.length) {\n      if (this.hfill[0] === 0) {\n        NodeUtil.setAttribute(mtd, 'columnalign', 'right');\n      }\n      if (this.hfill[this.hfill.length - 1] === this.Size()) {\n        NodeUtil.setAttribute(\n          mtd, 'columnalign',\n          NodeUtil.getAttribute(mtd, 'columnalign') ? 'center' : 'left');\n      }\n    }\n    this.row.push(mtd);\n    this.Clear();\n    this.hfill = [];\n  }\n\n\n  /**\n   * Finishes a single row of the array.\n   */\n  public EndRow() {\n    let node: MmlNode;\n    if (this.getProperty('isNumbered') && this.row.length === 3) {\n      // @test Label, Matrix Numbered\n      this.row.unshift(this.row.pop());  // move equation number to first\n      // position\n      node = this.create('node', 'mlabeledtr', this.row);\n    } else {\n      // @test Array1, Array2\n      node = this.create('node', 'mtr', this.row);\n    }\n    this.table.push(node);\n    this.row = [];\n  }\n\n\n  /**\n   * Finishes the table layout.\n   */\n  public EndTable() {\n    if (this.Size() || this.row.length) {\n      this.EndEntry();\n      this.EndRow();\n    }\n    this.checkLines();\n  }\n\n\n  /**\n   * Finishes line layout if not already given.\n   */\n  public checkLines() {\n    if (this.arraydef['rowlines']) {\n      const lines = (this.arraydef['rowlines'] as string).split(/ /);\n      if (lines.length === this.table.length) {\n        this.frame.push('bottom');\n        lines.pop();\n        this.arraydef['rowlines'] = lines.join(' ');\n      } else if (lines.length < this.table.length - 1) {\n        this.arraydef['rowlines'] += ' none';\n      }\n    }\n    if (this.getProperty('rowspacing')) {\n      const rows = (this.arraydef['rowspacing'] as string).split(/ /);\n      while (rows.length < this.table.length) {\n        rows.push(this.getProperty('rowspacing') + 'em');\n      }\n      this.arraydef['rowspacing'] = rows.join(' ');\n    }\n  }\n\n  /**\n   * Adds a row-spacing to the current row (padding out the rowspacing if needed to get there).\n   *\n   * @param {string} spacing   The rowspacing to use for the current row.\n   */\n  public addRowSpacing(spacing: string) {\n    if (this.arraydef['rowspacing']) {\n      const rows = (this.arraydef['rowspacing'] as string).split(/ /);\n      if (!this.getProperty('rowspacing')) {\n        // @test Array Custom Linebreak\n        let dimem = ParseUtil.dimen2em(rows[0]);\n        this.setProperty('rowspacing', dimem);\n      }\n      const rowspacing = this.getProperty('rowspacing') as number;\n      while (rows.length < this.table.length) {\n        rows.push(ParseUtil.Em(rowspacing));\n      }\n      rows[this.table.length - 1] = ParseUtil.Em(\n        Math.max(0, rowspacing + ParseUtil.dimen2em(spacing)));\n      this.arraydef['rowspacing'] = rows.join(' ');\n    }\n  }\n\n}\n\n\n/**\n * Item dealing with equation arrays as a special case of arrays. Handles\n * tagging information according to the given tagging style.\n */\nexport class EqnArrayItem extends ArrayItem {\n\n  /**\n   * The length of the longest row.\n   */\n  public maxrow: number = 0;\n\n  /**\n   * @override\n   */\n  constructor(factory: any, ...args: any[]) {\n    super(factory);\n    this.factory.configuration.tags.start(args[0], args[2], args[1]);\n  }\n\n\n  /**\n   * @override\n   */\n  get kind() {\n    return 'eqnarray';\n  }\n\n\n  /**\n   * @override\n   */\n  public EndEntry() {\n    // @test Cubic Binomial\n    if (this.row.length) {\n      ParseUtil.fixInitialMO(this.factory.configuration, this.nodes);\n    }\n    const node = this.create('node', 'mtd', this.nodes);\n    this.row.push(node);\n    this.Clear();\n  }\n\n  /**\n   * @override\n   */\n  public EndRow() {\n    if (this.row.length > this.maxrow) {\n      this.maxrow = this.row.length;\n    }\n    // @test Cubic Binomial\n    let mtr = 'mtr';\n    let tag = this.factory.configuration.tags.getTag();\n    if (tag) {\n      this.row = [tag].concat(this.row);\n      mtr = 'mlabeledtr';\n    }\n    this.factory.configuration.tags.clearTag();\n    const node = this.create('node', mtr, this.row);\n    this.table.push(node);\n    this.row = [];\n  }\n\n  /**\n   * @override\n   */\n  public EndTable() {\n    // @test Cubic Binomial\n    super.EndTable();\n    this.factory.configuration.tags.end();\n    //\n    // Repeat the column align and width specifications\n    //   to match the number of columns\n    //\n    this.extendArray('columnalign', this.maxrow);\n    this.extendArray('columnwidth', this.maxrow);\n    this.extendArray('columnspacing', this.maxrow - 1);\n  }\n\n  /**\n   * Extend a column specification to include a repeating set of values\n   *   so that it has enough to match the maximum row length.\n   */\n  protected extendArray(name: string, max: number) {\n    if (!this.arraydef[name]) return;\n    const repeat = (this.arraydef[name] as string).split(/ /);\n    const columns = [...repeat];\n    if (columns.length > 1) {\n      while (columns.length < max) {\n        columns.push(...repeat);\n      }\n      this.arraydef[name] = columns.slice(0, max).join(' ');\n    }\n  }\n}\n\n\n/**\n * Item dealing with simple equation environments.  Handles tagging information\n * according to the given tagging style.\n */\nexport class EquationItem extends BaseItem {\n\n  /**\n   * @override\n   */\n  constructor(factory: any, ...args: any[]) {\n    super(factory);\n    this.factory.configuration.tags.start('equation', true, args[0]);\n  }\n\n\n  /**\n   * @override\n   */\n  get kind() {\n    return 'equation';\n  }\n\n  /**\n   * @override\n   */\n  get isOpen() {\n    return true;\n  }\n\n  /**\n   * @override\n   */\n  public checkItem(item: StackItem): CheckType {\n    if (item.isKind('end')) {\n      let mml = this.toMml();\n      let tag = this.factory.configuration.tags.getTag();\n      this.factory.configuration.tags.end();\n      return [[tag ? this.factory.configuration.tags.enTag(mml, tag) : mml, item], true];\n    }\n    if (item.isKind('stop')) {\n      // @test EnvMissingEnd Equation\n      throw new TexError('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n    }\n    return super.checkItem(item);\n  }\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Constant definitions for the TeX Parser. These should\n *     eventually be combined with the MathML structure.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nexport namespace TexConstant {\n\n  export const Variant = {\n    NORMAL: 'normal',\n    BOLD: 'bold',\n    ITALIC: 'italic',\n    BOLDITALIC: 'bold-italic',\n    DOUBLESTRUCK: 'double-struck',\n    FRAKTUR: 'fraktur',\n    BOLDFRAKTUR: 'bold-fraktur',\n    SCRIPT: 'script',\n    BOLDSCRIPT: 'bold-script',\n    SANSSERIF: 'sans-serif',\n    BOLDSANSSERIF: 'bold-sans-serif',\n    SANSSERIFITALIC: 'sans-serif-italic',\n    SANSSERIFBOLDITALIC: 'sans-serif-bold-italic',\n    MONOSPACE: 'monospace',\n    INITIAL: 'inital',\n    TAILED: 'tailed',\n    LOOPED: 'looped',\n    STRETCHED: 'stretched',\n    CALLIGRAPHIC: '-tex-calligraphic',\n    BOLDCALLIGRAPHIC: '-tex-bold-calligraphic',\n    OLDSTYLE: '-tex-oldstyle',\n    BOLDOLDSTYLE: '-tex-bold-oldstyle',\n    MATHITALIC: '-tex-mathit'\n  };\n\n  export const Form = {\n    PREFIX: 'prefix',\n    INFIX: 'infix',\n    POSTFIX: 'postfix'\n  };\n\n  export const LineBreak = {\n    AUTO: 'auto',\n    NEWLINE: 'newline',\n    NOBREAK: 'nobreak',\n    GOODBREAK: 'goodbreak',\n    BADBREAK: 'badbreak'\n  };\n\n  export const LineBreakStyle = {\n    BEFORE: 'before',\n    AFTER: 'after',\n    DUPLICATE: 'duplicate',\n    INFIXLINBREAKSTYLE: 'infixlinebreakstyle'\n  };\n\n  export const IndentAlign = {\n    LEFT: 'left',\n    CENTER: 'center',\n    RIGHT: 'right',\n    AUTO: 'auto',\n    ID: 'id',\n    INDENTALIGN: 'indentalign'\n  };\n\n  export const IndentShift = {\n    INDENTSHIFT: 'indentshift'\n  };\n\n  export const LineThickness = {\n    THIN: 'thin',\n    MEDIUM: 'medium',\n    THICK: 'thick'\n  };\n\n  export const Notation = {\n    LONGDIV: 'longdiv',\n    ACTUARIAL: 'actuarial',\n    PHASORANGLE: 'phasorangle',\n    RADICAL: 'radical',\n    BOX: 'box',\n    ROUNDEDBOX: 'roundedbox',\n    CIRCLE: 'circle',\n    LEFT: 'left',\n    RIGHT: 'right',\n    TOP: 'top',\n    BOTTOM: 'bottom',\n    UPDIAGONALSTRIKE: 'updiagonalstrike',\n    DOWNDIAGONALSTRIKE: 'downdiagonalstrike',\n    VERTICALSTRIKE: 'verticalstrike',\n    HORIZONTALSTRIKE: 'horizontalstrike',\n    NORTHEASTARROW: 'northeastarrow',\n    MADRUWB: 'madruwb',\n    UPDIAGONALARROW: 'updiagonalarrow'\n  };\n\n  export const Align = {\n    TOP: 'top',\n    BOTTOM: 'bottom',\n    CENTER: 'center',\n    BASELINE: 'baseline',\n    AXIS: 'axis',\n    LEFT: 'left',\n    RIGHT: 'right'\n  };\n\n  export const Lines = {\n    NONE: 'none',\n    SOLID: 'solid',\n    DASHED: 'dashed'\n  };\n\n  export const Side = {\n    LEFT: 'left',\n    RIGHT: 'right',\n    LEFTOVERLAP: 'leftoverlap',\n    RIGHTOVERLAP: 'rightoverlap'\n  };\n\n  export const Width = {\n    AUTO: 'auto',\n    FIT: 'fit'\n  };\n\n  export const Actiontype = {\n    TOGGLE: 'toggle',\n    STATUSLINE: 'statusline',\n    TOOLTIP: 'tooltip',\n    INPUT: 'input'\n  };\n\n  export const Overflow = {\n    LINBREAK: 'linebreak',\n    SCROLL: 'scroll',\n    ELIDE: 'elide',\n    TRUNCATE: 'truncate',\n    SCALE: 'scale'\n  };\n\n  export const Unit = {\n    EM: 'em',\n    EX: 'ex',\n    PX: 'px',\n    IN: 'in',\n    CM: 'cm',\n    MM: 'mm',\n    PT: 'pt',\n    PC: 'pc'\n  };\n\n}\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview The Basic Parse methods.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport * as sitem from './BaseItems.js';\nimport {StackItem, EnvList} from '../StackItem.js';\nimport {Macro} from '../Symbol.js';\nimport {ParseMethod} from '../Types.js';\nimport NodeUtil from '../NodeUtil.js';\nimport TexError from '../TexError.js';\nimport TexParser from '../TexParser.js';\nimport {TexConstant} from '../TexConstants.js';\nimport ParseUtil from '../ParseUtil.js';\nimport {MmlNode, TEXCLASS} from '../../../core/MmlTree/MmlNode.js';\nimport {MmlMsubsup} from '../../../core/MmlTree/MmlNodes/msubsup.js';\nimport {MmlMunderover} from '../../../core/MmlTree/MmlNodes/munderover.js';\nimport {Label} from '../Tags.js';\nimport {em} from '../../../util/lengths.js';\nimport {entities} from '../../../util/Entities.js';\nimport {lookup} from '../../../util/Options.js';\n\n\n// Namespace\nlet BaseMethods: Record<string, ParseMethod> = {};\n\nconst P_HEIGHT = 1.2 / .85;   // cmex10 height plus depth over .85\nconst MmlTokenAllow: {[key: string]: number} = {\n  fontfamily: 1, fontsize: 1, fontweight: 1, fontstyle: 1,\n  color: 1, background: 1,\n  id: 1, 'class': 1, href: 1, style: 1\n};\n\n\n\n/**\n * Handle LaTeX tokens.\n */\n\n/**\n * Handle {\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Open = function(parser: TexParser, _c: string) {\n  // @test Identifier Font, Prime, Prime with subscript\n  parser.Push(parser.itemFactory.create('open'));\n};\n\n/**\n * Handle }\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Close = function(parser: TexParser, _c: string) {\n  // @test Identifier Font, Prime, Prime with subscript\n  parser.Push(parser.itemFactory.create('close'));\n};\n\n\n/**\n * Handle tilde and spaces.\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Tilde = function(parser: TexParser, _c: string) {\n  // @test Tilde, Tilde2\n  parser.Push(parser.create('token', 'mtext', {}, entities.nbsp));\n};\n\n/**\n * Handling space, by doing nothing.\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Space = function(_parser: TexParser, _c: string) {};\n\n/**\n * Handle ^\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Superscript = function(parser: TexParser, _c: string) {\n  if (parser.GetNext().match(/\\d/)) {\n    // don't treat numbers as a unit\n    parser.string = parser.string.substr(0, parser.i + 1) +\n      ' ' + parser.string.substr(parser.i + 1);\n  }\n  let primes: MmlNode;\n  let base: MmlNode | void;\n  const top = parser.stack.Top();\n  if (top.isKind('prime')) {\n    // @test Prime on Prime\n    [base, primes] = top.Peek(2);\n    parser.stack.Pop();\n  } else {\n    // @test Empty base2, Square, Cube\n    base = parser.stack.Prev();\n    if (!base) {\n      // @test Empty base\n      base = parser.create('token', 'mi', {}, '');\n    }\n  }\n  const movesupsub = NodeUtil.getProperty(base, 'movesupsub');\n  let position = NodeUtil.isType(base, 'msubsup') ? (base as MmlMsubsup).sup :\n    (base as MmlMunderover).over;\n  if ((NodeUtil.isType(base, 'msubsup') && !NodeUtil.isType(base, 'msup') &&\n       NodeUtil.getChildAt(base, (base as MmlMsubsup).sup)) ||\n      (NodeUtil.isType(base, 'munderover') && !NodeUtil.isType(base, 'mover') &&\n       NodeUtil.getChildAt(base, (base as MmlMunderover).over) &&\n       !NodeUtil.getProperty(base, 'subsupOK'))) {\n    // @test Double-super-error, Double-over-error\n    throw new TexError('DoubleExponent', 'Double exponent: use braces to clarify');\n  }\n  if (!NodeUtil.isType(base, 'msubsup') || NodeUtil.isType(base, 'msup')) {\n    if (movesupsub) {\n      // @test Move Superscript, Large Operator\n      if (!NodeUtil.isType(base, 'munderover') || NodeUtil.isType(base, 'mover') ||\n          NodeUtil.getChildAt(base, (base as MmlMunderover).over)) {\n        // @test Large Operator\n        base = parser.create('node', 'munderover', [base], {movesupsub: true});\n      }\n      position = (base as MmlMunderover).over;\n    } else {\n      // @test Empty base, Empty base2, Square, Cube\n      base = parser.create('node', 'msubsup', [base]);\n      position = (base as MmlMsubsup).sup;\n    }\n  }\n  parser.Push(\n    parser.itemFactory.create('subsup', base).setProperties({\n      position: position, primes: primes, movesupsub: movesupsub\n    }) );\n};\n\n\n/**\n * Handle _\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Subscript = function(parser: TexParser, _c: string) {\n  if (parser.GetNext().match(/\\d/)) {\n    // don't treat numbers as a unit\n    parser.string =\n      parser.string.substr(0, parser.i + 1) + ' ' +\n      parser.string.substr(parser.i + 1);\n  }\n  let primes, base;\n  const top = parser.stack.Top();\n  if (top.isKind('prime')) {\n    // @test Prime on Sub\n    [base, primes] = top.Peek(2);\n    parser.stack.Pop();\n  } else {\n    base = parser.stack.Prev();\n    if (!base) {\n      // @test Empty Base Index\n      base = parser.create('token', 'mi', {}, '');\n    }\n  }\n  const movesupsub = NodeUtil.getProperty(base, 'movesupsub');\n  let position = NodeUtil.isType(base, 'msubsup') ?\n    (base as MmlMsubsup).sub : (base as MmlMunderover).under;\n  if ((NodeUtil.isType(base, 'msubsup') && !NodeUtil.isType(base, 'msup') &&\n       NodeUtil.getChildAt(base, (base as MmlMsubsup).sub)) ||\n      (NodeUtil.isType(base, 'munderover') && !NodeUtil.isType(base, 'mover') &&\n       NodeUtil.getChildAt(base, (base as MmlMunderover).under) &&\n       !NodeUtil.getProperty(base, 'subsupOK'))) {\n    // @test Double-sub-error, Double-under-error\n    throw new TexError('DoubleSubscripts', 'Double subscripts: use braces to clarify');\n  }\n  if (!NodeUtil.isType(base, 'msubsup') || NodeUtil.isType(base, 'msup')) {\n    if (movesupsub) {\n      // @test Large Operator, Move Superscript\n      if (!NodeUtil.isType(base, 'munderover') || NodeUtil.isType(base, 'mover') ||\n          NodeUtil.getChildAt(base, (base as MmlMunderover).under)) {\n        // @test Move Superscript\n        base = parser.create('node', 'munderover', [base], {movesupsub: true});\n      }\n      position = (base as MmlMunderover).under;\n    } else {\n      // @test Empty Base Index, Empty Base Index2, Index\n      base = parser.create('node', 'msubsup', [base]);\n      position = (base as MmlMsubsup).sub;\n    }\n  }\n  parser.Push(\n    parser.itemFactory.create('subsup', base).setProperties({\n      position: position, primes: primes, movesupsub: movesupsub\n    }) );\n};\n\n\n/**\n * Handle '\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Prime = function(parser: TexParser, c: string) {\n  // @test Prime\n  let base = parser.stack.Prev();\n  if (!base) {\n    // @test PrimeSup, PrePrime, Prime on Sup\n    base = parser.create('node', 'mi');\n  }\n  if (NodeUtil.isType(base, 'msubsup') && !NodeUtil.isType(base, 'msup') &&\n      NodeUtil.getChildAt(base, (base as MmlMsubsup).sup)) {\n    // @test Double Prime Error\n    throw new TexError('DoubleExponentPrime',\n                        'Prime causes double exponent: use braces to clarify');\n  }\n  let sup = '';\n  parser.i--;\n  do {\n    // @test Prime, PrimeSup, Double Prime, PrePrime\n    sup += entities.prime; parser.i++, c = parser.GetNext();\n  } while (c === '\\'' || c === entities.rsquo);\n  sup = ['', '\\u2032', '\\u2033', '\\u2034', '\\u2057'][sup.length] || sup;\n  const node = parser.create('token', 'mo', {variantForm: true}, sup);\n  parser.Push(\n    parser.itemFactory.create('prime', base, node) );\n};\n\n\n/**\n * Handle comments\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Comment = function(parser: TexParser, _c: string) {\n  while (parser.i < parser.string.length && parser.string.charAt(parser.i) !== '\\n') {\n    parser.i++;\n  }\n};\n\n\n/**\n * Handle hash marks outside of definitions\n * @param {TexParser} parser The calling parser.\n * @param {string} c The parsed character.\n */\nBaseMethods.Hash = function(_parser: TexParser, _c: string) {\n  // @test Hash Error\n  throw new TexError('CantUseHash1',\n                      'You can\\'t use \\'macro parameter character #\\' in math mode');\n};\n\n\n\n/**\n *\n * Handle LaTeX Macros\n *\n */\n\n\n/**\n * Handle \\mathrm, \\mathbf, etc, allowing for multi-letter runs to be one <mi>.\n */\nBaseMethods.MathFont = function(parser: TexParser, name: string, variant: string) {\n  const text = parser.GetArgument(name);\n  let mml = new TexParser(text, {\n    ...parser.stack.env,\n    font: variant,\n    multiLetterIdentifiers: /^[a-zA-Z]+/ as any,\n    noAutoOP: true\n  }, parser.configuration).mml();\n  parser.Push(parser.create('node', 'TeXAtom', [mml]));\n};\n\n/**\n * Setting font, e.g., via \\\\rm, \\\\bf etc.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} font The font name.\n */\nBaseMethods.SetFont = function(parser: TexParser, _name: string, font: string) {\n  parser.stack.env['font'] = font;\n};\n\n/**\n * Setting style, e.g., via \\\\displaystyle, \\\\textstyle, etc.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} texStyle The tex style name: D, T, S, SS\n * @param {boolean} style True if we are in displaystyle.\n * @param {string} level The nesting level for scripts.\n */\nBaseMethods.SetStyle = function(parser: TexParser, _name: string,\n                                texStyle: string, style: boolean,\n                                level: string) {\n  parser.stack.env['style'] = texStyle;\n  parser.stack.env['level'] = level;\n  parser.Push(\n    parser.itemFactory.create('style').setProperty(\n      'styles', {displaystyle: style, scriptlevel: level}));\n};\n\n\n/**\n * Setting size of an expression, e.g., \\\\small, \\\\huge.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {number} size The size value.\n */\nBaseMethods.SetSize = function(parser: TexParser, _name: string, size: number) {\n  parser.stack.env['size'] = size;\n  parser.Push(\n    parser.itemFactory.create('style').setProperty('styles', {mathsize: em(size)}));\n};\n\n/**\n * Setting explicit spaces, e.g., via commata or colons.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} space The space value.\n */\nBaseMethods.Spacer = function(parser: TexParser, _name: string, space: number) {\n  // @test Positive Spacing, Negative Spacing\n  const node = parser.create('node', 'mspace', [], {width: em(space)});\n  const style = parser.create('node', 'mstyle', [node], {scriptlevel: 0});\n  parser.Push(style);\n};\n\n\n/**\n * Parses left/right fenced expressions.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.LeftRight = function(parser: TexParser, name: string) {\n  // @test Fenced, Fenced3\n  const first = name.substr(1);\n  parser.Push(parser.itemFactory.create(first, parser.GetDelimiter(name), parser.stack.env.color));\n};\n\n/**\n * Handle a named math function, e.g., \\\\sin, \\\\cos\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} id Alternative string representation of the function.\n */\nBaseMethods.NamedFn = function(parser: TexParser, name: string, id: string) {\n  // @test Named Function\n  if (!id) {\n    id = name.substr(1);\n  }\n  const mml = parser.create('token', 'mi', {texClass: TEXCLASS.OP}, id);\n  parser.Push(parser.itemFactory.create('fn', mml));\n};\n\n\n/**\n * Handle a named math operator, e.g., \\\\min, \\\\lim\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} id Alternative string representation of the operator.\n */\nBaseMethods.NamedOp = function(parser: TexParser, name: string, id: string) {\n  // @test Limit\n  if (!id) {\n    id = name.substr(1);\n  }\n  id = id.replace(/&thinsp;/, '\\u2006');\n  const mml = parser.create('token', 'mo', {\n    movablelimits: true,\n    movesupsub: true,\n    form: TexConstant.Form.PREFIX,\n    texClass: TEXCLASS.OP\n  }, id);\n  parser.Push(mml);\n};\n\n/**\n * Handle a limits command for math operators.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} limits The limits arguments.\n */\nBaseMethods.Limits = function(parser: TexParser, _name: string, limits: string) {\n  // @test Limits\n  let op = parser.stack.Prev(true);\n  // Get the texclass for the core operator.\n  if (!op || (NodeUtil.getTexClass(NodeUtil.getCoreMO(op)) !== TEXCLASS.OP &&\n              NodeUtil.getProperty(op, 'movesupsub') == null)) {\n    // @test Limits Error\n    throw new TexError('MisplacedLimits', '%1 is allowed only on operators', parser.currentCS);\n  }\n  const top = parser.stack.Top();\n  let node;\n  if (NodeUtil.isType(op, 'munderover') && !limits) {\n    // @test Limits UnderOver\n    node = parser.create('node', 'msubsup');\n    NodeUtil.copyChildren(op, node);\n    op = top.Last = node;\n  } else if (NodeUtil.isType(op, 'msubsup') && limits) {\n    // @test Limits SubSup\n    // node = parser.create('node', 'munderover', NodeUtil.getChildren(op), {});\n    // Needs to be copied, otherwise we get an error in MmlNode.appendChild!\n    node = parser.create('node', 'munderover');\n    NodeUtil.copyChildren(op, node);\n    op = top.Last = node;\n  }\n  NodeUtil.setProperty(op, 'movesupsub', limits ? true : false);\n  NodeUtil.setProperties(NodeUtil.getCoreMO(op), {'movablelimits': false});\n  if (NodeUtil.getAttribute(op, 'movablelimits') ||\n      NodeUtil.getProperty(op, 'movablelimits')) {\n    NodeUtil.setProperties(op, {'movablelimits': false});\n  }\n};\n\n\n/**\n * Handle over commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} open The open delimiter in case of a \"withdelim\" version.\n * @param {string} close The close delimiter.\n */\nBaseMethods.Over = function(parser: TexParser, name: string, open: string, close: string) {\n  // @test Over\n  const mml = parser.itemFactory.create('over').setProperty('name', parser.currentCS) ;\n  if (open || close) {\n    // @test Choose\n    mml.setProperty('open', open);\n    mml.setProperty('close', close);\n  } else if (name.match(/withdelims$/)) {\n    // @test Over With Delims, Above With Delims\n    mml.setProperty('open', parser.GetDelimiter(name));\n    mml.setProperty('close', parser.GetDelimiter(name));\n  }\n  if (name.match(/^\\\\above/)) {\n    // @test Above, Above With Delims\n    mml.setProperty('thickness', parser.GetDimen(name));\n  }\n  else if (name.match(/^\\\\atop/) || open || close) {\n    // @test Choose\n    mml.setProperty('thickness', 0);\n  }\n  parser.Push(mml);\n};\n\n/**\n * Parses a fraction.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Frac = function(parser: TexParser, name: string) {\n  // @test Frac\n  const num = parser.ParseArg(name);\n  const den = parser.ParseArg(name);\n  const node = parser.create('node', 'mfrac', [num, den]);\n  parser.Push(node);\n};\n\n/**\n * Parses a square root element.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Sqrt = function(parser: TexParser, name: string) {\n  const n = parser.GetBrackets(name);\n  let arg = parser.GetArgument(name);\n  if (arg === '\\\\frac') {\n    arg  += '{' + parser.GetArgument(arg) + '}{' + parser.GetArgument(arg) + '}';\n  }\n  let mml = new TexParser(arg, parser.stack.env, parser.configuration).mml();\n  if (!n) {\n    // @test Square Root\n    mml = parser.create('node', 'msqrt', [mml]);\n  } else {\n    // @test General Root\n    mml = parser.create('node', 'mroot', [mml, parseRoot(parser, n)]);\n  }\n  parser.Push(mml);\n};\n\n\n// Utility\n/**\n * Parse a general root.\n * @param {TexParser} parser The calling parser.\n * @param {string} n The index of the root.\n */\nfunction parseRoot(parser: TexParser, n: string) {\n  // @test General Root, Explicit Root\n  const env = parser.stack.env;\n  const inRoot = env['inRoot'];\n  env['inRoot'] = true;\n  const newParser = new TexParser(n, env, parser.configuration);\n  let node = newParser.mml();\n  const global = newParser.stack.global;\n  if (global['leftRoot'] || global['upRoot']) {\n    // @test Tweaked Root\n    const def: EnvList = {};\n    if (global['leftRoot']) {\n      def['width'] = global['leftRoot'];\n    }\n    if (global['upRoot']) {\n      def['voffset'] = global['upRoot'];\n      def['height'] = global['upRoot'];\n    }\n    node = parser.create('node', 'mpadded', [node], def);\n  }\n  env['inRoot'] = inRoot;\n  return node;\n}\n\n\n/**\n * Parse a general root.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Root = function(parser: TexParser, name: string) {\n  const n = parser.GetUpTo(name, '\\\\of');\n  const arg = parser.ParseArg(name);\n  const node = parser.create('node', 'mroot', [arg, parseRoot(parser, n)]);\n  parser.Push(node);\n};\n\n\n/**\n * Parses a movable index element in a root, e.g. \\\\uproot, \\\\leftroot\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} id Argument which should be a string representation of an integer.\n */\nBaseMethods.MoveRoot = function(parser: TexParser, name: string, id: string) {\n  // @test Tweaked Root\n  if (!parser.stack.env['inRoot']) {\n    // @test Misplaced Move Root\n    throw new TexError('MisplacedMoveRoot', '%1 can appear only within a root', parser.currentCS);\n  }\n  if (parser.stack.global[id]) {\n    // @test Multiple Move Root\n    throw new TexError('MultipleMoveRoot', 'Multiple use of %1', parser.currentCS);\n  }\n  let n = parser.GetArgument(name);\n  if (!n.match(/-?[0-9]+/)) {\n    // @test Incorrect Move Root\n    throw new TexError('IntegerArg', 'The argument to %1 must be an integer', parser.currentCS);\n  }\n  n = (parseInt(n, 10) / 15) + 'em';\n  if (n.substr(0, 1) !== '-') {\n    n = '+' + n;\n  }\n  parser.stack.global[id] = n;\n};\n\n\n/**\n * Handle accents.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} accent The accent.\n * @param {boolean} stretchy True if accent is stretchy.\n */\nBaseMethods.Accent = function(parser: TexParser, name: string, accent: string, stretchy: boolean) {\n  // @test Vector\n  const c = parser.ParseArg(name);\n  // @test Vector Font\n  const def = {...ParseUtil.getFontDef(parser), accent: true, mathaccent: true};\n  const entity = NodeUtil.createEntity(accent);\n  const moNode = parser.create('token', 'mo', def, entity);\n  const mml = moNode;\n  NodeUtil.setAttribute(mml, 'stretchy', stretchy ? true : false);\n  // @test Vector Op, Vector\n  const mo = (NodeUtil.isEmbellished(c) ? NodeUtil.getCoreMO(c) : c);\n  if (NodeUtil.isType(mo, 'mo') || NodeUtil.getProperty(mo, 'movablelimits')) {\n    // @test Vector Op\n    NodeUtil.setProperties(mo, {'movablelimits': false});\n  }\n  const muoNode = parser.create('node', 'munderover');\n  // This is necessary to get the empty element into the children.\n  NodeUtil.setChild(muoNode, 0, c);\n  NodeUtil.setChild(muoNode, 1, null);\n  NodeUtil.setChild(muoNode, 2, mml);\n  let texAtom = parser.create('node', 'TeXAtom', [muoNode]);\n  parser.Push(texAtom);\n};\n\n\n/**\n * Handles stacked elements.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} c Character to stack.\n * @param {boolean} stack True if stacked operator.\n */\nBaseMethods.UnderOver = function(parser: TexParser, name: string, c: string, stack: boolean) {\n  const entity = NodeUtil.createEntity(c);\n  const mo = parser.create('token', 'mo', {stretchy: true, accent: true}, entity);\n  const pos = (name.charAt(1) === 'o' ? 'over' : 'under');\n  const base = parser.ParseArg(name);\n  parser.Push(ParseUtil.underOver(parser, base, mo, pos, stack));\n};\n\n\n/**\n * Handles overset.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Overset = function(parser: TexParser, name: string) {\n  // @test Overset\n  const top = parser.ParseArg(name);\n  const base = parser.ParseArg(name);\n  ParseUtil.checkMovableLimits(base);\n  if (top.isKind('mo')) {\n    NodeUtil.setAttribute(top, 'accent', false);\n  }\n  const node = parser.create('node', 'mover', [base, top]);\n  parser.Push(node);\n};\n\n\n/**\n * Handles underset.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Underset = function(parser: TexParser, name: string) {\n  // @test Underset\n  const bot = parser.ParseArg(name);\n  const base = parser.ParseArg(name);\n  ParseUtil.checkMovableLimits(base);\n  if (bot.isKind('mo')) {\n    NodeUtil.setAttribute(bot, 'accent', false);\n  }\n  const node = parser.create('node', 'munder', [base, bot], {accentunder: false});\n  parser.Push(node);\n};\n\n\n/**\n * Handles overunderset.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Overunderset = function(parser: TexParser, name: string) {\n  const top = parser.ParseArg(name);\n  const bot = parser.ParseArg(name);\n  const base = parser.ParseArg(name);\n  ParseUtil.checkMovableLimits(base);\n  if (top.isKind('mo')) {\n    NodeUtil.setAttribute(top, 'accent', false);\n  }\n  if (bot.isKind('mo')) {\n    NodeUtil.setAttribute(bot, 'accent', false);\n  }\n  const node = parser.create('node', 'munderover', [base, bot, top], {accent: false, accentunder: false});\n  parser.Push(node);\n};\n\n\n/**\n * Creates TeXAtom, when class of element is changed explicitly.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {number} mclass The new TeX class.\n */\nBaseMethods.TeXAtom = function(parser: TexParser, name: string, mclass: number) {\n  let def: EnvList = {texClass: mclass};\n  let mml: StackItem | MmlNode;\n  let node: MmlNode;\n  let parsed: MmlNode;\n  if (mclass === TEXCLASS.OP) {\n    def['movesupsub'] = def['movablelimits'] = true;\n    const arg = parser.GetArgument(name);\n    const match = arg.match(/^\\s*\\\\rm\\s+([a-zA-Z0-9 ]+)$/);\n    if (match) {\n      // @test Mathop\n      def['mathvariant'] = TexConstant.Variant.NORMAL;\n      node = parser.create('token', 'mi', def, match[1]);\n    } else {\n      // @test Mathop Cal\n      parsed = new TexParser(arg, parser.stack.env, parser.configuration).mml();\n      node = parser.create('node', 'TeXAtom', [parsed], def);\n    }\n    mml = parser.itemFactory.create('fn', node);\n  } else {\n    // @test Mathrel\n    parsed = parser.ParseArg(name);\n    mml = parser.create('node', 'TeXAtom', [parsed], def);\n  }\n  parser.Push(mml);\n};\n\n\n/**\n * Creates mmltoken elements. Used in Macro substitutions.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.MmlToken = function(parser: TexParser, name: string) {\n  // @test Modulo\n  const kind = parser.GetArgument(name);\n  let attr = parser.GetBrackets(name, '').replace(/^\\s+/, '');\n  const text = parser.GetArgument(name);\n  const def: EnvList = {};\n  const keep: string[] = [];\n  let node: MmlNode;\n  try {\n    node = parser.create('node', kind);\n  } catch (e) {\n    node = null;\n  }\n  if (!node || !node.isToken) {\n    // @test Token Illegal Type, Token Wrong Type\n    throw new TexError('NotMathMLToken', '%1 is not a token element', kind);\n  }\n  while (attr !== '') {\n    const match = attr.match(/^([a-z]+)\\s*=\\s*('[^']*'|\"[^\"]*\"|[^ ,]*)\\s*,?\\s*/i);\n    if (!match) {\n      // @test Token Invalid Attribute\n      throw new TexError('InvalidMathMLAttr', 'Invalid MathML attribute: %1', attr);\n    }\n    if (!node.attributes.hasDefault(match[1]) && !MmlTokenAllow[match[1]]) {\n      // @test Token Unknown Attribute, Token Wrong Attribute\n      throw new TexError('UnknownAttrForElement',\n                          '%1 is not a recognized attribute for %2',\n                          match[1], kind);\n    }\n    let value: string | boolean = ParseUtil.MmlFilterAttribute(\n      parser, match[1], match[2].replace(/^(['\"])(.*)\\1$/, '$2'));\n    if (value) {\n      if (value.toLowerCase() === 'true') {\n        value = true;\n      }\n      else if (value.toLowerCase() === 'false') {\n        value = false;\n      }\n      def[match[1]] = value;\n      keep.push(match[1]);\n    }\n    attr = attr.substr(match[0].length);\n  }\n  if (keep.length) {\n    def['mjx-keep-attrs'] = keep.join(' ');\n  }\n  const textNode = parser.create('text', text);\n  node.appendChild(textNode);\n  NodeUtil.setProperties(node, def);\n  parser.Push(node);\n};\n\n\n/**\n * Handle strut.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Strut = function(parser: TexParser, _name: string) {\n  // @test Strut\n  const row = parser.create('node', 'mrow');\n  const padded = parser.create('node', 'mpadded', [row],\n                                                         {height: '8.6pt', depth: '3pt', width: 0});\n  parser.Push(padded);\n};\n\n/**\n * Handle phantom commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} v Vertical size.\n * @param {string} h Horizontal size.\n */\nBaseMethods.Phantom = function(parser: TexParser, name: string, v: string, h: string) {\n  // @test Phantom\n  let box = parser.create('node', 'mphantom', [parser.ParseArg(name)]);\n  if (v || h) {\n    // TEMP: Changes here\n    box = parser.create('node', 'mpadded', [box]);\n    if (h) {\n      // @test Horizontal Phantom\n      NodeUtil.setAttribute(box, 'height', 0);\n      NodeUtil.setAttribute(box, 'depth', 0);\n    }\n    if (v) {\n      // @test Vertical Phantom\n      NodeUtil.setAttribute(box, 'width', 0);\n    }\n  }\n  const atom = parser.create('node', 'TeXAtom', [box]);\n  parser.Push(atom);\n};\n\n/**\n * Handle smash.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Smash = function(parser: TexParser, name: string) {\n  // @test Smash, Smash Top, Smash Bottom\n  const bt = ParseUtil.trimSpaces(parser.GetBrackets(name, ''));\n  const smash = parser.create('node', 'mpadded', [parser.ParseArg(name)]);\n  // TEMP: Changes here:\n  switch (bt) {\n  case 'b': NodeUtil.setAttribute(smash, 'depth', 0); break;\n  case 't': NodeUtil.setAttribute(smash, 'height', 0); break;\n  default:\n    NodeUtil.setAttribute(smash, 'height', 0);\n    NodeUtil.setAttribute(smash, 'depth', 0);\n  }\n  const atom = parser.create('node', 'TeXAtom', [smash]);\n  parser.Push(atom);\n};\n\n/**\n * Handle rlap and llap commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Lap = function(parser: TexParser, name: string) {\n  // @test Llap, Rlap\n  const mml = parser.create('node', 'mpadded', [parser.ParseArg(name)], {width: 0});\n  if (name === '\\\\llap') {\n    // @test Llap\n    NodeUtil.setAttribute(mml, 'lspace', '-1width');\n  }\n  const atom = parser.create('node', 'TeXAtom', [mml]);\n  parser.Push(atom);\n};\n\n/**\n * Handle raise and lower commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.RaiseLower = function(parser: TexParser, name: string) {\n  // @test Raise, Lower, Raise Negative, Lower Negative\n  let h = parser.GetDimen(name);\n  let item =\n    parser.itemFactory.create('position').setProperties({name: parser.currentCS, move: 'vertical'}) ;\n  // TEMP: Changes here:\n  if (h.charAt(0) === '-') {\n    // @test Raise Negative, Lower Negative\n    h = h.slice(1);\n    name = name.substr(1) === 'raise' ? '\\\\lower' : '\\\\raise';\n  }\n  if (name === '\\\\lower') {\n    // @test Raise, Raise Negative\n    item.setProperty('dh', '-' + h);\n    item.setProperty('dd', '+' + h);\n  } else {\n    // @test Lower, Lower Negative\n    item.setProperty('dh', '+' + h);\n    item.setProperty('dd', '-' + h);\n  }\n  parser.Push(item);\n};\n\n\n/**\n * Handle moveleft, moveright commands\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.MoveLeftRight = function(parser: TexParser, name: string) {\n  // @test Move Left, Move Right, Move Left Negative, Move Right Negative\n  let h = parser.GetDimen(name);\n  let nh = (h.charAt(0) === '-' ? h.slice(1) : '-' + h);\n  if (name === '\\\\moveleft') {\n    let tmp = h;\n    h = nh;\n    nh = tmp;\n  }\n  parser.Push(\n    parser.itemFactory.create('position').setProperties({\n      name: parser.currentCS, move: 'horizontal',\n      left:  parser.create('node', 'mspace', [], {width: h}),\n      right: parser.create('node', 'mspace', [], {width: nh})}) );\n};\n\n\n/**\n * Handle horizontal spacing commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Hskip = function(parser: TexParser, name: string) {\n  // @test Modulo\n  const node = parser.create('node', 'mspace', [],\n                             {width: parser.GetDimen(name)});\n  parser.Push(node);\n};\n\n\n/**\n * Handle removal of spaces in script modes\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Nonscript = function(parser: TexParser, _name: string) {\n  parser.Push(parser.itemFactory.create('nonscript'));\n};\n\n\n/**\n * Handle Rule and Space command\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} style The style of the rule spacer.\n */\nBaseMethods.Rule = function(parser: TexParser, name: string, style: string) {\n  // @test Rule 3D, Space 3D\n  const w = parser.GetDimen(name),\n  h = parser.GetDimen(name),\n  d = parser.GetDimen(name);\n  let def: EnvList = {width: w, height: h, depth: d};\n  if (style !== 'blank') {\n    def['mathbackground'] = (parser.stack.env['color'] || 'black');\n  }\n  const node = parser.create('node', 'mspace', [], def);\n  parser.Push(node);\n};\n\n\n/**\n * Handle rule command.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.rule = function(parser: TexParser, name: string) {\n  // @test Rule 2D\n  const v = parser.GetBrackets(name),\n  w = parser.GetDimen(name),\n  h = parser.GetDimen(name);\n  let mml = parser.create('node', 'mspace', [], {\n    width: w, height: h,\n    mathbackground: (parser.stack.env['color'] || 'black') });\n  if (v) {\n    mml = parser.create('node', 'mpadded', [mml], {voffset: v});\n    if (v.match(/^\\-/)) {\n      NodeUtil.setAttribute(mml, 'height', v);\n      NodeUtil.setAttribute(mml, 'depth', '+' + v.substr(1));\n    } else {\n      NodeUtil.setAttribute(mml, 'height', '+' + v);\n    }\n  }\n  parser.Push(mml);\n};\n\n/**\n * Handle big command sequences, e.g., \\\\big, \\\\Bigg.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {number} mclass The TeX class of the element.\n * @param {number} size The em size.\n */\nBaseMethods.MakeBig = function(parser: TexParser, name: string, mclass: number, size: number) {\n  // @test Choose, Over With Delims, Above With Delims\n  size *= P_HEIGHT;\n  let sizeStr = String(size).replace(/(\\.\\d\\d\\d).+/, '$1') + 'em';\n  const delim = parser.GetDelimiter(name, true);\n  const mo = parser.create('token', 'mo', {\n    minsize: sizeStr, maxsize: sizeStr,\n    fence: true, stretchy: true, symmetric: true\n  }, delim);\n  const node = parser.create('node', 'TeXAtom', [mo], {texClass: mclass});\n  parser.Push(node);\n};\n\n\n/**\n * Handle buildrel command.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.BuildRel = function(parser: TexParser, name: string) {\n  // @test BuildRel, BuildRel Expression\n  const top = parser.ParseUpTo(name, '\\\\over');\n  const bot = parser.ParseArg(name);\n  const node = parser.create('node', 'munderover');\n  // This is necessary to get the empty element into the children.\n  NodeUtil.setChild(node, 0, bot);\n  NodeUtil.setChild(node, 1, null);\n  NodeUtil.setChild(node, 2, top);\n  const atom = parser.create('node', 'TeXAtom', [node], {texClass: TEXCLASS.REL});\n  parser.Push(atom);\n};\n\n\n/**\n * Handle horizontal boxes.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} style Box style.\n * @param {string} font The mathvariant to use\n */\nBaseMethods.HBox = function(parser: TexParser, name: string, style: string, font?: string) {\n  // @test Hbox\n  parser.PushAll(ParseUtil.internalMath(parser, parser.GetArgument(name), style, font));\n};\n\n/**\n * Handle framed boxes.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.FBox = function(parser: TexParser, name: string) {\n  // @test Fbox\n  const internal = ParseUtil.internalMath(parser, parser.GetArgument(name));\n  const node = parser.create('node', 'menclose', internal, {notation: 'box'});\n  parser.Push(node);\n};\n\n/**\n * Handle framed boxes with options.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.FrameBox = function(parser: TexParser, name: string) {\n  const width = parser.GetBrackets(name);\n  const pos = parser.GetBrackets(name) || 'c';\n  let mml = ParseUtil.internalMath(parser, parser.GetArgument(name));\n  if (width) {\n    mml = [parser.create('node', 'mpadded', mml, {\n      width,\n      'data-align': lookup(pos, {l: 'left', r: 'right'}, 'center')\n    })];\n  }\n  const node = parser.create('node', 'TeXAtom',\n                             [parser.create('node', 'menclose', mml, {notation: 'box'})],\n                             {texClass: TEXCLASS.ORD});\n  parser.Push(node);\n};\n\n\n/**\n * Handle \\\\not.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Not = function(parser: TexParser, _name: string) {\n  // @test Negation Simple, Negation Complex, Negation Explicit,\n  //       Negation Large\n  parser.Push(parser.itemFactory.create('not'));\n};\n\n\n/**\n * Handle dots.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Dots = function(parser: TexParser, _name: string) {\n  // @test Operator Dots\n  const ldotsEntity = NodeUtil.createEntity('2026');\n  const cdotsEntity = NodeUtil.createEntity('22EF');\n  const ldots = parser.create('token', 'mo', {stretchy: false}, ldotsEntity);\n  const cdots = parser.create('token', 'mo', {stretchy: false}, cdotsEntity);\n  parser.Push(\n    parser.itemFactory.create('dots').setProperties({\n      ldots: ldots,\n      cdots: cdots\n    }) );\n};\n\n\n/**\n * Handle small matrix environments.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} open Opening fence.\n * @param {string} close Closing fence.\n * @param {string} align Column alignment.\n * @param {string} spacing Column spacing.\n * @param {string} vspacing Row spacing.\n * @param {string} style Display or text style.\n * @param {boolean} cases Is it a cases environment.\n * @param {boolean} numbered Is it a numbered environment.\n */\nBaseMethods.Matrix = function(parser: TexParser, _name: string,\n                              open: string, close: string, align: string,\n                              spacing: string, vspacing: string, style: string,\n                              cases: boolean, numbered: boolean) {\n  const c = parser.GetNext();\n  if (c === '') {\n    // @test Matrix Error\n    throw new TexError('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n  }\n  if (c === '{') {\n    // @test Matrix Braces, Matrix Columns, Matrix Rows.\n    parser.i++;\n  } else {\n    // @test Matrix Arg\n    parser.string = c + '}' + parser.string.slice(parser.i + 1);\n    parser.i = 0;\n  }\n  // @test Matrix Braces, Matrix Columns, Matrix Rows.\n  const array = parser.itemFactory.create('array').setProperty('requireClose', true) as sitem.ArrayItem;\n  array.arraydef = {\n    rowspacing: (vspacing || '4pt'),\n    columnspacing: (spacing || '1em')\n  };\n  if (cases) {\n    // @test Matrix Cases\n    array.setProperty('isCases', true);\n  }\n  if (numbered) {\n    // @test Matrix Numbered\n    array.setProperty('isNumbered', true);\n    array.arraydef.side = numbered;\n  }\n  if (open || close) {\n    // @test Matrix Parens, Matrix Parens Subscript, Matrix Cases\n    array.setProperty('open', open);\n    array.setProperty('close', close);\n  }\n  if (style === 'D') {\n    // @test Matrix Numbered\n    array.arraydef.displaystyle = true;\n  }\n  if (align != null) {\n    // @test Matrix Cases, Matrix Numbered\n    array.arraydef.columnalign = align;\n  }\n  parser.Push(array);\n};\n\n\n/**\n * Handle array entry.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Entry = function(parser: TexParser, name: string) {\n  // @test Label, Array, Cross Product Formula\n  parser.Push(parser.itemFactory.create('cell').setProperties({isEntry: true, name: name}));\n  const top = parser.stack.Top();\n  const env = top.getProperty('casesEnv') as string;\n  const cases = top.getProperty('isCases');\n  if (!cases && !env) return;\n  //\n  //  Make second column be in \\text{...} (unless it is already\n  //  in a \\text{...}, for backward compatibility).\n  //\n  const str = parser.string;\n  let braces = 0, close = -1, i = parser.i, m = str.length;\n  const end = (env ? new RegExp(`^\\\\\\\\end\\\\s*\\\\{${env.replace(/\\*/, '\\\\*')}\\\\}`) : null);\n  //\n  //  Look through the string character by character...\n  //\n  while (i < m) {\n    const c = str.charAt(i);\n    if (c === '{') {\n      //\n      //  Increase the nested brace count and go on\n      //\n      braces++;\n      i++;\n    } else if (c === '}') {\n      //\n      //  If there are too many close braces, just end (we will get an\n      //    error message later when the rest of the string is parsed)\n      //  Otherwise\n      //    decrease the nested brace count,\n      //    if it is now zero and we haven't already marked the end of the\n      //      first brace group, record the position (use to check for \\text{} later)\n      //    go on to the next character.\n      //\n      if (braces === 0) {\n        m = 0;\n      } else {\n        braces--;\n        if (braces === 0 && close < 0) {\n          close = i - parser.i;\n        }\n        i++;\n      }\n    } else if (c === '&' && braces === 0) {\n      //\n      //  Extra alignment tabs are not allowed in cases\n      //\n      // @test ExtraAlignTab\n      throw new TexError('ExtraAlignTab', 'Extra alignment tab in \\\\cases text');\n    } else if (c === '\\\\') {\n      //\n      //  If the macro is \\cr or \\\\, end the search, otherwise skip the macro\n      //  (multi-letter names don't matter, as we will skip the rest of the\n      //   characters in the main loop)\n      //\n      const rest = str.substr(i);\n      if (rest.match(/^((\\\\cr)[^a-zA-Z]|\\\\\\\\)/) || (end && rest.match(end))) {\n        m = 0;\n      } else {\n        i += 2;\n      }\n    } else {\n      //\n      //  Go on to the next character\n      //\n      i++;\n    }\n  }\n  //\n  //  Check if the second column text is already in \\text{};\n  //  If not, process the second column as text and continue parsing from there,\n  //    (otherwise process the second column as normal, since it is in \\text{}\n  //\n  const text = str.substr(parser.i, i - parser.i);\n  if (!text.match(/^\\s*\\\\text[^a-zA-Z]/) || close !== text.replace(/\\s+$/, '').length - 1) {\n    const internal = ParseUtil.internalMath(parser, ParseUtil.trimSpaces(text), 0);\n    parser.PushAll(internal);\n    parser.i = i;\n  }\n};\n\n/**\n * Handle newline in array.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.Cr = function(parser: TexParser, name: string) {\n  // @test Cr Linebreak, Misplaced Cr\n  parser.Push(\n    parser.itemFactory.create('cell').setProperties({isCR: true, name: name}));\n};\n\n\n/**\n * Handle newline outside array.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {boolean} nobrackets Flag indicating if newline is followed by\n *     brackets.\n */\nBaseMethods.CrLaTeX = function(parser: TexParser, name: string, nobrackets: boolean = false) {\n  let n: string;\n  if (!nobrackets) {\n    // TODO: spaces before * and [ are not allowed in AMS environments like align, but\n    //       should be allowed in array and eqnarray.  This distinction should be honored here.\n    if (parser.string.charAt(parser.i) === '*') {  // The * controls page breaking, so ignore it\n      parser.i++;\n    }\n    if (parser.string.charAt(parser.i) === '[') {\n      let dim = parser.GetBrackets(name, '');\n      let [value, unit, ] = ParseUtil.matchDimen(dim);\n      // @test Custom Linebreak\n      if (dim && !value) {\n        // @test Dimension Error\n        throw new TexError('BracketMustBeDimension',\n                           'Bracket argument to %1 must be a dimension', parser.currentCS);\n      }\n      n = value + unit;\n    }\n  }\n  parser.Push(\n    parser.itemFactory.create('cell').setProperties({isCR: true, name: name, linebreak: true})\n  );\n  const top = parser.stack.Top();\n  let node: MmlNode;\n  if (top instanceof sitem.ArrayItem) {\n    // @test Array\n    if (n) {\n      top.addRowSpacing(n);\n    }\n  } else {\n    if (n) {\n      // @test Custom Linebreak\n      node = parser.create('node', 'mspace', [], {depth: n});\n      parser.Push(node);\n    }\n    // @test Linebreak\n    node = parser.create('node', 'mspace', [], {linebreak: TexConstant.LineBreak.NEWLINE});\n    parser.Push(node);\n  }\n};\n\n/**\n * Handle horizontal lines in arrays.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {string} style Style of the line. E.g., dashed.\n */\nBaseMethods.HLine = function(parser: TexParser, _name: string, style: string) {\n  if (style == null) {\n    style = 'solid';\n  }\n  const top = parser.stack.Top();\n  if (!(top instanceof sitem.ArrayItem) || top.Size()) {\n    // @test Misplaced hline\n    throw new TexError('Misplaced', 'Misplaced %1', parser.currentCS);\n  }\n  if (!top.table.length) {\n    // @test Enclosed top, Enclosed top bottom\n    top.frame.push('top');\n  } else {\n    // @test Enclosed bottom, Enclosed top bottom\n    const lines = (top.arraydef['rowlines'] ? (top.arraydef['rowlines'] as string).split(/ /) : []);\n    while (lines.length < top.table.length) {\n      lines.push('none');\n    }\n    lines[top.table.length - 1] = style;\n    top.arraydef['rowlines'] = lines.join(' ');\n  }\n};\n\n\n/**\n * Handle hfill commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.HFill = function(parser: TexParser, _name: string) {\n  const top = parser.stack.Top();\n  if (top instanceof sitem.ArrayItem) {\n    // @test Hfill\n    top.hfill.push(top.Size());\n  } else {\n    // @test UnsupportedHFill\n    throw new TexError('UnsupportedHFill', 'Unsupported use of %1', parser.currentCS);\n  }\n};\n\n\n/**\n *   LaTeX environments\n */\n\n/**\n * Handle begin and end environments. This is a macro method.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.BeginEnd = function(parser: TexParser, name: string) {\n  // @test Array1, Array2, Array Test\n  let env = parser.GetArgument(name);\n  if (env.match(/\\\\/i)) {\n    // @test InvalidEnv\n    throw new TexError('InvalidEnv', 'Invalid environment name \\'%1\\'', env);\n  }\n  let macro = parser.configuration.handlers.get('environment').lookup(env) as Macro;\n  if (macro && name === '\\\\end') {\n    // If the first argument is true, we have some sort of user defined\n    // environment. Otherwise we have a standard LaTeX environment that is\n    // handled with begin and end items.\n    if (!macro.args[0]) {\n      const mml = parser.itemFactory.create('end').setProperty('name', env);\n      parser.Push(mml);\n      return;\n    }\n    // Remember the user defined environment we are closing.\n    parser.stack.env['closing'] = env;\n  }\n  ParseUtil.checkMaxMacros(parser, false);\n  parser.parse('environment', [parser, env]);\n};\n\n\n/**\n * Handle array environment.\n * @param {TexParser} parser The calling parser.\n * @param {StackItem} begin The opening stackitem.\n * @param {string} open Opening fence.\n * @param {string} close Closing fence.\n * @param {string} align Column alignment.\n * @param {string} spacing Column spacing.\n * @param {string} vspacing Row spacing.\n * @param {string} style Display or text style.\n * @param {boolean} raggedHeight Does the height need to be adjusted?\n */\nBaseMethods.Array = function(parser: TexParser, begin: StackItem,\n                             open: string, close: string, align: string,\n                             spacing: string, vspacing: string, style: string,\n                             raggedHeight: boolean) {\n  if (!align) {\n    // @test Array Single\n    align = parser.GetArgument('\\\\begin{' + begin.getName() + '}');\n  }\n  let lines = ('c' + align).replace(/[^clr|:]/g, '').replace(/[^|:]([|:])+/g, '$1');\n  align = align.replace(/[^clr]/g, '').split('').join(' ');\n  align = align.replace(/l/g, 'left').replace(/r/g, 'right').replace(/c/g, 'center');\n  const array = parser.itemFactory.create('array') as sitem.ArrayItem;\n  array.arraydef = {\n    columnalign: align,\n    columnspacing: (spacing || '1em'),\n    rowspacing: (vspacing || '4pt')\n  };\n  if (lines.match(/[|:]/)) {\n    // @test Enclosed left right\n    if (lines.charAt(0).match(/[|:]/)) {\n      // @test Enclosed left right, Enclosed left\n      array.frame.push('left');\n      array.dashed = lines.charAt(0) === ':';\n    }\n    if (lines.charAt(lines.length - 1).match(/[|:]/)) {\n      // @test Enclosed left right, Enclosed right\n      array.frame.push('right');\n    }\n    // @test Enclosed left right\n    lines = lines.substr(1, lines.length - 2);\n    array.arraydef.columnlines =\n      lines.split('').join(' ').replace(/[^|: ]/g, 'none').replace(/\\|/g, 'solid').replace(/:/g, 'dashed');\n  }\n  if (open)  {\n    // @test Cross Product\n    array.setProperty('open', parser.convertDelimiter(open));\n  }\n  if (close) {\n    // @test Cross Product\n    array.setProperty('close', parser.convertDelimiter(close));\n  }\n  if ((style || '').charAt(1) === '\\'') {\n    array.arraydef['data-cramped'] = true;\n    style = style.charAt(0);\n  }\n  if (style === 'D') {\n    // TODO: This case never seems to occur! No test.\n    array.arraydef['displaystyle'] = true;\n  }\n  else if (style) {\n    // @test Subarray, Small Matrix\n    array.arraydef['displaystyle'] = false;\n  }\n  if (style === 'S') {\n    // @test Subarray, Small Matrix\n    array.arraydef['scriptlevel'] = 1;\n  }\n  if (raggedHeight)  {\n    // @test Subarray, Small Matrix\n    array.arraydef['useHeight'] = false;\n  }\n  parser.Push(begin);\n  return array;\n};\n\n\n/**\n * Handle aligned arrays.\n * @param {TexParser} parser The calling parser.\n * @param {StackItem} begin The opening stackitem.\n */\nBaseMethods.AlignedArray = function(parser: TexParser, begin: StackItem) {\n  // @test Array1, Array2, Array Test\n  const align = parser.GetBrackets('\\\\begin{' + begin.getName() + '}');\n  let item = BaseMethods.Array(parser, begin);\n  return ParseUtil.setArrayAlign(item as sitem.ArrayItem, align);\n};\n\n\n/**\n * Handle equation environment.\n * @param {TexParser} parser The calling parser.\n * @param {StackItem} begin The opening stackitem.\n * @param {boolean} numbered True if environment is numbered.\n */\nBaseMethods.Equation = function (parser: TexParser, begin: StackItem, numbered: boolean) {\n  parser.Push(begin);\n  ParseUtil.checkEqnEnv(parser);\n  return parser.itemFactory.create('equation', numbered).\n    setProperty('name', begin.getName());\n};\n\n\n/**\n * Handle eqnarray.\n * @param {TexParser} parser The calling parser.\n * @param {StackItem} begin The opening stackitem.\n * @param {boolean} numbered True if environment is numbered.\n * @param {boolean} taggable True if taggable.\n * @param {string} align Alignment string.\n * @param {string} spacing Spacing between columns.\n */\nBaseMethods.EqnArray = function(parser: TexParser, begin: StackItem,\n                                numbered: boolean, taggable: boolean,\n                                align: string, spacing: string) {\n  // @test The Lorenz Equations, Maxwell's Equations, Cubic Binomial\n  parser.Push(begin);\n  if (taggable) {\n    ParseUtil.checkEqnEnv(parser);\n  }\n  align = align.replace(/[^clr]/g, '').split('').join(' ');\n  align = align.replace(/l/g, 'left').replace(/r/g, 'right').replace(/c/g, 'center');\n  let newItem = parser.itemFactory.create('eqnarray', begin.getName(),\n                                          numbered, taggable, parser.stack.global) as sitem.ArrayItem;\n  newItem.arraydef = {\n    displaystyle: true,\n    columnalign: align,\n    columnspacing: (spacing || '1em'),\n    rowspacing: '3pt',\n    side: parser.options['tagSide'],\n    minlabelspacing: parser.options['tagIndent']\n  };\n  return newItem;\n};\n\n\n/**\n * Handles no tag commands.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.HandleNoTag = function(parser: TexParser, _name: string) {\n  parser.tags.notag();\n};\n\n\n/**\n * Record a label name for a tag\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.HandleLabel = function(parser: TexParser, name: string) {\n  // @test Label, Label Empty\n  let label = parser.GetArgument(name);\n  if (label === '') {\n    // @test Label Empty\n    return;\n  }\n  if (!parser.tags.refUpdate) {\n    // @test Label, Ref, Ref Unknown\n    if (parser.tags.label) {\n      // @test Double Label Error\n      throw new TexError('MultipleCommand', 'Multiple %1', parser.currentCS);\n    }\n    parser.tags.label = label;\n    if ((parser.tags.allLabels[label] || parser.tags.labels[label]) && !parser.options['ignoreDuplicateLabels']) {\n      // @ Duplicate Label Error\n      throw new TexError('MultipleLabel', 'Label \\'%1\\' multiply defined', label);\n    }\n    // TODO: This should be set in the tags structure!\n    parser.tags.labels[label] = new Label(); // will be replaced by tag value later\n  }\n};\n\n\n/**\n * Handle a label reference.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n * @param {boolean} eqref True if formatted as eqref.\n */\nBaseMethods.HandleRef = function(parser: TexParser, name: string, eqref: boolean) {\n  // @test Ref, Ref Unknown, Eqref, Ref Default, Ref Named\n  let label = parser.GetArgument(name);\n  let ref = parser.tags.allLabels[label] || parser.tags.labels[label];\n  if (!ref) {\n    // @test Ref Unknown\n    if (!parser.tags.refUpdate) {\n      parser.tags.redo = true;\n    }\n    ref = new Label();\n  }\n  let tag = ref.tag;\n  if (eqref) {\n    // @test Eqref\n    tag = parser.tags.formatTag(tag);\n  }\n  let node = parser.create('node', 'mrow', ParseUtil.internalMath(parser, tag), {\n    href: parser.tags.formatUrl(ref.id, parser.options.baseURL), 'class': 'MathJax_ref'\n  });\n  parser.Push(node);\n};\n\n\n\n/**\n * Macros\n */\nBaseMethods.Macro = function(parser: TexParser, name: string,\n                             macro: string, argcount: number,\n                             def?: string) {\n  if (argcount) {\n    const args: string[] = [];\n    if (def != null) {\n      const optional = parser.GetBrackets(name);\n      args.push(optional == null ? def : optional);\n    }\n    for (let i = args.length; i < argcount; i++) {\n      args.push(parser.GetArgument(name));\n    }\n    macro = ParseUtil.substituteArgs(parser, args, macro);\n  }\n  parser.string = ParseUtil.addArgs(parser, macro, parser.string.slice(parser.i));\n  parser.i = 0;\n  ParseUtil.checkMaxMacros(parser);\n};\n\n\n/**\n * Handle MathChoice for elements whose exact size/style properties can only be\n * determined after the expression has been parsed.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The macro name.\n */\nBaseMethods.MathChoice = function(parser: TexParser, name: string) {\n  const D  = parser.ParseArg(name);\n  const T  = parser.ParseArg(name);\n  const S  = parser.ParseArg(name);\n  const SS = parser.ParseArg(name);\n  parser.Push(parser.create('node', 'MathChoice', [D, T, S, SS]));\n};\n\n\nexport default BaseMethods;\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Base methods for TeX Parsing.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {Symbol} from './Symbol.js';\nimport TexParser from './TexParser.js';\nimport NodeUtil from './NodeUtil.js';\nimport {TexConstant} from './TexConstants.js';\nimport {MmlNode} from '../../core/MmlTree/MmlNode.js';\nimport ParseUtil from './ParseUtil.js';\n\n\nnamespace ParseMethods {\n\n  /**\n   * Handle a variable (a single letter or multi-letter if allowed).\n   * @param {TexParser} parser The current tex parser.\n   * @param {string} c The letter to transform into an mi.\n   */\n  export function variable(parser: TexParser, c: string) {\n    // @test Identifier Font\n    const def = ParseUtil.getFontDef(parser);\n    const env = parser.stack.env;\n    if (env.multiLetterIdentifiers && env.font !== '') {\n      c = parser.string.substr(parser.i - 1).match(env.multiLetterIdentifiers as any as RegExp)[0];\n      parser.i += c.length - 1;\n      if (def.mathvariant === TexConstant.Variant.NORMAL && env.noAutoOP && c.length > 1) {\n        def.autoOP = false;\n      }\n    }\n    // @test Identifier\n    const node = parser.create('token', 'mi', def, c);\n    parser.Push(node);\n  }\n\n\n  /**\n   * Handle a number (a sequence of digits, with decimal separator, etc.).\n   * @param {TexParser} parser The current tex parser.\n   * @param {string} c The first character of a number than can be parsed with\n   *     the digits pattern.\n   */\n  export function digit(parser: TexParser, c: string) {\n    let mml: MmlNode;\n    const pattern = parser.configuration.options['digits'];\n    const n = parser.string.slice(parser.i - 1).match(pattern);\n    // @test Integer Font\n    const def = ParseUtil.getFontDef(parser);\n    if (n) {\n      // @test Integer, Number, Decimal (European)\n      mml = parser.create('token', 'mn', def, n[0].replace(/[{}]/g, ''));\n      parser.i += n[0].length - 1;\n    } else {\n      // @test Decimal Point, Decimal Point European\n      mml = parser.create('token', 'mo', def, c);\n    }\n    parser.Push(mml);\n  }\n\n  /**\n   * Lookup a control-sequence and process it.\n   * @param {TexParser} parser The current tex parser.\n   * @param {string} c The string '\\'.\n   */\n  export function controlSequence(parser: TexParser, _c: string) {\n    const name = parser.GetCS();\n    parser.parse('macro', [parser, name]);\n  }\n\n\n  /**\n   * Handle normal mathchar (as an mi).\n   * @param {TexParser} parser The current tex parser.\n   * @param {Symbol} mchar The parsed symbol.\n   */\n  export function mathchar0mi(parser: TexParser, mchar: Symbol) {\n    const def = mchar.attributes || {mathvariant: TexConstant.Variant.ITALIC};\n    // @test Greek\n    const node = parser.create('token', 'mi', def, mchar.char);\n    parser.Push(node);\n  }\n\n  /**\n   * Handle normal mathchar (as an mo).\n   * @param {TexParser} parser The current tex parser.\n   * @param {Symbol} mchar The parsed symbol.\n   */\n  export function mathchar0mo(parser: TexParser, mchar: Symbol) {\n    const def = mchar.attributes || {};\n    def['stretchy'] = false;\n    // @test Large Set\n    const node = parser.create('token', 'mo', def, mchar.char);\n    NodeUtil.setProperty(node, 'fixStretchy', true);\n    parser.configuration.addNode('fixStretchy', node);\n    // PROBLEM: Attributes stop working when Char7 are explicitly set.\n    parser.Push(node);\n  }\n\n  /**\n   * Handle mathchar in current family.\n   * @param {TexParser} parser The current tex parser.\n   * @param {Symbol} mchar The parsed symbol.\n   */\n  export function mathchar7(parser: TexParser, mchar: Symbol) {\n    const def = mchar.attributes || {mathvariant: TexConstant.Variant.NORMAL};\n    if (parser.stack.env['font']) {\n      // @test MathChar7 Single Font\n      def['mathvariant'] = parser.stack.env['font'];\n    }\n    // @test MathChar7 Single, MathChar7 Operator, MathChar7 Multi\n    const node = parser.create('token', 'mi', def, mchar.char);\n    parser.Push(node);\n  }\n\n  /**\n   * Handle delimiter.\n   * @param {TexParser} parser The current tex parser.\n   * @param {Symbol} delim The parsed delimiter symbol.\n   */\n  export function delimiter(parser: TexParser, delim: Symbol) {\n    let def = delim.attributes || {};\n    // @test Fenced2, Delimiter (AMS)\n    def = Object.assign({fence: false, stretchy: false}, def);\n    const node = parser.create('token', 'mo', def, delim.char);\n    parser.Push(node);\n  }\n\n\n  /**\n   * Parse an environment.\n   * @param {TexParser} parser The current tex parser.\n   * @param {string} env The name of the environment.\n   * @param {Function} func The parse method for the environment.\n   * @param {any[]} args A list of additional arguments.\n   */\n  export function environment(parser: TexParser, env: string, func: Function, args: any[]) {\n    const end = args[0];\n    let mml = parser.itemFactory.create('begin').setProperties({name: env, end: end});\n    mml = func(parser, mml, ...args.slice(1));\n    parser.Push(mml);\n  }\n\n}\n\nexport default ParseMethods;\n", "/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Base mappings for TeX Parsing.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport *  as sm from '../SymbolMap.js';\nimport {TexConstant} from '../TexConstants.js';\nimport BaseMethods from './BaseMethods.js';\nimport ParseMethods from '../ParseMethods.js';\nimport ParseUtil from '../ParseUtil.js';\nimport {TEXCLASS} from '../../../core/MmlTree/MmlNode.js';\nimport {MATHSPACE, em} from '../../../util/lengths.js';\n\n\n/**\n * Letter pattern for parsing identifiers and operators.\n */\nnew sm.RegExpMap('letter', ParseMethods.variable, /[a-z]/i);\n\n\n/**\n * Digit pattern for parsing numbers.\n */\nnew sm.RegExpMap('digit', ParseMethods.digit, /[0-9.,]/);\n\n\n/**\n * Pattern for spotting start of commands.\n */\nnew sm.RegExpMap('command', ParseMethods.controlSequence, /^\\\\/ );\n\n\n/**\n * Treatment of special characters in LaTeX.\n */\nnew sm.MacroMap('special', {\n\n  // This is now handled with a RegExp!\n  // '\\\\':  'ControlSequence',\n\n  '{':   'Open',\n  '}':   'Close',\n  '~':   'Tilde',\n  '^':   'Superscript',\n  '_':   'Subscript',\n  ' ':   'Space',\n  '\\t':  'Space',\n  '\\r':  'Space',\n  '\\n':  'Space',\n  '\\'':  'Prime',\n  '%':   'Comment',\n  '&':   'Entry',\n  '#':   'Hash',\n  '\\u00A0': 'Space',\n  '\\u2019': 'Prime'\n}, BaseMethods);\n\n\n/**\n * Macros for identifiers.\n */\nnew sm.CharacterMap('mathchar0mi', ParseMethods.mathchar0mi, {\n  // Lower-case greek\n  alpha:        '\\u03B1',\n  beta:         '\\u03B2',\n  gamma:        '\\u03B3',\n  delta:        '\\u03B4',\n  epsilon:      '\\u03F5',\n  zeta:         '\\u03B6',\n  eta:          '\\u03B7',\n  theta:        '\\u03B8',\n  iota:         '\\u03B9',\n  kappa:        '\\u03BA',\n  lambda:       '\\u03BB',\n  mu:           '\\u03BC',\n  nu:           '\\u03BD',\n  xi:           '\\u03BE',\n  omicron:      '\\u03BF', // added for completeness\n  pi:           '\\u03C0',\n  rho:          '\\u03C1',\n  sigma:        '\\u03C3',\n  tau:          '\\u03C4',\n  upsilon:      '\\u03C5',\n  phi:          '\\u03D5',\n  chi:          '\\u03C7',\n  psi:          '\\u03C8',\n  omega:        '\\u03C9',\n  varepsilon:   '\\u03B5',\n  vartheta:     '\\u03D1',\n  varpi:        '\\u03D6',\n  varrho:       '\\u03F1',\n  varsigma:     '\\u03C2',\n  varphi:       '\\u03C6',\n\n  // Ord symbols\n  S:            ['\\u00A7', {mathvariant: TexConstant.Variant.NORMAL}],\n  aleph:        ['\\u2135', {mathvariant: TexConstant.Variant.NORMAL}],\n  hbar:         ['\\u210F', {variantForm: true}],\n  imath:        '\\u0131',\n  jmath:        '\\u0237',\n  ell:          '\\u2113',\n  wp:           ['\\u2118', {mathvariant: TexConstant.Variant.NORMAL}],\n  Re:           ['\\u211C', {mathvariant: TexConstant.Variant.NORMAL}],\n  Im:           ['\\u2111', {mathvariant: TexConstant.Variant.NORMAL}],\n  partial:      ['\\u2202', {mathvariant: TexConstant.Variant.ITALIC}],\n  infty:        ['\\u221E', {mathvariant: TexConstant.Variant.NORMAL}],\n  prime:        ['\\u2032', {variantForm: true}],\n  emptyset:     ['\\u2205', {mathvariant: TexConstant.Variant.NORMAL}],\n  nabla:        ['\\u2207', {mathvariant: TexConstant.Variant.NORMAL}],\n  top:          ['\\u22A4', {mathvariant: TexConstant.Variant.NORMAL}],\n  bot:          ['\\u22A5', {mathvariant: TexConstant.Variant.NORMAL}],\n  angle:        ['\\u2220', {mathvariant: TexConstant.Variant.NORMAL}],\n  triangle:     ['\\u25B3', {mathvariant: TexConstant.Variant.NORMAL}],\n  backslash:    ['\\u2216', {mathvariant: TexConstant.Variant.NORMAL}],\n  forall:       ['\\u2200', {mathvariant: TexConstant.Variant.NORMAL}],\n  exists:       ['\\u2203', {mathvariant: TexConstant.Variant.NORMAL}],\n  neg:          ['\\u00AC', {mathvariant: TexConstant.Variant.NORMAL}],\n  lnot:         ['\\u00AC', {mathvariant: TexConstant.Variant.NORMAL}],\n  flat:         ['\\u266D', {mathvariant: TexConstant.Variant.NORMAL}],\n  natural:      ['\\u266E', {mathvariant: TexConstant.Variant.NORMAL}],\n  sharp:        ['\\u266F', {mathvariant: TexConstant.Variant.NORMAL}],\n  clubsuit:     ['\\u2663', {mathvariant: TexConstant.Variant.NORMAL}],\n  diamondsuit:  ['\\u2662', {mathvariant: TexConstant.Variant.NORMAL}],\n  heartsuit:    ['\\u2661', {mathvariant: TexConstant.Variant.NORMAL}],\n  spadesuit:    ['\\u2660', {mathvariant: TexConstant.Variant.NORMAL}]\n});\n\n\n/**\n * Macros for operators.\n */\nnew sm.CharacterMap('mathchar0mo', ParseMethods.mathchar0mo, {\n  surd:         '\\u221A',\n\n  // big ops\n  coprod:       ['\\u2210', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigvee:       ['\\u22C1', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigwedge:     ['\\u22C0', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  biguplus:     ['\\u2A04', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigcap:       ['\\u22C2', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigcup:       ['\\u22C3', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  'int':        ['\\u222B', {texClass: TEXCLASS.OP}],\n  intop:        ['\\u222B', {texClass: TEXCLASS.OP,\n                            movesupsub: true, movablelimits: true}],\n  iint:         ['\\u222C', {texClass: TEXCLASS.OP}],\n  iiint:        ['\\u222D', {texClass: TEXCLASS.OP}],\n  prod:         ['\\u220F', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  sum:          ['\\u2211', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigotimes:    ['\\u2A02', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigoplus:     ['\\u2A01', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  bigodot:      ['\\u2A00', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  oint:         ['\\u222E', {texClass: TEXCLASS.OP}],\n  bigsqcup:     ['\\u2A06', {texClass: TEXCLASS.OP,\n                            movesupsub: true}],\n  smallint:     ['\\u222B', {largeop: false}],\n\n  // binary operations\n  triangleleft:      '\\u25C3',\n  triangleright:     '\\u25B9',\n  bigtriangleup:     '\\u25B3',\n  bigtriangledown:   '\\u25BD',\n  wedge:        '\\u2227',\n  land:         '\\u2227',\n  vee:          '\\u2228',\n  lor:          '\\u2228',\n  cap:          '\\u2229',\n  cup:          '\\u222A',\n  ddagger:      '\\u2021',\n  dagger:       '\\u2020',\n  sqcap:        '\\u2293',\n  sqcup:        '\\u2294',\n  uplus:        '\\u228E',\n  amalg:        '\\u2A3F',\n  diamond:      '\\u22C4',\n  bullet:       '\\u2219',\n  wr:           '\\u2240',\n  div:          '\\u00F7',\n  divsymbol:    '\\u00F7',\n  odot:         ['\\u2299', {largeop: false}],\n  oslash:       ['\\u2298', {largeop: false}],\n  otimes:       ['\\u2297', {largeop: false}],\n  ominus:       ['\\u2296', {largeop: false}],\n  oplus:        ['\\u2295', {largeop: false}],\n  mp:           '\\u2213',\n  pm:           '\\u00B1',\n  circ:         '\\u2218',\n  bigcirc:      '\\u25EF',\n  setminus:     '\\u2216',\n  cdot:         '\\u22C5',\n  ast:          '\\u2217',\n  times:        '\\u00D7',\n  star:         '\\u22C6',\n\n\n  // Relations\n  propto:       '\\u221D',\n  sqsubseteq:   '\\u2291',\n  sqsupseteq:   '\\u2292',\n  parallel:     '\\u2225',\n  mid:          '\\u2223',\n  dashv:        '\\u22A3',\n  vdash:        '\\u22A2',\n  leq:          '\\u2264',\n  le:           '\\u2264',\n  geq:          '\\u2265',\n  ge:           '\\u2265',\n  lt:           '\\u003C',\n  gt:           '\\u003E',\n  succ:         '\\u227B',\n  prec:         '\\u227A',\n  approx:       '\\u2248',\n  succeq:       '\\u2AB0',  // or '227C',\n  preceq:       '\\u2AAF',  // or '227D',\n  supset:       '\\u2283',\n  subset:       '\\u2282',\n  supseteq:     '\\u2287',\n  subseteq:     '\\u2286',\n  'in':         '\\u2208',\n  ni:           '\\u220B',\n  notin:        '\\u2209',\n  owns:         '\\u220B',\n  gg:           '\\u226B',\n  ll:           '\\u226A',\n  sim:          '\\u223C',\n  simeq:        '\\u2243',\n  perp:         '\\u22A5',\n  equiv:        '\\u2261',\n  asymp:        '\\u224D',\n  smile:        '\\u2323',\n  frown:        '\\u2322',\n  ne:           '\\u2260',\n  neq:          '\\u2260',\n  cong:         '\\u2245',\n  doteq:        '\\u2250',\n  bowtie:       '\\u22C8',\n  models:       '\\u22A8',\n\n  notChar:      '\\u29F8',\n\n\n  // Arrows\n  Leftrightarrow:     '\\u21D4',\n  Leftarrow:          '\\u21D0',\n  Rightarrow:         '\\u21D2',\n  leftrightarrow:     '\\u2194',\n  leftarrow:          '\\u2190',\n  gets:               '\\u2190',\n  rightarrow:         '\\u2192',\n  to:                ['\\u2192', {accent: false}],\n  mapsto:             '\\u21A6',\n  leftharpoonup:      '\\u21BC',\n  leftharpoondown:    '\\u21BD',\n  rightharpoonup:     '\\u21C0',\n  rightharpoondown:   '\\u21C1',\n  nearrow:            '\\u2197',\n  searrow:            '\\u2198',\n  nwarrow:            '\\u2196',\n  swarrow:            '\\u2199',\n  rightleftharpoons:  '\\u21CC',\n  hookrightarrow:     '\\u21AA',\n  hookleftarrow:      '\\u21A9',\n  longleftarrow:      '\\u27F5',\n  Longleftarrow:      '\\u27F8',\n  longrightarrow:     '\\u27F6',\n  Longrightarrow:     '\\u27F9',\n  Longleftrightarrow: '\\u27FA',\n  longleftrightarrow: '\\u27F7',\n  longmapsto:         '\\u27FC',\n\n\n  // Misc.\n  ldots:            '\\u2026',\n  cdots:            '\\u22EF',\n  vdots:            '\\u22EE',\n  ddots:            '\\u22F1',\n  dotsc:            '\\u2026',  // dots with commas\n  dotsb:            '\\u22EF',  // dots with binary ops and relations\n  dotsm:            '\\u22EF',  // dots with multiplication\n  dotsi:            '\\u22EF',  // dots with integrals\n  dotso:            '\\u2026',  // other dots\n\n  ldotp:            ['\\u002E', {texClass: TEXCLASS.PUNCT}],\n  cdotp:            ['\\u22C5', {texClass: TEXCLASS.PUNCT}],\n  colon:            ['\\u003A', {texClass: TEXCLASS.PUNCT}]\n});\n\n\n/**\n * Macros for special characters and identifiers.\n */\nnew sm.CharacterMap('mathchar7', ParseMethods.mathchar7, {\n  Gamma:        '\\u0393',\n  Delta:        '\\u0394',\n  Theta:        '\\u0398',\n  Lambda:       '\\u039B',\n  Xi:           '\\u039E',\n  Pi:           '\\u03A0',\n  Sigma:        '\\u03A3',\n  Upsilon:      '\\u03A5',\n  Phi:          '\\u03A6',\n  Psi:          '\\u03A8',\n  Omega:        '\\u03A9',\n\n  '_':          '\\u005F',\n  '#':          '\\u0023',\n  '$':          '\\u0024',\n  '%':          '\\u0025',\n  '&':          '\\u0026',\n  And:          '\\u0026'\n});\n\n\n/**\n * Macros for delimiters.\n */\nnew sm.DelimiterMap('delimiter', ParseMethods.delimiter, {\n  '(':                '(',\n  ')':                ')',\n  '[':                '[',\n  ']':                ']',\n  '<':                '\\u27E8',\n  '>':                '\\u27E9',\n  '\\\\lt':             '\\u27E8',\n  '\\\\gt':             '\\u27E9',\n  '/':                '/',\n  '|':                ['|', {texClass: TEXCLASS.ORD}],\n  '.':                '',\n  '\\\\\\\\':             '\\\\',\n  '\\\\lmoustache':     '\\u23B0',  // non-standard\n  '\\\\rmoustache':     '\\u23B1',  // non-standard\n  '\\\\lgroup':         '\\u27EE',  // non-standard\n  '\\\\rgroup':         '\\u27EF',  // non-standard\n  '\\\\arrowvert':      '\\u23D0',\n  '\\\\Arrowvert':      '\\u2016',\n  '\\\\bracevert':      '\\u23AA',  // non-standard\n  '\\\\Vert':           ['\\u2016', {texClass: TEXCLASS.ORD}],\n  '\\\\|':              ['\\u2016', {texClass: TEXCLASS.ORD}],\n  '\\\\vert':           ['|', {texClass: TEXCLASS.ORD}],\n  '\\\\uparrow':        '\\u2191',\n  '\\\\downarrow':      '\\u2193',\n  '\\\\updownarrow':    '\\u2195',\n  '\\\\Uparrow':        '\\u21D1',\n  '\\\\Downarrow':      '\\u21D3',\n  '\\\\Updownarrow':    '\\u21D5',\n  '\\\\backslash':      '\\\\',\n  '\\\\rangle':         '\\u27E9',\n  '\\\\langle':         '\\u27E8',\n  '\\\\rbrace':         '}',\n  '\\\\lbrace':         '{',\n  '\\\\}':              '}',\n  '\\\\{':              '{',\n  '\\\\rceil':          '\\u2309',\n  '\\\\lceil':          '\\u2308',\n  '\\\\rfloor':         '\\u230B',\n  '\\\\lfloor':         '\\u230A',\n  '\\\\lbrack':         '[',\n  '\\\\rbrack':         ']'\n});\n\n\n/**\n * Macros for LaTeX commands.\n */\nnew sm.CommandMap('macros', {\n  displaystyle:      ['SetStyle', 'D', true, 0],\n  textstyle:         ['SetStyle', 'T', false, 0],\n  scriptstyle:       ['SetStyle', 'S', false, 1],\n  scriptscriptstyle: ['SetStyle', 'SS', false, 2],\n\n  rm:                ['SetFont', TexConstant.Variant.NORMAL],\n  mit:               ['SetFont', TexConstant.Variant.ITALIC],\n  oldstyle:          ['SetFont', TexConstant.Variant.OLDSTYLE],\n  cal:               ['SetFont', TexConstant.Variant.CALLIGRAPHIC],\n  it:                ['SetFont', TexConstant.Variant.MATHITALIC], // needs special handling\n  bf:                ['SetFont', TexConstant.Variant.BOLD],\n  bbFont:            ['SetFont', TexConstant.Variant.DOUBLESTRUCK],\n  scr:               ['SetFont', TexConstant.Variant.SCRIPT],\n  frak:              ['SetFont', TexConstant.Variant.FRAKTUR],\n  sf:                ['SetFont', TexConstant.Variant.SANSSERIF],\n  tt:                ['SetFont', TexConstant.Variant.MONOSPACE],\n\n  mathrm:            ['MathFont', TexConstant.Variant.NORMAL],\n  mathup:            ['MathFont', TexConstant.Variant.NORMAL],\n  mathnormal:        ['MathFont', ''],\n  mathbf:            ['MathFont', TexConstant.Variant.BOLD],\n  mathbfup:          ['MathFont', TexConstant.Variant.BOLD],\n  mathit:            ['MathFont', TexConstant.Variant.MATHITALIC],\n  mathbfit:          ['MathFont', TexConstant.Variant.BOLDITALIC],\n  mathbb:            ['MathFont', TexConstant.Variant.DOUBLESTRUCK],\n  Bbb:               ['MathFont', TexConstant.Variant.DOUBLESTRUCK],\n  mathfrak:          ['MathFont', TexConstant.Variant.FRAKTUR],\n  mathbffrak:        ['MathFont', TexConstant.Variant.BOLDFRAKTUR],\n  mathscr:           ['MathFont', TexConstant.Variant.SCRIPT],\n  mathbfscr:         ['MathFont', TexConstant.Variant.BOLDSCRIPT],\n  mathsf:            ['MathFont', TexConstant.Variant.SANSSERIF],\n  mathsfup:          ['MathFont', TexConstant.Variant.SANSSERIF],\n  mathbfsf:          ['MathFont', TexConstant.Variant.BOLDSANSSERIF],\n  mathbfsfup:        ['MathFont', TexConstant.Variant.BOLDSANSSERIF],\n  mathsfit:          ['MathFont', TexConstant.Variant.SANSSERIFITALIC],\n  mathbfsfit:        ['MathFont', TexConstant.Variant.SANSSERIFBOLDITALIC],\n  mathtt:            ['MathFont', TexConstant.Variant.MONOSPACE],\n  mathcal:           ['MathFont', TexConstant.Variant.CALLIGRAPHIC],\n  mathbfcal:         ['MathFont', TexConstant.Variant.BOLDCALLIGRAPHIC],\n\n  symrm:             ['MathFont', TexConstant.Variant.NORMAL],\n  symup:             ['MathFont', TexConstant.Variant.NORMAL],\n  symnormal:         ['MathFont', ''],\n  symbf:             ['MathFont', TexConstant.Variant.BOLD],\n  symbfup:           ['MathFont', TexConstant.Variant.BOLD],\n  symit:             ['MathFont', TexConstant.Variant.ITALIC],\n  symbfit:           ['MathFont', TexConstant.Variant.BOLDITALIC],\n  symbb:             ['MathFont', TexConstant.Variant.DOUBLESTRUCK],\n  symfrak:           ['MathFont', TexConstant.Variant.FRAKTUR],\n  symbffrak:         ['MathFont', TexConstant.Variant.BOLDFRAKTUR],\n  symscr:            ['MathFont', TexConstant.Variant.SCRIPT],\n  symbfscr:          ['MathFont', TexConstant.Variant.BOLDSCRIPT],\n  symsf:             ['MathFont', TexConstant.Variant.SANSSERIF],\n  symsfup:           ['MathFont', TexConstant.Variant.SANSSERIF],\n  symbfsf:           ['MathFont', TexConstant.Variant.BOLDSANSSERIF],\n  symbfsfup:         ['MathFont', TexConstant.Variant.BOLDSANSSERIF],\n  symsfit:           ['MathFont', TexConstant.Variant.SANSSERIFITALIC],\n  symbfsfit:         ['MathFont', TexConstant.Variant.SANSSERIFBOLDITALIC],\n  symtt:             ['MathFont', TexConstant.Variant.MONOSPACE],\n  symcal:            ['MathFont', TexConstant.Variant.CALLIGRAPHIC],\n  symbfcal:          ['MathFont', TexConstant.Variant.BOLDCALLIGRAPHIC],\n\n  textrm:            ['HBox', null, TexConstant.Variant.NORMAL],\n  textup:            ['HBox', null, TexConstant.Variant.NORMAL],\n  textnormal:        ['HBox'],\n  textit:            ['HBox', null, TexConstant.Variant.ITALIC],\n  textbf:            ['HBox', null, TexConstant.Variant.BOLD],\n  textsf:            ['HBox', null, TexConstant.Variant.SANSSERIF],\n  texttt:            ['HBox', null, TexConstant.Variant.MONOSPACE],\n\n  tiny:              ['SetSize', 0.5],\n  Tiny:              ['SetSize', 0.6],  // non-standard\n  scriptsize:        ['SetSize', 0.7],\n  small:             ['SetSize', 0.85],\n  normalsize:        ['SetSize', 1.0],\n  large:             ['SetSize', 1.2],\n  Large:             ['SetSize', 1.44],\n  LARGE:             ['SetSize', 1.73],\n  huge:              ['SetSize', 2.07],\n  Huge:              ['SetSize', 2.49],\n\n  arcsin:             'NamedFn',\n  arccos:             'NamedFn',\n  arctan:             'NamedFn',\n  arg:                'NamedFn',\n  cos:                'NamedFn',\n  cosh:               'NamedFn',\n  cot:                'NamedFn',\n  coth:               'NamedFn',\n  csc:                'NamedFn',\n  deg:                'NamedFn',\n  det:                'NamedOp',\n  dim:                'NamedFn',\n  exp:                'NamedFn',\n  gcd:                'NamedOp',\n  hom:                'NamedFn',\n  inf:                'NamedOp',\n  ker:                'NamedFn',\n  lg:                 'NamedFn',\n  lim:                'NamedOp',\n  liminf:            ['NamedOp', 'lim&thinsp;inf'],\n  limsup:            ['NamedOp', 'lim&thinsp;sup'],\n  ln:                 'NamedFn',\n  log:                'NamedFn',\n  max:                'NamedOp',\n  min:                'NamedOp',\n  Pr:                 'NamedOp',\n  sec:                'NamedFn',\n  sin:                'NamedFn',\n  sinh:               'NamedFn',\n  sup:                'NamedOp',\n  tan:                'NamedFn',\n  tanh:               'NamedFn',\n\n  limits:            ['Limits', 1],\n  nolimits:          ['Limits', 0],\n\n  overline:            ['UnderOver', '2015'],\n  underline:           ['UnderOver', '2015'],\n  overbrace:           ['UnderOver', '23DE', 1],\n  underbrace:          ['UnderOver', '23DF', 1],\n  overparen:           ['UnderOver', '23DC'],\n  underparen:          ['UnderOver', '23DD'],\n  overrightarrow:      ['UnderOver', '2192'],\n  underrightarrow:     ['UnderOver', '2192'],\n  overleftarrow:       ['UnderOver', '2190'],\n  underleftarrow:      ['UnderOver', '2190'],\n  overleftrightarrow:  ['UnderOver', '2194'],\n  underleftrightarrow: ['UnderOver', '2194'],\n\n  overset:            'Overset',\n  underset:           'Underset',\n  overunderset:       'Overunderset',\n  stackrel:           ['Macro', '\\\\mathrel{\\\\mathop{#2}\\\\limits^{#1}}', 2],\n  stackbin:           ['Macro', '\\\\mathbin{\\\\mathop{#2}\\\\limits^{#1}}', 2],\n\n  over:               'Over',\n  overwithdelims:     'Over',\n  atop:               'Over',\n  atopwithdelims:     'Over',\n  above:              'Over',\n  abovewithdelims:    'Over',\n  brace:             ['Over', '{', '}'],\n  brack:             ['Over', '[', ']'],\n  choose:            ['Over', '(', ')'],\n\n  frac:               'Frac',\n  sqrt:               'Sqrt',\n  root:               'Root',\n  uproot:            ['MoveRoot', 'upRoot'],\n  leftroot:          ['MoveRoot', 'leftRoot'],\n\n  left:               'LeftRight',\n  right:              'LeftRight',\n  middle:             'LeftRight',\n\n  llap:               'Lap',\n  rlap:               'Lap',\n  raise:              'RaiseLower',\n  lower:              'RaiseLower',\n  moveleft:           'MoveLeftRight',\n  moveright:          'MoveLeftRight',\n\n  ',':               ['Spacer', MATHSPACE.thinmathspace],\n  ':':               ['Spacer', MATHSPACE.mediummathspace],\n  '>':               ['Spacer', MATHSPACE.mediummathspace],\n  ';':               ['Spacer', MATHSPACE.thickmathspace],\n  '!':               ['Spacer', MATHSPACE.negativethinmathspace],\n  enspace:           ['Spacer', .5],\n  quad:              ['Spacer', 1],\n  qquad:             ['Spacer', 2],\n  thinspace:         ['Spacer', MATHSPACE.thinmathspace],\n  negthinspace:      ['Spacer', MATHSPACE.negativethinmathspace],\n\n  hskip:              'Hskip',\n  hspace:             'Hskip',\n  kern:               'Hskip',\n  mskip:              'Hskip',\n  mspace:             'Hskip',\n  mkern:              'Hskip',\n  rule:               'rule',\n  Rule:              ['Rule'],\n  Space:             ['Rule', 'blank'],\n  nonscript:          'Nonscript',\n\n  big:               ['MakeBig', TEXCLASS.ORD, 0.85],\n  Big:               ['MakeBig', TEXCLASS.ORD, 1.15],\n  bigg:              ['MakeBig', TEXCLASS.ORD, 1.45],\n  Bigg:              ['MakeBig', TEXCLASS.ORD, 1.75],\n  bigl:              ['MakeBig', TEXCLASS.OPEN, 0.85],\n  Bigl:              ['MakeBig', TEXCLASS.OPEN, 1.15],\n  biggl:             ['MakeBig', TEXCLASS.OPEN, 1.45],\n  Biggl:             ['MakeBig', TEXCLASS.OPEN, 1.75],\n  bigr:              ['MakeBig', TEXCLASS.CLOSE, 0.85],\n  Bigr:              ['MakeBig', TEXCLASS.CLOSE, 1.15],\n  biggr:             ['MakeBig', TEXCLASS.CLOSE, 1.45],\n  Biggr:             ['MakeBig', TEXCLASS.CLOSE, 1.75],\n  bigm:              ['MakeBig', TEXCLASS.REL, 0.85],\n  Bigm:              ['MakeBig', TEXCLASS.REL, 1.15],\n  biggm:             ['MakeBig', TEXCLASS.REL, 1.45],\n  Biggm:             ['MakeBig', TEXCLASS.REL, 1.75],\n\n  mathord:           ['TeXAtom', TEXCLASS.ORD],\n  mathop:            ['TeXAtom', TEXCLASS.OP],\n  mathopen:          ['TeXAtom', TEXCLASS.OPEN],\n  mathclose:         ['TeXAtom', TEXCLASS.CLOSE],\n  mathbin:           ['TeXAtom', TEXCLASS.BIN],\n  mathrel:           ['TeXAtom', TEXCLASS.REL],\n  mathpunct:         ['TeXAtom', TEXCLASS.PUNCT],\n  mathinner:         ['TeXAtom', TEXCLASS.INNER],\n\n  vcenter:           ['TeXAtom', TEXCLASS.VCENTER],\n\n  buildrel:           'BuildRel',\n\n  hbox:               ['HBox', 0],\n  text:               'HBox',\n  mbox:               ['HBox', 0],\n  fbox:               'FBox',\n  boxed:              ['Macro', '\\\\fbox{$\\\\displaystyle{#1}$}', 1],\n  framebox:           'FrameBox',\n\n  strut:              'Strut',\n  mathstrut:         ['Macro', '\\\\vphantom{(}'],\n  phantom:            'Phantom',\n  vphantom:          ['Phantom', 1, 0],\n  hphantom:          ['Phantom', 0, 1],\n  smash:              'Smash',\n\n  acute:             ['Accent', '00B4'],  // or 0301 or 02CA\n  grave:             ['Accent', '0060'],  // or 0300 or 02CB\n  ddot:              ['Accent', '00A8'],  // or 0308\n  tilde:             ['Accent', '007E'],  // or 0303 or 02DC\n  bar:               ['Accent', '00AF'],  // or 0304 or 02C9\n  breve:             ['Accent', '02D8'],  // or 0306\n  check:             ['Accent', '02C7'],  // or 030C\n  hat:               ['Accent', '005E'],  // or 0302 or 02C6\n  vec:               ['Accent', '2192'],  // or 20D7\n  dot:               ['Accent', '02D9'],  // or 0307\n  widetilde:         ['Accent', '007E', 1], // or 0303 or 02DC\n  widehat:           ['Accent', '005E', 1], // or 0302 or 02C6\n\n  matrix:             'Matrix',\n  array:              'Matrix',\n  pmatrix:           ['Matrix', '(', ')'],\n  cases:             ['Matrix', '{', '', 'left left', null, '.1em', null,\n                      true],\n  eqalign:           ['Matrix', null, null, 'right left',\n                      em(MATHSPACE.thickmathspace), '.5em', 'D'],\n  displaylines:      ['Matrix', null, null, 'center', null, '.5em', 'D'],\n  cr:                 'Cr',\n  '\\\\':               'CrLaTeX',\n  newline:           ['CrLaTeX', true],\n  hline:             ['HLine', 'solid'],\n  hdashline:         ['HLine', 'dashed'],\n  //      noalign:            'HandleNoAlign',\n  eqalignno:         ['Matrix', null, null, 'right left',\n                      em(MATHSPACE.thickmathspace), '.5em', 'D', null,\n                      'right'],\n  leqalignno:        ['Matrix', null, null, 'right left',\n                      em(MATHSPACE.thickmathspace), '.5em', 'D', null,\n                      'left'],\n  hfill:              'HFill',\n  hfil:               'HFill',   // \\hfil treated as \\hfill for now\n  hfilll:             'HFill',   // \\hfilll treated as \\hfill for now\n\n  //  TeX substitution macros\n  bmod:              ['Macro', '\\\\mmlToken{mo}[lspace=\"thickmathspace\"' +\n                      ' rspace=\"thickmathspace\"]{mod}'],\n  pmod:              ['Macro', '\\\\pod{\\\\mmlToken{mi}{mod}\\\\kern 6mu #1}', 1],\n  mod:               ['Macro', '\\\\mathchoice{\\\\kern18mu}{\\\\kern12mu}' +\n                      '{\\\\kern12mu}{\\\\kern12mu}\\\\mmlToken{mi}{mod}\\\\,\\\\,#1',\n                      1],\n  pod:               ['Macro', '\\\\mathchoice{\\\\kern18mu}{\\\\kern8mu}' +\n                      '{\\\\kern8mu}{\\\\kern8mu}(#1)', 1],\n  iff:               ['Macro', '\\\\;\\\\Longleftrightarrow\\\\;'],\n  skew:              ['Macro', '{{#2{#3\\\\mkern#1mu}\\\\mkern-#1mu}{}}', 3],\n\n  pmb:               ['Macro', '\\\\rlap{#1}\\\\kern1px{#1}', 1],\n  TeX:               ['Macro', 'T\\\\kern-.14em\\\\lower.5ex{E}\\\\kern-.115em X'],\n  LaTeX:             ['Macro', 'L\\\\kern-.325em\\\\raise.21em' +\n                      '{\\\\scriptstyle{A}}\\\\kern-.17em\\\\TeX'],\n  ' ':               ['Macro', '\\\\text{ }'],\n\n  //  Specially handled\n  not:                'Not',\n  dots:               'Dots',\n  space:              'Tilde',\n  '\\u00A0':           'Tilde',\n\n\n  //  LaTeX\n  begin:              'BeginEnd',\n  end:                'BeginEnd',\n\n  label:              'HandleLabel',\n  ref:                'HandleRef',\n  nonumber:           'HandleNoTag',\n\n  // Internal use:\n  mathchoice:         'MathChoice',\n  mmlToken:           'MmlToken'\n}, BaseMethods);\n\n\n/**\n * Macros for LaTeX environments.\n */\nnew sm.EnvironmentMap('environment', ParseMethods.environment, {\n  array:         ['AlignedArray'],\n  equation:      ['Equation', null, true],\n  eqnarray:      ['EqnArray', null, true, true, 'rcl',\n                  ParseUtil.cols(0, MATHSPACE.thickmathspace), '.5em']\n}, BaseMethods);\n\n\n/**\n * Mapping for negated operators.\n */\nnew sm.CharacterMap('not_remap', null, {\n  '\\u2190': '\\u219A',\n  '\\u2192': '\\u219B',\n  '\\u2194': '\\u21AE',\n  '\\u21D0': '\\u21CD',\n  '\\u21D2': '\\u21CF',\n  '\\u21D4': '\\u21CE',\n  '\\u2208': '\\u2209',\n  '\\u220B': '\\u220C',\n  '\\u2223': '\\u2224',\n  '\\u2225': '\\u2226',\n  '\\u223C': '\\u2241',\n  '\\u007E': '\\u2241',\n  '\\u2243': '\\u2244',\n  '\\u2245': '\\u2247',\n  '\\u2248': '\\u2249',\n  '\\u224D': '\\u226D',\n  '\\u003D': '\\u2260',\n  '\\u2261': '\\u2262',\n  '\\u003C': '\\u226E',\n  '\\u003E': '\\u226F',\n  '\\u2264': '\\u2270',\n  '\\u2265': '\\u2271',\n  '\\u2272': '\\u2274',\n  '\\u2273': '\\u2275',\n  '\\u2276': '\\u2278',\n  '\\u2277': '\\u2279',\n  '\\u227A': '\\u2280',\n  '\\u227B': '\\u2281',\n  '\\u2282': '\\u2284',\n  '\\u2283': '\\u2285',\n  '\\u2286': '\\u2288',\n  '\\u2287': '\\u2289',\n  '\\u22A2': '\\u22AC',\n  '\\u22A8': '\\u22AD',\n  '\\u22A9': '\\u22AE',\n  '\\u22AB': '\\u22AF',\n  '\\u227C': '\\u22E0',\n  '\\u227D': '\\u22E1',\n  '\\u2291': '\\u22E2',\n  '\\u2292': '\\u22E3',\n  '\\u22B2': '\\u22EA',\n  '\\u22B3': '\\u22EB',\n  '\\u22B4': '\\u22EC',\n  '\\u22B5': '\\u22ED',\n  '\\u2203': '\\u2204'\n});\n", "/*************************************************************\n *\n *  Copyright (c) 2018-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n\n/**\n * @fileoverview Configuration for the Base LaTeX parser.\n *\n * <AUTHOR> (Volker Sorge)\n */\n\nimport {Configuration} from '../Configuration.js';\nimport {MapHandler} from '../MapHandler.js';\nimport TexError from '../TexError.js';\nimport NodeUtil from '../NodeUtil.js';\nimport TexParser from '../TexParser.js';\nimport {CharacterMap} from '../SymbolMap.js';\nimport * as bitem from './BaseItems.js';\nimport {AbstractTags} from '../Tags.js';\nimport './BaseMappings.js';\nimport {getRange} from '../../../core/MmlTree/OperatorDictionary.js';\nimport {MmlNode} from '../../../core/MmlTree/MmlNode.js';\nimport ParseOptions from '../ParseOptions.js';\n\n/**\n * Remapping some ASCII characters to their Unicode operator equivalent.\n */\nnew CharacterMap('remap', null, {\n  '-':   '\\u2212',\n  '*':   '\\u2217',\n  '`':   '\\u2018'   // map ` to back quote\n});\n\n\n/**\n * Default handling of characters (as <mo> elements).\n * @param {TexParser} parser The calling parser.\n * @param {string} char The character to parse.\n */\nexport function Other(parser: TexParser, char: string) {\n  const font = parser.stack.env['font'];\n  let def = font ?\n    // @test Other Font\n    {mathvariant: parser.stack.env['font']} : {};\n  const remap = (MapHandler.getMap('remap') as CharacterMap).lookup(char);\n  const range = getRange(char);\n  const type = (range ? range[3] : 'mo');\n  // @test Other\n  // @test Other Remap\n  let mo = parser.create('token', type, def, (remap ? remap.char : char));\n  range[4] && mo.attributes.set('mathvariant', range[4]);\n  if (type === 'mo') {\n    NodeUtil.setProperty(mo, 'fixStretchy', true);\n    parser.configuration.addNode('fixStretchy', mo);\n  }\n  parser.Push(mo);\n}\n\n\n/**\n * Handle undefined control sequence.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The name of the control sequence.\n */\nfunction csUndefined(_parser: TexParser, name: string) {\n  // @test Undefined-CS\n  throw new TexError('UndefinedControlSequence',\n                      'Undefined control sequence %1', '\\\\' + name);\n}\n\n\n/**\n * Handle undefined environments.\n * @param {TexParser} parser The calling parser.\n * @param {string} name The name of the control sequence.\n */\nfunction envUndefined(_parser: TexParser, env: string) {\n  // @test Undefined-Env\n  throw new TexError('UnknownEnv', 'Unknown environment \\'%1\\'', env);\n}\n\n/**\n * Filter for removing spacing following \\nonscript\n * @param{ParseOptions} data The active tex parser.\n */\nfunction filterNonscript({data}: {data: ParseOptions}) {\n  for (const mml of data.getList('nonscript')) {\n    //\n    //  This is the list of mspace elements or mrow > mstyle > mspace\n    //    that followed \\nonscript macros to be tested for removal.\n    //\n    if (mml.attributes.get('scriptlevel') > 0) {\n      //\n      //  The mspace needs to be removed, since we are in a script style.\n      //  Remove it from the DOM and from the list of mspace elements.\n      //\n      const parent = mml.parent;\n      parent.childNodes.splice(parent.childIndex(mml), 1);\n      data.removeFromList(mml.kind, [mml]);\n      //\n      //  If it is an mrow > mstyle > mspace, then we have just\n      //    removed the mrow from its list, and must remove\n      //    the mstyle and mspace from their lists as well.\n      //\n      if (mml.isKind('mrow')) {\n        const mstyle = mml.childNodes[0] as MmlNode;\n        data.removeFromList('mstyle', [mstyle]);\n        data.removeFromList('mspace', mstyle.childNodes[0].childNodes as MmlNode[]);\n      }\n    } else if (mml.isKind('mrow')) {\n      //\n      // This is an mrow > mstyle > mspace  but we're not in a script\n      //   style, so remove the mrow that we had added in the NonscriptItem.\n      //\n      mml.parent.replaceChild(mml.childNodes[0], mml);\n      data.removeFromList('mrow', [mml]);\n    }\n  }\n}\n\n\n/**\n * @constructor\n * @extends {AbstractTags}\n */\nexport class BaseTags extends AbstractTags { }\n\n\n/**\n * The base configuration.\n * @type {Configuration}\n */\nexport const BaseConfiguration: Configuration = Configuration.create(\n  'base',  {\n    handler: {\n      character: ['command', 'special', 'letter', 'digit'],\n      delimiter: ['delimiter'],\n      // Note, that the position of the delimiters here is important!\n      macro: ['delimiter', 'macros', 'mathchar0mi', 'mathchar0mo', 'mathchar7'],\n      environment: ['environment']\n    },\n    fallback: {\n      character: Other,\n      macro: csUndefined,\n      environment: envUndefined\n    },\n    items: {\n      // BaseItems\n      [bitem.StartItem.prototype.kind]: bitem.StartItem,\n      [bitem.StopItem.prototype.kind]: bitem.StopItem,\n      [bitem.OpenItem.prototype.kind]: bitem.OpenItem,\n      [bitem.CloseItem.prototype.kind]: bitem.CloseItem,\n      [bitem.PrimeItem.prototype.kind]: bitem.PrimeItem,\n      [bitem.SubsupItem.prototype.kind]: bitem.SubsupItem,\n      [bitem.OverItem.prototype.kind]: bitem.OverItem,\n      [bitem.LeftItem.prototype.kind]: bitem.LeftItem,\n      [bitem.Middle.prototype.kind]: bitem.Middle,\n      [bitem.RightItem.prototype.kind]: bitem.RightItem,\n      [bitem.BeginItem.prototype.kind]: bitem.BeginItem,\n      [bitem.EndItem.prototype.kind]: bitem.EndItem,\n      [bitem.StyleItem.prototype.kind]: bitem.StyleItem,\n      [bitem.PositionItem.prototype.kind]: bitem.PositionItem,\n      [bitem.CellItem.prototype.kind]: bitem.CellItem,\n      [bitem.MmlItem.prototype.kind]: bitem.MmlItem,\n      [bitem.FnItem.prototype.kind]: bitem.FnItem,\n      [bitem.NotItem.prototype.kind]: bitem.NotItem,\n      [bitem.NonscriptItem.prototype.kind]: bitem.NonscriptItem,\n      [bitem.DotsItem.prototype.kind]: bitem.DotsItem,\n      [bitem.ArrayItem.prototype.kind]: bitem.ArrayItem,\n      [bitem.EqnArrayItem.prototype.kind]: bitem.EqnArrayItem,\n      [bitem.EquationItem.prototype.kind]: bitem.EquationItem\n    },\n    options: {\n      maxMacros: 1000,\n      baseURL: (typeof(document) === 'undefined' ||\n                document.getElementsByTagName('base').length === 0) ?\n                '' : String(document.location).replace(/#.*$/, '')\n    },\n    tags: {\n      base: BaseTags\n    },\n    postprocessors: [[filterNonscript, -4]]\n  }\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,QAAA,eAAA;AACA,QAAA,UAAA;AAMA,QAAU;AAAV,KAAA,SAAUA,WAAQ;AAEhB,UAAM,QAA8B,oBAAI,IAAI;QAC1C,CAAC,UAAU,IAAI;QACf,CAAC,QAAQ,IAAI;QACb,CAAC,cAAc,IAAI;QACnB,CAAC,YAAY,IAAI;QACjB,CAAC,iBAAiB,IAAI;QACtB,CAAC,aAAa,IAAI;QAClB,CAAC,eAAe,IAAI;QACpB,CAAC,cAAc,IAAI;QACnB,CAAC,cAAc,IAAI;QACnB,CAAC,QAAQ,IAAI;QACb,CAAC,SAAS,IAAI;OACf;AAQD,eAAgB,aAAa,MAAY;AACvC,eAAO,OAAO,cAAc,SAAS,MAAM,EAAE,CAAC;MAChD;AAFgB,MAAAA,UAAA,eAAY;AAU5B,eAAgB,YAAY,MAAa;AACvC,eAAQ,KAAK;MACf;AAFgB,MAAAA,UAAA,cAAW;AAU3B,eAAgB,QAAQ,MAAc;AACpC,eAAO,KAAK,QAAO;MACrB;AAFgB,MAAAA,UAAA,UAAO;AAUvB,eAAgB,eAAe,MAAe,UAAmB;;;AAC/D,mBAAkB,aAAA,SAAA,QAAQ,GAAA,eAAA,WAAA,KAAA,GAAA,CAAA,aAAA,MAAA,eAAA,WAAA,KAAA,GAAE;AAAvB,gBAAI,QAAK,aAAA;AACZ,iBAAK,YAAY,KAAK;;;;;;;;;;;MAE1B;AAJgB,MAAAA,UAAA,iBAAc;AAa9B,eAAgB,aAAa,MAAe,WAAmB,OAAW;AACxE,aAAK,WAAW,IAAI,WAAW,KAAK;MACtC;AAFgB,MAAAA,UAAA,eAAY;AAW5B,eAAgB,YAAY,MAAe,UAAkB,OAAW;AACtE,aAAK,YAAY,UAAU,KAAK;MAClC;AAFgB,MAAAA,UAAA,cAAW;AAU3B,eAAgB,cAAc,MAAe,YAAwB;;;AACnE,mBAAmB,KAAA,SAAA,OAAO,KAAK,UAAU,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAvC,gBAAM,SAAI,GAAA;AACb,gBAAI,QAAQ,WAAW,MAAI;AAC3B,gBAAI,WAAS,YAAY;AACvB,mBAAK,WAAY;AACjB,mBAAK,YAAY,QAAM,KAAK;uBACnB,WAAS,iBAAiB;AACnC,mBAAK,YAAY,iBAAiB,KAAK;AACvC,kBAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC9C,qBAAK,WAAW,IAAI,iBAAiB,KAAK;;uBAEnC,WAAS,YAAY;uBAErB,MAAM,IAAI,MAAI,GAAG;AAC1B,mBAAK,YAAY,QAAM,KAAK;mBACvB;AACL,mBAAK,WAAW,IAAI,QAAM,KAAK;;;;;;;;;;;;MAGrC;AAnBgB,MAAAA,UAAA,gBAAa;AA4B7B,eAAgB,YAAY,MAAe,UAAgB;AACzD,eAAO,KAAK,YAAY,QAAQ;MAClC;AAFgB,MAAAA,UAAA,cAAW;AAW3B,eAAgB,aAAa,MAAe,MAAY;AACtD,eAAO,KAAK,WAAW,IAAI,IAAI;MACjC;AAFgB,MAAAA,UAAA,eAAY;AAU5B,eAAgB,iBAAiB,MAAa;AAAE,YAAA,aAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAuB;AAAvB,qBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC9C,aAAK,eAAc,MAAnB,MAAI,cAAA,CAAA,GAAA,OAAmB,UAAU,GAAA,KAAA,CAAA;MACnC;AAFgB,MAAAA,UAAA,mBAAgB;AAWhC,eAAgB,WAAW,MAAe,UAAgB;AACxD,eAAQ,KAAK,WAAW,QAAQ;MAClC;AAFgB,MAAAA,UAAA,aAAU;AAW1B,eAAgB,SAAS,MAAe,UAAkB,OAAc;AACtE,YAAI,WAAW,KAAK;AACpB,iBAAS,QAAQ,IAAI;AACrB,YAAI,OAAO;AACT,gBAAM,SAAS;;MAEnB;AANgB,MAAAA,UAAA,WAAQ;AAcxB,eAAgB,aAAa,SAAkB,SAAgB;AAC7D,YAAI,WAAW,QAAQ;AACvB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,mBAAS,SAAS,GAAG,SAAS,CAAC,CAAC;;MAEpC;AALgB,MAAAA,UAAA,eAAY;AAa5B,eAAgB,eAAe,SAAkB,SAAgB;AAC/D,gBAAQ,aAAa,QAAQ;AAC7B,sBAAc,SAAS,QAAQ,iBAAgB,CAAE;MACnD;AAHgB,MAAAA,UAAA,iBAAc;AAY9B,eAAgB,OAAO,MAAe,MAAY;AAChD,eAAO,KAAK,OAAO,IAAI;MACzB;AAFgB,MAAAA,UAAA,SAAM;AAUtB,eAAgB,cAAc,MAAa;AACzC,eAAO,KAAK;MACd;AAFgB,MAAAA,UAAA,gBAAa;AAU7B,eAAgB,YAAY,MAAa;AACvC,eAAO,KAAK;MACd;AAFgB,MAAAA,UAAA,cAAW;AAU3B,eAAgB,UAAU,MAAa;AACrC,eAAO,KAAK,OAAM;MACpB;AAFgB,MAAAA,UAAA,YAAS;AAUzB,eAAgB,OAAO,MAAS;AAC9B,eAAO,gBAAgB,aAAA,mBAAmB,gBAAgB,aAAA;MAC5D;AAFgB,MAAAA,UAAA,SAAM;AAUtB,eAAgB,WAAW,MAAa;AACtC,eAAO,KAAK;MACd;AAFgB,MAAAA,UAAA,aAAU;AAW1B,eAAgB,QAAQ,MAAa;;AACnC,YAAI,CAAC,OAAO,MAAM,IAAI,GAAG;AACvB,iBAAO;;AAET,YAAI,KAAK;AACT,YAAI,QAAQ,GAAG,SAAQ;;AACvB,mBAAiB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAAnB,gBAAI,OAAI,UAAA;AACX,gBAAI,SAAS,QAAA,MAAM,QAAQ,IAAI,EAAE,GAAG,QAAO,CAAE;AAC7C,gBAAI,QAAQ;AACV,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAbgB,MAAAA,UAAA,UAAO;IAezB,GAhRU,aAAA,WAAQ,CAAA,EAAA;AAkRlB,YAAA,UAAe;;;;;;;;;ACxRf,QAAA,WAAA,WAAA;AAuDE,eAAAC,UAAmB,IAAY,SAAe;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAiB;AAAjB,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAA7B,aAAA,KAAA;AACjB,aAAK,UAAUA,UAAS,cAAc,SAAS,IAAI;MACrD;AAxCe,MAAAA,UAAA,gBAAf,SAA6B,KAAa,MAAc;AACtD,YAAI,QAAQ,IAAI,MAAMA,UAAS,OAAO;AACtC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC/C,cAAI,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC;AAEzB,cAAI,KAAK,OAAO,KAAK,KAAK;AACxB,kBAAM,CAAC,IAAI,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;AAC1C,gBAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AAChC,oBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,SAAQ;;qBAErB,MAAM,KAAK;AACpB,gBAAI,MAAM,CAAC,EAAE,OAAO,CAAC;AACrB,gBAAI,KAAK,OAAO,KAAK,KAAK;AACxB,oBAAM,CAAC,IAAI,KAAK,SAAS,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC;AACzE,kBAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AAChC,sBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,SAAQ;;mBAEzB;AACL,kBAAI,QAAQ,MAAM,CAAC,EAAE,MAAM,6BAA6B;AACxD,kBAAI,OAAO;AAET,sBAAM,CAAC,IAAI,MAAM,MAAM,CAAC;;;;AAI9B,cAAI,MAAM,CAAC,KAAK,MAAM;AACpB,kBAAM,CAAC,IAAI;;;AAGf,eAAO,MAAM,KAAK,EAAE;MACtB;AA7Ce,MAAAA,UAAA,UACb;AAwDJ,aAAAA;MA3DA;sBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDrB,QAAA,eAAA;AAIA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,gBAAA;AAIA,QAAU;AAAV,KAAA,SAAUC,YAAS;AAGjB,UAAM,YAAY;AAClB,UAAM,YAAY;AAElB,UAAM,aAAwD;QAC5D,MAAM,SAAA,GAAC;AAAI,iBAAA;QAAA;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI;QAAJ;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI;QAAJ;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI;QAAJ;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI,YAAY;QAAhB;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI;QAAJ;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI,YAAY;QAAhB;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI,YAAY;QAAhB;QACX,MAAM,SAAA,GAAC;AAAI,iBAAA,IAAI;QAAJ;;AAEb,UAAM,MAAM;AACZ,UAAM,OAAO;AACb,UAAM,WAAW,OAAO,UAAU,MAAM,SAAS,OAAO,OAAO;AAC/D,UAAM,YAAY,OAAO,UAAU,MAAM,SAAS,OAAO,IAAI;AAW7D,eAAgB,WACd,KAAa,MAAqB;AAArB,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAqB;AAChC,YAAI,QAAQ,IAAI,MAAM,OAAO,YAAY,QAAQ;AACjD,eAAO,QACL,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ,KAAK,GAAG,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,MAAM,CAAC,IACjE,CAAC,MAAM,MAAM,CAAC;MACpB;AANgB,MAAAA,WAAA,aAAU;AAc1B,eAAS,UAAU,IAA+C;YAA/C,KAAA,OAAA,IAAA,CAAA,GAAC,QAAK,GAAA,CAAA,GAAEC,QAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACrC,YAAIA,UAAS,MAAM;AACjB,iBAAO,CAAC,OAAOA,OAAM,MAAM;;AAE7B,YAAI,KAAK,GAAG,WAAWA,KAAI,EAAE,WAAW,SAAS,GAAG,CAAC,CAAC;AACtD,eAAO,CAAC,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,MAAM;MACvC;AAQA,eAAgB,SAAS,KAAW;AAC9B,YAAA,KAAA,OAAgB,WAAW,GAAG,GAAC,CAAA,GAA9B,QAAK,GAAA,CAAA,GAAEA,QAAI,GAAA,CAAA;AAChB,YAAI,IAAI,WAAW,SAAS,GAAG;AAC/B,YAAI,OAAO,WAAWA,KAAI;AAC1B,eAAO,OAAO,KAAK,CAAC,IAAI;MAC1B;AALgB,MAAAD,WAAA,WAAQ;AAaxB,eAAgB,GAAG,GAAS;AAC1B,YAAI,KAAK,IAAI,CAAC,IAAI,MAAO;AACvB,iBAAO;;AAET,eAAO,EAAE,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAI;MAC9C;AALgB,MAAAA,WAAA,KAAE;AAalB,eAAgB,OAAI;AAAC,YAAA,IAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,YAAA,EAAA,IAAA,UAAA,EAAA;;AACnB,eAAO,EAAE,IAAI,SAAA,GAAC;AAAI,iBAAA,GAAG,CAAC;QAAJ,CAAK,EAAE,KAAK,GAAG;MACnC;AAFgB,MAAAA,WAAA,OAAI;AAapB,eAAgB,OAAO,eAA6B,MAAc,KAC3C,OAAe,KAAkB,OAAkB;AAApC,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAgB;AAAE,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAkB;AAExE,YAAI,KAAK,cAAc;AACvB,YAAI,OAAO,GAAG,OAAO,QAAQ,QAAQ,CAAA,GAChB,EAAC,MAAY,OAAc,UAAU,aAAA,SAAS,MAAK,CAAC;AACzE,YAAI;AACJ,YAAI,KAAK;AACP,eAAK,IAAI,eAAA,QAAU,OAAO,MAAM,MAAM,MAAM,cAAc,OAAO,MAAM,KAAK,aAAa,EAAE,IAAG;eACzF;AACL,cAAI,WAAW,GAAG,OAAO,QAAQ,IAAI;AACrC,eAAK,GAAG,OAAO,QAAQ,MAAM,CAAA,GACd,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,MAAM,UAAU,aAAA,SAAS,KAAI,GACtE,QAAQ;;AAEzB,sBAAA,QAAS,eAAe,MAAM,CAAC,IAAI,GAAG,CAAC;AACvC,YAAI,KAAK;AACP,eAAK,IAAI,eAAA,QAAU,OAAO,MAAM,MAAM,OAAO,cAAc,OAAO,MAAM,KAAK,aAAa,EAAE,IAAG;eAC1F;AACL,cAAI,YAAY,GAAG,OAAO,QAAQ,KAAK;AACvC,eAAK,GAAG,OAAO,QAAQ,MAAM,CAAA,GACd,EAAC,OAAO,MAAM,UAAU,MAAM,WAAW,MAAM,UAAU,aAAA,SAAS,MAAK,GACvE,SAAS;;AAE1B,iBAAS,GAAG,WAAW,IAAI,aAAa,KAAK;AAC7C,sBAAA,QAAS,eAAe,MAAM,CAAC,EAAE,CAAC;AAClC,eAAO;MACT;AA3BgB,MAAAA,WAAA,SAAM;AAsCtB,eAAgB,WAAW,eAA6B,MAC7B,KAAc,OAAa;AAEpD,YAAI,OAAO,cAAc,YAAY,OAAO,QAC1C,QAAQ,CAAA,GAAI,EAAC,MAAY,OAAc,UAAU,aAAA,SAAS,IAAG,CAAC;AAChE,YAAI,MAAM;AACR,wBAAA,QAAS,eAAe,MAAM,CAAC,YAAY,eAAe,MAAM,GAAG,CAAC,CAAC;;AAEvE,YAAI,cAAA,QAAS,OAAO,KAAK,MAAM,GAAG;AAChC,wBAAA,QAAS,eAAe,MAAM,cAAA,QAAS,YAAY,GAAG,CAAC;eAClD;AACL,wBAAA,QAAS,eAAe,MAAM,CAAC,GAAG,CAAC;;AAErC,YAAI,OAAO;AACT,wBAAA,QAAS,eAAe,MAAM,CAAC,YAAY,eAAe,OAAO,GAAG,CAAC,CAAC;;AAExE,eAAO;MACT;AAjBgB,MAAAA,WAAA,aAAU;AA6B1B,eAAgB,YAAY,eAA6B,OAC7B,MAAY;AACtC,YAAI,UAAU,OAAO,UAAU,KAAK;AAClC,kBAAQ,OAAO;;AAEjB,YAAI,IAAI,YAAY,OAAO,MAAM,QAAQ;AACzC,YAAI,IAAI,WAAW,OAAO,MAAM,QAAQ;AACxC,eAAO,IAAI,eAAA,QAAU,iBAAiB,IAAI,IAAI,IAAI,GAAG,CAAA,GAAI,aAAa,EAAE,IAAG;MAC7E;AARgB,MAAAA,WAAA,cAAW;AAmB3B,eAAgB,aAAa,eAA6B,OAAgB;AACxE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAI,QAAQ,MAAM,CAAC;AACnB,cAAI,UAAU,CAAC,cAAA,QAAS,OAAO,OAAO,QAAQ,MAC/B,CAAC,cAAA,QAAS,OAAO,OAAO,SAAS,KAChC,cAAA,QAAS,YAAY,KAAK,EAAE,CAAC,KAC7B,cAAA,QAAS,YAAY,cAAA,QAAS,YAAY,KAAK,EAAE,CAAC,CAAC,EAAE,UAAW;AAC9E,gBAAI,cAAA,QAAS,cAAc,KAAK,KAC3B,cAAA,QAAS,OAAO,OAAO,SAAS,KAAK,cAAA,QAAS,YAAY,KAAK,MAAM,aAAA,SAAS,KAAM;AACvF,kBAAI,KAAK,cAAc,YAAY,OAAO,QAAQ,IAAI;AACtD,oBAAM,QAAQ,EAAE;;AAElB;;;MAGN;AAfgB,MAAAA,WAAA,eAAY;AA0B5B,eAAgB,aAAa,QAAmB,MACnB,OAAyB,MAAa;AACjE,YAAI,OAAO,cAAc,QAAQ,cAAc;AAC7C,iBAAO,OAAO,cAAc,QAAQ,aAAa,QAAQ,MAAM,OAAO,IAAI;;AAE5E,YAAI,cAAc,QAAQ,OAAO,MAAM,IAAI;AAC3C,YAAI,MAAO,cAAc,EAAC,YAAW,IAAI,CAAA;AACzC,YAAI,MAAiB,CAAA,GAAI,IAAI,GAAG,IAAI,GAAG,GAAG,MAAM,QAAQ,IAAI,SAAS;AACrE,YAAI,KAAK,MAAM,iCAAiC,GAAG;AACjD,iBAAO,IAAI,KAAK,QAAQ;AACtB,gBAAI,KAAK,OAAO,GAAG;AACnB,gBAAI,MAAM,KAAK;AACb,kBAAI,UAAU,OAAO,WAAW,GAAG;AAEjC,uBAAO,OAAO,OACZ,QAAQ,WACR,CAAE,IAAI,eAAA,QAAU,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA,GAAI,OAAO,aAAa,EAAG,IAAG,CAAE,CAAC;AACzE,oBAAI,KAAK,IAAI;AACb,wBAAQ;AACR,oBAAI;yBACK,UAAU,IAAI;AAEvB,oBAAI,IAAI,IAAI,GAAG;AAEb,sBAAI,KAAK,aAAa,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;;AAE1D,wBAAQ;AACR,oBAAI;;uBAEG,MAAM,OAAO,UAAU,IAAI;AAEpC;uBACS,MAAM,KAAK;AAEpB,kBAAI,UAAU,OAAO,WAAW,GAAG;AAEjC,oBAAI,OAAQ,IAAI,eAAA,QAAU,KAAK,MAAM,GAAG,CAAC,GAAG,CAAA,GAAI,OAAO,aAAa,EAAG,IAAG;AAC1E,uBAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,IAAI,GAAG,GAAG;AACnD,oBAAI,KAAK,IAAI;AACb,wBAAQ;AACR,oBAAI;yBACK,UAAU,IAAI;AAEvB,oBAAI,QAAQ;AAEV;;;uBAGK,MAAM,MAAM;AAErB,kBAAI,UAAU,MAAM,KAAK,OAAO,CAAC,EAAE,MAAM,gBAAgB,GAAG;AAE1D,oBAAI,MAAQ,OAAe,IAAI,EAAa;AAC5C,oBAAI,IAAI,IAAI,GAAG;AAEb,sBAAI,KAAK,aAAa,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;;AAE1D,wBAAQ;AACR,oBAAI,IAAI;AACR,qBAAK;qBACA;AAEL,oBAAI,KAAK,OAAO,GAAG;AACnB,oBAAI,MAAM,OAAO,UAAU,IAAI;AAE7B,sBAAI,IAAI,IAAI,GAAG;AAEb,wBAAI,KAAK,aAAa,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;;AAE1D,0BAAQ;AAAK,sBAAI;2BACR,MAAM,OAAO,UAAU,OAAO,WAAW,GAAG;AAErD,yBAAO,OAAO,OACZ,QAAQ,WACR,CAAE,IAAI,eAAA,QAAU,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA,GAAI,OAAO,aAAa,EAAG,IAAG,CAAE,CAAC;AACzE,sBAAI,KAAK,IAAI;AACb,0BAAQ;AACR,sBAAI;2BACK,EAAE,MAAM,SAAS,KAAK,UAAU,IAAK;AAE9C;AACA,yBAAO,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;;;;;AAKpD,cAAI,UAAU,IAAI;AAEhB,kBAAM,IAAI,cAAA,QAAS,qBAAqB,iCAAiC;;;AAG7E,YAAI,IAAI,KAAK,QAAQ;AAEnB,cAAI,KAAK,aAAa,QAAQ,KAAK,MAAM,CAAC,GAAG,GAAG,CAAC;;AAEnD,YAAI,SAAS,MAAM;AAEjB,gBAAM,CAAC,OAAO,OAAO,QAAQ,UAAU,KAAK,EAAC,cAAc,OAAO,aAAa,MAAK,CAAC,CAAC;mBAC7E,IAAI,SAAS,GAAG;AAEzB,gBAAM,CAAC,OAAO,OAAO,QAAQ,QAAQ,GAAG,CAAC;;AAE3C,eAAO;MACT;AAvGgB,MAAAA,WAAA,eAAY;AAiH5B,eAAgB,aAAa,QAAmB,MAAc,KAAY;AAExE,eAAO,KAAK,QAAQ,QAAQ,cAAA,SAAS,IAAI,EAAE,QAAQ,QAAQ,cAAA,SAAS,IAAI;AACxE,YAAI,WAAW,OAAO,OAAO,QAAQ,IAAI;AACzC,eAAO,OAAO,OAAO,QAAQ,SAAS,CAAA,GAAI,KAAK,QAAQ;MACzD;AALgB,MAAAA,WAAA,eAAY;AAgB5B,eAAgB,UAAU,QAAmB,MAAe,QAAiB,KAAa,OAAc;AAEtG,QAAAA,WAAU,mBAAmB,IAAI;AACjC,YAAI,cAAA,QAAS,OAAO,MAAM,YAAY,KAAK,cAAA,QAAS,cAAc,IAAI,GAAG;AAEvE,wBAAA,QAAS,cAAc,cAAA,QAAS,UAAU,IAAI,GAAG,EAAC,QAAQ,GAAG,QAAQ,EAAC,CAAC;AACvE,cAAM,KAAK,OAAO,OAAO,QAAQ,MAAM,CAAA,GAAI,EAAC,QAAQ,EAAC,CAAC;AACtD,iBAAO,OAAO,OAAO,QAAQ,QAAQ,CAAC,IAAI,IAAI,CAAC;;AAGjD,YAAM,MAAM,OAAO,OAAO,QAAQ,cAAc,CAAC,IAAI,CAAC;AACtD,sBAAA,QAAS,SAAS,KAAK,QAAQ,SAAU,IAAI,OAAO,IAAI,OAAO,MAAM;AACrE,YAAI,OAAgB;AACpB,YAAI,OAAO;AAET,iBAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,GAAG,EAAC,UAAU,aAAA,SAAS,IAAI,YAAY,KAAI,CAAC;;AAE1F,sBAAA,QAAS,YAAY,MAAM,YAAY,IAAI;AAC3C,eAAO;MACT;AAnBgB,MAAAA,WAAA,YAAS;AAyBzB,eAAgB,mBAAmB,MAAa;AAC9C,YAAM,SAAU,cAAA,QAAS,OAAO,MAAM,IAAI,IAAI,cAAA,QAAS,QAAQ,IAAI,IAAI;AACvE,YAAI,cAAA,QAAS,YAAY,MAAM,eAAe,KAAM,UAAU,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,eAAgB;AAEnG,wBAAA,QAAS,cAAc,MAAM,EAAC,eAAe,MAAK,CAAC;;MAEvD;AANgB,MAAAA,WAAA,qBAAkB;AAalC,eAAgB,WAAW,MAAY;AACrC,YAAI,OAAO,SAAU,UAAU;AAC7B,iBAAO;;AAET,YAAI,OAAO,KAAK,KAAI;AACpB,YAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG;AACzC,kBAAQ;;AAEV,eAAO;MACT;AATgB,MAAAA,WAAA,aAAU;AAkB1B,eAAgB,cAAc,OAAkB,OAAa;AAE3D,gBAAQA,WAAU,WAAW,SAAS,EAAE;AACxC,YAAI,UAAU,KAAK;AACjB,gBAAM,SAAS,QAAQ;mBACd,UAAU,KAAK;AACxB,gBAAM,SAAS,QAAQ;mBACd,UAAU,KAAK;AACxB,gBAAM,SAAS,QAAQ;mBACd,OAAO;AAChB,gBAAM,SAAS,QAAQ;;AAEzB,eAAO;MACT;AAbgB,MAAAA,WAAA,gBAAa;AAuB7B,eAAgB,eAAe,QAAmB,MACnB,KAAW;AACxC,YAAI,OAAO;AACX,YAAI,YAAY;AAChB,YAAI,IAAI;AACR,eAAO,IAAI,IAAI,QAAQ;AACrB,cAAI,IAAI,IAAI,OAAO,GAAG;AACtB,cAAI,MAAM,MAAM;AACd,oBAAQ,IAAI,IAAI,OAAO,GAAG;qBAEnB,MAAM,KAAK;AAClB,gBAAI,IAAI,OAAO,GAAG;AAClB,gBAAI,MAAM,KAAK;AACb,sBAAQ;mBACH;AACL,kBAAI,CAAC,EAAE,MAAM,OAAO,KAAK,SAAS,GAAG,EAAE,IAAI,KAAK,QAAQ;AACtD,sBAAM,IAAI,cAAA,QAAS,qBACC,mCAAmC;;AAEzD,0BAAY,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,IAAI,GACvC,KAAK,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7C,qBAAO;;iBAEJ;AACL,oBAAQ;;;AAGZ,eAAO,QAAQ,QAAQ,WAAW,IAAI;MACxC;AA5BgB,MAAAA,WAAA,iBAAc;AAwC9B,eAAgB,QAAQ,QAAmB,IAAY,IAAU;AAC/D,YAAI,GAAG,MAAM,SAAS,KAAK,GAAG,MAAM,4BAA4B,GAAG;AACjE,gBAAM;;AAER,YAAI,GAAG,SAAS,GAAG,SAAS,OAAO,cAAc,QAAQ,WAAW,GAAG;AACrE,gBAAM,IAAI,cAAA,QAAS,iBACC,yEACwB;;AAE9C,eAAO,KAAK;MACd;AAVgB,MAAAA,WAAA,UAAO;AAiBvB,eAAgB,eAAe,QAAmB,SAAuB;AAAvB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAuB;AACvE,YAAI,EAAE,OAAO,cAAc,OAAO,cAAc,QAAQ,WAAW,GAAG;AACpE;;AAEF,YAAI,SAAS;AACX,gBAAM,IAAI,cAAA,QAAS,gBACA,oFACiC;eAC/C;AACL,gBAAM,IAAI,cAAA,QAAS,gBACA,sFACyC;;MAEhE;AAbgB,MAAAA,WAAA,iBAAc;AAmB9B,eAAgB,YAAY,QAAiB;AAC3C,YAAI,OAAO,MAAM,OAAO,QAAQ;AAE9B,gBAAM,IAAI,cAAA,QAAS,sBAAsB,0CAA0C;;AAErF,eAAO,MAAM,OAAO,SAAS;MAC/B;AANgB,MAAAA,WAAA,cAAW;AAe3B,eAAgB,SAAS,MAAe,QAAiB;AACvD,YAAM,OAAO,KAAK,KAAI;AACtB,YAAM,UAAU,OAAO;AACvB,aAAK,SAAS,SAAC,GAAU;;AACvB,kBAAQ,QAAQ,EAAE,MAAM,CAAC;AACzB,cAAM,SAAS,EAAE,YAAY,UAAU,KAAe,IAAI,MAAM,GAAG;;AACnE,qBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,kBAAM,OAAI,UAAA;AACb,sBAAQ,QAAQ,QAAQ,MAAM,CAAC;;;;;;;;;;;QAEnC,CAAC;AACD,eAAO;MACT;AAXgB,MAAAA,WAAA,WAAQ;AAoBxB,eAAgB,mBAAmB,SAAoB,OAAe,OAAa;AAEjF,eAAO;MACT;AAHgB,MAAAA,WAAA,qBAAkB;AAWlC,eAAgB,WAAW,QAAiB;AAC1C,YAAM,OAAO,OAAO,MAAM,IAAI,MAAM;AACpC,eAAQ,OAAO,EAAC,aAAa,KAAI,IAAI,CAAA;MACvC;AAHgB,MAAAA,WAAA,aAAU;AAgB1B,eAAgB,cAAc,QACA,SACA,OAAsB;;AADtB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAuC;AACvC,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAClD,YAAI,MAAe,WAAW,MAAM;AACpC,YAAI,SAAS;;AACX,qBAAgB,KAAA,SAAA,OAAO,KAAK,GAAG,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA7B,kBAAI,MAAG,GAAA;AACV,kBAAI,CAAC,QAAQ,eAAe,GAAG,GAAG;AAChC,oBAAI,OAAO;AACT,wBAAM,IAAI,cAAA,QAAS,iBAAiB,sBAAsB,GAAG;;AAE/D,uBAAO,IAAI,GAAG;;;;;;;;;;;;;AAIpB,eAAO;MACT;AAfgB,MAAAA,WAAA,gBAAa;AAwB7B,eAAS,WAAW,MAAY;;AAC9B,YAAI,UAAmB,CAAA;AACvB,YAAI,OAAO;AACX,YAAI,KAAK,KAAK;AACd,eAAO,MAAM;AACX,eAAA,OAAmB,UAAU,MAAM,CAAC,KAAK,GAAG,CAAC,GAAC,CAAA,GAA7C,MAAG,GAAA,CAAA,GAAE,MAAG,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AACf,cAAI,QAAQ,KAAK;AACf,iBAAA,OAAmB,UAAU,MAAM,CAAC,GAAG,CAAC,GAAC,CAAA,GAAxC,MAAG,GAAA,CAAA,GAAE,MAAG,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AACf,kBAAO,QAAQ,WAAW,QAAQ,SAC9B,KAAK,MAAM,GAAG,IAAI;AACtB,oBAAQ,GAAG,IAAI;qBACN,KAAK;AACd,oBAAQ,GAAG,IAAI;;;AAGnB,eAAO;MACT;AASA,eAAS,aAAa,MAAc,OAAa;AAC/C,eAAO,QAAQ,GAAG;AAChB,iBAAO,KAAK,KAAI,EAAG,MAAM,GAAG,EAAE;AAC9B;;AAEF,eAAO,KAAK,KAAI;MAClB;AAWA,eAAS,UAAU,MAAc,KAAa;AAC5C,YAAI,SAAS,KAAK;AAClB,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,YAAI,QAAQ;AACZ,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,YAAY;AAEhB,eAAO,QAAQ,QAAQ;AACrB,cAAI,IAAI,KAAK,OAAO;AACpB,kBAAQ,GAAG;YACX,KAAK;AACH;YACF,KAAK;AACH,kBAAI,YAAY;AACd;qBACK;AACL,4BAAY;AACZ,oBAAI,QAAQ,QAAQ;AAClB,0BAAQ;;;AAGZ;AACA;YACF,KAAK;AACH,kBAAI,QAAQ;AACV;;AAEF,kBAAI,cAAc,WAAW;AAC3B;AACA,4BAAY;;AAEd,2BAAa;AACb;YACF;AACE,kBAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,MAAM,IAAI;AACpC,uBAAO,CAAC,YAAY,SAEZ,aAAa,OAAO,KAAK,GAAG,GAAG,KAAK,MAAM,KAAK,CAAC;;AAE1D,2BAAa;AACb,0BAAY;;AAEd,mBAAS;;AAEX,YAAI,QAAQ;AACV,gBAAM,IAAI,cAAA,QAAS,yBACA,yCAAyC;;AAE9D,eAAO,CAAC,YAAY,SAAS,aAAa,OAAO,KAAK,GAAG,IAAI,KAAK,MAAM,KAAK,CAAC;MAChF;IAEF,GAnqBU,cAAA,YAAS,CAAA,EAAA;AAqqBnB,YAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/qBf,QAAA,gBAAA,gBAAA,kBAAA;AAMA,QAAA,QAAA,WAAA;AAmBE,eAAAE,OAAoB,UACA,MAAe,OAAc;AAD7B,aAAA,WAAA;AACA,aAAA,OAAA;AAfb,aAAA,SAAkB,CAAA;AAMjB,aAAA,QAAqB,CAAA;AAU3B,aAAK,SAAS,EAAC,SAAS,MAAK;AAC7B,aAAK,QAAQ,CAAE,KAAK,SAAS,OAAO,SAAS,KAAK,MAAM,CAAC;AACzD,YAAI,MAAM;AACR,eAAK,MAAM,CAAC,EAAE,MAAM;;AAEtB,aAAK,MAAM,KAAK,MAAM,CAAC,EAAE;MAC3B;AAOA,aAAA,eAAWA,OAAA,WAAA,OAAG;aASd,WAAA;AACE,iBAAO,KAAK;QACd;aAXA,SAAe,KAAY;AACzB,eAAK,OAAO;QACd;;;;AAgBO,MAAAA,OAAA,UAAA,OAAP,WAAA;;AAAY,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAgC;AAAhC,eAAA,EAAA,IAAA,UAAA,EAAA;;;AACV,mBAAmB,SAAA,SAAA,IAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAApB,gBAAM,OAAI,SAAA;AACb,gBAAI,CAAC,MAAM;AACT;;AAEF,gBAAM,OAAO,cAAA,QAAS,OAAO,IAAI,IAC/B,KAAK,SAAS,OAAO,OAAO,IAAI,IAAI;AACtC,iBAAK,SAAS,KAAK;AACb,gBAAA,KAAA,OACJ,KAAK,MAAM,SAAS,KAAK,IAAG,EAAG,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,GAAC,CAAA,GADxD,QAAG,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AAEnB,gBAAI,CAAC,SAAS;AACZ;;AAEF,gBAAI,OAAK;AACP,mBAAK,IAAG;AACR,mBAAK,KAAI,MAAT,MAAI,cAAA,CAAA,GAAA,OAAS,KAAG,GAAA,KAAA,CAAA;AAChB;;AAEF,iBAAK,MAAM,KAAK,IAAI;AACpB,gBAAI,KAAK,KAAK;AACZ,kBAAI,KAAK,SAAS;AAChB,uBAAO,OAAO,KAAK,KAAK,KAAK,GAAG;;AAElC,mBAAK,MAAM,KAAK;mBACX;AACL,mBAAK,MAAM,KAAK;;;;;;;;;;;;MAGtB;AAOO,MAAAA,OAAA,UAAA,MAAP,WAAA;AACE,YAAM,OAAO,KAAK,MAAM,IAAG;AAC3B,YAAI,CAAC,KAAK,QAAQ;AAChB,iBAAO,KAAK;;AAEd,aAAK,MAAO,KAAK,MAAM,SAAS,KAAK,IAAG,EAAG,MAAM,CAAA;AACjD,eAAO;MACT;AAQO,MAAAA,OAAA,UAAA,MAAP,SAAW,GAAa;AAAb,YAAA,MAAA,QAAA;AAAA,cAAA;QAAa;AACtB,eAAO,KAAK,MAAM,SAAS,IAAI,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;MACxE;AASO,MAAAA,OAAA,UAAA,OAAP,SAAY,OAAe;AACzB,YAAM,MAAM,KAAK,IAAG;AACpB,eAAO,QAAQ,IAAI,QAAQ,IAAI,IAAG;MACpC;AAMO,MAAAA,OAAA,UAAA,WAAP,WAAA;AACE,eAAO,eAAe,KAAK,MAAM,KAAK,MAAM,IAAI;MAClD;AAEF,aAAAA;IAAA,EA9HA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA,QAAA,iBAAA,gBAAA,mBAAA;AAEA,QAAA,aAAA,gBAAA,eAAA;AAGA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,eAAA;AAWA,QAAA,YAAA,WAAA;AAiCE,eAAAC,WAAoB,SAAiB,KAAqB,eAA2B;;AAAjE,aAAA,UAAA;AAAsC,aAAA,gBAAA;AA3BnD,aAAA,aAAqB;AAYrB,aAAA,IAAY;AAMZ,aAAA,YAAoB;AAUzB,YAAM,QAAQ,IAAI,eAAe,SAAS;AAC1C,YAAM,UAAU,IAAI,SAAS;AAC7B,eAAO,IAAI,SAAS;AACpB,YAAI;AACJ,YAAI,KAAK;AACP,gBAAM,CAAA;;AACN,qBAAiB,KAAA,SAAA,OAAO,KAAK,GAAG,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA9B,kBAAM,KAAE,GAAA;AACX,kBAAI,EAAE,IAAI,IAAI,EAAE;;;;;;;;;;;;AAGpB,aAAK,cAAc,WAAW,IAAI;AAClC,aAAK,QAAQ,IAAI,WAAA,QAAM,KAAK,aAAa,KAAK,QAAQ,UAAU,IAAI;AACpE,aAAK,MAAK;AACV,aAAK,KAAK,KAAK,YAAY,OAAO,MAAM,CAAC;MAC3C;AAKA,aAAA,eAAIA,WAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO,KAAK,cAAc;QAC5B;;;;AAKA,aAAA,eAAIA,WAAA,WAAA,eAAW;aAAf,WAAA;AACE,iBAAO,KAAK,cAAc;QAC5B;;;;AAKA,aAAA,eAAIA,WAAA,WAAA,QAAI;aAAR,WAAA;AACE,iBAAO,KAAK,cAAc;QAC5B;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,UAAM;aAOV,WAAA;AACE,iBAAO,KAAK;QACd;aATA,SAAW,KAAW;AACpB,eAAK,UAAU;QACjB;;;;AAgBO,MAAAA,WAAA,UAAA,QAAP,SAAa,MAAmB,OAAiB;AAC/C,eAAO,KAAK,cAAc,SAAS,IAAI,IAAI,EAAE,MAAM,KAAK;MAC1D;AASO,MAAAA,WAAA,UAAA,SAAP,SAAc,MAAmB,QAAc;AAC7C,eAAO,KAAK,cAAc,SAAS,IAAI,IAAI,EAAE,OAAO,MAAM;MAC5D;AAWO,MAAAA,WAAA,UAAA,WAAP,SAAgB,MAAmB,QAAc;AAC/C,eAAO,KAAK,cAAc,SAAS,IAAI,IAAI,EAAE,SAAS,MAAM;MAC9D;AAMO,MAAAA,WAAA,UAAA,WAAP,WAAA;;AACE,YAAI,MAAM;;AACV,mBAAqB,KAAA,SAAA,MAAM,KAAK,KAAK,cAAc,SAAS,KAAI,CAAE,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhE,gBAAM,SAAM,GAAA;AACf,mBAAO,SAAS,OACd,KAAK,cAAc,SAAS,IAAI,MAAqB,IAAI;;;;;;;;;;;AAE7D,eAAO;MACT;AAMO,MAAAA,WAAA,UAAA,QAAP,WAAA;AACE,YAAI;AACJ,eAAO,KAAK,IAAI,KAAK,OAAO,QAAQ;AAClC,cAAI,KAAK,aAAY;AACrB,eAAK,KAAK,EAAE;AACZ,eAAK,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC;;MAErC;AAQO,MAAAA,WAAA,UAAA,OAAP,SAAY,KAAwB;AAClC,YAAI,eAAe,aAAA,mBAAmB,IAAI,YAAY;AACpD,eAAK,QAAQ,IAAI,UAAU;eACtB;AACL,eAAK,MAAM,KAAK,GAAG;;MAEvB;AAOO,MAAAA,WAAA,UAAA,UAAP,SAAe,MAA6B;;;AAC1C,mBAAkB,SAAA,SAAA,IAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAAnB,gBAAM,MAAG,SAAA;AACZ,iBAAK,MAAM,KAAK,GAAG;;;;;;;;;;;MAEvB;AAMO,MAAAA,WAAA,UAAA,MAAP,WAAA;AACE,YAAI,CAAC,KAAK,MAAM,IAAG,EAAG,OAAO,KAAK,GAAG;AACnC,iBAAO;;AAET,YAAI,OAAO,KAAK,MAAM,IAAG,EAAG;AAC5B,aAAK,cAAc,UAAS;AAC5B,eAAO;MACT;AAYO,MAAAA,WAAA,UAAA,mBAAP,SAAwB,GAAS;AAC/B,YAAM,SAAS,KAAK,OAAO,aAAa,CAAC;AACzC,eAAO,SAAS,OAAO,OAAO;MAChC;AAKO,MAAAA,WAAA,UAAA,eAAP,WAAA;AACE,YAAM,OAAO,KAAK,OAAO,YAAY,KAAK,CAAC;AAC3C,eAAO,SAAS,SAAY,KAAK,OAAO,cAAc,IAAI;MAC5D;AAKO,MAAAA,WAAA,UAAA,cAAP,WAAA;AACE,eAAO,CAAC,CAAC,KAAK,OAAO,OAAO,KAAK,CAAC,EAAE,MAAM,IAAI;MAChD;AAKO,MAAAA,WAAA,UAAA,UAAP,WAAA;AACE,eAAO,KAAK,YAAW,GAAI;AACzB,eAAK;;AAEP,eAAO,KAAK,aAAY;MAC1B;AAKO,MAAAA,WAAA,UAAA,QAAP,WAAA;AACE,YAAI,KAAK,KAAK,OAAO,MAAM,KAAK,CAAC,EAAE,MAAM,mCAAmC;AAC5E,YAAI,IAAI;AACN,eAAK,KAAK,GAAG,CAAC,EAAE;AAChB,iBAAO,GAAG,CAAC,KAAK,GAAG,CAAC;eACf;AACL,eAAK;AACL,iBAAO;;MAEX;AASO,MAAAA,WAAA,UAAA,cAAP,SAAmB,OAAe,QAAgB;AAChD,gBAAQ,KAAK,QAAO,GAAI;UACxB,KAAK;AACH,gBAAI,CAAC,QAAQ;AAEX,oBAAM,IAAI,cAAA,QAAS,iBAAiB,2BAA2B,KAAK,SAAS;;AAE/E,mBAAO;UACT,KAAK;AACH,gBAAI,CAAC,QAAQ;AAEX,oBAAM,IAAI,cAAA,QAAS,yBACC,yCAAyC;;AAE/D,mBAAO;UACT,KAAK;AACH,iBAAK;AACL,mBAAO,OAAO,KAAK,MAAK;UAC1B,KAAK;AACH,gBAAI,IAAI,EAAE,KAAK,GAAG,SAAS;AAC3B,mBAAO,KAAK,IAAI,KAAK,OAAO,QAAQ;AAClC,sBAAQ,KAAK,OAAO,OAAO,KAAK,GAAG,GAAG;gBACtC,KAAK;AAAO,uBAAK;AAAK;gBACtB,KAAK;AAAO;AAAU;gBACtB,KAAK;AACH,sBAAI,EAAE,WAAW,GAAG;AAClB,2BAAO,KAAK,OAAO,MAAM,GAAG,KAAK,IAAI,CAAC;;AAExC;;;AAIJ,kBAAM,IAAI,cAAA,QAAS,qBAAqB,qBAAqB;;AAE/D,YAAM,IAAI,KAAK,aAAY;AAC3B,aAAK,KAAK,EAAE;AACZ,eAAO;MACT;AASO,MAAAA,WAAA,UAAA,cAAP,SAAmB,OAAe,KAAY;AAC5C,YAAI,KAAK,QAAO,MAAO,KAAK;AAC1B,iBAAO;;AAET,YAAI,IAAI,EAAE,KAAK,GAAG,SAAS;AAC3B,eAAO,KAAK,IAAI,KAAK,OAAO,QAAQ;AAClC,kBAAQ,KAAK,OAAO,OAAO,KAAK,GAAG,GAAG;YACtC,KAAK;AAAO;AAAU;YACtB,KAAK;AAAO,mBAAK;AAAK;YACtB,KAAK;AACH,kBAAI,YAAY,GAAG;AAEjB,sBAAM,IAAI,cAAA,QAAS,qBACC,0CAA0C,KAAO;;AAEvE;YACF,KAAK;AACH,kBAAI,WAAW,GAAG;AAChB,uBAAO,KAAK,OAAO,MAAM,GAAG,KAAK,IAAI,CAAC;;AAExC;;;AAIJ,cAAM,IAAI,cAAA,QAAS,uBACC,iDAAmD,KAAK,SAAS;MACvF;AAQO,MAAAA,WAAA,UAAA,eAAP,SAAoB,MAAc,SAAiB;AACjD,YAAI,IAAI,KAAK,QAAO;AAAI,aAAK,KAAK,EAAE;AACpC,YAAI,KAAK,KAAK,KAAK,OAAO,QAAQ;AAChC,cAAI,MAAM,MAAM;AACd,iBAAK,KAAK,MAAK;qBACN,MAAM,OAAO,SAAS;AAC/B,iBAAK;AACL,gBAAI,KAAK,YAAY,IAAI,EAAE,KAAI;;AAEjC,cAAI,KAAK,SAAS,aAAa,CAAC,GAAG;AACjC,mBAAO,KAAK,iBAAiB,CAAC;;;AAIlC,cAAM,IAAI,cAAA,QAAS,8BACC,4CAA4C,KAAK,SAAS;MAChF;AAOO,MAAAA,WAAA,UAAA,WAAP,SAAgB,MAAY;AAC1B,YAAI,KAAK,QAAO,MAAO,KAAK;AAC1B,cAAI,QAAQ,KAAK,YAAY,IAAI;AAC7B,cAAA,KAAA,OAAgB,eAAA,QAAU,WAAW,KAAK,GAAC,CAAA,GAA1C,QAAK,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AAChB,cAAI,OAAO;AAET,mBAAO,QAAQ;;eAEZ;AAEL,cAAI,QAAQ,KAAK,OAAO,MAAM,KAAK,CAAC;AAChC,cAAA,KAAA,OAAwB,eAAA,QAAU,WAAW,OAAO,IAAI,GAAC,CAAA,GAAxD,QAAK,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA,GAAE,WAAM,GAAA,CAAA;AACxB,cAAI,OAAO;AACT,iBAAK,KAAK;AACV,mBAAO,QAAQ;;;AAInB,cAAM,IAAI,cAAA,QAAS,qBACC,yCAAyC,KAAK,SAAS;MAC7E;AAQO,MAAAA,WAAA,UAAA,UAAP,SAAe,OAAe,OAAa;AACzC,eAAO,KAAK,YAAW,GAAI;AACzB,eAAK;;AAEP,YAAI,IAAI,KAAK;AACb,YAAI,SAAS;AACb,eAAO,KAAK,IAAI,KAAK,OAAO,QAAQ;AAClC,cAAI,IAAI,KAAK;AACb,cAAI,IAAI,KAAK,QAAO;AAAI,eAAK,KAAK,EAAE;AACpC,kBAAQ,GAAG;YACX,KAAK;AAAO,mBAAK,KAAK,MAAK;AAAI;YAC/B,KAAK;AAAO;AAAU;YACtB,KAAK;AACH,kBAAI,WAAW,GAAG;AAEhB,sBAAM,IAAI,cAAA,QAAS,qBACC,0CAA0C,KAAK;;AAErE;AACA;;AAEF,cAAI,WAAW,KAAK,MAAM,OAAO;AAC/B,mBAAO,KAAK,OAAO,MAAM,GAAG,CAAC;;;AAIjC,cAAM,IAAI,cAAA,QAAS,2BACC,4BAA4B,OAAO,KAAK,SAAS;MACvE;AAOO,MAAAA,WAAA,UAAA,WAAP,SAAgB,MAAY;AAC1B,eAAO,IAAIA,WAAU,KAAK,YAAY,IAAI,GAAG,KAAK,MAAM,KACnC,KAAK,aAAa,EAAE,IAAG;MAC9C;AAQO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAc,OAAa;AAC1C,eAAO,IAAIA,WAAU,KAAK,QAAQ,MAAM,KAAK,GAAG,KAAK,MAAM,KACtC,KAAK,aAAa,EAAE,IAAG;MAC9C;AAQO,MAAAA,WAAA,UAAA,kBAAP,SAAuB,MAAY;AACjC,YAAI,IAAI,eAAA,QAAU,WAAW,KAAK,YAAY,IAAI,CAAC;AACnD,YAAI,MAAM,IAAI;AACZ,iBAAO;;AAET,YAAI,KAAK,SAAS,aAAa,CAAC,GAAG;AACjC,iBAAO;;AAGT,cAAM,IAAI,cAAA,QAAS,8BACC,4CAA4C,KAAK,SAAS;MAChF;AAKO,MAAAA,WAAA,UAAA,UAAP,WAAA;AACE,YAAI,OAAQ,KAAK,QAAO,MAAO;AAC/B,YAAI,MAAM;AACR,eAAK;;AAEP,eAAO;MACT;AAUO,MAAAA,WAAA,UAAA,SAAP,SAAc,MAAY;;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC1B,gBAAO,KAAA,KAAK,cAAc,aAAY,OAAM,MAAA,IAAA,cAAA,CAAC,IAAI,GAAA,OAAK,IAAI,GAAA,KAAA,CAAA;MAC5D;AAGF,aAAAA;IAAA,EAvdA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChBA,QAAA,gBAAA,gBAAA,kBAAA;AAuEA,QAAA,WAAA,WAAA;AAOE,eAAAC,UAAoB,QAAiB;AAAjB,aAAA,SAAA;MAAqB;AAKzC,aAAA,eAAcA,UAAA,WAAA,SAAK;aAAnB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAKO,MAAAA,UAAA,UAAA,OAAP,WAAA;;AAAY,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAmB;AAAnB,gBAAA,EAAA,IAAA,UAAA,EAAA;;AACV,SAAA,KAAA,KAAK,QAAO,KAAI,MAAA,IAAA,cAAA,CAAA,GAAA,OAAI,KAAK,GAAA,KAAA,CAAA;MAC3B;AAMO,MAAAA,UAAA,UAAA,MAAP,WAAA;AACE,eAAO,KAAK,OAAO,IAAG;MACxB;AAMA,aAAA,eAAWA,UAAA,WAAA,SAAK;aAAhB,WAAA;AACE,iBAAO,KAAK,OAAO,KAAK,KAAI,IAAK,CAAC;QACpC;aAMA,SAAiB,MAAa;AAC5B,eAAK,OAAO,KAAK,KAAI,IAAK,CAAC,IAAI;QACjC;;;;AAMA,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK,OAAO,CAAC;QACtB;aAMA,SAAgB,MAAa;AAC3B,eAAK,OAAO,CAAC,IAAI;QACnB;;;;AAMO,MAAAA,UAAA,UAAA,OAAP,SAAY,GAAU;AACpB,YAAI,KAAK,MAAM;AACb,cAAI;;AAEN,eAAO,KAAK,OAAO,MAAM,KAAK,KAAI,IAAK,CAAC;MAC1C;AAMO,MAAAA,UAAA,UAAA,OAAP,WAAA;AACE,eAAO,KAAK,OAAO;MACrB;AAMO,MAAAA,UAAA,UAAA,QAAP,WAAA;AACE,aAAK,SAAS,CAAA;MAChB;AAQO,MAAAA,UAAA,UAAA,QAAP,SAAa,UAA0B,UAAkB;AAA5C,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAwB;AACnC,YAAI,KAAK,OAAO,WAAW,KAAK,CAAC,UAAU;AACzC,iBAAO,KAAK;;AAGd,eAAO,KAAK,OACV,QAAQ,WAAW,iBAAiB,QAAQ,KAAK,QAAQ,CAAA,CAAE;MAC/D;AASO,MAAAA,UAAA,UAAA,SAAP,SAAc,MAAY;;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC1B,gBAAO,KAAA,KAAK,QAAQ,cAAc,aAAY,OAAM,MAAA,IAAA,cAAA,CAAC,IAAI,GAAA,OAAK,IAAI,GAAA,KAAA,CAAA;MACpE;AAEF,aAAAA;IAAA,EApHA;AAAsB,YAAA,WAAA;AA0OtB,QAAA,WAAA,SAAA,QAAA;AAAuC,gBAAAC,WAAA,MAAA;AA2CrC,eAAAA,UAAsB,SAAyB;AAAE,YAAA,QAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAmB;AAAnB,gBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAAjD,YAAA,QACE,OAAA,KAAA,MAAM,KAAK,KAAC;AADQ,cAAA,UAAA;AAXf,cAAA,SAAkB,CAAA;AAIjB,cAAA,cAAwB,CAAA;AAS9B,YAAI,MAAK,QAAQ;AACf,gBAAK,OAAO,CAAA;;;MAEhB;AAKE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACA,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,UAAA,WAAA,OAAG;aAAd,WAAA;AACE,iBAAO,KAAK;QACd;aAMA,SAAe,OAAc;AAC3B,eAAK,OAAO;QACd;;;;AAKA,aAAA,eAAWA,UAAA,WAAA,WAAO;aAAlB,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,KAAW;AAC5B,eAAO,KAAK,YAAY,GAAG;MAC7B;AAKO,MAAAA,UAAA,UAAA,cAAP,SAAmB,KAAa,OAAW;AACzC,aAAK,YAAY,GAAG,IAAI;AACxB,eAAO;MACT;AAOA,aAAA,eAAIA,UAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAOA,aAAA,eAAIA,UAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,UAAA,UAAA,SAAP,SAAc,MAAY;AACxB,eAAO,SAAS,KAAK;MACvB;AAMO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,MAAM,KAAK,KAAK,QAAQ;AACtC,eAAK,YAAY,OAAO,KAAK,MAAM,KAAK,CAAC;AACzC,eAAK,MAAK;;AAEZ,YAAI,KAAK,OAAO,MAAM,KAAK,KAAK,QAAQ;AACtC,cAAI,KAAK,YAAY,WAAW,GAAG;AACjC,mBAAOA,UAAS;;AAGlB,gBAAM,IAAI,cAAA,QAAS,aAAa,gBAAgB,KAAK,QAAO,CAAE;;AAEhE,YAAI,KAAK,WAAW,KAAK,UAAU,KAAK,IAAI,GAAG;AAGvC,cAAA,KAAA,OAAgB,KAAK,UAAU,KAAK,IAAI,GAAC,CAAA,GAAxC,KAAE,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AAClB,gBAAM,IAAI,cAAA,QAAS,IAAI,SAAS,KAAK,QAAO,CAAE;;AAEhD,YAAI,CAAC,KAAK,SAAS;AACjB,iBAAOA,UAAS;;AAElB,aAAK,KAAK,KAAK,KAAK;AACpB,eAAOA,UAAS;MAClB;AAMO,MAAAA,UAAA,UAAA,WAAP,WAAA;;;AACE,mBAAiB,KAAA,SAAA,OAAO,KAAK,KAAK,GAAG,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,gBAAM,KAAE,GAAA;AACX,mBAAO,KAAK,IAAI,EAAE;;;;;;;;;;;MAEtB;AAMO,MAAAA,UAAA,UAAA,gBAAP,SAAqB,KAAa;AAChC,eAAO,OAAO,KAAK,aAAa,GAAG;AACnC,eAAO;MACT;AAMO,MAAAA,UAAA,UAAA,UAAP,WAAA;AACE,eAAO,KAAK,YAAY,MAAM;MAChC;AAMO,MAAAA,UAAA,UAAA,WAAP,WAAA;AACE,eAAO,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI;MACnD;AAUO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAY;AAC3B,YAAM,QAAS,KAAK;AACpB,gBAAQ,MAAM,UAAU,CAAA,GAAI,IAAI,KAAKA,UAAS,OAAO,IAAI;MAC3D;AArMiB,MAAAA,UAAA,OAAkB,CAAC,MAAM,KAAK;AAM9B,MAAAA,UAAA,UAAqB,CAAC,MAAM,IAAI;AAMhC,MAAAA,UAAA,SAAoC;QAEnD,KAAK,CAAC,wBAAwB,wCAAwC;QAEtE,OAAO,CAAC,yBAAyB,yCAAyC;QAE1E,OAAO,CAAC,yBAAyB,iCAAiC;QAClE,QAAQ,CAAC,eAAe,gBAAgB;;AAoL5C,aAAAA;MA7MuC,QAAQ;AAAzB,YAAA,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClTtB,QAAA,iBAAA;AAEA,QAAA,eAAA;AAGA,QAAA,YAAA,SAAA,QAAA;AAAwB,gBAAAC,YAAA,MAAA;AAAxB,eAAAA,aAAA;;MAAkC;AAAA,aAAAA;IAAA,EAAV,eAAA,QAAQ;AAShC,QAAA,mBAAA,SAAA,QAAA;AAA8C,gBAAAC,mBAAA,MAAA;AAA9C,eAAAA,oBAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAaS,cAAA,cAAc;AAOd,cAAA,gBAA8B;;MAEvC;AAjBgB,MAAAA,kBAAA,qBAAiB,KAAA,CAAA,GAC7B,GAAC,UAAU,UAAU,IAAI,IAAG;AAgBhC,aAAAA;MAtB8C,aAAA,eAAe;sBAAxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXrB,QAAA,gBAAA,gBAAA,kBAAA;AAKA,QAAA,cAAA,WAAA;AAAA,eAAAC,eAAA;AAaY,aAAA,aAAyB;AAM3B,aAAA,UACN;UAAC,QAAQA,aAAY;UACpB,SAASA,aAAY;UACrB,QAAQA,aAAY;UACpB,SAASA,aAAY;;MAuH1B;AA3GgB,MAAAA,aAAA,aAAd,SAAyB,SAAsB,MACtB,UAA0B,KAC1B,MAAe;AADf,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAAwB;AAAE,YAAA,QAAA,QAAA;AAAA,gBAAA,CAAA;QAAa;AAE9D,YAAM,OAAO,QAAQ,WAAW,OAAO,IAAI;AAC3C,aAAK,YAAY,QAAQ;AACzB,YAAI,MAAM;AACR,eAAK,YAAY,IAAI;;AAEvB,sBAAA,QAAS,cAAc,MAAM,GAAG;AAChC,eAAO;MACT;AAWc,MAAAA,aAAA,cAAd,SAA0B,SAAsB,MACtB,KAAe,MAAiB;AAAhC,YAAA,QAAA,QAAA;AAAA,gBAAA,CAAA;QAAa;AAAE,YAAA,SAAA,QAAA;AAAA,iBAAA;QAAiB;AACxD,YAAM,WAAW,QAAQ,OAAO,QAAQ,IAAI;AAC5C,eAAO,QAAQ,OAAO,QAAQ,MAAM,CAAA,GAAI,KAAK,QAAQ;MACvD;AASc,MAAAA,aAAA,aAAd,SAAyB,SAAsB,MAAY;AACzD,YAAI,QAAQ,MAAM;AAChB,iBAAO;;AAET,eAAQ,QAAQ,WAAW,OAAO,MAAM,EAAe,QAAQ,IAAI;MACrE;AASc,MAAAA,aAAA,cAAd,SAA0B,SAAsB,SAAe;AAC7D,YAAI,OAAO,QAAQ,OAAO,QAAQ,OAAO;AACzC,YAAI,QAAQ,QAAQ,OAAO,QAAQ,SAAS,CAAA,GAAI,CAAA,GAAI,IAAI;AACxD,YAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,CAAC,KAAK,GAAG,EAAC,kBAAkB,QAAO,CAAC;AACjF,eAAO;MACT;AAMO,MAAAA,aAAA,UAAA,gBAAP,SAAqB,YAAsB;AACzC,aAAK,aAAa;MACpB;AAOO,MAAAA,aAAA,UAAA,MAAP,SAAW,MAAc,MAAuB;AAC9C,aAAK,QAAQ,IAAI,IAAI;MACvB;AAOO,MAAAA,aAAA,UAAA,cAAP,SAAmB,MAAyC;AAC1D,iBAAS,QAAQ,MAAM;AACrB,eAAK,IAAI,MAAM,KAAK,IAAI,CAAC;;MAE7B;AASO,MAAAA,aAAA,UAAA,SAAP,SAAc,MAAY;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC1B,YAAM,OAAO,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,MAAM;AACtD,YAAM,OAAO,KAAI,MAAA,QAAA,cAAA,CAAC,MAAM,KAAK,CAAC,CAAC,GAAA,OAAK,KAAK,MAAM,CAAC,CAAC,GAAA,KAAA,CAAA;AACjD,YAAI,SAAS,QAAQ;AACnB,eAAK,cAAc,QAAQ,KAAK,CAAC,GAAG,IAAI;;AAE1C,eAAO;MACT;AAMO,MAAAA,aAAA,UAAA,MAAP,SAAW,MAAY;AACrB,eAAO,KAAK,QAAQ,IAAI;MAC1B;AAEF,aAAAA;IAAA,EA9IA;AAAa,YAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTb,QAAA,wBAAA,gBAAA,0BAAA;AAGA,QAAA,mBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AAGA,QAAA,eAAA;AAOA,QAAA,eAAA,WAAA;AA0EE,eAAAC,cAAmB,eAAoC,SAA0B;AAA1B,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAA0B;AA9D1E,aAAA,UAAsB,CAAA;AAwBtB,aAAA,cAAgC,oBAAI,IAAG;AASvC,aAAA,UAAuB,CAAA;AAOvB,aAAA,OAAgB;AAMhB,aAAA,YAAwC,CAAA;AAMxC,aAAA,QAAiB;AAWtB,aAAK,WAAW,cAAc;AAE9B,aAAK,cAAc,IAAI,iBAAA,YAAW;AAClC,aAAK,YAAY,gBAAgB;AACjC,aAAK,YAAY,YAAY,cAAc,KAAK;AAEhD,aAAK,cAAc,IAAI,sBAAA,QAAiB,cAAc,KAAK;AAC3D,aAAK,YAAY,gBAAgB;AAEjC,qBAAA,eAAc,MAAA,QAAA,cAAA,CAAC,KAAK,OAAO,GAAA,OAAK,OAAO,GAAA,KAAA,CAAA;AACvC,SAAA,GAAA,aAAA,gBAAe,KAAK,SAAS,cAAc,OAAO;MACpD;AAQO,MAAAA,cAAA,UAAA,aAAP,SAAkB,QAAiB;AACjC,aAAK,QAAQ,QAAQ,MAAM;MAC7B;AAMO,MAAAA,cAAA,UAAA,YAAP,WAAA;AACE,aAAK,QAAQ,MAAK;MACpB;AAMA,aAAA,eAAWA,cAAA,WAAA,UAAM;aAAjB,WAAA;AACE,iBAAO,KAAK,QAAQ,CAAC;QACvB;;;;AAKO,MAAAA,cAAA,UAAA,QAAP,WAAA;AACE,aAAK,UAAU,CAAA;AACf,aAAK,OAAO;AACZ,aAAK,YAAY,CAAA;AACjB,aAAK,QAAQ;AACb,aAAK,KAAK,SAAQ;MACpB;AASO,MAAAA,cAAA,UAAA,UAAP,SAAe,UAAkB,MAAa;AAC5C,YAAI,OAAO,KAAK,UAAU,QAAQ;AAClC,YAAI,CAAC,MAAM;AACT,iBAAO,KAAK,UAAU,QAAQ,IAAI,CAAA;;AAEpC,aAAK,KAAK,IAAI;AACd,YAAI,KAAK,SAAS,UAAU;AAK1B,cAAM,UAAW,cAAA,QAAS,YAAY,MAAM,UAAU,KAAe;AACrE,cAAM,SAAS,UAAU,QAAQ,MAAM,GAAG,IAAI,CAAA,GAAI,OAAO,QAAQ,EAAE,KAAK,GAAG;AAC3E,wBAAA,QAAS,YAAY,MAAM,YAAY,KAAK;;MAEhD;AAaO,MAAAA,cAAA,UAAA,UAAP,SAAe,UAAgB;;AAC7B,YAAI,OAAO,KAAK,UAAU,QAAQ,KAAK,CAAA;AACvC,YAAI,SAAS,CAAA;;AACb,mBAAiB,SAAA,SAAA,IAAI,GAAA,WAAA,OAAA,KAAA,GAAA,CAAA,SAAA,MAAA,WAAA,OAAA,KAAA,GAAE;AAAlB,gBAAI,OAAI,SAAA;AACX,gBAAI,KAAK,OAAO,IAAI,GAAG;AACrB,qBAAO,KAAK,IAAI;;;;;;;;;;;;AAGpB,aAAK,UAAU,QAAQ,IAAI;AAC3B,eAAO;MACT;AAUO,MAAAA,cAAA,UAAA,iBAAP,SAAsB,UAAkB,OAAgB;;AACtD,YAAM,OAAO,KAAK,UAAU,QAAQ,KAAK,CAAA;;AACzC,mBAAmB,UAAA,SAAA,KAAK,GAAA,YAAA,QAAA,KAAA,GAAA,CAAA,UAAA,MAAA,YAAA,QAAA,KAAA,GAAE;AAArB,gBAAM,OAAI,UAAA;AACb,gBAAM,IAAI,KAAK,QAAQ,IAAI;AAC3B,gBAAI,KAAK,GAAG;AACV,mBAAK,OAAO,GAAG,CAAC;;;;;;;;;;;;MAGtB;AAOQ,MAAAA,cAAA,UAAA,SAAR,SAAe,MAAa;AAC1B,eAAO,QAAQ,SAAS,KAAK,MAAM;AACjC,iBAAO,KAAK;;AAEd,eAAO,CAAC,CAAC;MACX;AAEF,aAAAA;IAAA,EA1MA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACdA,QAAA,iBAAA,gBAAA,mBAAA;AAWA,QAAA,QAAA,2BAAA;AAOE,eAAAC,OAAmB,KAA4B,IAAe;AAA3C,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAmB;AAAS,YAAA,OAAA,QAAA;AAAA,eAAA;QAAe;AAA3C,aAAA,MAAA;AAA4B,aAAA,KAAA;MAAkB;AACnE,aAAAA;IAAA,EARA;AAAa,YAAA,QAAA;AAcb,QAAA,UAAA,2BAAA;AAgBE,eAAAC,SAAqB,KACA,UACA,aACF,KACA,OACA,WACA,OACA,SAAoB;AAPlB,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAgB;AAChB,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAyB;AACzB,YAAA,gBAAA,QAAA;AAAA,wBAAA;QAA4B;AAC9B,YAAA,QAAA,QAAA;AAAA,gBAAA;QAAkB;AAClB,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAkB;AAClB,YAAA,cAAA,QAAA;AAAA,sBAAA;QAAsB;AACtB,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AACtB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAoB;AAPlB,aAAA,MAAA;AACA,aAAA,WAAA;AACA,aAAA,cAAA;AACF,aAAA,MAAA;AACA,aAAA,QAAA;AACA,aAAA,YAAA;AACA,aAAA,QAAA;AACA,aAAA,UAAA;MAAuB;AAE5C,aAAAA;IAAA,EAzBA;AAAa,YAAA,UAAA;AA6Lb,QAAA,eAAA,WAAA;AAAA,eAAAC,gBAAA;AAMY,aAAA,UAAkB;AAMlB,aAAA,aAAqB;AAKxB,aAAA,gBAA8B;AAK9B,aAAA,MAAgC,CAAA;AAKhC,aAAA,SAAmC,CAAA;AAKnC,aAAA,SAAiC,CAAA;AAKjC,aAAA,YAAoC,CAAA;AAKpC,aAAA,OAAgB;AAKhB,aAAA,YAAqB;AAKrB,aAAA,aAAsB,IAAI,QAAO;AAQ9B,aAAA,UAAqB,CAAA;AAEvB,aAAA,QAAmB,CAAA;AA2MpB,aAAA,QAAQ,SAAS,MAAe,KAAY;AACjD,cAAI,KAAK,KAAK,cAAc;AAC5B,cAAI,OAAO,GAAG,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC;AAC1C,cAAI,MAAM,GAAG,OAAO,QAAQ,cAAc,CAAC,KAAK,IAAI,CAAC;AACrD,cAAI,QAAQ,GAAG,OAAO,QAAQ,UAAU,CAAC,GAAG,GAAG;YAC7C,MAAM,KAAK,cAAc,QAAQ,SAAS;YAC1C,iBAAiB,KAAK,cAAc,QAAQ,WAAW;YACvD,cAAc;WACf;AACD,iBAAO;QACT;MA2BF;AA3OS,MAAAA,cAAA,UAAA,QAAP,SAAa,KAAa,UAAmB,aAAoB;AAC/D,YAAI,KAAK,YAAY;AACnB,eAAK,MAAM,KAAK,KAAK,UAAU;;AAEjC,aAAK,aAAa,IAAI,QAAQ,KAAK,UAAU,WAAW;MAC1D;AAEA,aAAA,eAAWA,cAAA,WAAA,OAAG;aAAd,WAAA;AACE,iBAAO,KAAK,WAAW;QACzB;;;;AAMO,MAAAA,cAAA,UAAA,MAAP,WAAA;AACE,aAAK,QAAQ,KAAK,KAAK,UAAU;AACjC,aAAK,aAAa,KAAK,MAAM,IAAG;MAClC;AAMO,MAAAA,cAAA,UAAA,MAAP,SAAW,KAAa,UAAiB;AACvC,aAAK,WAAW,MAAM;AACtB,aAAK,WAAW,YAAY,WAAW,MAAM,KAAK,UAAU,GAAG;AAC/D,aAAK,WAAW,QAAQ;MAC1B;AAMO,MAAAA,cAAA,UAAA,QAAP,WAAA;AACE,aAAK,IAAI,IAAI,IAAI;AACjB,aAAK,WAAW,QAAQ;MAC1B;AAEA,aAAA,eAAcA,cAAA,WAAA,SAAK;aAAnB,WAAA;AACE,iBAAO,KAAK,WAAW;QACzB;;;;AAEA,aAAA,eAAWA,cAAA,WAAA,SAAK;aAIhB,WAAA;AACE,iBAAO,KAAK,WAAW;QACzB;aANA,SAAiB,OAAa;AAC5B,eAAK,WAAW,UAAU;QAC5B;;;;AASO,MAAAA,cAAA,UAAA,YAAP,SAAiB,IAAY,MAAY;AACvC,eAAO,OAAO,MAAM,mBAAmB,EAAE;MAC3C;AAKO,MAAAA,cAAA,UAAA,YAAP,SAAiB,KAAW;AAC1B,eAAO,MAAM,MAAM;MACrB;AAOU,MAAAA,cAAA,UAAA,WAAV,SAAmB,IAAU;AAC3B,eAAO,aAAa,GAAG,QAAQ,OAAO,GAAG;MAC3C;AAOU,MAAAA,cAAA,UAAA,eAAV,SAAuB,GAAS;AAC9B,eAAO,EAAE,SAAQ;MACnB;AAMO,MAAAA,cAAA,UAAA,UAAP,WAAA;AACE,YAAI,KAAK,WAAW,OAAO,MAAM;AAC/B,eAAK;AACL,eAAK,IAAI,KAAK,aAAa,KAAK,OAAO,GAAG,KAAK;;MAEnD;AAMO,MAAAA,cAAA,UAAA,WAAP,WAAA;AACE,aAAK,QAAQ;AACb,aAAK,IAAI,MAAM,IAAI;AACnB,aAAK,WAAW,QAAQ;MAC1B;AAMO,MAAAA,cAAA,UAAA,SAAP,SAAc,OAAsB;AAAtB,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAsB;AAClC,YAAI,OAAO;AACT,eAAK,QAAO;AACZ,iBAAO,KAAK,QAAO;;AAErB,YAAM,KAAK,KAAK;AAChB,YAAI,GAAG,YAAY,CAAC,GAAG,OAAO;AAC5B,cAAI,GAAG,aAAa;AAClB,iBAAK,QAAO;;AAEd,cAAI,GAAG,KAAK;AACV,mBAAO,KAAK,QAAO;;;AAGvB,eAAO;MACT;AAMO,MAAAA,cAAA,UAAA,WAAP,WAAA;AACE,aAAK,UAAU,CAAA;AACf,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,SAAQ;MACf;AAKO,MAAAA,cAAA,UAAA,QAAP,SAAa,QAAkB;AAAlB,YAAA,WAAA,QAAA;AAAA,mBAAA;QAAkB;AAC7B,aAAK,SAAQ;AACb,aAAK,UAAU,KAAK,aAAa;AACjC,aAAK,YAAY,CAAA;AACjB,aAAK,SAAS,CAAA;MAChB;AAKO,MAAAA,cAAA,UAAA,gBAAP,SAAqB,MAA6B;AAChD,aAAK,UAAU,CAAA;AACf,aAAK,QAAQ,CAAA;AACb,aAAK,SAAQ;AACb,aAAK,aAAa,IAAI,QAAQ,IAAI,QAAW,MAAS;AACtD,aAAK,SAAS,CAAA;AACd,aAAK,MAAM,CAAA;AACX,aAAK,UAAU,KAAK;AACpB,aAAK,OAAO;AACZ,YAAM,YAAY,KAAK,UAAU;AACjC,YAAI,WAAW;AACb,eAAK,YAAY;AACjB,eAAK,UAAU,UAAU;;MAE7B;AAKO,MAAAA,cAAA,UAAA,iBAAP,SAAsB,MAA6B;AACjD,YAAI,KAAK,MAAM;AACb,eAAK,UAAU,YAAY;YACzB,OAAO,KAAK,MAAK;YACjB,SAAS,KAAK;;;AAGlB,YAAI,CAAC,KAAK,WAAW;AACnB,eAAK,aAAa,KAAK;;AAEzB,eAAO,OAAO,KAAK,QAAQ,KAAK,GAAG;AACnC,eAAO,OAAO,KAAK,WAAW,KAAK,MAAM;MAC3C;AAKO,MAAAA,cAAA,UAAA,WAAP,SAAgB,MAAe,KAAY;AACzC,YAAI,CAAC,IAAI,WAAW,KAAK,WAAW,OAChC,KAAK,WAAW,OAAO,MAAM;AAC/B,iBAAO;;AAET,YAAI,MAAM,KAAK,QAAO;AACtB,YAAI,QAAQ,KAAK,MAAM,MAAM,GAAG;AAChC,eAAO;MACT;AAqBQ,MAAAA,cAAA,UAAA,SAAR,WAAA;AACE,aAAK,WAAW,QAAQ,KAAK,SAC3B,KAAK,cAAc,QAAQ,aAAa,IACrC,KAAK,SAAS,KAAK,WAAW,MAAO,KAAK,WAAW,GAAG;MAC/D;AAMQ,MAAAA,cAAA,UAAA,UAAR,WAAA;AACE,aAAK,OAAM;AACX,YAAI,KAAK,OAAO;AACd,eAAK,OAAO,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK;;AAEhF,YAAI,MAAM,IAAI,eAAA,QAAU,YAAY,KAAK,WAAW,YAAY,KAAK,CAAA,GAC7C,KAAK,aAAa,EAAE,IAAG;AAC/C,eAAO,KAAK,cAAc,YAAY,OAAO,QAAQ,OAAO,CAAC,GAAG,GACnB,EAAC,IAAI,KAAK,WAAW,MAAK,CAAC;MAC1E;AAEF,aAAAA;IAAA,EA9SA;AAAa,YAAA,eAAA;AAsTb,QAAA,SAAA,SAAA,QAAA;AAA4B,gBAAAC,SAAA,MAAA;AAA5B,eAAAA,UAAA;;MAcA;AATS,MAAAA,QAAA,UAAA,UAAP,WAAA;MAAkB;AAKX,MAAAA,QAAA,UAAA,SAAP,WAAA;AACE,eAAO,CAAC,KAAK,WAAW,MAAM,OAAO,OAAA,UAAM,OAAM,KAAA,IAAA;MACnD;AAEF,aAAAA;IAAA,EAd4B,YAAY;AAA3B,YAAA,SAAA;AAuBb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAcA;AATS,MAAAA,SAAA,UAAA,WAAP,SAAgB,MAAe,KAAY;AACzC,YAAI,CAAC,IAAI,WAAW,KAAK,QAAQ,KAC/B,SAAS,GAAU;AAAI,iBAAO,EAAE;QAAU,CAAC,GAAG;AAC9C,iBAAO;;AAET,YAAI,MAAM,KAAK,OAAO,IAAI;AAC1B,eAAO,KAAK,MAAM,MAAM,GAAG;MAC7B;AAEF,aAAAA;IAAA,EAd6B,YAAY;AAA5B,YAAA,UAAA;AA0Bb,QAAiB;AAAjB,KAAA,SAAiBC,cAAW;AAE1B,UAAI,cAAc,oBAAI,IAAuB;QAC3C,CAAC,QAAQ,MAAM;QACf,CAAC,OAAO,OAAO;OAChB;AAED,UAAI,cAAc;AAMP,MAAAA,aAAA,UAAsB;QAE/B,MAAM;QAGN,SAAS;QAET,WAAW;QAKX,aAAa;QAEb,uBAAuB;;AASd,MAAAA,aAAA,MAAM,SAAS,MAAc,QAAiB;AACvD,oBAAY,IAAI,MAAM,MAAM;MAC9B;AAOW,MAAAA,aAAA,UAAU,SAAS,MAAiC;;;AAC7D,mBAAkB,KAAA,SAAA,OAAO,KAAK,IAAI,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,MAAG,GAAA;AACZ,YAAAA,aAAY,IAAI,KAAK,KAAK,GAAG,CAAC;;;;;;;;;;;MAElC;AAQW,MAAAA,aAAA,SAAS,SAAS,MAAY;AACvC,YAAI,SAAS,YAAY,IAAI,IAAI,KAAK,YAAY,IAAI,WAAW;AACjE,YAAI,CAAC,QAAQ;AACT,gBAAM,MAAM,oBAAoB;;AAEpC,eAAO,IAAI,OAAM;MACnB;AAOW,MAAAA,aAAA,aAAa,SAAS,MAAY;AAC3C,sBAAc;MAChB;AAMW,MAAAA,aAAA,aAAa,WAAA;AACtB,eAAOA,aAAY,OAAO,WAAW;MACvC;IAEF,GAlFiB,cAAA,QAAA,gBAAA,QAAA,cAAW,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1jB5B,QAAA,uBAAA;AACA,QAAA,oBAAA;AASA,QAAiB;AAAjB,KAAA,SAAiBC,aAAU;AAEzB,UAAI,OAA+B,oBAAI,IAAG;AAQ/B,MAAAA,YAAA,WAAW,SAAS,KAAc;AAC3C,aAAK,IAAI,IAAI,MAAM,GAAG;MACxB;AASW,MAAAA,YAAA,SAAS,SAAS,MAAY;AACvC,eAAO,KAAK,IAAI,IAAI;MACtB;IAEF,GAzBiB,aAAA,QAAA,eAAA,QAAA,aAAU,CAAA,EAAA;AA+B3B,QAAA,aAAA,WAAA;AAAA,eAAAC,cAAA;AAEU,aAAA,iBAA6C,IAAI,qBAAA,gBAAe;AAChE,aAAA,YAA0B,IAAI,kBAAA,aAAY;MAkHpD;AA1GS,MAAAA,YAAA,UAAA,MAAP,SAAW,MAAgB,UAChB,UAAkD;;AAAlD,YAAA,aAAA,QAAA;AAAA,qBAAmB,qBAAA,gBAAgB;QAAe;;AAC3D,mBAAmB,KAAA,SAAA,KAAK,MAAK,EAAG,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAtC,gBAAM,SAAI,GAAA;AACb,gBAAI,MAAM,WAAW,OAAO,MAAI;AAChC,gBAAI,CAAC,KAAK;AACR,mBAAK,KAAK,mBAAmB,SAAO,sBAAsB;AAC1D;;AAEF,iBAAK,eAAe,IAAI,KAAK,QAAQ;;;;;;;;;;;AAEvC,YAAI,UAAU;AACZ,eAAK,UAAU,IAAI,UAAU,QAAQ;;MAEzC;AAOO,MAAAA,YAAA,UAAA,QAAP,SAAa,OAAiB;;;AAC5B,mBAAwB,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,gBAAM,MAAG,GAAA,MAAA;AACjB,gBAAM,SAAS,IAAI,MAAM,KAAK;AAC9B,gBAAI,QAAQ;AACV,qBAAO;;;;;;;;;;;;AAGP,YAAA,KAAA,OAAgB,OAAK,CAAA,GAApB,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AAChB,cAAM,KAAK,KAAK,SAAS,EAAE,CAAC,EAAE,KAAK,KAAK,MAAM;MAChD;AASO,MAAAA,YAAA,UAAA,SAAP,SAAiB,QAAc;AAC7B,YAAI,MAAM,KAAK,WAAW,MAAM;AAChC,eAAO,MAAM,IAAI,OAAO,MAAM,IAAI;MACpC;AAUO,MAAAA,YAAA,UAAA,WAAP,SAAgB,QAAc;AAC5B,eAAO,KAAK,WAAW,MAAM,IAAI,OAAO;MAC1C;AAMO,MAAAA,YAAA,UAAA,WAAP,WAAA;;AACE,YAAI,QAAQ,CAAA;;AACZ,mBAAwB,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,gBAAM,MAAG,GAAA,MAAA;AACjB,kBAAM,KAAK,IAAI,IAAI;;;;;;;;;;;AAErB,eAAO,MAAM,KAAK,IAAI;MACxB;AAQO,MAAAA,YAAA,UAAA,aAAP,SAAkB,QAAc;;;AAC9B,mBAAwB,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,gBAAM,MAAG,GAAA,MAAA;AACjB,gBAAI,IAAI,SAAS,MAAM,GAAG;AACxB,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAQO,MAAAA,YAAA,UAAA,WAAP,SAAgB,MAAY;;;AAC1B,mBAAwB,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAnC,gBAAM,MAAG,GAAA,MAAA;AACjB,gBAAI,IAAI,SAAS,MAAM;AACrB,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAOQ,MAAAA,YAAA,UAAA,OAAR,SAAa,SAAe;AAC1B,gBAAQ,IAAI,wBAAwB,OAAO;MAC7C;AAEF,aAAAA;IAAA,EArHA;AAAa,YAAA,aAAA;AAwHb,QAAA,cAAA,WAAA;AAAA,eAAAC,eAAA;AAEU,aAAA,MAAM,oBAAI,IAAG;MAgEvB;AA1DS,MAAAA,aAAA,UAAA,MAAP,SAAW,UAAyB,WACzB,UAAkD;;AAAlD,YAAA,aAAA,QAAA;AAAA,qBAAmB,qBAAA,gBAAgB;QAAe;;AAC3D,mBAAkB,KAAA,SAAA,OAAO,KAAK,QAAQ,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApC,gBAAM,MAAG,GAAA;AACZ,gBAAI,SAAO;AACX,gBAAI,aAAa,KAAK,IAAI,MAAI;AAC9B,gBAAI,CAAC,YAAY;AACf,2BAAa,IAAI,WAAU;AAC3B,mBAAK,IAAI,QAAM,UAAU;;AAE3B,uBAAW,IAAI,SAAS,MAAI,GAAG,UAAU,MAAI,GAAG,QAAQ;;;;;;;;;;;MAE5D;AAQO,MAAAA,aAAA,UAAA,MAAP,SAAW,MAAmB,YAAsB;AAClD,aAAK,IAAI,IAAI,MAAM,UAAU;MAC/B;AAQO,MAAAA,aAAA,UAAA,MAAP,SAAW,MAAiB;AAC1B,eAAO,KAAK,IAAI,IAAI,IAAI;MAC1B;AAQO,MAAAA,aAAA,UAAA,WAAP,SAAgB,MAAY;;;AAC1B,mBAAsB,KAAA,SAAA,KAAK,IAAI,OAAM,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAApC,gBAAM,UAAO,GAAA;AAChB,gBAAI,MAAM,QAAQ,SAAS,IAAI;AAC/B,gBAAI,KAAK;AACP,qBAAO;;;;;;;;;;;;AAGX,eAAO;MACT;AAOO,MAAAA,aAAA,UAAA,OAAP,WAAA;AACE,eAAO,KAAK,IAAI,KAAI;MACtB;AAEF,aAAAA;IAAA,EAlEA;AAAa,YAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjKb,QAAA,eAAA;AACA,QAAA,kBAAA;AACA,QAAA,oBAAA;AAEA,QAAA,uBAAA;AACA,QAAA,YAAA;AAaA,QAAA,gBAAA,WAAA;AAyHE,eAAAC,eAA6B,MACA,SACA,UACA,OACA,MACA,SACA,OACA,eACA,gBACA,YACA,cACF,UACE,QAAc;AAXd,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAA2B;AAC3B,YAAA,aAAA,QAAA;AAAA,qBAAA,CAAA;QAA6B;AAC7B,YAAA,UAAA,QAAA;AAAA,kBAAA,CAAA;QAA2B;AAC3B,YAAA,SAAA,QAAA;AAAA,iBAAA,CAAA;QAAqB;AACrB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AACxB,YAAA,UAAA,QAAA;AAAA,kBAAA,CAAA;QAAgC;AAChC,YAAA,kBAAA,QAAA;AAAA,0BAAA,CAAA;QAAiC;AACjC,YAAA,mBAAA,QAAA;AAAA,2BAAA,CAAA;QAAkC;AAClC,YAAA,eAAA,QAAA;AAAA,uBAAA;QAAwC;AACxC,YAAA,iBAAA,QAAA;AAAA,yBAAA;QAA4C;AAV5C,aAAA,OAAA;AACA,aAAA,UAAA;AACA,aAAA,WAAA;AACA,aAAA,QAAA;AACA,aAAA,OAAA;AACA,aAAA,UAAA;AACA,aAAA,QAAA;AACA,aAAA,gBAAA;AACA,aAAA,iBAAA;AACA,aAAA,aAAA;AACA,aAAA,eAAA;AACF,aAAA,WAAA;AACE,aAAA,SAAA;AAE3B,aAAK,UAAU,OAAO,OACpB,EAAC,WAAW,CAAA,GAAI,WAAW,CAAA,GAAI,OAAO,CAAA,GAAI,aAAa,CAAA,EAAE,GAAG,OAAO;MACvE;AAhIe,MAAAA,eAAA,gBAAf,SAAgC,MAAyB,UAAgB;AACvE,eAAO,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,MAAM,QAAQ;MACrD;AAQe,MAAAA,eAAA,UAAf,SAAuB,MACA,QAYc;AAbrC,YAAA,QAAA;AACuB,YAAA,WAAA,QAAA;AAAA,mBAAA,CAAA;QAYc;AACnC,YAAI,WAAW,OAAO,YAAY,qBAAA,gBAAgB;AAClD,YAAI,OAAO,OAAO,OAAO,KAAK,cAAc,OAAO,MAAM,QAAQ,IAAI;AACrE,YAAI,OAAO,OAAO,SAAS,KAAK,cAAc,OAAO,QAAQ,QAAQ,IAAI;AACzE,YAAI,iBAAiB,OAAO,iBAAiB,CAAA,GAAI,IAC/C,SAAA,KAAG;AAAI,iBAAA,MAAK,cAAc,KAAK,QAAQ;QAAhC,CAAiC;AAC1C,YAAI,kBAAkB,OAAO,kBAAkB,CAAA,GAAI,IACjD,SAAA,MAAI;AAAI,iBAAA,MAAK,cAAc,MAAM,QAAQ;QAAjC,CAAkC;AAC5C,YAAI,SAAS,OAAO,UAAU;AAC9B,eAAO,IAAIA,eACT,MACA,OAAO,WAAW,CAAA,GAClB,OAAO,YAAY,CAAA,GACnB,OAAO,SAAS,CAAA,GAChB,OAAO,QAAQ,CAAA,GACf,OAAO,WAAW,CAAA,GAClB,OAAO,SAAS,CAAA,GAChB,eAAe,gBAAgB,MAAM,MAAM,UAC3C,MAAM;MAEV;AAwBc,MAAAA,eAAA,SAAd,SAAqB,MACA,QAYc;AAZd,YAAA,WAAA,QAAA;AAAA,mBAAA,CAAA;QAYc;AACjC,YAAI,gBAAgBA,eAAc,QAAQ,MAAM,MAAM;AACtD,6BAAqB,IAAI,MAAM,aAAa;AAC5C,eAAO;MACT;AAQc,MAAAA,eAAA,QAAd,SAAoB,QAYa;AAZb,YAAA,WAAA,QAAA;AAAA,mBAAA,CAAA;QAYa;AAC/B,eAAOA,eAAc,QAAQ,IAAI,MAAM;MACzC;AA4BA,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK,aAAa,KAAK,WAAW,CAAC,IAAI;QAChD;;;;AAMA,aAAA,eAAWA,eAAA,WAAA,UAAM;aAAjB,WAAA;AACE,iBAAO,KAAK,eAAe,KAAK,aAAa,CAAC,IAAI;QACpD;;;;AAEF,aAAAA;IAAA,EA3JA;AAAa,YAAA,gBAAA;AA8Jb,QAAiB;AAAjB,KAAA,SAAiBC,uBAAoB;AAEnC,UAAI,OAAmC,oBAAI,IAAG;AAQnC,MAAAA,sBAAA,MAAM,SAAS,MAAc,KAAkB;AACxD,aAAK,IAAI,MAAM,GAAG;MACpB;AASW,MAAAA,sBAAA,MAAM,SAAS,MAAY;AACpC,eAAO,KAAK,IAAI,IAAI;MACtB;AAKW,MAAAA,sBAAA,OAAO,WAAA;AAChB,eAAO,KAAK,KAAI;MAClB;IAEF,GAhCiB,uBAAA,QAAA,yBAAA,QAAA,uBAAoB,CAAA,EAAA;AAwCrC,QAAA,sBAAA,WAAA;AA6DE,eAAAC,qBAAY,UAAyC,SAA2B;;AAA3B,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAqB,KAAK;QAAC;AAvDtE,aAAA,aAA2B,IAAI,kBAAA,aAAY;AAM3C,aAAA,eAA6B,IAAI,kBAAA,aAAY;AAM7C,aAAA,iBAAiD,IAAI,qBAAA,gBAAe;AAKpE,aAAA,UAAoB,CAAA;AAMvB,aAAA,WAAwB,IAAI,gBAAA,YAAW;AAMvC,aAAA,QAAyB,CAAA;AAMzB,aAAA,OAAmB,CAAA;AAMnB,aAAA,UAAsB,CAAA;AAMtB,aAAA,QAA+B,CAAA;AASpC,aAAK,UAAU;;AACf,mBAAkB,KAAA,SAAA,SAAS,MAAK,EAAG,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAM,MAAG,GAAA;AACZ,iBAAK,WAAW,GAAG;;;;;;;;;;;;AAErB,mBAA+C,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA3D,gBAAA,KAAA,GAAA,OAAO,SAAM,GAAA,MAAY,WAAQ,GAAA;AACxC,iBAAK,OAAO,QAAQ,QAAQ;;;;;;;;;;;MAEhC;AAKO,MAAAA,qBAAA,UAAA,OAAP,WAAA;AACE,aAAK,WAAW,QAAQ,IAAI;MAC9B;AAMO,MAAAA,qBAAA,UAAA,SAAP,SAAc,KAAuB;;AACnC,aAAK,aAAa,QAAQ,MAAM,GAAG;;AACnC,mBAAqB,KAAA,SAAA,KAAK,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAArC,gBAAM,SAAM,GAAA;AACf,iBAAK,WAAW,KAAK,OAAO,IAAI;;;;;;;;;;;MAEpC;AAMO,MAAAA,qBAAA,UAAA,aAAP,SAAkB,KAAgC;AAChD,YAAM,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,CAAC;AAClD,YAAM,OAAO,KAAK,WAAW,IAAI;AACjC,gBAAQ,KAAK,eAAe,IAAI,MAAM,OAAO,QAAQ,WAAW,KAAK,WAAW,IAAI,CAAC,CAAC;MACxF;AAUO,MAAAA,qBAAA,UAAA,MAAP,SAAW,MAAc,KAAyB,SAAwB;;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA,CAAA;QAAwB;AACxE,YAAM,SAAS,KAAK,WAAW,IAAI;AACnC,aAAK,OAAO,MAAM;AAClB,aAAK,eAAe,IAAI,QAAQ,OAAO,QAAQ;AAC/C,aAAK,KAAI;AACT,YAAM,SAAS,IAAI;AACnB,eAAO,YAAY,YAAY,OAAO,KAAK;;AAC3C,mBAAmB,KAAA,SAAA,OAAO,KAAK,OAAO,KAAK,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAM,OAAI,GAAA;AACb,mBAAO,YAAY,aAAa,MAAM,OAAO,MAAM,IAAI,CAAC;;;;;;;;;;;AAE1D,kBAAA,YAAY,QAAQ,OAAO,IAAI;AAC/B,SAAA,GAAA,aAAA,gBAAe,OAAO,SAAS,OAAO,OAAO;AAC7C,SAAA,GAAA,aAAA,aAAY,OAAO,SAAS,OAAO;AACnC,aAAK,WAAW,KAAK,MAAM;AAC3B,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,MAAM,GAAG;;MAE3B;AAQU,MAAAA,qBAAA,UAAA,aAAV,SAAqB,MAAY;AAC/B,YAAM,SAAS,qBAAqB,IAAI,IAAI;AAC5C,YAAI,UAAU,KAAK,QAAQ,QAAQ,OAAO,MAAM,IAAI,GAAG;AACrD,gBAAM,MAAM,WAAA,OAAW,MAAI,mCAAA,CAAmC;;AAEhE,eAAO;MACT;AAOO,MAAAA,qBAAA,UAAA,SAAP,SAAc,QAAuB,UAAiB;AACpD,mBAAW,YAAY,OAAO;AAC9B,YAAI,OAAO,YAAY;AACrB,eAAK,WAAW,IAAI,OAAO,WAAW,CAAC,GAAG,OAAO,WAAW,CAAC,CAAC;;AAEhE,YAAI,OAAO,cAAc;AACrB,eAAK,aAAa,IAAI,OAAO,aAAa,CAAC,GAAG,OAAO,aAAa,CAAC,CAAC;;AAExE,aAAK,SAAS,IAAI,OAAO,SAAS,OAAO,UAAU,QAAQ;AAC3D,eAAO,OAAO,KAAK,OAAO,OAAO,KAAK;AACtC,eAAO,OAAO,KAAK,MAAM,OAAO,IAAI;AACpC,SAAA,GAAA,aAAA,gBAAe,KAAK,SAAS,OAAO,OAAO;AAC3C,eAAO,OAAO,KAAK,OAAO,OAAO,KAAK;MACxC;AAOQ,MAAAA,qBAAA,UAAA,aAAR,SAAmB,KAAyB,QAAqB;;;AAC/D,mBAA8B,KAAA,SAAA,OAAO,aAAa,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAzC,gBAAA,KAAA,OAAA,GAAA,OAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AACvB,gBAAI,WAAW,IAAI,KAAK,QAAQ;;;;;;;;;;;;AAElC,mBAA+B,KAAA,SAAA,OAAO,cAAc,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA3C,gBAAA,KAAA,OAAA,GAAA,OAAA,CAAA,GAAC,OAAI,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AACxB,gBAAI,YAAY,IAAI,MAAM,QAAQ;;;;;;;;;;;MAEtC;AAEF,aAAAA;IAAA,EA9KA;AAAa,YAAA,sBAAA;;;;;;;;;;ACrNb,QAAAC,UAAA,WAAA;AAQE,eAAAA,QAAoB,SAAyB,OACzB,aAAuB;AADvB,aAAA,UAAA;AAAyB,aAAA,QAAA;AACzB,aAAA,cAAA;MACpB;AAEA,aAAA,eAAWA,QAAA,WAAA,UAAM;aAAjB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEA,aAAA,eAAWA,QAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEA,aAAA,eAAWA,QAAA,WAAA,cAAU;aAArB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEF,aAAAA;IAAA,EAxBA;AAAa,YAAA,SAAAA;AA2Bb,QAAA,QAAA,WAAA;AAQE,eAAAC,OAAoB,SAAyB,OACzB,OAAkB;AAAlB,YAAA,UAAA,QAAA;AAAA,kBAAA,CAAA;QAAkB;AADlB,aAAA,UAAA;AAAyB,aAAA,QAAA;AACzB,aAAA,QAAA;MACpB;AAEA,aAAA,eAAWA,OAAA,WAAA,UAAM;aAAjB,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEA,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEA,aAAA,eAAWA,OAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAEF,aAAAA;IAAA,EAxBA;AAAa,YAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChCb,QAAA,cAAA;AACA,QAAA,kBAAA;AAkDA,aAAgB,YAAY,QAAmB;AAC7C,aAAO,WAAW,SAAS,OAAO;IACpC;AAFA,YAAA,cAAA;AAQA,QAAA,oBAAA,WAAA;AAQE,eAAAC,mBAAoB,OAAuB,SAAoB;AAA3C,aAAA,QAAA;AAAuB,aAAA,UAAA;AACzC,wBAAA,WAAW,SAAS,IAAI;MAC1B;AAMA,aAAA,eAAWA,mBAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO,KAAK;QACd;;;;AAYO,MAAAA,mBAAA,UAAA,YAAP,SAAiB,QAAc;AAC7B,eAAO,KAAK,SAAS,MAAM,IAAI,KAAK,SAAS;MAC/C;AAMO,MAAAA,mBAAA,UAAA,QAAP,SAAa,IAAyB;YAAzB,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACvB,YAAI,SAAS,KAAK,UAAU,MAAM;AAClC,YAAI,SAAS,KAAK,OAAO,MAAM;AAC/B,eAAQ,UAAU,SAAU,YAAY,OAAO,KAAK,MAAa,CAAC,IAAI;MACxE;AAGA,aAAA,eAAWA,mBAAA,WAAA,UAAM;aAIjB,WAAA;AACE,iBAAO,KAAK;QACd;aANA,SAAkB,QAAmB;AACnC,eAAK,UAAU;QACjB;;;;AAaF,aAAAA;IAAA,EA5DA;AAAsB,YAAA,oBAAA;AAmEtB,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAS7B,eAAAA,WAAY,MAAc,QAA6B,SAAe;AAAtE,YAAA,QACE,OAAA,KAAA,MAAM,MAAM,MAAM,KAAC;AADkC,cAAA,UAAA;;MAEvD;AAMO,MAAAA,WAAA,UAAA,WAAP,SAAgB,QAAc;AAC5B,eAAO,KAAK,QAAQ,KAAK,MAAM;MACjC;AAMO,MAAAA,WAAA,UAAA,SAAP,SAAc,QAAc;AAC1B,eAAO,KAAK,SAAS,MAAM,IAAI,SAAS;MAC1C;AAEF,aAAAA;IAAA,EA7B+B,iBAAiB;AAAnC,YAAA,YAAA;AAsCb,QAAA,mBAAA,SAAA,QAAA;AAAkD,gBAAAC,mBAAA,MAAA;AAAlD,eAAAA,oBAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAEU,cAAA,MAAsB,oBAAI,IAAG;;MAiCvC;AA5BS,MAAAA,kBAAA,UAAA,SAAP,SAAc,QAAc;AAC1B,eAAO,KAAK,IAAI,IAAI,MAAM;MAC5B;AAKO,MAAAA,kBAAA,UAAA,WAAP,SAAgB,QAAc;AAC5B,eAAO,KAAK,IAAI,IAAI,MAAM;MAC5B;AAOO,MAAAA,kBAAA,UAAA,MAAP,SAAW,QAAgB,QAAS;AAClC,aAAK,IAAI,IAAI,QAAQ,MAAM;MAC7B;AAMO,MAAAA,kBAAA,UAAA,SAAP,SAAc,QAAc;AAC1B,aAAK,IAAI,OAAO,MAAM;MACxB;AAEF,aAAAA;IAAA,EAnCkD,iBAAiB;AAA7C,YAAA,mBAAA;AA4CtB,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAQhC,eAAAA,cAAY,MAAc,QACd,MAAsD;;AADlE,YAAA,QAEE,OAAA,KAAA,MAAM,MAAM,MAAM,KAAC;;AACnB,mBAAkB,KAAA,SAAA,OAAO,KAAK,IAAI,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,MAAG,GAAA;AACZ,gBAAI,QAAQ,KAAK,GAAG;AAChB,gBAAA,KAAA,OAAiB,OAAO,UAAW,WAAY,CAAC,OAAO,IAAI,IAAI,OAAK,CAAA,GAAnE,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAChB,gBAAI,YAAY,IAAI,YAAA,OAAO,KAAK,MAAM,KAAK;AAC3C,kBAAK,IAAI,KAAK,SAAS;;;;;;;;;;;;MAE3B;AAEF,aAAAA;IAAA,EAnBkC,gBAAgB;AAArC,YAAA,eAAA;AA4Bb,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAAlC,eAAAA,gBAAA;;MASA;AAJS,MAAAA,cAAA,UAAA,QAAP,SAAa,IAAyB;YAAzB,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACvB,eAAO,OAAA,UAAM,MAAK,KAAA,MAAC,CAAC,KAAK,OAAO,MAAM,CAAC;MACzC;AAEF,aAAAA;IAAA,EATkC,YAAY;AAAjC,YAAA,eAAA;AAkBb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAS5B,eAAAA,UAAY,MACA,MACA,aAAwC;;AAFpD,YAAA,QAGE,OAAA,KAAA,MAAM,MAAM,IAAI,KAAC;;AACjB,mBAAkB,KAAA,SAAA,OAAO,KAAK,IAAI,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAhC,gBAAM,MAAG,GAAA;AACZ,gBAAI,QAAQ,KAAK,GAAG;AAChB,gBAAA,KAAA,OAAoB,OAAO,UAAW,WAAY,CAAC,KAAK,IAAI,KAAK,GAAhE,OAAI,GAAA,CAAA,GAAK,QAAK,GAAA,MAAA,CAAA;AACnB,gBAAI,YAAY,IAAI,YAAA,MAAM,KAAK,YAAY,IAAc,GAAG,KAAK;AACjE,kBAAK,IAAI,KAAK,SAAS;;;;;;;;;;;;MAE3B;AAMO,MAAAA,UAAA,UAAA,YAAP,SAAiB,QAAc;AAC7B,YAAI,QAAQ,KAAK,OAAO,MAAM;AAC9B,eAAO,QAAQ,MAAM,OAAO;MAC9B;AAMO,MAAAA,UAAA,UAAA,QAAP,SAAa,IAAyB;YAAzB,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACvB,YAAI,QAAQ,KAAK,OAAO,MAAM;AAC9B,YAAI,SAAS,KAAK,UAAU,MAAM;AAClC,YAAI,CAAC,SAAS,CAAC,QAAQ;AACrB,iBAAO;;AAET,eAAO,YAAY,OAAM,MAAA,QAAA,cAAA,CAAC,KAAK,MAAM,MAAM,GAAA,OAAK,MAAM,IAAI,GAAA,KAAA,CAAA,CAAA;MAC5D;AAEF,aAAAA;IAAA,EA3C8B,gBAAgB;AAAjC,YAAA,WAAA;AAoDb,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MAkBA;AAbS,MAAAA,YAAA,UAAA,QAAP,SAAa,IAAyB;YAAzB,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACvB,YAAI,QAAQ,KAAK,OAAO,MAAM;AAC9B,YAAI,SAAS,KAAK,UAAU,MAAM;AAClC,YAAI,CAAC,SAAS,CAAC,QAAQ;AACrB,iBAAO;;AAET,YAAI,cAAc,IAAI;AACtB,YAAI,YAAY,OAAO;AACvB,YAAI,SAAS,OAAM,MAAA,QAAA,cAAA,CAAC,KAAK,OAAO,MAAM,MAAM,GAAA,OAAK,MAAM,IAAI,GAAA,KAAA,CAAA;AAC3D,YAAI,YAAY;AAChB,eAAO,YAAY,MAAM;MAC3B;AAEF,aAAAA;IAAA,EAlBgC,QAAQ;AAA3B,YAAA,aAAA;AA6Bb,QAAA,iBAAA,SAAA,QAAA;AAAoC,gBAAAC,iBAAA,MAAA;AAUlC,eAAAA,gBAAY,MACA,QACA,MACA,aAAwC;AAHpD,YAAA,QAIE,OAAA,KAAA,MAAM,MAAM,MAAM,WAAW,KAAC;AAC9B,cAAK,SAAS;;MAChB;AAMO,MAAAA,gBAAA,UAAA,QAAP,SAAa,IAAyB;YAAzB,KAAA,OAAA,IAAA,CAAA,GAAC,MAAG,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACvB,YAAI,QAAQ,KAAK,OAAO,MAAM;AAC9B,YAAI,YAAY,KAAK,UAAU,MAAM;AACrC,YAAI,CAAC,SAAS,CAAC,WAAW;AACxB,iBAAO;;AAET,eAAO,YAAY,KAAK,OAAO,KAAK,MAAM,QAAQ,WAAW,MAAM,IAAI,CAAC;MAC1E;AAEF,aAAAA;IAAA,EA/BoC,QAAQ;AAA/B,YAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/Ub,QAAA,kBAAA;AAEA,QAAA,gBAAA;AACA,QAAA,eAAA;AAEA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AAGA,QAAA,iBAAA;AAMA,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAK7B,eAAAA,WAAY,SAAkC,QAAe;AAA7D,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AAD8B,cAAA,SAAA;;MAE9C;AAMA,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,MAAM,GAAG;AACvB,cAAI,OAAO,KAAK,MAAK;AACrB,cAAI,CAAC,KAAK,OAAO,SAAS;AACxB,mBAAO,KAAK,QAAQ,cAAc,KAAK,SAAS,MAAM,KAAK,GAAG;;AAEhE,iBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,IAAI,CAAC,GAAG,IAAI;;AAElD,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAEF,aAAAA;IAAA,EAvC+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AA8Cb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MAiBA;AAZE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EAjB8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AAuBb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MAuCA;AAxBE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,OAAO,GAAG;AAExB,cAAI,MAAM,KAAK,MAAK;AACpB,cAAM,OAAO,KAAK,OAAO,QAAQ,WAAW,CAAC,GAAG,CAAC;AACjD,iBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,IAAI,CAAC,GAAG,IAAI;;AAElD,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAhCiB,MAAAA,UAAA,SAAS,OAAO,OAAO,OAAO,OAAO,eAAA,SAAS,MAAM,GAAG;QAEtE,QAAQ;UAAC;UACA;QAAyC;OACnD;AA6BH,aAAAA;MAvC8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AA6Cb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MAiBA;AAZE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EAjB+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AAuBb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MAsBA;AAjBE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAe;AAC1B,YAAA,KAAA,OAAe,KAAK,KAAK,CAAC,GAAC,CAAA,GAA1B,OAAI,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AACf,YAAI,CAAC,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,cAAA,QAAS,OAAO,MAAM,MAAM,GAAG;AAEtE,cAAM,OAAO,KAAK,OAAO,QAAQ,QAAQ,CAAC,MAAM,IAAI,CAAC;AACrD,iBAAO,CAAC,CAAC,MAAM,IAAI,GAAG,IAAI;;AAE5B,sBAAA,QAAS,SAAS,MAAO,KAAoB,KAAK,IAAI;AACtD,eAAO,CAAC,CAAC,MAAM,IAAI,GAAG,IAAI;MAC5B;AACF,aAAAA;IAAA,EAtB+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AA6Bb,QAAA,aAAA,SAAA,QAAA;AAAgC,gBAAAC,aAAA,MAAA;AAAhC,eAAAA,cAAA;;MA6DA;AAzCE,aAAA,eAAWA,YAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,YAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG;AAC9C,iBAAO,eAAA,SAAS;;AAElB,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,KAAK,YAAY,UAAU;AAC5C,YAAI,KAAK,OAAO,KAAK,GAAG;AACtB,cAAI,KAAK,YAAY,QAAQ,GAAG;AAC9B,gBAAI,aAAa,GAAG;AAElB,4BAAA,QAAS,SAAS,KAAK,GAAG,KAAK,YAAY,QAAQ,CAAY;mBAC1D;AAEL,4BAAA,QAAS,YAAY,KAAK,YAAY,QAAQ,GAAc,eAAe,IAAI;AAC/E,kBAAM,OAAO,KAAK,OAAO,QAAQ,QAAQ,CAAC,KAAK,YAAY,QAAQ,GAAc,KAAK,KAAK,CAAC;AAC5F,mBAAK,QAAQ;;;AAGjB,wBAAA,QAAS,SAAS,KAAK,UAAU,KAAK,KAAK;AAC3C,cAAI,KAAK,YAAY,YAAY,KAAK,MAAM;AAE1C,0BAAA,QAAS,YAAY,KAAK,cAAc,KAAK,YAAY,YAAY,CAAa;;AAEpF,cAAM,SAAS,KAAK,QAAQ,OAAO,OAAO,GAAG;AAC7C,iBAAO,CAAC,CAAC,MAAM,GAAG,IAAI;;AAExB,YAAI,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI,EAAE,CAAC,GAAG;AAE5B,cAAM,QAAQ,KAAK,UAAU,CAAC,IAAI,OAAO,KAAK,EAAE,QAAQ,CAAC;AACzD,gBAAA,KAAU,cAAA,QAAQ,KAAA,MAAR,cAAA,SAAQ,cAAA,CAAA,QAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAA,OAAK,MAAM,OAAO,CAAC,CAAC,GAAA,KAAA,CAAA,GAAA;;AAE3D,eAAO;MACT;AAtDiB,MAAAA,YAAA,SAAS,OAAO,OAAO,OAAO,OAAO,eAAA,SAAS,MAAM,GAAG;QAEtE,QAAQ;UAAC;UACA;QAA2C;QAEpD,OAAO;UAAC;UACA;QAAoC;QAE5C,OAAO;UAAC;UACA;QAAkC;OAC3C;AA8CH,aAAAA;MA7DgC,eAAA,QAAQ;AAA3B,YAAA,aAAA;AAmEb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAK5B,eAAAA,UAAY,SAAyB;AAArC,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,YAAY,QAAQ,QAAQ;;MACnC;AAKA,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,MAAM,GAAG;AAEvB,gBAAM,IAAI,cAAA,QACR,kBAAkB,uBAAuB,KAAK,QAAO,CAAE;;AAE3D,YAAI,KAAK,SAAS;AAEhB,cAAI,MAAM,KAAK,OAAO,QACA,SAAS,CAAC,KAAK,YAAY,KAAK,GAAc,KAAK,MAAM,KAAK,CAAC,CAAC;AACtF,cAAI,KAAK,YAAY,WAAW,KAAK,MAAM;AAEzC,0BAAA,QAAS,aAAa,KAAK,iBACL,KAAK,YAAY,WAAW,CAAW;;AAE/D,cAAI,KAAK,YAAY,MAAM,KAAK,KAAK,YAAY,OAAO,GAAG;AAEzD,0BAAA,QAAS,YAAY,KAAK,cAAc,IAAI;AAC5C,kBAAM,eAAA,QAAU,WAAW,KAAK,QAAQ,eACb,KAAK,YAAY,MAAM,GAAa,KACpC,KAAK,YAAY,OAAO,CAAW;;AAEhE,iBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,GAAG,GAAG,IAAI,GAAG,IAAI;;AAEvD,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAMO,MAAAA,UAAA,UAAA,WAAP,WAAA;AACE,eAAO,UAAU,KAAK,YAAY,KAAK,IACrC,QAAQ,KAAK,MAAM,KAAK,IAAI,IAAI;MACpC;AAEF,aAAAA;IAAA,EAjE8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AAuEb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAe5B,eAAAA,UAAY,SAA2B,OAAa;AAApD,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,YAAY,SAAS,KAAK;;MACjC;AAKA,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAe;AAE9B,YAAI,KAAK,OAAO,OAAO,GAAG;AAIxB,iBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,eAAA,QAAU,OAC5C,KAAK,QAAQ,eACb,KAAK,YAAY,OAAO,GAAa,KAAK,MAAK,GAC/C,KAAK,YAAY,OAAO,GAAa,IAAI,KAAK,YAAY,OAAO,CAAW,CAAC,CAAC,GAAG,IAAI;;AAEzF,YAAI,KAAK,OAAO,QAAQ,GAAG;AAIzB,cAAM,MAAM,EAAC,UAAU,KAAI;AAC3B,cAAI,KAAK,YAAY,OAAO,GAAG;AAC7B,gBAAI,YAAY,KAAK,YAAY,OAAO;;AAE1C,eAAK,KACH,KAAK,OAAO,QAAQ,WAAW,CAAA,GAAI,EAAC,UAAU,aAAA,SAAS,MAAK,CAAC,GAC7D,KAAK,OAAO,SAAS,MAAM,KAAK,KAAK,YAAY,OAAO,CAAC,GACzD,KAAK,OAAO,QAAQ,WAAW,CAAA,GAAI,EAAC,UAAU,aAAA,SAAS,KAAI,CAAC,CAAC;AAE/D,eAAK,MAAM,CAAA;AACX,iBAAO,CAAC,CAAC,IAAI,GAAG,IAAI;;AAEtB,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AA9DiB,MAAAA,UAAA,SAAS,OAAO,OAAO,OAAO,OAAO,eAAA,SAAS,MAAM,GAAG;QAEtE,QAAQ;UAAC;UACA;QAAiC;OAC3C;AA4DH,aAAAA;MArE8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AA2Eb,QAAA,SAAA,SAAA,QAAA;AAA4B,gBAAAC,SAAA,MAAA;AAK1B,eAAAA,QAAY,SAA2B,OAAe,OAAa;AAAnE,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,YAAY,SAAS,KAAK;AAC/B,iBAAS,MAAK,YAAY,SAAS,KAAK;;MAC1C;AAKA,aAAA,eAAWA,QAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,QAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EA1B4B,eAAA,QAAQ;AAAvB,YAAA,SAAA;AAgCb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAK7B,eAAAA,WAAY,SAA2B,OAAe,OAAa;AAAnE,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,YAAY,SAAS,KAAK;AAC/B,iBAAS,MAAK,YAAY,SAAS,KAAK;;MAC1C;AAKA,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EA1B+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AAgCb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MAwCA;AAnCE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,KAAK,GAAG;AACtB,cAAI,KAAK,QAAO,MAAO,KAAK,QAAO,GAAI;AAErC,kBAAM,IAAI,cAAA,QAAS,aAAa,oCACb,KAAK,QAAO,GAAI,KAAK,QAAO,CAAE;;AAEnD,cAAI,CAAC,KAAK,YAAY,KAAK,GAAG;AAE5B,mBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,KAAK,MAAK,CAAE,CAAC,GAAG,IAAI;;AAE1D,iBAAO,eAAA,SAAS;;AAElB,YAAI,KAAK,OAAO,MAAM,GAAG;AAEvB,gBAAM,IAAI,cAAA,QAAS,iBAAiB,qBAAqB,KAAK,QAAO,CAAE;;AAEzE,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAEF,aAAAA;IAAA,EAxC+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AAgDb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAiBA;AAZE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,SAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EAjB6B,eAAA,QAAQ;AAAxB,YAAA,UAAA;AAuBb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;;MAqBA;AAhBE,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,CAAC,KAAK,SAAS;AACjB,iBAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;;AAG7B,YAAM,MAAM,KAAK,OAAO,QAAQ,UAAU,KAAK,OAAO,KAAK,YAAY,QAAQ,CAAC;AAChF,eAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,GAAG,GAAG,IAAI,GAAG,IAAI;MACvD;AAEF,aAAAA;IAAA,EArB+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AA2Bb,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAAlC,eAAAA,gBAAA;;MAoCA;AA/BE,aAAA,eAAWA,cAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,cAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,SAAS;AAEhB,gBAAM,IAAI,cAAA,QAAS,iBAAiB,sBAAsB,KAAK,QAAO,CAAE;;AAE1E,YAAI,KAAK,SAAS;AAChB,cAAI,MAAM,KAAK,MAAK;AACpB,kBAAQ,KAAK,YAAY,MAAM,GAAG;YAClC,KAAK;AAEH,oBAAM,KAAK,OAAO,QAAQ,WAAW,CAAC,GAAG,GACvB;gBAAC,QAAQ,KAAK,YAAY,IAAI;gBAC7B,OAAO,KAAK,YAAY,IAAI;gBAC5B,SAAS,KAAK,YAAY,IAAI;cAAC,CAAC;AACnD,qBAAO,CAAC,CAAC,KAAK,QAAQ,OAAO,OAAO,GAAG,CAAC,GAAG,IAAI;YACjD,KAAK;AAEH,qBAAO,CAAC;gBAAC,KAAK,QAAQ,OAAO,OAAO,KAAK,YAAY,MAAM,CAAY;gBAAG;gBACjE,KAAK,QAAQ,OAAO,OAAO,KAAK,YAAY,OAAO,CAAY;cAAC,GAAG,IAAI;;;AAGpF,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AACF,aAAAA;IAAA,EApCkC,eAAA,QAAQ;AAA7B,YAAA,eAAA;AA0Cb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MAgBA;AAXE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,UAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AACF,aAAAA;IAAA,EAhB8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AAsBb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;;MAgBA;AAXE,aAAA,eAAWA,SAAA,WAAA,WAAO;aAAlB,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAEF,aAAAA;IAAA,EAhB6B,eAAA,QAAQ;AAAxB,YAAA,UAAA;AAsBb,QAAA,SAAA,SAAA,QAAA;AAA4B,gBAAAC,SAAA,MAAA;AAA5B,eAAAA,UAAA;;MAkDA;AA7CE,aAAA,eAAWA,QAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,QAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAM,MAAM,KAAK;AACjB,YAAI,KAAK;AACP,cAAI,KAAK,QAAQ;AAEf,mBAAO,eAAA,SAAS;;AAElB,cAAI,CAAC,KAAK,OAAO,IAAI,GAAG;AAEtB,gBAAI,MAAM,KAAK;AACf,gBAAI,CAAC,KAAK,OAAO,KAAK,KAAK,CAAC,KAAK;AAE/B,qBAAO,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI;;AAE3B,gBAAK,cAAA,QAAS,OAAO,KAAK,QAAQ,KAAK,IAAI,WAAW,UACjD,cAAA,QAAS,OAAO,IAAI,WAAW,CAAC,EAAE,WAAW,CAAC,GAAc,QAAQ,KACpE,cAAA,QAAS,OAAO,KAAK,QAAQ,GAAG;AAEnC,qBAAO,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI;;AAE3B,gBAAI,cAAA,QAAS,cAAc,GAAG,GAAG;AAE/B,oBAAM,cAAA,QAAS,UAAU,GAAG;;AAE9B,gBAAM,OAAO,cAAA,QAAS,QAAQ,GAAG;AACjC,gBAAI,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG;AAE3D,qBAAO,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI;;;AAI7B,cAAM,OAAO,KAAK,OAAO,SAAS,MAAM,EAAC,UAAU,aAAA,SAAS,KAAI,GACvC,cAAA,SAAS,aAAa;AAC/C,iBAAO,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,IAAI;;AAGjC,eAAO,OAAA,UAAM,UAAU,MAAM,MAAM,SAAS;MAC9C;AACF,aAAAA;IAAA,EAlD4B,eAAA,QAAQ;AAAvB,YAAA,SAAA;AAyDb,QAAA,UAAA,SAAA,QAAA;AAA6B,gBAAAC,UAAA,MAAA;AAA7B,eAAAA,WAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAEU,cAAA,QAAQ,gBAAA,WAAW,OAAO,WAAW;;MA8C/C;AAzCE,aAAA,eAAWA,SAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,SAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG;AAE9C,iBAAO,eAAA,SAAS;;AAElB,YAAI,KAAK,OAAO,KAAK,MAChB,cAAA,QAAS,OAAO,KAAK,OAAO,IAAI,KAAK,cAAA,QAAS,OAAO,KAAK,OAAO,IAAI,KACrE,cAAA,QAAS,OAAO,KAAK,OAAO,OAAO,IAAI;AAC1C,gBAAM,KAAK;AACX,cAAI,cAAA,QAAS,QAAQ,GAAe;AACpC,cAAI,EAAE,WAAW,KAAK,CAAC,cAAA,QAAS,YAAY,KAAK,YAAY,KACzD,cAAA,QAAS,YAAY,GAAG,EAAE,WAAW,GAAG;AAC1C,gBAAI,KAAK,MAAM,SAAS,CAAC,GAAG;AAE1B,yBAAW,KAAK,OAAO,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI;AACxD,4BAAA,QAAS,SAAS,KAAK,GAAG,QAAQ;mBAC7B;AAEL,yBAAW,KAAK,OAAO,QAAQ,GAAQ;AACvC,4BAAA,QAAS,eAAe,KAAK,CAAC,QAAQ,CAAC;;AAEzC,mBAAO,CAAC,CAAC,IAAI,GAAG,IAAI;;;AAIxB,mBAAW,KAAK,OAAO,QAAQ,GAAQ;AACvC,YAAM,YAAY,KAAK,OAAO,QAAQ,SAAS,CAAA,GAAI,CAAA,GAAI,QAAQ;AAC/D,YAAM,aAAa,KAAK,OAAO,QAAQ,WAAW,CAAC,SAAS,GAAG,EAAC,OAAO,EAAC,CAAC;AACzE,cAAM,KAAK,OAAO,QAAQ,WAAW,CAAC,UAAU,GAAG,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;AAC3E,eAAO,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI;MAC3B;AACF,aAAAA;IAAA,EAhD6B,eAAA,QAAQ;AAAxB,YAAA,UAAA;AAqDb,QAAA,gBAAA,SAAA,QAAA;AAAmC,gBAAAC,gBAAA,MAAA;AAAnC,eAAAA,iBAAA;;MA2CA;AAtCE,aAAA,eAAWA,eAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,eAAA,UAAA,YAAP,SAAiB,MAAe;AAI9B,YAAI,KAAK,OAAO,KAAK,KAAK,KAAK,KAAI,MAAO,GAAG;AAC3C,cAAI,MAAM,KAAK;AAKf,cAAI,IAAI,OAAO,QAAQ,KAAK,IAAI,WAAW;AACzC,kBAAM,cAAA,QAAS,YAAY,cAAA,QAAS,YAAY,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;;AAE5D,cAAI,IAAI,OAAO,QAAQ,GAAG;AAMxB,gBAAI,QAAQ,KAAK,OAAO;AACtB,kBAAM,OAAO,KAAK,OAAO,QAAQ,QAAQ,CAAC,KAAK,IAAG,CAAE,CAAC;AACrD,mBAAK,KAAK,IAAI;;AAKhB,iBAAK,QAAQ,cAAc,QAAQ,aAAa,KAAK,KAAK;;;AAG9D,eAAO,CAAC,CAAC,IAAI,GAAG,IAAI;MACtB;AACF,aAAAA;IAAA,EA3CmC,eAAA,QAAQ;AAA9B,YAAA,gBAAA;AAgDb,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MA2BA;AAtBE,aAAA,eAAWA,UAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,UAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG;AAC9C,iBAAO,eAAA,SAAS;;AAElB,YAAI,OAAO,KAAK,YAAY,OAAO;AACnC,YAAI,MAAM,KAAK;AAEf,YAAI,KAAK,OAAO,KAAK,KAAK,cAAA,QAAS,cAAc,GAAG,GAAG;AACrD,cAAM,SAAS,cAAA,QAAS,YAAY,cAAA,QAAS,UAAU,GAAG,CAAC;AAC3D,cAAI,WAAW,aAAA,SAAS,OAAO,WAAW,aAAA,SAAS,KAAK;AACtD,mBAAO,KAAK,YAAY,OAAO;;;AAGnC,eAAO,CAAC,CAAC,MAAM,IAAI,GAAG,IAAI;MAC5B;AACF,aAAAA;IAAA,EA3B8B,eAAA,QAAQ;AAAzB,YAAA,WAAA;AAkCb,QAAA,YAAA,SAAA,QAAA;AAA+B,gBAAAC,YAAA,MAAA;AAA/B,eAAAA,aAAA;AAAA,YAAA,QAAA,WAAA,QAAA,OAAA,MAAA,MAAA,SAAA,KAAA;AAMS,cAAA,QAAmB,CAAA;AAMnB,cAAA,MAAiB,CAAA;AAMjB,cAAA,QAAkB,CAAA;AAMlB,cAAA,QAAkB,CAAA;AAMlB,cAAA,WAAuD,CAAA;AAMvD,cAAA,SAAkB;;MAyM3B;AApME,aAAA,eAAWA,WAAA,WAAA,QAAI;aAAf,WAAA;AACE,iBAAO;QACT;;;;AAMA,aAAA,eAAIA,WAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAIA,WAAA,WAAA,WAAO;aAAX,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,WAAA,UAAA,YAAP,SAAiB,MAAe;AAE9B,YAAI,KAAK,WAAW,CAAC,KAAK,OAAO,MAAM,GAAG;AAExC,cAAI,KAAK,YAAY,SAAS,GAAG;AAE/B,iBAAK,SAAQ;AACb,iBAAK,SAAQ;AACb,mBAAO,eAAA,SAAS;;AAElB,cAAI,KAAK,YAAY,MAAM,GAAG;AAE5B,iBAAK,SAAQ;AACb,iBAAK,OAAM;AACX,iBAAK,SAAQ;AACb,mBAAO,eAAA,SAAS;;AAElB,eAAK,SAAQ;AACb,eAAK,SAAQ;AACb,cAAI,UAAU,KAAK,QAAQ,OAAO,OAAO,KAAK,UAAS,CAAE;AACzD,cAAI,KAAK,YAAY,cAAc,GAAG;AAEpC,gBAAI,KAAK,OAAO,OAAO,GAAG;AAExB,qBAAO,CAAC,CAAC,OAAO,GAAG,IAAI;;AAGzB,kBAAM,IAAI,cAAA,QAAS,qBAAqB,qBAAqB;;AAE/D,iBAAO,CAAC,CAAC,SAAS,IAAI,GAAG,IAAI;;AAE/B,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAOO,MAAAA,WAAA,UAAA,YAAP,WAAA;AACE,YAAM,cAAc,KAAK,SAAS,aAAa;AAC/C,eAAO,KAAK,SAAS,aAAa;AAClC,YAAI,MAAM,KAAK,OAAO,QAAQ,UAAU,KAAK,OAAO,KAAK,QAAQ;AACjE,YAAI,aAAa;AACf,cAAI,YAAY,eAAe,WAAW;;AAE5C,YAAI,KAAK,MAAM,WAAW,GAAG;AAE3B,wBAAA,QAAS,aAAa,KAAK,SAAS,KAAK,SAAS,WAAW,OAAO;mBAC3D,KAAK,MAAM,QAAQ;AAE5B,cAAI,KAAK,SAAS,UAAU,GAAG;AAE7B,iBAAK,SAAS,UAAU,IACrB,KAAK,SAAS,UAAU,EAAa,QAAQ,iBAAiB,MAAM;;AAGzE,wBAAA,QAAS,aAAa,KAAK,SAAS,EAAE;AACtC,gBAAM,KAAK,OAAO,QAAQ,YAAY,CAAC,GAAG,GAAG,EAAC,UAAU,KAAK,MAAM,KAAK,GAAG,EAAC,CAAC;AAC7E,eAAK,KAAK,SAAS,aAAa,KAAK,YAAY,WAC5C,KAAK,SAAS,UAAU,KAAK,YAAY,QAAQ;AAGpD,0BAAA,QAAS,aAAa,KAAK,gBAAgB,CAAC;;;AAGhD,YAAI,KAAK,YAAY,MAAM,KAAK,KAAK,YAAY,OAAO,GAAG;AAEzD,gBAAM,eAAA,QAAU,OAAO,KAAK,QAAQ,eACb,KAAK,YAAY,MAAM,GAAa,KACpC,KAAK,YAAY,OAAO,CAAW;;AAE5D,eAAO;MACT;AAKO,MAAAA,WAAA,UAAA,WAAP,WAAA;AAEE,YAAM,MAAM,KAAK,OAAO,QAAQ,OAAO,KAAK,KAAK;AACjD,YAAI,KAAK,MAAM,QAAQ;AACrB,cAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,0BAAA,QAAS,aAAa,KAAK,eAAe,OAAO;;AAEnD,cAAI,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,MAAM,KAAK,KAAI,GAAI;AACrD,0BAAA,QAAS,aACP,KAAK,eACL,cAAA,QAAS,aAAa,KAAK,aAAa,IAAI,WAAW,MAAM;;;AAGnE,aAAK,IAAI,KAAK,GAAG;AACjB,aAAK,MAAK;AACV,aAAK,QAAQ,CAAA;MACf;AAMO,MAAAA,WAAA,UAAA,SAAP,WAAA;AACE,YAAI;AACJ,YAAI,KAAK,YAAY,YAAY,KAAK,KAAK,IAAI,WAAW,GAAG;AAE3D,eAAK,IAAI,QAAQ,KAAK,IAAI,IAAG,CAAE;AAE/B,iBAAO,KAAK,OAAO,QAAQ,cAAc,KAAK,GAAG;eAC5C;AAEL,iBAAO,KAAK,OAAO,QAAQ,OAAO,KAAK,GAAG;;AAE5C,aAAK,MAAM,KAAK,IAAI;AACpB,aAAK,MAAM,CAAA;MACb;AAMO,MAAAA,WAAA,UAAA,WAAP,WAAA;AACE,YAAI,KAAK,KAAI,KAAM,KAAK,IAAI,QAAQ;AAClC,eAAK,SAAQ;AACb,eAAK,OAAM;;AAEb,aAAK,WAAU;MACjB;AAMO,MAAAA,WAAA,UAAA,aAAP,WAAA;AACE,YAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,cAAM,QAAS,KAAK,SAAS,UAAU,EAAa,MAAM,GAAG;AAC7D,cAAI,MAAM,WAAW,KAAK,MAAM,QAAQ;AACtC,iBAAK,MAAM,KAAK,QAAQ;AACxB,kBAAM,IAAG;AACT,iBAAK,SAAS,UAAU,IAAI,MAAM,KAAK,GAAG;qBACjC,MAAM,SAAS,KAAK,MAAM,SAAS,GAAG;AAC/C,iBAAK,SAAS,UAAU,KAAK;;;AAGjC,YAAI,KAAK,YAAY,YAAY,GAAG;AAClC,cAAM,OAAQ,KAAK,SAAS,YAAY,EAAa,MAAM,GAAG;AAC9D,iBAAO,KAAK,SAAS,KAAK,MAAM,QAAQ;AACtC,iBAAK,KAAK,KAAK,YAAY,YAAY,IAAI,IAAI;;AAEjD,eAAK,SAAS,YAAY,IAAI,KAAK,KAAK,GAAG;;MAE/C;AAOO,MAAAA,WAAA,UAAA,gBAAP,SAAqB,SAAe;AAClC,YAAI,KAAK,SAAS,YAAY,GAAG;AAC/B,cAAM,OAAQ,KAAK,SAAS,YAAY,EAAa,MAAM,GAAG;AAC9D,cAAI,CAAC,KAAK,YAAY,YAAY,GAAG;AAEnC,gBAAI,QAAQ,eAAA,QAAU,SAAS,KAAK,CAAC,CAAC;AACtC,iBAAK,YAAY,cAAc,KAAK;;AAEtC,cAAM,aAAa,KAAK,YAAY,YAAY;AAChD,iBAAO,KAAK,SAAS,KAAK,MAAM,QAAQ;AACtC,iBAAK,KAAK,eAAA,QAAU,GAAG,UAAU,CAAC;;AAEpC,eAAK,KAAK,MAAM,SAAS,CAAC,IAAI,eAAA,QAAU,GACtC,KAAK,IAAI,GAAG,aAAa,eAAA,QAAU,SAAS,OAAO,CAAC,CAAC;AACvD,eAAK,SAAS,YAAY,IAAI,KAAK,KAAK,GAAG;;MAE/C;AAEF,aAAAA;IAAA,EA7O+B,eAAA,QAAQ;AAA1B,YAAA,YAAA;AAoPb,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAUhC,eAAAA,cAAY,SAAY;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAA1B,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AANT,cAAA,SAAiB;AAOtB,cAAK,QAAQ,cAAc,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;;MACjE;AAMA,aAAA,eAAIA,cAAA,WAAA,QAAI;aAAR,WAAA;AACE,iBAAO;QACT;;;;AAMO,MAAAA,cAAA,UAAA,WAAP,WAAA;AAEE,YAAI,KAAK,IAAI,QAAQ;AACnB,yBAAA,QAAU,aAAa,KAAK,QAAQ,eAAe,KAAK,KAAK;;AAE/D,YAAM,OAAO,KAAK,OAAO,QAAQ,OAAO,KAAK,KAAK;AAClD,aAAK,IAAI,KAAK,IAAI;AAClB,aAAK,MAAK;MACZ;AAKO,MAAAA,cAAA,UAAA,SAAP,WAAA;AACE,YAAI,KAAK,IAAI,SAAS,KAAK,QAAQ;AACjC,eAAK,SAAS,KAAK,IAAI;;AAGzB,YAAI,MAAM;AACV,YAAI,MAAM,KAAK,QAAQ,cAAc,KAAK,OAAM;AAChD,YAAI,KAAK;AACP,eAAK,MAAM,CAAC,GAAG,EAAE,OAAO,KAAK,GAAG;AAChC,gBAAM;;AAER,aAAK,QAAQ,cAAc,KAAK,SAAQ;AACxC,YAAM,OAAO,KAAK,OAAO,QAAQ,KAAK,KAAK,GAAG;AAC9C,aAAK,MAAM,KAAK,IAAI;AACpB,aAAK,MAAM,CAAA;MACb;AAKO,MAAAA,cAAA,UAAA,WAAP,WAAA;AAEE,eAAA,UAAM,SAAQ,KAAA,IAAA;AACd,aAAK,QAAQ,cAAc,KAAK,IAAG;AAKnC,aAAK,YAAY,eAAe,KAAK,MAAM;AAC3C,aAAK,YAAY,eAAe,KAAK,MAAM;AAC3C,aAAK,YAAY,iBAAiB,KAAK,SAAS,CAAC;MACnD;AAMU,MAAAA,cAAA,UAAA,cAAV,SAAsB,MAAc,KAAW;AAC7C,YAAI,CAAC,KAAK,SAAS,IAAI;AAAG;AAC1B,YAAM,SAAU,KAAK,SAAS,IAAI,EAAa,MAAM,GAAG;AACxD,YAAM,UAAO,cAAA,CAAA,GAAA,OAAO,MAAM,GAAA,KAAA;AAC1B,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,QAAQ,SAAS,KAAK;AAC3B,oBAAQ,KAAI,MAAZ,SAAO,cAAA,CAAA,GAAA,OAAS,MAAM,GAAA,KAAA,CAAA;;AAExB,eAAK,SAAS,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,EAAE,KAAK,GAAG;;MAExD;AACF,aAAAA;IAAA,EAxFkC,SAAS;AAA9B,YAAA,eAAA;AA+Fb,QAAA,eAAA,SAAA,QAAA;AAAkC,gBAAAC,eAAA,MAAA;AAKhC,eAAAA,cAAY,SAAY;AAAE,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,eAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAA1B,YAAA,QACE,OAAA,KAAA,MAAM,OAAO,KAAC;AACd,cAAK,QAAQ,cAAc,KAAK,MAAM,YAAY,MAAM,KAAK,CAAC,CAAC;;MACjE;AAMA,aAAA,eAAIA,cAAA,WAAA,QAAI;aAAR,WAAA;AACE,iBAAO;QACT;;;;AAKA,aAAA,eAAIA,cAAA,WAAA,UAAM;aAAV,WAAA;AACE,iBAAO;QACT;;;;AAKO,MAAAA,cAAA,UAAA,YAAP,SAAiB,MAAe;AAC9B,YAAI,KAAK,OAAO,KAAK,GAAG;AACtB,cAAI,MAAM,KAAK,MAAK;AACpB,cAAI,MAAM,KAAK,QAAQ,cAAc,KAAK,OAAM;AAChD,eAAK,QAAQ,cAAc,KAAK,IAAG;AACnC,iBAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,cAAc,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI;;AAEnF,YAAI,KAAK,OAAO,MAAM,GAAG;AAEvB,gBAAM,IAAI,cAAA,QAAS,iBAAiB,qBAAqB,KAAK,QAAO,CAAE;;AAEzE,eAAO,OAAA,UAAM,UAAS,KAAA,MAAC,IAAI;MAC7B;AAEF,aAAAA;IAAA,EA1CkC,eAAA,QAAQ;AAA7B,YAAA,eAAA;;;;;;;;;;ACtpCb,QAAiB;AAAjB,KAAA,SAAiBC,cAAW;AAEb,MAAAA,aAAA,UAAU;QACrB,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,SAAS;QACT,aAAa;QACb,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,eAAe;QACf,iBAAiB;QACjB,qBAAqB;QACrB,WAAW;QACX,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,cAAc;QACd,kBAAkB;QAClB,UAAU;QACV,cAAc;QACd,YAAY;;AAGD,MAAAA,aAAA,OAAO;QAClB,QAAQ;QACR,OAAO;QACP,SAAS;;AAGE,MAAAA,aAAA,YAAY;QACvB,MAAM;QACN,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;;AAGC,MAAAA,aAAA,iBAAiB;QAC5B,QAAQ;QACR,OAAO;QACP,WAAW;QACX,oBAAoB;;AAGT,MAAAA,aAAA,cAAc;QACzB,MAAM;QACN,QAAQ;QACR,OAAO;QACP,MAAM;QACN,IAAI;QACJ,aAAa;;AAGF,MAAAA,aAAA,cAAc;QACzB,aAAa;;AAGF,MAAAA,aAAA,gBAAgB;QAC3B,MAAM;QACN,QAAQ;QACR,OAAO;;AAGI,MAAAA,aAAA,WAAW;QACtB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ;QACR,kBAAkB;QAClB,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,SAAS;QACT,iBAAiB;;AAGN,MAAAA,aAAA,QAAQ;QACnB,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;QACN,MAAM;QACN,OAAO;;AAGI,MAAAA,aAAA,QAAQ;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;;AAGG,MAAAA,aAAA,OAAO;QAClB,MAAM;QACN,OAAO;QACP,aAAa;QACb,cAAc;;AAGH,MAAAA,aAAA,QAAQ;QACnB,MAAM;QACN,KAAK;;AAGM,MAAAA,aAAA,aAAa;QACxB,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,OAAO;;AAGI,MAAAA,aAAA,WAAW;QACtB,UAAU;QACV,QAAQ;QACR,OAAO;QACP,UAAU;QACV,OAAO;;AAGI,MAAAA,aAAA,OAAO;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;;IAGR,GA/IiB,cAAA,QAAA,gBAAA,QAAA,cAAW,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACD5B,QAAA,QAAA,aAAA,mBAAA;AAIA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,oBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,eAAA;AAGA,QAAA,YAAA;AACA,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,eAAA;AAIA,QAAI,cAA2C,CAAA;AAE/C,QAAM,WAAW,MAAM;AACvB,QAAM,gBAAyC;MAC7C,YAAY;MAAG,UAAU;MAAG,YAAY;MAAG,WAAW;MACtD,OAAO;MAAG,YAAY;MACtB,IAAI;MAAG,SAAS;MAAG,MAAM;MAAG,OAAO;;AAcrC,gBAAY,OAAO,SAAS,QAAmB,IAAU;AAEvD,aAAO,KAAK,OAAO,YAAY,OAAO,MAAM,CAAC;IAC/C;AAOA,gBAAY,QAAQ,SAAS,QAAmB,IAAU;AAExD,aAAO,KAAK,OAAO,YAAY,OAAO,OAAO,CAAC;IAChD;AAQA,gBAAY,QAAQ,SAAS,QAAmB,IAAU;AAExD,aAAO,KAAK,OAAO,OAAO,SAAS,SAAS,CAAA,GAAI,cAAA,SAAS,IAAI,CAAC;IAChE;AAOA,gBAAY,QAAQ,SAAS,SAAoB,IAAU;IAAG;AAO9D,gBAAY,cAAc,SAAS,QAAmB,IAAU;;AAC9D,UAAI,OAAO,QAAO,EAAG,MAAM,IAAI,GAAG;AAEhC,eAAO,SAAS,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,CAAC,IAClD,MAAM,OAAO,OAAO,OAAO,OAAO,IAAI,CAAC;;AAE3C,UAAI;AACJ,UAAI;AACJ,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI,IAAI,OAAO,OAAO,GAAG;AAEvB,aAAA,OAAiB,IAAI,KAAK,CAAC,GAAC,CAAA,GAA3B,OAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACb,eAAO,MAAM,IAAG;aACX;AAEL,eAAO,OAAO,MAAM,KAAI;AACxB,YAAI,CAAC,MAAM;AAET,iBAAO,OAAO,OAAO,SAAS,MAAM,CAAA,GAAI,EAAE;;;AAG9C,UAAM,aAAa,cAAA,QAAS,YAAY,MAAM,YAAY;AAC1D,UAAI,WAAW,cAAA,QAAS,OAAO,MAAM,SAAS,IAAK,KAAoB,MACpE,KAAuB;AAC1B,UAAK,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,CAAC,cAAA,QAAS,OAAO,MAAM,MAAM,KACjE,cAAA,QAAS,WAAW,MAAO,KAAoB,GAAG,KAClD,cAAA,QAAS,OAAO,MAAM,YAAY,KAAK,CAAC,cAAA,QAAS,OAAO,MAAM,OAAO,KACrE,cAAA,QAAS,WAAW,MAAO,KAAuB,IAAI,KACtD,CAAC,cAAA,QAAS,YAAY,MAAM,UAAU,GAAI;AAE7C,cAAM,IAAI,cAAA,QAAS,kBAAkB,wCAAwC;;AAE/E,UAAI,CAAC,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,cAAA,QAAS,OAAO,MAAM,MAAM,GAAG;AACtE,YAAI,YAAY;AAEd,cAAI,CAAC,cAAA,QAAS,OAAO,MAAM,YAAY,KAAK,cAAA,QAAS,OAAO,MAAM,OAAO,KACrE,cAAA,QAAS,WAAW,MAAO,KAAuB,IAAI,GAAG;AAE3D,mBAAO,OAAO,OAAO,QAAQ,cAAc,CAAC,IAAI,GAAG,EAAC,YAAY,KAAI,CAAC;;AAEvE,qBAAY,KAAuB;eAC9B;AAEL,iBAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,IAAI,CAAC;AAC9C,qBAAY,KAAoB;;;AAGpC,aAAO,KACL,OAAO,YAAY,OAAO,UAAU,IAAI,EAAE,cAAc;QACtD;QAAoB;QAAgB;OACrC,CAAC;IACN;AAQA,gBAAY,YAAY,SAAS,QAAmB,IAAU;;AAC5D,UAAI,OAAO,QAAO,EAAG,MAAM,IAAI,GAAG;AAEhC,eAAO,SACL,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,CAAC,IAAI,MACxC,OAAO,OAAO,OAAO,OAAO,IAAI,CAAC;;AAErC,UAAI,QAAQ;AACZ,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI,IAAI,OAAO,OAAO,GAAG;AAEvB,aAAA,OAAiB,IAAI,KAAK,CAAC,GAAC,CAAA,GAA3B,OAAI,GAAA,CAAA,GAAE,SAAM,GAAA,CAAA;AACb,eAAO,MAAM,IAAG;aACX;AACL,eAAO,OAAO,MAAM,KAAI;AACxB,YAAI,CAAC,MAAM;AAET,iBAAO,OAAO,OAAO,SAAS,MAAM,CAAA,GAAI,EAAE;;;AAG9C,UAAM,aAAa,cAAA,QAAS,YAAY,MAAM,YAAY;AAC1D,UAAI,WAAW,cAAA,QAAS,OAAO,MAAM,SAAS,IAC3C,KAAoB,MAAO,KAAuB;AACrD,UAAK,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,CAAC,cAAA,QAAS,OAAO,MAAM,MAAM,KACjE,cAAA,QAAS,WAAW,MAAO,KAAoB,GAAG,KAClD,cAAA,QAAS,OAAO,MAAM,YAAY,KAAK,CAAC,cAAA,QAAS,OAAO,MAAM,OAAO,KACrE,cAAA,QAAS,WAAW,MAAO,KAAuB,KAAK,KACvD,CAAC,cAAA,QAAS,YAAY,MAAM,UAAU,GAAI;AAE7C,cAAM,IAAI,cAAA,QAAS,oBAAoB,0CAA0C;;AAEnF,UAAI,CAAC,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,cAAA,QAAS,OAAO,MAAM,MAAM,GAAG;AACtE,YAAI,YAAY;AAEd,cAAI,CAAC,cAAA,QAAS,OAAO,MAAM,YAAY,KAAK,cAAA,QAAS,OAAO,MAAM,OAAO,KACrE,cAAA,QAAS,WAAW,MAAO,KAAuB,KAAK,GAAG;AAE5D,mBAAO,OAAO,OAAO,QAAQ,cAAc,CAAC,IAAI,GAAG,EAAC,YAAY,KAAI,CAAC;;AAEvE,qBAAY,KAAuB;eAC9B;AAEL,iBAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,IAAI,CAAC;AAC9C,qBAAY,KAAoB;;;AAGpC,aAAO,KACL,OAAO,YAAY,OAAO,UAAU,IAAI,EAAE,cAAc;QACtD;QAAoB;QAAgB;OACrC,CAAC;IACN;AAQA,gBAAY,QAAQ,SAAS,QAAmB,GAAS;AAEvD,UAAI,OAAO,OAAO,MAAM,KAAI;AAC5B,UAAI,CAAC,MAAM;AAET,eAAO,OAAO,OAAO,QAAQ,IAAI;;AAEnC,UAAI,cAAA,QAAS,OAAO,MAAM,SAAS,KAAK,CAAC,cAAA,QAAS,OAAO,MAAM,MAAM,KACjE,cAAA,QAAS,WAAW,MAAO,KAAoB,GAAG,GAAG;AAEvD,cAAM,IAAI,cAAA,QAAS,uBACC,qDAAqD;;AAE3E,UAAI,MAAM;AACV,aAAO;AACP,SAAG;AAED,eAAO,cAAA,SAAS;AAAO,eAAO,KAAK,IAAI,OAAO,QAAO;eAC9C,MAAM,OAAQ,MAAM,cAAA,SAAS;AACtC,YAAM,CAAC,IAAI,KAAU,KAAU,KAAU,GAAQ,EAAE,IAAI,MAAM,KAAK;AAClE,UAAM,OAAO,OAAO,OAAO,SAAS,MAAM,EAAC,aAAa,KAAI,GAAG,GAAG;AAClE,aAAO,KACL,OAAO,YAAY,OAAO,SAAS,MAAM,IAAI,CAAC;IAClD;AAQA,gBAAY,UAAU,SAAS,QAAmB,IAAU;AAC1D,aAAO,OAAO,IAAI,OAAO,OAAO,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,MAAM,MAAM;AACjF,eAAO;;IAEX;AAQA,gBAAY,OAAO,SAAS,SAAoB,IAAU;AAExD,YAAM,IAAI,cAAA,QAAS,gBACC,0DAA6D;IACnF;AAcA,gBAAY,WAAW,SAAS,QAAmB,MAAc,SAAe;AAC9E,UAAM,OAAO,OAAO,YAAY,IAAI;AACpC,UAAI,MAAM,IAAI,eAAA,QAAU,MAAI,SAAA,SAAA,CAAA,GACvB,OAAO,MAAM,GAAG,GAAA,EACnB,MAAM,SACN,wBAAwB,cACxB,UAAU,KAAI,CAAA,GACb,OAAO,aAAa,EAAE,IAAG;AAC5B,aAAO,KAAK,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC;IACrD;AAQA,gBAAY,UAAU,SAAS,QAAmB,OAAe,MAAY;AAC3E,aAAO,MAAM,IAAI,MAAM,IAAI;IAC7B;AAUA,gBAAY,WAAW,SAAS,QAAmB,OACnB,UAAkB,OAClB,OAAa;AAC3C,aAAO,MAAM,IAAI,OAAO,IAAI;AAC5B,aAAO,MAAM,IAAI,OAAO,IAAI;AAC5B,aAAO,KACL,OAAO,YAAY,OAAO,OAAO,EAAE,YACjC,UAAU,EAAC,cAAc,OAAO,aAAa,MAAK,CAAC,CAAC;IAC1D;AASA,gBAAY,UAAU,SAAS,QAAmB,OAAe,MAAY;AAC3E,aAAO,MAAM,IAAI,MAAM,IAAI;AAC3B,aAAO,KACL,OAAO,YAAY,OAAO,OAAO,EAAE,YAAY,UAAU,EAAC,WAAU,GAAA,aAAA,IAAG,IAAI,EAAC,CAAC,CAAC;IAClF;AAQA,gBAAY,SAAS,SAAS,QAAmB,OAAe,OAAa;AAE3E,UAAM,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,EAAC,QAAO,GAAA,aAAA,IAAG,KAAK,EAAC,CAAC;AACnE,UAAM,QAAQ,OAAO,OAAO,QAAQ,UAAU,CAAC,IAAI,GAAG,EAAC,aAAa,EAAC,CAAC;AACtE,aAAO,KAAK,KAAK;IACnB;AAQA,gBAAY,YAAY,SAAS,QAAmB,MAAY;AAE9D,UAAM,QAAQ,KAAK,OAAO,CAAC;AAC3B,aAAO,KAAK,OAAO,YAAY,OAAO,OAAO,OAAO,aAAa,IAAI,GAAG,OAAO,MAAM,IAAI,KAAK,CAAC;IACjG;AAQA,gBAAY,UAAU,SAAS,QAAmB,MAAc,IAAU;AAExE,UAAI,CAAC,IAAI;AACP,aAAK,KAAK,OAAO,CAAC;;AAEpB,UAAM,MAAM,OAAO,OAAO,SAAS,MAAM,EAAC,UAAU,aAAA,SAAS,GAAE,GAAG,EAAE;AACpE,aAAO,KAAK,OAAO,YAAY,OAAO,MAAM,GAAG,CAAC;IAClD;AASA,gBAAY,UAAU,SAAS,QAAmB,MAAc,IAAU;AAExE,UAAI,CAAC,IAAI;AACP,aAAK,KAAK,OAAO,CAAC;;AAEpB,WAAK,GAAG,QAAQ,YAAY,GAAQ;AACpC,UAAM,MAAM,OAAO,OAAO,SAAS,MAAM;QACvC,eAAe;QACf,YAAY;QACZ,MAAM,kBAAA,YAAY,KAAK;QACvB,UAAU,aAAA,SAAS;SAClB,EAAE;AACL,aAAO,KAAK,GAAG;IACjB;AAQA,gBAAY,SAAS,SAAS,QAAmB,OAAe,QAAc;AAE5E,UAAI,KAAK,OAAO,MAAM,KAAK,IAAI;AAE/B,UAAI,CAAC,MAAO,cAAA,QAAS,YAAY,cAAA,QAAS,UAAU,EAAE,CAAC,MAAM,aAAA,SAAS,MAC1D,cAAA,QAAS,YAAY,IAAI,YAAY,KAAK,MAAO;AAE3D,cAAM,IAAI,cAAA,QAAS,mBAAmB,mCAAmC,OAAO,SAAS;;AAE3F,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI;AACJ,UAAI,cAAA,QAAS,OAAO,IAAI,YAAY,KAAK,CAAC,QAAQ;AAEhD,eAAO,OAAO,OAAO,QAAQ,SAAS;AACtC,sBAAA,QAAS,aAAa,IAAI,IAAI;AAC9B,aAAK,IAAI,OAAO;iBACP,cAAA,QAAS,OAAO,IAAI,SAAS,KAAK,QAAQ;AAInD,eAAO,OAAO,OAAO,QAAQ,YAAY;AACzC,sBAAA,QAAS,aAAa,IAAI,IAAI;AAC9B,aAAK,IAAI,OAAO;;AAElB,oBAAA,QAAS,YAAY,IAAI,cAAc,SAAS,OAAO,KAAK;AAC5D,oBAAA,QAAS,cAAc,cAAA,QAAS,UAAU,EAAE,GAAG,EAAC,iBAAiB,MAAK,CAAC;AACvE,UAAI,cAAA,QAAS,aAAa,IAAI,eAAe,KACzC,cAAA,QAAS,YAAY,IAAI,eAAe,GAAG;AAC7C,sBAAA,QAAS,cAAc,IAAI,EAAC,iBAAiB,MAAK,CAAC;;IAEvD;AAUA,gBAAY,OAAO,SAAS,QAAmB,MAAc,MAAc,OAAa;AAEtF,UAAM,MAAM,OAAO,YAAY,OAAO,MAAM,EAAE,YAAY,QAAQ,OAAO,SAAS;AAClF,UAAI,QAAQ,OAAO;AAEjB,YAAI,YAAY,QAAQ,IAAI;AAC5B,YAAI,YAAY,SAAS,KAAK;iBACrB,KAAK,MAAM,aAAa,GAAG;AAEpC,YAAI,YAAY,QAAQ,OAAO,aAAa,IAAI,CAAC;AACjD,YAAI,YAAY,SAAS,OAAO,aAAa,IAAI,CAAC;;AAEpD,UAAI,KAAK,MAAM,UAAU,GAAG;AAE1B,YAAI,YAAY,aAAa,OAAO,SAAS,IAAI,CAAC;iBAE3C,KAAK,MAAM,SAAS,KAAK,QAAQ,OAAO;AAE/C,YAAI,YAAY,aAAa,CAAC;;AAEhC,aAAO,KAAK,GAAG;IACjB;AAOA,gBAAY,OAAO,SAAS,QAAmB,MAAY;AAEzD,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,OAAO,QAAQ,SAAS,CAAC,KAAK,GAAG,CAAC;AACtD,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,OAAO,SAAS,QAAmB,MAAY;AACzD,UAAM,IAAI,OAAO,YAAY,IAAI;AACjC,UAAI,MAAM,OAAO,YAAY,IAAI;AACjC,UAAI,QAAQ,UAAU;AACpB,eAAQ,MAAM,OAAO,YAAY,GAAG,IAAI,OAAO,OAAO,YAAY,GAAG,IAAI;;AAE3E,UAAI,MAAM,IAAI,eAAA,QAAU,KAAK,OAAO,MAAM,KAAK,OAAO,aAAa,EAAE,IAAG;AACxE,UAAI,CAAC,GAAG;AAEN,cAAM,OAAO,OAAO,QAAQ,SAAS,CAAC,GAAG,CAAC;aACrC;AAEL,cAAM,OAAO,OAAO,QAAQ,SAAS,CAAC,KAAK,UAAU,QAAQ,CAAC,CAAC,CAAC;;AAElE,aAAO,KAAK,GAAG;IACjB;AASA,aAAS,UAAU,QAAmB,GAAS;AAE7C,UAAM,MAAM,OAAO,MAAM;AACzB,UAAM,SAAS,IAAI,QAAQ;AAC3B,UAAI,QAAQ,IAAI;AAChB,UAAM,YAAY,IAAI,eAAA,QAAU,GAAG,KAAK,OAAO,aAAa;AAC5D,UAAI,OAAO,UAAU,IAAG;AACxB,UAAM,SAAS,UAAU,MAAM;AAC/B,UAAI,OAAO,UAAU,KAAK,OAAO,QAAQ,GAAG;AAE1C,YAAM,MAAe,CAAA;AACrB,YAAI,OAAO,UAAU,GAAG;AACtB,cAAI,OAAO,IAAI,OAAO,UAAU;;AAElC,YAAI,OAAO,QAAQ,GAAG;AACpB,cAAI,SAAS,IAAI,OAAO,QAAQ;AAChC,cAAI,QAAQ,IAAI,OAAO,QAAQ;;AAEjC,eAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,IAAI,GAAG,GAAG;;AAErD,UAAI,QAAQ,IAAI;AAChB,aAAO;IACT;AAQA,gBAAY,OAAO,SAAS,QAAmB,MAAY;AACzD,UAAM,IAAI,OAAO,QAAQ,MAAM,MAAM;AACrC,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,OAAO,QAAQ,SAAS,CAAC,KAAK,UAAU,QAAQ,CAAC,CAAC,CAAC;AACvE,aAAO,KAAK,IAAI;IAClB;AASA,gBAAY,WAAW,SAAS,QAAmB,MAAc,IAAU;AAEzE,UAAI,CAAC,OAAO,MAAM,IAAI,QAAQ,GAAG;AAE/B,cAAM,IAAI,cAAA,QAAS,qBAAqB,oCAAoC,OAAO,SAAS;;AAE9F,UAAI,OAAO,MAAM,OAAO,EAAE,GAAG;AAE3B,cAAM,IAAI,cAAA,QAAS,oBAAoB,sBAAsB,OAAO,SAAS;;AAE/E,UAAI,IAAI,OAAO,YAAY,IAAI;AAC/B,UAAI,CAAC,EAAE,MAAM,UAAU,GAAG;AAExB,cAAM,IAAI,cAAA,QAAS,cAAc,yCAAyC,OAAO,SAAS;;AAE5F,UAAK,SAAS,GAAG,EAAE,IAAI,KAAM;AAC7B,UAAI,EAAE,OAAO,GAAG,CAAC,MAAM,KAAK;AAC1B,YAAI,MAAM;;AAEZ,aAAO,MAAM,OAAO,EAAE,IAAI;IAC5B;AAUA,gBAAY,SAAS,SAAS,QAAmB,MAAc,QAAgB,UAAiB;AAE9F,UAAM,IAAI,OAAO,SAAS,IAAI;AAE9B,UAAM,MAAG,SAAA,SAAA,CAAA,GAAO,eAAA,QAAU,WAAW,MAAM,CAAC,GAAA,EAAE,QAAQ,MAAM,YAAY,KAAI,CAAA;AAC5E,UAAM,SAAS,cAAA,QAAS,aAAa,MAAM;AAC3C,UAAM,SAAS,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM;AACvD,UAAM,MAAM;AACZ,oBAAA,QAAS,aAAa,KAAK,YAAY,WAAW,OAAO,KAAK;AAE9D,UAAM,KAAM,cAAA,QAAS,cAAc,CAAC,IAAI,cAAA,QAAS,UAAU,CAAC,IAAI;AAChE,UAAI,cAAA,QAAS,OAAO,IAAI,IAAI,KAAK,cAAA,QAAS,YAAY,IAAI,eAAe,GAAG;AAE1E,sBAAA,QAAS,cAAc,IAAI,EAAC,iBAAiB,MAAK,CAAC;;AAErD,UAAM,UAAU,OAAO,OAAO,QAAQ,YAAY;AAElD,oBAAA,QAAS,SAAS,SAAS,GAAG,CAAC;AAC/B,oBAAA,QAAS,SAAS,SAAS,GAAG,IAAI;AAClC,oBAAA,QAAS,SAAS,SAAS,GAAG,GAAG;AACjC,UAAI,UAAU,OAAO,OAAO,QAAQ,WAAW,CAAC,OAAO,CAAC;AACxD,aAAO,KAAK,OAAO;IACrB;AAUA,gBAAY,YAAY,SAAS,QAAmB,MAAc,GAAW,OAAc;AACzF,UAAM,SAAS,cAAA,QAAS,aAAa,CAAC;AACtC,UAAM,KAAK,OAAO,OAAO,SAAS,MAAM,EAAC,UAAU,MAAM,QAAQ,KAAI,GAAG,MAAM;AAC9E,UAAM,MAAO,KAAK,OAAO,CAAC,MAAM,MAAM,SAAS;AAC/C,UAAM,OAAO,OAAO,SAAS,IAAI;AACjC,aAAO,KAAK,eAAA,QAAU,UAAU,QAAQ,MAAM,IAAI,KAAK,KAAK,CAAC;IAC/D;AAQA,gBAAY,UAAU,SAAS,QAAmB,MAAY;AAE5D,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,SAAS,IAAI;AACjC,qBAAA,QAAU,mBAAmB,IAAI;AACjC,UAAI,IAAI,OAAO,IAAI,GAAG;AACpB,sBAAA,QAAS,aAAa,KAAK,UAAU,KAAK;;AAE5C,UAAM,OAAO,OAAO,OAAO,QAAQ,SAAS,CAAC,MAAM,GAAG,CAAC;AACvD,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,WAAW,SAAS,QAAmB,MAAY;AAE7D,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,SAAS,IAAI;AACjC,qBAAA,QAAU,mBAAmB,IAAI;AACjC,UAAI,IAAI,OAAO,IAAI,GAAG;AACpB,sBAAA,QAAS,aAAa,KAAK,UAAU,KAAK;;AAE5C,UAAM,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG,EAAC,aAAa,MAAK,CAAC;AAC9E,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,eAAe,SAAS,QAAmB,MAAY;AACjE,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,SAAS,IAAI;AACjC,qBAAA,QAAU,mBAAmB,IAAI;AACjC,UAAI,IAAI,OAAO,IAAI,GAAG;AACpB,sBAAA,QAAS,aAAa,KAAK,UAAU,KAAK;;AAE5C,UAAI,IAAI,OAAO,IAAI,GAAG;AACpB,sBAAA,QAAS,aAAa,KAAK,UAAU,KAAK;;AAE5C,UAAM,OAAO,OAAO,OAAO,QAAQ,cAAc,CAAC,MAAM,KAAK,GAAG,GAAG,EAAC,QAAQ,OAAO,aAAa,MAAK,CAAC;AACtG,aAAO,KAAK,IAAI;IAClB;AASA,gBAAY,UAAU,SAAS,QAAmB,MAAc,QAAc;AAC5E,UAAI,MAAe,EAAC,UAAU,OAAM;AACpC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,WAAW,aAAA,SAAS,IAAI;AAC1B,YAAI,YAAY,IAAI,IAAI,eAAe,IAAI;AAC3C,YAAM,MAAM,OAAO,YAAY,IAAI;AACnC,YAAM,QAAQ,IAAI,MAAM,6BAA6B;AACrD,YAAI,OAAO;AAET,cAAI,aAAa,IAAI,kBAAA,YAAY,QAAQ;AACzC,iBAAO,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM,CAAC,CAAC;eAC5C;AAEL,mBAAS,IAAI,eAAA,QAAU,KAAK,OAAO,MAAM,KAAK,OAAO,aAAa,EAAE,IAAG;AACvE,iBAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;;AAEvD,cAAM,OAAO,YAAY,OAAO,MAAM,IAAI;aACrC;AAEL,iBAAS,OAAO,SAAS,IAAI;AAC7B,cAAM,OAAO,OAAO,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;;AAEtD,aAAO,KAAK,GAAG;IACjB;AAQA,gBAAY,WAAW,SAAS,QAAmB,MAAY;AAE7D,UAAM,OAAO,OAAO,YAAY,IAAI;AACpC,UAAI,OAAO,OAAO,YAAY,MAAM,EAAE,EAAE,QAAQ,QAAQ,EAAE;AAC1D,UAAM,OAAO,OAAO,YAAY,IAAI;AACpC,UAAM,MAAe,CAAA;AACrB,UAAM,OAAiB,CAAA;AACvB,UAAI;AACJ,UAAI;AACF,eAAO,OAAO,OAAO,QAAQ,IAAI;eAC1B,GAAG;AACV,eAAO;;AAET,UAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AAE1B,cAAM,IAAI,cAAA,QAAS,kBAAkB,6BAA6B,IAAI;;AAExE,aAAO,SAAS,IAAI;AAClB,YAAM,QAAQ,KAAK,MAAM,mDAAmD;AAC5E,YAAI,CAAC,OAAO;AAEV,gBAAM,IAAI,cAAA,QAAS,qBAAqB,gCAAgC,IAAI;;AAE9E,YAAI,CAAC,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,CAAC,cAAc,MAAM,CAAC,CAAC,GAAG;AAErE,gBAAM,IAAI,cAAA,QAAS,yBACC,2CACA,MAAM,CAAC,GAAG,IAAI;;AAEpC,YAAI,QAA0B,eAAA,QAAU,mBACtC,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,QAAQ,kBAAkB,IAAI,CAAC;AAC5D,YAAI,OAAO;AACT,cAAI,MAAM,YAAW,MAAO,QAAQ;AAClC,oBAAQ;qBAED,MAAM,YAAW,MAAO,SAAS;AACxC,oBAAQ;;AAEV,cAAI,MAAM,CAAC,CAAC,IAAI;AAChB,eAAK,KAAK,MAAM,CAAC,CAAC;;AAEpB,eAAO,KAAK,OAAO,MAAM,CAAC,EAAE,MAAM;;AAEpC,UAAI,KAAK,QAAQ;AACf,YAAI,gBAAgB,IAAI,KAAK,KAAK,GAAG;;AAEvC,UAAM,WAAW,OAAO,OAAO,QAAQ,IAAI;AAC3C,WAAK,YAAY,QAAQ;AACzB,oBAAA,QAAS,cAAc,MAAM,GAAG;AAChC,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,QAAQ,SAAS,QAAmB,OAAa;AAE3D,UAAM,MAAM,OAAO,OAAO,QAAQ,MAAM;AACxC,UAAM,SAAS,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,GACG,EAAC,QAAQ,SAAS,OAAO,OAAO,OAAO,EAAC,CAAC;AAChG,aAAO,KAAK,MAAM;IACpB;AASA,gBAAY,UAAU,SAAS,QAAmB,MAAc,GAAW,GAAS;AAElF,UAAI,MAAM,OAAO,OAAO,QAAQ,YAAY,CAAC,OAAO,SAAS,IAAI,CAAC,CAAC;AACnE,UAAI,KAAK,GAAG;AAEV,cAAM,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,CAAC;AAC5C,YAAI,GAAG;AAEL,wBAAA,QAAS,aAAa,KAAK,UAAU,CAAC;AACtC,wBAAA,QAAS,aAAa,KAAK,SAAS,CAAC;;AAEvC,YAAI,GAAG;AAEL,wBAAA,QAAS,aAAa,KAAK,SAAS,CAAC;;;AAGzC,UAAM,OAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,CAAC;AACnD,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,QAAQ,SAAS,QAAmB,MAAY;AAE1D,UAAM,KAAK,eAAA,QAAU,WAAW,OAAO,YAAY,MAAM,EAAE,CAAC;AAC5D,UAAM,QAAQ,OAAO,OAAO,QAAQ,WAAW,CAAC,OAAO,SAAS,IAAI,CAAC,CAAC;AAEtE,cAAQ,IAAI;QACZ,KAAK;AAAK,wBAAA,QAAS,aAAa,OAAO,SAAS,CAAC;AAAG;QACpD,KAAK;AAAK,wBAAA,QAAS,aAAa,OAAO,UAAU,CAAC;AAAG;QACrD;AACE,wBAAA,QAAS,aAAa,OAAO,UAAU,CAAC;AACxC,wBAAA,QAAS,aAAa,OAAO,SAAS,CAAC;;AAEzC,UAAM,OAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,KAAK,CAAC;AACrD,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,MAAM,SAAS,QAAmB,MAAY;AAExD,UAAM,MAAM,OAAO,OAAO,QAAQ,WAAW,CAAC,OAAO,SAAS,IAAI,CAAC,GAAG,EAAC,OAAO,EAAC,CAAC;AAChF,UAAI,SAAS,UAAU;AAErB,sBAAA,QAAS,aAAa,KAAK,UAAU,SAAS;;AAEhD,UAAM,OAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,CAAC;AACnD,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,aAAa,SAAS,QAAmB,MAAY;AAE/D,UAAI,IAAI,OAAO,SAAS,IAAI;AAC5B,UAAI,OACF,OAAO,YAAY,OAAO,UAAU,EAAE,cAAc,EAAC,MAAM,OAAO,WAAW,MAAM,WAAU,CAAC;AAEhG,UAAI,EAAE,OAAO,CAAC,MAAM,KAAK;AAEvB,YAAI,EAAE,MAAM,CAAC;AACb,eAAO,KAAK,OAAO,CAAC,MAAM,UAAU,YAAY;;AAElD,UAAI,SAAS,WAAW;AAEtB,aAAK,YAAY,MAAM,MAAM,CAAC;AAC9B,aAAK,YAAY,MAAM,MAAM,CAAC;aACzB;AAEL,aAAK,YAAY,MAAM,MAAM,CAAC;AAC9B,aAAK,YAAY,MAAM,MAAM,CAAC;;AAEhC,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,gBAAgB,SAAS,QAAmB,MAAY;AAElE,UAAI,IAAI,OAAO,SAAS,IAAI;AAC5B,UAAI,KAAM,EAAE,OAAO,CAAC,MAAM,MAAM,EAAE,MAAM,CAAC,IAAI,MAAM;AACnD,UAAI,SAAS,cAAc;AACzB,YAAI,MAAM;AACV,YAAI;AACJ,aAAK;;AAEP,aAAO,KACL,OAAO,YAAY,OAAO,UAAU,EAAE,cAAc;QAClD,MAAM,OAAO;QAAW,MAAM;QAC9B,MAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,EAAC,OAAO,EAAC,CAAC;QACrD,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,EAAC,OAAO,GAAE,CAAC;OAAE,CAAC;IAC/D;AAQA,gBAAY,QAAQ,SAAS,QAAmB,MAAY;AAE1D,UAAM,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAClB,EAAC,OAAO,OAAO,SAAS,IAAI,EAAC,CAAC;AACzD,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,YAAY,SAAS,QAAmB,OAAa;AAC/D,aAAO,KAAK,OAAO,YAAY,OAAO,WAAW,CAAC;IACpD;AASA,gBAAY,OAAO,SAAS,QAAmB,MAAc,OAAa;AAExE,UAAM,IAAI,OAAO,SAAS,IAAI,GAC9B,IAAI,OAAO,SAAS,IAAI,GACxB,IAAI,OAAO,SAAS,IAAI;AACxB,UAAI,MAAe,EAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,EAAC;AACjD,UAAI,UAAU,SAAS;AACrB,YAAI,gBAAgB,IAAK,OAAO,MAAM,IAAI,OAAO,KAAK;;AAExD,UAAM,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,GAAG;AACpD,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,OAAO,SAAS,QAAmB,MAAY;AAEzD,UAAM,IAAI,OAAO,YAAY,IAAI,GACjC,IAAI,OAAO,SAAS,IAAI,GACxB,IAAI,OAAO,SAAS,IAAI;AACxB,UAAI,MAAM,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI;QAC5C,OAAO;QAAG,QAAQ;QAClB,gBAAiB,OAAO,MAAM,IAAI,OAAO,KAAK;OAAU;AAC1D,UAAI,GAAG;AACL,cAAM,OAAO,OAAO,QAAQ,WAAW,CAAC,GAAG,GAAG,EAAC,SAAS,EAAC,CAAC;AAC1D,YAAI,EAAE,MAAM,KAAK,GAAG;AAClB,wBAAA,QAAS,aAAa,KAAK,UAAU,CAAC;AACtC,wBAAA,QAAS,aAAa,KAAK,SAAS,MAAM,EAAE,OAAO,CAAC,CAAC;eAChD;AACL,wBAAA,QAAS,aAAa,KAAK,UAAU,MAAM,CAAC;;;AAGhD,aAAO,KAAK,GAAG;IACjB;AASA,gBAAY,UAAU,SAAS,QAAmB,MAAc,QAAgB,MAAY;AAE1F,cAAQ;AACR,UAAI,UAAU,OAAO,IAAI,EAAE,QAAQ,gBAAgB,IAAI,IAAI;AAC3D,UAAM,QAAQ,OAAO,aAAa,MAAM,IAAI;AAC5C,UAAM,KAAK,OAAO,OAAO,SAAS,MAAM;QACtC,SAAS;QAAS,SAAS;QAC3B,OAAO;QAAM,UAAU;QAAM,WAAW;SACvC,KAAK;AACR,UAAM,OAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,EAAE,GAAG,EAAC,UAAU,OAAM,CAAC;AACtE,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,WAAW,SAAS,QAAmB,MAAY;AAE7D,UAAM,MAAM,OAAO,UAAU,MAAM,QAAQ;AAC3C,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,UAAM,OAAO,OAAO,OAAO,QAAQ,YAAY;AAE/C,oBAAA,QAAS,SAAS,MAAM,GAAG,GAAG;AAC9B,oBAAA,QAAS,SAAS,MAAM,GAAG,IAAI;AAC/B,oBAAA,QAAS,SAAS,MAAM,GAAG,GAAG;AAC9B,UAAM,OAAO,OAAO,OAAO,QAAQ,WAAW,CAAC,IAAI,GAAG,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;AAC9E,aAAO,KAAK,IAAI;IAClB;AAUA,gBAAY,OAAO,SAAS,QAAmB,MAAc,OAAe,MAAa;AAEvF,aAAO,QAAQ,eAAA,QAAU,aAAa,QAAQ,OAAO,YAAY,IAAI,GAAG,OAAO,IAAI,CAAC;IACtF;AAOA,gBAAY,OAAO,SAAS,QAAmB,MAAY;AAEzD,UAAM,WAAW,eAAA,QAAU,aAAa,QAAQ,OAAO,YAAY,IAAI,CAAC;AACxE,UAAM,OAAO,OAAO,OAAO,QAAQ,YAAY,UAAU,EAAC,UAAU,MAAK,CAAC;AAC1E,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,WAAW,SAAS,QAAmB,MAAY;AAC7D,UAAM,QAAQ,OAAO,YAAY,IAAI;AACrC,UAAM,MAAM,OAAO,YAAY,IAAI,KAAK;AACxC,UAAI,MAAM,eAAA,QAAU,aAAa,QAAQ,OAAO,YAAY,IAAI,CAAC;AACjE,UAAI,OAAO;AACT,cAAM,CAAC,OAAO,OAAO,QAAQ,WAAW,KAAK;UAC3C;UACA,eAAc,GAAA,aAAA,QAAO,KAAK,EAAC,GAAG,QAAQ,GAAG,QAAO,GAAG,QAAQ;SAC5D,CAAC;;AAEJ,UAAM,OAAO,OAAO,OAAO,QAAQ,WACR,CAAC,OAAO,OAAO,QAAQ,YAAY,KAAK,EAAC,UAAU,MAAK,CAAC,CAAC,GAC1D,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;AACnD,aAAO,KAAK,IAAI;IAClB;AAQA,gBAAY,MAAM,SAAS,QAAmB,OAAa;AAGzD,aAAO,KAAK,OAAO,YAAY,OAAO,KAAK,CAAC;IAC9C;AAQA,gBAAY,OAAO,SAAS,QAAmB,OAAa;AAE1D,UAAM,cAAc,cAAA,QAAS,aAAa,MAAM;AAChD,UAAM,cAAc,cAAA,QAAS,aAAa,MAAM;AAChD,UAAM,QAAQ,OAAO,OAAO,SAAS,MAAM,EAAC,UAAU,MAAK,GAAG,WAAW;AACzE,UAAM,QAAQ,OAAO,OAAO,SAAS,MAAM,EAAC,UAAU,MAAK,GAAG,WAAW;AACzE,aAAO,KACL,OAAO,YAAY,OAAO,MAAM,EAAE,cAAc;QAC9C;QACA;OACD,CAAC;IACN;AAgBA,gBAAY,SAAS,SAAS,QAAmB,OACnB,MAAc,OAAe,OAC7B,SAAiB,UAAkB,OACnC,OAAgB,UAAiB;AAC7D,UAAM,IAAI,OAAO,QAAO;AACxB,UAAI,MAAM,IAAI;AAEZ,cAAM,IAAI,cAAA,QAAS,iBAAiB,2BAA2B,OAAO,SAAS;;AAEjF,UAAI,MAAM,KAAK;AAEb,eAAO;aACF;AAEL,eAAO,SAAS,IAAI,MAAM,OAAO,OAAO,MAAM,OAAO,IAAI,CAAC;AAC1D,eAAO,IAAI;;AAGb,UAAM,QAAQ,OAAO,YAAY,OAAO,OAAO,EAAE,YAAY,gBAAgB,IAAI;AACjF,YAAM,WAAW;QACf,YAAa,YAAY;QACzB,eAAgB,WAAW;;AAE7B,UAAI,OAAO;AAET,cAAM,YAAY,WAAW,IAAI;;AAEnC,UAAI,UAAU;AAEZ,cAAM,YAAY,cAAc,IAAI;AACpC,cAAM,SAAS,OAAO;;AAExB,UAAI,QAAQ,OAAO;AAEjB,cAAM,YAAY,QAAQ,IAAI;AAC9B,cAAM,YAAY,SAAS,KAAK;;AAElC,UAAI,UAAU,KAAK;AAEjB,cAAM,SAAS,eAAe;;AAEhC,UAAI,SAAS,MAAM;AAEjB,cAAM,SAAS,cAAc;;AAE/B,aAAO,KAAK,KAAK;IACnB;AAQA,gBAAY,QAAQ,SAAS,QAAmB,MAAY;AAE1D,aAAO,KAAK,OAAO,YAAY,OAAO,MAAM,EAAE,cAAc,EAAC,SAAS,MAAM,KAAU,CAAC,CAAC;AACxF,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAM,MAAM,IAAI,YAAY,UAAU;AACtC,UAAM,QAAQ,IAAI,YAAY,SAAS;AACvC,UAAI,CAAC,SAAS,CAAC;AAAK;AAKpB,UAAM,MAAM,OAAO;AACnB,UAAI,SAAS,GAAG,QAAQ,IAAI,IAAI,OAAO,GAAG,IAAI,IAAI;AAClD,UAAM,MAAO,MAAM,IAAI,OAAO,kBAAA,OAAkB,IAAI,QAAQ,MAAM,KAAK,GAAC,KAAA,CAAK,IAAI;AAIjF,aAAO,IAAI,GAAG;AACZ,YAAM,IAAI,IAAI,OAAO,CAAC;AACtB,YAAI,MAAM,KAAK;AAIb;AACA;mBACS,MAAM,KAAK;AAUpB,cAAI,WAAW,GAAG;AAChB,gBAAI;iBACC;AACL;AACA,gBAAI,WAAW,KAAK,QAAQ,GAAG;AAC7B,sBAAQ,IAAI,OAAO;;AAErB;;mBAEO,MAAM,OAAO,WAAW,GAAG;AAKpC,gBAAM,IAAI,cAAA,QAAS,iBAAiB,qCAAqC;mBAChE,MAAM,MAAM;AAMrB,cAAM,OAAO,IAAI,OAAO,CAAC;AACzB,cAAI,KAAK,MAAM,yBAAyB,KAAM,OAAO,KAAK,MAAM,GAAG,GAAI;AACrE,gBAAI;iBACC;AACL,iBAAK;;eAEF;AAIL;;;AAQJ,UAAM,OAAO,IAAI,OAAO,OAAO,GAAG,IAAI,OAAO,CAAC;AAC9C,UAAI,CAAC,KAAK,MAAM,qBAAqB,KAAK,UAAU,KAAK,QAAQ,QAAQ,EAAE,EAAE,SAAS,GAAG;AACvF,YAAM,WAAW,eAAA,QAAU,aAAa,QAAQ,eAAA,QAAU,WAAW,IAAI,GAAG,CAAC;AAC7E,eAAO,QAAQ,QAAQ;AACvB,eAAO,IAAI;;IAEf;AAOA,gBAAY,KAAK,SAAS,QAAmB,MAAY;AAEvD,aAAO,KACL,OAAO,YAAY,OAAO,MAAM,EAAE,cAAc,EAAC,MAAM,MAAM,KAAU,CAAC,CAAC;IAC7E;AAUA,gBAAY,UAAU,SAAS,QAAmB,MAAc,YAA2B;AAA3B,UAAA,eAAA,QAAA;AAAA,qBAAA;MAA2B;AACzF,UAAI;AACJ,UAAI,CAAC,YAAY;AAGf,YAAI,OAAO,OAAO,OAAO,OAAO,CAAC,MAAM,KAAK;AAC1C,iBAAO;;AAET,YAAI,OAAO,OAAO,OAAO,OAAO,CAAC,MAAM,KAAK;AAC1C,cAAI,MAAM,OAAO,YAAY,MAAM,EAAE;AACjC,cAAA,KAAA,OAAkB,eAAA,QAAU,WAAW,GAAG,GAAC,CAAA,GAA1C,QAAK,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AAEhB,cAAI,OAAO,CAAC,OAAO;AAEjB,kBAAM,IAAI,cAAA,QAAS,0BACA,8CAA8C,OAAO,SAAS;;AAEnF,cAAI,QAAQ;;;AAGhB,aAAO,KACL,OAAO,YAAY,OAAO,MAAM,EAAE,cAAc,EAAC,MAAM,MAAM,MAAY,WAAW,KAAI,CAAC,CAAC;AAE5F,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI;AACJ,UAAI,eAAe,MAAM,WAAW;AAElC,YAAI,GAAG;AACL,cAAI,cAAc,CAAC;;aAEhB;AACL,YAAI,GAAG;AAEL,iBAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,EAAC,OAAO,EAAC,CAAC;AACrD,iBAAO,KAAK,IAAI;;AAGlB,eAAO,OAAO,OAAO,QAAQ,UAAU,CAAA,GAAI,EAAC,WAAW,kBAAA,YAAY,UAAU,QAAO,CAAC;AACrF,eAAO,KAAK,IAAI;;IAEpB;AAQA,gBAAY,QAAQ,SAAS,QAAmB,OAAe,OAAa;AAC1E,UAAI,SAAS,MAAM;AACjB,gBAAQ;;AAEV,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI,EAAE,eAAe,MAAM,cAAc,IAAI,KAAI,GAAI;AAEnD,cAAM,IAAI,cAAA,QAAS,aAAa,gBAAgB,OAAO,SAAS;;AAElE,UAAI,CAAC,IAAI,MAAM,QAAQ;AAErB,YAAI,MAAM,KAAK,KAAK;aACf;AAEL,YAAM,QAAS,IAAI,SAAS,UAAU,IAAK,IAAI,SAAS,UAAU,EAAa,MAAM,GAAG,IAAI,CAAA;AAC5F,eAAO,MAAM,SAAS,IAAI,MAAM,QAAQ;AACtC,gBAAM,KAAK,MAAM;;AAEnB,cAAM,IAAI,MAAM,SAAS,CAAC,IAAI;AAC9B,YAAI,SAAS,UAAU,IAAI,MAAM,KAAK,GAAG;;IAE7C;AAQA,gBAAY,QAAQ,SAAS,QAAmB,OAAa;AAC3D,UAAM,MAAM,OAAO,MAAM,IAAG;AAC5B,UAAI,eAAe,MAAM,WAAW;AAElC,YAAI,MAAM,KAAK,IAAI,KAAI,CAAE;aACpB;AAEL,cAAM,IAAI,cAAA,QAAS,oBAAoB,yBAAyB,OAAO,SAAS;;IAEpF;AAYA,gBAAY,WAAW,SAAS,QAAmB,MAAY;AAE7D,UAAI,MAAM,OAAO,YAAY,IAAI;AACjC,UAAI,IAAI,MAAM,KAAK,GAAG;AAEpB,cAAM,IAAI,cAAA,QAAS,cAAc,iCAAmC,GAAG;;AAEzE,UAAI,QAAQ,OAAO,cAAc,SAAS,IAAI,aAAa,EAAE,OAAO,GAAG;AACvE,UAAI,SAAS,SAAS,SAAS;AAI7B,YAAI,CAAC,MAAM,KAAK,CAAC,GAAG;AAClB,cAAM,MAAM,OAAO,YAAY,OAAO,KAAK,EAAE,YAAY,QAAQ,GAAG;AACpE,iBAAO,KAAK,GAAG;AACf;;AAGF,eAAO,MAAM,IAAI,SAAS,IAAI;;AAEhC,qBAAA,QAAU,eAAe,QAAQ,KAAK;AACtC,aAAO,MAAM,eAAe,CAAC,QAAQ,GAAG,CAAC;IAC3C;AAeA,gBAAY,QAAQ,SAAS,QAAmB,OACnB,MAAc,OAAe,OAC7B,SAAiB,UAAkB,OACnC,cAAqB;AAChD,UAAI,CAAC,OAAO;AAEV,gBAAQ,OAAO,YAAY,aAAa,MAAM,QAAO,IAAK,GAAG;;AAE/D,UAAI,SAAS,MAAM,OAAO,QAAQ,aAAa,EAAE,EAAE,QAAQ,iBAAiB,IAAI;AAChF,cAAQ,MAAM,QAAQ,WAAW,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AACvD,cAAQ,MAAM,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,QAAQ;AACjF,UAAM,QAAQ,OAAO,YAAY,OAAO,OAAO;AAC/C,YAAM,WAAW;QACf,aAAa;QACb,eAAgB,WAAW;QAC3B,YAAa,YAAY;;AAE3B,UAAI,MAAM,MAAM,MAAM,GAAG;AAEvB,YAAI,MAAM,OAAO,CAAC,EAAE,MAAM,MAAM,GAAG;AAEjC,gBAAM,MAAM,KAAK,MAAM;AACvB,gBAAM,SAAS,MAAM,OAAO,CAAC,MAAM;;AAErC,YAAI,MAAM,OAAO,MAAM,SAAS,CAAC,EAAE,MAAM,MAAM,GAAG;AAEhD,gBAAM,MAAM,KAAK,OAAO;;AAG1B,gBAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC;AACxC,cAAM,SAAS,cACb,MAAM,MAAM,EAAE,EAAE,KAAK,GAAG,EAAE,QAAQ,WAAW,MAAM,EAAE,QAAQ,OAAO,OAAO,EAAE,QAAQ,MAAM,QAAQ;;AAEvG,UAAI,MAAO;AAET,cAAM,YAAY,QAAQ,OAAO,iBAAiB,IAAI,CAAC;;AAEzD,UAAI,OAAO;AAET,cAAM,YAAY,SAAS,OAAO,iBAAiB,KAAK,CAAC;;AAE3D,WAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAM;AACpC,cAAM,SAAS,cAAc,IAAI;AACjC,gBAAQ,MAAM,OAAO,CAAC;;AAExB,UAAI,UAAU,KAAK;AAEjB,cAAM,SAAS,cAAc,IAAI;iBAE1B,OAAO;AAEd,cAAM,SAAS,cAAc,IAAI;;AAEnC,UAAI,UAAU,KAAK;AAEjB,cAAM,SAAS,aAAa,IAAI;;AAElC,UAAI,cAAe;AAEjB,cAAM,SAAS,WAAW,IAAI;;AAEhC,aAAO,KAAK,KAAK;AACjB,aAAO;IACT;AAQA,gBAAY,eAAe,SAAS,QAAmB,OAAgB;AAErE,UAAM,QAAQ,OAAO,YAAY,aAAa,MAAM,QAAO,IAAK,GAAG;AACnE,UAAI,OAAO,YAAY,MAAM,QAAQ,KAAK;AAC1C,aAAO,eAAA,QAAU,cAAc,MAAyB,KAAK;IAC/D;AASA,gBAAY,WAAW,SAAU,QAAmB,OAAkB,UAAiB;AACrF,aAAO,KAAK,KAAK;AACjB,qBAAA,QAAU,YAAY,MAAM;AAC5B,aAAO,OAAO,YAAY,OAAO,YAAY,QAAQ,EACnD,YAAY,QAAQ,MAAM,QAAO,CAAE;IACvC;AAYA,gBAAY,WAAW,SAAS,QAAmB,OACnB,UAAmB,UACnB,OAAe,SAAe;AAE5D,aAAO,KAAK,KAAK;AACjB,UAAI,UAAU;AACZ,uBAAA,QAAU,YAAY,MAAM;;AAE9B,cAAQ,MAAM,QAAQ,WAAW,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AACvD,cAAQ,MAAM,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,QAAQ;AACjF,UAAI,UAAU,OAAO,YAAY,OAAO,YAAY,MAAM,QAAO,GACzB,UAAU,UAAU,OAAO,MAAM,MAAM;AAC/E,cAAQ,WAAW;QACjB,cAAc;QACd,aAAa;QACb,eAAgB,WAAW;QAC3B,YAAY;QACZ,MAAM,OAAO,QAAQ,SAAS;QAC9B,iBAAiB,OAAO,QAAQ,WAAW;;AAE7C,aAAO;IACT;AAQA,gBAAY,cAAc,SAAS,QAAmB,OAAa;AACjE,aAAO,KAAK,MAAK;IACnB;AAQA,gBAAY,cAAc,SAAS,QAAmB,MAAY;AAEhE,UAAI,QAAQ,OAAO,YAAY,IAAI;AACnC,UAAI,UAAU,IAAI;AAEhB;;AAEF,UAAI,CAAC,OAAO,KAAK,WAAW;AAE1B,YAAI,OAAO,KAAK,OAAO;AAErB,gBAAM,IAAI,cAAA,QAAS,mBAAmB,eAAe,OAAO,SAAS;;AAEvE,eAAO,KAAK,QAAQ;AACpB,aAAK,OAAO,KAAK,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,MAAM,CAAC,OAAO,QAAQ,uBAAuB,GAAG;AAE3G,gBAAM,IAAI,cAAA,QAAS,iBAAiB,+BAAiC,KAAK;;AAG5E,eAAO,KAAK,OAAO,KAAK,IAAI,IAAI,UAAA,MAAK;;IAEzC;AASA,gBAAY,YAAY,SAAS,QAAmB,MAAc,OAAc;AAE9E,UAAI,QAAQ,OAAO,YAAY,IAAI;AACnC,UAAI,MAAM,OAAO,KAAK,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK;AAClE,UAAI,CAAC,KAAK;AAER,YAAI,CAAC,OAAO,KAAK,WAAW;AAC1B,iBAAO,KAAK,OAAO;;AAErB,cAAM,IAAI,UAAA,MAAK;;AAEjB,UAAI,MAAM,IAAI;AACd,UAAI,OAAO;AAET,cAAM,OAAO,KAAK,UAAU,GAAG;;AAEjC,UAAI,OAAO,OAAO,OAAO,QAAQ,QAAQ,eAAA,QAAU,aAAa,QAAQ,GAAG,GAAG;QAC5E,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI,OAAO,QAAQ,OAAO;QAAG,SAAS;OACvE;AACD,aAAO,KAAK,IAAI;IAClB;AAOA,gBAAY,QAAQ,SAAS,QAAmB,MACnB,OAAe,UACf,KAAY;AACvC,UAAI,UAAU;AACZ,YAAM,OAAiB,CAAA;AACvB,YAAI,OAAO,MAAM;AACf,cAAM,WAAW,OAAO,YAAY,IAAI;AACxC,eAAK,KAAK,YAAY,OAAO,MAAM,QAAQ;;AAE7C,iBAAS,IAAI,KAAK,QAAQ,IAAI,UAAU,KAAK;AAC3C,eAAK,KAAK,OAAO,YAAY,IAAI,CAAC;;AAEpC,gBAAQ,eAAA,QAAU,eAAe,QAAQ,MAAM,KAAK;;AAEtD,aAAO,SAAS,eAAA,QAAU,QAAQ,QAAQ,OAAO,OAAO,OAAO,MAAM,OAAO,CAAC,CAAC;AAC9E,aAAO,IAAI;AACX,qBAAA,QAAU,eAAe,MAAM;IACjC;AASA,gBAAY,aAAa,SAAS,QAAmB,MAAY;AAC/D,UAAM,IAAK,OAAO,SAAS,IAAI;AAC/B,UAAM,IAAK,OAAO,SAAS,IAAI;AAC/B,UAAM,IAAK,OAAO,SAAS,IAAI;AAC/B,UAAM,KAAK,OAAO,SAAS,IAAI;AAC/B,aAAO,KAAK,OAAO,OAAO,QAAQ,cAAc,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IAChE;AAGA,YAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvjDf,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,oBAAA;AAEA,QAAA,iBAAA,gBAAA,mBAAA;AAGA,QAAU;AAAV,KAAA,SAAUC,eAAY;AAOpB,eAAgB,SAAS,QAAmB,GAAS;AAEnD,YAAM,MAAM,eAAA,QAAU,WAAW,MAAM;AACvC,YAAM,MAAM,OAAO,MAAM;AACzB,YAAI,IAAI,0BAA0B,IAAI,SAAS,IAAI;AACjD,cAAI,OAAO,OAAO,OAAO,OAAO,IAAI,CAAC,EAAE,MAAM,IAAI,sBAAuC,EAAE,CAAC;AAC3F,iBAAO,KAAK,EAAE,SAAS;AACvB,cAAI,IAAI,gBAAgB,kBAAA,YAAY,QAAQ,UAAU,IAAI,YAAY,EAAE,SAAS,GAAG;AAClF,gBAAI,SAAS;;;AAIjB,YAAM,OAAO,OAAO,OAAO,SAAS,MAAM,KAAK,CAAC;AAChD,eAAO,KAAK,IAAI;MAClB;AAdgB,MAAAA,cAAA,WAAQ;AAuBxB,eAAgB,MAAM,QAAmB,GAAS;AAChD,YAAI;AACJ,YAAM,UAAU,OAAO,cAAc,QAAQ,QAAQ;AACrD,YAAM,IAAI,OAAO,OAAO,MAAM,OAAO,IAAI,CAAC,EAAE,MAAM,OAAO;AAEzD,YAAM,MAAM,eAAA,QAAU,WAAW,MAAM;AACvC,YAAI,GAAG;AAEL,gBAAM,OAAO,OAAO,SAAS,MAAM,KAAK,EAAE,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC;AACjE,iBAAO,KAAK,EAAE,CAAC,EAAE,SAAS;eACrB;AAEL,gBAAM,OAAO,OAAO,SAAS,MAAM,KAAK,CAAC;;AAE3C,eAAO,KAAK,GAAG;MACjB;AAfgB,MAAAA,cAAA,QAAK;AAsBrB,eAAgB,gBAAgB,QAAmB,IAAU;AAC3D,YAAM,OAAO,OAAO,MAAK;AACzB,eAAO,MAAM,SAAS,CAAC,QAAQ,IAAI,CAAC;MACtC;AAHgB,MAAAA,cAAA,kBAAe;AAW/B,eAAgB,YAAY,QAAmB,OAAa;AAC1D,YAAM,MAAM,MAAM,cAAc,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM;AAExE,YAAM,OAAO,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI;AACzD,eAAO,KAAK,IAAI;MAClB;AALgB,MAAAA,cAAA,cAAW;AAY3B,eAAgB,YAAY,QAAmB,OAAa;AAC1D,YAAM,MAAM,MAAM,cAAc,CAAA;AAChC,YAAI,UAAU,IAAI;AAElB,YAAM,OAAO,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI;AACzD,sBAAA,QAAS,YAAY,MAAM,eAAe,IAAI;AAC9C,eAAO,cAAc,QAAQ,eAAe,IAAI;AAEhD,eAAO,KAAK,IAAI;MAClB;AATgB,MAAAA,cAAA,cAAW;AAgB3B,eAAgB,UAAU,QAAmB,OAAa;AACxD,YAAM,MAAM,MAAM,cAAc,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM;AACxE,YAAI,OAAO,MAAM,IAAI,MAAM,GAAG;AAE5B,cAAI,aAAa,IAAI,OAAO,MAAM,IAAI,MAAM;;AAG9C,YAAM,OAAO,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI;AACzD,eAAO,KAAK,IAAI;MAClB;AATgB,MAAAA,cAAA,YAAS;AAgBzB,eAAgB,UAAU,QAAmB,OAAa;AACxD,YAAI,MAAM,MAAM,cAAc,CAAA;AAE9B,cAAM,OAAO,OAAO,EAAC,OAAO,OAAO,UAAU,MAAK,GAAG,GAAG;AACxD,YAAM,OAAO,OAAO,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI;AACzD,eAAO,KAAK,IAAI;MAClB;AANgB,MAAAA,cAAA,YAAS;AAgBzB,eAAgB,YAAY,QAAmB,KAAa,MAAgB,MAAW;AACrF,YAAM,MAAM,KAAK,CAAC;AAClB,YAAI,MAAM,OAAO,YAAY,OAAO,OAAO,EAAE,cAAc,EAAC,MAAM,KAAK,IAAQ,CAAC;AAChF,cAAM,KAAI,MAAA,QAAA,cAAA,CAAC,QAAQ,GAAG,GAAA,OAAK,KAAK,MAAM,CAAC,CAAC,GAAA,KAAA,CAAA;AACxC,eAAO,KAAK,GAAG;MACjB;AALgB,MAAAA,cAAA,cAAW;IAO7B,GAlIU,iBAAA,eAAY,CAAA,EAAA;AAoItB,YAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5If,QAAA,KAAA,aAAA,mBAAA;AACA,QAAA,oBAAA;AACA,QAAA,mBAAA,gBAAA,qBAAA;AACA,QAAA,oBAAA,gBAAA,sBAAA;AACA,QAAA,iBAAA,gBAAA,mBAAA;AACA,QAAA,eAAA;AACA,QAAA,eAAA;AAMA,QAAI,GAAG,UAAU,UAAU,kBAAA,QAAa,UAAU,QAAQ;AAM1D,QAAI,GAAG,UAAU,SAAS,kBAAA,QAAa,OAAO,SAAS;AAMvD,QAAI,GAAG,UAAU,WAAW,kBAAA,QAAa,iBAAiB,KAAK;AAM/D,QAAI,GAAG,SAAS,WAAW;MAKzB,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,MAAO;MACP,MAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAO;MACP,KAAU;MACV,KAAU;OACT,iBAAA,OAAW;AAMd,QAAI,GAAG,aAAa,eAAe,kBAAA,QAAa,aAAa;MAE3D,OAAc;MACd,MAAc;MACd,OAAc;MACd,OAAc;MACd,SAAc;MACd,MAAc;MACd,KAAc;MACd,OAAc;MACd,MAAc;MACd,OAAc;MACd,QAAc;MACd,IAAc;MACd,IAAc;MACd,IAAc;MACd,SAAc;MACd,IAAc;MACd,KAAc;MACd,OAAc;MACd,KAAc;MACd,SAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;MACd,OAAc;MACd,YAAc;MACd,UAAc;MACd,OAAc;MACd,QAAc;MACd,UAAc;MACd,QAAc;MAGd,GAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,MAAc,CAAC,KAAU,EAAC,aAAa,KAAI,CAAC;MAC5C,OAAc;MACd,OAAc;MACd,KAAc;MACd,IAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,IAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,IAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,SAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,KAAI,CAAC;MAC5C,UAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,KAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,KAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,UAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,WAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,QAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,QAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,KAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,MAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,MAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,SAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,OAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,UAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,aAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,WAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;MAClE,WAAc,CAAC,KAAU,EAAC,aAAa,kBAAA,YAAY,QAAQ,OAAM,CAAC;KACnE;AAMD,QAAI,GAAG,aAAa,eAAe,kBAAA,QAAa,aAAa;MAC3D,MAAc;MAGd,QAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,QAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,UAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,UAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,QAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,QAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,OAAc,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,GAAE,CAAC;MAChD,OAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;QAAM,eAAe;MAAI,CAAC;MAChE,MAAc,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,GAAE,CAAC;MAChD,OAAc,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,GAAE,CAAC;MAChD,MAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,KAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,WAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,UAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,SAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,MAAc,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,GAAE,CAAC;MAChD,UAAc,CAAC,KAAU;QAAC,UAAU,aAAA,SAAS;QACnB,YAAY;MAAI,CAAC;MAC3C,UAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MAGzC,cAAmB;MACnB,eAAmB;MACnB,eAAmB;MACnB,iBAAmB;MACnB,OAAc;MACd,MAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;MACd,SAAc;MACd,QAAc;MACd,OAAc;MACd,OAAc;MACd,OAAc;MACd,OAAc;MACd,SAAc;MACd,QAAc;MACd,IAAc;MACd,KAAc;MACd,WAAc;MACd,MAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MACzC,QAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MACzC,QAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MACzC,QAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MACzC,OAAc,CAAC,KAAU,EAAC,SAAS,MAAK,CAAC;MACzC,IAAc;MACd,IAAc;MACd,MAAc;MACd,SAAc;MACd,UAAc;MACd,MAAc;MACd,KAAc;MACd,OAAc;MACd,MAAc;MAId,QAAc;MACd,YAAc;MACd,YAAc;MACd,UAAc;MACd,KAAc;MACd,OAAc;MACd,OAAc;MACd,KAAc;MACd,IAAc;MACd,KAAc;MACd,IAAc;MACd,IAAc;MACd,IAAc;MACd,MAAc;MACd,MAAc;MACd,QAAc;MACd,QAAc;MACd,QAAc;MACd,QAAc;MACd,QAAc;MACd,UAAc;MACd,UAAc;MACd,MAAc;MACd,IAAc;MACd,OAAc;MACd,MAAc;MACd,IAAc;MACd,IAAc;MACd,KAAc;MACd,OAAc;MACd,MAAc;MACd,OAAc;MACd,OAAc;MACd,OAAc;MACd,OAAc;MACd,IAAc;MACd,KAAc;MACd,MAAc;MACd,OAAc;MACd,QAAc;MACd,QAAc;MAEd,SAAc;MAId,gBAAoB;MACpB,WAAoB;MACpB,YAAoB;MACpB,gBAAoB;MACpB,WAAoB;MACpB,MAAoB;MACpB,YAAoB;MACpB,IAAmB,CAAC,KAAU,EAAC,QAAQ,MAAK,CAAC;MAC7C,QAAoB;MACpB,eAAoB;MACpB,iBAAoB;MACpB,gBAAoB;MACpB,kBAAoB;MACpB,SAAoB;MACpB,SAAoB;MACpB,SAAoB;MACpB,SAAoB;MACpB,mBAAoB;MACpB,gBAAoB;MACpB,eAAoB;MACpB,eAAoB;MACpB,eAAoB;MACpB,gBAAoB;MACpB,gBAAoB;MACpB,oBAAoB;MACpB,oBAAoB;MACpB,YAAoB;MAIpB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAClB,OAAkB;MAElB,OAAkB,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,MAAK,CAAC;MACvD,OAAkB,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,MAAK,CAAC;MACvD,OAAkB,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,MAAK,CAAC;KACxD;AAMD,QAAI,GAAG,aAAa,aAAa,kBAAA,QAAa,WAAW;MACvD,OAAc;MACd,OAAc;MACd,OAAc;MACd,QAAc;MACd,IAAc;MACd,IAAc;MACd,OAAc;MACd,SAAc;MACd,KAAc;MACd,KAAc;MACd,OAAc;MAEd,KAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;MACd,KAAc;KACf;AAMD,QAAI,GAAG,aAAa,aAAa,kBAAA,QAAa,WAAW;MACvD,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,QAAoB;MACpB,QAAoB;MACpB,KAAoB;MACpB,KAAoB,CAAC,KAAK,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;MAClD,KAAoB;MACpB,QAAoB;MACpB,gBAAoB;MACpB,gBAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,eAAoB;MACpB,eAAoB;MACpB,eAAoB;MACpB,UAAoB,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;MACvD,OAAoB,CAAC,KAAU,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;MACvD,UAAoB,CAAC,KAAK,EAAC,UAAU,aAAA,SAAS,IAAG,CAAC;MAClD,aAAoB;MACpB,eAAoB;MACpB,iBAAoB;MACpB,aAAoB;MACpB,eAAoB;MACpB,iBAAoB;MACpB,eAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,OAAoB;MACpB,OAAoB;MACpB,WAAoB;MACpB,WAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,YAAoB;MACpB,YAAoB;KACrB;AAMD,QAAI,GAAG,WAAW,UAAU;MAC1B,cAAmB,CAAC,YAAY,KAAK,MAAM,CAAC;MAC5C,WAAmB,CAAC,YAAY,KAAK,OAAO,CAAC;MAC7C,aAAmB,CAAC,YAAY,KAAK,OAAO,CAAC;MAC7C,mBAAmB,CAAC,YAAY,MAAM,OAAO,CAAC;MAE9C,IAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,MAAM;MACzD,KAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,MAAM;MACzD,UAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,QAAQ;MAC3D,KAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,YAAY;MAC/D,IAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,UAAU;MAC7D,IAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,IAAI;MACvD,QAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,YAAY;MAC/D,KAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,MAAM;MACzD,MAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,OAAO;MAC1D,IAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,SAAS;MAC5D,IAAmB,CAAC,WAAW,kBAAA,YAAY,QAAQ,SAAS;MAE5D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,YAAmB,CAAC,YAAY,EAAE;MAClC,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,IAAI;MACxD,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,IAAI;MACxD,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,UAAU;MAC9D,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,UAAU;MAC9D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,YAAY;MAChE,KAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,YAAY;MAChE,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,OAAO;MAC3D,YAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,WAAW;MAC/D,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,WAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,UAAU;MAC9D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,aAAa;MACjE,YAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,aAAa;MACjE,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,eAAe;MACnE,YAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,mBAAmB;MACvE,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,YAAY;MAChE,WAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,gBAAgB;MAEpE,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,WAAmB,CAAC,YAAY,EAAE;MAClC,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,IAAI;MACxD,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,IAAI;MACxD,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,UAAU;MAC9D,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,YAAY;MAChE,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,OAAO;MAC3D,WAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,WAAW;MAC/D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,MAAM;MAC1D,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,UAAU;MAC9D,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,aAAa;MACjE,WAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,aAAa;MACjE,SAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,eAAe;MACnE,WAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,mBAAmB;MACvE,OAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,SAAS;MAC7D,QAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,YAAY;MAChE,UAAmB,CAAC,YAAY,kBAAA,YAAY,QAAQ,gBAAgB;MAEpE,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,MAAM;MAC5D,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,MAAM;MAC5D,YAAmB,CAAC,MAAM;MAC1B,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,MAAM;MAC5D,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,IAAI;MAC1D,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,SAAS;MAC/D,QAAmB,CAAC,QAAQ,MAAM,kBAAA,YAAY,QAAQ,SAAS;MAE/D,MAAmB,CAAC,WAAW,GAAG;MAClC,MAAmB,CAAC,WAAW,GAAG;MAClC,YAAmB,CAAC,WAAW,GAAG;MAClC,OAAmB,CAAC,WAAW,IAAI;MACnC,YAAmB,CAAC,WAAW,CAAG;MAClC,OAAmB,CAAC,WAAW,GAAG;MAClC,OAAmB,CAAC,WAAW,IAAI;MACnC,OAAmB,CAAC,WAAW,IAAI;MACnC,MAAmB,CAAC,WAAW,IAAI;MACnC,MAAmB,CAAC,WAAW,IAAI;MAEnC,QAAoB;MACpB,QAAoB;MACpB,QAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,MAAoB;MACpB,KAAoB;MACpB,MAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,IAAoB;MACpB,KAAoB;MACpB,QAAmB,CAAC,WAAW,gBAAgB;MAC/C,QAAmB,CAAC,WAAW,gBAAgB;MAC/C,IAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,IAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,MAAoB;MACpB,KAAoB;MACpB,KAAoB;MACpB,MAAoB;MAEpB,QAAmB,CAAC,UAAU,CAAC;MAC/B,UAAmB,CAAC,UAAU,CAAC;MAE/B,UAAqB,CAAC,aAAa,MAAM;MACzC,WAAqB,CAAC,aAAa,MAAM;MACzC,WAAqB,CAAC,aAAa,QAAQ,CAAC;MAC5C,YAAqB,CAAC,aAAa,QAAQ,CAAC;MAC5C,WAAqB,CAAC,aAAa,MAAM;MACzC,YAAqB,CAAC,aAAa,MAAM;MACzC,gBAAqB,CAAC,aAAa,MAAM;MACzC,iBAAqB,CAAC,aAAa,MAAM;MACzC,eAAqB,CAAC,aAAa,MAAM;MACzC,gBAAqB,CAAC,aAAa,MAAM;MACzC,oBAAqB,CAAC,aAAa,MAAM;MACzC,qBAAqB,CAAC,aAAa,MAAM;MAEzC,SAAoB;MACpB,UAAoB;MACpB,cAAoB;MACpB,UAAoB,CAAC,SAAS,wCAAwC,CAAC;MACvE,UAAoB,CAAC,SAAS,wCAAwC,CAAC;MAEvE,MAAoB;MACpB,gBAAoB;MACpB,MAAoB;MACpB,gBAAoB;MACpB,OAAoB;MACpB,iBAAoB;MACpB,OAAmB,CAAC,QAAQ,KAAK,GAAG;MACpC,OAAmB,CAAC,QAAQ,KAAK,GAAG;MACpC,QAAmB,CAAC,QAAQ,KAAK,GAAG;MAEpC,MAAoB;MACpB,MAAoB;MACpB,MAAoB;MACpB,QAAmB,CAAC,YAAY,QAAQ;MACxC,UAAmB,CAAC,YAAY,UAAU;MAE1C,MAAoB;MACpB,OAAoB;MACpB,QAAoB;MAEpB,MAAoB;MACpB,MAAoB;MACpB,OAAoB;MACpB,OAAoB;MACpB,UAAoB;MACpB,WAAoB;MAEpB,KAAmB,CAAC,UAAU,aAAA,UAAU,aAAa;MACrD,KAAmB,CAAC,UAAU,aAAA,UAAU,eAAe;MACvD,KAAmB,CAAC,UAAU,aAAA,UAAU,eAAe;MACvD,KAAmB,CAAC,UAAU,aAAA,UAAU,cAAc;MACtD,KAAmB,CAAC,UAAU,aAAA,UAAU,qBAAqB;MAC7D,SAAmB,CAAC,UAAU,GAAE;MAChC,MAAmB,CAAC,UAAU,CAAC;MAC/B,OAAmB,CAAC,UAAU,CAAC;MAC/B,WAAmB,CAAC,UAAU,aAAA,UAAU,aAAa;MACrD,cAAmB,CAAC,UAAU,aAAA,UAAU,qBAAqB;MAE7D,OAAoB;MACpB,QAAoB;MACpB,MAAoB;MACpB,OAAoB;MACpB,QAAoB;MACpB,OAAoB;MACpB,MAAoB;MACpB,MAAmB,CAAC,MAAM;MAC1B,OAAmB,CAAC,QAAQ,OAAO;MACnC,WAAoB;MAEpB,KAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,KAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,MAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,MAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,MAAmB,CAAC,WAAW,aAAA,SAAS,MAAM,IAAI;MAClD,MAAmB,CAAC,WAAW,aAAA,SAAS,MAAM,IAAI;MAClD,OAAmB,CAAC,WAAW,aAAA,SAAS,MAAM,IAAI;MAClD,OAAmB,CAAC,WAAW,aAAA,SAAS,MAAM,IAAI;MAClD,MAAmB,CAAC,WAAW,aAAA,SAAS,OAAO,IAAI;MACnD,MAAmB,CAAC,WAAW,aAAA,SAAS,OAAO,IAAI;MACnD,OAAmB,CAAC,WAAW,aAAA,SAAS,OAAO,IAAI;MACnD,OAAmB,CAAC,WAAW,aAAA,SAAS,OAAO,IAAI;MACnD,MAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,MAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,OAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MACjD,OAAmB,CAAC,WAAW,aAAA,SAAS,KAAK,IAAI;MAEjD,SAAmB,CAAC,WAAW,aAAA,SAAS,GAAG;MAC3C,QAAmB,CAAC,WAAW,aAAA,SAAS,EAAE;MAC1C,UAAmB,CAAC,WAAW,aAAA,SAAS,IAAI;MAC5C,WAAmB,CAAC,WAAW,aAAA,SAAS,KAAK;MAC7C,SAAmB,CAAC,WAAW,aAAA,SAAS,GAAG;MAC3C,SAAmB,CAAC,WAAW,aAAA,SAAS,GAAG;MAC3C,WAAmB,CAAC,WAAW,aAAA,SAAS,KAAK;MAC7C,WAAmB,CAAC,WAAW,aAAA,SAAS,KAAK;MAE7C,SAAmB,CAAC,WAAW,aAAA,SAAS,OAAO;MAE/C,UAAoB;MAEpB,MAAoB,CAAC,QAAQ,CAAC;MAC9B,MAAoB;MACpB,MAAoB,CAAC,QAAQ,CAAC;MAC9B,MAAoB;MACpB,OAAoB,CAAC,SAAS,gCAAgC,CAAC;MAC/D,UAAoB;MAEpB,OAAoB;MACpB,WAAmB,CAAC,SAAS,eAAe;MAC5C,SAAoB;MACpB,UAAmB,CAAC,WAAW,GAAG,CAAC;MACnC,UAAmB,CAAC,WAAW,GAAG,CAAC;MACnC,OAAoB;MAEpB,OAAmB,CAAC,UAAU,MAAM;MACpC,OAAmB,CAAC,UAAU,MAAM;MACpC,MAAmB,CAAC,UAAU,MAAM;MACpC,OAAmB,CAAC,UAAU,MAAM;MACpC,KAAmB,CAAC,UAAU,MAAM;MACpC,OAAmB,CAAC,UAAU,MAAM;MACpC,OAAmB,CAAC,UAAU,MAAM;MACpC,KAAmB,CAAC,UAAU,MAAM;MACpC,KAAmB,CAAC,UAAU,MAAM;MACpC,KAAmB,CAAC,UAAU,MAAM;MACpC,WAAmB,CAAC,UAAU,QAAQ,CAAC;MACvC,SAAmB,CAAC,UAAU,QAAQ,CAAC;MAEvC,QAAoB;MACpB,OAAoB;MACpB,SAAmB,CAAC,UAAU,KAAK,GAAG;MACtC,OAAmB;QAAC;QAAU;QAAK;QAAI;QAAa;QAAM;QAAQ;QAC9C;MAAI;MACxB,SAAmB;QAAC;QAAU;QAAM;QAAM;SACtB,GAAA,aAAA,IAAG,aAAA,UAAU,cAAc;QAAG;QAAQ;MAAG;MAC7D,cAAmB,CAAC,UAAU,MAAM,MAAM,UAAU,MAAM,QAAQ,GAAG;MACrE,IAAoB;MACpB,MAAoB;MACpB,SAAmB,CAAC,WAAW,IAAI;MACnC,OAAmB,CAAC,SAAS,OAAO;MACpC,WAAmB,CAAC,SAAS,QAAQ;MAErC,WAAmB;QAAC;QAAU;QAAM;QAAM;SACtB,GAAA,aAAA,IAAG,aAAA,UAAU,cAAc;QAAG;QAAQ;QAAK;QAC3C;MAAO;MAC3B,YAAmB;QAAC;QAAU;QAAM;QAAM;SACtB,GAAA,aAAA,IAAG,aAAA,UAAU,cAAc;QAAG;QAAQ;QAAK;QAC3C;MAAM;MAC1B,OAAoB;MACpB,MAAoB;MACpB,QAAoB;MAGpB,MAAmB,CAAC,SAAS,sEACuB;MACpD,MAAmB,CAAC,SAAS,2CAA2C,CAAC;MACzE,KAAmB;QAAC;QAAS;QAET;MAAC;MACrB,KAAmB,CAAC,SAAS,iEACqB,CAAC;MACnD,KAAmB,CAAC,SAAS,4BAA4B;MACzD,MAAmB,CAAC,SAAS,uCAAuC,CAAC;MAErE,KAAmB,CAAC,SAAS,2BAA2B,CAAC;MACzD,KAAmB,CAAC,SAAS,4CAA4C;MACzE,OAAmB,CAAC,SAAS,+DAC4B;MACzD,KAAmB,CAAC,SAAS,WAAW;MAGxC,KAAoB;MACpB,MAAoB;MACpB,OAAoB;MACpB,KAAoB;MAIpB,OAAoB;MACpB,KAAoB;MAEpB,OAAoB;MACpB,KAAoB;MACpB,UAAoB;MAGpB,YAAoB;MACpB,UAAoB;OACnB,iBAAA,OAAW;AAMd,QAAI,GAAG,eAAe,eAAe,kBAAA,QAAa,aAAa;MAC7D,OAAe,CAAC,cAAc;MAC9B,UAAe,CAAC,YAAY,MAAM,IAAI;MACtC,UAAe;QAAC;QAAY;QAAM;QAAM;QAAM;QAC9B,eAAA,QAAU,KAAK,GAAG,aAAA,UAAU,cAAc;QAAG;MAAM;OAClE,iBAAA,OAAW;AAMd,QAAI,GAAG,aAAa,aAAa,MAAM;MACrC,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;MACV,KAAU;KACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/tBD,QAAA,qBAAA;AACA,QAAA,kBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AACA,QAAA,gBAAA,gBAAA,kBAAA;AAEA,QAAA,iBAAA;AACA,QAAA,QAAA,aAAA,mBAAA;AACA,QAAA,YAAA;AACA;AACA,QAAA,0BAAA;AAOA,QAAI,eAAA,aAAa,SAAS,MAAM;MAC9B,KAAO;MACP,KAAO;MACP,KAAO;KACR;AAQD,aAAgB,MAAM,QAAmB,MAAY;AACnD,UAAM,OAAO,OAAO,MAAM,IAAI,MAAM;AACpC,UAAI,MAAM,OAER,EAAC,aAAa,OAAO,MAAM,IAAI,MAAM,EAAC,IAAI,CAAA;AAC5C,UAAM,QAAS,gBAAA,WAAW,OAAO,OAAO,EAAmB,OAAO,IAAI;AACtE,UAAM,SAAQ,GAAA,wBAAA,UAAS,IAAI;AAC3B,UAAM,OAAQ,QAAQ,MAAM,CAAC,IAAI;AAGjC,UAAI,KAAK,OAAO,OAAO,SAAS,MAAM,KAAM,QAAQ,MAAM,OAAO,IAAK;AACtE,YAAM,CAAC,KAAK,GAAG,WAAW,IAAI,eAAe,MAAM,CAAC,CAAC;AACrD,UAAI,SAAS,MAAM;AACjB,sBAAA,QAAS,YAAY,IAAI,eAAe,IAAI;AAC5C,eAAO,cAAc,QAAQ,eAAe,EAAE;;AAEhD,aAAO,KAAK,EAAE;IAChB;AAjBA,YAAA,QAAA;AAyBA,aAAS,YAAY,SAAoB,MAAY;AAEnD,YAAM,IAAI,cAAA,QAAS,4BACC,iCAAiC,OAAO,IAAI;IAClE;AAQA,aAAS,aAAa,SAAoB,KAAW;AAEnD,YAAM,IAAI,cAAA,QAAS,cAAc,4BAA8B,GAAG;IACpE;AAMA,aAAS,gBAAgBC,KAA4B;;UAA3B,OAAIA,IAAA;;AAC5B,iBAAkB,KAAA,SAAA,KAAK,QAAQ,WAAW,CAAC,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAAxC,cAAM,MAAG,GAAA;AAKZ,cAAI,IAAI,WAAW,IAAI,aAAa,IAAI,GAAG;AAKzC,gBAAM,WAAS,IAAI;AACnB,qBAAO,WAAW,OAAO,SAAO,WAAW,GAAG,GAAG,CAAC;AAClD,iBAAK,eAAe,IAAI,MAAM,CAAC,GAAG,CAAC;AAMnC,gBAAI,IAAI,OAAO,MAAM,GAAG;AACtB,kBAAM,SAAS,IAAI,WAAW,CAAC;AAC/B,mBAAK,eAAe,UAAU,CAAC,MAAM,CAAC;AACtC,mBAAK,eAAe,UAAU,OAAO,WAAW,CAAC,EAAE,UAAuB;;qBAEnE,IAAI,OAAO,MAAM,GAAG;AAK7B,gBAAI,OAAO,aAAa,IAAI,WAAW,CAAC,GAAG,GAAG;AAC9C,iBAAK,eAAe,QAAQ,CAAC,GAAG,CAAC;;;;;;;;;;;;IAGvC;AAOA,QAAA,WAAA,SAAA,QAAA;AAA8B,gBAAAC,WAAA,MAAA;AAA9B,eAAAA,YAAA;;MAA6C;AAAA,aAAAA;IAAA,EAAf,UAAA,YAAY;AAA7B,YAAA,WAAA;AAOA,YAAA,oBAAmC,mBAAA,cAAc,OAC5D,QAAS;MACP,SAAS;QACP,WAAW,CAAC,WAAW,WAAW,UAAU,OAAO;QACnD,WAAW,CAAC,WAAW;QAEvB,OAAO,CAAC,aAAa,UAAU,eAAe,eAAe,WAAW;QACxE,aAAa,CAAC,aAAa;;MAE7B,UAAU;QACR,WAAW;QACX,OAAO;QACP,aAAa;;MAEf,QAAK,KAAA,CAAA,GAEH,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,WAAW,UAAU,IAAI,IAAG,MAAM,YACzC,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,OAAO,UAAU,IAAI,IAAG,MAAM,QACrC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,QAAQ,UAAU,IAAI,IAAG,MAAM,SACtC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,aAAa,UAAU,IAAI,IAAG,MAAM,cAC3C,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,QAAQ,UAAU,IAAI,IAAG,MAAM,SACtC,GAAC,MAAM,OAAO,UAAU,IAAI,IAAG,MAAM,QACrC,GAAC,MAAM,QAAQ,UAAU,IAAI,IAAG,MAAM,SACtC,GAAC,MAAM,cAAc,UAAU,IAAI,IAAG,MAAM,eAC5C,GAAC,MAAM,SAAS,UAAU,IAAI,IAAG,MAAM,UACvC,GAAC,MAAM,UAAU,UAAU,IAAI,IAAG,MAAM,WACxC,GAAC,MAAM,aAAa,UAAU,IAAI,IAAG,MAAM,cAC3C,GAAC,MAAM,aAAa,UAAU,IAAI,IAAG,MAAM;MAE7C,SAAS;QACP,WAAW;QACX,SAAU,OAAO,aAAc,eACrB,SAAS,qBAAqB,MAAM,EAAE,WAAW,IACjD,KAAK,OAAO,SAAS,QAAQ,EAAE,QAAQ,QAAQ,EAAE;;MAE7D,MAAM;QACJ,MAAM;;MAER,gBAAgB,CAAC,CAAC,iBAAiB,EAAE,CAAC;KACvC;;;", "names": ["Node<PERSON>til", "TexError", "<PERSON><PERSON><PERSON><PERSON>", "unit", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MmlStack", "BaseItem", "Dummy<PERSON><PERSON>", "StackItemFactory", "NodeFactory", "ParseOptions", "Label", "TagInfo", "AbstractTags", "NoTags", "AllTags", "TagsFactory", "Map<PERSON>andler", "SubHandler", "SubHandlers", "Configuration", "Configuration<PERSON>andler", "ParserConfiguration", "Symbol", "Macro", "AbstractSymbolMap", "RegExpMap", "AbstractParseMap", "CharacterMap", "DelimiterMap", "MacroMap", "CommandMap", "EnvironmentMap", "StartItem", "StopItem", "OpenItem", "CloseItem", "PrimeItem", "SubsupItem", "OverItem", "LeftItem", "Middle", "RightItem", "BeginItem", "EndItem", "StyleItem", "PositionItem", "CellItem", "MmlItem", "FnItem", "NotItem", "NonscriptItem", "DotsItem", "ArrayItem", "EqnArrayItem", "EquationItem", "TexConstant", "ParseMethods", "_a", "BaseTags"]}