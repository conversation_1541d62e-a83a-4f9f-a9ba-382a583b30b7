{"version": 3, "file": "mmultiscripts.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mmultiscripts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,iDAA2C;AAiC9B,QAAA,UAAU,GAAoC;IACzD,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;CACrB,CAAC;AAKW,QAAA,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAqB,CAAC;AA0E9E,SAAgB,wBAAwB,CAGtC,IAAO;IAEP;QAAqB,2BAAI;QAevB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAEd;YAbM,gBAAU,GAAe,IAAI,CAAC;YAK9B,oBAAc,GAAG,CAAC,CAAC;YAOxB,KAAI,CAAC,aAAa,EAAE,CAAC;;QACvB,CAAC;QASM,gCAAc,GAArB,UAAsB,GAAS,EAAE,IAAU;YACzC,IAAM,IAAI,GAAG,IAAI,cAAI,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAOM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YAKvD,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YACjD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAA,KAAA,OAAS,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAA,EAA7B,CAAC,QAAA,EAAE,CAAC,QAAyB,CAAC;YAIrC,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;aACzC;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,CAAC,IAAI,WAAW,CAAC;aACvB;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAKM,+BAAa,GAApB;YAIE,IAAM,IAAI,GAAe,IAAI,CAAC,UAAU,GAAG;gBACzC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,cAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,cAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,cAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,cAAI,CAAC,KAAK,EAAE;gBACxF,aAAa,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;aAChC,CAAC;YAIF,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACxE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5E,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAI1B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QACzC,CAAC;QAKM,oCAAkB,GAAzB;;YACE,IAAM,KAAK,GAAgB;gBACzB,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE;aAC/D,CAAC;YAKF,IAAI,MAAM,GAAmB,MAAM,CAAC;;gBACpC,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;oBAAhC,IAAM,KAAK,WAAA;oBACd,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;wBACpC,MAAM,GAAG,UAAU,CAAC;qBACrB;yBAAM;wBACL,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;wBACzC,MAAM,GAAG,kBAAU,CAAC,MAAM,CAAC,CAAC;qBAC7B;iBACF;;;;;;;;;YAID,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAItE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAQM,0BAAQ,GAAf,UAAgB,KAAa,EAAE,KAAa;YAC1C,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;gBAC/B,KAAK,CAAC,IAAI,CAAC,cAAI,CAAC,KAAK,EAAE,CAAC,CAAC;aAC1B;QACH,CAAC;QAQM,kCAAgB,GAAvB,UAAwB,KAAW,EAAE,KAAW,EAAE,KAAa,EAAE,KAAa;YAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,IAAA,KAAA,OAAe,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAA,EAAzC,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAA+B,CAAC;gBAC3C,IAAA,KAAA,OAAe,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAA,EAAzC,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAA+B,CAAC;gBACjD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC3B,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;gBACb,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;gBACb,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;oBAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC/B,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;oBAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC/B,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;oBAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC/B,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;oBAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;aAChC;QACH,CAAC;QAKM,8BAAY,GAAnB,UAAoB,IAAU;YACrB,IAAA,CAAC,GAAkB,IAAI,EAAtB,EAAE,CAAC,GAAe,IAAI,EAAnB,EAAE,CAAC,GAAY,IAAI,EAAhB,EAAE,MAAM,GAAI,IAAI,OAAR,CAAS;YAC/B,OAAO,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;QAC9C,CAAC;QAOM,wBAAM,GAAb,UAAc,MAAY,EAAE,MAAY;;YACtC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACT,IAAA,KAAA,OAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAA,EAApB,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAa,CAAC;gBAC1B,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;oBAIpC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;iBACjB;qBAAM,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;oBAI3C,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;iBAClB;qBAAM;oBAIL,KAAA,OAAY,iBAAM,MAAM,YAAC,MAAM,EAAE,MAAM,CAAC,IAAA,EAAvC,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,CAAiC;iBAC1C;gBACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtB;YACD,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB,CAAC;QAEH,cAAC;IAAD,CAAC,AAjMM,CAAc,IAAI,GAiMvB;AAEJ,CAAC;AAxMD,4DAwMC"}