{"version": 3, "file": "Region.js", "sourceRoot": "", "sources": ["../../../ts/a11y/explorer/Region.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA0BA,wDAAkD;AA0ClD;IA0CE,wBAAmB,QAAsB;QAAtB,aAAQ,GAAR,QAAQ,CAAc;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAoC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAMM,kCAAS,GAAhB;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YACzB,OAAO;SACR;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;YACxD,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;IAC/B,CAAC;IAMM,mCAAU,GAAjB;QACE,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC5C,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,OAAO;YACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;YACpC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1B,CAAC;IAMM,6BAAI,GAAX,UAAY,IAAiB,EAAE,WAA4B;QACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC;IACzD,CAAC;IAoBM,6BAAI,GAAX;QACE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC;IAC5D,CAAC;IAmBS,qCAAY,GAAtB,UAAuB,IAAiB;QAEtC,IAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC1C,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC;QACxC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CACjE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,SAAA,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;gBACvB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACzE,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACpE;SACF;QACD,IAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC;QAC9E,IAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC;QAC/F,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACpC,CAAC;IAxIgB,yBAAU,GAAY,KAAK,CAAC;IA0I/C,qBAAC;CAAA,AAtJD,IAsJC;AAtJqB,wCAAc;AAwJpC;IAAiC,+BAAoB;IAArD;;IAyCA,CAAC;IApCQ,2BAAK,GAAZ,cAAgB,CAAC;IAKV,4BAAM,GAAb,cAAiB,CAAC;IAKX,0BAAI,GAAX,cAAe,CAAC;IAKT,0BAAI,GAAX,cAAe,CAAC;IAKT,gCAAU,GAAjB,cAAqB,CAAC;IAKf,+BAAS,GAAhB,cAAoB,CAAC;IAKd,8BAAQ,GAAf,cAAmB,CAAC;IAKb,+BAAS,GAAhB,UAAiB,YAA6B,IAAG,CAAC;IACpD,kBAAC;AAAD,CAAC,AAzCD,CAAiC,cAAc,GAyC9C;AAzCY,kCAAW;AA4CxB;IAAkC,gCAAsB;IAAxD;;IAqCA,CAAC;IAhCQ,4BAAK,GAAZ;QACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;IACxC,CAAC;IAMM,6BAAM,GAAb,UAAc,MAAc;QAC1B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;IAClC,CAAC;IAKS,+BAAQ,GAAlB,UAAmB,IAAiB;QAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAMS,gCAAS,GAAnB,UAAoB,WAA4B;QAC9C,IAAM,KAAK,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;IAC5C,CAAC;IAEH,mBAAC;AAAD,CAAC,AArCD,CAAkC,cAAc,GAqC/C;AArCY,oCAAY;AAwCzB;IAA6B,2BAAY;IAAzC;;IAwBA,CAAC;IAnBkB,iBAAS,GAAG,aAAa,CAAC;IAK1B,aAAK,GACpB,IAAI,wBAAS;QACX,GAAC,GAAG,GAAG,OAAO,CAAC,SAAS,IAAG;YACzB,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc;YAC7C,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;SAC5B;QACD,GAAC,GAAG,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO,IAAG;YACnC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ;YACjE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;YAC1C,eAAe,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU;YACzD,SAAS,EAAE,GAAG;SACf;YACD,CAAC;IAEP,cAAC;CAAA,AAxBD,CAA6B,YAAY,GAwBxC;AAxBY,0BAAO;AA2BpB;IAAgC,8BAAY;IA8B1C,oBAAmB,QAAsB;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAEhB;QAHkB,cAAQ,GAAR,QAAQ,CAAc;QAEvC,KAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;;IAClD,CAAC;IA5BgB,oBAAS,GAAG,gBAAgB,CAAC;IAK7B,gBAAK,GACpB,IAAI,wBAAS;QACX,GAAC,GAAG,GAAG,UAAU,CAAC,SAAS,IAAG;YAC5B,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;YAC3D,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;SACnC;QACD,GAAC,GAAG,GAAG,UAAU,CAAC,SAAS,GAAG,OAAO,IAAG;YACtC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAC7D,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK;YAChD,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ;YACrC,kBAAkB,EAAE,sBAAsB,EAAE,YAAY,EAAE,oBAAoB;YAC9E,MAAM,EAAE,mBAAmB;SAC5B;YACD,CAAC;IAYP,iBAAC;CAAA,AAnCD,CAAgC,YAAY,GAmC3C;AAnCY,gCAAU;AAuCvB;IAAiC,+BAA2B;IA6B1D,qBAAmB,QAAsB;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAEhB;QAHkB,cAAQ,GAAR,QAAQ,CAAc;QAEvC,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;;IACpC,CAAC;IAQS,8BAAQ,GAAlB,UAAmB,IAAiB;QAClC,IAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC9C,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;QACjD,IAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACzC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7B,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;QACjC,IAAI,GAAG,CAAC;QACR,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;YAC1C,KAAK,KAAK;gBACR,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,CAAE;gBAC1C,MAAM;YACR,KAAK,QAAQ;gBACX,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC;gBAC3B,MAAM;YACR,KAAK,QAAQ,CAAC;YACd;gBACE,IAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrD,GAAG,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACtC;QACD,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;QAC/B,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACpC,CAAC;IAKS,+BAAS,GAAnB,UAAoB,WAA4B;QAE9C,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU;YACrB,CAAE,IAAI,CAAC,KAAK,CAAC,UAA0B,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE;YACzE,OAAO;SACR;QACD,IAAM,KAAK,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;IAC5C,CAAC;IAKM,0BAAI,GAAX,UAAY,IAAiB,EAAE,WAA4B;QACzD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClB,iBAAM,IAAI,YAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAChC,CAAC;IAKM,2BAAK,GAAZ;QACE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;IACxC,CAAC;IAKM,4BAAM,GAAb,UAAc,IAAiB;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAOO,+BAAS,GAAjB,UAAkB,IAAiB;QACjC,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAC;QAC9C,IAAI,GAAG,CAAC,QAAQ,KAAK,eAAe,EAAE;YAEpC,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,EAAE;gBACxB,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC;aACpD;YACD,IAAI,SAAS,GAAG,IAAI,CAAC;YACrB,OAAO,SAAS,IAAI,SAAS,CAAC,QAAQ,KAAK,eAAe,EAAE;gBAC1D,SAAS,GAAG,SAAS,CAAC,UAAyB,CAAC;aACjD;YACD,IAAI,GAAG,CAAC,QAAQ,KAAK,UAAU,IAAI,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE;gBACzD,IAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC;gBACnC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAyB,CAAC;gBAIxE,IAAI,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE;oBACzB,GAAG,CAAC,UAA0B,CAAC,YAAY,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;oBAClF,IAAM,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,IAAM,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC1C,IAAA,KAAyB,IAAY,CAAC,OAAO,EAAE,EAA9C,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAA2B,CAAC;oBACtD,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACzE,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAC7B,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;oBAClD,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;oBACpD,SAAS,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;iBAClD;aACF;YACD,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAyB,CAAC;YAE5E,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;SACxB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IA7IgB,qBAAS,GAAG,iBAAiB,CAAC;IAK9B,iBAAK,GACpB,IAAI,wBAAS;QACX,GAAC,GAAG,GAAG,WAAW,CAAC,SAAS,IAAG;YAC7B,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;YACjD,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;SACnC;QACD,GAAC,GAAG,GAAG,WAAW,CAAC,SAAS,GAAG,OAAO,IAAG;YACvC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM;YAC1D,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;YACpE,kBAAkB,EAAE,sBAAsB;YAC1C,YAAY,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB;SAChE;YACD,CAAC;IA8HP,kBAAC;CAAA,AApJD,CAAiC,cAAc,GAoJ9C;AApJY,kCAAW"}