{"version": 3, "file": "ParseOptions.js", "sourceRoot": "", "sources": ["../../../ts/input/tex/ParseOptions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,8EAAqD;AAGrD,mDAA6C;AAC7C,8DAAqC;AAGrC,oDAAiE;AAOjE;IA0EE,sBAAmB,aAAkC,EAAE,OAA0B;QAA1B,wBAAA,EAAA,YAA0B;QA9D1E,YAAO,GAAe,EAAE,CAAC;QAwBzB,gBAAW,GAAqB,IAAI,GAAG,EAAE,CAAC;QAS1C,YAAO,GAAgB,EAAE,CAAC;QAO1B,SAAI,GAAY,IAAI,CAAC;QAMrB,cAAS,GAA+B,EAAE,CAAC;QAM3C,UAAK,GAAY,KAAK,CAAC;QAW5B,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;QAEvC,IAAI,CAAC,WAAW,GAAG,IAAI,4BAAW,EAAE,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAElD,IAAI,CAAC,WAAW,GAAG,IAAI,6BAAgB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC;QAEtC,2BAAc,8BAAC,IAAI,CAAC,OAAO,UAAK,OAAO,WAAE;QACzC,IAAA,2BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;IACtD,CAAC;IAQM,iCAAU,GAAjB,UAAkB,MAAiB;QACjC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAMM,gCAAS,GAAhB;QACE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAMD,sBAAW,gCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;;;OAAA;IAKM,4BAAK,GAAZ;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC;IASM,8BAAO,GAAd,UAAe,QAAgB,EAAE,IAAa;QAC5C,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SACtC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAK1B,IAAM,OAAO,GAAG,CAAC,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAW,IAAI,EAAE,CAAC,CAAC;YACzE,IAAM,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7E,qBAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;SAC/C;IACH,CAAC;IAaM,8BAAO,GAAd,UAAe,QAAgB;;QAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC1C,IAAI,MAAM,GAAG,EAAE,CAAC;;YAChB,KAAiB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;gBAAlB,IAAI,IAAI,iBAAA;gBACX,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACnB;aACF;;;;;;;;;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAClC,OAAO,MAAM,CAAC;IAChB,CAAC;IAUM,qCAAc,GAArB,UAAsB,QAAgB,EAAE,KAAgB;;QACtD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;YAC5C,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,IAAI,kBAAA;gBACb,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,EAAE;oBACV,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACnB;aACF;;;;;;;;;IACH,CAAC;IAOO,6BAAM,GAAd,UAAe,IAAa;QAC1B,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YACjC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;SACpB;QACD,OAAO,CAAC,CAAC,IAAI,CAAC;IAChB,CAAC;IAEH,mBAAC;AAAD,CAAC,AA1MD,IA0MC"}