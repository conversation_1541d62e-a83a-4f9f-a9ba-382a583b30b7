{"version": 3, "file": "NewcommandConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/newcommand/NewcommandConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,wDAAuE;AACvE,2DAAkD;AAClD,0EAAiD;AACjD,mCAAiC;AACjC,uEAA8C;AAC9C,kDAAsC;AAOtC,IAAI,IAAI,GAAG,UAAS,MAA2B;IAC7C,IAAI,EAAE,CAAC,YAAY,CAAC,2BAAc,CAAC,aAAa,EAC5B,yBAAY,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAChD,IAAI,EAAE,CAAC,UAAU,CAAC,2BAAc,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACtD,IAAI,EAAE,CAAC,cAAc,CAAC,2BAAc,CAAC,eAAe,EAC9B,yBAAY,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACxD,MAAM,CAAC,MAAM,CAAC,gCAAa,CAAC,KAAK,CAC/B,EAAC,OAAO,EAAE,EAAC,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,2BAAc,CAAC,aAAa,CAAC;YACzC,KAAK,EAAE,CAAC,2BAAc,CAAC,aAAa;gBAC5B,2BAAc,CAAC,WAAW,CAAC;YACnC,WAAW,EAAE,CAAC,2BAAc,CAAC,eAAe,CAAC;SAC7C;QACV,QAAQ,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC;AAGW,QAAA,uBAAuB,GAAG,gCAAa,CAAC,MAAM,CACzD,YAAY,EAAE;IACZ,OAAO,EAAE;QACP,KAAK,EAAE,CAAC,mBAAmB,CAAC;KAC7B;IACD,KAAK;QACH,GAAC,iCAAY,CAAC,SAAS,CAAC,IAAI,IAAG,iCAAY;WAC5C;IACD,OAAO,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC;IAC1B,IAAI,EAAE,IAAI;CACX,CACF,CAAC"}