{"version": 3, "file": "Parser.js", "sourceRoot": "", "sources": ["../../../ts/adaptors/lite/Parser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,+DAAmD;AAEnD,2CAAyC;AACzC,qCAAgD;AAMhD,IAAiB,QAAQ,CAcxB;AAdD,WAAiB,QAAQ;IACV,gBAAO,GAAG,kBAAkB,CAAC;IAC7B,gBAAO,GAAG,mBAAmB,CAAC;IAC9B,cAAK,GAAI,mCAAgC,CAAC;IAC1C,mBAAU,GAAI,yCAAsC,CAAC;IACrD,cAAK,GAAG,cAAc,CAAC;IACvB,sBAAa,GAAG,cAAc,CAAC;IAC/B,kBAAS,GAAG,SAAA,OAAO,GAAG,KAAK,GAAG,SAAA,aAAa,GAAG,GAAG,GAAG,SAAA,aAAa,GAAG,SAAA,KAAK,GAAG,IAAI,CAAC;IACjF,uBAAc,GAAG,GAAG,GAAG,SAAA,OAAO,GAAG,MAAM,GAAG,SAAA,aAAa,GAAG,GAAG,GAAG,SAAA,aAAa,GAAG,SAAA,UAAU,GAAG,IAAI,CAAC;IAClG,YAAG,GAAG,OAAO,GAAG,SAAA,OAAO,GAAG,KAAK,GAAG,SAAA,KAAK,GAAG,SAAA,SAAS,GAAG,IAAI;UAChD,SAAA,aAAa,GAAG,MAAM,GAAG,SAAA,OAAO,GAAG,6BAA6B,CAAC;IAC3E,YAAG,GAAG,IAAI,MAAM,CAAC,SAAA,GAAG,EAAE,GAAG,CAAC,CAAC;IAC3B,aAAI,GAAG,IAAI,MAAM,CAAC,SAAA,SAAS,EAAE,GAAG,CAAC,CAAC;IAClC,kBAAS,GAAG,IAAI,MAAM,CAAC,SAAA,cAAc,EAAE,GAAG,CAAC,CAAC;AAC3D,CAAC,EAdgB,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAcxB;AAOD;IAAA;IA4VA,CAAC;IAhSQ,oCAAe,GAAtB,UAAuB,IAAY,EAAE,OAA6B,EAAE,OAA2B;QAA1D,wBAAA,EAAA,qBAA6B;QAAE,wBAAA,EAAA,cAA2B;QAC7F,IAAM,IAAI,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACtC,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAK9B,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC,MAAM,EAAE;YACnB,IAAM,MAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,MAAI,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,MAAI,CAAC,CAAC;aACnC;YACD,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7C,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACzB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;iBACrC;qBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAChC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;iBAC1C;qBAAM;oBACL,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;iBAChD;aACF;SACF;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAQS,4BAAO,GAAjB,UAAkB,OAAoB,EAAE,IAAiB,EAAE,IAAY;QACrE,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAa,CAAC;IAC9D,CAAC;IAQS,+BAAU,GAApB,UAAqB,OAAoB,EAAE,IAAiB,EAAE,OAAe;QAC3E,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,qBAAW,CAAC,OAAO,CAAC,CAAgB,CAAC;IACvE,CAAC;IAQS,6BAAQ,GAAlB,UAAmB,OAAoB,EAAE,IAAiB,EAAE,GAAW;QACrE,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACxD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YAC1D,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IASS,4BAAO,GAAjB,UAAkB,OAAoB,EAAE,IAAiB,EAAE,GAAW,EAAE,KAAe;QACrF,IAAM,MAAM,GAAI,IAAI,CAAC,WAAiC,CAAC,MAAM,CAAC;QAC9D,IAAM,YAAY,GAAI,IAAI,CAAC,WAAiC,CAAC,YAAY,CAAC;QAI1E,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3D,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAgB,CAAC;QAMhD,IAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAI7E,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAC/C,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAS5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;oBAChB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;iBAChD;qBAAM;oBACL,IAAI,GAAG,KAAK,CAAC;iBACd;aACF;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAQS,kCAAa,GAAvB,UAAwB,OAAoB,EAAE,IAAiB,EAAE,UAAoB;QACnF,IAAM,UAAU,GAAI,IAAI,CAAC,WAAiC,CAAC,UAAU,CAAC;QACtE,OAAO,UAAU,CAAC,MAAM,EAAE;YACpB,IAAA,KAAA,OAAwB,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAA,EAA3C,MAAI,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAA2B,CAAC;YACpD,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,MAAI,CAAC,EAAE;gBACrB,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACnC;YACD,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,MAAI,EAAE,KAAK,CAAC,CAAC;SACzC;IACH,CAAC;IAQS,iCAAY,GAAtB,UAAuB,OAAoB,EAAE,IAAiB,EAAE,IAAY,EAAE,KAAe;QAC3F,IAAM,MAAM,GAAG,EAAc,CAAC;QAC9B,IAAM,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;QAC/B,IAAI,IAAI,GAAG,EAAE,CAAC;QAMd,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3B,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;SACtB;QAID,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAUS,kCAAa,GAAvB,UAAwB,OAAoB,EAAE,IAAkB;;QAC9D,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI;YAAE,OAAO;;YAClB,KAAoB,IAAA,KAAA,SAAA,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA,gBAAA,4BAAE;gBAAvD,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,MAAM;iBACP;gBACD,IAAI,KAAK,YAAY,qBAAW,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;oBACnE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;iBACzB;aACF;;;;;;;;;QACD,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B,KAAK,MAAM;;oBAIT,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,QAAQ,CAAA,gBAAA,4BAAE;wBAA9B,IAAM,KAAK,WAAA;wBACd,QAAQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;4BAC7B,KAAK,MAAM;gCACT,IAAI,CAAC,IAAI,GAAG,KAAoB,CAAC;gCACjC,MAAM;4BACR,KAAK,MAAM;gCACT,IAAI,CAAC,IAAI,GAAG,KAAoB,CAAC;gCACjC,MAAM;yBACP;qBACF;;;;;;;;;gBAID,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;iBACjC;gBACD,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;oBACtC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;iBACtC;gBACD,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAgB,CAAC;gBAC5D,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAgB,CAAC;gBAC5D,MAAM;SACP;IACH,CAAC;IAUS,iCAAY,GAAtB,UAAuB,OAAoB,EAAE,IAAiB;;QAC5D,IAAI,IAAI,GAAgB,IAAI,CAAC;;YAC7B,KAAoB,IAAA,KAAA,SAAA,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,YAAY,wBAAW,EAAE;oBAChC,IAAI,IAAI;wBAAE,OAAO,IAAI,CAAC;oBACtB,IAAI,GAAG,KAAK,CAAC;iBACd;aACF;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAQM,8BAAS,GAAhB,UAAiB,OAAoB,EAAE,IAAiB,EAAE,GAAoB;QAA9E,iBAYC;QAZyD,oBAAA,EAAA,WAAoB;QAC5E,IAAM,YAAY,GAAI,IAAI,CAAC,WAAiC,CAAC,YAAY,CAAC;QAC1E,IAAM,KAAK,GAAI,IAAI,CAAC,WAAiC,CAAC,UAAU,CAAC;QACjE,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,CAChD,UAAC,CAAgB,IAAK,OAAA,CAAC,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,EAAhF,CAAgF,CACvG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QACxD,IAAM,IAAI,GACR,GAAG,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;cAC1C,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAI,OAAO,eAAK,GAAG,MAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC;IACd,CAAC;IAOM,mCAAc,GAArB,UAAsB,OAAoB,EAAE,IAAiB,EAAE,GAAoB;QAAnF,iBAWC;QAX8D,oBAAA,EAAA,WAAoB;QACjF,IAAM,MAAM,GAAI,IAAI,CAAC,WAAiC,CAAC,MAAM,CAAC;QAC9D,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACrE;QACD,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;YACnC,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI,KAAK,UAAU,CAAC,CAAC,CAAE,CAAiB,CAAC,KAAK,CAAC,CAAC;oBAChD,KAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAgB,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;IAMM,qCAAgB,GAAvB,UAAwB,IAAY;QAClC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAMM,gCAAW,GAAlB,UAAmB,IAAY;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aAC/B,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3B,CAAC;IArVa,uBAAY,GAA8B;QACtD,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,IAAI;QACV,EAAE,EAAE,IAAI;QACR,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,IAAI;QACX,EAAE,EAAE,IAAI;QACR,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,IAAI;QACX,GAAG,EAAE,IAAI;KACV,CAAC;IAKY,iBAAM,GAA8B;QAChD,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;KACb,CAAC;IAKY,qBAAU,GAA8B;QACpD,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,IAAI;QACT,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;KACb,CAAC;IAqSJ,iBAAC;CAAA,AA5VD,IA4VC;AA5VY,gCAAU"}