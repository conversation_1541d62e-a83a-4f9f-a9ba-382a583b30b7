{"version": 3, "file": "LegacyMmlVisitor.js", "sourceRoot": "", "sources": ["../../../ts/core/MmlTree/LegacyMmlVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,iDAA2C;AAO3C,IAAI,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;AAOjC;IAAsC,oCAAU;IAAhD;;IA0FA,CAAC;IAjFQ,oCAAS,GAAhB,UAAiB,IAAa;QAC5B,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAMM,wCAAa,GAApB,UAAqB,IAAc,EAAE,MAAW;QAC9C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAMM,uCAAY,GAAnB,UAAoB,IAAa,EAAE,MAAW;QAC5C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IASM,gDAAqB,GAA5B,UAA6B,IAAa,EAAE,MAAW;;;YACrD,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aAC/B;;;;;;;;;IACH,CAAC;IAaM,uCAAY,GAAnB,UAAoB,IAAa,EAAE,MAAW;;QAC5C,IAAI,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;YAC9B,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC5B;;;;;;;;;QACD,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAMM,wCAAa,GAApB,UAAqB,IAAa,EAAE,GAAQ;;QAC1C,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,KAAK,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;;YAC1C,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,GAAG,CAAC,MAAI,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC;aAC1C;;;;;;;;;IACH,CAAC;IAMM,wCAAa,GAApB,UAAqB,IAAa,EAAE,GAAQ;;QAC1C,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;;YACpC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,GAAG,CAAC,MAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC;aACpC;;;;;;;;;IACH,CAAC;IAEH,uBAAC;AAAD,CAAC,AA1FD,CAAsC,0BAAU,GA0F/C;AA1FY,4CAAgB"}