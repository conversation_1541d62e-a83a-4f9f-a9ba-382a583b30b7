"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Macro = exports.Symbol = void 0;
var Symbol = (function () {
    function Symbol(_symbol, _char, _attributes) {
        this._symbol = _symbol;
        this._char = _char;
        this._attributes = _attributes;
    }
    Object.defineProperty(Symbol.prototype, "symbol", {
        get: function () {
            return this._symbol;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Symbol.prototype, "char", {
        get: function () {
            return this._char;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Symbol.prototype, "attributes", {
        get: function () {
            return this._attributes;
        },
        enumerable: false,
        configurable: true
    });
    return Symbol;
}());
exports.Symbol = Symbol;
var Macro = (function () {
    function Macro(_symbol, _func, _args) {
        if (_args === void 0) { _args = []; }
        this._symbol = _symbol;
        this._func = _func;
        this._args = _args;
    }
    Object.defineProperty(Macro.prototype, "symbol", {
        get: function () {
            return this._symbol;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Macro.prototype, "func", {
        get: function () {
            return this._func;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Macro.prototype, "args", {
        get: function () {
            return this._args;
        },
        enumerable: false,
        configurable: true
    });
    return Macro;
}());
exports.Macro = Macro;
//# sourceMappingURL=Symbol.js.map