{"version": 3, "file": "mspace.js", "sourceRoot": "", "sources": ["../../../../ts/output/chtml/Wrappers/mspace.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,4CAA6D;AAC7D,6DAAkE;AAClE,sEAAmE;AAWnE;IACA,+BAAgE;IADhE;;IA8BA,CAAC;IAnBQ,6BAAO,GAAd,UAAe,MAAS;QACtB,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACvC,IAAA,KAAY,IAAI,CAAC,OAAO,EAAE,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,GAAG,CAAC,CAAC;SACP;QACD,IAAI,CAAC,EAAE;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;QACD,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,EAAE;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,EAAE;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5D;IACH,CAAC;IAtBa,gBAAI,GAAG,qBAAS,CAAC,SAAS,CAAC,IAAI,CAAC;IAwBhD,kBAAC;CAAA,AA9BD,CACA,IAAA,6BAAiB,EAAkC,yBAAY,CAAC,GA6B/D;AA9BY,kCAAW"}