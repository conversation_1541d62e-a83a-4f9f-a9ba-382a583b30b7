{"name": "mj-context-menu", "version": "0.6.1", "description": "A generic context menu", "main": "dist/ContextMenu.js", "scripts": {"build": "./node_modules/.bin/tsc; ./node_modules/.bin/webpack", "prepare": "npx rimraf js; npx rimraf dist; ./node_modules/.bin/tsc; ./node_modules/.bin/webpack"}, "maintainers": ["Volker Sorge <<EMAIL>>"], "bugs": {"email": "<EMAIL>", "url": "https://github.com/zorkow/context-menu"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/zorkow/context-menu"}, "keywords": ["context-menu"], "homepage": "https://github.com/zorkow/context-menu", "dependencies": {}, "devDependencies": {"@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "babel-loader": "^8.1.0", "terser-webpack-plugin": "^4.1.0", "tslint": "^6.1.3", "tslint-jsdoc-rules": "^0.2.0", "tslint-unix-formatter": "^0.2.0", "typescript": "^3.*", "webpack": "^4.44.1", "webpack-cli": "^3.3.12"}, "files": ["LICENSE", "README.md", "js", "dist"]}