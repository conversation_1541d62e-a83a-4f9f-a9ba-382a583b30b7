{"version": 3, "file": "SerializedMmlVisitor.js", "sourceRoot": "", "sources": ["../../../ts/core/MmlTree/SerializedMmlVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,iDAA2C;AAC3C,2CAAiF;AACjF,0CAAuC;AAG1B,QAAA,OAAO,GAAG,WAAW,CAAC;AAE5B,IAAM,QAAQ,GAAG,UAAC,CAAS,IAAK,OAAA,KAAK,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,EAAzD,CAAyD,CAAC;AAApF,QAAA,QAAQ,YAA4E;AAUjG;IAA0C,wCAAU;IAApD;;IA8MA,CAAC;IAlLQ,wCAAS,GAAhB,UAAiB,IAAa;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAOM,4CAAa,GAApB,UAAqB,IAAc,EAAE,MAAc;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACxC,CAAC;IAOM,2CAAY,GAAnB,UAAoB,IAAa,EAAE,KAAa;QAC9C,OAAO,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACzC,CAAC;IAUM,oDAAqB,GAA5B,UAA6B,IAAa,EAAE,KAAa;;QACvD,IAAI,GAAG,GAAG,EAAE,CAAC;;YACb,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;aACxC;;;;;;;;;QACD,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAUM,+CAAgB,GAAvB,UAAwB,IAAa,EAAE,KAAa;QAClD,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3D,IAAI,GAAG,GAAG,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG;YACxD,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;QACpE,OAAO,GAAG,CAAC;IACb,CAAC;IAOM,kDAAmB,GAA1B,UAA2B,IAAa,EAAE,KAAa;QACrD,OAAO,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG;cACzD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC;cAC/B,eAAe,CAAC;IACtB,CAAC;IAaM,2CAAY,GAAnB,UAAoB,IAAa,EAAE,KAAa;QAC9C,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACjB,IAAA,KAAA,OAAiB,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAA,EAAzF,EAAE,QAAA,EAAE,QAAQ,QAA6E,CAAC;QAC/F,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3D,OAAO,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG;cAC3C,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;cACtD,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;IACnC,CAAC;IAQS,2CAAY,GAAtB,UAAuB,IAAa,EAAE,KAAa,EAAE,EAAU;;QAC7D,IAAI,GAAG,GAAG,EAAE,CAAC;;YACb,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAhC,IAAM,KAAK,WAAA;gBACd,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;aAC1C;;;;;;;;;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAMS,4CAAa,GAAvB,UAAwB,IAAa;;QACnC,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAM,QAAQ,GAAI,IAAI,CAAC,WAA2C,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACtG,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EACF,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAClC,CAAC;QAClC,IAAM,QAAQ,GAAI,IAAI,CAAC,WAA2C,CAAC,QAAQ,CAAC;QAC5E,IAAI,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YAC/F,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;SAC3D;;YACD,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,MAAI,WAAA;gBACb,IAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,MAAI,CAAC,CAAC,CAAC;gBACvC,IAAI,KAAK,KAAK,SAAS;oBAAE,SAAS;gBAClC,IAAI,CAAC,IAAI,CAAC,MAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;aACtD;;;;;;;;;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjD,CAAC;IAQS,gDAAiB,GAA3B,UAA4B,IAAa;QACvC,IAAM,IAAI,GAAG,EAAkB,CAAC;QAChC,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAW,CAAC;QACrE,IAAM,QAAQ,GAAI,IAAI,CAAC,WAA2C,CAAC,QAAQ,CAAC;QAC5E,OAAO,IAAI,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/F,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QACjF,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACxF,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAW,CAAC;QAC9D,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;QACxE,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAW,CAAC;QACxD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,QAAQ,KAAK,qBAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACjD,IAAM,MAAI,GAAI,IAAc,CAAC,OAAO,EAAE,CAAC;gBACvC,QAAQ,GAAG,CAAC,CAAC,MAAI,CAAC,MAAM,GAAG,CAAC,IAAI,MAAI,CAAC,KAAK,CAAC,aAAK,CAAC,YAAY,CAAC,CAAC,CAAC;aACjE;YACD,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,0BAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;SACtG;QACD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,KAAK;YACxE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAOS,+CAAgB,GAA1B,UAA2B,IAAkB,EAAE,IAAY,EAAE,KAAa;QACxE,IAAI,CAAC,eAAO,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;IAC/B,CAAC;IAUS,wCAAS,GAAnB,UAAoB,KAAa;QAC/B,OAAO,KAAK;aACT,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aAC3C,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;aACxB,OAAO,CAAC,mBAAmB,EAAE,gBAAQ,CAAC;aACtC,OAAO,CAAC,+BAA+B,EAAE,gBAAQ,CAAC,CAAC;IACxD,CAAC;IAvMa,6BAAQ,GAAiB;QACrC,mBAAmB,EAAO,QAAQ;QAClC,wBAAwB,EAAE,aAAa;QACvC,eAAe,EAAW,QAAQ;QAClC,oBAAoB,EAAM,MAAM;QAChC,aAAa,EAAa,QAAQ;KACnC,CAAC;IAKY,sCAAiB,GAAmC;QAChE,IAAI,EAAE;YACJ,KAAK,EAAE,oCAAoC;SAC5C;KACF,CAAC;IA0LJ,2BAAC;CAAA,AA9MD,CAA0C,0BAAU,GA8MnD;AA9MY,oDAAoB"}