{"version": 3, "file": "system.js", "sourceRoot": "", "sources": ["../../../ts/util/asyncLoad/system.ts"], "names": [], "mappings": ";;;AAuBA,+CAAyC;AAKzC,IAAI,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;AAE7D,IAAI,CAAC,oBAAO,CAAC,SAAS,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;IACxE,oBAAO,CAAC,SAAS,GAAG,UAAC,IAAY;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC;CACH;AAKD,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,GAAG,GAAG,CAAC;IACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACtB,IAAI,IAAI,GAAG,CAAC;KACb;AACH,CAAC;AALD,gCAKC"}