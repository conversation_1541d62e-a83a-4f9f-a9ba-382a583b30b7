import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/input/tex/bbox/BboxConfiguration.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('[tex]/bbox', VERSION, 'tex-extension');
}

combineWithMathJax({_: {
  input: {
    tex: {
      bbox: {
        BboxConfiguration: module1
      }
    }
  }
}});
