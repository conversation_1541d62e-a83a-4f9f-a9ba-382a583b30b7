{"version": 3, "file": "lengths.js", "sourceRoot": "", "sources": ["../../ts/util/lengths.ts"], "names": [], "mappings": ";;;AA0Ba,QAAA,QAAQ,GAAG,OAAO,CAAC;AAKnB,QAAA,KAAK,GAA6B;IAC7C,EAAE,EAAE,CAAC;IACL,IAAI,EAAE,EAAE;IACR,EAAE,EAAE,EAAE,GAAG,IAAI;IACb,EAAE,EAAE,EAAE,GAAG,IAAI;CACd,CAAC;AAKW,QAAA,QAAQ,GAA6B;IAChD,EAAE,EAAE,CAAC;IACL,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,CAAC,GAAG,EAAE;IACV,EAAE,EAAE,EAAE,GAAG,EAAE;IACX,EAAE,EAAE,CAAC,GAAG,EAAE;CACX,CAAC;AAKW,QAAA,SAAS,GAA6B;IAEjD,qBAAqB,EAAY,CAAC,GAAC,EAAE;IACrC,iBAAiB,EAAgB,CAAC,GAAC,EAAE;IACrC,aAAa,EAAoB,CAAC,GAAC,EAAE;IACrC,eAAe,EAAkB,CAAC,GAAC,EAAE;IACrC,cAAc,EAAmB,CAAC,GAAC,EAAE;IACrC,kBAAkB,EAAe,CAAC,GAAC,EAAE;IACrC,sBAAsB,EAAW,CAAC,GAAC,EAAE;IACrC,6BAA6B,EAAG,CAAC,CAAC,GAAC,EAAE;IACrC,yBAAyB,EAAO,CAAC,CAAC,GAAC,EAAE;IACrC,qBAAqB,EAAW,CAAC,CAAC,GAAC,EAAE;IACrC,uBAAuB,EAAS,CAAC,CAAC,GAAC,EAAE;IACrC,sBAAsB,EAAU,CAAC,CAAC,GAAC,EAAE;IACrC,0BAA0B,EAAM,CAAC,CAAC,GAAC,EAAE;IACrC,8BAA8B,EAAE,CAAC,CAAC,GAAC,EAAE;IAGrC,IAAI,EAAI,GAAG;IACX,MAAM,EAAE,GAAG;IACX,KAAK,EAAG,EAAE;IAEV,MAAM,EAAG,CAAC;IACV,GAAG,EAAM,CAAC;IACV,KAAK,EAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEzB,QAAQ,EAAG,gBAAQ;CACpB,CAAC;AAUF,SAAgB,SAAS,CAAC,MAAuB,EAAE,IAAgB,EAAE,KAAiB,EAAE,EAAe;IAApD,qBAAA,EAAA,QAAgB;IAAE,sBAAA,EAAA,SAAiB;IAAE,mBAAA,EAAA,OAAe;IACrG,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;KACzB;IACD,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE;QACnC,OAAO,IAAI,CAAC;KACb;IACD,IAAI,iBAAS,CAAC,MAAM,CAAC,EAAE;QACrB,OAAO,iBAAS,CAAC,MAAM,CAAC,CAAC;KAC1B;IACD,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;IAC/F,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IACD,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,aAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,CAAC,GAAG,aAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;KACrC;IACD,IAAI,gBAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QACjC,OAAO,CAAC,GAAG,gBAAQ,CAAC,IAAI,CAAC,CAAC;KAC3B;IACD,IAAI,IAAI,KAAK,GAAG,EAAE;QAChB,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;KACvB;IACD,OAAO,CAAC,GAAG,IAAI,CAAC;AAClB,CAAC;AAzBD,8BAyBC;AAMD,SAAgB,OAAO,CAAC,CAAS;IAC/B,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AAC1D,CAAC;AAFD,0BAEC;AAMD,SAAgB,EAAE,CAAC,CAAS;IAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;QAAE,OAAO,GAAG,CAAC;IACnC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;AACrD,CAAC;AAHD,gBAGC;AAOD,SAAgB,SAAS,CAAC,CAAS,EAAE,EAAe;IAAf,mBAAA,EAAA,OAAe;IAClD,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;IACpC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;QAAE,OAAO,KAAK,CAAC;IACrC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;AACnD,CAAC;AAJD,8BAIC;AASD,SAAgB,EAAE,CAAC,CAAS,EAAE,CAAqB,EAAE,EAAe;IAAtC,kBAAA,EAAA,KAAa,gBAAQ;IAAE,mBAAA,EAAA,OAAe;IAClE,CAAC,IAAI,EAAE,CAAC;IACR,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAAE,CAAC,GAAG,CAAC,CAAC;IACtB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;QAAE,OAAO,GAAG,CAAC;IACjC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;AACjD,CAAC;AALD,gBAKC"}