{"version": 3, "file": "assistive-mml.js", "sourceRoot": "", "sources": ["../../ts/a11y/assistive-mml.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,mDAAgF;AAEhF,mFAA6E;AAC7E,iDAA0D;AAK1D;IAAuC,qCAAoB;IAA3D;;IAYA,CAAC;IAPW,yCAAa,GAAvB,UAAwB,IAAa;QAInC,OAAO,iBAAM,aAAa,YAAC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAEH,wBAAC;AAAD,CAAC,AAZD,CAAuC,8CAAoB,GAY1D;AAZY,8CAAiB;AAwB9B,IAAA,sBAAQ,EAAC,cAAc,EAAE,GAAG,CAAC,CAAC;AA4B9B,SAAgB,yBAAyB,CACvC,YAAe;IAGf;QAAqB,2BAAY;QAA1B;;QAmCP,CAAC;QA7BQ,8BAAY,GAAnB,UAAoB,QAA2C,EAAE,KAAsB;YAAtB,sBAAA,EAAA,aAAsB;YACrF,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,mBAAK,CAAC,YAAY;gBAAE,OAAO;YAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,IAAI,KAAK,CAAC,EAAE;gBACrE,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAIjC,IAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;gBAItF,IAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;gBAInF,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBAC7C,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;iBACjE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAKf,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAM,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;gBACvF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBAC3D,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CAAC,mBAAK,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;QAEH,cAAC;IAAD,CAAC,AAnCM,CAAc,YAAY,GAmC/B;AAEJ,CAAC;AAzCD,8DAyCC;AAuCD,SAAgB,6BAA6B,CAE3C,YAAe;;IAGf;YAA+B,6BAAY;YAqDzC;gBAAY,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAA1B,wDACW,IAAI,mBAcd;gBAbC,IAAM,KAAK,GAAI,KAAI,CAAC,WAAgC,CAAC;gBACrD,IAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;oBACrC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;iBACvC;gBACD,KAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC;gBACtD,KAAI,CAAC,OAAO,CAAC,QAAQ;oBACnB,yBAAyB,CACvB,KAAI,CAAC,OAAO,CAAC,QAAQ,CACtB,CAAC;gBACJ,IAAI,WAAW,IAAI,KAAI,EAAE;oBACtB,KAAY,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBAChD;;YACH,CAAC;YAMM,yBAAK,GAAZ,UAAa,IAAa;gBACxB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YAKM,gCAAY,GAAnB;;gBACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;;wBAC1C,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,CAAA,gBAAA,4BAAE;4BAAzB,IAAM,IAAI,WAAA;4BACZ,IAAsC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;yBAC5D;;;;;;;;;oBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;iBACrC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAKM,yBAAK,GAAZ,UAAa,KAAa,EAAE,OAAwB;gBAAxB,wBAAA,EAAA,eAAwB;gBAClD,iBAAM,KAAK,YAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5B,IAAI,KAAK,GAAG,mBAAK,CAAC,YAAY,EAAE;oBAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBACvC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAEH,gBAAC;QAAD,CAAC,AAtGM,CAAwB,YAAY;QAK3B,UAAO,yBAChB,YAAY,CAAC,OAAO,KACvB,kBAAkB,EAAE,IAAI,EACxB,aAAa,EAAE,IAAA,uBAAU,wBACpB,YAAY,CAAC,OAAO,CAAC,aAAa,KACrC,YAAY,EAAE,CAAC,mBAAK,CAAC,YAAY,CAAC,IAClC,GACF;QAKY,kBAAe,GAAc;YACzC,mBAAmB,EAAE;gBACnB,QAAQ,EAAE,qBAAqB;gBAC/B,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK;gBACvB,IAAI,EAAE,0BAA0B;gBAChC,OAAO,EAAE,4BAA4B;gBACrC,MAAM,EAAE,gBAAgB;gBACxB,OAAO,EAAE,kBAAkB;gBAC3B,KAAK,EAAE,iBAAiB;gBACxB,QAAQ,EAAE,mBAAmB;gBAI7B,uBAAuB,EAAE,MAAM;gBAC/B,qBAAqB,EAAE,MAAM;gBAC7B,oBAAoB,EAAE,MAAM;gBAC5B,kBAAkB,EAAE,MAAM;gBAC1B,iBAAiB,EAAE,MAAM;gBACzB,aAAa,EAAE,MAAM;aACtB;YACD,oCAAoC,EAAE;gBACpC,KAAK,EAAE,iBAAiB;aACzB;SACD;WA8DF;AAEJ,CAAC;AA7GD,sEA6GC;AAcD,SAAgB,mBAAmB,CAAU,OAAyB;IACpE,OAAO,CAAC,aAAa;QACnB,6BAA6B,CAC3B,OAAO,CAAC,aAAa,CACtB,CAAC;IACJ,OAAO,OAAO,CAAC;AACjB,CAAC;AAND,kDAMC"}