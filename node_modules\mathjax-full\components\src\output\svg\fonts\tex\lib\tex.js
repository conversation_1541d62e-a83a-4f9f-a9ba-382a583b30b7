import {combineWithMathJax} from '../../../../../../../js/components/global.js';
import {VERSION} from '../../../../../../../js/components/version.js';

import * as module1 from '../../../../../../../js/output/common/fonts/tex/bold-italic.js';
import * as module2 from '../../../../../../../js/output/common/fonts/tex/bold.js';
import * as module3 from '../../../../../../../js/output/common/fonts/tex/delimiters.js';
import * as module4 from '../../../../../../../js/output/common/fonts/tex/double-struck.js';
import * as module5 from '../../../../../../../js/output/common/fonts/tex/fraktur-bold.js';
import * as module6 from '../../../../../../../js/output/common/fonts/tex/fraktur.js';
import * as module7 from '../../../../../../../js/output/common/fonts/tex/italic.js';
import * as module8 from '../../../../../../../js/output/common/fonts/tex/largeop.js';
import * as module9 from '../../../../../../../js/output/common/fonts/tex/monospace.js';
import * as module10 from '../../../../../../../js/output/common/fonts/tex/normal.js';
import * as module11 from '../../../../../../../js/output/common/fonts/tex/sans-serif-bold-italic.js';
import * as module12 from '../../../../../../../js/output/common/fonts/tex/sans-serif-bold.js';
import * as module13 from '../../../../../../../js/output/common/fonts/tex/sans-serif-italic.js';
import * as module14 from '../../../../../../../js/output/common/fonts/tex/sans-serif.js';
import * as module15 from '../../../../../../../js/output/common/fonts/tex/script-bold.js';
import * as module16 from '../../../../../../../js/output/common/fonts/tex/script.js';
import * as module17 from '../../../../../../../js/output/common/fonts/tex/smallop.js';
import * as module18 from '../../../../../../../js/output/common/fonts/tex/tex-calligraphic-bold.js';
import * as module19 from '../../../../../../../js/output/common/fonts/tex/tex-calligraphic.js';
import * as module20 from '../../../../../../../js/output/common/fonts/tex/tex-mathit.js';
import * as module21 from '../../../../../../../js/output/common/fonts/tex/tex-oldstyle-bold.js';
import * as module22 from '../../../../../../../js/output/common/fonts/tex/tex-oldstyle.js';
import * as module23 from '../../../../../../../js/output/common/fonts/tex/tex-size3.js';
import * as module24 from '../../../../../../../js/output/common/fonts/tex/tex-size4.js';
import * as module25 from '../../../../../../../js/output/common/fonts/tex/tex-variant.js';
import * as module26 from '../../../../../../../js/output/svg/fonts/tex.js';
import * as module27 from '../../../../../../../js/output/svg/fonts/tex/bold-italic.js';
import * as module28 from '../../../../../../../js/output/svg/fonts/tex/bold.js';
import * as module29 from '../../../../../../../js/output/svg/fonts/tex/fraktur-bold.js';
import * as module30 from '../../../../../../../js/output/svg/fonts/tex/fraktur.js';
import * as module31 from '../../../../../../../js/output/svg/fonts/tex/italic.js';
import * as module32 from '../../../../../../../js/output/svg/fonts/tex/largeop.js';
import * as module33 from '../../../../../../../js/output/svg/fonts/tex/monospace.js';
import * as module34 from '../../../../../../../js/output/svg/fonts/tex/normal.js';
import * as module35 from '../../../../../../../js/output/svg/fonts/tex/sans-serif-bold-italic.js';
import * as module36 from '../../../../../../../js/output/svg/fonts/tex/sans-serif-bold.js';
import * as module37 from '../../../../../../../js/output/svg/fonts/tex/sans-serif-italic.js';
import * as module38 from '../../../../../../../js/output/svg/fonts/tex/sans-serif.js';
import * as module39 from '../../../../../../../js/output/svg/fonts/tex/smallop.js';
import * as module40 from '../../../../../../../js/output/svg/fonts/tex/tex-calligraphic-bold.js';
import * as module41 from '../../../../../../../js/output/svg/fonts/tex/tex-calligraphic.js';
import * as module42 from '../../../../../../../js/output/svg/fonts/tex/tex-mathit.js';
import * as module43 from '../../../../../../../js/output/svg/fonts/tex/tex-oldstyle-bold.js';
import * as module44 from '../../../../../../../js/output/svg/fonts/tex/tex-oldstyle.js';
import * as module45 from '../../../../../../../js/output/svg/fonts/tex/tex-size3.js';
import * as module46 from '../../../../../../../js/output/svg/fonts/tex/tex-size4.js';
import * as module47 from '../../../../../../../js/output/svg/fonts/tex/tex-variant.js';

if (MathJax.loader) {
  MathJax.loader.checkVersion('output/svg/fonts/tex', VERSION, 'svg-font');
}

combineWithMathJax({_: {
  output: {
    common: {
      fonts: {
        tex: {
          "bold-italic": module1,
          bold: module2,
          delimiters: module3,
          "double-struck": module4,
          "fraktur-bold": module5,
          fraktur: module6,
          italic: module7,
          largeop: module8,
          monospace: module9,
          normal: module10,
          "sans-serif-bold-italic": module11,
          "sans-serif-bold": module12,
          "sans-serif-italic": module13,
          "sans-serif": module14,
          "script-bold": module15,
          script: module16,
          smallop: module17,
          "tex-calligraphic-bold": module18,
          "tex-calligraphic": module19,
          "tex-mathit": module20,
          "tex-oldstyle-bold": module21,
          "tex-oldstyle": module22,
          "tex-size3": module23,
          "tex-size4": module24,
          "tex-variant": module25
        }
      }
    },
    svg: {
      fonts: {
        tex_ts: module26,
        tex: {
          "bold-italic": module27,
          bold: module28,
          "fraktur-bold": module29,
          fraktur: module30,
          italic: module31,
          largeop: module32,
          monospace: module33,
          normal: module34,
          "sans-serif-bold-italic": module35,
          "sans-serif-bold": module36,
          "sans-serif-italic": module37,
          "sans-serif": module38,
          smallop: module39,
          "tex-calligraphic-bold": module40,
          "tex-calligraphic": module41,
          "tex-mathit": module42,
          "tex-oldstyle-bold": module43,
          "tex-oldstyle": module44,
          "tex-size3": module45,
          "tex-size4": module46,
          "tex-variant": module47
        }
      }
    }
  }
}});
