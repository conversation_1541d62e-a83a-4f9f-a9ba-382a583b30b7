import { CHTMLConstructor } from '../Wrapper.js';
import { StyleList } from '../../../util/StyleList.js';
declare const CHTMLmo_base: import("../../common/Wrappers/mo.js").MoConstructor & CHTMLConstructor<any, any, any>;
export declare class CHTMLmo<N, T, D> extends CHTMLmo_base {
    static kind: string;
    static styles: StyleList;
    toCHTML(parent: N): void;
    protected stretchHTML(chtml: N): void;
}
export {};
