{"version": 3, "file": "loader.js", "sourceRoot": "", "sources": ["../../ts/components/loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAyBA,yCAC2F;AAE3F,2CAAgF;AAChF,2CAAgF;AAAxE,qGAAA,OAAO,OAAA;AAAE,0GAAA,YAAY,OAAA;AAG7B,2DAAqD;AAsDxC,QAAA,WAAW,GAAyC;IAI/D,MAAM,EAAE,UAAC,IAAI;QACX,IAAI,cAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3C,IAAI,CAAC,IAAI,GAAG,cAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,SAAS,EAAE,UAAC,IAAI;QACd,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,EAAE;YACjD,IAAI,CAAC,IAAI,GAAG,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YACjD,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC;SACpB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,MAAM,EAAE,UAAC,IAAI;QACX,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE;YACjD,IAAI,CAAC,cAAM,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAE,MAAM;YAClD,IAAI,CAAC,IAAI,GAAG,cAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;SACxE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CAEF,CAAC;AAMF,IAAiB,MAAM,CAgItB;AAhID,WAAiB,MAAM;IAKrB,IAAM,OAAO,GAAG,mBAAQ,CAAC,OAAO,CAAC;IAKpB,eAAQ,GAAwB,IAAI,GAAG,EAAE,CAAC;IAQvD,SAAgB,KAAK;;QAAC,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;QACtC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;SAC7C;QACD,IAAM,QAAQ,GAAG,EAAE,CAAC;;YACpB,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,IAAM,SAAS,GAAG,oBAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAI,CAAC,IAAI,IAAI,oBAAO,CAAC,MAAI,EAAE,IAAI,CAAC,CAAC;gBACxE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAClC;;;;;;;;;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAVe,YAAK,QAUpB,CAAA;IAQD,SAAgB,IAAI;;QAAC,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;QACrC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,IAAM,QAAQ,GAAG,EAAE,CAAC;gCACT,MAAI;YACb,IAAI,SAAS,GAAG,oBAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,SAAS,EAAE;gBACd,SAAS,GAAG,IAAI,oBAAO,CAAC,MAAI,CAAC,CAAC;gBAC9B,SAAS,CAAC,QAAQ,CAAC,cAAM,CAAC,QAAQ,CAAC,MAAI,CAAC,CAAC,CAAC;aAC3C;YACD,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;gBACnC,IAAI,CAAC,cAAM,CAAC,eAAe;oBAAE,OAAO;gBACpC,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,OAAA,QAAQ,CAAC,GAAG,CAAC,oBAAO,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC,EAAE;oBAClE,OAAO,CAAC,IAAI,CAAC,yDAAkD,MAAI,CAAE,CAAC,CAAC;iBACxE;YACH,CAAC,CAAkB,CAAC,CAAC;;;YAZvB,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA;gBAAnB,IAAM,MAAI,kBAAA;wBAAJ,MAAI;aAad;;;;;;;;;QACD,oBAAO,CAAC,OAAO,EAAE,CAAC;QAClB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IArBe,WAAI,OAqBnB,CAAA;IAOD,SAAgB,OAAO;;QAAC,eAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,0BAAkB;;;YACxC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,MAAI,kBAAA;gBACb,IAAI,SAAS,GAAG,oBAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,IAAI,oBAAO,CAAC,MAAI,EAAE,IAAI,CAAC,CAAC;oBACpC,SAAS,CAAC,QAAQ,CAAC,cAAM,CAAC,QAAQ,CAAC,MAAI,CAAC,CAAC,CAAC;iBAC3C;gBACD,SAAS,CAAC,MAAM,EAAE,CAAC;aACpB;;;;;;;;;IACH,CAAC;IATe,cAAO,UAStB,CAAA;IAKD,SAAgB,YAAY;QAC1B,IAAI,OAAO,eAAO,CAAC,OAAO,KAAK,WAAW,EAAE;YAC1C,eAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SAChC;IACH,CAAC;IAJe,mBAAY,eAI3B,CAAA;IAOD,SAAgB,OAAO;QACrB,IAAI,IAAI,GAAG,SAAS,GAAG,YAAY,CAAC;QACpC,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YACnF,IAAI,MAAM,EAAE;gBACV,IAAI,GAAI,MAA4B,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aACnE;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IATe,cAAO,UAStB,CAAA;IAUD,SAAgB,YAAY,CAAC,IAAY,EAAE,OAAe,EAAE,KAAc;QACxE,OAAA,QAAQ,CAAC,GAAG,CAAC,oBAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QACjD,IAAI,cAAM,CAAC,eAAe,IAAI,OAAO,KAAK,OAAO,EAAE;YACjD,OAAO,CAAC,IAAI,CAAC,oBAAa,IAAI,mBAAS,OAAO,4CAAkC,OAAO,CAAE,CAAC,CAAC;YAC3F,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAPe,mBAAY,eAO3B,CAAA;IAKY,kBAAW,GAAG,IAAI,8BAAY,EAAE,CAAC;IAK9C,OAAA,WAAW,CAAC,GAAG,CAAC,mBAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACvC,OAAA,WAAW,CAAC,GAAG,CAAC,mBAAW,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAC3C,OAAA,WAAW,CAAC,GAAG,CAAC,mBAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAC,EAhIgB,MAAM,GAAN,cAAM,KAAN,cAAM,QAgItB;AAKY,QAAA,OAAO,GAAG,mBAAyB,CAAC;AAOjD,IAAI,OAAO,eAAO,CAAC,MAAM,KAAK,WAAW,EAAE;IAEzC,IAAA,2BAAe,EAAC,eAAO,CAAC,MAAM,EAAE,QAAQ,EAAE;QACxC,KAAK,EAAE;YACL,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;SAC1B;QACD,MAAM,EAAE,EAAE;QACV,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,EAAE;QACZ,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;QACvC,MAAM,EAAE,UAAC,KAAmB,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,kBAAW,KAAK,CAAC,OAAO,IAAI,GAAG,gBAAM,KAAK,CAAC,OAAO,CAAE,CAAC,EAAjE,CAAiE;QAClG,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,EAAE;QACf,eAAe,EAAE,IAAI;KACtB,CAAC,CAAC;IACH,IAAA,8BAAkB,EAAC;QACjB,MAAM,EAAE,MAAM;KACf,CAAC,CAAC;;QAKH,KAAqB,IAAA,KAAA,SAAA,eAAO,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAA,gBAAA,4BAAE;YAAnD,IAAM,MAAM,WAAA;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACzB,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9C;iBAAM;gBACL,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAChC;SACF;;;;;;;;;CACF;AAKY,QAAA,MAAM,GAAG,eAAO,CAAC,MAAM,CAAC,MAAM,CAAC"}