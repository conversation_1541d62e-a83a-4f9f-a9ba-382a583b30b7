import { ArrayItem, EqnArrayItem } from '../base/BaseItems.js';
export declare class MultlineItem extends ArrayItem {
    constructor(factory: any, ...args: any[]);
    get kind(): string;
    EndEntry(): void;
    EndRow(): void;
    EndTable(): void;
}
export declare class FlalignItem extends EqnArrayItem {
    name: string;
    numbered: boolean;
    padded: boolean;
    center: boolean;
    get kind(): string;
    constructor(factory: any, name: string, numbered: boolean, padded: boolean, center: boolean);
    EndEntry(): void;
    EndRow(): void;
    EndTable(): void;
}
