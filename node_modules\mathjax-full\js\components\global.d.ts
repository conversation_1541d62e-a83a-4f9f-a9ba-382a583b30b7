export interface MathJaxConfig {
    [name: string]: any;
}
export interface MathJaxLibrary {
    [name: string]: any;
}
export interface MathJaxObject {
    version: string;
    _: MathJaxLibrary;
    config: MathJaxConfig;
}
export declare function isObject(x: any): boolean;
export declare function combineConfig(dst: any, src: any): any;
export declare function combineDefaults(dst: any, name: string, src: any): any;
export declare function combineWithMathJax(config: any): MathJaxObject;
export declare const MathJax: MathJaxObject;
