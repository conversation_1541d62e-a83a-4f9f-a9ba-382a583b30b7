{"version": 3, "sources": ["../../mathjax-full/ts/core/MathItem.ts"], "sourcesContent": ["/*************************************************************\n *\n *  Copyright (c) 2017-2022 The MathJax Consortium\n *\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n/**\n * @fileoverview  Implements the interface and abstract class for MathItem objects\n *\n * <AUTHOR> (<PERSON><PERSON>)\n */\n\nimport {MathDocument} from './MathDocument.js';\nimport {InputJax} from './InputJax.js';\nimport {OptionList} from '../util/Options.js';\nimport {MmlNode} from './MmlTree/MmlNode.js';\n\n/*****************************************************************/\n/**\n *  The Location gives a location of a position in a document\n *  (either a node and character position within it, or\n *  an index into a string array, the character position within\n *  the string, and the delimiter at that location).\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n */\nexport type Location<N, T> = {\n  i?: number;\n  n?: number;\n  delim?: string;\n  node?: N | T;\n};\n\n/*****************************************************************/\n/**\n *  The Metrics object includes the data needed to typeset\n *  a MathItem.\n */\nexport type Metrics = {\n  em: number;\n  ex: number;\n  containerWidth: number;\n  lineWidth: number;\n  scale: number;\n};\n\n/*****************************************************************/\n/**\n *  The MathItem interface\n *\n *  The MathItem is the object that holds the information about a\n *  particular expression on the page, including pointers to\n *  where it is in the document, its compiled version (in the\n *  internal format), its typeset version, its bounding box,\n *  and so on.\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport interface MathItem<N, T, D> {\n  /**\n   * The string representing the expression to be processed\n   */\n  math: string;\n\n  /**\n   * The input jax used to process the math\n   */\n  inputJax: InputJax<N, T, D>;\n\n  /**\n   * Whether the math is in display mode or inline mode\n   */\n  display: boolean;\n\n  /**\n   * Whether this item is an escaped character or not\n   */\n  isEscaped: boolean;\n\n  /**\n   * The start and ending locations in the document of\n   *   this expression\n   */\n  start: Location<N, T>;\n  end: Location<N, T>;\n\n  /**\n   * The internal format for this expression (once compiled)\n   */\n  root: MmlNode;\n\n  /**\n   * The typeset version of the expression (once typeset)\n   */\n  typesetRoot: N;\n\n  /**\n   * The metric information at the location of the math\n   * (the em-size, scaling factor, etc.)\n   */\n  metrics: Metrics;\n\n  /**\n   * Extra data needed by the input or output jax, as needed\n   */\n  inputData: OptionList;\n  outputData: OptionList;\n\n  /**\n   * Perform the renderActions on the document\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   */\n  render(document: MathDocument<N, T, D>): void;\n\n  /**\n   * Rerenders an already rendered item and inserts it into the document\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   * @param {number=} start          The state to start rerendering at (default = RERENDER)\n   */\n  rerender(document: MathDocument<N, T, D>, start?: number): void;\n\n  /**\n   * Converts the expression by calling the render actions until the state matches the end state\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   * @param {number=} end            The state to end rerendering at (default = LAST)\n   */\n  convert(document: MathDocument<N, T, D>, end?: number): void;\n\n  /**\n   * Converts the expression into the internal format by calling the input jax\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   */\n  compile(document: MathDocument<N, T, D>): void;\n\n  /**\n   * Converts the internal format to the typeset version by calling the output jax\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   */\n  typeset(document: MathDocument<N, T, D>): void;\n\n  /**\n   * Inserts the typeset version in place of the original form in the document\n   *\n   * @param {MathDocument} document  The MathDocument in which the math resides\n   */\n  updateDocument(document: MathDocument<N, T, D>): void;\n\n  /**\n   * Removes the typeset version from the document, optionally replacing the original\n   * form of the expression and its delimiters.\n   *\n   * @param {boolean} restore  True if the original version is to be restored\n   */\n  removeFromDocument(restore: boolean): void;\n\n  /**\n   * Sets the metric information for this expression\n   *\n   * @param {number} em      The size of 1 em in pixels\n   * @param {number} ex      The size of 1 ex in pixels\n   * @param {number} cwidth  The container width in pixels\n   * @param {number} lwidth  The line breaking width in pixels\n   * @param {number} scale   The scaling factor (unitless)\n   */\n  setMetrics(em: number, ex: number, cwidth: number, lwidth: number, scale: number): void;\n\n  /**\n   * Set or return the current processing state of this expression,\n   * optionally restoring the document if rolling back an expression\n   * that has been added to the document.\n   *\n   * @param {number} state    The state to set for the expression\n   * @param {number} restore  True if the original form should be restored\n   *                           to the document when rolling back a typeset version\n   * @returns {number}        The current state\n   */\n  state(state?: number, restore?: boolean): number;\n\n  /**\n   * Reset the item to its unprocessed state\n   *\n   * @param {number} restore  True if the original form should be restored\n   *                           to the document when rolling back a typeset version\n   */\n  reset(restore?: boolean): void;\n}\n\n/*****************************************************************/\n/**\n *  The ProtoItem interface\n *\n *  This is what is returned by the FindMath class, giving the location\n *  of math within the document, and is used to produce the full\n *  MathItem later (e.g., when the position within a string array\n *  is translated back into the actual node location in the DOM).\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n */\nexport type ProtoItem<N, T> = {\n  math: string;            // The math expression itself\n  start: Location<N, T>;   // The starting location of the math\n  end: Location<N, T>;     // The ending location of the math\n  open?: string;           // The opening delimiter\n  close?: string;          // The closing delimiter\n  n?: number;              // The index of the string in which this math is found\n  display: boolean;        // True means display mode, false is inline mode\n};\n\n/**\n *  Produce a proto math item that can be turned into a MathItem\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n */\nexport function protoItem<N, T>(open: string, math: string, close: string, n: number,\n                                start: number, end: number, display: boolean = null) {\n  let item: ProtoItem<N, T> = {open: open, math: math, close: close,\n                               n: n, start: {n: start}, end: {n: end}, display: display};\n  return item;\n}\n\n/*****************************************************************/\n/**\n *  Implements the MathItem class\n *\n * @template N  The HTMLElement node class\n * @template T  The Text node class\n * @template D  The Document class\n */\nexport abstract class AbstractMathItem<N, T, D> implements MathItem<N, T, D> {\n\n  /**\n   * The source text for the math (e.g., TeX string)\n   */\n  public math: string;\n\n  /**\n   * The input jax associated with this item\n   */\n\n  public inputJax: InputJax<N, T, D>;\n\n  /**\n   * True when this math is in display mode\n   */\n  public display: boolean;\n\n  /**\n   * Reference to the beginning of the math in the document\n   */\n  public start: Location<N, T>;\n  /**\n   * Reference to the end of the math in the document\n   */\n  public end: Location<N, T>;\n\n  /**\n   * The compiled internal MathML (result of InputJax)\n   */\n  public root: MmlNode = null;\n  /**\n   * The typeset result (result of OutputJax)\n   */\n  public typesetRoot: N = null;\n\n  /**\n   * The metric information about the surrounding environment\n   */\n  public metrics: Metrics = {} as Metrics;\n\n  /**\n   * Data private to the input jax\n   */\n  public inputData: OptionList = {};\n\n  /**\n   * Data private to the output jax\n   */\n  public outputData: OptionList = {};\n\n  /**\n   * The current state of the item (how far in the render actions it has been processed)\n   */\n  protected _state: number = STATE.UNPROCESSED;\n\n  /**\n   * @return {boolean}   True when this item is an escaped delimiter\n   */\n  public get isEscaped(): boolean {\n    return this.display === null;\n  }\n\n  /**\n   * @param {string} math      The math expression for this item\n   * @param {Inputjax} jax     The input jax to use for this item\n   * @param {boolean} display  True if display mode, false if inline\n   * @param {Location} start   The starting position of the math in the document\n   * @param {Location} end     The ending position of the math in the document\n   * @constructor\n   */\n  constructor (math: string, jax: InputJax<N, T, D>, display: boolean = true,\n               start: Location<N, T> = {i: 0, n: 0, delim: ''},\n               end: Location<N, T> = {i: 0, n: 0, delim: ''}) {\n    this.math = math;\n    this.inputJax = jax;\n    this.display = display;\n    this.start = start;\n    this.end = end;\n    this.root = null;\n    this.typesetRoot = null;\n    this.metrics = {} as Metrics;\n    this.inputData = {};\n    this.outputData = {};\n  }\n\n  /**\n   * @override\n   */\n  public render(document: MathDocument<N, T, D>) {\n    document.renderActions.renderMath(this, document);\n  }\n\n  /**\n   * @override\n   */\n  public rerender(document: MathDocument<N, T, D>, start: number = STATE.RERENDER) {\n    if (this.state() >= start) {\n      this.state(start - 1);\n    }\n    document.renderActions.renderMath(this, document, start);\n  }\n\n  /**\n   * @override\n   */\n  public convert(document: MathDocument<N, T, D>, end: number = STATE.LAST) {\n    document.renderActions.renderConvert(this, document, end);\n  }\n\n  /**\n   * @override\n   */\n  public compile(document: MathDocument<N, T, D>) {\n    if (this.state() < STATE.COMPILED) {\n      this.root = this.inputJax.compile(this, document);\n      this.state(STATE.COMPILED);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public typeset(document: MathDocument<N, T, D>) {\n    if (this.state() < STATE.TYPESET) {\n      this.typesetRoot = document.outputJax[this.isEscaped ? 'escaped' : 'typeset'](this, document);\n      this.state(STATE.TYPESET);\n    }\n  }\n\n  /**\n   * @override\n   */\n  public updateDocument(_document: MathDocument<N, T, D>) {}\n\n  /**\n   * @override\n   */\n  public removeFromDocument(_restore: boolean = false) {}\n\n  /**\n   * @override\n   */\n  public setMetrics(em: number, ex: number, cwidth: number, lwidth: number, scale: number) {\n    this.metrics = {\n      em: em, ex: ex,\n      containerWidth: cwidth,\n      lineWidth: lwidth,\n      scale: scale\n    };\n  }\n\n  /**\n   * @override\n   */\n  public state(state: number = null, restore: boolean = false) {\n    if (state != null) {\n      if (state < STATE.INSERTED && this._state >= STATE.INSERTED) {\n        this.removeFromDocument(restore);\n      }\n      if (state < STATE.TYPESET && this._state >= STATE.TYPESET) {\n        this.outputData = {};\n      }\n      if (state < STATE.COMPILED && this._state >= STATE.COMPILED) {\n        this.inputData = {};\n      }\n      this._state = state;\n    }\n    return this._state;\n  }\n\n  /**\n   * @override\n   */\n  public reset(restore: boolean = false) {\n    this.state(STATE.UNPROCESSED, restore);\n  }\n\n}\n\n/*****************************************************************/\n/**\n * The various states that a MathItem (or MathDocument) can be in\n *   (open-ended so that extensions can add to it)\n */\nexport const STATE: {[state: string]: number} = {\n  UNPROCESSED: 0,\n  FINDMATH: 10,\n  COMPILED: 20,\n  CONVERT: 100,\n  METRICS: 110,\n  RERENDER: 125,\n  TYPESET: 150,\n  INSERTED: 200,\n  LAST: 10000\n};\n\n/**\n * Allocate a new named state\n *\n * @param {string} name    The name of the new state\n * @param {number} state   The value for the new state\n */\nexport function newState(name: string, state: number) {\n  if (name in STATE) {\n    throw Error('State ' + name + ' already exists');\n  }\n  STATE[name] = state;\n}\n"], "mappings": ";;;;;;;;;;AA0OA,aAAgB,UAAgB,MAAc,MAAc,OAAe,GAC3C,OAAe,KAAa,SAAuB;AAAvB,UAAA,YAAA,QAAA;AAAA,kBAAA;MAAuB;AACjF,UAAI,OAAwB;QAAC;QAAY;QAAY;QACxB;QAAM,OAAO,EAAC,GAAG,MAAK;QAAG,KAAK,EAAC,GAAG,IAAG;QAAG;MAAgB;AACrF,aAAO;IACT;AALA,YAAA,YAAA;AAeA,QAAA,mBAAA,WAAA;AAuEE,eAAAA,kBAAa,MAAc,KAAwB,SACtC,OACA,KAA6C;AAFP,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAuB;AAC7D,YAAA,UAAA,QAAA;AAAA,kBAAA,EAAyB,GAAG,GAAG,GAAG,GAAG,OAAO,GAAE;QAAC;AAC/C,YAAA,QAAA,QAAA;AAAA,gBAAA,EAAuB,GAAG,GAAG,GAAG,GAAG,OAAO,GAAE;QAAC;AA3CnD,aAAA,OAAgB;AAIhB,aAAA,cAAiB;AAKjB,aAAA,UAAmB,CAAA;AAKnB,aAAA,YAAwB,CAAA;AAKxB,aAAA,aAAyB,CAAA;AAKtB,aAAA,SAAiB,QAAA,MAAM;AAoB/B,aAAK,OAAO;AACZ,aAAK,WAAW;AAChB,aAAK,UAAU;AACf,aAAK,QAAQ;AACb,aAAK,MAAM;AACX,aAAK,OAAO;AACZ,aAAK,cAAc;AACnB,aAAK,UAAU,CAAA;AACf,aAAK,YAAY,CAAA;AACjB,aAAK,aAAa,CAAA;MACpB;AAzBA,aAAA,eAAWA,kBAAA,WAAA,aAAS;aAApB,WAAA;AACE,iBAAO,KAAK,YAAY;QAC1B;;;;AA4BO,MAAAA,kBAAA,UAAA,SAAP,SAAc,UAA+B;AAC3C,iBAAS,cAAc,WAAW,MAAM,QAAQ;MAClD;AAKO,MAAAA,kBAAA,UAAA,WAAP,SAAgB,UAAiC,OAA8B;AAA9B,YAAA,UAAA,QAAA;AAAA,kBAAgB,QAAA,MAAM;QAAQ;AAC7E,YAAI,KAAK,MAAK,KAAM,OAAO;AACzB,eAAK,MAAM,QAAQ,CAAC;;AAEtB,iBAAS,cAAc,WAAW,MAAM,UAAU,KAAK;MACzD;AAKO,MAAAA,kBAAA,UAAA,UAAP,SAAe,UAAiC,KAAwB;AAAxB,YAAA,QAAA,QAAA;AAAA,gBAAc,QAAA,MAAM;QAAI;AACtE,iBAAS,cAAc,cAAc,MAAM,UAAU,GAAG;MAC1D;AAKO,MAAAA,kBAAA,UAAA,UAAP,SAAe,UAA+B;AAC5C,YAAI,KAAK,MAAK,IAAK,QAAA,MAAM,UAAU;AACjC,eAAK,OAAO,KAAK,SAAS,QAAQ,MAAM,QAAQ;AAChD,eAAK,MAAM,QAAA,MAAM,QAAQ;;MAE7B;AAKO,MAAAA,kBAAA,UAAA,UAAP,SAAe,UAA+B;AAC5C,YAAI,KAAK,MAAK,IAAK,QAAA,MAAM,SAAS;AAChC,eAAK,cAAc,SAAS,UAAU,KAAK,YAAY,YAAY,SAAS,EAAE,MAAM,QAAQ;AAC5F,eAAK,MAAM,QAAA,MAAM,OAAO;;MAE5B;AAKO,MAAAA,kBAAA,UAAA,iBAAP,SAAsB,WAAgC;MAAG;AAKlD,MAAAA,kBAAA,UAAA,qBAAP,SAA0B,UAAyB;AAAzB,YAAA,aAAA,QAAA;AAAA,qBAAA;QAAyB;MAAG;AAK/C,MAAAA,kBAAA,UAAA,aAAP,SAAkB,IAAY,IAAY,QAAgB,QAAgB,OAAa;AACrF,aAAK,UAAU;UACb;UAAQ;UACR,gBAAgB;UAChB,WAAW;UACX;;MAEJ;AAKO,MAAAA,kBAAA,UAAA,QAAP,SAAa,OAAsB,SAAwB;AAA9C,YAAA,UAAA,QAAA;AAAA,kBAAA;QAAoB;AAAE,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AACzD,YAAI,SAAS,MAAM;AACjB,cAAI,QAAQ,QAAA,MAAM,YAAY,KAAK,UAAU,QAAA,MAAM,UAAU;AAC3D,iBAAK,mBAAmB,OAAO;;AAEjC,cAAI,QAAQ,QAAA,MAAM,WAAW,KAAK,UAAU,QAAA,MAAM,SAAS;AACzD,iBAAK,aAAa,CAAA;;AAEpB,cAAI,QAAQ,QAAA,MAAM,YAAY,KAAK,UAAU,QAAA,MAAM,UAAU;AAC3D,iBAAK,YAAY,CAAA;;AAEnB,eAAK,SAAS;;AAEhB,eAAO,KAAK;MACd;AAKO,MAAAA,kBAAA,UAAA,QAAP,SAAa,SAAwB;AAAxB,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAwB;AACnC,aAAK,MAAM,QAAA,MAAM,aAAa,OAAO;MACvC;AAEF,aAAAA;IAAA,EAlLA;AAAsB,YAAA,mBAAA;AAyLT,YAAA,QAAmC;MAC9C,aAAa;MACb,UAAU;MACV,UAAU;MACV,SAAS;MACT,SAAS;MACT,UAAU;MACV,SAAS;MACT,UAAU;MACV,MAAM;;AASR,aAAgB,SAAS,MAAc,OAAa;AAClD,UAAI,QAAQ,QAAA,OAAO;AACjB,cAAM,MAAM,WAAW,OAAO,iBAAiB;;AAEjD,cAAA,MAAM,IAAI,IAAI;IAChB;AALA,YAAA,WAAA;;;", "names": ["AbstractMathItem"]}