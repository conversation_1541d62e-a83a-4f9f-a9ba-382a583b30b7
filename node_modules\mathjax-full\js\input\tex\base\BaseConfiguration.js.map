{"version": 3, "file": "BaseConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/base/BaseConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,wDAAkD;AAClD,kDAA4C;AAC5C,+DAAsC;AACtC,+DAAsC;AAEtC,gDAA6C;AAC7C,oDAAwC;AACxC,sCAAwC;AACxC,6BAA2B;AAC3B,qFAAqE;AAOrE,IAAI,2BAAY,CAAC,OAAO,EAAE,IAAI,EAAE;IAC9B,GAAG,EAAI,QAAQ;IACf,GAAG,EAAI,QAAQ;IACf,GAAG,EAAI,QAAQ;CAChB,CAAC,CAAC;AAQH,SAAgB,KAAK,CAAC,MAAiB,EAAE,IAAY;IACnD,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC;QAEd,EAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/C,IAAM,KAAK,GAAI,0BAAU,CAAC,MAAM,CAAC,OAAO,CAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxE,IAAM,KAAK,GAAG,IAAA,gCAAQ,EAAC,IAAI,CAAC,CAAC;IAC7B,IAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAGvC,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,qBAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;KACjD;IACD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC;AAjBD,sBAiBC;AAQD,SAAS,WAAW,CAAC,OAAkB,EAAE,IAAY;IAEnD,MAAM,IAAI,qBAAQ,CAAC,0BAA0B,EACzB,+BAA+B,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC;AACpE,CAAC;AAQD,SAAS,YAAY,CAAC,OAAkB,EAAE,GAAW;IAEnD,MAAM,IAAI,qBAAQ,CAAC,YAAY,EAAE,4BAA4B,EAAE,GAAG,CAAC,CAAC;AACtE,CAAC;AAMD,SAAS,eAAe,CAAC,EAA4B;;QAA3B,IAAI,UAAA;;QAC5B,KAAkB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA,gBAAA,4BAAE;YAAxC,IAAM,GAAG,WAAA;YAKZ,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBAKzC,IAAM,QAAM,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC1B,QAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;gBAMrC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBACtB,IAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAY,CAAC;oBAC5C,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;oBACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAuB,CAAC,CAAC;iBAC7E;aACF;iBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAK7B,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAChD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACpC;SACF;;;;;;;;;AACH,CAAC;AAOD;IAA8B,4BAAY;IAA1C;;IAA6C,CAAC;IAAD,eAAC;AAAD,CAAC,AAA9C,CAA8B,sBAAY,GAAI;AAAjC,4BAAQ;AAOR,QAAA,iBAAiB,GAAkB,gCAAa,CAAC,MAAM,CAClE,MAAM,EAAG;IACP,OAAO,EAAE;QACP,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;QACpD,SAAS,EAAE,CAAC,WAAW,CAAC;QAExB,KAAK,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC;QACzE,WAAW,EAAE,CAAC,aAAa,CAAC;KAC7B;IACD,QAAQ,EAAE;QACR,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,YAAY;KAC1B;IACD,KAAK;QAEH,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,UAAU;QACnD,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,MAAM;QAC3C,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,OAAO;QAC7C,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,YAAY;QACvD,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,OAAO;QAC7C,GAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,MAAM;QAC3C,GAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,OAAO;QAC7C,GAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,aAAa;QACzD,GAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,QAAQ;QAC/C,GAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,SAAS;QACjD,GAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,YAAY;QACvD,GAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG,KAAK,CAAC,YAAY;WACxD;IACD,OAAO,EAAE;QACP,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,CAAC,OAAM,CAAC,QAAQ,CAAC,KAAK,WAAW;YAChC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;YACrD,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;KAC7D;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;KACf;IACD,cAAc,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC;CACxC,CACF,CAAC"}