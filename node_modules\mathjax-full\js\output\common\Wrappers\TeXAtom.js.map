{"version": 3, "file": "TeXAtom.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/TeXAtom.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAyBA,+DAA0D;AAoB1D,SAAgB,kBAAkB,CAA+B,IAAO;IAEtE;QAAqB,2BAAI;QAAlB;;QAsBP,CAAC;QAjBQ,6BAAW,GAAlB,UAAmB,IAAU,EAAE,SAA0B;YAA1B,0BAAA,EAAA,iBAA0B;YACvD,iBAAM,WAAW,YAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACnC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE;gBACpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;aACtC;YAID,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,qBAAQ,CAAC,OAAO,EAAE;gBACpC,IAAA,CAAC,GAAO,IAAI,EAAX,EAAE,CAAC,GAAI,IAAI,EAAR,CAAS;gBACpB,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACvC,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;gBACb,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;aACd;QACH,CAAC;QAEH,cAAC;IAAD,CAAC,AAtBM,CAAc,IAAI,GAsBvB;AAEJ,CAAC;AA1BD,gDA0BC"}