{"version": 3, "file": "SetOptionsConfiguration.js", "sourceRoot": "", "sources": ["../../../../ts/input/tex/setoptions/SetOptionsConfiguration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwBA,wDAA6F;AAG7F,gDAA2C;AAC3C,+DAAsC;AACtC,iEAAwC;AACxC,0CAAmC;AACnC,0EAAiD;AACjD,uDAA8D;AAEjD,QAAA,cAAc,GAAG;IAS5B,aAAa,EAAb,UAAc,MAAiB,EAAE,SAAiB;QAChD,IAAI,SAAS,KAAK,KAAK,IAAI,CAAC,uCAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YAC/D,MAAM,IAAI,qBAAQ,CAAC,aAAa,EAAE,2BAA2B,EAAE,SAAS,CAAC,CAAC;SAC3E;QACD,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACzC,IAAM,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,OAAO,KAAK,KAAK,EAAE;YAC/E,MAAM,IAAI,qBAAQ,CAAC,oBAAoB,EAAE,wCAAwC,EAAE,SAAS,CAAC,CAAC;SAC/F;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAUD,YAAY,EAAZ,UAAa,MAAiB,EAAE,SAAiB,EAAE,MAAc;;QAC/D,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACzC,IAAM,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACrD,IAAM,KAAK,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,qBAAQ,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACtG,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE;YACtE,MAAM,IAAI,qBAAQ,CAAC,mBAAmB,EAAE,sCAAsC,EAAE,MAAM,CAAC,CAAC;SACzF;QACD,IAAI,CAAC,CAAA,MAAA,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,0CAAE,cAAc,CAAC,MAAM,CAAC,CAAA,EAAE;YAC/F,IAAI,SAAS,KAAK,KAAK,EAAE;gBACvB,MAAM,IAAI,qBAAQ,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,MAAM,CAAC,CAAC;aAC3E;iBAAM;gBACL,MAAM,IAAI,qBAAQ,CAAC,kBAAkB,EAAE,sCAAsC,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aACnG;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAWD,WAAW,EAAX,UAAY,OAAkB,EAAE,UAAkB,EAAE,OAAe,EAAE,KAAa;QAChF,OAAO,KAAK,CAAC;IACf,CAAC;CAEF,CAAC;AAEF,IAAM,aAAa,GAAG,IAAI,yBAAU,CAAC,YAAY,EAAE;IACjD,UAAU,EAAE,YAAY;CACzB,EAAE;IAOD,UAAU,EAAV,UAAW,MAAiB,EAAE,IAAY;;QACxC,IAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;QACpD,IAAM,OAAO,GAAG,sBAAS,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC;YAAE,OAAO;;YACrD,KAAkB,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,gBAAA,4BAAE;gBAAnC,IAAM,GAAG,WAAA;gBACZ,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,EAAE;oBAC/C,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;wBACrE,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC5D;aACF;;;;;;;;;IACH,CAAC;CACF,CAAC,CAAC;AAUH,SAAS,gBAAgB,CAAC,OAA4B,EAAE,GAAuB;IAC7E,IAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,CAAQ,CAAC;IAChF,IAAI,OAAO,EAAE;QACX,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,iBAAK,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,iBAAK,CAAC,SAAS,EAAE,wBAAW,CAAC,KAAK,EAC5B,CAAC,mCAAmC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;KACvF;AACH,CAAC;AAEY,QAAA,uBAAuB,GAAG,gCAAa,CAAC,MAAM,CACzD,YAAY,EAAE;IACZ,OAAO,EAAE,EAAC,KAAK,EAAE,CAAC,YAAY,CAAC,EAAC;IAChC,MAAM,EAAE,gBAAgB;IACxB,QAAQ,EAAE,CAAC;IACX,OAAO,EAAE;QACP,UAAU,EAAE;YACV,aAAa,EAAE,sBAAc,CAAC,aAAa;YAC3C,YAAY,EAAE,sBAAc,CAAC,YAAY;YACzC,WAAW,EAAE,sBAAc,CAAC,WAAW;YACvC,mBAAmB,EAAE,IAAI;YACzB,mBAAmB,EAAE,IAAI;YACzB,YAAY,EAAE,IAAA,uBAAU,EAAC;gBAKvB,GAAG,EAAE;oBACH,OAAO,EAAE,KAAK;oBACd,WAAW,EAAE,KAAK;oBAClB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,KAAK;oBAChB,MAAM,EAAE,KAAK;oBACb,YAAY,EAAE,KAAK;iBACpB;gBAID,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,KAAK;gBACnB,SAAS,EAAE,KAAK;aACjB,CAAC;SACH;KACF;CACF,CACF,CAAC"}