pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*
  IR_Black style (c) <PERSON><PERSON> <v<PERSON><PERSON>@programica.ru>
*/
.hljs {
  background: #000;
  color: #f8f8f8
}
.hljs-comment,
.hljs-quote,
.hljs-meta {
  color: #7c7c7c
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-tag,
.hljs-name {
  color: #96cbfe
}
.hljs-attribute,
.hljs-selector-id {
  color: #ffffb6
}
.hljs-string,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-addition {
  color: #a8ff60
}
.hljs-subst {
  color: #daefa3
}
.hljs-regexp,
.hljs-link {
  color: #e9c062
}
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-doctag {
  color: #ffffb6
}
.hljs-symbol,
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-literal {
  color: #c6c5fe
}
.hljs-number,
.hljs-deletion {
  color: #ff73fd
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}