{"version": 3, "file": "mml3-node.js", "sourceRoot": "", "sources": ["../../../../ts/input/mathml/mml3/mml3-node.ts"], "names": [], "mappings": ";;;AAoCA,SAAgB,eAAe;IAE7B,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IAEpC,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAClC,IAAI;QACF,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;KACjC;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,KAAK,CAAC,iFAAiF,CAAC,CAAC;KAChG;IACD,IAAM,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;IACtC,IAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACjC,IAAM,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACxD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,OAAO,UAAC,IAAO,EAAE,GAA0B;QACzC,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC5B,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAIlC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YAC5B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,oBAAoB,EAAE,0DAA0D,CAAC,CAAC;SACrG;QAID,IAAI,MAAM,CAAC;QACX,IAAI;YACF,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;gBACrE,kBAAkB,EAAE,IAAI;gBACxB,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,YAAY;aAC1B,CAAC,CAAC,eAAe,CAAC,CAAC,CAAM,CAAC;SAC5B;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,GAAG,IAAI,CAAC;SACf;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAvCD,0CAuCC"}