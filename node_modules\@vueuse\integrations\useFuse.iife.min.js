(function(a,i,e){"use strict";function c(o,s,n){const r=()=>{var t,u;return new i((t=e.toValue(s))!=null?t:[],(u=e.toValue(n))==null?void 0:u.fuseOptions)},l=e.ref(r());e.watch(()=>{var t;return(t=e.toValue(n))==null?void 0:t.fuseOptions},()=>{l.value=r()},{deep:!0}),e.watch(()=>e.toValue(s),t=>{l.value.setCollection(t)},{deep:!0});const V=e.computed(()=>{const t=e.toValue(n);if(t?.matchAllWhenSearchEmpty&&!e.toValue(o))return e.toValue(s).map((d,f)=>({item:d,refIndex:f}));const u=t?.resultLimit;return l.value.search(e.toValue(o),u?{limit:u}:void 0)});return{fuse:l,results:V}}a.useFuse=c})(this.VueUse=this.VueUse||{},Fuse,Vue);
