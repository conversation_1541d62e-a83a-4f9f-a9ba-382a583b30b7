(function(){"use strict";var __webpack_modules__={706:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=new Map;t.default=n},8905:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}},c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.sreReady=t.Sre=void 0;var l,u=i(n(9037)),d=i(n(907)),p=i(n(7317)),h=i(n(4998)),f=c(n(4886)),m=n(3955),g=i(n(9009)),b=n(4513),y=c(n(706));!function(e){e.locales=b.Variables.LOCALES,e.sreReady=u.engineReady,e.setupEngine=u.setupEngine,e.engineSetup=u.engineSetup,e.toEnriched=u.toEnriched,e.toSpeech=u.toSpeech,e.clearspeakPreferences=m.ClearspeakPreferences,e.getHighlighter=g.highlighter,e.getSpeechGenerator=p.generator,e.getWalker=d.walker,e.clearspeakStyle=function(){return h.DOMAIN_TO_STYLES.clearspeak},e.preloadLocales=function(e){return s(this,void 0,void 0,(function(){var t;return a(this,(function(n){return[2,(t=y.default.get(e))?new Promise((function(e,n){return e(JSON.stringify(t))})):u.localeLoader()(e)]}))}))}}(l=t.Sre||(t.Sre={})),t.sreReady=l.sreReady,f.default.getInstance().delay=!0,t.default=l},7306:function(e,t){t.q=void 0,t.q="3.2.2"},3021:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.MJContextMenu=void 0;var a=n(9964),c=n(2745),l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.mathItem=null,t.annotation="",t.annotationTypes={},t}return o(t,e),t.prototype.post=function(t,n){if(this.mathItem){if(void 0!==n){var r=this.mathItem.inputJax.name,o=this.findID("Show","Original");o.content="MathML"===r?"Original MathML":r+" Commands",this.findID("Copy","Original").content=o.content;var i=this.findID("Settings","semantics");"MathML"===r?i.disable():i.enable(),this.getAnnotationMenu(),this.dynamicSubmenus()}e.prototype.post.call(this,t,n)}},t.prototype.unpost=function(){e.prototype.unpost.call(this),this.mathItem=null},t.prototype.findID=function(){for(var e,t,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=this,s=null;try{for(var a=i(n),l=a.next();!l.done;l=a.next()){var u=l.value;o?(s=o.find(u),o=s instanceof c.Submenu?s.submenu:null):s=null}}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return s},t.prototype.getAnnotationMenu=function(){var e=this,t=this.getAnnotations(this.getSemanticNode());this.createAnnotationMenu("Show",t,(function(){return e.showAnnotation.post()})),this.createAnnotationMenu("Copy",t,(function(){return e.copyAnnotation()}))},t.prototype.getSemanticNode=function(){for(var e=this.mathItem.root;e&&!e.isKind("semantics");){if(e.isToken||1!==e.childNodes.length)return null;e=e.childNodes[0]}return e},t.prototype.getAnnotations=function(e){var t,n,r=[];if(!e)return r;try{for(var o=i(e.childNodes),s=o.next();!s.done;s=o.next()){var a=s.value;if(a.isKind("annotation")){var c=this.annotationMatch(a);if(c){var l=a.childNodes.reduce((function(e,t){return e+t.toString()}),"");r.push([c,l])}}}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return r},t.prototype.annotationMatch=function(e){var t,n,r=e.attributes.get("encoding");try{for(var o=i(Object.keys(this.annotationTypes)),s=o.next();!s.done;s=o.next()){var a=s.value;if(this.annotationTypes[a].indexOf(r)>=0)return a}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return null},t.prototype.createAnnotationMenu=function(e,t,n){var r=this,o=this.findID(e,"Annotation");o.submenu=this.factory.get("subMenu")(this.factory,{items:t.map((function(e){var t=s(e,2),o=t[0],i=t[1];return{type:"command",id:o,content:o,action:function(){r.annotation=i,n()}}})),id:"annotations"},o),t.length?o.enable():o.disable()},t.prototype.dynamicSubmenus=function(){var e,n;try{for(var r=i(t.DynamicSubmenus),o=r.next();!o.done;o=r.next()){var a=s(o.value,2),c=a[0],l=a[1],u=this.find(c);if(u){var d=l(this,u);u.submenu=d,d.items.length?u.enable():u.disable()}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}},t.DynamicSubmenus=new Map,t}(a.ContextMenu);t.MJContextMenu=l},1062:function(e,t,n){var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s},o=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Menu=void 0;var s=n(3184),a=n(4769),c=n(8723),l=n(9077),u=n(3021),d=n(9232),p=n(7309),h=n(6932),f=n(7258),m=n(1156),g=n(6837),b=i(n(8905)),y=c.MathJax,S="undefined"!=typeof window&&window.navigator&&"Mac"===window.navigator.platform.substr(0,3),E=function(){function e(e,t){void 0===t&&(t={});var n=this;this.settings=null,this.defaultSettings=null,this.menu=null,this.MmlVisitor=new d.MmlVisitor,this.jax={CHTML:null,SVG:null},this.rerenderStart=a.STATE.LAST,this.about=new h.Info('<b style="font-size:120%;">MathJax</b> v'+s.mathjax.version,(function(){var e=[];return e.push("Input Jax: "+n.document.inputJax.map((function(e){return e.name})).join(", ")),e.push("Output Jax: "+n.document.outputJax.name),e.push("Document Type: "+n.document.kind),e.join("<br/>")}),'<a href="https://www.mathjax.org">www.mathjax.org</a>'),this.help=new h.Info("<b>MathJax Help</b>",(function(){return["<p><b>MathJax</b> is a JavaScript library that allows page"," authors to include mathematics within their web pages."," As a reader, you don't need to do anything to make that happen.</p>","<p><b>Browsers</b>: MathJax works with all modern browsers including"," Edge, Firefox, Chrome, Safari, Opera, and most mobile browsers.</p>","<p><b>Math Menu</b>: MathJax adds a contextual menu to equations."," Right-click or CTRL-click on any mathematics to access the menu.</p>",'<div style="margin-left: 1em;">',"<p><b>Show Math As:</b> These options allow you to view the formula's"," source markup (as MathML or in its original format).</p>","<p><b>Copy to Clipboard:</b> These options copy the formula's source markup,"," as MathML or in its original format, to the clipboard"," (in browsers that support that).</p>","<p><b>Math Settings:</b> These give you control over features of MathJax,"," such the size of the mathematics, and the mechanism used"," to display equations.</p>","<p><b>Accessibility</b>: MathJax can work with screen"," readers to make mathematics accessible to the visually impaired."," Turn on the explorer to enable generation of speech strings"," and the ability to investigate expressions interactively.</p>","<p><b>Language</b>: This menu lets you select the language used by MathJax"," for its menus and warning messages. (Not yet implemented in version 3.)</p>","</div>","<p><b>Math Zoom</b>: If you are having difficulty reading an"," equation, MathJax can enlarge it to help you see it better, or"," you can scall all the math on the page to make it larger."," Turn these features on in the <b>Math Settings</b> menu.</p>","<p><b>Preferences</b>: MathJax uses your browser's localStorage database"," to save the preferences set via this menu locally in your browser.  These"," are not used to track you, and are not transferred or used remotely by"," MathJax in any way.</p>"].join("\n")}),'<a href="https://www.mathjax.org">www.mathjax.org</a>'),this.mathmlCode=new p.SelectableInfo("MathJax MathML Expression",(function(){if(!n.menu.mathItem)return"";var e=n.toMML(n.menu.mathItem);return"<pre>"+n.formatSource(e)+"</pre>"}),""),this.originalText=new p.SelectableInfo("MathJax Original Source",(function(){if(!n.menu.mathItem)return"";var e=n.menu.mathItem.math;return'<pre style="font-size:125%; margin:0">'+n.formatSource(e)+"</pre>"}),""),this.annotationText=new p.SelectableInfo("MathJax Annotation Text",(function(){if(!n.menu.mathItem)return"";var e=n.menu.annotation;return'<pre style="font-size:125%; margin:0">'+n.formatSource(e)+"</pre>"}),""),this.zoomBox=new h.Info("MathJax Zoomed Expression",(function(){if(!n.menu.mathItem)return"";var e=n.menu.mathItem.typesetRoot.cloneNode(!0);return e.style.margin="0",'<div style="font-size: '******parseFloat(n.settings.zscale)+'%">'+e.outerHTML+"</div>"}),""),this.document=e,this.options=(0,l.userOptions)((0,l.defaultOptions)({},this.constructor.OPTIONS),t),this.initSettings(),this.mergeUserSettings(),this.initMenu(),this.applySettings()}return Object.defineProperty(e.prototype,"isLoading",{get:function(){return e.loading>0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"loadingPromise",{get:function(){return this.isLoading?(e._loadingPromise||(e._loadingPromise=new Promise((function(t,n){e._loadingOK=t,e._loadingFailed=n}))),e._loadingPromise):Promise.resolve()},enumerable:!1,configurable:!0}),e.prototype.initSettings=function(){this.settings=this.options.settings,this.jax=this.options.jax;var e=this.document.outputJax;this.jax[e.name]=e,this.settings.renderer=e.name,y._.a11y&&y._.a11y.explorer&&Object.assign(this.settings,this.document.options.a11y),this.settings.scale=e.options.scale,this.defaultSettings=Object.assign({},this.settings)},e.prototype.initMenu=function(){var e=this,t=new f.Parser([["contextMenu",u.MJContextMenu.fromJson.bind(u.MJContextMenu)]]);this.menu=t.parse({type:"contextMenu",id:"MathJax_Menu",pool:[this.variable("texHints"),this.variable("semantics"),this.variable("zoom"),this.variable("zscale"),this.variable("renderer",(function(t){return e.setRenderer(t)})),this.variable("alt"),this.variable("cmd"),this.variable("ctrl"),this.variable("shift"),this.variable("scale",(function(t){return e.setScale(t)})),this.variable("explorer",(function(t){return e.setExplorer(t)})),this.a11yVar("highlight"),this.a11yVar("backgroundColor"),this.a11yVar("backgroundOpacity"),this.a11yVar("foregroundColor"),this.a11yVar("foregroundOpacity"),this.a11yVar("speech"),this.a11yVar("subtitles"),this.a11yVar("braille"),this.a11yVar("viewBraille"),this.a11yVar("locale",(function(e){return b.default.setupEngine({locale:e})})),this.a11yVar("speechRules",(function(t){var n=r(t.split("-"),2),o=n[0],i=n[1];e.document.options.sre.domain=o,e.document.options.sre.style=i})),this.a11yVar("magnification"),this.a11yVar("magnify"),this.a11yVar("treeColoring"),this.a11yVar("infoType"),this.a11yVar("infoRole"),this.a11yVar("infoPrefix"),this.variable("autocollapse"),this.variable("collapsible",(function(t){return e.setCollapsible(t)})),this.variable("inTabOrder",(function(t){return e.setTabOrder(t)})),this.variable("assistiveMml",(function(t){return e.setAssistiveMml(t)}))],items:[this.submenu("Show","Show Math As",[this.command("MathMLcode","MathML Code",(function(){return e.mathmlCode.post()})),this.command("Original","Original Form",(function(){return e.originalText.post()})),this.submenu("Annotation","Annotation")]),this.submenu("Copy","Copy to Clipboard",[this.command("MathMLcode","MathML Code",(function(){return e.copyMathML()})),this.command("Original","Original Form",(function(){return e.copyOriginal()})),this.submenu("Annotation","Annotation")]),this.rule(),this.submenu("Settings","Math Settings",[this.submenu("Renderer","Math Renderer",this.radioGroup("renderer",[["CHTML"],["SVG"]])),this.rule(),this.submenu("ZoomTrigger","Zoom Trigger",[this.command("ZoomNow","Zoom Once Now",(function(){return e.zoom(null,"",e.menu.mathItem)})),this.rule(),this.radioGroup("zoom",[["Click"],["DoubleClick","Double-Click"],["NoZoom","No Zoom"]]),this.rule(),this.label("TriggerRequires","Trigger Requires:"),this.checkbox(S?"Option":"Alt",S?"Option":"Alt","alt"),this.checkbox("Command","Command","cmd",{hidden:!S}),this.checkbox("Control","Control","ctrl",{hiddne:S}),this.checkbox("Shift","Shift","shift")]),this.submenu("ZoomFactor","Zoom Factor",this.radioGroup("zscale",[["150%"],["175%"],["200%"],["250%"],["300%"],["400%"]])),this.rule(),this.command("Scale","Scale All Math...",(function(){return e.scaleAllMath()})),this.rule(),this.checkbox("texHints","Add TeX hints to MathML","texHints"),this.checkbox("semantics","Add original as annotation","semantics"),this.rule(),this.command("Reset","Reset to defaults",(function(){return e.resetDefaults()}))]),this.submenu("Accessibility","Accessibility",[this.checkbox("Activate","Activate","explorer"),this.submenu("Speech","Speech",[this.checkbox("Speech","Speech Output","speech"),this.checkbox("Subtitles","Speech Subtitles","subtitles"),this.checkbox("Braille","Braille Output","braille"),this.checkbox("View Braille","Braille Subtitles","viewBraille"),this.rule(),this.submenu("A11yLanguage","Language"),this.rule(),this.submenu("Mathspeak","Mathspeak Rules",this.radioGroup("speechRules",[["mathspeak-default","Verbose"],["mathspeak-brief","Brief"],["mathspeak-sbrief","Superbrief"]])),this.submenu("Clearspeak","Clearspeak Rules",this.radioGroup("speechRules",[["clearspeak-default","Auto"]])),this.submenu("ChromeVox","ChromeVox Rules",this.radioGroup("speechRules",[["chromevox-default","Standard"],["chromevox-alternative","Alternative"]]))]),this.submenu("Highlight","Highlight",[this.submenu("Background","Background",this.radioGroup("backgroundColor",[["Blue"],["Red"],["Green"],["Yellow"],["Cyan"],["Magenta"],["White"],["Black"]])),{type:"slider",variable:"backgroundOpacity",content:" "},this.submenu("Foreground","Foreground",this.radioGroup("foregroundColor",[["Black"],["White"],["Magenta"],["Cyan"],["Yellow"],["Green"],["Red"],["Blue"]])),{type:"slider",variable:"foregroundOpacity",content:" "},this.rule(),this.radioGroup("highlight",[["None"],["Hover"],["Flame"]]),this.rule(),this.checkbox("TreeColoring","Tree Coloring","treeColoring")]),this.submenu("Magnification","Magnification",[this.radioGroup("magnification",[["None"],["Keyboard"],["Mouse"]]),this.rule(),this.radioGroup("magnify",[["200%"],["300%"],["400%"],["500%"]])]),this.submenu("Semantic Info","Semantic Info",[this.checkbox("Type","Type","infoType"),this.checkbox("Role","Role","infoRole"),this.checkbox("Prefix","Prefix","infoPrefix")],!0),this.rule(),this.checkbox("Collapsible","Collapsible Math","collapsible"),this.checkbox("AutoCollapse","Auto Collapse","autocollapse",{disabled:!0}),this.rule(),this.checkbox("InTabOrder","Include in Tab Order","inTabOrder"),this.checkbox("AssistiveMml","Include Hidden MathML","assistiveMml")]),this.submenu("Language","Language"),this.rule(),this.command("About","About MathJax",(function(){return e.about.post()})),this.command("Help","MathJax Help",(function(){return e.help.post()}))]});var n=this.menu;this.about.attachMenu(n),this.help.attachMenu(n),this.originalText.attachMenu(n),this.annotationText.attachMenu(n),this.mathmlCode.attachMenu(n),this.zoomBox.attachMenu(n),this.checkLoadableItems(),this.enableExplorerItems(this.settings.explorer),n.showAnnotation=this.annotationText,n.copyAnnotation=this.copyAnnotation.bind(this),n.annotationTypes=this.options.annotationTypes,g.CssStyles.addInfoStyles(this.document.document),g.CssStyles.addMenuStyles(this.document.document)},e.prototype.checkLoadableItems=function(){var e,t;if(y&&y._&&y.loader&&y.startup)!this.settings.collapsible||y._.a11y&&y._.a11y.complexity||this.loadA11y("complexity"),!this.settings.explorer||y._.a11y&&y._.a11y.explorer||this.loadA11y("explorer"),!this.settings.assistiveMml||y._.a11y&&y._.a11y["assistive-mml"]||this.loadA11y("assistive-mml");else{var n=this.menu;try{for(var r=o(Object.keys(this.jax)),i=r.next();!i.done;i=r.next()){var s=i.value;this.jax[s]||n.findID("Settings","Renderer",s).disable()}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}n.findID("Accessibility","Activate").disable(),n.findID("Accessibility","AutoCollapse").disable(),n.findID("Accessibility","Collapsible").disable()}},e.prototype.enableExplorerItems=function(e){var t,n,r=this.menu.findID("Accessibility","Activate").menu;try{for(var i=o(r.items.slice(1)),s=i.next();!s.done;s=i.next()){var a=s.value;if(a instanceof m.Rule)break;e?a.enable():a.disable()}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.mergeUserSettings=function(){try{var t=localStorage.getItem(e.MENU_STORAGE);if(!t)return;Object.assign(this.settings,JSON.parse(t)),this.setA11y(this.settings)}catch(e){console.log("MathJax localStorage error: "+e.message)}},e.prototype.saveUserSettings=function(){var t,n,r={};try{for(var i=o(Object.keys(this.settings)),s=i.next();!s.done;s=i.next()){var a=s.value;this.settings[a]!==this.defaultSettings[a]&&(r[a]=this.settings[a])}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}try{Object.keys(r).length?localStorage.setItem(e.MENU_STORAGE,JSON.stringify(r)):localStorage.removeItem(e.MENU_STORAGE)}catch(e){console.log("MathJax localStorage error: "+e.message)}},e.prototype.setA11y=function(e){y._.a11y&&y._.a11y.explorer&&y._.a11y.explorer_ts.setA11yOptions(this.document,e)},e.prototype.getA11y=function(e){if(y._.a11y&&y._.a11y.explorer)return void 0!==this.document.options.a11y[e]?this.document.options.a11y[e]:this.document.options.sre[e]},e.prototype.applySettings=function(){this.setTabOrder(this.settings.inTabOrder),this.document.options.enableAssistiveMml=this.settings.assistiveMml,this.document.outputJax.options.scale=parseFloat(this.settings.scale),this.settings.renderer!==this.defaultSettings.renderer&&this.setRenderer(this.settings.renderer)},e.prototype.setScale=function(e){this.document.outputJax.options.scale=parseFloat(e),this.document.rerender()},e.prototype.setRenderer=function(e){var t=this;if(this.jax[e])this.setOutputJax(e);else{var n=e.toLowerCase();this.loadComponent("output/"+n,(function(){var r=y.startup;n in r.constructors&&(r.useOutput(n,!0),r.output=r.getOutputJax(),t.jax[e]=r.output,t.setOutputJax(e))}))}},e.prototype.setOutputJax=function(e){this.jax[e].setAdaptor(this.document.adaptor),this.document.outputJax=this.jax[e],this.rerender()},e.prototype.setTabOrder=function(e){this.menu.store.inTaborder(e)},e.prototype.setAssistiveMml=function(e){this.document.options.enableAssistiveMml=e,!e||y._.a11y&&y._.a11y["assistive-mml"]?this.rerender():this.loadA11y("assistive-mml")},e.prototype.setExplorer=function(e){this.enableExplorerItems(e),this.document.options.enableExplorer=e,!e||y._.a11y&&y._.a11y.explorer?this.rerender(this.settings.collapsible?a.STATE.RERENDER:a.STATE.COMPILED):this.loadA11y("explorer")},e.prototype.setCollapsible=function(e){this.document.options.enableComplexity=e,!e||y._.a11y&&y._.a11y.complexity?this.rerender(a.STATE.COMPILED):this.loadA11y("complexity")},e.prototype.scaleAllMath=function(){var e=(100*parseFloat(this.settings.scale)).toFixed(1).replace(/.0$/,""),t=prompt("Scale all mathematics (compared to surrounding text) by",e+"%");if(t)if(t.match(/^\s*\d+(\.\d*)?\s*%?\s*$/)){var n=parseFloat(t)/100;n?this.menu.pool.lookup("scale").setValue(String(n)):alert("The scale should not be zero")}else alert("The scale should be a percentage (e.g., 120%)")},e.prototype.resetDefaults=function(){var t,n;e.loading++;var r=this.menu.pool,i=this.defaultSettings;try{for(var s=o(Object.keys(this.settings)),c=s.next();!c.done;c=s.next()){var l=c.value,u=r.lookup(l);if(u){u.setValue(i[l]);var d=u.items[0];d&&d.executeCallbacks_()}else this.settings[l]=i[l]}}catch(e){t={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}e.loading--,this.rerender(a.STATE.COMPILED)},e.prototype.checkComponent=function(t){var n=e.loadingPromises.get(t);n&&s.mathjax.retryAfter(n)},e.prototype.loadComponent=function(t,n){if(!e.loadingPromises.has(t)){var r=y.loader;if(r){e.loading++;var o=r.load(t).then((function(){e.loading--,e.loadingPromises.delete(t),n(),0===e.loading&&e._loadingPromise&&(e._loadingPromise=null,e._loadingOK())})).catch((function(t){e._loadingPromise?(e._loadingPromise=null,e._loadingFailed(t)):console.log(t)}));e.loadingPromises.set(t,o)}}},e.prototype.loadA11y=function(t){var n=this,r=!a.STATE.ENRICHED;this.loadComponent("a11y/"+t,(function(){var o=y.startup;s.mathjax.handlers.unregister(o.handler),o.handler=o.getHandler(),s.mathjax.handlers.register(o.handler);var i=n.document;n.document=o.document=o.getDocument(),n.document.menu=n,n.document.outputJax.reset(),n.transferMathList(i),n.document.processed=i.processed,e._loadingPromise||(n.document.outputJax.reset(),n.rerender("complexity"===t||r?a.STATE.COMPILED:a.STATE.TYPESET))}))},e.prototype.transferMathList=function(e){var t,n,r=this.document.options.MathItem;try{for(var i=o(e.math),s=i.next();!s.done;s=i.next()){var a=s.value,c=new r;Object.assign(c,a),this.document.math.push(c)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.formatSource=function(e){return e.trim().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},e.prototype.toMML=function(e){return this.MmlVisitor.visitTree(e.root,e,{texHints:this.settings.texHints,semantics:this.settings.semantics&&"MathML"!==e.inputJax.name})},e.prototype.zoom=function(e,t,n){e&&!this.isZoomEvent(e,t)||(this.menu.mathItem=n,e&&this.menu.post(e),this.zoomBox.post())},e.prototype.isZoomEvent=function(e,t){return this.settings.zoom===t&&(!this.settings.alt||e.altKey)&&(!this.settings.ctrl||e.ctrlKey)&&(!this.settings.cmd||e.metaKey)&&(!this.settings.shift||e.shiftKey)},e.prototype.rerender=function(t){void 0===t&&(t=a.STATE.TYPESET),this.rerenderStart=Math.min(t,this.rerenderStart),e.loading||(this.rerenderStart<=a.STATE.COMPILED&&this.document.reset({inputJax:[]}),this.document.rerender(this.rerenderStart),this.rerenderStart=a.STATE.LAST)},e.prototype.copyMathML=function(){this.copyToClipboard(this.toMML(this.menu.mathItem))},e.prototype.copyOriginal=function(){this.copyToClipboard(this.menu.mathItem.math.trim())},e.prototype.copyAnnotation=function(){this.copyToClipboard(this.menu.annotation.trim())},e.prototype.copyToClipboard=function(e){var t=document.createElement("textarea");t.value=e,t.setAttribute("readonly",""),t.style.cssText="height: 1px; width: 1px; padding: 1px; position: absolute; left: -10px",document.body.appendChild(t),t.select();try{document.execCommand("copy")}catch(e){alert("Can't copy to clipboard: "+e.message)}document.body.removeChild(t)},e.prototype.addMenu=function(e){var t=this,n=e.typesetRoot;n.addEventListener("contextmenu",(function(){return t.menu.mathItem=e}),!0),n.addEventListener("keydown",(function(){return t.menu.mathItem=e}),!0),n.addEventListener("click",(function(n){return t.zoom(n,"Click",e)}),!0),n.addEventListener("dblclick",(function(n){return t.zoom(n,"DoubleClick",e)}),!0),this.menu.store.insert(n)},e.prototype.clear=function(){this.menu.store.clear()},e.prototype.variable=function(e,t){var n=this;return{name:e,getter:function(){return n.settings[e]},setter:function(r){n.settings[e]=r,t&&t(r),n.saveUserSettings()}}},e.prototype.a11yVar=function(e,t){var n=this;return{name:e,getter:function(){return n.getA11y(e)},setter:function(r){n.settings[e]=r;var o={};o[e]=r,n.setA11y(o),t&&t(r),n.saveUserSettings()}}},e.prototype.submenu=function(e,t,n,r){var i,s;void 0===n&&(n=[]),void 0===r&&(r=!1);var a=[];try{for(var c=o(n),l=c.next();!l.done;l=c.next()){var u=l.value;Array.isArray(u)?a=a.concat(u):a.push(u)}}catch(e){i={error:e}}finally{try{l&&!l.done&&(s=c.return)&&s.call(c)}finally{if(i)throw i.error}}return{type:"submenu",id:e,content:t,menu:{items:a},disabled:0===a.length||r}},e.prototype.command=function(e,t,n,r){return void 0===r&&(r={}),Object.assign({type:"command",id:e,content:t,action:n},r)},e.prototype.checkbox=function(e,t,n,r){return void 0===r&&(r={}),Object.assign({type:"checkbox",id:e,content:t,variable:n},r)},e.prototype.radioGroup=function(e,t){var n=this;return t.map((function(t){return n.radio(t[0],t[1]||t[0],e)}))},e.prototype.radio=function(e,t,n,r){return void 0===r&&(r={}),Object.assign({type:"radio",id:e,content:t,variable:n},r)},e.prototype.label=function(e,t){return{type:"label",id:e,content:t}},e.prototype.rule=function(){return{type:"rule"}},e.MENU_STORAGE="MathJax-Menu-Settings",e.OPTIONS={settings:{texHints:!0,semantics:!1,zoom:"NoZoom",zscale:"200%",renderer:"CHTML",alt:!1,cmd:!1,ctrl:!1,shift:!1,scale:1,autocollapse:!1,collapsible:!1,inTabOrder:!0,assistiveMml:!0,explorer:!1},jax:{CHTML:null,SVG:null},annotationTypes:(0,l.expandable)({TeX:["TeX","LaTeX","application/x-tex"],StarMath:["StarMath 5.0"],Maple:["Maple"],ContentMathML:["MathML-Content","application/mathml-content+xml"],OpenMath:["OpenMath"]})},e.loading=0,e.loadingPromises=new Map,e._loadingPromise=null,e._loadingOK=null,e._loadingFailed=null,e}();t.Menu=E},9003:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)},s=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s},a=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},c=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MenuHandler=t.MenuMathDocumentMixin=t.MenuMathItemMixin=void 0;var l=n(3184),u=n(4769),d=n(9077),p=n(1062);function h(e){return function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.addMenu=function(e,t){void 0===t&&(t=!1),this.state()>=u.STATE.CONTEXT_MENU||(this.isEscaped||!e.options.enableMenu&&!t||e.menu.addMenu(this),this.state(u.STATE.CONTEXT_MENU))},t.prototype.checkLoading=function(e){e.checkLoading()},t}(e)}function f(e){var t;return t=function(e){function t(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=e.apply(this,a([],s(t),!1))||this;r.menu=new r.options.MenuClass(r,r.options.menuOptions);var o=r.constructor.ProcessBits;return o.has("context-menu")||o.allocate("context-menu"),r.options.MathItem=h(r.options.MathItem),r}return o(t,e),t.prototype.addMenu=function(){var e,t;if(!this.processed.isSet("context-menu")){try{for(var n=c(this.math),r=n.next();!r.done;r=n.next()){r.value.addMenu(this)}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}this.processed.set("context-menu")}return this},t.prototype.checkLoading=function(){this.menu.isLoading&&l.mathjax.retryAfter(this.menu.loadingPromise.catch((function(e){return console.log(e)})));var e=this.menu.settings;return e.collapsible&&(this.options.enableComplexity=!0,this.menu.checkComponent("a11y/complexity")),e.explorer&&(this.options.enableEnrichment=!0,this.options.enableExplorer=!0,this.menu.checkComponent("a11y/explorer")),this},t.prototype.state=function(t,n){return void 0===n&&(n=!1),e.prototype.state.call(this,t,n),t<u.STATE.CONTEXT_MENU&&this.processed.clear("context-menu"),this},t.prototype.updateDocument=function(){return e.prototype.updateDocument.call(this),this.menu.menu.store.sort(),this},t}(e),t.OPTIONS=i(i({enableEnrichment:!0,enableComplexity:!0,enableExplorer:!0,enrichSpeech:"none",enrichError:function(e,t,n){return console.warn("Enrichment Error:",n)}},e.OPTIONS),{MenuClass:p.Menu,menuOptions:p.Menu.OPTIONS,enableMenu:!0,sre:e.OPTIONS.sre||(0,d.expandable)({}),a11y:e.OPTIONS.a11y||(0,d.expandable)({}),renderActions:(0,d.expandable)(i(i({},e.OPTIONS.renderActions),{addMenu:[u.STATE.CONTEXT_MENU],checkLoading:[u.STATE.UNPROCESSED+1]}))}),t}(0,u.newState)("CONTEXT_MENU",170),t.MenuMathItemMixin=h,t.MenuMathDocumentMixin=f,t.MenuHandler=function(e){return e.documentClass=f(e.documentClass),e}},9232:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.MmlVisitor=void 0;var i=n(2433),s=n(9077),a=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.options={texHints:!0,semantics:!1},t.mathItem=null,t}return o(t,e),t.prototype.visitTree=function(e,t,n){return void 0===t&&(t=null),void 0===n&&(n={}),this.mathItem=t,(0,s.userOptions)(this.options,n),this.visitNode(e,"")},t.prototype.visitTeXAtomNode=function(t,n){return this.options.texHints?e.prototype.visitTeXAtomNode.call(this,t,n):t.childNodes[0]&&1===t.childNodes[0].childNodes.length?this.visitNode(t.childNodes[0],n):n+"<mrow"+this.getAttributes(t)+">\n"+this.childNodeMml(t,n+"  ","\n")+n+"</mrow>"},t.prototype.visitMathNode=function(t,n){if(!this.options.semantics||"TeX"!==this.mathItem.inputJax.name)return e.prototype.visitDefault.call(this,t,n);var r=t.childNodes.length&&t.childNodes[0].childNodes.length>1;return n+"<math"+this.getAttributes(t)+">\n"+n+"  <semantics>\n"+(r?n+"    <mrow>\n":"")+this.childNodeMml(t,n+(r?"      ":"    "),"\n")+(r?n+"    </mrow>\n":"")+n+'    <annotation encoding="application/x-tex">'+this.mathItem.math+"</annotation>\n"+n+"  </semantics>\n"+n+"</math>"},t}(i.SerializedMmlVisitor);t.MmlVisitor=a},7309:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.SelectableInfo=void 0;var i=n(6932),s=n(1698),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.addEvents=function(e){var t=this;e.addEventListener("keypress",(function(e){"a"===e.key&&(e.ctrlKey||e.metaKey)&&(t.selectAll(),t.stop(e))}))},t.prototype.selectAll=function(){document.getSelection().selectAllChildren(this.html.querySelector("pre"))},t.prototype.copyToClipboard=function(){this.selectAll();try{document.execCommand("copy")}catch(e){alert("Can't copy to clipboard: "+e.message)}document.getSelection().removeAllRanges()},t.prototype.generateHtml=function(){var t=this;e.prototype.generateHtml.call(this);var n=this.html.querySelector("span."+s.HtmlClasses.INFOSIGNATURE).appendChild(document.createElement("input"));n.type="button",n.value="Copy to Clipboard",n.addEventListener("click",(function(e){return t.copyToClipboard()}))},t}(i.Info);t.SelectableInfo=a},8723:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=MathJax._.components.global.isObject,t.combineConfig=MathJax._.components.global.combineConfig,t.combineDefaults=MathJax._.components.global.combineDefaults,t.combineWithMathJax=MathJax._.components.global.combineWithMathJax,t.MathJax=MathJax._.components.global.MathJax},4769:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.protoItem=MathJax._.core.MathItem.protoItem,t.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,t.STATE=MathJax._.core.MathItem.STATE,t.newState=MathJax._.core.MathItem.newState},2433:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.DATAMJX=MathJax._.core.MmlTree.SerializedMmlVisitor.DATAMJX,t.toEntity=MathJax._.core.MmlTree.SerializedMmlVisitor.toEntity,t.SerializedMmlVisitor=MathJax._.core.MmlTree.SerializedMmlVisitor.SerializedMmlVisitor},3184:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.mathjax=MathJax._.mathjax.mathjax},9077:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=MathJax._.util.Options.isObject,t.APPEND=MathJax._.util.Options.APPEND,t.REMOVE=MathJax._.util.Options.REMOVE,t.OPTIONS=MathJax._.util.Options.OPTIONS,t.Expandable=MathJax._.util.Options.Expandable,t.expandable=MathJax._.util.Options.expandable,t.makeArray=MathJax._.util.Options.makeArray,t.keys=MathJax._.util.Options.keys,t.copy=MathJax._.util.Options.copy,t.insert=MathJax._.util.Options.insert,t.defaultOptions=MathJax._.util.Options.defaultOptions,t.userOptions=MathJax._.util.Options.userOptions,t.selectOptions=MathJax._.util.Options.selectOptions,t.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,t.separateOptions=MathJax._.util.Options.separateOptions,t.lookup=MathJax._.util.Options.lookup},2612:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractEntry=void 0;var i=n(9943),s=n(1698),a=function(e){function t(t,n){var r=e.call(this)||this;return r._menu=t,r._type=n,r.className=s.HtmlClasses.MENUITEM,r.role="menuitem",r.hidden=!1,r}return o(t,e),Object.defineProperty(t.prototype,"menu",{get:function(){return this._menu},set:function(e){this._menu=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),t.prototype.hide=function(){this.hidden=!0,this.menu.generateMenu()},t.prototype.show=function(){this.hidden=!1,this.menu.generateMenu()},t.prototype.isHidden=function(){return this.hidden},t}(i.MenuElement);t.AbstractEntry=a},1341:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractItem=void 0;var s=n(2612),a=n(865),c=n(1698),l=function(e){function t(t,n,r,o){var i=e.call(this,t,n)||this;return i._content=r,i.disabled=!1,i.callbacks=[],i._id=o||r,i}return o(t,e),Object.defineProperty(t.prototype,"content",{get:function(){return this._content},set:function(e){this._content=e,this.generateHtml(),this.menu&&this.menu.generateHtml()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),t.prototype.press=function(){this.disabled||(this.executeAction(),this.executeCallbacks_())},t.prototype.executeAction=function(){},t.prototype.registerCallback=function(e){-1===this.callbacks.indexOf(e)&&this.callbacks.push(e)},t.prototype.unregisterCallback=function(e){var t=this.callbacks.indexOf(e);-1!==t&&this.callbacks.splice(t,1)},t.prototype.mousedown=function(e){this.press(),this.stop(e)},t.prototype.mouseover=function(e){this.focus(),this.stop(e)},t.prototype.mouseout=function(e){this.deactivate(),this.stop(e)},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this);var t=this.html;t.setAttribute("aria-disabled","false"),t.textContent=this.content},t.prototype.activate=function(){this.disabled||this.html.classList.add(c.HtmlClasses.MENUACTIVE)},t.prototype.deactivate=function(){this.html.classList.remove(c.HtmlClasses.MENUACTIVE)},t.prototype.focus=function(){this.menu.focused=this,e.prototype.focus.call(this),this.activate()},t.prototype.unfocus=function(){this.deactivate(),e.prototype.unfocus.call(this)},t.prototype.escape=function(e){a.MenuUtil.close(this)},t.prototype.up=function(e){this.menu.up(e)},t.prototype.down=function(e){this.menu.down(e)},t.prototype.left=function(e){this.menu.left(e)},t.prototype.right=function(e){this.menu.right(e)},t.prototype.space=function(e){this.press()},t.prototype.disable=function(){this.disabled=!0;var e=this.html;e.classList.add(c.HtmlClasses.MENUDISABLED),e.setAttribute("aria-disabled","true")},t.prototype.enable=function(){this.disabled=!1;var e=this.html;e.classList.remove(c.HtmlClasses.MENUDISABLED),e.removeAttribute("aria-disabled")},t.prototype.executeCallbacks_=function(){var e,t;try{for(var n=i(this.callbacks),r=n.next();!r.done;r=n.next()){var o=r.value;try{o(this)}catch(e){a.MenuUtil.error(e,"Callback for menu entry "+this.id+" failed.")}}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},t}(s.AbstractEntry);t.AbstractItem=l},1585:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractMenu=void 0;var s=n(3757),a=n(1341),c=n(1698),l=n(2745),u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.className=c.HtmlClasses.CONTEXTMENU,t.role="menu",t._items=[],t._baseMenu=null,t}return o(t,e),Object.defineProperty(t.prototype,"baseMenu",{get:function(){return this._baseMenu},set:function(e){this._baseMenu=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"items",{get:function(){return this._items},set:function(e){this._items=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"pool",{get:function(){return this.variablePool},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"focused",{get:function(){return this._focused},set:function(e){if(this._focused!==e){this._focused||this.unfocus();var t=this._focused;this._focused=e,t&&t.unfocus()}},enumerable:!1,configurable:!0}),t.prototype.up=function(e){var t=this.items.filter((function(e){return e instanceof a.AbstractItem&&!e.isHidden()}));if(0!==t.length)if(this.focused){var n=t.indexOf(this.focused);-1!==n&&t[n=n?--n:t.length-1].focus()}else t[t.length-1].focus()},t.prototype.down=function(e){var t=this.items.filter((function(e){return e instanceof a.AbstractItem&&!e.isHidden()}));if(0!==t.length)if(this.focused){var n=t.indexOf(this.focused);-1!==n&&t[n=++n===t.length?0:n].focus()}else t[0].focus()},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.generateMenu()},t.prototype.generateMenu=function(){var e,t,n=this.html;n.classList.add(c.HtmlClasses.MENU);try{for(var r=i(this.items),o=r.next();!o.done;o=r.next()){var s=o.value;if(s.isHidden()){var a=s.html;a.parentNode&&a.parentNode.removeChild(a)}else n.appendChild(s.html)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},t.prototype.post=function(t,n){this.variablePool.update(),e.prototype.post.call(this,t,n)},t.prototype.unpostSubmenus=function(){var e,t,n=this.items.filter((function(e){return e instanceof l.Submenu}));try{for(var r=i(n),o=r.next();!o.done;o=r.next()){var s=o.value;s.submenu.unpost(),s!==this.focused&&s.unfocus()}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},t.prototype.unpost=function(){e.prototype.unpost.call(this),this.unpostSubmenus(),this.focused=null},t.prototype.find=function(e){var t,n;try{for(var r=i(this.items),o=r.next();!o.done;o=r.next()){var s=o.value;if("rule"!==s.type){if(s.id===e)return s;if("submenu"===s.type){var a=s.submenu.find(e);if(a)return a}}}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return null},t}(s.AbstractPostable);t.AbstractMenu=u},2817:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractNavigatable=void 0;var r=n(2635),o=n(9674),i=function(){function e(){this.bubble=!1}return e.prototype.bubbleKey=function(){this.bubble=!0},e.prototype.keydown=function(e){switch(e.keyCode){case r.KEY.ESCAPE:this.escape(e);break;case r.KEY.RIGHT:this.right(e);break;case r.KEY.LEFT:this.left(e);break;case r.KEY.UP:this.up(e);break;case r.KEY.DOWN:this.down(e);break;case r.KEY.RETURN:case r.KEY.SPACE:this.space(e);break;default:return}this.bubble?this.bubble=!1:this.stop(e)},e.prototype.escape=function(e){},e.prototype.space=function(e){},e.prototype.left=function(e){},e.prototype.right=function(e){},e.prototype.up=function(e){},e.prototype.down=function(e){},e.prototype.stop=function(e){e&&(e.stopPropagation(),e.preventDefault(),e.cancelBubble=!0)},e.prototype.mousedown=function(e){return this.stop(e)},e.prototype.mouseup=function(e){return this.stop(e)},e.prototype.mouseover=function(e){return this.stop(e)},e.prototype.mouseout=function(e){return this.stop(e)},e.prototype.click=function(e){return this.stop(e)},e.prototype.addEvents=function(e){e.addEventListener(o.MOUSE.DOWN,this.mousedown.bind(this)),e.addEventListener(o.MOUSE.UP,this.mouseup.bind(this)),e.addEventListener(o.MOUSE.OVER,this.mouseover.bind(this)),e.addEventListener(o.MOUSE.OUT,this.mouseout.bind(this)),e.addEventListener(o.MOUSE.CLICK,this.click.bind(this)),e.addEventListener("keydown",this.keydown.bind(this)),e.addEventListener("dragstart",this.stop.bind(this)),e.addEventListener(o.MOUSE.SELECTSTART,this.stop.bind(this)),e.addEventListener("contextmenu",this.stop.bind(this)),e.addEventListener(o.MOUSE.DBLCLICK,this.stop.bind(this))},e}();t.AbstractNavigatable=i},3757:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractPostable=void 0;var i=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.posted=!1,t}return o(t,e),t.prototype.isPosted=function(){return this.posted},t.prototype.post=function(e,t){this.posted||(void 0!==e&&void 0!==t&&this.html.setAttribute("style","left: "+e+"px; top: "+t+"px;"),this.display(),this.posted=!0)},t.prototype.unpost=function(){if(this.posted){var e=this.html;e.parentNode&&e.parentNode.removeChild(e),this.posted=!1}},t}(n(9943).MenuElement);t.AbstractPostable=i},2467:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractVariableItem=void 0;var i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this);var t=this.html;this.span||this.generateSpan(),t.appendChild(this.span),this.update()},t.prototype.register=function(){this.variable.register(this)},t.prototype.unregister=function(){this.variable.unregister(this)},t.prototype.update=function(){this.updateAria(),this.span&&this.updateSpan()},t}(n(1341).AbstractItem);t.AbstractVariableItem=i},725:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.CloseButton=void 0;var i=n(3757),s=n(1698),a=function(e){function t(t){var n=e.call(this)||this;return n.element=t,n.className=s.HtmlClasses.MENUCLOSE,n.role="button",n}return o(t,e),t.prototype.generateHtml=function(){var e=document.createElement("span");e.classList.add(this.className),e.setAttribute("role",this.role),e.setAttribute("tabindex","0");var t=document.createElement("span");t.textContent="\xd7",e.appendChild(t),this.html=e},t.prototype.display=function(){},t.prototype.unpost=function(){e.prototype.unpost.call(this),this.element.unpost()},t.prototype.keydown=function(t){this.bubbleKey(),e.prototype.keydown.call(this,t)},t.prototype.space=function(e){this.unpost(),this.stop(e)},t.prototype.mousedown=function(e){this.unpost(),this.stop(e)},t}(i.AbstractPostable);t.CloseButton=a},9964:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.ContextMenu=void 0;var i=n(1585),s=n(1698),a=n(8525),c=n(6964),l=function(e){function t(t){var n=e.call(this)||this;return n.factory=t,n.id="",n.moving=!1,n._store=new a.MenuStore(n),n.widgets=[],n.variablePool=new c.VariablePool,n}return o(t,e),t.fromJson=function(e,t){var n=t.pool,r=t.items,o=t.id,i=void 0===o?"":o,s=new this(e);s.id=i;var a=e.get("variable");n.forEach((function(t){return a(e,t,s.pool)}));var c=e.get("items")(e,r,s);return s.items=c,s},t.prototype.generateHtml=function(){this.isPosted()&&this.unpost(),e.prototype.generateHtml.call(this),this._frame=document.createElement("div"),this._frame.classList.add(s.HtmlClasses.MENUFRAME);var t="left: 0px; top: 0px; z-index: 200; width: 100%; height: 100%; border: 0px; padding: 0px; margin: 0px;";this._frame.setAttribute("style","position: absolute; "+t);var n=document.createElement("div");n.setAttribute("style","position: fixed; "+t),this._frame.appendChild(n),n.addEventListener("mousedown",function(e){this.unpost(),this.unpostWidgets(),this.stop(e)}.bind(this))},t.prototype.display=function(){document.body.appendChild(this.frame),this.frame.appendChild(this.html),this.focus()},t.prototype.escape=function(e){this.unpost(),this.unpostWidgets()},t.prototype.unpost=function(){if(e.prototype.unpost.call(this),!(this.widgets.length>0)){this.frame.parentNode.removeChild(this.frame);var t=this.store;this.moving||t.insertTaborder(),t.active.focus()}},t.prototype.left=function(e){this.move_(this.store.previous())},t.prototype.right=function(e){this.move_(this.store.next())},Object.defineProperty(t.prototype,"frame",{get:function(){return this._frame},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"store",{get:function(){return this._store},enumerable:!1,configurable:!0}),t.prototype.post=function(t,n){if(void 0!==n)return this.moving||this.store.removeTaborder(),void e.prototype.post.call(this,t,n);var r,o,i,s=t;if(s instanceof Event?(r=s.target,this.stop(s)):r=s,s instanceof MouseEvent&&(o=s.pageX,i=s.pageY,o||i||!s.clientX||(o=s.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,i=s.clientY+document.body.scrollTop+document.documentElement.scrollTop)),!o&&!i&&r){var a=window.pageXOffset||document.documentElement.scrollLeft,c=window.pageYOffset||document.documentElement.scrollTop,l=r.getBoundingClientRect();o=(l.right+l.left)/2+a,i=(l.bottom+l.top)/2+c}this.store.active=r,this.anchor=this.store.active;var u=this.html;o+u.offsetWidth>document.body.offsetWidth-5&&(o=document.body.offsetWidth-u.offsetWidth-5),this.post(o,i)},t.prototype.registerWidget=function(e){this.widgets.push(e)},t.prototype.unregisterWidget=function(e){var t=this.widgets.indexOf(e);t>-1&&this.widgets.splice(t,1),0===this.widgets.length&&this.unpost()},t.prototype.unpostWidgets=function(){this.widgets.forEach((function(e){return e.unpost()}))},t.prototype.toJson=function(){return{type:""}},t.prototype.move_=function(e){this.anchor&&e!==this.anchor&&(this.moving=!0,this.unpost(),this.post(e),this.moving=!1)},t}(i.AbstractMenu);t.ContextMenu=l},6837:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CssStyles=void 0;var r=n(1698);!function(e){function t(e){return"."+(r.HtmlClasses[e]||e)}var n={};n[t("INFOCLOSE")]="{  top:.2em; right:.2em;}",n[t("INFOCONTENT")]="{  overflow:auto; text-align:left; font-size:80%;  padding:.4em .6em; border:1px inset; margin:1em 0px;  max-height:20em; max-width:30em; background-color:#EEEEEE;  white-space:normal;}",n[t("INFO")+t("MOUSEPOST")]="{outline:none;}",n[t("INFO")]='{  position:fixed; left:50%; width:auto; text-align:center;  border:3px outset; padding:1em 2em; background-color:#DDDDDD;  color:black;  cursor:default; font-family:message-box; font-size:120%;  font-style:normal; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 15px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius:15px;               /* Safari and Chrome */  -moz-border-radius:15px;                  /* Firefox */  -khtml-border-radius:15px;                /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color="gray", Positive="true"); /* IE */}';var o={};o[t("MENU")]="{  position:absolute;  background-color:white;  color:black;  width:auto; padding:5px 0px;  border:1px solid #CCCCCC; margin:0; cursor:default;  font: menu; text-align:left; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 5px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius: 5px;             /* Safari and Chrome */  -moz-border-radius: 5px;                /* Firefox */  -khtml-border-radius: 5px;              /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */}",o[t("MENUITEM")]="{  padding: 1px 2em;  background:transparent;}",o[t("MENUARROW")]="{  position:absolute; right:.5em; padding-top:.25em; color:#666666;  font-family: null; font-size: .75em}",o[t("MENUACTIVE")+" "+t("MENUARROW")]="{color:white}",o[t("MENUARROW")+t("RTL")]="{left:.5em; right:auto}",o[t("MENUCHECK")]="{  position:absolute; left:.7em;  font-family: null}",o[t("MENUCHECK")+t("RTL")]="{ right:.7em; left:auto }",o[t("MENURADIOCHECK")]="{  position:absolute; left: .7em;}",o[t("MENURADIOCHECK")+t("RTL")]="{  right: .7em; left:auto}",o[t("MENUINPUTBOX")]="{  padding-left: 1em; right:.5em; color:#666666;  font-family: null;}",o[t("MENUINPUTBOX")+t("RTL")]="{  left: .1em;}",o[t("MENUCOMBOBOX")]="{  left:.1em; padding-bottom:.5em;}",o[t("MENUSLIDER")]="{  left: .1em;}",o[t("SLIDERVALUE")]="{  position:absolute; right:.1em; padding-top:.25em; color:#333333;  font-size: .75em}",o[t("SLIDERBAR")]="{  outline: none; background: #d3d3d3}",o[t("MENULABEL")]="{  padding: 1px 2em 3px 1.33em;  font-style:italic}",o[t("MENURULE")]="{  border-top: 1px solid #DDDDDD;  margin: 4px 3px;}",o[t("MENUDISABLED")]="{  color:GrayText}",o[t("MENUACTIVE")]="{  background-color: #606872;  color: white;}",o[t("MENUDISABLED")+":focus"]="{  background-color: #E8E8E8}",o[t("MENULABEL")+":focus"]="{  background-color: #E8E8E8}",o[t("CONTEXTMENU")+":focus"]="{  outline:none}",o[t("CONTEXTMENU")+" "+t("MENUITEM")+":focus"]="{  outline:none}",o[t("SELECTIONMENU")]="{  position:relative; float:left;  border-bottom: none; -webkit-box-shadow:none; -webkit-border-radius:0px; }",o[t("SELECTIONITEM")]="{  padding-right: 1em;}",o[t("SELECTION")]="{  right: 40%; width:50%; }",o[t("SELECTIONBOX")]="{  padding: 0em; max-height:20em; max-width: none;  background-color:#FFFFFF;}",o[t("SELECTIONDIVIDER")]="{  clear: both; border-top: 2px solid #000000;}",o[t("MENU")+" "+t("MENUCLOSE")]="{  top:-10px; left:-10px}";var i={};i[t("MENUCLOSE")]='{  position:absolute;  cursor:pointer;  display:inline-block;  border:2px solid #AAA;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  font-family: "Courier New", Courier;  font-size:24px;  color:#F0F0F0}',i[t("MENUCLOSE")+" span"]="{  display:block; background-color:#AAA; border:1.5px solid;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  line-height:0;  padding:8px 0 6px     /* may need to be browser-specific */}",i[t("MENUCLOSE")+":hover"]="{  color:white!important;  border:2px solid #CCC!important}",i[t("MENUCLOSE")+":hover span"]="{  background-color:#CCC!important}",i[t("MENUCLOSE")+":hover:focus"]="{  outline:none}";var s=!1,a=!1,c=!1;function l(e){c||(u(i,e),c=!0)}function u(e,t){var n=t||document,r=n.createElement("style");r.type="text/css";var o="";for(var i in e)o+=i,o+=" ",o+=e[i],o+="\n";r.innerHTML=o,n.head.appendChild(r)}e.addMenuStyles=function(e){a||(u(o,e),a=!0,l(e))},e.addInfoStyles=function(e){s||(u(n,e),s=!0,l(e))}}(t.CssStyles||(t.CssStyles={}))},1698:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlAttrs=t.HtmlClasses=void 0;function n(e){return"CtxtMenu_"+e}function r(e){return n(e)}function o(e){return n(e)}t.HtmlClasses={ATTACHED:r("Attached"),CONTEXTMENU:r("ContextMenu"),MENU:r("Menu"),MENUARROW:r("MenuArrow"),MENUACTIVE:r("MenuActive"),MENUCHECK:r("MenuCheck"),MENUCLOSE:r("MenuClose"),MENUCOMBOBOX:r("MenuComboBox"),MENUDISABLED:r("MenuDisabled"),MENUFRAME:r("MenuFrame"),MENUITEM:r("MenuItem"),MENULABEL:r("MenuLabel"),MENURADIOCHECK:r("MenuRadioCheck"),MENUINPUTBOX:r("MenuInputBox"),MENURULE:r("MenuRule"),MENUSLIDER:r("MenuSlider"),MOUSEPOST:r("MousePost"),RTL:r("RTL"),INFO:r("Info"),INFOCLOSE:r("InfoClose"),INFOCONTENT:r("InfoContent"),INFOSIGNATURE:r("InfoSignature"),INFOTITLE:r("InfoTitle"),SLIDERVALUE:r("SliderValue"),SLIDERBAR:r("SliderBar"),SELECTION:r("Selection"),SELECTIONBOX:r("SelectionBox"),SELECTIONMENU:r("SelectionMenu"),SELECTIONDIVIDER:r("SelectionDivider"),SELECTIONITEM:r("SelectionItem")},t.HtmlAttrs={COUNTER:o("Counter"),KEYDOWNFUNC:o("keydownFunc"),CONTEXTMENUFUNC:o("contextmenuFunc"),OLDTAB:o("Oldtabindex"),TOUCHFUNC:o("TouchFunc")}},6932:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Info=void 0;var i=n(725),s=n(1698),a=function(e){function t(t,n,r){var o=e.call(this)||this;return o.title=t,o.signature=r,o.className=s.HtmlClasses.INFO,o.role="dialog",o.contentDiv=o.generateContent(),o.close=o.generateClose(),o.content=n||function(){return""},o}return o(t,e),t.prototype.attachMenu=function(e){this.menu=e},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this);var t=this.html;t.appendChild(this.generateTitle()),t.appendChild(this.contentDiv),t.appendChild(this.generateSignature()),t.appendChild(this.close.html),t.setAttribute("tabindex","0")},t.prototype.post=function(){e.prototype.post.call(this);var t=document.documentElement,n=this.html,r=window.innerHeight||t.clientHeight||t.scrollHeight||0,o=Math.floor(-n.offsetWidth/2),i=Math.floor((r-n.offsetHeight)/3);n.setAttribute("style","margin-left: "+o+"px; top: "+i+"px;"),window.event instanceof MouseEvent&&n.classList.add(s.HtmlClasses.MOUSEPOST),n.focus()},t.prototype.display=function(){this.menu.registerWidget(this),this.contentDiv.innerHTML=this.content();var e=this.menu.html;e.parentNode&&e.parentNode.removeChild(e),this.menu.frame.appendChild(this.html)},t.prototype.click=function(e){},t.prototype.keydown=function(t){this.bubbleKey(),e.prototype.keydown.call(this,t)},t.prototype.escape=function(e){this.unpost()},t.prototype.unpost=function(){e.prototype.unpost.call(this),this.html.classList.remove(s.HtmlClasses.MOUSEPOST),this.menu.unregisterWidget(this)},t.prototype.generateClose=function(){var e=new i.CloseButton(this),t=e.html;return t.classList.add(s.HtmlClasses.INFOCLOSE),t.setAttribute("aria-label","Close Dialog Box"),e},t.prototype.generateTitle=function(){var e=document.createElement("span");return e.innerHTML=this.title,e.classList.add(s.HtmlClasses.INFOTITLE),e},t.prototype.generateContent=function(){var e=document.createElement("div");return e.classList.add(s.HtmlClasses.INFOCONTENT),e.setAttribute("tabindex","0"),e},t.prototype.generateSignature=function(){var e=document.createElement("span");return e.innerHTML=this.signature,e.classList.add(s.HtmlClasses.INFOSIGNATURE),e},t.prototype.toJson=function(){return{type:""}},t}(n(3757).AbstractPostable);t.Info=a},3177:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Checkbox=void 0;var i=n(2467),s=n(865),a=n(1698),c=function(e){function t(t,n,r,o){var i=e.call(this,t,"checkbox",n,o)||this;return i.role="menuitemcheckbox",i.variable=t.pool.lookup(r),i.register(),i}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.variable,t.id)},t.prototype.executeAction=function(){this.variable.setValue(!this.variable.getValue()),s.MenuUtil.close(this)},t.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(a.HtmlClasses.MENUCHECK)},t.prototype.updateAria=function(){this.html.setAttribute("aria-checked",this.variable.getValue()?"true":"false")},t.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()?"":"none"},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractVariableItem);t.Checkbox=c},7253:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Combo=void 0;var i=n(2467),s=n(865),a=n(1698),c=n(2635),l=function(e){function t(t,n,r,o){var i=e.call(this,t,"combobox",n,o)||this;return i.role="combobox",i.inputEvent=!1,i.variable=t.pool.lookup(r),i.register(),i}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.variable,t.id)},t.prototype.executeAction=function(){this.variable.setValue(this.input.value,s.MenuUtil.getActiveElement(this))},t.prototype.space=function(t){e.prototype.space.call(this,t),s.MenuUtil.close(this)},t.prototype.focus=function(){e.prototype.focus.call(this),this.input.focus()},t.prototype.unfocus=function(){e.prototype.unfocus.call(this),this.updateSpan()},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.MENUCOMBOBOX)},t.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.classList.add(a.HtmlClasses.MENUINPUTBOX),this.input=document.createElement("input"),this.input.addEventListener("keydown",this.inputKey.bind(this)),this.input.setAttribute("size","10em"),this.input.setAttribute("type","text"),this.input.setAttribute("tabindex","-1"),this.span.appendChild(this.input)},t.prototype.inputKey=function(e){this.bubbleKey(),this.inputEvent=!0},t.prototype.keydown=function(t){if(this.inputEvent&&t.keyCode!==c.KEY.ESCAPE&&t.keyCode!==c.KEY.RETURN)return this.inputEvent=!1,void t.stopPropagation();e.prototype.keydown.call(this,t),t.stopPropagation()},t.prototype.updateAria=function(){},t.prototype.updateSpan=function(){var e;try{e=this.variable.getValue(s.MenuUtil.getActiveElement(this))}catch(t){e=""}this.input.value=e},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractVariableItem);t.Combo=l},9665:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Command=void 0;var i=n(1341),s=n(865),a=function(e){function t(t,n,r,o){var i=e.call(this,t,"command",n,o)||this;return i.command=r,i}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.action,t.id)},t.prototype.executeAction=function(){try{this.command(s.MenuUtil.getActiveElement(this))}catch(e){s.MenuUtil.error(e,"Illegal command callback.")}s.MenuUtil.close(this)},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractItem);t.Command=a},1817:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Label=void 0;var i=n(1341),s=n(1698),a=function(e){function t(t,n,r){return e.call(this,t,"label",n,r)||this}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.id)},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.html.classList.add(s.HtmlClasses.MENULABEL)},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractItem);t.Label=a},3348:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Radio=void 0;var i=n(2467),s=n(865),a=n(1698),c=function(e){function t(t,n,r,o){var i=e.call(this,t,"radio",n,o)||this;return i.role="menuitemradio",i.variable=t.pool.lookup(r),i.register(),i}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.variable,t.id)},t.prototype.executeAction=function(){this.variable.setValue(this.id),s.MenuUtil.close(this)},t.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(a.HtmlClasses.MENURADIOCHECK)},t.prototype.updateAria=function(){this.html.setAttribute("aria-checked",this.variable.getValue()===this.id?"true":"false")},t.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()===this.id?"":"none"},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractVariableItem);t.Radio=c},1156:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Rule=void 0;var i=n(2612),s=n(1698),a=function(e){function t(t){var n=e.call(this,t,"rule")||this;return n.className=s.HtmlClasses.MENUITEM,n.role="separator",n}return o(t,e),t.fromJson=function(e,t,n){return new this(n)},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this);var t=this.html;t.classList.add(s.HtmlClasses.MENURULE),t.setAttribute("aria-orientation","vertical")},t.prototype.addEvents=function(e){},t.prototype.toJson=function(){return{type:"rule"}},t}(i.AbstractEntry);t.Rule=a},2129:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Slider=void 0;var i=n(2467),s=n(865),a=n(1698),c=n(2635),l=function(e){function t(t,n,r,o){var i=e.call(this,t,"slider",n,o)||this;return i.role="slider",i.labelId="ctx_slideLabel"+s.MenuUtil.counter(),i.valueId="ctx_slideValue"+s.MenuUtil.counter(),i.inputEvent=!1,i.variable=t.pool.lookup(r),i.register(),i}return o(t,e),t.fromJson=function(e,t,n){return new this(n,t.content,t.variable,t.id)},t.prototype.executeAction=function(){this.variable.setValue(this.input.value,s.MenuUtil.getActiveElement(this)),this.update()},t.prototype.space=function(t){e.prototype.space.call(this,t),s.MenuUtil.close(this)},t.prototype.focus=function(){e.prototype.focus.call(this),this.input.focus()},t.prototype.unfocus=function(){e.prototype.unfocus.call(this),this.updateSpan()},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.MENUSLIDER),this.valueSpan=document.createElement("span"),this.valueSpan.setAttribute("id",this.valueId),this.valueSpan.classList.add(a.HtmlClasses.SLIDERVALUE),this.html.appendChild(this.valueSpan)},t.prototype.generateSpan=function(){this.span=document.createElement("span"),this.labelSpan=document.createElement("span"),this.labelSpan.setAttribute("id",this.labelId),this.labelSpan.appendChild(this.html.childNodes[0]),this.html.appendChild(this.labelSpan),this.input=document.createElement("input"),this.input.setAttribute("type","range"),this.input.setAttribute("min","0"),this.input.setAttribute("max","100"),this.input.setAttribute("aria-valuemin","0"),this.input.setAttribute("aria-valuemax","100"),this.input.setAttribute("aria-labelledby",this.labelId),this.input.addEventListener("keydown",this.inputKey.bind(this)),this.input.addEventListener("input",this.executeAction.bind(this)),this.input.classList.add(a.HtmlClasses.SLIDERBAR),this.span.appendChild(this.input)},t.prototype.inputKey=function(e){this.inputEvent=!0},t.prototype.mousedown=function(e){e.stopPropagation()},t.prototype.mouseup=function(e){event.stopPropagation()},t.prototype.keydown=function(t){var n=t.keyCode;return n===c.KEY.UP||n===c.KEY.DOWN?(t.preventDefault(),void e.prototype.keydown.call(this,t)):this.inputEvent&&n!==c.KEY.ESCAPE&&n!==c.KEY.RETURN?(this.inputEvent=!1,void t.stopPropagation()):(e.prototype.keydown.call(this,t),void t.stopPropagation())},t.prototype.updateAria=function(){var e=this.variable.getValue();e&&this.input&&(this.input.setAttribute("aria-valuenow",e),this.input.setAttribute("aria-valuetext",e+"%"))},t.prototype.updateSpan=function(){var e;try{e=this.variable.getValue(s.MenuUtil.getActiveElement(this)),this.valueSpan.innerHTML=e+"%"}catch(t){e=""}this.input.value=e},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractVariableItem);t.Slider=l},2745:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.Submenu=void 0;var i=n(1341),s=n(1698),a=function(e){function t(t,n,r){var o=e.call(this,t,"submenu",n,r)||this;return o._submenu=null,o}return o(t,e),t.fromJson=function(e,t,n){var r=t.content,o=t.menu,i=new this(n,r,t.id),s=e.get("subMenu")(e,o,i);return i.submenu=s,i},Object.defineProperty(t.prototype,"submenu",{get:function(){return this._submenu},set:function(e){this._submenu=e},enumerable:!1,configurable:!0}),t.prototype.mouseover=function(e){this.focus(),this.stop(e)},t.prototype.mouseout=function(e){this.stop(e)},t.prototype.unfocus=function(){if(this.submenu.isPosted()){if(this.menu.focused!==this)return e.prototype.unfocus.call(this),void this.menu.unpostSubmenus();this.html.setAttribute("tabindex","-1"),this.html.blur()}else e.prototype.unfocus.call(this)},t.prototype.focus=function(){e.prototype.focus.call(this),this.submenu.isPosted()||this.disabled||this.submenu.post()},t.prototype.executeAction=function(){this.submenu.isPosted()?this.submenu.unpost():this.submenu.post()},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this);var t=this.html;this.span=document.createElement("span"),this.span.textContent="\u25ba",this.span.classList.add(s.HtmlClasses.MENUARROW),t.appendChild(this.span),t.setAttribute("aria-haspopup","true")},t.prototype.left=function(t){this.submenu.isPosted()?this.submenu.unpost():e.prototype.left.call(this,t)},t.prototype.right=function(e){this.submenu.isPosted()?this.submenu.down(e):this.submenu.post()},t.prototype.toJson=function(){return{type:""}},t}(i.AbstractItem);t.Submenu=a},2635:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,function(e){e[e.RETURN=13]="RETURN",e[e.ESCAPE=27]="ESCAPE",e[e.SPACE=32]="SPACE",e[e.LEFT=37]="LEFT",e[e.UP=38]="UP",e[e.RIGHT=39]="RIGHT",e[e.DOWN=40]="DOWN"}(t.KEY||(t.KEY={}))},9943:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.MenuElement=void 0;var i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.addAttributes=function(e){for(var t in e)this.html.setAttribute(t,e[t])},Object.defineProperty(t.prototype,"html",{get:function(){return this._html||this.generateHtml(),this._html},set:function(e){this._html=e,this.addEvents(e)},enumerable:!1,configurable:!0}),t.prototype.generateHtml=function(){var e=document.createElement("div");e.classList.add(this.className),e.setAttribute("role",this.role),this.html=e},t.prototype.focus=function(){var e=this.html;e.setAttribute("tabindex","0"),e.focus()},t.prototype.unfocus=function(){var e=this.html;e.hasAttribute("tabindex")&&e.setAttribute("tabindex","-1");try{e.blur()}catch(e){}e.blur()},t}(n(2817).AbstractNavigatable);t.MenuElement=i},8525:function(e,t,n){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MenuStore=void 0;var o=n(865),i=n(1698),s=n(2635),a=function(){function e(e){this.menu=e,this.store=[],this._active=null,this.counter=0,this.attachedClass=i.HtmlClasses.ATTACHED+"_"+o.MenuUtil.counter(),this.taborder=!0,this.attrMap={}}return Object.defineProperty(e.prototype,"active",{get:function(){return this._active},set:function(e){do{if(-1!==this.store.indexOf(e)){this._active=e;break}e=e.parentNode}while(e)},enumerable:!1,configurable:!0}),e.prototype.next=function(){var e=this.store.length;if(0===e)return this.active=null,null;var t=this.store.indexOf(this.active);return t=-1===t?0:t<e-1?t+1:0,this.active=this.store[t],this.active},e.prototype.previous=function(){var e=this.store.length;if(0===e)return this.active=null,null;var t=e-1,n=this.store.indexOf(this.active);return n=-1===n||0===n?t:n-1,this.active=this.store[n],this.active},e.prototype.clear=function(){this.remove(this.store)},e.prototype.insert=function(e){var t,n,o=e instanceof HTMLElement?[e]:e;try{for(var i=r(o),s=i.next();!s.done;s=i.next()){var a=s.value;this.insertElement(a)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}this.sort()},e.prototype.remove=function(e){var t,n,o=e instanceof HTMLElement?[e]:e;try{for(var i=r(o),s=i.next();!s.done;s=i.next()){var a=s.value;this.removeElement(a)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}this.sort()},e.prototype.inTaborder=function(e){this.taborder&&!e&&this.removeTaborder(),!this.taborder&&e&&this.insertTaborder(),this.taborder=e},e.prototype.insertTaborder=function(){this.taborder&&this.insertTaborder_()},e.prototype.removeTaborder=function(){this.taborder&&this.removeTaborder_()},e.prototype.insertElement=function(e){e.classList.contains(this.attachedClass)||(e.classList.add(this.attachedClass),this.taborder&&this.addTabindex(e),this.addEvents(e))},e.prototype.removeElement=function(e){e.classList.contains(this.attachedClass)&&(e.classList.remove(this.attachedClass),this.taborder&&this.removeTabindex(e),this.removeEvents(e))},e.prototype.sort=function(){var e=document.getElementsByClassName(this.attachedClass);this.store=[].slice.call(e)},e.prototype.insertTaborder_=function(){this.store.forEach((function(e){return e.setAttribute("tabindex","0")}))},e.prototype.removeTaborder_=function(){this.store.forEach((function(e){return e.setAttribute("tabindex","-1")}))},e.prototype.addTabindex=function(e){e.hasAttribute("tabindex")&&e.setAttribute(i.HtmlAttrs.OLDTAB,e.getAttribute("tabindex")),e.setAttribute("tabindex","0")},e.prototype.removeTabindex=function(e){e.hasAttribute(i.HtmlAttrs.OLDTAB)?(e.setAttribute("tabindex",e.getAttribute(i.HtmlAttrs.OLDTAB)),e.removeAttribute(i.HtmlAttrs.OLDTAB)):e.removeAttribute("tabindex")},e.prototype.addEvents=function(e){e.hasAttribute(i.HtmlAttrs.COUNTER)||(this.addEvent(e,"contextmenu",this.menu.post.bind(this.menu)),this.addEvent(e,"keydown",this.keydown.bind(this)),e.setAttribute(i.HtmlAttrs.COUNTER,this.counter.toString()),this.counter++)},e.prototype.addEvent=function(e,t,n){var r=i.HtmlAttrs[t.toUpperCase()+"FUNC"];this.attrMap[r+this.counter]=n,e.addEventListener(t,n)},e.prototype.removeEvents=function(e){if(e.hasAttribute(i.HtmlAttrs.COUNTER)){var t=e.getAttribute(i.HtmlAttrs.COUNTER);this.removeEvent(e,"contextmenu",t),this.removeEvent(e,"keydown",t),e.removeAttribute(i.HtmlAttrs.COUNTER)}},e.prototype.removeEvent=function(e,t,n){var r=i.HtmlAttrs[t.toUpperCase()+"FUNC"],o=this.attrMap[r+n];e.removeEventListener(t,o)},e.prototype.keydown=function(e){e.keyCode===s.KEY.SPACE&&(this.menu.post(e),e.preventDefault(),e.stopImmediatePropagation())},e}();t.MenuStore=a},865:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MenuUtil=void 0,function(e){e.close=function(e){var t=e.menu;t.baseMenu?t.baseMenu.unpost():t.unpost()},e.getActiveElement=function(e){var t=e.menu;return(t.baseMenu?t.baseMenu:t).store.active},e.error=function(e,t){console.error("ContextMenu Error: "+t)},e.counter=function(){return t++};var t=0}(t.MenuUtil||(t.MenuUtil={}))},9674:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MOUSE=void 0,t.MOUSE={CLICK:"click",DBLCLICK:"dblclick",DOWN:"mousedown",UP:"mouseup",OVER:"mouseover",OUT:"mouseout",MOVE:"mousemove",SELECTEND:"selectend",SELECTSTART:"selectstart"}},7258:function(e,t,n){var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},o=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s},i=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__spread||function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(o(arguments[t]));return e};Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=void 0;var a=n(9665),c=n(9964),l=n(4842),u=n(3177),d=n(7253),p=n(1817),h=n(3348),f=n(2745),m=n(1156),g=n(2129),b=n(9467),y=n(7365),S=n(8534),E=function(){function e(e){var t=this;void 0===e&&(e=[]),this._initList=[["command",a.Command.fromJson.bind(a.Command)],["checkbox",u.Checkbox.fromJson.bind(u.Checkbox)],["combo",d.Combo.fromJson.bind(d.Combo)],["slider",g.Slider.fromJson.bind(g.Slider)],["label",p.Label.fromJson.bind(p.Label)],["radio",h.Radio.fromJson.bind(h.Radio)],["rule",m.Rule.fromJson.bind(m.Rule)],["submenu",f.Submenu.fromJson.bind(f.Submenu)],["contextMenu",c.ContextMenu.fromJson.bind(c.ContextMenu)],["subMenu",b.SubMenu.fromJson.bind(b.SubMenu)],["variable",l.Variable.fromJson.bind(l.Variable)],["items",this.items.bind(this)],["selectionMenu",y.SelectionMenu.fromJson.bind(y.SelectionMenu)],["selectionBox",y.SelectionBox.fromJson.bind(y.SelectionBox)]],this._factory=new S.ParserFactory(this._initList),e.forEach((function(e){var n=o(e,2),r=n[0],i=n[1];return t.factory.add(r,i)}))}return Object.defineProperty(e.prototype,"factory",{get:function(){return this._factory},enumerable:!1,configurable:!0}),e.prototype.items=function(e,t,n){var r,o,s=[];try{for(var a=i(t),c=a.next();!c.done;c=a.next()){var l=c.value,u=this.parse(l,n);u&&(n.items.push(u),l.disabled&&u.disable(),l.hidden&&s.push(u))}}catch(e){r={error:e}}finally{try{c&&!c.done&&(o=a.return)&&o.call(a)}finally{if(r)throw r.error}}return s.forEach((function(e){return e.hide()})),n.items},e.prototype.parse=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=e.type,i=r(e,["type"]),a=this.factory.get(o);return a?a.apply(void 0,s([this.factory,i],t)):null},e}();t.Parser=E},8534:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ParserFactory=void 0;var n=function(){function e(e){this._parser=new Map(e)}return e.prototype.get=function(e){return this._parser.get(e)},e.prototype.add=function(e,t){this._parser.set(e,t)},e}();t.ParserFactory=n},7365:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionBox=t.SelectionMenu=void 0;var s=n(865),a=n(1698),c=n(1585),l=n(6932),u=function(e){function t(t){var n=e.call(this)||this;return n.anchor=t,n.className=a.HtmlClasses.SELECTIONMENU,n.variablePool=n.anchor.menu.pool,n.baseMenu=n.anchor.menu,n}return o(t,e),t.fromJson=function(e,t,n){var r=t.title,o=t.values,i=t.variable,s=new this(n),a=e.get("label")(e,{content:r||"",id:r||"id"},s),c=e.get("rule")(e,{},s),l=o.map((function(t){return e.get("radio")(e,{content:t,variable:i,id:t},s)})),u=[a,c].concat(l);return s.items=u,s},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.items.forEach((function(e){return e.html.classList.add(a.HtmlClasses.SELECTIONITEM)}))},t.prototype.display=function(){},t.prototype.right=function(e){this.anchor.right(e)},t.prototype.left=function(e){this.anchor.left(e)},t}(c.AbstractMenu);t.SelectionMenu=u;var d=function(e){function t(t,n,r,o){void 0===r&&(r="none"),void 0===o&&(o="vertical");var i=e.call(this,t,null,n)||this;return i.style=r,i.grid=o,i._selections=[],i.prefix="ctxt-selection",i._balanced=!0,i}return o(t,e),t.fromJson=function(e,t,n){var r=t.title,o=t.signature,i=t.selections,s=new this(r,o,t.order,t.grid);s.attachMenu(n);var a=i.map((function(t){return e.get("selectionMenu")(e,t,s)}));return s.selections=a,s},t.prototype.attachMenu=function(e){this.menu=e},Object.defineProperty(t.prototype,"selections",{get:function(){return this._selections},set:function(e){var t=this;this._selections=[],e.forEach((function(e){return t.addSelection(e)}))},enumerable:!1,configurable:!0}),t.prototype.addSelection=function(e){e.anchor=this,this._selections.push(e)},t.prototype.rowDiv=function(e){var t=this,n=document.createElement("div");this.contentDiv.appendChild(n);var r=e.map((function(e){return n.appendChild(e.html),e.html.id||(e.html.id=t.prefix+s.MenuUtil.counter()),e.html.getBoundingClientRect()})),o=r.map((function(e){return e.width})),i=o.reduce((function(e,t){return e+t}),0),c=r.reduce((function(e,t){return Math.max(e,t.height)}),0);return n.classList.add(a.HtmlClasses.SELECTIONDIVIDER),n.setAttribute("style","height: "+c+"px;"),[n,i,c,o]},t.prototype.display=function(){if(e.prototype.display.call(this),this.order(),this.selections.length){for(var t=[],n=0,r=[],o=this.getChunkSize(this.selections.length),s=function(e){var s=a.selections.slice(e,e+o),c=i(a.rowDiv(s),4),l=c[0],u=c[1],d=c[2],p=c[3];t.push(l),n=Math.max(n,u),s.forEach((function(e){return e.html.style.height=d+"px"})),r=a.combineColumn(r,p)},a=this,c=0;c<this.selections.length;c+=o)s(c);this._balanced&&(this.balanceColumn(t,r),n=r.reduce((function(e,t){return e+t}),20)),t.forEach((function(e){return e.style.width=n+"px"}))}},t.prototype.getChunkSize=function(e){switch(this.grid){case"square":return Math.floor(Math.sqrt(e));case"horizontal":return Math.floor(e/t.chunkSize);default:return t.chunkSize}},t.prototype.balanceColumn=function(e,t){e.forEach((function(e){for(var n=Array.from(e.children),r=0,o=void 0;o=n[r];r++)o.style.width=t[r]+"px"}))},t.prototype.combineColumn=function(e,t){for(var n=[],r=0;e[r]||t[r];){if(!e[r]){n=n.concat(t.slice(r));break}if(!t[r]){n=n.concat(e.slice(r));break}n.push(Math.max(e[r],t[r])),r++}return n},t.prototype.left=function(e){var t=this;this.move(e,(function(e){return(0===e?t.selections.length:e)-1}))},t.prototype.right=function(e){var t=this;this.move(e,(function(e){return e===t.selections.length-1?0:e+1}))},t.prototype.generateHtml=function(){e.prototype.generateHtml.call(this),this.html.classList.add(a.HtmlClasses.SELECTION)},t.prototype.generateContent=function(){var t=e.prototype.generateContent.call(this);return t.classList.add(a.HtmlClasses.SELECTIONBOX),t.removeAttribute("tabindex"),t},t.prototype.findSelection=function(e){var t=e.target,n=null;if(t.id&&(n=this.selections.find((function(e){return e.html.id===t.id}))),!n){var r=t.parentElement.id;n=this.selections.find((function(e){return e.html.id===r}))}return n},t.prototype.move=function(e,t){var n=this.findSelection(e);n.focused&&n.focused.unfocus();var r=t(this.selections.indexOf(n));this.selections[r].focus()},t.prototype.order=function(){this.selections.sort(t.orderMethod.get(this.style))},t.prototype.toJson=function(){return{type:""}},t.chunkSize=4,t.orderMethod=new Map([["alphabetical",function(e,t){return e.items[0].content.localeCompare(t.items[0].content)}],["none",function(e,t){return 1}],["decreasing",function(e,t){var n=e.items.length,r=t.items.length;return n<r?1:r<n?-1:0}],["increasing",function(e,t){var n=e.items.length,r=t.items.length;return n<r?-1:r<n?1:0}]]),t}(l.Info);t.SelectionBox=d},9467:function(e,t,n){var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.SubMenu=void 0;var i=function(e){function t(t){var n=e.call(this)||this;return n._anchor=t,n.variablePool=n.anchor.menu.pool,n.setBaseMenu(),n}return o(t,e),t.fromJson=function(e,t,n){var r=t.items,o=new this(n),i=e.get("items")(e,r,o);return o.items=i,o},Object.defineProperty(t.prototype,"anchor",{get:function(){return this._anchor},enumerable:!1,configurable:!0}),t.prototype.post=function(){if(this.anchor.menu.isPosted()){for(var t=this.anchor.html,n=this.html,r=this.baseMenu.frame,o=t.offsetWidth,i=o-2,s=0;t&&t!==r;)i+=t.offsetLeft,s+=t.offsetTop,t=t.parentNode;i+n.offsetWidth>document.body.offsetWidth-5&&(i=Math.max(5,i-o-n.offsetWidth+6)),e.prototype.post.call(this,i,s)}},t.prototype.display=function(){this.baseMenu.frame.appendChild(this.html)},t.prototype.setBaseMenu=function(){var e=this;do{e=e.anchor.menu}while(e instanceof t);this.baseMenu=e},t.prototype.left=function(e){this.focused=null,this.anchor.focus()},t.prototype.toJson=function(){return{type:""}},t}(n(1585).AbstractMenu);t.SubMenu=i},4842:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Variable=void 0;var r=n(865),o=function(){function e(e,t,n){this._name=e,this.getter=t,this.setter=n,this.items=[]}return e.fromJson=function(e,t,n){var r=new this(t.name,t.getter,t.setter);n.insert(r)},Object.defineProperty(e.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),e.prototype.getValue=function(e){try{return this.getter(e)}catch(e){return r.MenuUtil.error(e,"Command of variable "+this.name+" failed."),null}},e.prototype.setValue=function(e,t){try{this.setter(e,t)}catch(e){r.MenuUtil.error(e,"Command of variable "+this.name+" failed.")}this.update()},e.prototype.register=function(e){-1===this.items.indexOf(e)&&this.items.push(e)},e.prototype.unregister=function(e){var t=this.items.indexOf(e);-1!==t&&this.items.splice(t,1)},e.prototype.update=function(){this.items.forEach((function(e){return e.update()}))},e.prototype.registerCallback=function(e){this.items.forEach((function(t){return t.registerCallback(e)}))},e.prototype.unregisterCallback=function(e){this.items.forEach((function(t){return t.unregisterCallback(e)}))},e.prototype.toJson=function(){return{type:"variable",name:this.name,getter:this.getter.toString(),setter:this.setter.toString()}},e}();t.Variable=o},6964:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.VariablePool=void 0;var n=function(){function e(){this.pool={}}return e.prototype.insert=function(e){this.pool[e.name]=e},e.prototype.lookup=function(e){return this.pool[e]},e.prototype.remove=function(e){delete this.pool[e]},e.prototype.update=function(){for(var e in this.pool)this.pool[e].update()},e}();t.VariablePool=n},7086:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractAudioRenderer=void 0;const r=n(4886);t.AbstractAudioRenderer=class{constructor(){this.separator_=" "}setSeparator(e){this.separator_=e}getSeparator(){return"braille"===r.default.getInstance().modality?"":this.separator_}error(e){return null}merge(e){let t="";const n=e.length-1;for(let r,o=0;r=e[o];o++)if(t+=r.speech,o<n){const e=r.attributes.separator;t+=void 0!==e?e:this.getSeparator()}return t}finalize(e){return e}pauseValue(e){let t;switch(e){case"long":t=750;break;case"medium":t=500;break;case"short":t=250;break;default:t=parseInt(e,10)}return Math.floor(t*r.default.getInstance().getRate()/100)}}},9202:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AcssRenderer=void 0;const r=n(4998),o=n(6988),i=n(2599),s=n(9610);class a extends s.MarkupRenderer{markup(e){this.setScaleFunction(-2,2,0,10,0);const t=i.personalityMarkup(e),n=[],r={open:[]};let o=null,s=!1;for(let e,a=0;e=t[a];a++){if(i.isMarkupElement(e)){i.mergeMarkup(r,e);continue}if(i.isPauseElement(e)){s&&(o=i.mergePause(o,e,Math.max));continue}const t='"'+this.merge(e.span)+'"';s=!0,o&&(n.push(this.pause(o)),o=null);const a=this.prosody_(r);n.push(a?"(text ("+a+") "+t+")":t)}return"(exp "+n.join(" ")+")"}error(e){return'(error "'+o.Move.get(e)+'")'}prosodyElement(e,t){switch(t=this.applyScaleFunction(t),e){case r.personalityProps.RATE:return"(richness . "+t+")";case r.personalityProps.PITCH:return"(average-pitch . "+t+")";case r.personalityProps.VOLUME:return"(stress . "+t+")"}return"(value . "+t+")"}pause(e){return"(pause . "+this.pauseValue(e[r.personalityProps.PAUSE])+")"}prosody_(e){const t=e.open,n=[];for(let r,o=0;r=t[o];o++)n.push(this.prosodyElement(r,e[r]));return n.join(" ")}}t.AcssRenderer=a},2599:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isSpanElement=t.isPauseElement=t.isMarkupElement=t.personalityMarkup=t.sortClose=t.mergeMarkup=t.mergePause=void 0;const r=n(1426),o=n(4998),i=n(1930);function s(e,t,n){return(n||function(n,r){return"number"==typeof n&&"number"==typeof r?n+r:"number"==typeof n?r:"number"==typeof r?n:[e,t].sort()[0]}).call(null,e,t)}t.mergePause=function(e,t,n){return e?{pause:s(e.pause,t.pause,n)}:t},t.mergeMarkup=function(e,t){delete e.open,t.close.forEach((t=>delete e[t])),t.open.forEach((n=>e[n]=t[n]));const n=Object.keys(e);e.open=n},t.sortClose=function(e,t){if(e.length<=1)return e;const n=[];for(let r,o=0;r=t[o],e.length;o++)r.close&&r.close.length&&r.close.forEach((function(t){const r=e.indexOf(t);-1!==r&&(n.unshift(t),e.splice(r,1))}));return n};let a={},c=[];function l(e,t){const n=e[e.length-1];if(n){if(h(t)&&h(n)){if(void 0===n.join)return void(n.span=n.span.concat(t.span));const e=n.span.pop(),r=t.span.shift();return n.span.push(e+n.join+r),n.span=n.span.concat(t.span),void(n.join=t.join)}p(t)&&p(n)?n.pause=s(n.pause,t.pause):e.push(t)}else e.push(t)}function u(e,t){e.rate&&(t.rate=e.rate),e.pitch&&(t.pitch=e.pitch),e.volume&&(t.volume=e.volume)}function d(e){return"object"==typeof e&&e.open}function p(e){return"object"==typeof e&&1===Object.keys(e).length&&Object.keys(e)[0]===o.personalityProps.PAUSE}function h(e){const t=Object.keys(e);return"object"==typeof e&&(1===t.length&&"span"===t[0]||2===t.length&&("span"===t[0]&&"join"===t[1]||"span"===t[1]&&"join"===t[0]))}function f(e,t,n,r,a,c=!1){if(c){const c=e[e.length-1];let l;if(c&&(l=c[o.personalityProps.JOIN]),c&&!t.speech&&a&&p(c)){const e=o.personalityProps.PAUSE;c[e]=s(c[e],a[e]),a=null}if(c&&t.speech&&0===Object.keys(n).length&&h(c)){if(void 0!==l){const e=c.span.pop();t=new i.Span(e.speech+l+t.speech,e.attributes)}c.span.push(t),t=new i.Span("",{}),c[o.personalityProps.JOIN]=r}}0!==Object.keys(n).length&&e.push(n),t.speech&&e.push({span:[t],join:r}),a&&e.push(a)}function m(e,t){if(!t)return e;const n={};for(const r of o.personalityPropList){const o=e[r],i=t[r];if(!o&&!i||o&&i&&o===i)continue;const s=o||0;d(n)||(n.open=[],n.close=[]),o||n.close.push(r),i||n.open.push(r),i&&o&&(n.close.push(r),n.open.push(r)),t[r]=s,n[r]=s,a[r]?a[r].push(s):a[r]=[s]}if(d(n)){let e=n.close.slice();for(;e.length>0;){let o=c.pop();const i=(0,r.setdifference)(o,e);if(e=(0,r.setdifference)(e,o),o=i,0!==e.length){if(0!==o.length){n.close=n.close.concat(o),n.open=n.open.concat(o);for(let e,r=0;e=o[r];r++)n[e]=t[e]}}else 0!==o.length&&c.push(o)}c.push(n.open)}return n}t.personalityMarkup=function(e){a={},c=[];let t=[];const n={};for(let r,i=0;r=e[i];i++){let e=null;const i=r.descriptionSpan(),s=r.personality,a=s[o.personalityProps.JOIN];delete s[o.personalityProps.JOIN],void 0!==s[o.personalityProps.PAUSE]&&(e={[o.personalityProps.PAUSE]:s[o.personalityProps.PAUSE]},delete s[o.personalityProps.PAUSE]);f(t,i,m(s,n),a,e,!0)}return t=t.concat(function(){const e=[];for(let t=c.length-1;t>=0;t--){const n=c[t];if(n.length){const t={open:[],close:[]};for(let e=0;e<n.length;e++){const r=n[e];t.close.push(r),t[r]=0}e.push(t)}}return e}()),t=function(e){const t={},n=[];for(let r,o=0;r=e[o];o++){if(!d(r)){l(n,r);continue}if(!r.close||1!==r.close.length||r.open.length){u(r,t),n.push(r);continue}let i=e[o+1];if(!i||h(i)){u(r,t),n.push(r);continue}const s=p(i)?i:null;s&&(i=e[o+2]),i&&d(i)&&i.open[0]===r.close[0]&&!i.close.length&&i[i.open[0]]===t[i.open[0]]?s?(l(n,s),o+=2):o+=1:(u(r,t),n.push(r))}return n}(t),t},t.isMarkupElement=d,t.isPauseElement=p,t.isSpanElement=h},4148:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AuditoryDescription=void 0;const r=n(1058),o=n(1930);class i{constructor({context:e,text:t,userValue:n,annotation:r,attributes:o,personality:i,layout:s}){this.context=e||"",this.text=t||"",this.userValue=n||"",this.annotation=r||"",this.attributes=o||{},this.personality=i||{},this.layout=s||""}static create(e,t={}){return e.text=r.Grammar.getInstance().apply(e.text,t),new i(e)}isEmpty(){return 0===this.context.length&&0===this.text.length&&0===this.userValue.length&&0===this.annotation.length}clone(){let e,t;if(this.personality){e={};for(const t in this.personality)e[t]=this.personality[t]}if(this.attributes){t={};for(const e in this.attributes)t[e]=this.attributes[e]}return new i({context:this.context,text:this.text,userValue:this.userValue,annotation:this.annotation,personality:e,attributes:t,layout:this.layout})}toString(){return'AuditoryDescription(context="'+this.context+'"  text="'+this.text+'"  userValue="'+this.userValue+'"  annotation="'+this.annotation+'")'}descriptionString(){return this.context&&this.text?this.context+" "+this.text:this.context||this.text}descriptionSpan(){return new o.Span(this.descriptionString(),this.attributes)}equals(e){return this.context===e.context&&this.text===e.text&&this.userValue===e.userValue&&this.annotation===e.annotation}}t.AuditoryDescription=i},4253:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isXml=t.registerRenderer=t.error=t.finalize=t.merge=t.markup=t.getSeparator=t.setSeparator=void 0;const r=n(4886),o=n(4998),i=n(9202),s=n(1292),a=n(1674),c=n(8078),l=n(1930),u=n(4115),d=n(6123),p=n(8244),h=n(4708),f=new u.SsmlRenderer,m=new Map([[o.Markup.NONE,new p.StringRenderer],[o.Markup.PUNCTUATION,new a.PunctuationRenderer],[o.Markup.LAYOUT,new s.LayoutRenderer],[o.Markup.ACSS,new i.AcssRenderer],[o.Markup.SABLE,new c.SableRenderer],[o.Markup.VOICEXML,f],[o.Markup.SSML,f],[o.Markup.SSML_STEP,new d.SsmlStepRenderer]]);t.setSeparator=function(e){const t=m.get(r.default.getInstance().markup);t&&t.setSeparator(e)},t.getSeparator=function(){const e=m.get(r.default.getInstance().markup);return e?e.getSeparator():""},t.markup=function(e){const t=m.get(r.default.getInstance().markup);return t?t.markup(e):""},t.merge=function(e){const t=e.map((e=>"string"==typeof e?new l.Span(e,{}):e)),n=m.get(r.default.getInstance().markup);return n?n.merge(t):e.join()},t.finalize=function(e){const t=m.get(r.default.getInstance().markup);return t?t.finalize(e):e},t.error=function(e){const t=m.get(r.default.getInstance().markup);return t?t.error(e):""},t.registerRenderer=function(e,t){m.set(e,t)},t.isXml=function(){return m.get(r.default.getInstance().markup)instanceof h.XmlRenderer}},1292:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.LayoutRenderer=void 0;const r=n(1984),o=n(6671),i=n(4998),s=n(2599),a=n(4708);class c extends a.XmlRenderer{finalize(e){return function(e){l="";const t=o.parseInput(`<all>${e}</all>`);return r.Debugger.getInstance().output(o.formatXml(t.toString())),l=h(t),l}(e)}pause(e){return""}prosodyElement(e,t){return e===i.personalityProps.LAYOUT?`<${t}>`:""}closeTag(e){return`</${e}>`}markup(e){const t=[];let n=[];for(const r of e){if(!r.layout){n.push(r);continue}t.push(this.processContent(n)),n=[];const e=r.layout;e.match(/^begin/)?t.push("<"+e.replace(/^begin/,"")+">"):e.match(/^end/)?t.push("</"+e.replace(/^end/,"")+">"):console.warn("Something went wrong with layout markup: "+e)}return t.push(this.processContent(n)),t.join("")}processContent(e){const t=[],n=s.personalityMarkup(e);for(let e,r=0;e=n[r];r++)e.span?t.push(this.merge(e.span)):s.isPauseElement(e);return t.join("")}}t.LayoutRenderer=c;let l="";const u={TABLE:function(e){let t=b(e);t.forEach((e=>{e.cells=e.cells.slice(1).slice(0,-1),e.width=e.width.slice(1).slice(0,-1)}));const[n,r]=y(t);return t=S(t,r),E(t,n)},CASES:function(e){let t=b(e);t.forEach((e=>{e.cells=e.cells.slice(0,-1),e.width=e.width.slice(0,-1)}));const[n,r]=y(t);return t=S(t,r),E(t,n)},CAYLEY:function(e){let t=b(e);t.forEach((e=>{e.cells=e.cells.slice(1).slice(0,-1),e.width=e.width.slice(1).slice(0,-1),e.sep=e.sep+e.sep}));const[n,r]=y(t),o={lfence:"",rfence:"",cells:r.map((e=>"\u2810"+new Array(e).join("\u2812"))),width:r,height:1,sep:t[0].sep};return t.splice(1,0,o),t=S(t,r),E(t,n)},MATRIX:function(e){let t=b(e);const[n,r]=y(t);return t=S(t,r),E(t,n)},CELL:h,FENCE:h,ROW:h,FRACTION:function(e){const[t,n,,r,o]=Array.from(e.childNodes),i=d(n),s=d(r),a=m(i),c=m(s);let l=Math.max(a,c);const u=t+new Array(l+1).join("\u2812")+o;return l=u.length,`${v(i,l)}\n${u}\n${v(s,l)}`},NUMERATOR:C,DENOMINATOR:C};function d(e){const t=o.tagName(e),n=u[t];return n?n(e):e.textContent}function p(e,t){if(!e||!t)return e+t;const n=f(e),r=f(t),o=n-r;e=o<0?g(e,r,m(e)):e,t=o>0?g(t,n,m(t)):t;const i=e.split(/\r\n|\r|\n/),s=t.split(/\r\n|\r|\n/),a=[];for(let e=0;e<i.length;e++)a.push(i[e]+s[e]);return a.join("\n")}function h(e){let t="";for(const n of Array.from(e.childNodes))t=n.nodeType!==o.NodeType.TEXT_NODE?p(t,d(n)):p(t,n.textContent);return t}function f(e){return e.split(/\r\n|\r|\n/).length}function m(e){return e.split(/\r\n|\r|\n/).reduce(((e,t)=>Math.max(t.length,e)),0)}function g(e,t,n){return e=function(e,t){const n=t-f(e);return e+(n>0?new Array(n+1).join("\n"):"")}(e,t),function(e,t){const n=e.split(/\r\n|\r|\n/),r=[];for(const e of n){const n=t-e.length;r.push(e+(n>0?new Array(n+1).join("\u2800"):""))}return r.join("\n")}(e,n)}function b(e){const t=Array.from(e.childNodes),n=[];for(const e of t)e.nodeType===o.NodeType.ELEMENT_NODE&&n.push(_(e));return n}function y(e){const t=e.reduce(((e,t)=>Math.max(t.height,e)),0),n=[];for(let t=0;t<e[0].width.length;t++)n.push(e.map((e=>e.width[t])).reduce(((e,t)=>Math.max(e,t)),0));return[t,n]}function S(e,t){const n=[];for(const r of e){if(0===r.height)continue;const e=[];for(let n=0;n<r.cells.length;n++)e.push(g(r.cells[n],r.height,t[n]));r.cells=e,n.push(r)}return n}function E(e,t){if(1===t)return e.map((e=>e.lfence+e.cells.join(e.sep)+e.rfence)).join("\n");const n=[];for(const t of e){const e=N(t.sep,t.height);let r=t.cells.shift();for(;t.cells.length;)r=p(r,e),r=p(r,t.cells.shift());r=p(N(t.lfence,t.height),r),r=p(r,N(t.rfence,t.height)),n.push(r),n.push(t.lfence+new Array(m(r)-3).join(t.sep)+t.rfence)}return n.slice(0,-1).join("\n")}function N(e,t){let n="";for(;t;)n+=e+"\n",t--;return n.slice(0,-1)}function A(e){return e.nodeType===o.NodeType.ELEMENT_NODE&&"FENCE"===o.tagName(e)?d(e):""}function _(e){const t=Array.from(e.childNodes),n=A(t[0]),r=A(t[t.length-1]);n&&t.shift(),r&&t.pop();let i="";const s=[];for(const e of t){if(e.nodeType===o.NodeType.TEXT_NODE){i=e.textContent;continue}const t=d(e);s.push(t)}return{lfence:n,rfence:r,sep:i,cells:s,height:s.reduce(((e,t)=>Math.max(f(t),e)),0),width:s.map(m)}}function v(e,t){const n=(t-m(e))/2,[r,o]=Math.floor(n)===n?[n,n]:[Math.floor(n),Math.ceil(n)],i=e.split(/\r\n|\r|\n/),s=[],[a,c]=[new Array(r+1).join("\u2800"),new Array(o+1).join("\u2800")];for(const e of i)s.push(a+e+c);return s.join("\n")}function C(e){const t=e.firstChild,n=h(e);if(t&&t.nodeType===o.NodeType.ELEMENT_NODE){if("ENGLISH"===o.tagName(t))return"\u2830"+n;if("NUMBER"===o.tagName(t))return"\u283c"+n}return n}},9610:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MarkupRenderer=void 0;const r=n(4998),o=n(7086);class i extends o.AbstractAudioRenderer{constructor(){super(...arguments),this.ignoreElements=[r.personalityProps.LAYOUT],this.scaleFunction=null}setScaleFunction(e,t,n,r,o=0){this.scaleFunction=i=>{const s=(i-e)/(t-e),a=n*(1-s)+r*s;return+(Math.round(a+"e+"+o)+"e-"+o)}}applyScaleFunction(e){return this.scaleFunction?this.scaleFunction(e):e}ignoreElement(e){return-1!==this.ignoreElements.indexOf(e)}}t.MarkupRenderer=i},1674:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.PunctuationRenderer=void 0;const r=n(4998),o=n(7086),i=n(2599);class s extends o.AbstractAudioRenderer{markup(e){const t=i.personalityMarkup(e);let n="",o=null,s=!1;for(let e,a=0;e=t[a];a++)i.isMarkupElement(e)||(i.isPauseElement(e)?s&&(o=i.mergePause(o,e,Math.max)):(o&&(n+=this.pause(o[r.personalityProps.PAUSE]),o=null),n+=(s?this.getSeparator():"")+this.merge(e.span),s=!0));return n}pause(e){let t;return t="number"==typeof e?e<=250?"short":e<=500?"medium":"long":e,s.PAUSE_PUNCTUATION.get(t)||""}}t.PunctuationRenderer=s,s.PAUSE_PUNCTUATION=new Map([["short",","],["medium",";"],["long","."]])},8078:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SableRenderer=void 0;const r=n(4998),o=n(4708);class i extends o.XmlRenderer{finalize(e){return'<?xml version="1.0"?><!DOCTYPE SABLE PUBLIC "-//SABLE//DTD SABLE speech mark up//EN" "Sable.v0_2.dtd" []><SABLE>'+this.getSeparator()+e+this.getSeparator()+"</SABLE>"}pause(e){return'<BREAK MSEC="'+this.pauseValue(e[r.personalityProps.PAUSE])+'"/>'}prosodyElement(e,t){switch(t=this.applyScaleFunction(t),e){case r.personalityProps.PITCH:return'<PITCH RANGE="'+t+'%">';case r.personalityProps.RATE:return'<RATE SPEED="'+t+'%">';case r.personalityProps.VOLUME:return'<VOLUME LEVEL="'+t+'%">';default:return"<"+e.toUpperCase()+' VALUE="'+t+'">'}}closeTag(e){return"</"+e.toUpperCase()+">"}}t.SableRenderer=i},1930:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Span=void 0;t.Span=class{constructor(e,t){this.speech=e,this.attributes=t}}},4115:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SsmlRenderer=void 0;const r=n(4886),o=n(4998),i=n(4708);class s extends i.XmlRenderer{finalize(e){return'<?xml version="1.0"?><speak version="1.1" xmlns="http://www.w3.org/2001/10/synthesis"><prosody rate="'+r.default.getInstance().getRate()+'%">'+this.getSeparator()+e+this.getSeparator()+"</prosody></speak>"}pause(e){return'<break time="'+this.pauseValue(e[o.personalityProps.PAUSE])+'ms"/>'}prosodyElement(e,t){const n=(t=Math.floor(this.applyScaleFunction(t)))<0?t.toString():"+"+t.toString();return"<prosody "+e.toLowerCase()+'="'+n+(e===o.personalityProps.VOLUME?">":'%">')}closeTag(e){return"</prosody>"}}t.SsmlRenderer=s},6123:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SsmlStepRenderer=void 0;const r=n(4115);class o extends r.SsmlRenderer{markup(e){return o.MARKS={},super.markup(e)}merge(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n],i=r.attributes.extid;i&&!o.MARKS[i]&&(t.push('<mark name="'+i+'"/>'),o.MARKS[i]=!0),1===r.speech.length&&r.speech.match(/[a-zA-Z]/)?t.push('<say-as interpret-as="'+o.CHARACTER_ATTR+'">'+r.speech+"</say-as>"):t.push(r.speech)}return t.join(this.getSeparator())}}t.SsmlStepRenderer=o,o.CHARACTER_ATTR="character",o.MARKS={}},8244:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.StringRenderer=void 0;const r=n(7086),o=n(2599);class i extends r.AbstractAudioRenderer{markup(e){let t="";const n=(0,o.personalityMarkup)(e).filter((e=>e.span));if(!n.length)return t;const r=n.length-1;for(let e,o=0;e=n[o];o++){if(e.span&&(t+=this.merge(e.span)),o>=r)continue;const n=e.join;t+=void 0===n?this.getSeparator():n}return t}}t.StringRenderer=i},4708:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.XmlRenderer=void 0;const r=n(4886),o=n(2599),i=n(9610);class s extends i.MarkupRenderer{markup(e){this.setScaleFunction(-2,2,-100,100,2);const t=o.personalityMarkup(e),n=[],i=[];for(let e,s=0;e=t[s];s++)if(e.span)n.push(this.merge(e.span));else if(o.isPauseElement(e))n.push(this.pause(e));else{if(e.close.length)for(let t=0;t<e.close.length;t++){const t=i.pop();if(-1===e.close.indexOf(t))throw new r.SREError("Unknown closing markup element: "+t);n.push(this.closeTag(t))}if(e.open.length){o.sortClose(e.open.slice(),t.slice(s+1)).forEach((t=>{n.push(this.prosodyElement(t,e[t])),i.push(t)}))}}return n.join(" ")}}t.XmlRenderer=s},1426:function(e,t){function n(e,t){return e?t?e.filter((e=>t.indexOf(e)<0)):e:[]}Object.defineProperty(t,"__esModule",{value:!0}),t.union=t.setdifference=t.interleaveLists=t.removeEmpty=void 0,t.removeEmpty=function(e){return e.filter((e=>e))},t.interleaveLists=function(e,t){const n=[];for(;e.length||t.length;)e.length&&n.push(e.shift()),t.length&&n.push(t.shift());return n},t.setdifference=n,t.union=function(e,t){return e&&t?e.concat(n(t,e)):e||t||[]}},9501:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.loadScript=t.loadMapsForIE_=t.installWGXpath_=t.loadWGXpath_=t.mapsForIE=t.detectEdge=t.detectIE=void 0;const r=n(4755),o=n(5024);function i(e){c(r.default.WGXpath),s(e)}function s(e,t){let n=t||1;"undefined"==typeof wgxpath&&n<10?setTimeout((function(){s(e,n++)}),200):n>=10||(r.default.wgxpath=wgxpath,e?r.default.wgxpath.install({document:document}):r.default.wgxpath.install(),o.xpath.evaluate=document.evaluate,o.xpath.result=XPathResult,o.xpath.createNSResolver=document.createNSResolver)}function a(){c(r.default.mathmapsIePath)}function c(e){const t=r.default.document.createElement("script");t.type="text/javascript",t.src=e,r.default.document.head?r.default.document.head.appendChild(t):r.default.document.body.appendChild(t)}t.detectIE=function(){return"undefined"!=typeof window&&"ActiveXObject"in window&&"clipboardData"in window&&(a(),i(),!0)},t.detectEdge=function(){var e;return"undefined"!=typeof window&&"MSGestureEvent"in window&&null===(null===(e=window.chrome)||void 0===e?void 0:e.loadTimes)&&(document.evaluate=null,i(!0),!0)},t.mapsForIE=null,t.loadWGXpath_=i,t.installWGXpath_=s,t.loadMapsForIE_=a,t.loadScript=c},1984:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Debugger=void 0;const r=n(4755);class o{constructor(){this.isActive_=!1,this.outputFunction_=console.info,this.stream_=null}static getInstance(){return o.instance=o.instance||new o,o.instance}init(e){e&&this.startDebugFile_(e),this.isActive_=!0}output(...e){this.isActive_&&this.output_(e)}generateOutput(e){this.isActive_&&this.output_(e.apply(e,[]))}exit(e=(()=>{})){this.isActive_&&this.stream_&&this.stream_.end("","",e)}startDebugFile_(e){this.stream_=r.default.fs.createWriteStream(e),this.outputFunction_=function(...e){this.stream_.write(e.join(" ")),this.stream_.write("\n")}.bind(this),this.stream_.on("error",function(e){console.info("Invalid log file. Debug information sent to console."),this.outputFunction_=console.info}.bind(this)),this.stream_.on("finish",(function(){console.info("Finalizing debug file.")}))}output_(e){this.outputFunction_.apply(console.info===this.outputFunction_?console:this.outputFunction_,["Speech Rule Engine Debugger:"].concat(e))}}t.Debugger=o},6671:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.serializeXml=t.cloneNode=t.tagName=t.querySelectorAll=t.querySelectorAllByAttrValue=t.querySelectorAllByAttr=t.formatXml=t.createTextNode=t.createElementNS=t.createElement=t.replaceNode=t.NodeType=t.parseInput=t.XML_ENTITIES=t.trimInput_=t.toArray=void 0;const r=n(4886),o=n(4998),i=n(4755),s=n(5024);function a(e){const t=[];for(let n=0,r=e.length;n<r;n++)t.push(e[n]);return t}function c(e){return(e=e.replace(/&nbsp;/g,"\xa0")).replace(/>[ \f\n\r\t\v\u200b]+</g,"><").trim()}function l(e,t){if(!t)return[!1,""];const n=e.match(/^<([^> ]+).*>/),r=t.match(/^<\/([^>]+)>(.*)/);return n&&r&&n[1]===r[1]?[!0,r[2]]:[!1,""]}t.toArray=a,t.trimInput_=c,t.XML_ENTITIES={"&lt;":!0,"&gt;":!0,"&amp;":!0,"&quot;":!0,"&apos;":!0},t.parseInput=function(e){const t=new i.default.xmldom.DOMParser,n=c(e),a=!!n.match(/&(?!lt|gt|amp|quot|apos)\w+;/g);if(!n)throw new Error("Empty input!");try{const e=t.parseFromString(n,a?"text/html":"text/xml");return r.default.getInstance().mode===o.Mode.HTTP?(s.xpath.currentDocument=e,a?e.body.childNodes[0]:e.documentElement):e.documentElement}catch(e){throw new r.SREError("Illegal input: "+e.message)}},function(e){e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE",e[e.NOTATION_NODE=12]="NOTATION_NODE"}(t.NodeType||(t.NodeType={})),t.replaceNode=function(e,t){e.parentNode&&(e.parentNode.insertBefore(t,e),e.parentNode.removeChild(e))},t.createElement=function(e){return i.default.document.createElement(e)},t.createElementNS=function(e,t){return i.default.document.createElementNS(e,t)},t.createTextNode=function(e){return i.default.document.createTextNode(e)},t.formatXml=function(e){let t="",n=/(>)(<)(\/*)/g,r=0,o=(e=e.replace(n,"$1\r\n$2$3")).split("\r\n");for(n=/(\.)*(<)(\/*)/g,o=o.map((e=>e.replace(n,"$1\r\n$2$3").split("\r\n"))).reduce(((e,t)=>e.concat(t)),[]);o.length;){let e=o.shift();if(!e)continue;let n=0;if(e.match(/^<\w[^>/]*>[^>]+$/)){const t=l(e,o[0]);t[0]?t[1]?(e+=o.shift().slice(0,-t[1].length),t[1].trim()&&o.unshift(t[1])):e+=o.shift():n=1}else if(e.match(/^<\/\w/))0!==r&&(r-=1);else if(e.match(/^<\w[^>]*[^/]>.*$/))n=1;else if(e.match(/^<\w[^>]*\/>.+$/)){const t=e.indexOf(">")+1;e.slice(t).trim()&&o.unshift(),e=e.slice(0,t)}else n=0;t+=new Array(r+1).join("  ")+e+"\r\n",r+=n}return t},t.querySelectorAllByAttr=function(e,t){return e.querySelectorAll?a(e.querySelectorAll(`[${t}]`)):s.evalXPath(`.//*[@${t}]`,e)},t.querySelectorAllByAttrValue=function(e,t,n){return e.querySelectorAll?a(e.querySelectorAll(`[${t}="${n}"]`)):s.evalXPath(`.//*[@${t}="${n}"]`,e)},t.querySelectorAll=function(e,t){return e.querySelectorAll?a(e.querySelectorAll(t)):s.evalXPath(`.//${t}`,e)},t.tagName=function(e){return e.tagName.toUpperCase()},t.cloneNode=function(e){return e.cloneNode(!0)},t.serializeXml=function(e){return(new i.default.xmldom.XMLSerializer).serializeToString(e)}},4886:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.EnginePromise=t.SREError=void 0;const r=n(8310),o=n(4998),i=n(1984),s=n(4513);class a extends Error{constructor(e=""){super(),this.message=e,this.name="SRE Error"}}t.SREError=a;class c{constructor(){this.customLoader=null,this.parsers={},this.comparator=null,this.mode=o.Mode.SYNC,this.init=!0,this.delay=!1,this.comparators={},this.domain="mathspeak",this.style=r.DynamicCstr.DEFAULT_VALUES[r.Axis.STYLE],this._defaultLocale=r.DynamicCstr.DEFAULT_VALUES[r.Axis.LOCALE],this.locale=this.defaultLocale,this.subiso="",this.modality=r.DynamicCstr.DEFAULT_VALUES[r.Axis.MODALITY],this.speech=o.Speech.NONE,this.markup=o.Markup.NONE,this.walker="Table",this.structure=!1,this.ruleSets=[],this.strict=!1,this.isIE=!1,this.isEdge=!1,this.rate="100",this.pprint=!1,this.config=!1,this.rules="",this.prune="",this.evaluator=c.defaultEvaluator,this.defaultParser=new r.DynamicCstrParser(r.DynamicCstr.DEFAULT_ORDER),this.parser=this.defaultParser,this.dynamicCstr=r.DynamicCstr.defaultCstr()}set defaultLocale(e){this._defaultLocale=s.Variables.ensureLocale(e,this._defaultLocale)}get defaultLocale(){return this._defaultLocale}static getInstance(){return c.instance=c.instance||new c,c.instance}static defaultEvaluator(e,t){return e}static evaluateNode(e){return c.nodeEvaluator(e)}getRate(){const e=parseInt(this.rate,10);return isNaN(e)?100:e}setDynamicCstr(e){if(this.defaultLocale&&(r.DynamicCstr.DEFAULT_VALUES[r.Axis.LOCALE]=this.defaultLocale),e){const t=Object.keys(e);for(let n=0;n<t.length;n++){const o=t[n];if(-1!==r.DynamicCstr.DEFAULT_ORDER.indexOf(o)){const t=e[o];this[o]=t}}}o.DOMAIN_TO_STYLES[this.domain]=this.style;const t=[this.locale,this.modality,this.domain,this.style].join("."),n=r.DynamicProperties.createProp([r.DynamicCstr.DEFAULT_VALUES[r.Axis.LOCALE]],[r.DynamicCstr.DEFAULT_VALUES[r.Axis.MODALITY]],[r.DynamicCstr.DEFAULT_VALUES[r.Axis.DOMAIN]],[r.DynamicCstr.DEFAULT_VALUES[r.Axis.STYLE]]),i=this.comparators[this.domain],s=this.parsers[this.domain];this.parser=s||this.defaultParser,this.dynamicCstr=this.parser.parse(t),this.dynamicCstr.updateProperties(n.getProperties()),this.comparator=i?i():new r.DefaultComparator(this.dynamicCstr)}configurate(e){this.mode!==o.Mode.HTTP||this.config||(!function(e){const t=document.documentElement.querySelectorAll('script[type="text/x-sre-config"]');for(let n=0,r=t.length;n<r;n++){let r;try{r=t[n].innerHTML;const o=JSON.parse(r);for(const t in o)e[t]=o[t]}catch(e){i.Debugger.getInstance().output("Illegal configuration ",r)}}}(e),this.config=!0),function(e){if("undefined"!=typeof SREfeature)for(const[t,n]of Object.entries(SREfeature))e[t]=n}(e)}setCustomLoader(e){e&&(this.customLoader=e)}}t.default=c,c.BINARY_FEATURES=["strict","structure","pprint"],c.STRING_FEATURES=["markup","style","domain","speech","walker","defaultLocale","locale","delay","modality","rate","rules","subiso","prune"],c.nodeEvaluator=function(e){return[]};class l{static get(e=c.getInstance().locale){return l.promises[e]||Promise.resolve("")}static getall(){return Promise.all(Object.values(l.promises))}}t.EnginePromise=l,l.loaded={},l.promises={}},4998:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.DOMAIN_TO_STYLES=t.Markup=t.Speech=t.personalityPropList=t.personalityProps=t.Mode=void 0,function(e){e.SYNC="sync",e.ASYNC="async",e.HTTP="http"}(t.Mode||(t.Mode={})),function(e){e.PITCH="pitch",e.RATE="rate",e.VOLUME="volume",e.PAUSE="pause",e.JOIN="join",e.LAYOUT="layout"}(n=t.personalityProps||(t.personalityProps={})),t.personalityPropList=[n.PITCH,n.RATE,n.VOLUME,n.PAUSE,n.JOIN],function(e){e.NONE="none",e.SHALLOW="shallow",e.DEEP="deep"}(t.Speech||(t.Speech={})),function(e){e.NONE="none",e.LAYOUT="layout",e.PUNCTUATION="punctuation",e.SSML="ssml",e.SSML_STEP="ssml_step",e.ACSS="acss",e.SABLE="sable",e.VOICEXML="voicexml"}(t.Markup||(t.Markup={})),t.DOMAIN_TO_STYLES={mathspeak:"default",clearspeak:"default"}},985:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.setup=void 0;const o=n(2371),i=n(5659),s=n(9501),a=n(4886),c=n(7129),l=n(4755);t.setup=function(e){return r(this,void 0,void 0,(function*(){const t=a.default.getInstance();"default"!==e.domain||"speech"!==e.modality&&e.modality&&"speech"!==t.modality||(e.domain="mathspeak");const n=n=>{void 0!==e[n]&&(t[n]=e[n])};return n("mode"),t.configurate(e),a.default.BINARY_FEATURES.forEach((n=>{void 0!==e[n]&&(t[n]=!!e[n])})),a.default.STRING_FEATURES.forEach(n),e.json&&(l.default.jsonPath=c.makePath(e.json)),e.xpath&&(l.default.WGXpath=e.xpath),t.setCustomLoader(e.custom),function(e){e.isIE=s.detectIE(),e.isEdge=s.detectEdge()}(t),o.setLocale(),t.setDynamicCstr(),t.init?(a.EnginePromise.promises.init=new Promise(((e,t)=>{setTimeout((()=>{e("init")}),10)})),t.init=!1,a.EnginePromise.get()):t.delay?(t.delay=!1,a.EnginePromise.get()):i.loadLocale()}))}},6988:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Event=t.EventType=t.Move=t.KeyCode=void 0,function(e){e[e.ENTER=13]="ENTER",e[e.ESC=27]="ESC",e[e.SPACE=32]="SPACE",e[e.PAGE_UP=33]="PAGE_UP",e[e.PAGE_DOWN=34]="PAGE_DOWN",e[e.END=35]="END",e[e.HOME=36]="HOME",e[e.LEFT=37]="LEFT",e[e.UP=38]="UP",e[e.RIGHT=39]="RIGHT",e[e.DOWN=40]="DOWN",e[e.TAB=9]="TAB",e[e.LESS=188]="LESS",e[e.GREATER=190]="GREATER",e[e.DASH=189]="DASH",e[e.ZERO=48]="ZERO",e[e.ONE=49]="ONE",e[e.TWO=50]="TWO",e[e.THREE=51]="THREE",e[e.FOUR=52]="FOUR",e[e.FIVE=53]="FIVE",e[e.SIX=54]="SIX",e[e.SEVEN=55]="SEVEN",e[e.EIGHT=56]="EIGHT",e[e.NINE=57]="NINE",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z"}(t.KeyCode||(t.KeyCode={})),t.Move=new Map([[13,"ENTER"],[27,"ESC"],[32,"SPACE"],[33,"PAGE_UP"],[34,"PAGE_DOWN"],[35,"END"],[36,"HOME"],[37,"LEFT"],[38,"UP"],[39,"RIGHT"],[40,"DOWN"],[9,"TAB"],[188,"LESS"],[190,"GREATER"],[189,"DASH"],[48,"ZERO"],[49,"ONE"],[50,"TWO"],[51,"THREE"],[52,"FOUR"],[53,"FIVE"],[54,"SIX"],[55,"SEVEN"],[56,"EIGHT"],[57,"NINE"],[65,"A"],[66,"B"],[67,"C"],[68,"D"],[69,"E"],[70,"F"],[71,"G"],[72,"H"],[73,"I"],[74,"J"],[75,"K"],[76,"L"],[77,"M"],[78,"N"],[79,"O"],[80,"P"],[81,"Q"],[82,"R"],[83,"S"],[84,"T"],[85,"U"],[86,"V"],[87,"W"],[88,"X"],[89,"Y"],[90,"Z"]]),function(e){e.CLICK="click",e.DBLCLICK="dblclick",e.MOUSEDOWN="mousedown",e.MOUSEUP="mouseup",e.MOUSEOVER="mouseover",e.MOUSEOUT="mouseout",e.MOUSEMOVE="mousemove",e.SELECTSTART="selectstart",e.KEYPRESS="keypress",e.KEYDOWN="keydown",e.KEYUP="keyup",e.TOUCHSTART="touchstart",e.TOUCHMOVE="touchmove",e.TOUCHEND="touchend",e.TOUCHCANCEL="touchcancel"}(t.EventType||(t.EventType={}));t.Event=class{constructor(e,t,n){this.src=e,this.type=t,this.callback=n}add(){this.src.addEventListener(this.type,this.callback)}remove(){this.src.removeEventListener(this.type,this.callback)}}},7129:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.localePath=t.makePath=void 0;const r=n(4755);function o(e){return e.match("/$")?e:e+"/"}t.makePath=o,t.localePath=function(e,t="json"){return o(r.default.jsonPath)+e+(t.match(/^\./)?t:"."+t)}},3539:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.KeyProcessor=t.Processor=void 0;const r=n(6988);class o{constructor(e,t){this.name=e,this.process=t.processor,this.postprocess=t.postprocessor||((e,t)=>e),this.processor=this.postprocess?function(e){return this.postprocess(this.process(e),e)}:this.process,this.print=t.print||o.stringify_,this.pprint=t.pprint||this.print}static stringify_(e){return e?e.toString():e}}t.Processor=o,o.LocalState={walker:null,speechGenerator:null,highlighter:null};class i extends o{constructor(e,t){super(e,t),this.key=t.key||i.getKey_}static getKey_(e){return"string"==typeof e?r.KeyCode[e.toUpperCase()]:e}}t.KeyProcessor=i},9615:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.keypress=t.output=t.print=t.process=t.set=void 0;const r=n(4253),o=n(7450),i=n(9009),s=n(4524),a=n(1939),c=n(7317),l=n(144),u=n(907),d=n(8835),p=n(6671),h=n(4886),f=n(4998),m=n(3539),g=n(5024),b=new Map;function y(e){b.set(e.name,e)}function S(e){const t=b.get(e);if(!t)throw new h.SREError("Unknown processor "+e);return t}function E(e,t){const n=S(e);try{return n.processor(t)}catch(e){throw new h.SREError("Processing error for expression "+t)}}function N(e,t){const n=S(e);return h.default.getInstance().pprint?n.pprint(t):n.print(t)}t.set=y,t.process=E,t.print=N,t.output=function(e,t){const n=S(e);try{const e=n.processor(t);return h.default.getInstance().pprint?n.pprint(e):n.print(e)}catch(e){throw new h.SREError("Processing error for expression "+t)}},t.keypress=function(e,t){const n=S(e),r=n instanceof m.KeyProcessor?n.key(t):t,o=n.processor(r);return h.default.getInstance().pprint?n.pprint(o):n.print(o)},y(new m.Processor("semantic",{processor:function(e){const t=p.parseInput(e);return a.xmlTree(t)},postprocessor:function(e,t){const n=h.default.getInstance().speech;if(n===f.Speech.NONE)return e;const o=p.cloneNode(e);let i=l.computeMarkup(o);if(n===f.Speech.SHALLOW)return e.setAttribute("speech",r.finalize(i)),e;const s=g.evalXPath(".//*[@id]",e),a=g.evalXPath(".//*[@id]",o);for(let e,t,n=0;e=s[n],t=a[n];n++)i=l.computeMarkup(t),e.setAttribute("speech",r.finalize(i));return e},pprint:function(e){return p.formatXml(e.toString())}})),y(new m.Processor("speech",{processor:function(e){const t=p.parseInput(e),n=a.xmlTree(t),o=l.computeSpeech(n);return r.finalize(r.markup(o))},pprint:function(e){const t=e.toString();return r.isXml()?p.formatXml(t):t}})),y(new m.Processor("json",{processor:function(e){const t=p.parseInput(e);return a.getTree(t).toJson()},postprocessor:function(e,t){const n=h.default.getInstance().speech;if(n===f.Speech.NONE)return e;const o=p.parseInput(t),i=a.xmlTree(o),s=l.computeMarkup(i);if(n===f.Speech.SHALLOW)return e.stree.speech=r.finalize(s),e;const c=e=>{const t=g.evalXPath(`.//*[@id=${e.id}]`,i)[0],n=l.computeMarkup(t);e.speech=r.finalize(n),e.children&&e.children.forEach(c)};return c(e.stree),e},print:function(e){return JSON.stringify(e)},pprint:function(e){return JSON.stringify(e,null,2)}})),y(new m.Processor("description",{processor:function(e){const t=p.parseInput(e),n=a.xmlTree(t);return l.computeSpeech(n)},print:function(e){return JSON.stringify(e)},pprint:function(e){return JSON.stringify(e,null,2)}})),y(new m.Processor("enriched",{processor:function(e){return o.semanticMathmlSync(e)},postprocessor:function(e,t){const n=d.getSemanticRoot(e);let r;switch(h.default.getInstance().speech){case f.Speech.NONE:break;case f.Speech.SHALLOW:r=c.generator("Adhoc"),r.getSpeech(n,e);break;case f.Speech.DEEP:r=c.generator("Tree"),r.getSpeech(e,e)}return e},pprint:function(e){return p.formatXml(e.toString())}})),y(new m.Processor("walker",{processor:function(e){const t=c.generator("Node");m.Processor.LocalState.speechGenerator=t,t.setOptions({modality:h.default.getInstance().modality,locale:h.default.getInstance().locale,domain:h.default.getInstance().domain,style:h.default.getInstance().style}),m.Processor.LocalState.highlighter=i.highlighter({color:"black"},{color:"white"},{renderer:"NativeMML"});const n=E("enriched",e),r=N("enriched",n);return m.Processor.LocalState.walker=u.walker(h.default.getInstance().walker,n,t,m.Processor.LocalState.highlighter,r),m.Processor.LocalState.walker},print:function(e){return m.Processor.LocalState.walker.speech()}})),y(new m.KeyProcessor("move",{processor:function(e){if(!m.Processor.LocalState.walker)return null;return!1===m.Processor.LocalState.walker.move(e)?r.error(e):m.Processor.LocalState.walker.speech()}})),y(new m.Processor("number",{processor:function(e){const t=parseInt(e,10);return isNaN(t)?"":s.LOCALE.NUMBERS.numberToWords(t)}})),y(new m.Processor("ordinal",{processor:function(e){const t=parseInt(e,10);return isNaN(t)?"":s.LOCALE.NUMBERS.wordOrdinal(t)}})),y(new m.Processor("numericOrdinal",{processor:function(e){const t=parseInt(e,10);return isNaN(t)?"":s.LOCALE.NUMBERS.numericOrdinal(t)}})),y(new m.Processor("vulgar",{processor:function(e){const[t,n]=e.split("/").map((e=>parseInt(e,10)));return isNaN(t)||isNaN(n)?"":E("speech",`<mfrac><mn>${t}</mn><mn>${n}</mn></mfrac>`)}}))},9037:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.localePath=t.exit=t.move=t.walk=t.processFile=t.file=t.vulgar=t.numericOrdinal=t.ordinal=t.number=t.toEnriched=t.toDescription=t.toJson=t.toSemantic=t.toSpeech=t.localeLoader=t.engineReady=t.engineSetup=t.setupEngine=t.version=void 0;const o=n(4886),i=n(985),s=n(4998),a=n(7129),c=n(9615),l=n(4755),u=n(4513),d=n(5659);function p(e){return r(this,void 0,void 0,(function*(){return(0,i.setup)(e)}))}function h(e,t){return c.process(e,t)}function f(e,t,n){switch(o.default.getInstance().mode){case s.Mode.ASYNC:return function(e,t,n){return r(this,void 0,void 0,(function*(){const r=yield l.default.fs.promises.readFile(t,{encoding:"utf8"}),i=c.output(e,r);if(n)try{l.default.fs.promises.writeFile(n,i)}catch(e){throw new o.SREError("Can not write to file: "+n)}return i}))}(e,t,n);case s.Mode.SYNC:return function(e,t,n){const r=function(e){let t;try{t=l.default.fs.readFileSync(e,{encoding:"utf8"})}catch(t){throw new o.SREError("Can not open file: "+e)}return t}(t),i=c.output(e,r);if(n)try{l.default.fs.writeFileSync(n,i)}catch(e){throw new o.SREError("Can not write to file: "+n)}return i}(e,t,n);default:throw new o.SREError(`Can process files in ${o.default.getInstance().mode} mode`)}}t.version=u.Variables.VERSION,t.setupEngine=p,t.engineSetup=function(){const e=["mode"].concat(o.default.STRING_FEATURES,o.default.BINARY_FEATURES),t=o.default.getInstance(),n={};return e.forEach((function(e){n[e]=t[e]})),n.json=l.default.jsonPath,n.xpath=l.default.WGXpath,n.rules=t.ruleSets.slice(),n},t.engineReady=function(){return r(this,void 0,void 0,(function*(){return p({}).then((()=>o.EnginePromise.getall()))}))},t.localeLoader=d.standardLoader,t.toSpeech=function(e){return h("speech",e)},t.toSemantic=function(e){return h("semantic",e)},t.toJson=function(e){return h("json",e)},t.toDescription=function(e){return h("description",e)},t.toEnriched=function(e){return h("enriched",e)},t.number=function(e){return h("number",e)},t.ordinal=function(e){return h("ordinal",e)},t.numericOrdinal=function(e){return h("numericOrdinal",e)},t.vulgar=function(e){return h("vulgar",e)},t.file={},t.file.toSpeech=function(e,t){return f("speech",e,t)},t.file.toSemantic=function(e,t){return f("semantic",e,t)},t.file.toJson=function(e,t){return f("json",e,t)},t.file.toDescription=function(e,t){return f("description",e,t)},t.file.toEnriched=function(e,t){return f("enriched",e,t)},t.processFile=f,t.walk=function(e){return c.output("walker",e)},t.move=function(e){return c.keypress("move",e)},t.exit=function(e){const t=e||0;o.EnginePromise.getall().then((()=>process.exit(t)))},t.localePath=a.localePath,l.default.documentSupported?p({mode:s.Mode.HTTP}).then((()=>p({}))):p({mode:s.Mode.SYNC}).then((()=>p({mode:s.Mode.ASYNC})))},4755:function(__unused_webpack_module,exports,__webpack_require__){var __dirname="/";Object.defineProperty(exports,"__esModule",{value:!0});const variables_1=__webpack_require__(4513);class SystemExternal{static extRequire(library){if("undefined"!=typeof process){const nodeRequire=eval("require");return nodeRequire(library)}return null}}exports.default=SystemExternal,SystemExternal.windowSupported=!("undefined"==typeof window),SystemExternal.documentSupported=SystemExternal.windowSupported&&!(void 0===window.document),SystemExternal.xmldom=SystemExternal.documentSupported?window:SystemExternal.extRequire("xmldom-sre"),SystemExternal.document=SystemExternal.documentSupported?window.document:(new SystemExternal.xmldom.DOMImplementation).createDocument("","",0),SystemExternal.xpath=SystemExternal.documentSupported?document:function(){const e={document:{},XPathResult:{}};return SystemExternal.extRequire("wicked-good-xpath").install(e),e.document.XPathResult=e.XPathResult,e.document}(),SystemExternal.mathmapsIePath="https://cdn.jsdelivr.net/npm/sre-mathmaps-ie@"+variables_1.Variables.VERSION+"mathmaps_ie.js",SystemExternal.commander=SystemExternal.documentSupported?null:SystemExternal.extRequire("commander"),SystemExternal.fs=SystemExternal.documentSupported?null:SystemExternal.extRequire("fs"),SystemExternal.url=variables_1.Variables.url,SystemExternal.jsonPath=(SystemExternal.documentSupported?SystemExternal.url:process.env.SRE_JSON_PATH||__webpack_require__.g.SRE_JSON_PATH||__dirname+"/mathmaps")+"/",SystemExternal.WGXpath=variables_1.Variables.WGXpath,SystemExternal.wgxpath=null},4513:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Variables=void 0;class n{static ensureLocale(e,t){return n.LOCALES.get(e)?e:(console.error(`Locale ${e} does not exist! Using ${n.LOCALES.get(t)} instead.`),t)}}t.Variables=n,n.VERSION="4.0.6",n.LOCALES=new Map([["ca","Catalan"],["da","Danish"],["de","German"],["en","English"],["es","Spanish"],["fr","French"],["hi","Hindi"],["it","Italian"],["nb","Bokm\xe5l"],["nn","Nynorsk"],["sv","Swedish"],["nemeth","Nemeth"]]),n.mathjaxVersion="3.2.1",n.url="https://cdn.jsdelivr.net/npm/speech-rule-engine@"+n.VERSION+"/lib/mathmaps",n.WGXpath="https://cdn.jsdelivr.net/npm/wicked-good-xpath@1.3.0/dist/wgxpath.install.js"},5024:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.updateEvaluator=t.evaluateString=t.evaluateBoolean=t.getLeafNodes=t.evalXPath=t.resolveNameSpace=t.xpath=void 0;const r=n(4886),o=n(4998),i=n(4755);function s(){return"undefined"!=typeof XPathResult}t.xpath={currentDocument:null,evaluate:s()?document.evaluate:i.default.xpath.evaluate,result:s()?XPathResult:i.default.xpath.XPathResult,createNSResolver:s()?document.createNSResolver:i.default.xpath.createNSResolver};const a={xhtml:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",mml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function c(e){return a[e]||null}t.resolveNameSpace=c;class l{constructor(){this.lookupNamespaceURI=c}}function u(e,n,i){return r.default.getInstance().mode!==o.Mode.HTTP||r.default.getInstance().isIE||r.default.getInstance().isEdge?t.xpath.evaluate(e,n,new l,i,null):t.xpath.currentDocument.evaluate(e,n,c,i,null)}function d(e,n){let r;try{r=u(e,n,t.xpath.result.ORDERED_NODE_ITERATOR_TYPE)}catch(e){return[]}const o=[];for(let e=r.iterateNext();e;e=r.iterateNext())o.push(e);return o}t.evalXPath=d,t.getLeafNodes=function(e){return d(".//*[count(*)=0]",e)},t.evaluateBoolean=function(e,n){let r;try{r=u(e,n,t.xpath.result.BOOLEAN_TYPE)}catch(e){return!1}return r.booleanValue},t.evaluateString=function(e,n){let r;try{r=u(e,n,t.xpath.result.STRING_TYPE)}catch(e){return""}return r.stringValue},t.updateEvaluator=function(e){if(r.default.getInstance().mode!==o.Mode.HTTP)return;let n=e;for(;n&&!n.evaluate;)n=n.parentNode;n&&n.evaluate?t.xpath.currentDocument=n:e.ownerDocument&&(t.xpath.currentDocument=e.ownerDocument)}},9341:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractEnrichCase=void 0;t.AbstractEnrichCase=class{constructor(e){this.semantic=e}}},4306:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseBinomial=void 0;const r=n(6671),o=n(9341),i=n(8672),s=n(8171);class a extends o.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){return!e.mathmlTree&&"line"===e.type&&"binomial"===e.role}getMathml(){if(!this.semantic.childNodes.length)return this.mml;const e=this.semantic.childNodes[0];if(this.mml=(0,i.walkTree)(e),this.mml.hasAttribute(s.Attribute.TYPE)){const e=r.createElement("mrow");e.setAttribute(s.Attribute.ADDED,"true"),r.replaceNode(this.mml,e),e.appendChild(this.mml),this.mml=e}return(0,s.setAttributes)(this.mml,this.semantic),this.mml}}t.CaseBinomial=a},8871:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseDoubleScript=void 0;const r=n(6671),o=n(9341),i=n(8672),s=n(8171);class a extends o.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){if(!e.mathmlTree||!e.childNodes.length)return!1;const t=r.tagName(e.mathmlTree),n=e.childNodes[0].role;return"MSUBSUP"===t&&"subsup"===n||"MUNDEROVER"===t&&"underover"===n}getMathml(){const e=this.semantic.childNodes[0],t=e.childNodes[0],n=this.semantic.childNodes[1],r=e.childNodes[1],o=i.walkTree(n),a=i.walkTree(t),c=i.walkTree(r);return(0,s.setAttributes)(this.mml,this.semantic),this.mml.setAttribute(s.Attribute.CHILDREN,(0,s.makeIdList)([t,r,n])),[a,c,o].forEach((e=>i.getInnerNode(e).setAttribute(s.Attribute.PARENT,this.mml.getAttribute(s.Attribute.ID)))),this.mml.setAttribute(s.Attribute.TYPE,e.role),i.addCollapsedAttribute(this.mml,[this.semantic.id,[e.id,t.id,r.id],n.id]),this.mml}}t.CaseDoubleScript=a},928:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseEmbellished=void 0;const r=n(6671),o=n(9444),i=n(9341),s=n(8871),a=n(4308),c=n(439),l=n(8672),u=n(8171);class d extends i.AbstractEnrichCase{constructor(e){super(e),this.fenced=null,this.fencedMml=null,this.fencedMmlNodes=[],this.ofence=null,this.ofenceMml=null,this.ofenceMap={},this.cfence=null,this.cfenceMml=null,this.cfenceMap={},this.parentCleanup=[]}static test(e){return!(!e.mathmlTree||!e.fencePointer||e.mathmlTree.getAttribute("data-semantic-type"))}static makeEmptyNode_(e){const t=r.createElement("mrow"),n=new o.SemanticNode(e);return n.type="empty",n.mathmlTree=t,n}static fencedMap_(e,t){t[e.id]=e.mathmlTree,e.embellished&&d.fencedMap_(e.childNodes[0],t)}getMathml(){this.getFenced_(),this.fencedMml=l.walkTree(this.fenced),this.getFencesMml_(),"empty"!==this.fenced.type||this.fencedMml.parentNode||(this.fencedMml.setAttribute(u.Attribute.ADDED,"true"),this.cfenceMml.parentNode.insertBefore(this.fencedMml,this.cfenceMml)),this.getFencedMml_();return this.rewrite_()}fencedElement(e){return"fenced"===e.type||"matrix"===e.type||"vector"===e.type}getFenced_(){let e=this.semantic;for(;!this.fencedElement(e);)e=e.childNodes[0];this.fenced=e.childNodes[0],this.ofence=e.contentNodes[0],this.cfence=e.contentNodes[1],d.fencedMap_(this.ofence,this.ofenceMap),d.fencedMap_(this.cfence,this.cfenceMap)}getFencedMml_(){let e=this.ofenceMml.nextSibling;for(e=e===this.fencedMml?e:this.fencedMml;e&&e!==this.cfenceMml;)this.fencedMmlNodes.push(e),e=e.nextSibling}getFencesMml_(){let e=this.semantic;const t=Object.keys(this.ofenceMap),n=Object.keys(this.cfenceMap);for(;!(this.ofenceMml&&this.cfenceMml||e===this.fenced);)-1===t.indexOf(e.fencePointer)||this.ofenceMml||(this.ofenceMml=e.mathmlTree),-1===n.indexOf(e.fencePointer)||this.cfenceMml||(this.cfenceMml=e.mathmlTree),e=e.childNodes[0];this.ofenceMml||(this.ofenceMml=this.ofence.mathmlTree),this.cfenceMml||(this.cfenceMml=this.cfence.mathmlTree),this.ofenceMml&&(this.ofenceMml=l.ascendNewNode(this.ofenceMml)),this.cfenceMml&&(this.cfenceMml=l.ascendNewNode(this.cfenceMml))}rewrite_(){let e=this.semantic,t=null;const n=this.introduceNewLayer_();for((0,u.setAttributes)(n,this.fenced.parent);!this.fencedElement(e);){const o=e.mathmlTree,i=this.specialCase_(e,o);if(i)e=i;else{(0,u.setAttributes)(o,e);const t=[];for(let n,r=1;n=e.childNodes[r];r++)t.push(l.walkTree(n));e=e.childNodes[0]}const s=r.createElement("dummy"),a=o.childNodes[0];r.replaceNode(o,s),r.replaceNode(n,o),r.replaceNode(o.childNodes[0],n),r.replaceNode(s,a),t||(t=o)}return l.walkTree(this.ofence),l.walkTree(this.cfence),this.cleanupParents_(),t||n}specialCase_(e,t){const n=r.tagName(t);let o,i=null;if("MSUBSUP"===n?(i=e.childNodes[0],o=s.CaseDoubleScript):"MMULTISCRIPTS"===n&&("superscript"===e.type||"subscript"===e.type?o=a.CaseMultiscripts:"tensor"===e.type&&(o=c.CaseTensor),i=o&&e.childNodes[0]&&"subsup"===e.childNodes[0].role?e.childNodes[0]:e),!i)return null;const l=i.childNodes[0],u=d.makeEmptyNode_(l.id);return i.childNodes[0]=u,t=new o(e).getMathml(),i.childNodes[0]=l,this.parentCleanup.push(t),i.childNodes[0]}introduceNewLayer_(){const e=this.fullFence(this.ofenceMml),t=this.fullFence(this.cfenceMml);let n=r.createElement("mrow");if(r.replaceNode(this.fencedMml,n),this.fencedMmlNodes.forEach((e=>n.appendChild(e))),n.insertBefore(e,this.fencedMml),n.appendChild(t),!n.parentNode){const e=r.createElement("mrow");for(;n.childNodes.length>0;)e.appendChild(n.childNodes[0]);n.appendChild(e),n=e}return n}fullFence(e){const t=this.fencedMml.parentNode;let n=e;for(;n.parentNode&&n.parentNode!==t;)n=n.parentNode;return n}cleanupParents_(){this.parentCleanup.forEach((function(e){const t=e.childNodes[1].getAttribute(u.Attribute.PARENT);e.childNodes[0].setAttribute(u.Attribute.PARENT,t)}))}}t.CaseEmbellished=d},9763:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseLimit=void 0;const r=n(6671),o=n(9341),i=n(8672),s=n(8171);class a extends o.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){if(!e.mathmlTree||!e.childNodes.length)return!1;const t=r.tagName(e.mathmlTree),n=e.type;return("limupper"===n||"limlower"===n)&&("MSUBSUP"===t||"MUNDEROVER"===t)||"limboth"===n&&("MSUB"===t||"MUNDER"===t||"MSUP"===t||"MOVER"===t)}static walkTree_(e){e&&i.walkTree(e)}getMathml(){const e=this.semantic.childNodes;return"limboth"!==this.semantic.type&&this.mml.childNodes.length>=3&&(this.mml=i.introduceNewLayer([this.mml],this.semantic)),(0,s.setAttributes)(this.mml,this.semantic),e[0].mathmlTree||(e[0].mathmlTree=this.semantic.mathmlTree),e.forEach(a.walkTree_),this.mml}}t.CaseLimit=a},7978:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseLine=void 0;const r=n(9341),o=n(8672),i=n(8171);class s extends r.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){return!!e.mathmlTree&&"line"===e.type}getMathml(){return this.semantic.contentNodes.length&&o.walkTree(this.semantic.contentNodes[0]),this.semantic.childNodes.length&&o.walkTree(this.semantic.childNodes[0]),(0,i.setAttributes)(this.mml,this.semantic),this.mml}}t.CaseLine=s},2124:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseMultiindex=void 0;const r=n(6671),o=n(9341),i=n(8672),s=n(8171);class a extends o.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static multiscriptIndex(e){return"punctuated"===e.type&&"dummy"===e.contentNodes[0].role?i.collapsePunctuated(e):(i.walkTree(e),e.id)}static createNone_(e){const t=r.createElement("none");return e&&(0,s.setAttributes)(t,e),t.setAttribute(s.Attribute.ADDED,"true"),t}completeMultiscript(e,t){const n=r.toArray(this.mml.childNodes).slice(1);let o=0;const c=e=>{for(let t,r=0;t=e[r];r++){const e=n[o];if(e&&t===parseInt(i.getInnerNode(e).getAttribute(s.Attribute.ID)))i.getInnerNode(e).setAttribute(s.Attribute.PARENT,this.semantic.id.toString()),o++;else{const n=this.semantic.querySelectorAll((e=>e.id===t));this.mml.insertBefore(a.createNone_(n[0]),e||null)}}};c(e),n[o]&&"MPRESCRIPTS"!==r.tagName(n[o])?this.mml.insertBefore(n[o],r.createElement("mprescripts")):o++,c(t)}}t.CaseMultiindex=a},4308:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseMultiscripts=void 0;const r=n(6671),o=n(7984),i=n(2124),s=n(8672),a=n(8171);class c extends i.CaseMultiindex{static test(e){if(!e.mathmlTree)return!1;return"MMULTISCRIPTS"===r.tagName(e.mathmlTree)&&("superscript"===e.type||"subscript"===e.type)}constructor(e){super(e)}getMathml(){let e,t,n;if((0,a.setAttributes)(this.mml,this.semantic),this.semantic.childNodes[0]&&"subsup"===this.semantic.childNodes[0].role){const r=this.semantic.childNodes[0];e=r.childNodes[0],t=i.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[1]),n=i.CaseMultiindex.multiscriptIndex(r.childNodes[1]);const c=[this.semantic.id,[r.id,e.id,n],t];s.addCollapsedAttribute(this.mml,c),this.mml.setAttribute(a.Attribute.TYPE,r.role),this.completeMultiscript(o.SemanticSkeleton.interleaveIds(n,t),[])}else{e=this.semantic.childNodes[0],t=i.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[1]);const n=[this.semantic.id,e.id,t];s.addCollapsedAttribute(this.mml,n)}const r=o.SemanticSkeleton.collapsedLeafs(n||[],t),c=s.walkTree(e);return s.getInnerNode(c).setAttribute(a.Attribute.PARENT,this.semantic.id.toString()),r.unshift(e.id),this.mml.setAttribute(a.Attribute.CHILDREN,r.join(",")),this.mml}}t.CaseMultiscripts=c},5326:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseProof=void 0;const r=n(9341),o=n(8672),i=n(8171);class s extends r.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){return!!e.mathmlTree&&("inference"===e.type||"premises"===e.type)}getMathml(){return this.semantic.childNodes.length?(this.semantic.contentNodes.forEach((function(e){o.walkTree(e),(0,i.setAttributes)(e.mathmlTree,e)})),this.semantic.childNodes.forEach((function(e){o.walkTree(e)})),(0,i.setAttributes)(this.mml,this.semantic),this.mml.getAttribute("data-semantic-id")===this.mml.getAttribute("data-semantic-parent")&&this.mml.removeAttribute("data-semantic-parent"),this.mml):this.mml}}t.CaseProof=s},6998:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseTable=void 0;const r=n(6671),o=n(9341),i=n(8672),s=n(8171);class a extends o.AbstractEnrichCase{constructor(e){super(e),this.inner=[],this.mml=e.mathmlTree}static test(e){return"matrix"===e.type||"vector"===e.type||"cases"===e.type}getMathml(){const e=i.cloneContentNode(this.semantic.contentNodes[0]),t=this.semantic.contentNodes[1]?i.cloneContentNode(this.semantic.contentNodes[1]):null;if(this.inner=this.semantic.childNodes.map(i.walkTree),this.mml)if("MFENCED"===r.tagName(this.mml)){const n=this.mml.childNodes;this.mml.insertBefore(e,n[0]||null),t&&this.mml.appendChild(t),this.mml=i.rewriteMfenced(this.mml)}else{const n=[e,this.mml];t&&n.push(t),this.mml=i.introduceNewLayer(n,this.semantic)}else this.mml=i.introduceNewLayer([e].concat(this.inner,[t]),this.semantic);return(0,s.setAttributes)(this.mml,this.semantic),this.mml}}t.CaseTable=a},439:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseTensor=void 0;const r=n(7984),o=n(2124),i=n(8672),s=n(8171);class a extends o.CaseMultiindex{static test(e){return!!e.mathmlTree&&"tensor"===e.type}constructor(e){super(e)}getMathml(){i.walkTree(this.semantic.childNodes[0]);const e=o.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[1]),t=o.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[2]),n=o.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[3]),a=o.CaseMultiindex.multiscriptIndex(this.semantic.childNodes[4]);(0,s.setAttributes)(this.mml,this.semantic);const c=[this.semantic.id,this.semantic.childNodes[0].id,e,t,n,a];i.addCollapsedAttribute(this.mml,c);const l=r.SemanticSkeleton.collapsedLeafs(e,t,n,a);return l.unshift(this.semantic.childNodes[0].id),this.mml.setAttribute(s.Attribute.CHILDREN,l.join(",")),this.completeMultiscript(r.SemanticSkeleton.interleaveIds(n,a),r.SemanticSkeleton.interleaveIds(e,t)),this.mml}}t.CaseTensor=a},598:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CaseText=void 0;const r=n(9341),o=n(8672),i=n(8171);class s extends r.AbstractEnrichCase{constructor(e){super(e),this.mml=e.mathmlTree}static test(e){return"punctuated"===e.type&&("text"===e.role||e.contentNodes.every((e=>"dummy"===e.role)))}getMathml(){const e=[],t=o.collapsePunctuated(this.semantic,e);return this.mml=o.introduceNewLayer(e,this.semantic),(0,i.setAttributes)(this.mml,this.semantic),this.mml.removeAttribute(i.Attribute.CONTENT),o.addCollapsedAttribute(this.mml,t),this.mml}}t.CaseText=s},7450:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.prepareMmlString=t.testTranslation__=t.semanticMathml=t.semanticMathmlSync=t.semanticMathmlNode=void 0;const r=n(1984),o=n(6671),i=n(4886),s=n(1939),a=n(8672),c=n(8171);function l(e){const t=o.cloneNode(e),n=s.getTree(t);return a.enrich(t,n)}function u(e){return l(o.parseInput(e))}function d(e){return e.match(/^<math/)||(e="<math>"+e),e.match(/\/math>$/)||(e+="</math>"),e}n(7813),t.semanticMathmlNode=l,t.semanticMathmlSync=u,t.semanticMathml=function(e,t){i.EnginePromise.getall().then((()=>{const n=o.parseInput(e);t(l(n))}))},t.testTranslation__=function(e){r.Debugger.getInstance().init();const t=u(d(e)).toString();return(0,c.removeAttributePrefix)(t),r.Debugger.getInstance().exit(),t},t.prepareMmlString=d},8171:function(e,t){var n;function r(e){return e.map((function(e){return e.id})).join(",")}function o(e,t){const r=[];"mglyph"===t.role&&r.push("image"),t.attributes.href&&r.push("link"),r.length&&e.setAttribute(n.POSTFIX,r.join(" "))}Object.defineProperty(t,"__esModule",{value:!0}),t.addPrefix=t.removeAttributePrefix=t.setPostfix=t.setAttributes=t.makeIdList=t.EnrichAttributes=t.Attribute=t.Prefix=void 0,t.Prefix="data-semantic-",function(e){e.ADDED="data-semantic-added",e.ALTERNATIVE="data-semantic-alternative",e.CHILDREN="data-semantic-children",e.COLLAPSED="data-semantic-collapsed",e.CONTENT="data-semantic-content",e.EMBELLISHED="data-semantic-embellished",e.FENCEPOINTER="data-semantic-fencepointer",e.FONT="data-semantic-font",e.ID="data-semantic-id",e.ANNOTATION="data-semantic-annotation",e.OPERATOR="data-semantic-operator",e.OWNS="data-semantic-owns",e.PARENT="data-semantic-parent",e.POSTFIX="data-semantic-postfix",e.PREFIX="data-semantic-prefix",e.ROLE="data-semantic-role",e.SPEECH="data-semantic-speech",e.STRUCTURE="data-semantic-structure",e.TYPE="data-semantic-type"}(n=t.Attribute||(t.Attribute={})),t.EnrichAttributes=[n.ADDED,n.ALTERNATIVE,n.CHILDREN,n.COLLAPSED,n.CONTENT,n.EMBELLISHED,n.FENCEPOINTER,n.FONT,n.ID,n.ANNOTATION,n.OPERATOR,n.OWNS,n.PARENT,n.POSTFIX,n.PREFIX,n.ROLE,n.SPEECH,n.STRUCTURE,n.TYPE],t.makeIdList=r,t.setAttributes=function(e,i){e.setAttribute(n.TYPE,i.type);const s=i.allAttributes();for(let n,r=0;n=s[r];r++)e.setAttribute(t.Prefix+n[0].toLowerCase(),n[1]);i.childNodes.length&&e.setAttribute(n.CHILDREN,r(i.childNodes)),i.contentNodes.length&&e.setAttribute(n.CONTENT,r(i.contentNodes)),i.parent&&e.setAttribute(n.PARENT,i.parent.id.toString()),o(e,i)},t.setPostfix=o,t.removeAttributePrefix=function(e){return e.toString().replace(new RegExp(t.Prefix,"g"),"")},t.addPrefix=function(e){return t.Prefix+e}},9775:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.factory=t.getCase=void 0,t.getCase=function(e){for(let n,r=0;n=t.factory[r];r++)if(n.test(e))return n.constr(e);return null},t.factory=[]},7813:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(4306),o=n(8871),i=n(928),s=n(9763),a=n(7978),c=n(4308),l=n(5326),u=n(6998),d=n(439),p=n(598);n(9775).factory.push({test:s.CaseLimit.test,constr:e=>new s.CaseLimit(e)},{test:i.CaseEmbellished.test,constr:e=>new i.CaseEmbellished(e)},{test:o.CaseDoubleScript.test,constr:e=>new o.CaseDoubleScript(e)},{test:d.CaseTensor.test,constr:e=>new d.CaseTensor(e)},{test:c.CaseMultiscripts.test,constr:e=>new c.CaseMultiscripts(e)},{test:a.CaseLine.test,constr:e=>new a.CaseLine(e)},{test:r.CaseBinomial.test,constr:e=>new r.CaseBinomial(e)},{test:l.CaseProof.test,constr:e=>new l.CaseProof(e)},{test:u.CaseTable.test,constr:e=>new u.CaseTable(e)},{test:p.CaseText.test,constr:e=>new p.CaseText(e)})},8672:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.printNodeList__=t.collapsePunctuated=t.formattedOutput_=t.formattedOutput=t.getInnerNode=t.setOperatorAttribute_=t.createInvisibleOperator_=t.rewriteMfenced=t.cloneContentNode=t.addCollapsedAttribute=t.parentNode_=t.isIgnorable_=t.unitChild_=t.descendNode_=t.ascendNewNode=t.validLca_=t.pathToRoot_=t.attachedElement_=t.prunePath_=t.mathmlLca_=t.lcaType=t.functionApplication_=t.isDescendant_=t.insertNewChild_=t.mergeChildren_=t.collectChildNodes_=t.collateChildNodes_=t.childrenSubset_=t.moveSemanticAttributes_=t.introduceLayerAboveLca=t.introduceNewLayer=t.walkTree=t.enrich=t.SETTINGS=void 0;const r=n(1984),o=n(6671),i=n(4886),s=n(4020),a=n(2721),c=n(7984),l=n(8901),u=n(8171),d=n(9775);function p(e){const t=(0,d.getCase)(e);let n;if(t)return n=t.getMathml(),x(n);if(1===e.mathml.length)return r.Debugger.getInstance().output("Walktree Case 0"),n=e.mathml[0],u.setAttributes(n,e),e.childNodes.length&&(r.Debugger.getInstance().output("Walktree Case 0.1"),e.childNodes.forEach((function(e){"empty"===e.type&&n.appendChild(p(e))}))),x(n);const o=e.contentNodes.map(w);k(e,o);const i=e.childNodes.map(p),s=c.SemanticSkeleton.combineContentChildren(e,o,i);if(n=e.mathmlTree,null===n)r.Debugger.getInstance().output("Walktree Case 1"),n=h(s,e);else{const e=M(s);r.Debugger.getInstance().output("Walktree Case 2"),e?(r.Debugger.getInstance().output("Walktree Case 2.1"),n=e.parentNode):(r.Debugger.getInstance().output("Walktree Case 2.2"),n=U(n))}return n=D(n),S(n,s,e),u.setAttributes(n,e),x(n)}function h(e,t){const n=v(e);let i=n.node;const s=n.type;if(s!==_.VALID||!l.hasEmptyTag(i))if(r.Debugger.getInstance().output("Walktree Case 1.1"),i=o.createElement("mrow"),s===_.PRUNED)r.Debugger.getInstance().output("Walktree Case 1.1.0"),i=f(i,n.node,e);else if(e[0]){r.Debugger.getInstance().output("Walktree Case 1.1.1");const t=M(e),n=g(t.parentNode,e);o.replaceNode(t,i),n.forEach((function(e){i.appendChild(e)}))}return t.mathmlTree||(t.mathmlTree=i),i}function f(e,t,n){let i=I(t);if(l.hasMathTag(i)){r.Debugger.getInstance().output("Walktree Case 1.1.0.0"),m(i,e),o.toArray(i.childNodes).forEach((function(t){e.appendChild(t)}));const t=e;e=i,i=t}const s=n.indexOf(t);return n[s]=i,o.replaceNode(i,e),e.appendChild(i),n.forEach((function(t){e.appendChild(t)})),e}function m(e,t){for(const n of u.EnrichAttributes)e.hasAttribute(n)&&(t.setAttribute(n,e.getAttribute(n)),e.removeAttribute(n))}function g(e,t){const n=o.toArray(e.childNodes);let r=1/0,i=-1/0;return t.forEach((function(e){const t=n.indexOf(e);-1!==t&&(r=Math.min(r,t),i=Math.max(i,t))})),n.slice(r,i+1)}function b(e,t,n){const r=[];let i=o.toArray(e.childNodes),s=!1;for(;i.length;){const e=i.shift();if(e.hasAttribute(u.Attribute.TYPE)){r.push(e);continue}const t=y(e);0!==t.length&&(1!==t.length?(s?e.setAttribute("AuxiliaryImplicit",!0):s=!0,i=t.concat(i)):r.push(e))}const a=[],c=n.childNodes.map((function(e){return e.mathmlTree}));for(;c.length;){const e=c.pop();if(e){if(-1!==r.indexOf(e))break;-1!==t.indexOf(e)&&a.unshift(e)}}return r.concat(a)}function y(e){const t=[];let n=o.toArray(e.childNodes);for(;n.length;){const e=n.shift();e.nodeType===o.NodeType.ELEMENT_NODE&&(e.hasAttribute(u.Attribute.TYPE)?t.push(e):n=o.toArray(e.childNodes).concat(n))}return t}function S(e,t,n){const r="implicit"===n.role&&a.flags.combine_juxtaposition?b(e,t,n):o.toArray(e.childNodes);if(!r.length)return void t.forEach((function(t){e.appendChild(t)}));let i=0;for(;t.length;){const n=t[0];r[i]===n||A(r[i],n)?(t.shift(),i++):r[i]&&-1===t.indexOf(r[i])?i++:(N(n,e)||E(e,r[i],n),t.shift())}}function E(e,t,n){if(!t)return void e.insertBefore(n,null);let r=t,o=R(r);for(;o&&o.firstChild===r&&!r.hasAttribute("AuxiliaryImplicit")&&o!==e;)r=o,o=R(r);o&&(o.insertBefore(n,r),r.removeAttribute("AuxiliaryImplicit"))}function N(e,t){if(!e)return!1;do{if((e=e.parentNode)===t)return!0}while(e);return!1}function A(e,t){const n=s.functionApplication();if(e&&t&&e.textContent&&t.textContent&&e.textContent===n&&t.textContent===n&&"true"===t.getAttribute(u.Attribute.ADDED)){for(let n,r=0;n=e.attributes[r];r++)t.hasAttribute(n.nodeName)||t.setAttribute(n.nodeName,n.nodeValue);return o.replaceNode(e,t),!0}return!1}var _;function v(e){const t=M(e);if(!t)return{type:_.INVALID,node:null};const n=M(e.slice().reverse());if(t===n)return{type:_.VALID,node:t};const r=O(t),o=C(r,e),i=O(n,(function(e){return-1!==o.indexOf(e)})),s=i[0],a=o.indexOf(s);return-1===a?{type:_.INVALID,node:null}:{type:o.length!==r.length?_.PRUNED:T(o[a+1],i[1])?_.VALID:_.INVALID,node:s}}function C(e,t){let n=0;for(;e[n]&&-1===t.indexOf(e[n]);)n++;return e.slice(0,n+1)}function M(e){let t=0,n=null;for(;!n&&t<e.length;)e[t].parentNode&&(n=e[t]),t++;return n}function O(e,t){const n=t||(e=>!1),r=[e];for(;!n(e)&&!l.hasMathTag(e)&&e.parentNode;)e=R(e),r.unshift(e);return r}function T(e,t){return!(!e||!t||e.previousSibling||t.nextSibling)}function x(e){for(;!l.hasMathTag(e)&&L(e);)e=R(e);return e}function I(e){const t=o.toArray(e.childNodes);if(!t)return e;const n=t.filter((function(e){return e.nodeType===o.NodeType.ELEMENT_NODE&&!l.hasIgnoreTag(e)}));return 1===n.length&&l.hasEmptyTag(n[0])&&!n[0].hasAttribute(u.Attribute.TYPE)?I(n[0]):e}function L(e){const t=R(e);return!(!t||!l.hasEmptyTag(t))&&o.toArray(t.childNodes).every((function(t){return t===e||P(t)}))}function P(e){if(e.nodeType!==o.NodeType.ELEMENT_NODE)return!0;if(!e||l.hasIgnoreTag(e))return!0;const t=o.toArray(e.childNodes);return!(!l.hasEmptyTag(e)&&t.length||l.hasDisplayTag(e)||e.hasAttribute(u.Attribute.TYPE)||l.isOrphanedGlyph(e))&&o.toArray(e.childNodes).every(P)}function R(e){return e.parentNode}function w(e){if(e.mathml.length)return p(e);const n=t.SETTINGS.implicit?F(e):o.createElement("mrow");return e.mathml=[n],n}function D(e){if("MFENCED"!==o.tagName(e))return e;const t=o.createElement("mrow");for(let n,r=0;n=e.attributes[r];r++)-1===["open","close","separators"].indexOf(n.name)&&t.setAttribute(n.name,n.value);return o.toArray(e.childNodes).forEach((function(e){t.appendChild(e)})),o.replaceNode(e,t),t}function F(e){const t=o.createElement("mo"),n=o.createTextNode(e.textContent);return t.appendChild(n),u.setAttributes(t,e),t.setAttribute(u.Attribute.ADDED,"true"),t}function k(e,t){const n=e.type+(e.textContent?","+e.textContent:"");t.forEach((function(e){U(e).setAttribute(u.Attribute.OPERATOR,n)}))}function U(e){const t=o.toArray(e.childNodes);if(!t)return e;const n=t.filter((function(e){return!P(e)})),r=[];for(let e,t=0;e=n[t];t++)if(l.hasEmptyTag(e)){const t=U(e);t&&t!==e&&r.push(t)}else r.push(e);return 1===r.length?r[0]:e}function B(e,t,n,r){const o=r||!1;j(e,"Original MathML",o),j(n,"Semantic Tree",o),j(t,"Semantically enriched MathML",o)}function j(e,t,n){const r=o.formatXml(e.toString());n?console.info(t+":\n```html\n"+u.removeAttributePrefix(r)+"\n```\n"):console.info(r)}t.SETTINGS={collapsed:!0,implicit:!0},t.enrich=function(e,t){const n=o.cloneNode(e);return p(t.root),i.default.getInstance().structure&&e.setAttribute(u.Attribute.STRUCTURE,c.SemanticSkeleton.fromStructure(e,t).toString()),r.Debugger.getInstance().generateOutput((function(){return B(n,e,t,!0),[]})),e},t.walkTree=p,t.introduceNewLayer=h,t.introduceLayerAboveLca=f,t.moveSemanticAttributes_=m,t.childrenSubset_=g,t.collateChildNodes_=b,t.collectChildNodes_=y,t.mergeChildren_=S,t.insertNewChild_=E,t.isDescendant_=N,t.functionApplication_=A,function(e){e.VALID="valid",e.INVALID="invalid",e.PRUNED="pruned"}(_=t.lcaType||(t.lcaType={})),t.mathmlLca_=v,t.prunePath_=C,t.attachedElement_=M,t.pathToRoot_=O,t.validLca_=T,t.ascendNewNode=x,t.descendNode_=I,t.unitChild_=L,t.isIgnorable_=P,t.parentNode_=R,t.addCollapsedAttribute=function(e,t){const n=new c.SemanticSkeleton(t);e.setAttribute(u.Attribute.COLLAPSED,n.toString())},t.cloneContentNode=w,t.rewriteMfenced=D,t.createInvisibleOperator_=F,t.setOperatorAttribute_=k,t.getInnerNode=U,t.formattedOutput=B,t.formattedOutput_=j,t.collapsePunctuated=function(e,t){const n=!!t,r=t||[],o=e.parent,i=e.contentNodes.map((function(e){return e.id}));i.unshift("c");const s=[e.id,i];for(let t,i=0;t=e.childNodes[i];i++){const e=p(t);r.push(e);const i=U(e);o&&!n&&i.setAttribute(u.Attribute.PARENT,o.id.toString()),s.push(t.id)}return s},t.printNodeList__=function(e,t){console.info(e),o.toArray(t).forEach((function(e){console.info(e.toString())})),console.info("<<<<<<<<<<<<<<<<<")}},7228:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractHighlighter=void 0;const r=n(5024),o=n(8171);class i{constructor(){this.color=null,this.mactionName="",this.currentHighlights=[]}highlight(e){this.currentHighlights.push(e.map((e=>{const t=this.highlightNode(e);return this.setHighlighted(e),t})))}highlightAll(e){const t=this.getMactionNodes(e);for(let e,n=0;e=t[n];n++)this.highlight([e])}unhighlight(){const e=this.currentHighlights.pop();e&&e.forEach((e=>{this.isHighlighted(e.node)&&(this.unhighlightNode(e),this.unsetHighlighted(e.node))}))}unhighlightAll(){for(;this.currentHighlights.length>0;)this.unhighlight()}setColor(e){this.color=e}colorString(){return this.color.rgba()}addEvents(e,t){const n=this.getMactionNodes(e);for(let e,r=0;e=n[r];r++)for(const n in t)e.addEventListener(n,t[n])}getMactionNodes(e){return Array.from(e.getElementsByClassName(this.mactionName))}isMactionNode(e){const t=e.className||e.getAttribute("class");return!!t&&!!t.match(new RegExp(this.mactionName))}isHighlighted(e){return e.hasAttribute(i.ATTR)}setHighlighted(e){e.setAttribute(i.ATTR,"true")}unsetHighlighted(e){e.removeAttribute(i.ATTR)}colorizeAll(e){r.evalXPath(`.//*[@${o.Attribute.ID}]`,e).forEach((e=>this.colorize(e)))}uncolorizeAll(e){r.evalXPath(`.//*[@${o.Attribute.ID}]`,e).forEach((e=>this.uncolorize(e)))}colorize(e){const t=(0,o.addPrefix)("foreground");e.hasAttribute(t)&&(e.setAttribute(t+"-old",e.style.color),e.style.color=e.getAttribute(t))}uncolorize(e){const t=(0,o.addPrefix)("foreground")+"-old";e.hasAttribute(t)&&(e.style.color=e.getAttribute(t))}}t.AbstractHighlighter=i,i.ATTR="sre-highlight"},7567:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ChtmlHighlighter=void 0;const r=n(9400);class o extends r.CssHighlighter{constructor(){super()}isMactionNode(e){return e.tagName.toUpperCase()===this.mactionName.toUpperCase()}getMactionNodes(e){return Array.from(e.getElementsByTagName(this.mactionName))}}t.ChtmlHighlighter=o},1123:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ContrastPicker=t.ColorPicker=void 0;const n={red:{red:255,green:0,blue:0},green:{red:0,green:255,blue:0},blue:{red:0,green:0,blue:255},yellow:{red:255,green:255,blue:0},cyan:{red:0,green:255,blue:255},magenta:{red:255,green:0,blue:255},white:{red:255,green:255,blue:255},black:{red:0,green:0,blue:0}};function r(e,t){const r=e||{color:t};let o=Object.prototype.hasOwnProperty.call(r,"color")?n[r.color]:r;return o||(o=n[t]),o.alpha=Object.prototype.hasOwnProperty.call(r,"alpha")?r.alpha:1,function(e){const t=e=>(e=Math.max(e,0),e=Math.min(255,e),Math.round(e));return e.red=t(e.red),e.green=t(e.green),e.blue=t(e.blue),e.alpha=Math.max(e.alpha,0),e.alpha=Math.min(1,e.alpha),e}(o)}class o{constructor(e,t){this.foreground=r(t,o.DEFAULT_FOREGROUND_),this.background=r(e,o.DEFAULT_BACKGROUND_)}static toHex(e){const t=e.toString(16);return 1===t.length?"0"+t:t}rgba(){const e=function(e){return"rgba("+e.red+","+e.green+","+e.blue+","+e.alpha+")"};return{background:e(this.background),foreground:e(this.foreground)}}rgb(){const e=function(e){return"rgb("+e.red+","+e.green+","+e.blue+")"};return{background:e(this.background),alphaback:this.background.alpha.toString(),foreground:e(this.foreground),alphafore:this.foreground.alpha.toString()}}hex(){const e=function(e){return"#"+o.toHex(e.red)+o.toHex(e.green)+o.toHex(e.blue)};return{background:e(this.background),alphaback:this.background.alpha.toString(),foreground:e(this.foreground),alphafore:this.foreground.alpha.toString()}}}t.ColorPicker=o,o.DEFAULT_BACKGROUND_="blue",o.DEFAULT_FOREGROUND_="black";t.ContrastPicker=class{constructor(){this.hue=10,this.sat=100,this.light=50,this.incr=50}generate(){return t=function(e,t,n){t=t>1?t/100:t,n=n>1?n/100:n;const r=(1-Math.abs(2*n-1))*t,o=r*(1-Math.abs(e/60%2-1)),i=n-r/2;let s=0,a=0,c=0;return 0<=e&&e<60?[s,a,c]=[r,o,0]:60<=e&&e<120?[s,a,c]=[o,r,0]:120<=e&&e<180?[s,a,c]=[0,r,o]:180<=e&&e<240?[s,a,c]=[0,o,r]:240<=e&&e<300?[s,a,c]=[o,0,r]:300<=e&&e<360&&([s,a,c]=[r,0,o]),{red:s+i,green:a+i,blue:c+i}}(this.hue,this.sat,this.light),"rgb("+(e={red:Math.round(255*t.red),green:Math.round(255*t.green),blue:Math.round(255*t.blue)}).red+","+e.green+","+e.blue+")";var e,t}increment(){this.hue=(this.hue+this.incr)%360}}},9400:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.CssHighlighter=void 0;const r=n(7228);class o extends r.AbstractHighlighter{constructor(){super(),this.mactionName="mjx-maction"}highlightNode(e){const t={node:e,background:e.style.backgroundColor,foreground:e.style.color},n=this.colorString();return e.style.backgroundColor=n.background,e.style.color=n.foreground,t}unhighlightNode(e){e.node.style.backgroundColor=e.background,e.node.style.color=e.foreground}}t.CssHighlighter=o},9009:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.highlighterMapping_=t.addEvents=t.highlighter=void 0;const r=n(7567),o=n(1123),i=n(9400),s=n(7872),a=n(3711),c=n(4662),l=n(3507),u=n(280);t.highlighter=function(e,n,r){const i=new o.ColorPicker(e,n),s="NativeMML"===r.renderer&&"Safari"===r.browser?"MML-CSS":"SVG"===r.renderer&&"v3"===r.browser?"SVG-V3":r.renderer,a=new(t.highlighterMapping_[s]||t.highlighterMapping_.NativeMML);return a.setColor(i),a},t.addEvents=function(e,n,r){const o=t.highlighterMapping_[r.renderer];o&&(new o).addEvents(e,n)},t.highlighterMapping_={SVG:l.SvgHighlighter,"SVG-V3":u.SvgV3Highlighter,NativeMML:c.MmlHighlighter,"HTML-CSS":s.HtmlHighlighter,"MML-CSS":a.MmlCssHighlighter,CommonHTML:i.CssHighlighter,CHTML:r.ChtmlHighlighter}},7872:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlHighlighter=void 0;const r=n(6671),o=n(7228);class i extends o.AbstractHighlighter{constructor(){super(),this.mactionName="maction"}highlightNode(e){const t={node:e,foreground:e.style.color,position:e.style.position},n=this.color.rgb();e.style.color=n.foreground,e.style.position="relative";const o=e.bbox;if(o&&o.w){const i=.05,s=0,a=r.createElement("span"),c=parseFloat(e.style.paddingLeft||"0");a.style.backgroundColor=n.background,a.style.opacity=n.alphaback.toString(),a.style.display="inline-block",a.style.height=o.h+o.d+2*i+"em",a.style.verticalAlign=-o.d+"em",a.style.marginTop=a.style.marginBottom=-i+"em",a.style.width=o.w+2*s+"em",a.style.marginLeft=c-s+"em",a.style.marginRight=-o.w-s-c+"em",e.parentNode.insertBefore(a,e),t.box=a}return t}unhighlightNode(e){const t=e.node;t.style.color=e.foreground,t.style.position=e.position,e.box&&e.box.parentNode.removeChild(e.box)}}t.HtmlHighlighter=i},3711:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MmlCssHighlighter=void 0;const r=n(9400);class o extends r.CssHighlighter{constructor(){super(),this.mactionName="maction"}getMactionNodes(e){return Array.from(e.getElementsByTagName(this.mactionName))}isMactionNode(e){return e.tagName===this.mactionName}}t.MmlCssHighlighter=o},4662:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MmlHighlighter=void 0;const r=n(7228);class o extends r.AbstractHighlighter{constructor(){super(),this.mactionName="maction"}highlightNode(e){let t=e.getAttribute("style");return t+=";background-color: "+this.colorString().background,t+=";color: "+this.colorString().foreground,e.setAttribute("style",t),{node:e}}unhighlightNode(e){let t=e.node.getAttribute("style");t=t.replace(";background-color: "+this.colorString().background,""),t=t.replace(";color: "+this.colorString().foreground,""),e.node.setAttribute("style",t)}colorString(){return this.color.rgba()}getMactionNodes(e){return Array.from(e.getElementsByTagName(this.mactionName))}isMactionNode(e){return e.tagName===this.mactionName}}t.MmlHighlighter=o},3507:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SvgHighlighter=void 0;const r=n(6671),o=n(7228);class i extends o.AbstractHighlighter{constructor(){super(),this.mactionName="mjx-svg-maction"}highlightNode(e){let t;if(this.isHighlighted(e))return t={node:e.previousSibling||e,background:e.style.backgroundColor,foreground:e.style.color},t;if("svg"===e.tagName){const t={node:e,background:e.style.backgroundColor,foreground:e.style.color};return e.style.backgroundColor=this.colorString().background,e.style.color=this.colorString().foreground,t}const n=r.createElementNS("http://www.w3.org/2000/svg","rect");let i;if("use"===e.nodeName){const t=r.createElementNS("http://www.w3.org/2000/svg","g");e.parentNode.insertBefore(t,e),t.appendChild(e),i=t.getBBox(),t.parentNode.replaceChild(e,t)}else i=e.getBBox();n.setAttribute("x",(i.x-40).toString()),n.setAttribute("y",(i.y-40).toString()),n.setAttribute("width",(i.width+80).toString()),n.setAttribute("height",(i.height+80).toString());const s=e.getAttribute("transform");return s&&n.setAttribute("transform",s),n.setAttribute("fill",this.colorString().background),n.setAttribute(o.AbstractHighlighter.ATTR,"true"),e.parentNode.insertBefore(n,e),t={node:n,foreground:e.getAttribute("fill")},e.setAttribute("fill",this.colorString().foreground),t}setHighlighted(e){"svg"===e.tagName&&super.setHighlighted(e)}unhighlightNode(e){if("background"in e)return e.node.style.backgroundColor=e.background,void(e.node.style.color=e.foreground);e.foreground?e.node.nextSibling.setAttribute("fill",e.foreground):e.node.nextSibling.removeAttribute("fill"),e.node.parentNode.removeChild(e.node)}isMactionNode(e){let t=e.className||e.getAttribute("class");return t=void 0!==t.baseVal?t.baseVal:t,!!t&&!!t.match(new RegExp(this.mactionName))}}t.SvgHighlighter=i},280:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SvgV3Highlighter=void 0;const r=n(6671),o=n(5024),i=n(7228),s=n(1123),a=n(3507);class c extends a.SvgHighlighter{constructor(){super(),this.mactionName="maction"}highlightNode(e){let t;if(this.isHighlighted(e))return t={node:e,background:this.colorString().background,foreground:this.colorString().foreground},t;if("svg"===e.tagName||"MJX-CONTAINER"===e.tagName)return t={node:e,background:e.style.backgroundColor,foreground:e.style.color},e.style.backgroundColor=this.colorString().background,e.style.color=this.colorString().foreground,t;const n=r.createElementNS("http://www.w3.org/2000/svg","rect");n.setAttribute("sre-highlighter-added","true");const o=e.getBBox();n.setAttribute("x",(o.x-40).toString()),n.setAttribute("y",(o.y-40).toString()),n.setAttribute("width",(o.width+80).toString()),n.setAttribute("height",(o.height+80).toString());const a=e.getAttribute("transform");if(a&&n.setAttribute("transform",a),n.setAttribute("fill",this.colorString().background),e.setAttribute(i.AbstractHighlighter.ATTR,"true"),e.parentNode.insertBefore(n,e),t={node:e,foreground:e.getAttribute("fill")},"rect"===e.nodeName){const t=new s.ColorPicker({alpha:0,color:"black"});e.setAttribute("fill",t.rgba().foreground)}else e.setAttribute("fill",this.colorString().foreground);return t}unhighlightNode(e){const t=e.node.previousSibling;if(t&&t.hasAttribute("sre-highlighter-added"))return e.foreground?e.node.setAttribute("fill",e.foreground):e.node.removeAttribute("fill"),void e.node.parentNode.removeChild(t);e.node.style.backgroundColor=e.background,e.node.style.color=e.foreground}isMactionNode(e){return e.getAttribute("data-mml-node")===this.mactionName}getMactionNodes(e){return Array.from(o.evalXPath(`.//*[@data-mml-node="${this.mactionName}"]`,e))}}t.SvgV3Highlighter=c},1473:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.StaticTrieNode=t.AbstractTrieNode=void 0;const r=n(1984),o=n(9259);class i{constructor(e,t){this.constraint=e,this.test=t,this.children_={},this.kind=o.TrieNodeKind.ROOT}getConstraint(){return this.constraint}getKind(){return this.kind}applyTest(e){return this.test(e)}addChild(e){const t=e.getConstraint(),n=this.children_[t];return this.children_[t]=e,n}getChild(e){return this.children_[e]}getChildren(){const e=[];for(const t in this.children_)e.push(this.children_[t]);return e}findChildren(e){const t=[];for(const n in this.children_){const r=this.children_[n];r.applyTest(e)&&t.push(r)}return t}removeChild(e){delete this.children_[e]}toString(){return this.constraint}}t.AbstractTrieNode=i;t.StaticTrieNode=class extends i{constructor(e,t){super(e,t),this.rule_=null,this.kind=o.TrieNodeKind.STATIC}getRule(){return this.rule_}setRule(e){this.rule_&&r.Debugger.getInstance().output("Replacing rule "+this.rule_+" with "+e),this.rule_=e}toString(){return this.getRule()?this.constraint+"\n==> "+this.getRule().action:this.constraint}}},2803:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Trie=void 0;const r=n(9259),o=n(9146);class i{constructor(){this.root=(0,o.getNode)(r.TrieNodeKind.ROOT,"",null)}static collectRules_(e){const t=[];let n=[e];for(;n.length;){const e=n.shift();if(e.getKind()===r.TrieNodeKind.QUERY||e.getKind()===r.TrieNodeKind.BOOLEAN){const n=e.getRule();n&&t.unshift(n)}n=n.concat(e.getChildren())}return t}static printWithDepth_(e,t,n){n+=new Array(t+2).join(t.toString())+": "+e.toString()+"\n";const r=e.getChildren();for(let e,o=0;e=r[o];o++)n=i.printWithDepth_(e,t+1,n);return n}static order_(e){const t=e.getChildren();if(!t.length)return 0;const n=Math.max.apply(null,t.map(i.order_));return Math.max(t.length,n)}addRule(e){let t=this.root;const n=e.context,o=e.dynamicCstr.getValues();for(let e=0,i=o.length;e<i;e++)t=this.addNode_(t,o[e],r.TrieNodeKind.DYNAMIC,n);t=this.addNode_(t,e.precondition.query,r.TrieNodeKind.QUERY,n);const i=e.precondition.constraints;for(let e=0,o=i.length;e<o;e++)t=this.addNode_(t,i[e],r.TrieNodeKind.BOOLEAN,n);t.setRule(e)}lookupRules(e,t){let n=[this.root];const o=[];for(;t.length;){const e=t.shift(),o=[];for(;n.length;){n.shift().getChildren().forEach((t=>{t.getKind()===r.TrieNodeKind.DYNAMIC&&-1===e.indexOf(t.getConstraint())||o.push(t)}))}n=o.slice()}for(;n.length;){const t=n.shift();if(t.getRule){const e=t.getRule();e&&o.push(e)}const r=t.findChildren(e);n=n.concat(r)}return o}hasSubtrie(e){let t=this.root;for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t=t.getChild(r),!t)return!1}return!0}toString(){return i.printWithDepth_(this.root,0,"")}collectRules(){return i.collectRules_(this.root)}order(){return i.order_(this.root)}enumerate(e){return this.enumerate_(this.root,e)}byConstraint(e){let t=this.root;for(;e.length&&t;){const n=e.shift();t=t.getChild(n)}return t||null}enumerate_(e,t){t=t||{};const n=e.getChildren();for(let e,o=0;e=n[o];o++)e.kind===r.TrieNodeKind.DYNAMIC&&(t[e.getConstraint()]=this.enumerate_(e,t[e.getConstraint()]));return t}addNode_(e,t,n,r){let i=e.getChild(t);return i||(i=(0,o.getNode)(n,t,r),e.addChild(i)),i}}t.Trie=i},9259:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.TrieNodeKind=void 0,function(e){e.ROOT="root",e.DYNAMIC="dynamic",e.QUERY="query",e.BOOLEAN="boolean",e.STATIC="static"}(t.TrieNodeKind||(t.TrieNodeKind={}))},9146:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.BooleanTrieNode=t.QueryTrieNode=t.constraintTest_=t.DynamicTrieNode=t.RootTrieNode=t.getNode=void 0;const r=n(6671),o=n(5024),i=n(1058),s=n(4161),a=n(1473),c=n(1473),l=n(9259);t.getNode=function(e,t,n){switch(e){case l.TrieNodeKind.ROOT:return new u;case l.TrieNodeKind.DYNAMIC:return new d(t);case l.TrieNodeKind.QUERY:return new f(t,n);case l.TrieNodeKind.BOOLEAN:return new m(t,n);default:return null}};class u extends a.AbstractTrieNode{constructor(){super("",(()=>!0)),this.kind=l.TrieNodeKind.ROOT}}t.RootTrieNode=u;class d extends a.AbstractTrieNode{constructor(e){super(e,(t=>t===e)),this.kind=l.TrieNodeKind.DYNAMIC}}t.DynamicTrieNode=d;const p={"=":(e,t)=>e===t,"!=":(e,t)=>e!==t,"<":(e,t)=>e<t,">":(e,t)=>e>t,"<=":(e,t)=>e<=t,">=":(e,t)=>e>=t};function h(e){if(e.match(/^self::\*$/))return e=>!0;if(e.match(/^self::\w+$/)){const t=e.slice(6).toUpperCase();return e=>e.tagName&&r.tagName(e)===t}if(e.match(/^self::\w+:\w+$/)){const t=e.split(":"),n=o.resolveNameSpace(t[2]);if(!n)return null;const r=t[3].toUpperCase();return e=>e.localName&&e.localName.toUpperCase()===r&&e.namespaceURI===n}if(e.match(/^@\w+$/)){const t=e.slice(1);return e=>e.hasAttribute&&e.hasAttribute(t)}if(e.match(/^@\w+="[\w\d ]+"$/)){const t=e.split("="),n=t[0].slice(1),r=t[1].slice(1,-1);return e=>e.hasAttribute&&e.hasAttribute(n)&&e.getAttribute(n)===r}if(e.match(/^@\w+!="[\w\d ]+"$/)){const t=e.split("!="),n=t[0].slice(1),r=t[1].slice(1,-1);return e=>!e.hasAttribute||!e.hasAttribute(n)||e.getAttribute(n)!==r}if(e.match(/^contains\(\s*@grammar\s*,\s*"[\w\d ]+"\s*\)$/)){const t=e.split('"')[1];return e=>!!i.Grammar.getInstance().getParameter(t)}if(e.match(/^not\(\s*contains\(\s*@grammar\s*,\s*"[\w\d ]+"\s*\)\s*\)$/)){const t=e.split('"')[1];return e=>!i.Grammar.getInstance().getParameter(t)}if(e.match(/^name\(\.\.\/\.\.\)="\w+"$/)){const t=e.split('"')[1].toUpperCase();return e=>{var n,o;return(null===(o=null===(n=e.parentNode)||void 0===n?void 0:n.parentNode)||void 0===o?void 0:o.tagName)&&r.tagName(e.parentNode.parentNode)===t}}if(e.match(/^count\(preceding-sibling::\*\)=\d+$/)){const t=e.split("="),n=parseInt(t[1],10);return e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.childNodes[n])===e}}if(e.match(/^.+\[@category!?=".+"\]$/)){let[,t,n,r]=e.match(/^(.+)\[@category(!?=)"(.+)"\]$/);const i=r.match(/^unit:(.+)$/);let a="";return i&&(r=i[1],a=":unit"),e=>{const i=o.evalXPath(t,e)[0];if(i){const e=s.lookupCategory(i.textContent+a);return"="===n?e===r:e!==r}return!1}}if(e.match(/^string-length\(.+\)\W+\d+/)){const[,t,n,r]=e.match(/^string-length\((.+)\)(\W+)(\d+)/),i=p[n]||p["="],s=parseInt(r,10);return e=>{const n=o.evalXPath(t,e)[0];return!!n&&i(Array.from(n.textContent).length,s)}}return null}t.constraintTest_=h;class f extends c.StaticTrieNode{constructor(e,t){super(e,h(e)),this.context=t,this.kind=l.TrieNodeKind.QUERY}applyTest(e){return this.test?this.test(e):this.context.applyQuery(e,this.constraint)===e}}t.QueryTrieNode=f;class m extends c.StaticTrieNode{constructor(e,t){super(e,h(e)),this.context=t,this.kind=l.TrieNodeKind.BOOLEAN}applyTest(e){return this.test?this.test(e):this.context.applyConstraint(e,this.constraint)}}t.BooleanTrieNode=m},2371:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.completeLocale=t.getLocale=t.setLocale=t.locales=void 0;const r=n(4886),o=n(4513),i=n(1058),s=n(8597),a=n(9883),c=n(2523),l=n(3938),u=n(9139),d=n(1547),p=n(346),h=n(13),f=n(6238),m=n(2913),g=n(3305),b=n(9770),y=n(4524);function S(){const e=o.Variables.ensureLocale(r.default.getInstance().locale,r.default.getInstance().defaultLocale);return r.default.getInstance().locale=e,t.locales[e]()}t.locales={ca:s.ca,da:a.da,de:c.de,en:l.en,es:u.es,fr:d.fr,hi:p.hi,it:h.it,nb:f.nb,nn:g.nn,sv:b.sv,nemeth:m.nemeth},t.setLocale=function(){const e=S();if(function(e){const t=r.default.getInstance().subiso;-1===e.SUBISO.all.indexOf(t)&&(r.default.getInstance().subiso=e.SUBISO.default);e.SUBISO.current=r.default.getInstance().subiso}(e),e){for(const t of Object.getOwnPropertyNames(e))y.LOCALE[t]=e[t];for(const[t,n]of Object.entries(e.CORRECTIONS))i.Grammar.getInstance().setCorrection(t,n)}},t.getLocale=S,t.completeLocale=function(e){const n=t.locales[e.locale];if(!n)return void console.error("Locale "+e.locale+" does not exist!");const r=e.kind.toUpperCase(),o=e.messages;if(!o)return;const i=n();for(const[e,t]of Object.entries(o))i[r][e]=t}},4524:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createLocale=t.LOCALE=void 0;const r=n(4277);function o(){return{FUNCTIONS:(0,r.FUNCTIONS)(),MESSAGES:(0,r.MESSAGES)(),ALPHABETS:(0,r.ALPHABETS)(),NUMBERS:(0,r.NUMBERS)(),COMBINERS:{},CORRECTIONS:{},SUBISO:(0,r.SUBISO)()}}t.LOCALE=o(),t.createLocale=o},3319:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.localeFontCombiner=t.extractString=t.localEnclose=t.localRole=t.localFont=t.combinePostfixIndex=t.nestingToString=void 0;const r=n(4524),o=n(9385);function i(e,t){return void 0===e?t:"string"==typeof e?e:e[0]}t.nestingToString=function(e){switch(e){case 1:return r.LOCALE.MESSAGES.MS.ONCE||"";case 2:return r.LOCALE.MESSAGES.MS.TWICE;default:return e.toString()}},t.combinePostfixIndex=function(e,t){return e===r.LOCALE.MESSAGES.MS.ROOTINDEX||e===r.LOCALE.MESSAGES.MS.INDEX?e:e+" "+t},t.localFont=function(e){return i(r.LOCALE.MESSAGES.font[e],e)},t.localRole=function(e){return i(r.LOCALE.MESSAGES.role[e],e)},t.localEnclose=function(e){return i(r.LOCALE.MESSAGES.enclose[e],e)},t.extractString=i,t.localeFontCombiner=function(e){return"string"==typeof e?{font:e,combiner:r.LOCALE.ALPHABETS.combiner}:{font:e[0],combiner:r.LOCALE.COMBINERS[e[1]]||o.Combiners[e[1]]||r.LOCALE.ALPHABETS.combiner}}},8597:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ca=void 0;const r=n(4524),o=n(3319),i=n(165),s=n(9385),a=function(e,t,n){return e="sans serif "+(n?n+" "+e:e),t?e+" "+t:e};let c=null;t.ca=function(){return c||(c=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.COMBINERS.sansserif=a,e.FUNCTIONS.fracNestDepth=e=>!1,e.FUNCTIONS.combineRootIndex=o.combinePostfixIndex,e.FUNCTIONS.combineNestedRadical=(e,t,n)=>e+n,e.FUNCTIONS.fontRegexp=e=>RegExp("^"+e+" "),e.FUNCTIONS.plural=e=>/.*os$/.test(e)?e+"sos":/.*s$/.test(e)?e+"os":/.*ga$/.test(e)?e.slice(0,-2)+"gues":/.*\xe7a$/.test(e)?e.slice(0,-2)+"ces":/.*ca$/.test(e)?e.slice(0,-2)+"ques":/.*ja$/.test(e)?e.slice(0,-2)+"ges":/.*qua$/.test(e)?e.slice(0,-3)+"q\xfces":/.*a$/.test(e)?e.slice(0,-1)+"es":/.*(e|i)$/.test(e)?e+"ns":/.*\xed$/.test(e)?e.slice(0,-1)+"ins":e+"s",e.FUNCTIONS.si=(e,t)=>(t.match(/^metre/)&&(e=e.replace(/a$/,"\xe0").replace(/o$/,"\xf2").replace(/i$/,"\xed")),e+t),e.ALPHABETS.combiner=s.Combiners.prefixCombiner,e}()),c}},9883:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.da=void 0;const r=n(4524),o=n(3319),i=n(5571),s=n(9385);let a=null;t.da=function(){return a||(a=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.FUNCTIONS.radicalNestDepth=o.nestingToString,e.FUNCTIONS.fontRegexp=t=>t===e.ALPHABETS.capPrefix.default?RegExp("^"+t+" "):RegExp(" "+t+"$"),e.ALPHABETS.combiner=s.Combiners.postfixCombiner,e.ALPHABETS.digitTrans.default=i.default.numberToWords,e}()),a}},2523:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.de=void 0;const r=n(1058),o=n(3319),i=n(4524),s=n(757),a=function(e,t,n){return"s"===n&&(t=t.split(" ").map((function(e){return e.replace(/s$/,"")})).join(" "),n=""),e=n?n+" "+e:e,t?t+" "+e:e},c=function(e,t,n){return e=n&&"s"!==n?n+" "+e:e,t?e+" "+t:e};let l=null;t.de=function(){return l||(l=function(){const e=(0,i.createLocale)();return e.NUMBERS=s.default,e.COMBINERS.germanPostfix=c,e.ALPHABETS.combiner=a,e.FUNCTIONS.radicalNestDepth=t=>t>1?e.NUMBERS.numberToWords(t)+"fach":"",e.FUNCTIONS.combineRootIndex=(e,t)=>{const n=t?t+"wurzel":"";return e.replace("Wurzel",n)},e.FUNCTIONS.combineNestedRadical=(e,t,n)=>{const r=(t?t+" ":"")+(e=n.match(/exponent$/)?e+"r":e);return n.match(/ /)?n.replace(/ /," "+r+" "):r+" "+n},e.FUNCTIONS.fontRegexp=function(e){return e=e.split(" ").map((function(e){return e.replace(/s$/,"(|s)")})).join(" "),new RegExp("((^"+e+" )|( "+e+"$))")},e.CORRECTIONS.correctOne=e=>e.replace(/^eins$/,"ein"),e.CORRECTIONS.localFontNumber=e=>(0,o.localFont)(e).split(" ").map((function(e){return e.replace(/s$/,"")})).join(" "),e.CORRECTIONS.lowercase=e=>e.toLowerCase(),e.CORRECTIONS.article=e=>{const t=r.Grammar.getInstance().getParameter("case"),n=r.Grammar.getInstance().getParameter("plural");return"dative"===t?{der:"dem",die:n?"den":"der",das:"dem"}[e]:e},e.CORRECTIONS.masculine=e=>"dative"===r.Grammar.getInstance().getParameter("case")?e+"n":e,e}()),l}},3938:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.en=void 0;const r=n(1058),o=n(4524),i=n(3319),s=n(166),a=n(9385);let c=null;t.en=function(){return c||(c=function(){const e=(0,o.createLocale)();return e.NUMBERS=s.default,e.FUNCTIONS.radicalNestDepth=i.nestingToString,e.FUNCTIONS.plural=e=>/.*s$/.test(e)?e:e+"s",e.ALPHABETS.combiner=a.Combiners.prefixCombiner,e.ALPHABETS.digitTrans.default=s.default.numberToWords,e.CORRECTIONS.article=e=>r.Grammar.getInstance().getParameter("noArticle")?"":e,e}()),c}},9139:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.es=void 0;const r=n(4524),o=n(3319),i=n(6154),s=n(9385),a=function(e,t,n){return e="sans serif "+(n?n+" "+e:e),t?e+" "+t:e};let c=null;t.es=function(){return c||(c=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.COMBINERS.sansserif=a,e.FUNCTIONS.fracNestDepth=e=>!1,e.FUNCTIONS.combineRootIndex=o.combinePostfixIndex,e.FUNCTIONS.combineNestedRadical=(e,t,n)=>e+n,e.FUNCTIONS.fontRegexp=e=>RegExp("^"+e+" "),e.FUNCTIONS.plural=e=>/.*(a|e|i|o|u)$/.test(e)?e+"s":/.*z$/.test(e)?e.slice(0,-1)+"ces":/.*c$/.test(e)?e.slice(0,-1)+"ques":/.*g$/.test(e)?e+"ues":/.*\u00f3n$/.test(e)?e.slice(0,-2)+"ones":e+"es",e.FUNCTIONS.si=(e,t)=>(t.match(/^metro/)&&(e=e.replace(/a$/,"\xe1").replace(/o$/,"\xf3").replace(/i$/,"\xed")),e+t),e.ALPHABETS.combiner=s.Combiners.prefixCombiner,e}()),c}},1547:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.fr=void 0;const r=n(1058),o=n(4524),i=n(3319),s=n(4394),a=n(9385);let c=null;t.fr=function(){return c||(c=function(){const e=(0,o.createLocale)();return e.NUMBERS=s.default,e.FUNCTIONS.radicalNestDepth=i.nestingToString,e.FUNCTIONS.combineRootIndex=i.combinePostfixIndex,e.FUNCTIONS.combineNestedFraction=(e,t,n)=>n.replace(/ $/g,"")+t+e,e.FUNCTIONS.combineNestedRadical=(e,t,n)=>n+" "+e,e.FUNCTIONS.fontRegexp=e=>RegExp(" (en |)"+e+"$"),e.FUNCTIONS.plural=e=>/.*s$/.test(e)?e:e+"s",e.CORRECTIONS.article=e=>r.Grammar.getInstance().getParameter("noArticle")?"":e,e.ALPHABETS.combiner=a.Combiners.romanceCombiner,e.SUBISO={default:"fr",current:"fr",all:["fr","be","ch"]},e}()),c}},346:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.hi=void 0;const r=n(4524),o=n(1779),i=n(9385),s=n(3319);let a=null;t.hi=function(){return a||(a=function(){const e=(0,r.createLocale)();return e.NUMBERS=o.default,e.ALPHABETS.combiner=i.Combiners.prefixCombiner,e.FUNCTIONS.radicalNestDepth=s.nestingToString,e}()),a}},13:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.it=void 0;const r=n(3319),o=n(4524),i=n(5952),s=n(9385),a=function(e,t,n){return e.match(/^[a-zA-Z]$/)&&(t=t.replace("cerchiato","cerchiata")),e=n?e+" "+n:e,t?e+" "+t:e};let c=null;t.it=function(){return c||(c=function(){const e=(0,o.createLocale)();return e.NUMBERS=i.default,e.COMBINERS.italianPostfix=a,e.FUNCTIONS.radicalNestDepth=r.nestingToString,e.FUNCTIONS.combineRootIndex=r.combinePostfixIndex,e.FUNCTIONS.combineNestedFraction=(e,t,n)=>n.replace(/ $/g,"")+t+e,e.FUNCTIONS.combineNestedRadical=(e,t,n)=>n+" "+e,e.FUNCTIONS.fontRegexp=e=>RegExp(" (en |)"+e+"$"),e.ALPHABETS.combiner=s.Combiners.romanceCombiner,e}()),c}},6238:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.nb=void 0;const r=n(4524),o=n(3319),i=n(984),s=n(9385);let a=null;t.nb=function(){return a||(a=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.ALPHABETS.combiner=s.Combiners.prefixCombiner,e.ALPHABETS.digitTrans.default=i.default.numberToWords,e.FUNCTIONS.radicalNestDepth=o.nestingToString,e}()),a}},2913:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.nemeth=void 0;const r=n(4524),o=n(3669),i=n(9385),s=function(e){return e.match(RegExp("^"+p.ALPHABETS.languagePrefix.english))?e.slice(1):e},a=function(e,t,n){return e=s(e),t?e+t:e},c=function(e,t,n){return t+s(e)},l=function(e,t,n){return t+(n||"")+(e=s(e))+"\u283b"},u=function(e,t,n){return t+(n||"")+(e=s(e))+"\u283b\u283b"},d=function(e,t,n){return t+(e=s(e))+"\u283e"};let p=null;t.nemeth=function(){return p||(p=function(){const e=(0,r.createLocale)();return e.NUMBERS=o.default,e.COMBINERS={postfixCombiner:a,germanCombiner:c,embellishCombiner:l,doubleEmbellishCombiner:u,parensCombiner:d},e.FUNCTIONS.fracNestDepth=e=>!1,e.FUNCTIONS.fontRegexp=e=>RegExp("^"+e),e.FUNCTIONS.si=i.identityTransformer,e.ALPHABETS.combiner=(e,t,n)=>t?t+n+e:s(e),e.ALPHABETS.digitTrans={default:o.default.numberToWords},e}()),p}},3305:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.nn=void 0;const r=n(4524),o=n(3319),i=n(984),s=n(9385);let a=null;t.nn=function(){return a||(a=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.ALPHABETS.combiner=s.Combiners.prefixCombiner,e.ALPHABETS.digitTrans.default=i.default.numberToWords,e.FUNCTIONS.radicalNestDepth=o.nestingToString,e.SUBISO={default:"",current:"",all:["","alt"]},e}()),a}},9770:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.sv=void 0;const r=n(4524),o=n(3319),i=n(6416),s=n(9385);let a=null;t.sv=function(){return a||(a=function(){const e=(0,r.createLocale)();return e.NUMBERS=i.default,e.FUNCTIONS.radicalNestDepth=o.nestingToString,e.FUNCTIONS.fontRegexp=function(e){return new RegExp("((^"+e+" )|( "+e+"$))")},e.ALPHABETS.combiner=s.Combiners.prefixCombiner,e.ALPHABETS.digitTrans.default=i.default.numberToWords,e.CORRECTIONS.correctOne=e=>e.replace(/^ett$/,"en"),e}()),a}},4277:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SUBISO=t.FUNCTIONS=t.ALPHABETS=t.NUMBERS=t.MESSAGES=void 0;const r=n(9385);t.MESSAGES=function(){return{MS:{},MSroots:{},font:{},embellish:{},role:{},enclose:{},navigate:{},regexp:{},unitTimes:""}},t.NUMBERS=function(){return{zero:"zero",ones:[],tens:[],large:[],special:{},wordOrdinal:r.identityTransformer,numericOrdinal:r.identityTransformer,numberToWords:r.identityTransformer,numberToOrdinal:r.pluralCase,vulgarSep:" ",numSep:" "}},t.ALPHABETS=function(){return{latinSmall:[],latinCap:[],greekSmall:[],greekCap:[],capPrefix:{default:""},smallPrefix:{default:""},digitPrefix:{default:""},languagePrefix:{},digitTrans:{default:r.identityTransformer,mathspeak:r.identityTransformer,clearspeak:r.identityTransformer},letterTrans:{default:r.identityTransformer},combiner:(e,t,n)=>e}},t.FUNCTIONS=function(){return{fracNestDepth:e=>r.vulgarFractionSmall(e,10,100),radicalNestDepth:e=>"",combineRootIndex:function(e,t){return e},combineNestedFraction:r.Combiners.identityCombiner,combineNestedRadical:r.Combiners.identityCombiner,fontRegexp:function(e){return new RegExp("^"+e.split(/ |-/).join("( |-)")+"( |-)")},si:r.siCombiner,plural:r.identityTransformer}},t.SUBISO=function(){return{default:"",current:"",all:[]}}},165:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(1058);function o(e){const t=e%1e3,n=Math.floor(t/100),r=n?1===n?"cent":a.ones[n]+"-cents":"",o=function(e){const t=e%100;if(t<20)return a.ones[t];const n=Math.floor(t/10),r=a.tens[n],o=a.ones[t%10];return r&&o?r+(2===n?"-i-":"-")+o:r||o}(t%100);return r&&o?r+a.numSep+o:r||o}function i(e){if(0===e)return a.zero;if(e>=Math.pow(10,36))return e.toString();let t=0,n="";for(;e>0;){const r=e%(t>1?1e6:1e3);if(r){let e=a.large[t];if(t)if(1===t)n=(1===r?"":o(r)+a.numSep)+e+(n?a.numSep+n:"");else{const t=i(r);e=1===r?e:e.replace(/\u00f3$/,"ons"),n=t+a.numSep+e+(n?a.numSep+n:"")}else n=o(r)}e=Math.floor(e/(t>1?1e6:1e3)),t++}return n}function s(e){const t=r.Grammar.getInstance().getParameter("gender");return e.toString()+("f"===t?"a":"n")}const a=(0,n(4277).NUMBERS)();a.numericOrdinal=s,a.numberToWords=i,a.numberToOrdinal=function(e,t){if(e>1999)return s(e);if(e<=10)return a.special.onesOrdinals[e-1];const n=i(e);return n.match(/mil$/)?n.replace(/mil$/,"mil\xb7l\xe8sima"):n.match(/u$/)?n.replace(/u$/,"vena"):n.match(/a$/)?n.replace(/a$/,"ena"):n+(n.match(/e$/)?"na":"ena")},t.default=a},5571:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function r(e,t=!1){return e===a.ones[1]?t?"et":"en":e}function o(e,t=!1){let n=e%1e3,o="",i=a.ones[Math.floor(n/100)];if(o+=i?r(i,!0)+" hundrede":"",n%=100,n)if(o+=o?" og ":"",i=t?a.special.smallOrdinals[n]:a.ones[n],i)o+=i;else{const e=t?a.special.tensOrdinals[Math.floor(n/10)]:a.tens[Math.floor(n/10)];i=a.ones[n%10],o+=i?r(i)+"og"+e:e}return o}function i(e,t=!1){if(0===e)return a.zero;if(e>=Math.pow(10,36))return e.toString();let n=0,i="";for(;e>0;){const s=e%1e3;if(s){const e=o(s,t&&!n);if(n){const t=a.large[n],o=s>1?"er":"";i=r(e,n<=1)+" "+t+o+(i?" og ":"")+i}else i=r(e)+i}e=Math.floor(e/1e3),n++}return i}function s(e){if(e%100)return i(e,!0);const t=i(e);return t.match(/e$/)?t:t+"e"}const a=(0,n(4277).NUMBERS)();a.wordOrdinal=s,a.numericOrdinal=function(e){return e.toString()+"."},a.numberToWords=i,a.numberToOrdinal=function(e,t){return 1===e?t?"hel":"hele":2===e?t?"halv":"halve":s(e)+(t?"dele":"del")},t.default=a},757:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function r(e,t=!1){return e===a.ones[1]?t?"eine":"ein":e}function o(e){let t=e%1e3,n="",o=a.ones[Math.floor(t/100)];if(n+=o?r(o)+"hundert":"",t%=100,t)if(n+=n?a.numSep:"",o=a.ones[t],o)n+=o;else{const e=a.tens[Math.floor(t/10)];o=a.ones[t%10],n+=o?r(o)+"und"+e:e}return n}function i(e){if(0===e)return a.zero;if(e>=Math.pow(10,36))return e.toString();let t=0,n="";for(;e>0;){const i=e%1e3;if(i){const s=o(e%1e3);if(t){const e=a.large[t],o=t>1&&i>1?e.match(/e$/)?"n":"en":"";n=r(s,t>1)+e+o+n}else n=r(s,t>1)+n}e=Math.floor(e/1e3),t++}return n.replace(/ein$/,"eins")}function s(e){if(1===e)return"erste";if(3===e)return"dritte";if(7===e)return"siebte";if(8===e)return"achte";return i(e)+(e<19?"te":"ste")}const a=(0,n(4277).NUMBERS)();a.wordOrdinal=s,a.numericOrdinal=function(e){return e.toString()+"."},a.numberToWords=i,a.numberToOrdinal=function(e,t){return 1===e?"eintel":2===e?t?"halbe":"halb":s(e)+"l"},t.default=a},166:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function r(e){let t=e%1e3,n="";return n+=s.ones[Math.floor(t/100)]?s.ones[Math.floor(t/100)]+s.numSep+"hundred":"",t%=100,t&&(n+=n?s.numSep:"",n+=s.ones[t]||s.tens[Math.floor(t/10)]+(t%10?s.numSep+s.ones[t%10]:"")),n}function o(e){if(0===e)return s.zero;if(e>=Math.pow(10,36))return e.toString();let t=0,n="";for(;e>0;){e%1e3&&(n=r(e%1e3)+(t?"-"+s.large[t]+"-":"")+n),e=Math.floor(e/1e3),t++}return n.replace(/-$/,"")}function i(e){let t=o(e);return t.match(/one$/)?t=t.slice(0,-3)+"first":t.match(/two$/)?t=t.slice(0,-3)+"second":t.match(/three$/)?t=t.slice(0,-5)+"third":t.match(/five$/)?t=t.slice(0,-4)+"fifth":t.match(/eight$/)?t=t.slice(0,-5)+"eighth":t.match(/nine$/)?t=t.slice(0,-4)+"ninth":t.match(/twelve$/)?t=t.slice(0,-6)+"twelfth":t.match(/ty$/)?t=t.slice(0,-2)+"tieth":t+="th",t}const s=(0,n(4277).NUMBERS)();s.wordOrdinal=i,s.numericOrdinal=function(e){const t=e%100,n=e.toString();if(t>10&&t<20)return n+"th";switch(e%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd";default:return n+"th"}},s.numberToWords=o,s.numberToOrdinal=function(e,t){if(1===e)return t?"oneths":"oneth";if(2===e)return t?"halves":"half";const n=i(e);return t?n+"s":n},t.default=s},6154:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(1058);function o(e){const t=e%1e3,n=Math.floor(t/100),r=i.special.hundreds[n],o=function(e){const t=e%100;if(t<30)return i.ones[t];const n=i.tens[Math.floor(t/10)],r=i.ones[t%10];return n&&r?n+" y "+r:n||r}(t%100);return 1===n?o?r+"to "+o:r:r&&o?r+" "+o:r||o}const i=(0,n(4277).NUMBERS)();i.numericOrdinal=function(e){const t=r.Grammar.getInstance().getParameter("gender");return e.toString()+("f"===t?"a":"o")},i.numberToWords=function(e){if(0===e)return i.zero;if(e>=Math.pow(10,36))return e.toString();let t=0,n="";for(;e>0;){const r=e%1e3;if(r){let e=i.large[t];const s=o(r);t?1===r?(e=e.match("/^mil( |$)/")?e:"un "+e,n=e+(n?" "+n:"")):(e=e.replace(/\u00f3n$/,"ones"),n=o(r)+" "+e+(n?" "+n:"")):n=s}e=Math.floor(e/1e3),t++}return n},i.numberToOrdinal=function(e,t){if(e>1999)return e.toString()+"a";if(e<=12)return i.special.onesOrdinals[e-1];const n=[];if(e>=1e3&&(e-=1e3,n.push("mil\xe9sima")),!e)return n.join(" ");let r=0;return r=Math.floor(e/100),r>0&&(n.push(i.special.hundredsOrdinals[r-1]),e%=100),e<=12?n.push(i.special.onesOrdinals[e-1]):(r=Math.floor(e/10),r>0&&(n.push(i.special.tensOrdinals[r-1]),e%=10),e>0&&n.push(i.special.onesOrdinals[e-1])),n.join(" ")},t.default=i},4394:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(4886),o=n(1058),i=n(4277);function s(e){let t=e%1e3,n="";if(n+=u.ones[Math.floor(t/100)]?u.ones[Math.floor(t/100)]+"-cent":"",t%=100,t){n+=n?"-":"";let e=u.ones[t];if(e)n+=e;else{const r=u.tens[Math.floor(t/10)];r.match(/-dix$/)?(e=u.ones[t%10+10],n+=r.replace(/-dix$/,"")+"-"+e):n+=r+(t%10?"-"+u.ones[t%10]:"")}}const r=n.match(/s-\w+$/);return r?n.replace(/s-\w+$/,r[0].slice(1)):n.replace(/-un$/,"-et-un")}function a(e){if(0===e)return u.zero;if(e>=Math.pow(10,36))return e.toString();u.special["tens-"+r.default.getInstance().subiso]&&(u.tens=u.special["tens-"+r.default.getInstance().subiso]);let t=0,n="";for(;e>0;){const r=e%1e3;if(r){let e=u.large[t];const o=s(r);if(e&&e.match(/^mille /)){const r=e.replace(/^mille /,"");n=n.match(RegExp(r))?o+(t?"-mille-":"")+n:n.match(RegExp(r.replace(/s$/,"")))?o+(t?"-mille-":"")+n.replace(r.replace(/s$/,""),r):o+(t?"-"+e+"-":"")+n}else e=1===r&&e?e.replace(/s$/,""):e,n=o+(t?"-"+e+"-":"")+n}e=Math.floor(e/1e3),t++}return n.replace(/-$/,"")}const c={1:"uni\xe8me",2:"demi",3:"tiers",4:"quart"};function l(e){if(1===e)return"premi\xe8re";let t=a(e);return t.match(/^neuf$/)?t=t.slice(0,-1)+"v":t.match(/cinq$/)?t+="u":t.match(/trois$/)?t+="":(t.match(/e$/)||t.match(/s$/))&&(t=t.slice(0,-1)),t+="i\xe8me",t}const u=(0,i.NUMBERS)();u.wordOrdinal=l,u.numericOrdinal=function(e){const t=o.Grammar.getInstance().getParameter("gender");return 1===e?e.toString()+("m"===t?"er":"re"):e.toString()+"e"},u.numberToWords=a,u.numberToOrdinal=function(e,t){const n=c[e]||l(e);return 3===e?n:t?n+"s":n},t.default=u},1779:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(1058);function o(e){if(0===e)return s.zero;if(e>=Math.pow(10,32))return e.toString();let t=0,n="";const r=function(e){let t=e%1e3,n="";return n+=s.ones[Math.floor(t/100)]?s.ones[Math.floor(t/100)]+s.numSep+s.special.hundred:"",t%=100,t&&(n+=n?s.numSep:"",n+=s.ones[t]),n}(e%1e3);if(!(e=Math.floor(e/1e3)))return r;for(;e>0;){const r=e%100;r&&(n=s.ones[r]+s.numSep+s.large[t]+(n?s.numSep+n:"")),e=Math.floor(e/100),t++}return r?n+s.numSep+r:n}function i(e){const t=r.Grammar.getInstance().getParameter("gender");if(e<=0)return e.toString();if(e<10)return"f"===t?s.special.ordinalsFeminine[e]:s.special.ordinalsMasculine[e];return o(e)+("f"===t?"\u0935\u0940\u0902":"\u0935\u093e\u0901")}const s=(0,n(4277).NUMBERS)();s.wordOrdinal=i,s.numericOrdinal=function(e){const t=r.Grammar.getInstance().getParameter("gender");return e>0&&e<10?"f"===t?s.special.simpleSmallOrdinalsFeminine[e]:s.special.simpleSmallOrdinalsMasculine[e]:e.toString().split("").map((function(e){const t=parseInt(e,10);return isNaN(t)?"":s.special.simpleNumbers[t]})).join("")+("f"===t?"\u0935\u0940\u0902":"\u0935\u093e\u0901")},s.numberToWords=o,s.numberToOrdinal=function(e,t){return e<=10?s.special.smallDenominators[e]:i(e)+" \u0905\u0902\u0936"},t.default=s},5952:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(1058);function o(e){let t=e%1e3,n="";if(n+=a.ones[Math.floor(t/100)]?a.ones[Math.floor(t/100)]+a.numSep+"cento":"",t%=100,t){n+=n?a.numSep:"";const e=a.ones[t];if(e)n+=e;else{let e=a.tens[Math.floor(t/10)];const r=t%10;1!==r&&8!==r||(e=e.slice(0,-1)),n+=e,n+=r?a.numSep+a.ones[t%10]:""}}return n}function i(e){if(0===e)return a.zero;if(e>=Math.pow(10,36))return e.toString();if(1===e&&r.Grammar.getInstance().getParameter("fraction"))return"un";let t=0,n="";for(;e>0;){e%1e3&&(n=o(e%1e3)+(t?"-"+a.large[t]+"-":"")+n),e=Math.floor(e/1e3),t++}return n.replace(/-$/,"")}function s(e){const t="m"===r.Grammar.getInstance().getParameter("gender")?"o":"a";let n=a.special.onesOrdinals[e];return n?n.slice(0,-1)+t:(n=i(e),n.slice(0,-1)+"esim"+t)}const a=(0,n(4277).NUMBERS)();a.wordOrdinal=s,a.numericOrdinal=function(e){const t=r.Grammar.getInstance().getParameter("gender");return e.toString()+("m"===t?"o":"a")},a.numberToWords=i,a.numberToOrdinal=function(e,t){if(2===e)return t?"mezzi":"mezzo";const n=s(e);if(!t)return n;const r=n.match(/o$/)?"i":"e";return n.slice(0,-1)+r},t.default=a},3669:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function r(e){return e.toString().split("").map((function(e){return o.ones[parseInt(e,10)]})).join("")}const o=(0,n(4277).NUMBERS)();o.numberToWords=r,o.numberToOrdinal=r,t.default=o},984:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(4886);function o(e,t=!1){let n=e%1e3,r="";const o=Math.floor(n/100),s=a.ones[o];if(r+=s?(1===o?"":s)+"hundre":"",n%=100,n){if(r+=r?"og":"",t){const e=a.special.smallOrdinals[n];if(e)return r+e;if(n%10)return r+a.tens[Math.floor(n/10)]+a.special.smallOrdinals[n%10]}r+=a.ones[n]||a.tens[Math.floor(n/10)]+(n%10?a.ones[n%10]:"")}return t?i(r):r}function i(e){const t=a.special.endOrdinal[0];return"a"===t&&e.match(/en$/)?e.slice(0,-2)+a.special.endOrdinal:e.match(/(d|n)$/)||e.match(/hundre$/)?e+"de":e.match(/i$/)?e+a.special.endOrdinal:"a"===t&&e.match(/e$/)?e.slice(0,-1)+a.special.endOrdinal:(e.match(/e$/),e+"nde")}function s(e){return u(e,!0)}const a=(0,n(4277).NUMBERS)();function c(e,t=!1){return e===a.ones[1]?"ein"===e?"eitt ":t?"et":"ett":e}function l(e,t=!1){let n=e%1e3,r="",o=a.ones[Math.floor(n/100)];if(r+=o?c(o)+"hundre":"",n%=100,n){if(r+=r?"og":"",t){const e=a.special.smallOrdinals[n];if(e)return r+e}if(o=a.ones[n],o)r+=o;else{const e=a.tens[Math.floor(n/10)];o=a.ones[n%10],r+=o?o+"og"+e:e}}return t?i(r):r}function u(e,t=!1){const n="alt"===r.default.getInstance().subiso?function(e,t=!1){if(0===e)return t?a.special.smallOrdinals[0]:a.zero;if(e>=Math.pow(10,36))return e.toString();let n=0,r="";for(;e>0;){const o=e%1e3;if(o){const i=l(e%1e3,!n&&t);!n&&t&&(t=!t),r=(1===n?c(i,!0):i)+(n>1?a.numSep:"")+(n?a.large[n]+(n>1&&o>1?"er":""):"")+(n>1&&r?a.numSep:"")+r}e=Math.floor(e/1e3),n++}return t?r+(r.match(/tusen$/)?"de":"te"):r}(e,t):function(e,t=!1){if(0===e)return t?a.special.smallOrdinals[0]:a.zero;if(e>=Math.pow(10,36))return e.toString();let n=0,r="";for(;e>0;){const i=e%1e3;if(i){const s=o(e%1e3,!n&&t);!n&&t&&(t=!t),r=s+(n?" "+a.large[n]+(n>1&&i>1?"er":"")+(r?" ":""):"")+r}e=Math.floor(e/1e3),n++}return t?r+(r.match(/tusen$/)?"de":"te"):r}(e,t);return n}a.wordOrdinal=s,a.numericOrdinal=function(e){return e.toString()+"."},a.numberToWords=u,a.numberToOrdinal=function(e,t){return s(e)},t.default=a},6416:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function r(e){let t=e%1e3,n="";const r=Math.floor(t/100);return n+=s.ones[r]?(1===r?"":s.ones[r]+s.numSep)+"hundra":"",t%=100,t&&(n+=n?s.numSep:"",n+=s.ones[t]||s.tens[Math.floor(t/10)]+(t%10?s.numSep+s.ones[t%10]:"")),n}function o(e,t=!1){if(0===e)return s.zero;if(e>=Math.pow(10,36))return e.toString();let n=0,o="";for(;e>0;){const i=e%1e3;if(i){const a=s.large[n],c=i>1&&n>1&&!t?"er":"";o=(1===n&&1===i?"":(n>1&&1===i?"en":r(e%1e3))+(n>1?" ":""))+(n?a+c+(n>1?" ":""):"")+o}e=Math.floor(e/1e3),n++}return o.replace(/ $/,"")}function i(e){let t=o(e,!0);return t.match(/^noll$/)?t="nollte":t.match(/ett$/)?t=t.replace(/ett$/,"f\xf6rsta"):t.match(/tv\xe5$/)?t=t.replace(/tv\xe5$/,"andra"):t.match(/tre$/)?t=t.replace(/tre$/,"tredje"):t.match(/fyra$/)?t=t.replace(/fyra$/,"fj\xe4rde"):t.match(/fem$/)?t=t.replace(/fem$/,"femte"):t.match(/sex$/)?t=t.replace(/sex$/,"sj\xe4tte"):t.match(/sju$/)?t=t.replace(/sju$/,"sjunde"):t.match(/\xe5tta$/)?t=t.replace(/\xe5tta$/,"\xe5ttonde"):t.match(/nio$/)?t=t.replace(/nio$/,"nionde"):t.match(/tio$/)?t=t.replace(/tio$/,"tionde"):t.match(/elva$/)?t=t.replace(/elva$/,"elfte"):t.match(/tolv$/)?t=t.replace(/tolv$/,"tolfte"):t.match(/tusen$/)?t=t.replace(/tusen$/,"tusonde"):t.match(/jard$/)||t.match(/jon$/)?t+="te":t+="de",t}const s=(0,n(4277).NUMBERS)();s.wordOrdinal=i,s.numericOrdinal=function(e){const t=e.toString();return t.match(/11$|12$/)?t+":e":t+(t.match(/1$|2$/)?":a":":e")},s.numberToWords=o,s.numberToOrdinal=function(e,t){if(1===e)return"hel";if(2===e)return t?"halva":"halv";let n=i(e);return n=n.match(/de$/)?n.replace(/de$/,""):n,n+(t?"delar":"del")},t.default=s},9385:function(e,t){function n(e,t=""){if(!e.childNodes||!e.childNodes[0]||!e.childNodes[0].childNodes||e.childNodes[0].childNodes.length<2||"number"!==e.childNodes[0].childNodes[0].tagName||"integer"!==e.childNodes[0].childNodes[0].getAttribute("role")||"number"!==e.childNodes[0].childNodes[1].tagName||"integer"!==e.childNodes[0].childNodes[1].getAttribute("role"))return{convertible:!1,content:e.textContent};const n=e.childNodes[0].childNodes[1].textContent,r=e.childNodes[0].childNodes[0].textContent,o=Number(n),i=Number(r);return isNaN(o)||isNaN(i)?{convertible:!1,content:`${r} ${t} ${n}`}:{convertible:!0,enumerator:i,denominator:o}}Object.defineProperty(t,"__esModule",{value:!0}),t.vulgarFractionSmall=t.convertVulgarFraction=t.Combiners=t.siCombiner=t.identityTransformer=t.pluralCase=void 0,t.pluralCase=function(e,t){return e.toString()},t.identityTransformer=function(e){return e.toString()},t.siCombiner=function(e,t){return e+t.toLowerCase()},t.Combiners={},t.Combiners.identityCombiner=function(e,t,n){return e+t+n},t.Combiners.prefixCombiner=function(e,t,n){return e=n?n+" "+e:e,t?t+" "+e:e},t.Combiners.postfixCombiner=function(e,t,n){return e=n?n+" "+e:e,t?e+" "+t:e},t.Combiners.romanceCombiner=function(e,t,n){return e=n?e+" "+n:e,t?e+" "+t:e},t.convertVulgarFraction=n,t.vulgarFractionSmall=function(e,t,r){const o=n(e);if(o.convertible){const e=o.enumerator,n=o.denominator;return e>0&&e<t&&n>0&&n<r}return!1}},1970:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Condition=t.BaseRuleStore=void 0;const r=n(4148),o=n(8310),i=n(7039),s=n(443);class a{constructor(){this.context=new s.SpeechRuleContext,this.parseOrder=o.DynamicCstr.DEFAULT_ORDER,this.parser=new o.DynamicCstrParser(this.parseOrder),this.locale=o.DynamicCstr.DEFAULT_VALUES[o.Axis.LOCALE],this.modality=o.DynamicCstr.DEFAULT_VALUES[o.Axis.MODALITY],this.domain="",this.initialized=!1,this.inherits=null,this.kind="standard",this.customTranscriptions={},this.preconditions=new Map,this.speechRules_=[],this.rank=0,this.parseMethods={Rule:this.defineRule,Generator:this.generateRules,Action:this.defineAction,Precondition:this.definePrecondition,Ignore:this.ignoreRules}}static compareStaticConstraints_(e,t){if(e.length!==t.length)return!1;for(let n,r=0;n=e[r];r++)if(-1===t.indexOf(n))return!1;return!0}static comparePreconditions_(e,t){const n=e.precondition,r=t.precondition;return n.query===r.query&&a.compareStaticConstraints_(n.constraints,r.constraints)}defineRule(e,t,n,r,...o){const s=this.parseAction(n),a=this.parsePrecondition(r,o),c=this.parseCstr(t);if(!(s&&a&&c))return console.error(`Rule Error: ${r}, (${t}): ${n}`),null;const l=new i.SpeechRule(e,c,a,s);return l.precondition.rank=this.rank++,this.addRule(l),l}addRule(e){e.context=this.context,this.speechRules_.unshift(e)}deleteRule(e){const t=this.speechRules_.indexOf(e);-1!==t&&this.speechRules_.splice(t,1)}findRule(e){for(let t,n=0;t=this.speechRules_[n];n++)if(e(t))return t;return null}findAllRules(e){return this.speechRules_.filter(e)}evaluateDefault(e){const t=e.textContent.slice(0);return t.match(/^\s+$/)?this.evaluateWhitespace(t):this.evaluateString(t)}evaluateWhitespace(e){return[]}evaluateCustom(e){const t=this.customTranscriptions[e];return void 0!==t?r.AuditoryDescription.create({text:t},{adjust:!0,translate:!1}):null}evaluateCharacter(e){return this.evaluateCustom(e)||r.AuditoryDescription.create({text:e},{adjust:!0,translate:!0})}removeDuplicates(e){for(let t,n=this.speechRules_.length-1;t=this.speechRules_[n];n--)t!==e&&e.dynamicCstr.equal(t.dynamicCstr)&&a.comparePreconditions_(t,e)&&this.speechRules_.splice(n,1)}getSpeechRules(){return this.speechRules_}setSpeechRules(e){this.speechRules_=e}getPreconditions(){return this.preconditions}parseCstr(e){try{return this.parser.parse(this.locale+"."+this.modality+(this.domain?"."+this.domain:"")+"."+e)}catch(t){if("RuleError"===t.name)return console.error("Rule Error ",`Illegal Dynamic Constraint: ${e}.`,t.message),null;throw t}}parsePrecondition(e,t){try{const n=this.parsePrecondition_(e);e=n[0];let r=n.slice(1);for(const e of t)r=r.concat(this.parsePrecondition_(e));return new i.Precondition(e,...r)}catch(n){if("RuleError"===n.name)return console.error("Rule Error ",`Illegal preconditions: ${e}, ${t}.`,n.message),null;throw n}}parseAction(e){try{return i.Action.fromString(e)}catch(t){if("RuleError"===t.name)return console.error("Rule Error ",`Illegal action: ${e}.`,t.message),null;throw t}}parse(e){this.modality=e.modality||this.modality,this.locale=e.locale||this.locale,this.domain=e.domain||this.domain,this.context.parse(e.functions||[]),"actions"!==e.kind&&(this.kind=e.kind||this.kind,this.inheritRules()),this.parseRules(e.rules||[])}parseRules(e){for(let t,n=0;t=e[n];n++){const e=t[0],n=this.parseMethods[e];e&&n&&n.apply(this,t.slice(1))}}generateRules(e){const t=this.context.customGenerators.lookup(e);t&&t(this)}defineAction(e,t){let n;try{n=i.Action.fromString(t)}catch(e){if("RuleError"===e.name)return void console.error("Action Error ",t,e.message);throw e}const r=this.getFullPreconditions(e);if(!r)return void console.error(`Action Error: No precondition for action ${e}`);this.ignoreRules(e);const o=new RegExp("^\\w+\\.\\w+\\."+(this.domain?"\\w+\\.":""));r.conditions.forEach((([t,r])=>{const s=this.parseCstr(t.toString().replace(o,""));this.addRule(new i.SpeechRule(e,s,r,n))}))}getFullPreconditions(e){const t=this.preconditions.get(e);return t||!this.inherits?t:this.inherits.getFullPreconditions(e)}definePrecondition(e,t,n,...r){const o=this.parsePrecondition(n,r),i=this.parseCstr(t);o&&i?(o.rank=this.rank++,this.preconditions.set(e,new c(i,o))):console.error(`Precondition Error: ${n}, (${t})`)}inheritRules(){if(!this.inherits||!this.inherits.getSpeechRules().length)return;const e=new RegExp("^\\w+\\.\\w+\\."+(this.domain?"\\w+\\.":""));this.inherits.getSpeechRules().forEach((t=>{const n=this.parseCstr(t.dynamicCstr.toString().replace(e,""));this.addRule(new i.SpeechRule(t.name,n,t.precondition,t.action))}))}ignoreRules(e,...t){let n=this.findAllRules((t=>t.name===e));if(!t.length)return void n.forEach(this.deleteRule.bind(this));let r=[];for(const e of t){const t=this.parseCstr(e);for(const e of n)t.equal(e.dynamicCstr)?this.deleteRule(e):r.push(e);n=r,r=[]}}parsePrecondition_(e){const t=this.context.customGenerators.lookup(e);return t?t():[e]}}t.BaseRuleStore=a;class c{constructor(e,t){this.base=e,this._conditions=[],this.constraints=[],this.allCstr={},this.constraints.push(e),this.addCondition(e,t)}get conditions(){return this._conditions}addConstraint(e){if(this.constraints.filter((t=>t.equal(e))).length)return;this.constraints.push(e);const t=[];for(const[n,r]of this.conditions)this.base.equal(n)&&t.push([e,r]);this._conditions=this._conditions.concat(t)}addBaseCondition(e){this.addCondition(this.base,e)}addFullCondition(e){this.constraints.forEach((t=>this.addCondition(t,e)))}addCondition(e,t){const n=e.toString()+" "+t.toString();this.allCstr.condStr||(this.allCstr[n]=!0,this._conditions.push([e,t]))}}t.Condition=c},1462:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.BrailleStore=void 0;const r=n(4036),o=n(7478);class i extends o.MathStore{constructor(){super(...arguments),this.modality="braille",this.customTranscriptions={"\u22ca":"\u2808\u2821\u2833"}}evaluateString(e){const t=[],n=Array.from(e);for(let e=0;e<n.length;e++)t.push(this.evaluateCharacter(n[e]));return t}annotations(){for(let e,t=0;e=this.annotators[t];t++)(0,r.activate)(this.locale,e)}}t.BrailleStore=i},8310:function(e,t){var n;Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultComparator=t.DynamicCstrParser=t.DynamicCstr=t.DynamicProperties=t.Axis=void 0,function(e){e.DOMAIN="domain",e.STYLE="style",e.LOCALE="locale",e.TOPIC="topic",e.MODALITY="modality"}(n=t.Axis||(t.Axis={}));class r{constructor(e,t=Object.keys(e)){this.properties=e,this.order=t}static createProp(...e){const t=o.DEFAULT_ORDER,n={};for(let r=0,o=e.length,i=t.length;r<o&&r<i;r++)n[t[r]]=e[r];return new r(n)}getProperties(){return this.properties}getOrder(){return this.order}getAxes(){return this.order}getProperty(e){return this.properties[e]}updateProperties(e){this.properties=e}allProperties(){const e=[];return this.order.forEach((t=>e.push(this.getProperty(t).slice()))),e}toString(){const e=[];return this.order.forEach((t=>e.push(t+": "+this.getProperty(t).toString()))),e.join("\n")}}t.DynamicProperties=r;class o extends r{constructor(e,t){const n={};for(const[t,r]of Object.entries(e))n[t]=[r];super(n,t),this.components=e}static createCstr(...e){const t=o.DEFAULT_ORDER,n={};for(let r=0,o=e.length,i=t.length;r<o&&r<i;r++)n[t[r]]=e[r];return new o(n)}static defaultCstr(){return o.createCstr.apply(null,o.DEFAULT_ORDER.map((function(e){return o.DEFAULT_VALUES[e]})))}static validOrder(e){const t=o.DEFAULT_ORDER.slice();return e.every((e=>{const n=t.indexOf(e);return-1!==n&&t.splice(n,1)}))}getComponents(){return this.components}getValue(e){return this.components[e]}getValues(){return this.order.map((e=>this.getValue(e)))}allProperties(){const e=super.allProperties();for(let t,n,r=0;t=e[r],n=this.order[r];r++){const e=this.getValue(n);-1===t.indexOf(e)&&t.unshift(e)}return e}toString(){return this.getValues().join(".")}equal(e){const t=e.getAxes();if(this.order.length!==t.length)return!1;for(let n,r=0;n=t[r];r++){const t=this.getValue(n);if(!t||e.getValue(n)!==t)return!1}return!0}}t.DynamicCstr=o,o.DEFAULT_ORDER=[n.LOCALE,n.MODALITY,n.DOMAIN,n.STYLE,n.TOPIC],o.BASE_LOCALE="base",o.DEFAULT_VALUE="default",o.DEFAULT_VALUES={[n.LOCALE]:"en",[n.DOMAIN]:o.DEFAULT_VALUE,[n.STYLE]:o.DEFAULT_VALUE,[n.TOPIC]:o.DEFAULT_VALUE,[n.MODALITY]:"speech"};t.DynamicCstrParser=class{constructor(e){this.order=e}parse(e){const t=e.split("."),n={};if(t.length>this.order.length)throw new Error("Invalid dynamic constraint: "+n);let r=0;for(let e,o=0;e=this.order[o],t.length;o++,r++){const r=t.shift();n[e]=r}return new o(n,this.order.slice(0,r))}};t.DefaultComparator=class{constructor(e,t=new r(e.getProperties(),e.getOrder())){this.reference=e,this.fallback=t,this.order=this.reference.getOrder()}getReference(){return this.reference}setReference(e,t){this.reference=e,this.fallback=t||new r(e.getProperties(),e.getOrder()),this.order=this.reference.getOrder()}match(e){const t=e.getAxes();return t.length===this.reference.getAxes().length&&t.every((t=>{const n=e.getValue(t);return n===this.reference.getValue(t)||-1!==this.fallback.getProperty(t).indexOf(n)}))}compare(e,t){let n=!1;for(let r,o=0;r=this.order[o];o++){const o=e.getValue(r),i=t.getValue(r);if(!n){const e=this.reference.getValue(r);if(e===o&&e!==i)return-1;if(e===i&&e!==o)return 1;if(e===o&&e===i)continue;e!==o&&e!==i&&(n=!0)}const s=this.fallback.getProperty(r),a=s.indexOf(o),c=s.indexOf(i);if(a<c)return-1;if(c<a)return 1}return 0}toString(){return this.reference.toString()+"\n"+this.fallback.toString()}}},1058:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.numbersToAlpha=t.Grammar=t.ATTRIBUTE=void 0;const r=n(6671),o=n(4886),i=n(3319),s=n(4524);t.ATTRIBUTE="grammar";class a{constructor(){this.currentFlags={},this.parameters_={},this.corrections_={},this.preprocessors_={},this.stateStack_=[]}static getInstance(){return a.instance=a.instance||new a,a.instance}static parseInput(e){const t={},n=e.split(":");for(let e=0,r=n.length;e<r;e++){const r=n[e].split("="),o=r[0].trim();r[1]?t[o]=r[1].trim():o.match(/^!/)?t[o.slice(1)]=!1:t[o]=!0}return t}static parseState(e){const t={},n=e.split(" ");for(let e=0,r=n.length;e<r;e++){const r=n[e].split(":"),o=r[0],i=r[1];t[o]=i||!0}return t}static translateString_(e){if(e.match(/:unit$/))return a.translateUnit_(e);const t=o.default.getInstance();let n=t.evaluator(e,t.dynamicCstr);return n=null===n?e:n,a.getInstance().getParameter("plural")&&(n=s.LOCALE.FUNCTIONS.plural(n)),n}static translateUnit_(e){e=a.prepareUnit_(e);const t=o.default.getInstance(),n=a.getInstance().getParameter("plural"),r=t.strict,i=`${t.locale}.${t.modality}.default`;let c,l;return t.strict=!0,n&&(c=t.defaultParser.parse(i+".plural"),l=t.evaluator(e,c)),l?(t.strict=r,l):(c=t.defaultParser.parse(i+".default"),l=t.evaluator(e,c),t.strict=r,l?(n&&(l=s.LOCALE.FUNCTIONS.plural(l)),l):a.cleanUnit_(e))}static prepareUnit_(e){const t=e.match(/:unit$/);return t?e.slice(0,t.index).replace(/\s+/g," ")+e.slice(t.index):e}static cleanUnit_(e){return e.match(/:unit$/)?e.replace(/:unit$/,""):e}clear(){this.parameters_={},this.stateStack_=[]}setParameter(e,t){const n=this.parameters_[e];return t?this.parameters_[e]=t:delete this.parameters_[e],n}getParameter(e){return this.parameters_[e]}setCorrection(e,t){this.corrections_[e]=t}setPreprocessor(e,t){this.preprocessors_[e]=t}getCorrection(e){return this.corrections_[e]}getState(){const e=[];for(const t in this.parameters_){const n=this.parameters_[t];e.push("string"==typeof n?t+":"+n:t)}return e.join(" ")}pushState(e){for(const t in e)e[t]=this.setParameter(t,e[t]);this.stateStack_.push(e)}popState(){const e=this.stateStack_.pop();for(const t in e)this.setParameter(t,e[t])}setAttribute(e){if(e&&e.nodeType===r.NodeType.ELEMENT_NODE){const n=this.getState();n&&e.setAttribute(t.ATTRIBUTE,n)}}preprocess(e){return this.runProcessors_(e,this.preprocessors_)}correct(e){return this.runProcessors_(e,this.corrections_)}apply(e,t){return this.currentFlags=t||{},e=this.currentFlags.adjust||this.currentFlags.preprocess?a.getInstance().preprocess(e):e,(this.parameters_.translate||this.currentFlags.translate)&&(e=a.translateString_(e)),e=this.currentFlags.adjust||this.currentFlags.correct?a.getInstance().correct(e):e,this.currentFlags={},e}runProcessors_(e,t){for(const n in this.parameters_){const r=t[n];if(!r)continue;const o=this.parameters_[n];e=!0===o?r(e):r(e,o)}return e}}function c(e,t){if(!t||!e)return e;const n=s.LOCALE.FUNCTIONS.fontRegexp(i.localFont(t));return e.replace(n,"")}function l(e){return e.match(/\d+/)?s.LOCALE.NUMBERS.numberToWords(parseInt(e,10)):e}t.Grammar=a,t.numbersToAlpha=l,a.getInstance().setCorrection("localFont",i.localFont),a.getInstance().setCorrection("localRole",i.localRole),a.getInstance().setCorrection("localEnclose",i.localEnclose),a.getInstance().setCorrection("ignoreFont",c),a.getInstance().setPreprocessor("annotation",(function(e,t){return e+":"+t})),a.getInstance().setPreprocessor("noTranslateText",(function(e){return e.match(new RegExp("^["+s.LOCALE.MESSAGES.regexp.TEXT+"]+$"))&&(a.getInstance().currentFlags.translate=!1),e})),a.getInstance().setCorrection("ignoreCaps",(function(e){let t=s.LOCALE.ALPHABETS.capPrefix[o.default.getInstance().domain];return void 0===t&&(t=s.LOCALE.ALPHABETS.capPrefix.default),c(e,t)})),a.getInstance().setPreprocessor("numbers2alpha",l)},4161:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.enumerate=t.lookupString=t.lookupCategory=t.lookupRule=t.addSiUnitRules=t.addUnitRules=t.addFunctionRules=t.addSymbolRules=t.defineRule=t.defineRules=t.setSiPrefixes=void 0;const r=n(1984),o=n(4886),i=n(2371),s=n(4974),a=n(8310);let c=a.DynamicCstr.DEFAULT_VALUES[a.Axis.LOCALE],l=a.DynamicCstr.DEFAULT_VALUES[a.Axis.MODALITY],u={};t.setSiPrefixes=function(e){u=e};const d={};function p(e,t,n,r){const o=E(t);N(o,n),o.defineRulesFromMappings(e,c,l,t,r)}function h(e){if(S(e))return;const t=e.names,n=e.mappings,r=e.category;for(let e,o=0;e=t[o];o++)p(e,e,r,n)}function f(e){for(const t of Object.keys(u)){const n=Object.assign({},e);n.mappings={};const r=u[t];n.key=t+n.key,n.names=n.names.map((function(e){return t+e}));for(const t of Object.keys(e.mappings)){n.mappings[t]={};for(const o of Object.keys(e.mappings[t]))n.mappings[t][o]=i.locales[c]().FUNCTIONS.si(r,e.mappings[t][o])}y(n)}y(e)}function m(e,t){const n=d[e];return n?n.lookupRule(null,t):null}function g(e,t){const n=m(e,t);return n?n.action:null}function b(e,t){return t=t||{},e.length?(t[e[0]]=b(e.slice(1),t[e[0]]),t):t}function y(e){const t=e.names;t&&(e.names=t.map((function(e){return e+":unit"}))),h(e)}function S(e){return!(!e.locale&&!e.modality)&&(c=e.locale||c,l=e.modality||l,!0)}function E(e){let t=d[e];return t?(r.Debugger.getInstance().output("Store exists! "+e),t):(t=new s.MathSimpleStore,d[e]=t,t)}function N(e,t){t&&(e.category=t)}t.defineRules=p,t.defineRule=function(e,t,n,r,o,i){const s=E(o);N(s,r),s.defineRuleFromStrings(e,c,l,t,n,o,i)},t.addSymbolRules=function(e){if(S(e))return;const t=s.MathSimpleStore.parseUnicode(e.key);p(e.key,t,e.category,e.mappings)},t.addFunctionRules=h,t.addUnitRules=function(e){S(e)||(e.si?f(e):y(e))},t.addSiUnitRules=f,t.lookupRule=m,t.lookupCategory=function(e){const t=d[e];return t?t.category:""},t.lookupString=g,o.default.getInstance().evaluator=g,t.enumerate=function(e={}){for(const t of Object.values(d))for(const[,n]of t.rules.entries())for(const{cstr:t}of n)e=b(t.getValues(),e);return e}},4974:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MathSimpleStore=void 0;const r=n(4886),o=n(8310);class i{constructor(){this.category="",this.rules=new Map}static parseUnicode(e){const t=parseInt(e,16);return String.fromCodePoint(t)}static testDynamicConstraints_(e,t){return r.default.getInstance().strict?t.cstr.equal(e):r.default.getInstance().comparator.match(t.cstr)}defineRulesFromMappings(e,t,n,r,o){for(const i in o)for(const s in o[i]){const a=o[i][s];this.defineRuleFromStrings(e,t,n,i,s,r,a)}}getRules(e){let t=this.rules.get(e);return t||(t=[],this.rules.set(e,t)),t}defineRuleFromStrings(e,t,n,o,i,s,a){let c=this.getRules(t);const l=r.default.getInstance().parsers[o]||r.default.getInstance().defaultParser,u=r.default.getInstance().comparators[o],d=`${t}.${n}.${o}.${i}`,p=l.parse(d),h=u?u():r.default.getInstance().comparator,f=h.getReference();h.setReference(p);const m={cstr:p,action:a};c=c.filter((e=>!p.equal(e.cstr))),c.push(m),this.rules.set(t,c),h.setReference(f)}lookupRule(e,t){let n=this.getRules(t.getValue(o.Axis.LOCALE));return n=n.filter((function(e){return i.testDynamicConstraints_(t,e)})),1===n.length?n[0]:n.length?n.sort(((e,t)=>r.default.getInstance().comparator.compare(e.cstr,t.cstr)))[0]:null}}t.MathSimpleStore=i},7478:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MathStore=void 0;const r=n(1426),o=n(4524),i=n(4036),s=n(1970),a=n(7039);class c extends s.BaseRuleStore{constructor(){super(),this.annotators=[],this.parseMethods.Alias=this.defineAlias,this.parseMethods.SpecializedRule=this.defineSpecializedRule,this.parseMethods.Specialized=this.defineSpecialized}initialize(){this.initialized||(this.annotations(),this.initialized=!0)}annotations(){for(let e,t=0;e=this.annotators[t];t++)(0,i.activate)(this.domain,e)}defineAlias(e,t,...n){const r=this.parsePrecondition(t,n);if(!r)return void console.error(`Precondition Error: ${t} ${n}`);const o=this.preconditions.get(e);o?o.addFullCondition(r):console.error(`Alias Error: No precondition by the name of ${e}`)}defineRulesAlias(e,t,...n){const r=this.findAllRules((function(t){return t.name===e}));if(0===r.length)throw new a.OutputError("Rule with name "+e+" does not exist.");const o=[];r.forEach((e=>{(e=>{const t=e.dynamicCstr.toString(),n=e.action.toString();for(let e,r=0;e=o[r];r++)if(e.action===n&&e.cstr===t)return!1;return o.push({cstr:t,action:n}),!0})(e)&&this.addAlias_(e,t,n)}))}defineSpecializedRule(e,t,n,r){const o=this.parseCstr(t),i=this.findRule((t=>t.name===e&&o.equal(t.dynamicCstr))),s=this.parseCstr(n);if(!i&&r)throw new a.OutputError("Rule named "+e+" with style "+t+" does not exist.");const c=r?a.Action.fromString(r):i.action,l=new a.SpeechRule(i.name,s,i.precondition,c);this.addRule(l)}defineSpecialized(e,t,n){const r=this.parseCstr(n);if(!r)return void console.error(`Dynamic Constraint Error: ${n}`);const o=this.preconditions.get(e);o?o.addConstraint(r):console.error(`Alias Error: No precondition by the name of ${e}`)}evaluateString(e){const t=[];if(e.match(/^\s+$/))return t;let n=this.matchNumber_(e);if(n&&n.length===e.length)return t.push(this.evaluateCharacter(n.number)),t;const i=r.removeEmpty(e.replace(/\s/g," ").split(" "));for(let e,r=0;e=i[r];r++)if(1===e.length)t.push(this.evaluateCharacter(e));else if(e.match(new RegExp("^["+o.LOCALE.MESSAGES.regexp.TEXT+"]+$")))t.push(this.evaluateCharacter(e));else{let r=e;for(;r;){n=this.matchNumber_(r);const e=r.match(new RegExp("^["+o.LOCALE.MESSAGES.regexp.TEXT+"]+"));if(n)t.push(this.evaluateCharacter(n.number)),r=r.substring(n.length);else if(e)t.push(this.evaluateCharacter(e[0])),r=r.substring(e[0].length);else{const e=Array.from(r),n=e[0];t.push(this.evaluateCharacter(n)),r=e.slice(1).join("")}}}return t}parse(e){super.parse(e),this.annotators=e.annotators||[]}addAlias_(e,t,n){const r=this.parsePrecondition(t,n),o=new a.SpeechRule(e.name,e.dynamicCstr,r,e.action);o.name=e.name,this.addRule(o)}matchNumber_(e){const t=e.match(new RegExp("^"+o.LOCALE.MESSAGES.regexp.NUMBER)),n=e.match(new RegExp("^"+c.regexp.NUMBER));if(!t&&!n)return null;const r=n&&n[0]===e;if(t&&t[0]===e||!r)return t?{number:t[0],length:t[0].length}:null;return{number:n[0].replace(new RegExp(c.regexp.DIGIT_GROUP,"g"),"X").replace(new RegExp(c.regexp.DECIMAL_MARK,"g"),o.LOCALE.MESSAGES.regexp.DECIMAL_MARK).replace(/X/g,o.LOCALE.MESSAGES.regexp.DIGIT_GROUP.replace(/\\/g,"")),length:n[0].length}}}t.MathStore=c,c.regexp={NUMBER:"((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+",DECIMAL_MARK:"\\.",DIGIT_GROUP:","}},7039:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.OutputError=t.Precondition=t.Action=t.Component=t.ActionType=t.SpeechRule=void 0;const r=n(4886),o=n(1058);var i;function s(e){switch(e){case"[n]":return i.NODE;case"[m]":return i.MULTI;case"[t]":return i.TEXT;case"[p]":return i.PERSONALITY;default:throw"Parse error: "+e}}t.SpeechRule=class{constructor(e,t,n,r){this.name=e,this.dynamicCstr=t,this.precondition=n,this.action=r,this.context=null}toString(){return this.name+" | "+this.dynamicCstr.toString()+" | "+this.precondition.toString()+" ==> "+this.action.toString()}},function(e){e.NODE="NODE",e.MULTI="MULTI",e.TEXT="TEXT",e.PERSONALITY="PERSONALITY"}(i=t.ActionType||(t.ActionType={}));class a{constructor({type:e,content:t,attributes:n,grammar:r}){this.type=e,this.content=t,this.attributes=n,this.grammar=r}static grammarFromString(e){return o.Grammar.parseInput(e)}static fromString(e){const t={type:s(e.substring(0,3))};let n=e.slice(3).trim();if(!n)throw new u("Missing content.");switch(t.type){case i.TEXT:if('"'===n[0]){const e=d(n,"\\(")[0].trim();if('"'!==e.slice(-1))throw new u("Invalid string syntax.");t.content=e,n=n.slice(e.length).trim(),-1===n.indexOf("(")&&(n="");break}case i.NODE:case i.MULTI:{const e=n.indexOf(" (");if(-1===e){t.content=n.trim(),n="";break}t.content=n.substring(0,e).trim(),n=n.slice(e).trim()}}if(n){const e=a.attributesFromString(n);e.grammar&&(t.grammar=e.grammar,delete e.grammar),Object.keys(e).length&&(t.attributes=e)}return new a(t)}static attributesFromString(e){if("("!==e[0]||")"!==e.slice(-1))throw new u("Invalid attribute expression: "+e);const t={},n=d(e.slice(1,-1),",");for(let e=0,r=n.length;e<r;e++){const r=n[e],i=r.indexOf(":");if(-1===i)t[r.trim()]="true";else{const e=r.substring(0,i).trim(),n=r.slice(i+1).trim();t[e]=e===o.ATTRIBUTE?a.grammarFromString(n):n}}return t}toString(){let e="";e+=function(e){switch(e){case i.NODE:return"[n]";case i.MULTI:return"[m]";case i.TEXT:return"[t]";case i.PERSONALITY:return"[p]";default:throw"Unknown type error: "+e}}(this.type),e+=this.content?" "+this.content:"";const t=this.attributesToString();return e+=t?" "+t:"",e}grammarToString(){return this.getGrammar().join(":")}getGrammar(){const e=[];for(const t in this.grammar)!0===this.grammar[t]?e.push(t):!1===this.grammar[t]?e.push("!"+t):e.push(t+"="+this.grammar[t]);return e}attributesToString(){const e=this.getAttributes(),t=this.grammarToString();return t&&e.push("grammar:"+t),e.length>0?"("+e.join(", ")+")":""}getAttributes(){const e=[];for(const t in this.attributes){const n=this.attributes[t];"true"===n?e.push(t):e.push(t+":"+n)}return e}}t.Component=a;class c{constructor(e){this.components=e}static fromString(e){const t=d(e,";").filter((function(e){return e.match(/\S/)})).map((function(e){return e.trim()})),n=[];for(let e=0,r=t.length;e<r;e++){const r=a.fromString(t[e]);r&&n.push(r)}return new c(n)}toString(){return this.components.map((function(e){return e.toString()})).join("; ")}}t.Action=c;class l{constructor(e,...t){this.query=e,this.constraints=t;const[n,r]=this.presetPriority();this.priority=n?r:this.calculatePriority()}static constraintValue(e,t){for(let n,r=0;n=t[r];r++)if(e.match(n))return++r;return 0}toString(){const e=this.constraints.join(", ");return`${this.query}, ${e} (${this.priority}, ${this.rank})`}calculatePriority(){const e=l.constraintValue(this.query,l.queryPriorities);if(!e)return 0;const t=this.query.match(/^self::.+\[(.+)\]/)[1];return 100*e+10*l.constraintValue(t,l.attributePriorities)}presetPriority(){if(!this.constraints.length)return[!1,0];const e=this.constraints[this.constraints.length-1].match(/^priority=(.*$)/);if(!e)return[!1,0];this.constraints.pop();const t=parseFloat(e[1]);return[!0,isNaN(t)?0:t]}}t.Precondition=l,l.queryPriorities=[/^self::\*\[.+\]$/,/^self::[\w-]+\[.+\]$/],l.attributePriorities=[/^@[\w-]+$/,/^@[\w-]+!=".+"$/,/^not\(contains\(@[\w-]+,\s*".+"\)\)$/,/^contains\(@[\w-]+,".+"\)$/,/^@[\w-]+=".+"$/];class u extends r.SREError{constructor(e){super(e),this.name="RuleError"}}function d(e,t){const n=[];let r="";for(;""!==e;){const o=e.search(t);if(-1===o){if((e.match(/"/g)||[]).length%2!=0)throw new u("Invalid string in expression: "+e);n.push(r+e),r="",e=""}else if((e.substring(0,o).match(/"/g)||[]).length%2==0)n.push(r+e.substring(0,o)),r="",e=e.substring(o+1);else{const t=e.substring(o).search('"');if(-1===t)throw new u("Invalid string in expression: "+e);r+=e.substring(0,o+t+1),e=e.substring(o+t+1)}}return r&&n.push(r),n}t.OutputError=u},443:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SpeechRuleContext=void 0;const r=n(5024),o=n(3686);t.SpeechRuleContext=class{constructor(){this.customQueries=new o.CustomQueries,this.customStrings=new o.CustomStrings,this.contextFunctions=new o.ContextFunctions,this.customGenerators=new o.CustomGenerators}applyCustomQuery(e,t){const n=this.customQueries.lookup(t);return n?n(e):null}applySelector(e,t){return this.applyCustomQuery(e,t)||r.evalXPath(t,e)}applyQuery(e,t){const n=this.applySelector(e,t);return n.length>0?n[0]:null}applyConstraint(e,t){return!!this.applyQuery(e,t)||r.evaluateBoolean(t,e)}constructString(e,t){if(!t)return"";if('"'===t.charAt(0))return t.slice(1,-1);const n=this.customStrings.lookup(t);return n?n(e):r.evaluateString(t,e)}parse(e){const t=Array.isArray(e)?e:Object.entries(e);for(let e,n=0;e=t[n];n++){switch(e[0].slice(0,3)){case"CQF":this.customQueries.add(e[0],e[1]);break;case"CSF":this.customStrings.add(e[0],e[1]);break;case"CTF":this.contextFunctions.add(e[0],e[1]);break;case"CGF":this.customGenerators.add(e[0],e[1]);break;default:console.error("FunctionError: Invalid function name "+e[0])}}}}},6060:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.storeFactory=t.SpeechRuleEngine=void 0;const r=n(4148),o=n(1984),i=n(6671),s=n(4886),a=n(4998),c=n(5024),l=n(7278),u=n(9805),d=n(1462),p=n(8310),h=n(1058),f=n(7478),m=n(7039),g=n(2803);class b{constructor(){this.trie=null,this.evaluators_={},this.trie=new g.Trie}static getInstance(){return b.instance=b.instance||new b,b.instance}static debugSpeechRule(e,t){const n=e.precondition,r=e.context.applyQuery(t,n.query);o.Debugger.getInstance().output(n.query,r?r.toString():r),n.constraints.forEach((n=>o.Debugger.getInstance().output(n,e.context.applyConstraint(t,n))))}static debugNamedSpeechRule(e,t){const n=b.getInstance().trie.collectRules().filter((t=>t.name==e));for(let r,i=0;r=n[i];i++)o.Debugger.getInstance().output("Rule",e,"DynamicCstr:",r.dynamicCstr.toString(),"number",i),b.debugSpeechRule(r,t)}evaluateNode(e){(0,c.updateEvaluator)(e);const t=(new Date).getTime();let n=[];try{n=this.evaluateNode_(e)}catch(e){console.error("Something went wrong computing speech."),o.Debugger.getInstance().output(e)}const r=(new Date).getTime();return o.Debugger.getInstance().output("Time:",r-t),n}toString(){return this.trie.collectRules().map((e=>e.toString())).join("\n")}runInSetting(e,t){const n=s.default.getInstance(),r={};for(const t in e)r[t]=n[t],n[t]=e[t];n.setDynamicCstr();const o=t();for(const e in r)n[e]=r[e];return n.setDynamicCstr(),o}addStore(e){const t=S(e);"abstract"!==t.kind&&t.getSpeechRules().forEach((e=>this.trie.addRule(e))),this.addEvaluator(t)}processGrammar(e,t,n){const r={};for(const o in n){const i=n[o];r[o]="string"==typeof i?e.constructString(t,i):i}h.Grammar.getInstance().pushState(r)}addEvaluator(e){const t=e.evaluateDefault.bind(e),n=this.evaluators_[e.locale];if(n)return void(n[e.modality]=t);const r={};r[e.modality]=t,this.evaluators_[e.locale]=r}getEvaluator(e,t){const n=this.evaluators_[e]||this.evaluators_[p.DynamicCstr.DEFAULT_VALUES[p.Axis.LOCALE]];return n[t]||n[p.DynamicCstr.DEFAULT_VALUES[p.Axis.MODALITY]]}enumerate(e){return this.trie.enumerate(e)}evaluateNode_(e){return e?(this.updateConstraint_(),this.evaluateTree_(e)):[]}evaluateTree_(e){const t=s.default.getInstance();let n;o.Debugger.getInstance().output(t.mode!==a.Mode.HTTP?e.toString():e),h.Grammar.getInstance().setAttribute(e);const i=this.lookupRule(e,t.dynamicCstr);if(!i)return t.strict?[]:(n=this.getEvaluator(t.locale,t.modality)(e),e.attributes&&this.addPersonality_(n,{},!1,e),n);o.Debugger.getInstance().generateOutput((()=>["Apply Rule:",i.name,i.dynamicCstr.toString(),(t.mode,a.Mode.HTTP,e).toString()]));const l=i.context,u=i.action.components;n=[];for(let t,o=0;t=u[o];o++){let o=[];const i=t.content||"",a=t.attributes||{};let u=!1;t.grammar&&this.processGrammar(l,e,t.grammar);let d=null;if(a.engine){d=s.default.getInstance().dynamicCstr.getComponents();const e=h.Grammar.parseInput(a.engine);s.default.getInstance().setDynamicCstr(e)}switch(t.type){case m.ActionType.NODE:{const t=l.applyQuery(e,i);t&&(o=this.evaluateTree_(t))}break;case m.ActionType.MULTI:{u=!0;const t=l.applySelector(e,i);t.length>0&&(o=this.evaluateNodeList_(l,t,a.sepFunc,l.constructString(e,a.separator),a.ctxtFunc,l.constructString(e,a.context)))}break;case m.ActionType.TEXT:{const t=a.span,n={};if(t){const r=(0,c.evalXPath)(t,e);r.length&&(n.extid=r[0].getAttribute("extid"))}const s=l.constructString(e,i);(s||""===s)&&(o=Array.isArray(s)?s.map((function(e){return r.AuditoryDescription.create({text:e.speech,attributes:e.attributes},{adjust:!0})})):[r.AuditoryDescription.create({text:s,attributes:n},{adjust:!0})])}break;case m.ActionType.PERSONALITY:default:o=[r.AuditoryDescription.create({text:i})]}o[0]&&!u&&(a.context&&(o[0].context=l.constructString(e,a.context)+(o[0].context||"")),a.annotation&&(o[0].annotation=a.annotation)),this.addLayout(o,a,u),t.grammar&&h.Grammar.getInstance().popState(),n=n.concat(this.addPersonality_(o,a,u,e)),d&&s.default.getInstance().setDynamicCstr(d)}return n}evaluateNodeList_(e,t,n,o,i,s){if(!t.length)return[];const a=o||"",c=s||"",l=e.contextFunctions.lookup(i),u=l?l(t,c):function(){return c},d=e.contextFunctions.lookup(n),p=d?d(t,a):function(){return[r.AuditoryDescription.create({text:a},{translate:!0})]};let h=[];for(let e,n=0;e=t[n];n++){const r=this.evaluateTree_(e);if(r.length>0&&(r[0].context=u()+(r[0].context||""),h=h.concat(r),n<t.length-1)){const e=p();h=h.concat(e)}}return h}addLayout(e,t,n){const o=t.layout;o&&(o.match(/^begin/)?e.unshift(new r.AuditoryDescription({text:"",layout:o})):o.match(/^end/)?e.push(new r.AuditoryDescription({text:"",layout:o})):(e.unshift(new r.AuditoryDescription({text:"",layout:`begin${o}`})),e.push(new r.AuditoryDescription({text:"",layout:`end${o}`}))))}addPersonality_(e,t,n,o){const i={};let s=null;for(const e of a.personalityPropList){const n=t[e];if(void 0===n)continue;const r=parseFloat(n),o=isNaN(r)?'"'===n.charAt(0)?n.slice(1,-1):n:r;e===a.personalityProps.PAUSE?s=o:i[e]=o}for(let t,n=0;t=e[n];n++)this.addRelativePersonality_(t,i),this.addExternalAttributes_(t,o);if(n&&e.length&&delete e[e.length-1].personality[a.personalityProps.JOIN],s&&e.length){const t=e[e.length-1];t.text||Object.keys(t.personality).length?e.push(r.AuditoryDescription.create({text:"",personality:{pause:s}})):t.personality[a.personalityProps.PAUSE]=s}return e}addExternalAttributes_(e,t){if(t.hasAttributes()){const n=t.attributes;for(let t=n.length-1;t>=0;t--){const r=n[t].name;!e.attributes[r]&&r.match(/^ext/)&&(e.attributes[r]=n[t].value)}}}addRelativePersonality_(e,t){if(!e.personality)return e.personality=t,e;const n=e.personality;for(const e in t)n[e]&&"number"==typeof n[e]&&"number"==typeof t[e]?n[e]=n[e]+t[e]:n[e]||(n[e]=t[e]);return e}updateConstraint_(){const e=s.default.getInstance().dynamicCstr,t=s.default.getInstance().strict,n=this.trie,r={};let o=e.getValue(p.Axis.LOCALE),i=e.getValue(p.Axis.MODALITY),a=e.getValue(p.Axis.DOMAIN);n.hasSubtrie([o,i,a])||(a=p.DynamicCstr.DEFAULT_VALUES[p.Axis.DOMAIN],n.hasSubtrie([o,i,a])||(i=p.DynamicCstr.DEFAULT_VALUES[p.Axis.MODALITY],n.hasSubtrie([o,i,a])||(o=p.DynamicCstr.DEFAULT_VALUES[p.Axis.LOCALE]))),r[p.Axis.LOCALE]=[o],r[p.Axis.MODALITY]=["summary"!==i?i:p.DynamicCstr.DEFAULT_VALUES[p.Axis.MODALITY]],r[p.Axis.DOMAIN]=["speech"!==i?p.DynamicCstr.DEFAULT_VALUES[p.Axis.DOMAIN]:a];const c=e.getOrder();for(let n,o=0;n=c[o];o++)if(!r[n]){const o=e.getValue(n),i=this.makeSet_(o,e.preference),s=p.DynamicCstr.DEFAULT_VALUES[n];t||o===s||i.push(s),r[n]=i}e.updateProperties(r)}makeSet_(e,t){return t&&Object.keys(t).length?e.split(":"):[e]}lookupRule(e,t){if(!e||e.nodeType!==i.NodeType.ELEMENT_NODE&&e.nodeType!==i.NodeType.TEXT_NODE)return null;const n=this.lookupRules(e,t);return n.length>0?this.pickMostConstraint_(t,n):null}lookupRules(e,t){return this.trie.lookupRules(e,t.allProperties())}pickMostConstraint_(e,t){const n=s.default.getInstance().comparator;return t.sort((function(e,t){return n.compare(e.dynamicCstr,t.dynamicCstr)||t.precondition.priority-e.precondition.priority||t.precondition.constraints.length-e.precondition.constraints.length||t.precondition.rank-e.precondition.rank})),o.Debugger.getInstance().generateOutput((()=>t.map((e=>e.name+"("+e.dynamicCstr.toString()+")"))).bind(this)),t[0]}}t.SpeechRuleEngine=b;const y=new Map;function S(e){const t=`${e.locale}.${e.modality}.${e.domain}`;if("actions"===e.kind){const n=y.get(t);return n.parse(e),n}u.init(),e&&!e.functions&&(e.functions=l.getStore(e.locale,e.modality,e.domain));const n="braille"===e.modality?new d.BrailleStore:new f.MathStore;return y.set(t,n),e.inherits&&(n.inherits=y.get(`${e.inherits}.${e.modality}.${e.domain}`)),n.parse(e),n.initialize(),n}t.storeFactory=S,s.default.nodeEvaluator=b.getInstance().evaluateNode.bind(b.getInstance())},3686:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.CustomGenerators=t.ContextFunctions=t.CustomStrings=t.CustomQueries=void 0;class n{constructor(e,t){this.prefix=e,this.store=t}add(e,t){this.checkCustomFunctionSyntax_(e)&&(this.store[e]=t)}addStore(e){const t=Object.keys(e.store);for(let n,r=0;n=t[r];r++)this.add(n,e.store[n])}lookup(e){return this.store[e]}checkCustomFunctionSyntax_(e){const t=new RegExp("^"+this.prefix);return!!e.match(t)||(console.error("FunctionError: Invalid function name. Expected prefix "+this.prefix),!1)}}t.CustomQueries=class extends n{constructor(){super("CQF",{})}};t.CustomStrings=class extends n{constructor(){super("CSF",{})}};t.ContextFunctions=class extends n{constructor(){super("CTF",{})}};t.CustomGenerators=class extends n{constructor(){super("CGF",{})}}},931:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.contentIterator=t.pauseSeparator=t.nodeCounter=void 0;const r=n(4148),o=n(5024),i=n(4886);t.nodeCounter=function(e,t){const n=e.length;let r=0,o=t;return t||(o=""),function(){return r<n&&(r+=1),o+" "+r}},t.pauseSeparator=function(e,t){const n=parseFloat(t),o=isNaN(n)?t:n;return function(){return[r.AuditoryDescription.create({text:"",personality:{pause:o}})]}},t.contentIterator=function(e,t){let n;return n=e.length>0?o.evalXPath("../../content/*",e[0]):[],function(){const e=n.shift(),o=t?[r.AuditoryDescription.create({text:t},{translate:!0})]:[];if(!e)return o;const s=i.default.evaluateNode(e);return o.concat(s)}}},1939:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getTreeFromString=t.getTree=t.xmlTree=void 0;const r=n(6671),o=n(1784);function i(e){return new o.SemanticTree(e)}t.xmlTree=function(e){return i(e).xml()},t.getTree=i,t.getTreeFromString=function(e){return i(r.parseInput(e))}},4036:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.annotate=t.activate=t.register=t.visitors=t.annotators=void 0;const r=n(241);t.annotators=new Map,t.visitors=new Map,t.register=function(e){const n=e.domain+":"+e.name;e instanceof r.SemanticAnnotator?t.annotators.set(n,e):t.visitors.set(n,e)},t.activate=function(e,n){const r=e+":"+n,o=t.annotators.get(r)||t.visitors.get(r);o&&(o.active=!0)},t.annotate=function(e){for(const n of t.annotators.values())n.active&&n.annotate(e);for(const n of t.visitors.values())n.active&&n.visit(e,Object.assign({},n.def))}},241:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticVisitor=t.SemanticAnnotator=void 0;t.SemanticAnnotator=class{constructor(e,t,n){this.domain=e,this.name=t,this.func=n,this.active=!1}annotate(e){e.childNodes.forEach(this.annotate.bind(this)),e.addAnnotation(this.domain,this.func(e))}};t.SemanticVisitor=class{constructor(e,t,n,r={}){this.domain=e,this.name=t,this.func=n,this.def=r,this.active=!1}visit(e,t){let n=this.func(e,t);e.addAnnotation(this.domain,n[0]);for(let t,r=0;t=e.childNodes[r];r++)n=this.visit(t,n[1]);return n}}},4020:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.lookupSecondary=t.isEmbellishedType=t.isMatchingFence=t.functionApplication=t.invisibleComma=t.invisiblePlus=t.invisibleTimes=t.lookupMeaning=t.lookupRole=t.lookupType=t.equal=t.allLettersRegExp=void 0;const n=String.fromCodePoint(8291),r=["\uff0c","\ufe50",",",n],o=["\xaf","\u2012","\u2013","\u2014","\u2015","\ufe58","-","\u207b","\u208b","\u2212","\u2796","\ufe63","\uff0d","\u2010","\u2011","\u203e","_"],i=["~","\u0303","\u223c","\u02dc","\u223d","\u02f7","\u0334","\u0330"],s={"(":")","[":"]","{":"}","\u2045":"\u2046","\u2329":"\u232a","\u2768":"\u2769","\u276a":"\u276b","\u276c":"\u276d","\u276e":"\u276f","\u2770":"\u2771","\u2772":"\u2773","\u2774":"\u2775","\u27c5":"\u27c6","\u27e6":"\u27e7","\u27e8":"\u27e9","\u27ea":"\u27eb","\u27ec":"\u27ed","\u27ee":"\u27ef","\u2983":"\u2984","\u2985":"\u2986","\u2987":"\u2988","\u2989":"\u298a","\u298b":"\u298c","\u298d":"\u298e","\u298f":"\u2990","\u2991":"\u2992","\u2993":"\u2994","\u2995":"\u2996","\u2997":"\u2998","\u29d8":"\u29d9","\u29da":"\u29db","\u29fc":"\u29fd","\u2e22":"\u2e23","\u2e24":"\u2e25","\u2e26":"\u2e27","\u2e28":"\u2e29","\u3008":"\u3009","\u300a":"\u300b","\u300c":"\u300d","\u300e":"\u300f","\u3010":"\u3011","\u3014":"\u3015","\u3016":"\u3017","\u3018":"\u3019","\u301a":"\u301b","\u301d":"\u301e","\ufd3e":"\ufd3f","\ufe17":"\ufe18","\ufe59":"\ufe5a","\ufe5b":"\ufe5c","\ufe5d":"\ufe5e","\uff08":"\uff09","\uff3b":"\uff3d","\uff5b":"\uff5d","\uff5f":"\uff60","\uff62":"\uff63","\u2308":"\u2309","\u230a":"\u230b","\u230c":"\u230d","\u230e":"\u230f","\u231c":"\u231d","\u231e":"\u231f","\u239b":"\u239e","\u239c":"\u239f","\u239d":"\u23a0","\u23a1":"\u23a4","\u23a2":"\u23a5","\u23a3":"\u23a6","\u23a7":"\u23ab","\u23a8":"\u23ac","\u23a9":"\u23ad","\u23b0":"\u23b1","\u23b8":"\u23b9"},a={"\u23b4":"\u23b5","\u23dc":"\u23dd","\u23de":"\u23df","\u23e0":"\u23e1","\ufe35":"\ufe36","\ufe37":"\ufe38","\ufe39":"\ufe3a","\ufe3b":"\ufe3c","\ufe3d":"\ufe3e","\ufe3f":"\ufe40","\ufe41":"\ufe42","\ufe43":"\ufe44","\ufe47":"\ufe48"},c=Object.keys(s),l=Object.values(s);l.push("\u301f");const u=Object.keys(a),d=Object.values(a),p=["|","\xa6","\u2223","\u23d0","\u23b8","\u23b9","\u2758","\uff5c","\uffe4","\ufe31","\ufe32"],h=["\u2016","\u2225","\u2980","\u2af4"],f=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],m=["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","\u0131","\u0237"],g=["\uff21","\uff22","\uff23","\uff24","\uff25","\uff26","\uff27","\uff28","\uff29","\uff2a","\uff2b","\uff2c","\uff2d","\uff2e","\uff2f","\uff30","\uff31","\uff32","\uff33","\uff34","\uff35","\uff36","\uff37","\uff38","\uff39","\uff3a"],b=["\uff41","\uff42","\uff43","\uff44","\uff45","\uff46","\uff47","\uff48","\uff49","\uff4a","\uff4b","\uff4c","\uff4d","\uff4e","\uff4f","\uff50","\uff51","\uff52","\uff53","\uff54","\uff55","\uff56","\uff57","\uff58","\uff59","\uff5a"],y=["\ud835\udc00","\ud835\udc01","\ud835\udc02","\ud835\udc03","\ud835\udc04","\ud835\udc05","\ud835\udc06","\ud835\udc07","\ud835\udc08","\ud835\udc09","\ud835\udc0a","\ud835\udc0b","\ud835\udc0c","\ud835\udc0d","\ud835\udc0e","\ud835\udc0f","\ud835\udc10","\ud835\udc11","\ud835\udc12","\ud835\udc13","\ud835\udc14","\ud835\udc15","\ud835\udc16","\ud835\udc17","\ud835\udc18","\ud835\udc19"],S=["\ud835\udc1a","\ud835\udc1b","\ud835\udc1c","\ud835\udc1d","\ud835\udc1e","\ud835\udc1f","\ud835\udc20","\ud835\udc21","\ud835\udc22","\ud835\udc23","\ud835\udc24","\ud835\udc25","\ud835\udc26","\ud835\udc27","\ud835\udc28","\ud835\udc29","\ud835\udc2a","\ud835\udc2b","\ud835\udc2c","\ud835\udc2d","\ud835\udc2e","\ud835\udc2f","\ud835\udc30","\ud835\udc31","\ud835\udc32","\ud835\udc33"],E=["\ud835\udc34","\ud835\udc35","\ud835\udc36","\ud835\udc37","\ud835\udc38","\ud835\udc39","\ud835\udc3a","\ud835\udc3b","\ud835\udc3c","\ud835\udc3d","\ud835\udc3e","\ud835\udc3f","\ud835\udc40","\ud835\udc41","\ud835\udc42","\ud835\udc43","\ud835\udc44","\ud835\udc45","\ud835\udc46","\ud835\udc47","\ud835\udc48","\ud835\udc49","\ud835\udc4a","\ud835\udc4b","\ud835\udc4c","\ud835\udc4d"],N=["\ud835\udc4e","\ud835\udc4f","\ud835\udc50","\ud835\udc51","\ud835\udc52","\ud835\udc53","\ud835\udc54","\u210e","\ud835\udc56","\ud835\udc57","\ud835\udc58","\ud835\udc59","\ud835\udc5a","\ud835\udc5b","\ud835\udc5c","\ud835\udc5d","\ud835\udc5e","\ud835\udc5f","\ud835\udc60","\ud835\udc61","\ud835\udc62","\ud835\udc63","\ud835\udc64","\ud835\udc65","\ud835\udc66","\ud835\udc67","\ud835\udea4","\ud835\udea5"],A=["\ud835\udc68","\ud835\udc69","\ud835\udc6a","\ud835\udc6b","\ud835\udc6c","\ud835\udc6d","\ud835\udc6e","\ud835\udc6f","\ud835\udc70","\ud835\udc71","\ud835\udc72","\ud835\udc73","\ud835\udc74","\ud835\udc75","\ud835\udc76","\ud835\udc77","\ud835\udc78","\ud835\udc79","\ud835\udc7a","\ud835\udc7b","\ud835\udc7c","\ud835\udc7d","\ud835\udc7e","\ud835\udc7f","\ud835\udc80","\ud835\udc81"],_=["\ud835\udc82","\ud835\udc83","\ud835\udc84","\ud835\udc85","\ud835\udc86","\ud835\udc87","\ud835\udc88","\ud835\udc89","\ud835\udc8a","\ud835\udc8b","\ud835\udc8c","\ud835\udc8d","\ud835\udc8e","\ud835\udc8f","\ud835\udc90","\ud835\udc91","\ud835\udc92","\ud835\udc93","\ud835\udc94","\ud835\udc95","\ud835\udc96","\ud835\udc97","\ud835\udc98","\ud835\udc99","\ud835\udc9a","\ud835\udc9b"],v=["\ud835\udc9c","\u212c","\ud835\udc9e","\ud835\udc9f","\u2130","\u2131","\ud835\udca2","\u210b","\u2110","\ud835\udca5","\ud835\udca6","\u2112","\u2133","\ud835\udca9","\ud835\udcaa","\ud835\udcab","\ud835\udcac","\u211b","\ud835\udcae","\ud835\udcaf","\ud835\udcb0","\ud835\udcb1","\ud835\udcb2","\ud835\udcb3","\ud835\udcb4","\ud835\udcb5","\u2118"],C=["\ud835\udcb6","\ud835\udcb7","\ud835\udcb8","\ud835\udcb9","\u212f","\ud835\udcbb","\u210a","\ud835\udcbd","\ud835\udcbe","\ud835\udcbf","\ud835\udcc0","\ud835\udcc1","\ud835\udcc2","\ud835\udcc3","\u2134","\ud835\udcc5","\ud835\udcc6","\ud835\udcc7","\ud835\udcc8","\ud835\udcc9","\ud835\udcca","\ud835\udccb","\ud835\udccc","\ud835\udccd","\ud835\udcce","\ud835\udccf","\u2113"],M=["\ud835\udcd0","\ud835\udcd1","\ud835\udcd2","\ud835\udcd3","\ud835\udcd4","\ud835\udcd5","\ud835\udcd6","\ud835\udcd7","\ud835\udcd8","\ud835\udcd9","\ud835\udcda","\ud835\udcdb","\ud835\udcdc","\ud835\udcdd","\ud835\udcde","\ud835\udcdf","\ud835\udce0","\ud835\udce1","\ud835\udce2","\ud835\udce3","\ud835\udce4","\ud835\udce5","\ud835\udce6","\ud835\udce7","\ud835\udce8","\ud835\udce9"],O=["\ud835\udcea","\ud835\udceb","\ud835\udcec","\ud835\udced","\ud835\udcee","\ud835\udcef","\ud835\udcf0","\ud835\udcf1","\ud835\udcf2","\ud835\udcf3","\ud835\udcf4","\ud835\udcf5","\ud835\udcf6","\ud835\udcf7","\ud835\udcf8","\ud835\udcf9","\ud835\udcfa","\ud835\udcfb","\ud835\udcfc","\ud835\udcfd","\ud835\udcfe","\ud835\udcff","\ud835\udd00","\ud835\udd01","\ud835\udd02","\ud835\udd03"],T=["\ud835\udd04","\ud835\udd05","\u212d","\ud835\udd07","\ud835\udd08","\ud835\udd09","\ud835\udd0a","\u210c","\u2111","\ud835\udd0d","\ud835\udd0e","\ud835\udd0f","\ud835\udd10","\ud835\udd11","\ud835\udd12","\ud835\udd13","\ud835\udd14","\u211c","\ud835\udd16","\ud835\udd17","\ud835\udd18","\ud835\udd19","\ud835\udd1a","\ud835\udd1b","\ud835\udd1c","\u2128"],x=["\ud835\udd1e","\ud835\udd1f","\ud835\udd20","\ud835\udd21","\ud835\udd22","\ud835\udd23","\ud835\udd24","\ud835\udd25","\ud835\udd26","\ud835\udd27","\ud835\udd28","\ud835\udd29","\ud835\udd2a","\ud835\udd2b","\ud835\udd2c","\ud835\udd2d","\ud835\udd2e","\ud835\udd2f","\ud835\udd30","\ud835\udd31","\ud835\udd32","\ud835\udd33","\ud835\udd34","\ud835\udd35","\ud835\udd36","\ud835\udd37"],I=["\ud835\udd38","\ud835\udd39","\u2102","\ud835\udd3b","\ud835\udd3c","\ud835\udd3d","\ud835\udd3e","\u210d","\ud835\udd40","\ud835\udd41","\ud835\udd42","\ud835\udd43","\ud835\udd44","\u2115","\ud835\udd46","\u2119","\u211a","\u211d","\ud835\udd4a","\ud835\udd4b","\ud835\udd4c","\ud835\udd4d","\ud835\udd4e","\ud835\udd4f","\ud835\udd50","\u2124"],L=["\ud835\udd52","\ud835\udd53","\ud835\udd54","\ud835\udd55","\ud835\udd56","\ud835\udd57","\ud835\udd58","\ud835\udd59","\ud835\udd5a","\ud835\udd5b","\ud835\udd5c","\ud835\udd5d","\ud835\udd5e","\ud835\udd5f","\ud835\udd60","\ud835\udd61","\ud835\udd62","\ud835\udd63","\ud835\udd64","\ud835\udd65","\ud835\udd66","\ud835\udd67","\ud835\udd68","\ud835\udd69","\ud835\udd6a","\ud835\udd6b"],P=["\ud835\udd6c","\ud835\udd6d","\ud835\udd6e","\ud835\udd6f","\ud835\udd70","\ud835\udd71","\ud835\udd72","\ud835\udd73","\ud835\udd74","\ud835\udd75","\ud835\udd76","\ud835\udd77","\ud835\udd78","\ud835\udd79","\ud835\udd7a","\ud835\udd7b","\ud835\udd7c","\ud835\udd7d","\ud835\udd7e","\ud835\udd7f","\ud835\udd80","\ud835\udd81","\ud835\udd82","\ud835\udd83","\ud835\udd84","\ud835\udd85"],R=["\ud835\udd86","\ud835\udd87","\ud835\udd88","\ud835\udd89","\ud835\udd8a","\ud835\udd8b","\ud835\udd8c","\ud835\udd8d","\ud835\udd8e","\ud835\udd8f","\ud835\udd90","\ud835\udd91","\ud835\udd92","\ud835\udd93","\ud835\udd94","\ud835\udd95","\ud835\udd96","\ud835\udd97","\ud835\udd98","\ud835\udd99","\ud835\udd9a","\ud835\udd9b","\ud835\udd9c","\ud835\udd9d","\ud835\udd9e","\ud835\udd9f"],w=["\ud835\udda0","\ud835\udda1","\ud835\udda2","\ud835\udda3","\ud835\udda4","\ud835\udda5","\ud835\udda6","\ud835\udda7","\ud835\udda8","\ud835\udda9","\ud835\uddaa","\ud835\uddab","\ud835\uddac","\ud835\uddad","\ud835\uddae","\ud835\uddaf","\ud835\uddb0","\ud835\uddb1","\ud835\uddb2","\ud835\uddb3","\ud835\uddb4","\ud835\uddb5","\ud835\uddb6","\ud835\uddb7","\ud835\uddb8","\ud835\uddb9"],D=["\ud835\uddba","\ud835\uddbb","\ud835\uddbc","\ud835\uddbd","\ud835\uddbe","\ud835\uddbf","\ud835\uddc0","\ud835\uddc1","\ud835\uddc2","\ud835\uddc3","\ud835\uddc4","\ud835\uddc5","\ud835\uddc6","\ud835\uddc7","\ud835\uddc8","\ud835\uddc9","\ud835\uddca","\ud835\uddcb","\ud835\uddcc","\ud835\uddcd","\ud835\uddce","\ud835\uddcf","\ud835\uddd0","\ud835\uddd1","\ud835\uddd2","\ud835\uddd3"],F=["\ud835\uddd4","\ud835\uddd5","\ud835\uddd6","\ud835\uddd7","\ud835\uddd8","\ud835\uddd9","\ud835\uddda","\ud835\udddb","\ud835\udddc","\ud835\udddd","\ud835\uddde","\ud835\udddf","\ud835\udde0","\ud835\udde1","\ud835\udde2","\ud835\udde3","\ud835\udde4","\ud835\udde5","\ud835\udde6","\ud835\udde7","\ud835\udde8","\ud835\udde9","\ud835\uddea","\ud835\uddeb","\ud835\uddec","\ud835\udded"],k=["\ud835\uddee","\ud835\uddef","\ud835\uddf0","\ud835\uddf1","\ud835\uddf2","\ud835\uddf3","\ud835\uddf4","\ud835\uddf5","\ud835\uddf6","\ud835\uddf7","\ud835\uddf8","\ud835\uddf9","\ud835\uddfa","\ud835\uddfb","\ud835\uddfc","\ud835\uddfd","\ud835\uddfe","\ud835\uddff","\ud835\ude00","\ud835\ude01","\ud835\ude02","\ud835\ude03","\ud835\ude04","\ud835\ude05","\ud835\ude06","\ud835\ude07"],U=["\ud835\ude08","\ud835\ude09","\ud835\ude0a","\ud835\ude0b","\ud835\ude0c","\ud835\ude0d","\ud835\ude0e","\ud835\ude0f","\ud835\ude10","\ud835\ude11","\ud835\ude12","\ud835\ude13","\ud835\ude14","\ud835\ude15","\ud835\ude16","\ud835\ude17","\ud835\ude18","\ud835\ude19","\ud835\ude1a","\ud835\ude1b","\ud835\ude1c","\ud835\ude1d","\ud835\ude1e","\ud835\ude1f","\ud835\ude20","\ud835\ude21"],B=["\ud835\ude22","\ud835\ude23","\ud835\ude24","\ud835\ude25","\ud835\ude26","\ud835\ude27","\ud835\ude28","\ud835\ude29","\ud835\ude2a","\ud835\ude2b","\ud835\ude2c","\ud835\ude2d","\ud835\ude2e","\ud835\ude2f","\ud835\ude30","\ud835\ude31","\ud835\ude32","\ud835\ude33","\ud835\ude34","\ud835\ude35","\ud835\ude36","\ud835\ude37","\ud835\ude38","\ud835\ude39","\ud835\ude3a","\ud835\ude3b"],j=["\ud835\ude3c","\ud835\ude3d","\ud835\ude3e","\ud835\ude3f","\ud835\ude40","\ud835\ude41","\ud835\ude42","\ud835\ude43","\ud835\ude44","\ud835\ude45","\ud835\ude46","\ud835\ude47","\ud835\ude48","\ud835\ude49","\ud835\ude4a","\ud835\ude4b","\ud835\ude4c","\ud835\ude4d","\ud835\ude4e","\ud835\ude4f","\ud835\ude50","\ud835\ude51","\ud835\ude52","\ud835\ude53","\ud835\ude54","\ud835\ude55"],V=["\ud835\ude56","\ud835\ude57","\ud835\ude58","\ud835\ude59","\ud835\ude5a","\ud835\ude5b","\ud835\ude5c","\ud835\ude5d","\ud835\ude5e","\ud835\ude5f","\ud835\ude60","\ud835\ude61","\ud835\ude62","\ud835\ude63","\ud835\ude64","\ud835\ude65","\ud835\ude66","\ud835\ude67","\ud835\ude68","\ud835\ude69","\ud835\ude6a","\ud835\ude6b","\ud835\ude6c","\ud835\ude6d","\ud835\ude6e","\ud835\ude6f"],H=["\ud835\ude70","\ud835\ude71","\ud835\ude72","\ud835\ude73","\ud835\ude74","\ud835\ude75","\ud835\ude76","\ud835\ude77","\ud835\ude78","\ud835\ude79","\ud835\ude7a","\ud835\ude7b","\ud835\ude7c","\ud835\ude7d","\ud835\ude7e","\ud835\ude7f","\ud835\ude80","\ud835\ude81","\ud835\ude82","\ud835\ude83","\ud835\ude84","\ud835\ude85","\ud835\ude86","\ud835\ude87","\ud835\ude88","\ud835\ude89"],G=["\ud835\ude8a","\ud835\ude8b","\ud835\ude8c","\ud835\ude8d","\ud835\ude8e","\ud835\ude8f","\ud835\ude90","\ud835\ude91","\ud835\ude92","\ud835\ude93","\ud835\ude94","\ud835\ude95","\ud835\ude96","\ud835\ude97","\ud835\ude98","\ud835\ude99","\ud835\ude9a","\ud835\ude9b","\ud835\ude9c","\ud835\ude9d","\ud835\ude9e","\ud835\ude9f","\ud835\udea0","\ud835\udea1","\ud835\udea2","\ud835\udea3"],$=["\u2145","\u2146","\u2147","\u2148","\u2149"],J=["\u0391","\u0392","\u0393","\u0394","\u0395","\u0396","\u0397","\u0398","\u0399","\u039a","\u039b","\u039c","\u039d","\u039e","\u039f","\u03a0","\u03a1","\u03a3","\u03a4","\u03a5","\u03a6","\u03a7","\u03a8","\u03a9"],X=["\u03b1","\u03b2","\u03b3","\u03b4","\u03b5","\u03b6","\u03b7","\u03b8","\u03b9","\u03ba","\u03bb","\u03bc","\u03bd","\u03be","\u03bf","\u03c0","\u03c1","\u03c2","\u03c3","\u03c4","\u03c5","\u03c6","\u03c7","\u03c8","\u03c9"],W=["\ud835\udea8","\ud835\udea9","\ud835\udeaa","\ud835\udeab","\ud835\udeac","\ud835\udead","\ud835\udeae","\ud835\udeaf","\ud835\udeb0","\ud835\udeb1","\ud835\udeb2","\ud835\udeb3","\ud835\udeb4","\ud835\udeb5","\ud835\udeb6","\ud835\udeb7","\ud835\udeb8","\ud835\udeba","\ud835\udebb","\ud835\udebc","\ud835\udebd","\ud835\udebe","\ud835\udebf","\ud835\udec0"],K=["\ud835\udec2","\ud835\udec3","\ud835\udec4","\ud835\udec5","\ud835\udec6","\ud835\udec7","\ud835\udec8","\ud835\udec9","\ud835\udeca","\ud835\udecb","\ud835\udecc","\ud835\udecd","\ud835\udece","\ud835\udecf","\ud835\uded0","\ud835\uded1","\ud835\uded2","\ud835\uded3","\ud835\uded4","\ud835\uded5","\ud835\uded6","\ud835\uded7","\ud835\uded8","\ud835\uded9","\ud835\udeda"],q=["\ud835\udee2","\ud835\udee3","\ud835\udee4","\ud835\udee5","\ud835\udee6","\ud835\udee7","\ud835\udee8","\ud835\udee9","\ud835\udeea","\ud835\udeeb","\ud835\udeec","\ud835\udeed","\ud835\udeee","\ud835\udeef","\ud835\udef0","\ud835\udef1","\ud835\udef2","\ud835\udef4","\ud835\udef5","\ud835\udef6","\ud835\udef7","\ud835\udef8","\ud835\udef9","\ud835\udefa"],Y=["\ud835\udefc","\ud835\udefd","\ud835\udefe","\ud835\udeff","\ud835\udf00","\ud835\udf01","\ud835\udf02","\ud835\udf03","\ud835\udf04","\ud835\udf05","\ud835\udf06","\ud835\udf07","\ud835\udf08","\ud835\udf09","\ud835\udf0a","\ud835\udf0b","\ud835\udf0c","\ud835\udf0d","\ud835\udf0e","\ud835\udf0f","\ud835\udf10","\ud835\udf11","\ud835\udf12","\ud835\udf13","\ud835\udf14"],z=["\ud835\udf1c","\ud835\udf1d","\ud835\udf1e","\ud835\udf1f","\ud835\udf20","\ud835\udf21","\ud835\udf22","\ud835\udf23","\ud835\udf24","\ud835\udf25","\ud835\udf26","\ud835\udf27","\ud835\udf28","\ud835\udf29","\ud835\udf2a","\ud835\udf2b","\ud835\udf2c","\ud835\udf2e","\ud835\udf2f","\ud835\udf30","\ud835\udf31","\ud835\udf32","\ud835\udf33","\ud835\udf34"],Q=["\ud835\udf36","\ud835\udf37","\ud835\udf38","\ud835\udf39","\ud835\udf3a","\ud835\udf3b","\ud835\udf3c","\ud835\udf3d","\ud835\udf3e","\ud835\udf3f","\ud835\udf40","\ud835\udf41","\ud835\udf42","\ud835\udf43","\ud835\udf44","\ud835\udf45","\ud835\udf46","\ud835\udf47","\ud835\udf48","\ud835\udf49","\ud835\udf4a","\ud835\udf4b","\ud835\udf4c","\ud835\udf4d","\ud835\udf4e"],Z=["\ud835\udf56","\ud835\udf57","\ud835\udf58","\ud835\udf59","\ud835\udf5a","\ud835\udf5b","\ud835\udf5c","\ud835\udf5d","\ud835\udf5e","\ud835\udf5f","\ud835\udf60","\ud835\udf61","\ud835\udf62","\ud835\udf63","\ud835\udf64","\ud835\udf65","\ud835\udf66","\ud835\udf68","\ud835\udf69","\ud835\udf6a","\ud835\udf6b","\ud835\udf6c","\ud835\udf6d","\ud835\udf6e"],ee=["\ud835\udf70","\ud835\udf71","\ud835\udf72","\ud835\udf73","\ud835\udf74","\ud835\udf75","\ud835\udf76","\ud835\udf77","\ud835\udf78","\ud835\udf79","\ud835\udf7a","\ud835\udf7b","\ud835\udf7c","\ud835\udf7d","\ud835\udf7e","\ud835\udf7f","\ud835\udf80","\ud835\udf81","\ud835\udf82","\ud835\udf83","\ud835\udf84","\ud835\udf85","\ud835\udf86","\ud835\udf87","\ud835\udf88"],te=["\ud835\udf90","\ud835\udf91","\ud835\udf92","\ud835\udf93","\ud835\udf94","\ud835\udf95","\ud835\udf96","\ud835\udf97","\ud835\udf98","\ud835\udf99","\ud835\udf9a","\ud835\udf9b","\ud835\udf9c","\ud835\udf9d","\ud835\udf9e","\ud835\udf9f","\ud835\udfa0","\ud835\udfa2","\ud835\udfa3","\ud835\udfa4","\ud835\udfa5","\ud835\udfa6","\ud835\udfa7","\ud835\udfa8"],ne=["\ud835\udfaa","\ud835\udfab","\ud835\udfac","\ud835\udfad","\ud835\udfae","\ud835\udfaf","\ud835\udfb0","\ud835\udfb1","\ud835\udfb2","\ud835\udfb3","\ud835\udfb4","\ud835\udfb5","\ud835\udfb6","\ud835\udfb7","\ud835\udfb8","\ud835\udfb9","\ud835\udfba","\ud835\udfbb","\ud835\udfbc","\ud835\udfbd","\ud835\udfbe","\ud835\udfbf","\ud835\udfc0","\ud835\udfc1","\ud835\udfc2"],re=["\u213c","\u213d","\u213e","\u213f"],oe=["\u03d0","\u03d1","\u03d5","\u03d6","\u03d7","\u03f0","\u03f1","\u03f5","\u03f6","\u03f4"],ie=["\ud835\udedc","\ud835\udedd","\ud835\udede","\ud835\udedf","\ud835\udee0","\ud835\udee1"],se=["\ud835\udf16","\ud835\udf17","\ud835\udf18","\ud835\udf19","\ud835\udf1a","\ud835\udf1b"],ae=["\ud835\udf8a","\ud835\udf8b","\ud835\udf8c","\ud835\udf8d","\ud835\udf8e","\ud835\udf8f"],ce=["\u2135","\u2136","\u2137","\u2138"],le=f.concat(m,g,b,y,S,E,A,_,N,v,C,M,O,T,x,I,L,P,R,w,D,F,k,U,B,j,V,H,G,$,J,X,W,K,q,Y,z,Q,Z,ee,re,oe,te,ne,ie,se,ae,ce);t.allLettersRegExp=new RegExp(le.join("|"));const ue=["+","\xb1","\u2213","\u2214","\u2227","\u2228","\u2229","\u222a","\u228c","\u228d","\u228e","\u2293","\u2294","\u229d","\u229e","\u22a4","\u22a5","\u22ba","\u22bb","\u22bc","\u22c4","\u22ce","\u22cf","\u22d2","\u22d3","\u2a5e","\u2295","\u22d4"],de=String.fromCodePoint(8292);ue.push(de);const pe=["\u2020","\u2021","\u2210","\u2217","\u2218","\u2219","\u2240","\u229a","\u229b","\u22a0","\u22a1","\u22c5","\u22c6","\u22c7","\u22c8","\u22c9","\u22ca","\u22cb","\u22cc","\u25cb","\xb7","*","\u2297","\u2299"],he=String.fromCodePoint(8290);pe.push(he);const fe=String.fromCodePoint(8289),me=["\xbc","\xbd","\xbe","\u2150","\u2151","\u2152","\u2153","\u2154","\u2155","\u2156","\u2157","\u2158","\u2159","\u215a","\u215b","\u215c","\u215d","\u215e","\u215f","\u2189"],ge=["\xb2","\xb3","\xb9","\u2070","\u2074","\u2075","\u2076","\u2077","\u2078","\u2079"].concat(["\u2080","\u2081","\u2082","\u2083","\u2084","\u2085","\u2086","\u2087","\u2088","\u2089"],["\u2460","\u2461","\u2462","\u2463","\u2464","\u2465","\u2466","\u2467","\u2468","\u2469","\u246a","\u246b","\u246c","\u246d","\u246e","\u246f","\u2470","\u2471","\u2472","\u2473","\u24ea","\u24eb","\u24ec","\u24ed","\u24ee","\u24ef","\u24f0","\u24f1","\u24f2","\u24f3","\u24f4","\u24f5","\u24f6","\u24f7","\u24f8","\u24f9","\u24fa","\u24fb","\u24fc","\u24fd","\u24fe","\u24ff","\u2776","\u2777","\u2778","\u2779","\u277a","\u277b","\u277c","\u277d","\u277e","\u277f","\u2780","\u2781","\u2782","\u2783","\u2784","\u2785","\u2786","\u2787","\u2788","\u2789","\u278a","\u278b","\u278c","\u278d","\u278e","\u278f","\u2790","\u2791","\u2792","\u2793","\u3248","\u3249","\u324a","\u324b","\u324c","\u324d","\u324e","\u324f","\u3251","\u3252","\u3253","\u3254","\u3255","\u3256","\u3257","\u3258","\u3259","\u325a","\u325b","\u325c","\u325d","\u325e","\u325f","\u32b1","\u32b2","\u32b3","\u32b4","\u32b5","\u32b6","\u32b7","\u32b8","\u32b9","\u32ba","\u32bb","\u32bc","\u32bd","\u32be","\u32bf"],["\u2474","\u2475","\u2476","\u2477","\u2478","\u2479","\u247a","\u247b","\u247c","\u247d","\u247e","\u247f","\u2480","\u2481","\u2482","\u2483","\u2484","\u2485","\u2486","\u2487"],["\u2488","\u2489","\u248a","\u248b","\u248c","\u248d","\u248e","\u248f","\u2490","\u2491","\u2492","\u2493","\u2494","\u2495","\u2496","\u2497","\u2498","\u2499","\u249a","\u249b","\ud83c\udd00","\ud83c\udd01","\ud83c\udd02","\ud83c\udd03","\ud83c\udd04","\ud83c\udd05","\ud83c\udd06","\ud83c\udd07","\ud83c\udd08","\ud83c\udd09","\ud83c\udd0a"]),be=["cos","cot","csc","sec","sin","tan","arccos","arccot","arccsc","arcsec","arcsin","arctan","arc cos","arc cot","arc csc","arc sec","arc sin","arc tan"].concat(["cosh","coth","csch","sech","sinh","tanh","arcosh","arcoth","arcsch","arsech","arsinh","artanh","arccosh","arccoth","arccsch","arcsech","arcsinh","arctanh"],["deg","det","dim","hom","ker","Tr","tr"],["log","ln","lg","exp","expt","gcd","gcd","arg","im","re","Pr"]),ye=[{set:["!",'"',"#","%","&",";","?","@","\\","\xa1","\xa7","\xb6","\xbf","\u2017","\u2020","\u2021","\u2022","\u2023","\u2024","\u2025","\u2027","\u2030","\u2031","\u2038","\u203b","\u203c","\u203d","\u203e","\u2041","\u2042","\u2043","\u2047","\u2048","\u2049","\u204b","\u204c","\u204d","\u204e","\u204f","\u2050","\u2051","\u2053","\u2055","\u2056","\u2058","\u2059","\u205a","\u205b","\u205c","\u205d","\u205e","\ufe10","\ufe14","\ufe15","\ufe16","\ufe30","\ufe45","\ufe46","\ufe49","\ufe4a","\ufe4b","\ufe4c","\ufe54","\ufe56","\ufe57","\ufe5f","\ufe60","\ufe61","\ufe68","\ufe6a","\ufe6b","\uff01","\uff02","\uff03","\uff05","\uff06","\uff07","\uff0a","\uff0f","\uff1b","\uff1f","\uff20","\uff3c"],type:"punctuation",role:"unknown"},{set:["\ufe13",":","\uff1a","\ufe55"],type:"punctuation",role:"colon"},{set:r,type:"punctuation",role:"comma"},{set:["\u2026","\u22ee","\u22ef","\u22f0","\u22f1","\ufe19"],type:"punctuation",role:"ellipsis"},{set:[".","\ufe52","\uff0e"],type:"punctuation",role:"fullstop"},{set:o,type:"operator",role:"dash"},{set:i,type:"operator",role:"tilde"},{set:["'","\u2032","\u2033","\u2034","\u2035","\u2036","\u2037","\u2057","\u02b9","\u02ba"],type:"punctuation",role:"prime"},{set:["\xb0"],type:"punctuation",role:"degree"},{set:c,type:"fence",role:"open"},{set:l,type:"fence",role:"close"},{set:u,type:"fence",role:"top"},{set:d,type:"fence",role:"bottom"},{set:p,type:"fence",role:"neutral"},{set:h,type:"fence",role:"metric"},{set:m,type:"identifier",role:"latinletter",font:"normal"},{set:f,type:"identifier",role:"latinletter",font:"normal"},{set:b,type:"identifier",role:"latinletter",font:"normal"},{set:g,type:"identifier",role:"latinletter",font:"normal"},{set:S,type:"identifier",role:"latinletter",font:"bold"},{set:y,type:"identifier",role:"latinletter",font:"bold"},{set:N,type:"identifier",role:"latinletter",font:"italic"},{set:E,type:"identifier",role:"latinletter",font:"italic"},{set:_,type:"identifier",role:"latinletter",font:"bold-italic"},{set:A,type:"identifier",role:"latinletter",font:"bold-italic"},{set:C,type:"identifier",role:"latinletter",font:"script"},{set:v,type:"identifier",role:"latinletter",font:"script"},{set:O,type:"identifier",role:"latinletter",font:"bold-script"},{set:M,type:"identifier",role:"latinletter",font:"bold-script"},{set:x,type:"identifier",role:"latinletter",font:"fraktur"},{set:T,type:"identifier",role:"latinletter",font:"fraktur"},{set:L,type:"identifier",role:"latinletter",font:"double-struck"},{set:I,type:"identifier",role:"latinletter",font:"double-struck"},{set:R,type:"identifier",role:"latinletter",font:"bold-fraktur"},{set:P,type:"identifier",role:"latinletter",font:"bold-fraktur"},{set:D,type:"identifier",role:"latinletter",font:"sans-serif"},{set:w,type:"identifier",role:"latinletter",font:"sans-serif"},{set:k,type:"identifier",role:"latinletter",font:"sans-serif-bold"},{set:F,type:"identifier",role:"latinletter",font:"sans-serif-bold"},{set:B,type:"identifier",role:"latinletter",font:"sans-serif-italic"},{set:U,type:"identifier",role:"latinletter",font:"sans-serif-italic"},{set:V,type:"identifier",role:"latinletter",font:"sans-serif-bold-italic"},{set:j,type:"identifier",role:"latinletter",font:"sans-serif-bold-italic"},{set:G,type:"identifier",role:"latinletter",font:"monospace"},{set:H,type:"identifier",role:"latinletter",font:"monospace"},{set:$,type:"identifier",role:"latinletter",font:"double-struck-italic"},{set:X,type:"identifier",role:"greekletter",font:"normal"},{set:J,type:"identifier",role:"greekletter",font:"normal"},{set:K,type:"identifier",role:"greekletter",font:"bold"},{set:W,type:"identifier",role:"greekletter",font:"bold"},{set:Y,type:"identifier",role:"greekletter",font:"italic"},{set:q,type:"identifier",role:"greekletter",font:"italic"},{set:Q,type:"identifier",role:"greekletter",font:"bold-italic"},{set:z,type:"identifier",role:"greekletter",font:"bold-italic"},{set:ee,type:"identifier",role:"greekletter",font:"sans-serif-bold"},{set:Z,type:"identifier",role:"greekletter",font:"sans-serif-bold"},{set:te,type:"identifier",role:"greekletter",font:"sans-serif-bold-italic"},{set:ne,type:"identifier",role:"greekletter",font:"sans-serif-bold-italic"},{set:re,type:"identifier",role:"greekletter",font:"double-struck"},{set:oe,type:"identifier",role:"greekletter",font:"normal"},{set:ie,type:"identifier",role:"greekletter",font:"bold"},{set:se,type:"identifier",role:"greekletter",font:"italic"},{set:ae,type:"identifier",role:"greekletter",font:"sans-serif-bold"},{set:ce,type:"identifier",role:"otherletter",font:"normal"},{set:["0","1","2","3","4","5","6","7","8","9"],type:"number",role:"integer",font:"normal"},{set:["\uff10","\uff11","\uff12","\uff13","\uff14","\uff15","\uff16","\uff17","\uff18","\uff19"],type:"number",role:"integer",font:"normal"},{set:["\ud835\udfce","\ud835\udfcf","\ud835\udfd0","\ud835\udfd1","\ud835\udfd2","\ud835\udfd3","\ud835\udfd4","\ud835\udfd5","\ud835\udfd6","\ud835\udfd7"],type:"number",role:"integer",font:"bold"},{set:["\ud835\udfd8","\ud835\udfd9","\ud835\udfda","\ud835\udfdb","\ud835\udfdc","\ud835\udfdd","\ud835\udfde","\ud835\udfdf","\ud835\udfe0","\ud835\udfe1"],type:"number",role:"integer",font:"double-struck"},{set:["\ud835\udfe2","\ud835\udfe3","\ud835\udfe4","\ud835\udfe5","\ud835\udfe6","\ud835\udfe7","\ud835\udfe8","\ud835\udfe9","\ud835\udfea","\ud835\udfeb"],type:"number",role:"integer",font:"sans-serif"},{set:["\ud835\udfec","\ud835\udfed","\ud835\udfee","\ud835\udfef","\ud835\udff0","\ud835\udff1","\ud835\udff2","\ud835\udff3","\ud835\udff4","\ud835\udff5"],type:"number",role:"integer",font:"sans-serif-bold"},{set:["\ud835\udff6","\ud835\udff7","\ud835\udff8","\ud835\udff9","\ud835\udffa","\ud835\udffb","\ud835\udffc","\ud835\udffd","\ud835\udffe","\ud835\udfff"],type:"number",role:"integer",font:"monospace"},{set:me,type:"number",role:"float"},{set:ge,type:"number",role:"othernumber"},{set:ue,type:"operator",role:"addition"},{set:pe,type:"operator",role:"multiplication"},{set:["\xaf","-","\u2052","\u207b","\u208b","\u2212","\u2216","\u2238","\u2242","\u2296","\u229f","\u2796","\u2a29","\u2a2a","\u2a2b","\u2a2c","\u2a3a","\u2a41","\ufe63","\uff0d","\u2010","\u2011"],type:"operator",role:"subtraction"},{set:["/","\xf7","\u2044","\u2215","\u2298","\u27cc","\u29bc","\u2a38"],type:"operator",role:"division"},{set:["\u2200","\u2203","\u2206","\u2207","\u2202","\u2201","\u2204"],type:"operator",role:"prefix operator"},{set:["\ud835\udec1","\ud835\udedb","\ud835\udfca","\ud835\udfcb"],type:"operator",role:"prefix operator",font:"bold"},{set:["\ud835\udefb","\ud835\udf15"],type:"operator",role:"prefix operator",font:"italic"},{set:["\ud835\udf6f","\ud835\udf89"],type:"operator",role:"prefix operator",font:"sans-serif-bold"},{set:["=","~","\u207c","\u208c","\u223c","\u223d","\u2243","\u2245","\u2248","\u224a","\u224b","\u224c","\u224d","\u224e","\u2251","\u2252","\u2253","\u2254","\u2255","\u2256","\u2257","\u2258","\u2259","\u225a","\u225b","\u225c","\u225d","\u225e","\u225f","\u2261","\u2263","\u29e4","\u2a66","\u2a6e","\u2a6f","\u2a70","\u2a71","\u2a72","\u2a73","\u2a74","\u2a75","\u2a76","\u2a77","\u2a78","\u22d5","\u2a6d","\u2a6a","\u2a6b","\u2a6c","\ufe66","\uff1d","\u2a6c","\u229c","\u2237"],type:"relation",role:"equality"},{set:["<",">","\u2241","\u2242","\u2244","\u2246","\u2247","\u2249","\u224f","\u2250","\u2260","\u2262","\u2264","\u2265","\u2266","\u2267","\u2268","\u2269","\u226a","\u226b","\u226c","\u226d","\u226e","\u226f","\u2270","\u2271","\u2272","\u2273","\u2274","\u2275","\u2276","\u2277","\u2278","\u2279","\u227a","\u227b","\u227c","\u227d","\u227e","\u227f","\u2280","\u2281","\u22d6","\u22d7","\u22d8","\u22d9","\u22da","\u22db","\u22dc","\u22dd","\u22de","\u22df","\u22e0","\u22e1","\u22e6","\u22e7","\u22e8","\u22e9","\u2a79","\u2a7a","\u2a7b","\u2a7c","\u2a7d","\u2a7e","\u2a7f","\u2a80","\u2a81","\u2a82","\u2a83","\u2a84","\u2a85","\u2a86","\u2a87","\u2a88","\u2a89","\u2a8a","\u2a8b","\u2a8c","\u2a8d","\u2a8e","\u2a8f","\u2a90","\u2a91","\u2a92","\u2a93","\u2a94","\u2a95","\u2a96","\u2a97","\u2a98","\u2a99","\u2a9a","\u2a9b","\u2a9c","\u2a9d","\u2a9e","\u2a9f","\u2aa0","\u2aa1","\u2aa2","\u2aa3","\u2aa4","\u2aa5","\u2aa6","\u2aa7","\u2aa8","\u2aa9","\u2aaa","\u2aab","\u2aac","\u2aad","\u2aae","\u2aaf","\u2ab0","\u2ab1","\u2ab2","\u2ab3","\u2ab4","\u2ab5","\u2ab6","\u2ab7","\u2ab8","\u2ab9","\u2aba","\u2abb","\u2abc","\u2af7","\u2af8","\u2af9","\u2afa","\u29c0","\u29c1","\ufe64","\ufe65","\uff1c","\uff1e"],type:"relation",role:"inequality"},{set:["\u22e2","\u22e3","\u22e4","\u22e5","\u2282","\u2283","\u2284","\u2285","\u2286","\u2287","\u2288","\u2289","\u228a","\u228b","\u228f","\u2290","\u2291","\u2292","\u2abd","\u2abe","\u2abf","\u2ac0","\u2ac1","\u2ac2","\u2ac3","\u2ac4","\u2ac5","\u2ac6","\u2ac7","\u2ac8","\u2ac9","\u2aca","\u2acb","\u2acc","\u2acd","\u2ace","\u2acf","\u2ad0","\u2ad1","\u2ad2","\u2ad3","\u2ad4","\u2ad5","\u2ad6","\u2ad7","\u2ad8","\u22d0","\u22d1","\u22ea","\u22eb","\u22ec","\u22ed","\u22b2","\u22b3","\u22b4","\u22b5"],type:"relation",role:"set"},{set:["\u22a2","\u22a3","\u22a6","\u22a7","\u22a8","\u22a9","\u22aa","\u22ab","\u22ac","\u22ad","\u22ae","\u22af","\u2ade","\u2adf","\u2ae0","\u2ae1","\u2ae2","\u2ae3","\u2ae4","\u2ae5","\u2ae6","\u2ae7","\u2ae8","\u2ae9","\u2aea","\u2aeb","\u2aec","\u2aed"],type:"relation",role:"unknown"},{set:["\u2190","\u2191","\u2192","\u2193","\u2194","\u2195","\u2196","\u2197","\u2198","\u2199","\u219a","\u219b","\u219c","\u219d","\u219e","\u219f","\u21a0","\u21a1","\u21a2","\u21a3","\u21a4","\u21a5","\u21a6","\u21a7","\u21a8","\u21a9","\u21aa","\u21ab","\u21ac","\u21ad","\u21ae","\u21af","\u21b0","\u21b1","\u21b2","\u21b3","\u21b4","\u21b5","\u21b6","\u21b7","\u21b8","\u21b9","\u21ba","\u21bb","\u21c4","\u21c5","\u21c6","\u21c7","\u21c8","\u21c9","\u21ca","\u21cd","\u21ce","\u21cf","\u21d0","\u21d1","\u21d2","\u21d3","\u21d4","\u21d5","\u21d6","\u21d7","\u21d8","\u21d9","\u21da","\u21db","\u21dc","\u21dd","\u21de","\u21df","\u21e0","\u21e1","\u21e2","\u21e3","\u21e4","\u21e5","\u21e6","\u21e7","\u21e8","\u21e9","\u21ea","\u21eb","\u21ec","\u21ed","\u21ee","\u21ef","\u21f0","\u21f1","\u21f2","\u21f3","\u21f4","\u21f5","\u21f6","\u21f7","\u21f8","\u21f9","\u21fa","\u21fb","\u21fc","\u21fd","\u21fe","\u21ff","\u2301","\u2303","\u2304","\u2324","\u238b","\u2794","\u2798","\u2799","\u279a","\u279b","\u279c","\u279d","\u279e","\u279f","\u27a0","\u27a1","\u27a2","\u27a3","\u27a4","\u27a5","\u27a6","\u27a7","\u27a8","\u27a9","\u27aa","\u27ab","\u27ac","\u27ad","\u27ae","\u27af","\u27b1","\u27b2","\u27b3","\u27b4","\u27b5","\u27b6","\u27b7","\u27b8","\u27b9","\u27ba","\u27bb","\u27bc","\u27bd","\u27be","\u27f0","\u27f1","\u27f2","\u27f3","\u27f4","\u27f5","\u27f6","\u27f7","\u27f8","\u27f9","\u27fa","\u27fb","\u27fc","\u27fd","\u27fe","\u27ff","\u2900","\u2901","\u2902","\u2903","\u2904","\u2905","\u2906","\u2907","\u2908","\u2909","\u290a","\u290b","\u290c","\u290d","\u290e","\u290f","\u2910","\u2911","\u2912","\u2913","\u2914","\u2915","\u2916","\u2917","\u2918","\u2919","\u291a","\u291b","\u291c","\u291d","\u291e","\u291f","\u2920","\u2921","\u2922","\u2923","\u2924","\u2925","\u2926","\u2927","\u2928","\u2929","\u292a","\u292d","\u292e","\u292f","\u2930","\u2931","\u2932","\u2933","\u2934","\u2935","\u2936","\u2937","\u2938","\u2939","\u293a","\u293b","\u293c","\u293d","\u293e","\u293f","\u2940","\u2941","\u2942","\u2943","\u2944","\u2945","\u2946","\u2947","\u2948","\u2949","\u2970","\u2971","\u2972","\u2973","\u2974","\u2975","\u2976","\u2977","\u2978","\u2979","\u297a","\u297b","\u29b3","\u29b4","\u29bd","\u29ea","\u29ec","\u29ed","\u2a17","\u2b00","\u2b01","\u2b02","\u2b03","\u2b04","\u2b05","\u2b06","\u2b07","\u2b08","\u2b09","\u2b0a","\u2b0b","\u2b0c","\u2b0d","\u2b0e","\u2b0f","\u2b10","\u2b11","\u2b30","\u2b31","\u2b32","\u2b33","\u2b34","\u2b35","\u2b36","\u2b37","\u2b38","\u2b39","\u2b3a","\u2b3b","\u2b3c","\u2b3d","\u2b3e","\u2b3f","\u2b40","\u2b41","\u2b42","\u2b43","\u2b44","\u2b45","\u2b46","\u2b47","\u2b48","\u2b49","\u2b4a","\u2b4b","\u2b4c","\uffe9","\uffea","\uffeb","\uffec","\u21bc","\u21bd","\u21be","\u21bf","\u21c0","\u21c1","\u21c2","\u21c3","\u21cb","\u21cc","\u294a","\u294b","\u294c","\u294d","\u294e","\u294f","\u2950","\u2951","\u2952","\u2953","\u2954","\u2955","\u2956","\u2957","\u2958","\u2959","\u295a","\u295b","\u295c","\u295d","\u295e","\u295f","\u2960","\u2961","\u2962","\u2963","\u2964","\u2965","\u2966","\u2967","\u2968","\u2969","\u296a","\u296b","\u296c","\u296d","\u296e","\u296f","\u297c","\u297d","\u297e","\u297f"],type:"relation",role:"arrow"},{set:["\u2208","\u220a","\u22f2","\u22f3","\u22f4","\u22f5","\u22f6","\u22f7","\u22f8","\u22f9","\u22ff"],type:"operator",role:"element"},{set:["\u2209"],type:"operator",role:"nonelement"},{set:["\u220b","\u220d","\u22fa","\u22fb","\u22fc","\u22fd","\u22fe"],type:"operator",role:"reelement"},{set:["\u220c"],type:"operator",role:"renonelement"},{set:["\u2140","\u220f","\u2210","\u2211","\u22c0","\u22c1","\u22c2","\u22c3","\u2a00","\u2a01","\u2a02","\u2a03","\u2a04","\u2a05","\u2a06","\u2a07","\u2a08","\u2a09","\u2a0a","\u2a0b","\u2afc","\u2aff"],type:"largeop",role:"sum"},{set:["\u222b","\u222c","\u222d","\u222e","\u222f","\u2230","\u2231","\u2232","\u2233","\u2a0c","\u2a0d","\u2a0e","\u2a0f","\u2a10","\u2a11","\u2a12","\u2a13","\u2a14","\u2a15","\u2a16","\u2a17","\u2a18","\u2a19","\u2a1a","\u2a1b","\u2a1c"],type:"largeop",role:"integral"},{set:["\u221f","\u2220","\u2221","\u2222","\u22be","\u22bf","\u25b3","\u25b7","\u25bd","\u25c1"],type:"operator",role:"geometry"},{set:["inf","lim","liminf","limsup","max","min","sup","injlim","projlim","inj lim","proj lim"],type:"function",role:"limit function"},{set:be,type:"function",role:"prefix function"},{set:["mod","rem"],type:"operator",role:"prefix function"}],Se=function(){const e={};for(let t,n=0;t=ye[n];n++)t.set.forEach((function(n){e[n]={role:t.role||"unknown",type:t.type||"unknown",font:t.font||"unknown"}}));return e}();t.equal=function(e,t){return e.type===t.type&&e.role===t.role&&e.font===t.font},t.lookupType=function(e){var t;return(null===(t=Se[e])||void 0===t?void 0:t.type)||"unknown"},t.lookupRole=function(e){var t;return(null===(t=Se[e])||void 0===t?void 0:t.role)||"unknown"},t.lookupMeaning=function(e){return Se[e]||{role:"unknown",type:"unknown",font:"unknown"}},t.invisibleTimes=function(){return he},t.invisiblePlus=function(){return de},t.invisibleComma=function(){return n},t.functionApplication=function(){return fe},t.isMatchingFence=function(e,t){return-1!==p.indexOf(e)||-1!==h.indexOf(e)?e===t:s[e]===t||a[e]===t},t.isEmbellishedType=function(e){return"operator"===e||"relation"===e||"fence"===e||"punctuation"===e};const Ee=new Map;function Ne(e,t){return`${e} ${t}`}function Ae(e,t,n=""){for(const r of t)Ee.set(Ne(e,r),n||e)}Ae("d",["d","\u2146","\uff44","\ud835\udc1d","\ud835\udc51","\ud835\udcb9","\ud835\udced","\ud835\udd21","\ud835\udd55","\ud835\udd89","\ud835\uddbd","\ud835\uddf1","\ud835\ude25","\ud835\ude8d"]),Ae("bar",o),Ae("tilde",i),t.lookupSecondary=function(e,t){return Ee.get(Ne(e,t))}},7405:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticMeaningCollator=t.SemanticNodeCollator=t.SemanticDefault=void 0;const r=n(4020),o=n(178);class i{constructor(){this.map={}}static key(e,t){return t?e+":"+t:e}add(e,t){this.map[i.key(e,t.font)]=t}addNode(e){this.add(e.textContent,e.meaning())}retrieve(e,t){return this.map[i.key(e,t)]}retrieveNode(e){return this.retrieve(e.textContent,e.font)}size(){return Object.keys(this.map).length}}t.SemanticDefault=i;class s{constructor(){this.map={}}add(e,t){const n=this.map[e];n?n.push(t):this.map[e]=[t]}retrieve(e,t){return this.map[i.key(e,t)]}retrieveNode(e){return this.retrieve(e.textContent,e.font)}copy(){const e=this.copyCollator();for(const t in this.map)e.map[t]=this.map[t];return e}minimize(){for(const e in this.map)1===this.map[e].length&&delete this.map[e]}minimalCollator(){const e=this.copy();for(const t in e.map)1===e.map[t].length&&delete e.map[t];return e}isMultiValued(){for(const e in this.map)if(this.map[e].length>1)return!0;return!1}isEmpty(){return!Object.keys(this.map).length}}class a extends s{copyCollator(){return new a}add(e,t){const n=i.key(e,t.font);super.add(n,t)}addNode(e){this.add(e.textContent,e)}toString(){const e=[];for(const t in this.map){const n=Array(t.length+3).join(" "),r=this.map[t],o=[];for(let e,t=0;e=r[t];t++)o.push(e.toString());e.push(t+": "+o.join("\n"+n))}return e.join("\n")}collateMeaning(){const e=new c;for(const t in this.map)e.map[t]=this.map[t].map((function(e){return e.meaning()}));return e}}t.SemanticNodeCollator=a;class c extends s{copyCollator(){return new c}add(e,t){const n=this.retrieve(e,t.font);if(!n||!n.find((function(e){return r.equal(e,t)}))){const n=i.key(e,t.font);super.add(n,t)}}addNode(e){this.add(e.textContent,e.meaning())}toString(){const e=[];for(const t in this.map){const n=Array(t.length+3).join(" "),r=this.map[t],o=[];for(let e,t=0;e=r[t];t++)o.push("{type: "+e.type+", role: "+e.role+", font: "+e.font+"}");e.push(t+": "+o.join("\n"+n))}return e.join("\n")}reduce(){for(const e in this.map)1!==this.map[e].length&&(this.map[e]=(0,o.reduce)(this.map[e]))}default(){const e=new i;for(const t in this.map)1===this.map[t].length&&(e.map[t]=this.map[t][0]);return e}newDefault(){const e=this.default();this.reduce();const t=this.default();return e.size()!==t.size()?t:null}}t.SemanticMeaningCollator=c},5958:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticMultiHeuristic=t.SemanticTreeHeuristic=t.SemanticAbstractHeuristic=void 0;class n{constructor(e,t,n=(e=>!1)){this.name=e,this.apply=t,this.applicable=n}}t.SemanticAbstractHeuristic=n;t.SemanticTreeHeuristic=class extends n{};t.SemanticMultiHeuristic=class extends n{}},2721:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.lookup=t.run=t.add=t.blacklist=t.flags=t.updateFactory=t.factory=void 0,t.factory=null,t.updateFactory=function(e){t.factory=e};const n=new Map;function r(e){return n.get(e)}t.flags={combine_juxtaposition:!0,convert_juxtaposition:!0,multioperator:!0},t.blacklist={},t.add=function(e){const r=e.name;n.set(r,e),t.flags[r]||(t.flags[r]=!1)},t.run=function(e,n,o){const i=r(e);return i&&!t.blacklist[e]&&(t.flags[e]||i.applicable(n))?i.apply(n):o?o(n):n},t.lookup=r},7103:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(1984),o=n(4886),i=n(4020),s=n(2721),a=n(5958),c=n(6161),l=n(7793),u=n(8901);function d(e,t,n){let r=null;if(!e.length)return r;const o=n[n.length-1],i=o&&o.length,s=t&&t.length,a=l.default.getInstance();if(i&&s){if("infixop"===t[0].type&&"implicit"===t[0].role)return r=e.pop(),o.push(a.postfixNode_(o.pop(),e)),r;r=e.shift();const n=a.prefixNode_(t.shift(),e);return t.unshift(n),r}return i?(o.push(a.postfixNode_(o.pop(),e)),r):(s&&t.unshift(a.prefixNode_(t.shift(),e)),r)}function p(e,t,n){if(!t.length)return e;const o=e.pop(),i=t.shift(),a=n.shift();if(c.isImplicitOp(i)){r.Debugger.getInstance().output("Juxta Heuristic Case 2");const s=(o?[o,i]:[i]).concat(a);return p(e.concat(s),t,n)}if(!o)return r.Debugger.getInstance().output("Juxta Heuristic Case 3"),p([i].concat(a),t,n);const l=a.shift();if(!l){r.Debugger.getInstance().output("Juxta Heuristic Case 9");const a=s.factory.makeBranchNode("infixop",[o,t.shift()],[i],i.textContent);return a.role="implicit",s.run("combine_juxtaposition",a),t.unshift(a),p(e,t,n)}if(c.isOperator(o)||c.isOperator(l))return r.Debugger.getInstance().output("Juxta Heuristic Case 4"),p(e.concat([o,i,l]).concat(a),t,n);let u=null;return c.isImplicitOp(o)&&c.isImplicitOp(l)?(r.Debugger.getInstance().output("Juxta Heuristic Case 5"),o.contentNodes.push(i),o.contentNodes=o.contentNodes.concat(l.contentNodes),o.childNodes.push(l),o.childNodes=o.childNodes.concat(l.childNodes),l.childNodes.forEach((e=>e.parent=o)),i.parent=o,o.addMathmlNodes(i.mathml),o.addMathmlNodes(l.mathml),u=o):c.isImplicitOp(o)?(r.Debugger.getInstance().output("Juxta Heuristic Case 6"),o.contentNodes.push(i),o.childNodes.push(l),l.parent=o,i.parent=o,o.addMathmlNodes(i.mathml),o.addMathmlNodes(l.mathml),u=o):c.isImplicitOp(l)?(r.Debugger.getInstance().output("Juxta Heuristic Case 7"),l.contentNodes.unshift(i),l.childNodes.unshift(o),o.parent=l,i.parent=l,l.addMathmlNodes(i.mathml),l.addMathmlNodes(o.mathml),u=l):(r.Debugger.getInstance().output("Juxta Heuristic Case 8"),u=s.factory.makeBranchNode("infixop",[o,l],[i],i.textContent),u.role="implicit"),e.push(u),p(e.concat(a),t,n)}s.add(new a.SemanticTreeHeuristic("combine_juxtaposition",(function(e){for(let t,n=e.childNodes.length-1;t=e.childNodes[n];n--)c.isImplicitOp(t)&&!t.nobreaking&&(e.childNodes.splice(n,1,...t.childNodes),e.contentNodes.splice(n,0,...t.contentNodes),t.childNodes.concat(t.contentNodes).forEach((function(t){t.parent=e})),e.addMathmlNodes(t.mathml));return e}))),s.add(new a.SemanticTreeHeuristic("propagateSimpleFunction",(e=>("infixop"!==e.type&&"fraction"!==e.type||!e.childNodes.every(c.isSimpleFunction)||(e.role="composed function"),e)),(e=>"clearspeak"===o.default.getInstance().domain))),s.add(new a.SemanticTreeHeuristic("simpleNamedFunction",(e=>("unit"!==e.role&&-1!==["f","g","h","F","G","H"].indexOf(e.textContent)&&(e.role="simple function"),e)),(e=>"clearspeak"===o.default.getInstance().domain))),s.add(new a.SemanticTreeHeuristic("propagateComposedFunction",(e=>("fenced"===e.type&&"composed function"===e.childNodes[0].role&&(e.role="composed function"),e)),(e=>"clearspeak"===o.default.getInstance().domain))),s.add(new a.SemanticTreeHeuristic("multioperator",(e=>{if("unknown"!==e.role||e.textContent.length<=1)return;const t=[...e.textContent].map(i.lookupMeaning).reduce((function(e,t){return e&&t.role&&"unknown"!==t.role&&t.role!==e?"unknown"===e?t.role:null:e}),"unknown");t&&(e.role=t)}))),s.add(new a.SemanticMultiHeuristic("convert_juxtaposition",(e=>{let t=u.partitionNodes(e,(function(e){return e.textContent===i.invisibleTimes()&&"operator"===e.type}));t=t.rel.length?function(e){const t=[],n=[];let r=e.comp.shift(),o=null,i=[];for(;e.comp.length;)if(i=[],r.length)o&&t.push(o),n.push(r),o=e.rel.shift(),r=e.comp.shift();else{for(o&&i.push(o);!r.length&&e.comp.length;)r=e.comp.shift(),i.push(e.rel.shift());o=d(i,r,n)}i.length||r.length?(t.push(o),n.push(r)):(i.push(o),d(i,r,n));return{rel:t,comp:n}}(t):t,e=t.comp[0];for(let n,r,o=1;n=t.comp[o],r=t.rel[o-1];o++)e.push(r),e=e.concat(n);return t=u.partitionNodes(e,(function(e){return e.textContent===i.invisibleTimes()&&("operator"===e.type||"infixop"===e.type)})),t.rel.length?p(t.comp.shift(),t.rel,t.comp):e}))),s.add(new a.SemanticTreeHeuristic("simple2prefix",(e=>(e.textContent.length>1&&!e.textContent[0].match(/[A-Z]/)&&(e.role="prefix function"),e)),(e=>"braille"===o.default.getInstance().modality&&"identifier"===e.type))),s.add(new a.SemanticTreeHeuristic("detect_cycle",(e=>{e.type="matrix",e.role="cycle";const t=e.childNodes[0];return t.type="row",t.role="cycle",t.textContent="",t.contentNodes=[],e}),(e=>"fenced"===e.type&&"infixop"===e.childNodes[0].type&&"implicit"===e.childNodes[0].role&&e.childNodes[0].childNodes.every((function(e){return"number"===e.type}))&&e.childNodes[0].contentNodes.every((function(e){return"space"===e.role})))))},8122:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticMathml=void 0;const r=n(6671),o=n(6098),i=n(6161),s=n(7793),a=n(8901);class c extends o.SemanticAbstractParser{constructor(){super("MathML"),this.parseMap_={SEMANTICS:this.semantics_.bind(this),MATH:this.rows_.bind(this),MROW:this.rows_.bind(this),MPADDED:this.rows_.bind(this),MSTYLE:this.rows_.bind(this),MFRAC:this.fraction_.bind(this),MSUB:this.limits_.bind(this),MSUP:this.limits_.bind(this),MSUBSUP:this.limits_.bind(this),MOVER:this.limits_.bind(this),MUNDER:this.limits_.bind(this),MUNDEROVER:this.limits_.bind(this),MROOT:this.root_.bind(this),MSQRT:this.sqrt_.bind(this),MTABLE:this.table_.bind(this),MLABELEDTR:this.tableLabeledRow_.bind(this),MTR:this.tableRow_.bind(this),MTD:this.tableCell_.bind(this),MS:this.text_.bind(this),MTEXT:this.text_.bind(this),MSPACE:this.space_.bind(this),"ANNOTATION-XML":this.text_.bind(this),MI:this.identifier_.bind(this),MN:this.number_.bind(this),MO:this.operator_.bind(this),MFENCED:this.fenced_.bind(this),MENCLOSE:this.enclosed_.bind(this),MMULTISCRIPTS:this.multiscripts_.bind(this),ANNOTATION:this.empty_.bind(this),NONE:this.empty_.bind(this),MACTION:this.action_.bind(this)};const e={type:"identifier",role:"numbersetletter",font:"double-struck"};["C","H","N","P","Q","R","Z","\u2102","\u210d","\u2115","\u2119","\u211a","\u211d","\u2124"].forEach((t=>this.getFactory().defaultMap.add(t,e)).bind(this))}static getAttribute_(e,t,n){if(!e.hasAttribute(t))return n;const r=e.getAttribute(t);return r.match(/^\s*$/)?null:r}parse(e){s.default.getInstance().setNodeFactory(this.getFactory());const t=r.toArray(e.childNodes),n=r.tagName(e),o=this.parseMap_[n],i=(o||this.dummy_.bind(this))(e,t);return a.addAttributes(i,e),-1!==["MATH","MROW","MPADDED","MSTYLE","SEMANTICS"].indexOf(n)||(i.mathml.unshift(e),i.mathmlTree=e),i}semantics_(e,t){return t.length?this.parse(t[0]):this.getFactory().makeEmptyNode()}rows_(e,t){const n=e.getAttribute("semantics");if(n&&n.match("bspr_"))return s.default.proof(e,n,this.parseList.bind(this));let r;return 1===(t=a.purgeNodes(t)).length?(r=this.parse(t[0]),"empty"!==r.type||r.mathmlTree||(r.mathmlTree=e)):r=s.default.getInstance().row(this.parseList(t)),r.mathml.unshift(e),r}fraction_(e,t){if(!t.length)return this.getFactory().makeEmptyNode();const n=this.parse(t[0]),r=t[1]?this.parse(t[1]):this.getFactory().makeEmptyNode();return s.default.getInstance().fractionLikeNode(n,r,e.getAttribute("linethickness"),"true"===e.getAttribute("bevelled"))}limits_(e,t){return s.default.getInstance().limitNode(r.tagName(e),this.parseList(t))}root_(e,t){return t[1]?this.getFactory().makeBranchNode("root",[this.parse(t[1]),this.parse(t[0])],[]):this.sqrt_(e,t)}sqrt_(e,t){const n=this.parseList(a.purgeNodes(t));return this.getFactory().makeBranchNode("sqrt",[s.default.getInstance().row(n)],[])}table_(e,t){const n=e.getAttribute("semantics");if(n&&n.match("bspr_"))return s.default.proof(e,n,this.parseList.bind(this));const r=this.getFactory().makeBranchNode("table",this.parseList(t),[]);return r.mathmlTree=e,s.default.tableToMultiline(r),r}tableRow_(e,t){const n=this.getFactory().makeBranchNode("row",this.parseList(t),[]);return n.role="table",n}tableLabeledRow_(e,t){if(!t.length)return this.tableRow_(e,t);const n=this.parse(t[0]);n.role="label";const r=this.getFactory().makeBranchNode("row",this.parseList(t.slice(1)),[n]);return r.role="table",r}tableCell_(e,t){const n=this.parseList(a.purgeNodes(t));let r;r=n.length?1===n.length&&i.isType(n[0],"empty")?n:[s.default.getInstance().row(n)]:[];const o=this.getFactory().makeBranchNode("cell",r,[]);return o.role="table",o}space_(e,t){const n=e.getAttribute("width"),o=n&&n.match(/[a-z]*$/);if(!o)return this.empty_(e,t);const i=o[0],a=parseFloat(n.slice(0,o.index)),c={cm:.4,pc:.5,em:.5,ex:1,in:.15,pt:5,mm:5}[i];if(!c||isNaN(a)||a<c)return this.empty_(e,t);const l=this.getFactory().makeUnprocessed(e);return s.default.getInstance().text(l,r.tagName(e))}text_(e,t){const n=this.leaf_(e,t);return e.textContent?(n.updateContent(e.textContent,!0),s.default.getInstance().text(n,r.tagName(e))):n}identifier_(e,t){const n=this.leaf_(e,t);return s.default.getInstance().identifierNode(n,s.default.getInstance().font(e.getAttribute("mathvariant")),e.getAttribute("class"))}number_(e,t){const n=this.leaf_(e,t);return s.default.number(n),n}operator_(e,t){const n=this.leaf_(e,t);return s.default.getInstance().operatorNode(n),n}fenced_(e,t){const n=this.parseList(a.purgeNodes(t)),r=c.getAttribute_(e,"separators",","),o=c.getAttribute_(e,"open","("),i=c.getAttribute_(e,"close",")"),l=s.default.getInstance().mfenced(o,i,r,n);return s.default.getInstance().tablesInRow([l])[0]}enclosed_(e,t){const n=this.parseList(a.purgeNodes(t)),r=this.getFactory().makeBranchNode("enclose",[s.default.getInstance().row(n)],[]);return r.role=e.getAttribute("notation")||"unknown",r}multiscripts_(e,t){if(!t.length)return this.getFactory().makeEmptyNode();const n=this.parse(t.shift());if(!t.length)return n;const o=[],i=[],c=[],l=[];let u=!1,d=0;for(let e,n=0;e=t[n];n++)"MPRESCRIPTS"!==r.tagName(e)?(u?1&d?o.push(e):i.push(e):1&d?c.push(e):l.push(e),d++):(u=!0,d=0);return a.purgeNodes(o).length||a.purgeNodes(i).length?s.default.getInstance().tensor(n,this.parseList(i),this.parseList(o),this.parseList(l),this.parseList(c)):s.default.getInstance().pseudoTensor(n,this.parseList(l),this.parseList(c))}empty_(e,t){return this.getFactory().makeEmptyNode()}action_(e,t){return t.length>1?this.parse(t[1]):this.getFactory().makeUnprocessed(e)}dummy_(e,t){const n=this.getFactory().makeUnprocessed(e);return n.role=e.tagName,n.textContent=e.textContent,n}leaf_(e,t){if(1===t.length&&t[0].nodeType!==r.NodeType.TEXT_NODE){const n=this.getFactory().makeUnprocessed(e);return n.role=t[0].tagName,a.addAttributes(n,t[0]),n}return this.getFactory().makeLeafNode(e.textContent,s.default.getInstance().font(e.getAttribute("mathvariant")))}}t.SemanticMathml=c},9444:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticNode=void 0;const r=n(6671),o=n(4020),i=n(8901);class s{constructor(e){this.id=e,this.mathml=[],this.parent=null,this.type="unknown",this.role="unknown",this.font="unknown",this.embellished=null,this.fencePointer="",this.childNodes=[],this.textContent="",this.mathmlTree=null,this.contentNodes=[],this.annotation={},this.attributes={},this.nobreaking=!1}static fromXml(e){const t=parseInt(e.getAttribute("id"),10),n=new s(t);return n.type=e.tagName,s.setAttribute(n,e,"role"),s.setAttribute(n,e,"font"),s.setAttribute(n,e,"embellished"),s.setAttribute(n,e,"fencepointer","fencePointer"),e.getAttribute("annotation")&&n.parseAnnotation(e.getAttribute("annotation")),i.addAttributes(n,e),s.processChildren(n,e),n}static setAttribute(e,t,n,r){r=r||n;const o=t.getAttribute(n);o&&(e[r]=o)}static processChildren(e,t){for(const n of r.toArray(t.childNodes)){if(n.nodeType===r.NodeType.TEXT_NODE){e.textContent=n.textContent;continue}const t=r.toArray(n.childNodes).map(s.fromXml);t.forEach((t=>t.parent=e)),"CONTENT"===r.tagName(n)?e.contentNodes=t:e.childNodes=t}}querySelectorAll(e){let t=[];for(let n,r=0;n=this.childNodes[r];r++)t=t.concat(n.querySelectorAll(e));for(let n,r=0;n=this.contentNodes[r];r++)t=t.concat(n.querySelectorAll(e));return e(this)&&t.unshift(this),t}xml(e,t){const n=function(n,r){const o=r.map((function(n){return n.xml(e,t)})),i=e.createElementNS("",n);for(let e,t=0;e=o[t];t++)i.appendChild(e);return i},r=e.createElementNS("",this.type);return t||this.xmlAttributes(r),r.textContent=this.textContent,this.contentNodes.length>0&&r.appendChild(n("content",this.contentNodes)),this.childNodes.length>0&&r.appendChild(n("children",this.childNodes)),r}toString(e=!1){const t=r.parseInput("<snode/>");return r.serializeXml(this.xml(t,e))}allAttributes(){const e=[];return e.push(["role",this.role]),"unknown"!==this.font&&e.push(["font",this.font]),Object.keys(this.annotation).length&&e.push(["annotation",this.xmlAnnotation()]),this.embellished&&e.push(["embellished",this.embellished]),this.fencePointer&&e.push(["fencepointer",this.fencePointer]),e.push(["id",this.id.toString()]),e}xmlAnnotation(){const e=[];for(const t in this.annotation)this.annotation[t].forEach((function(n){e.push(t+":"+n)}));return e.join(";")}toJson(){const e={};e.type=this.type;const t=this.allAttributes();for(let n,r=0;n=t[r];r++)e[n[0]]=n[1].toString();return this.textContent&&(e.$t=this.textContent),this.childNodes.length&&(e.children=this.childNodes.map((function(e){return e.toJson()}))),this.contentNodes.length&&(e.content=this.contentNodes.map((function(e){return e.toJson()}))),e}updateContent(e,t){const n=t?e.replace(/^[ \f\n\r\t\v\u200b]*/,"").replace(/[ \f\n\r\t\v\u200b]*$/,""):e.trim();if(e=e&&!n?e:n,this.textContent===e)return;const r=(0,o.lookupMeaning)(e);this.textContent=e,this.role=r.role,this.type=r.type,this.font=r.font}addMathmlNodes(e){for(let t,n=0;t=e[n];n++)-1===this.mathml.indexOf(t)&&this.mathml.push(t)}appendChild(e){this.childNodes.push(e),this.addMathmlNodes(e.mathml),e.parent=this}replaceChild(e,t){const n=this.childNodes.indexOf(e);if(-1===n)return;e.parent=null,t.parent=this,this.childNodes[n]=t;const r=e.mathml.filter((function(e){return-1===t.mathml.indexOf(e)})),o=t.mathml.filter((function(t){return-1===e.mathml.indexOf(t)}));this.removeMathmlNodes(r),this.addMathmlNodes(o)}appendContentNode(e){e&&(this.contentNodes.push(e),this.addMathmlNodes(e.mathml),e.parent=this)}removeContentNode(e){if(e){const t=this.contentNodes.indexOf(e);-1!==t&&this.contentNodes.slice(t,1)}}equals(e){if(!e)return!1;if(this.type!==e.type||this.role!==e.role||this.textContent!==e.textContent||this.childNodes.length!==e.childNodes.length||this.contentNodes.length!==e.contentNodes.length)return!1;for(let t,n,r=0;t=this.childNodes[r],n=e.childNodes[r];r++)if(!t.equals(n))return!1;for(let t,n,r=0;t=this.contentNodes[r],n=e.contentNodes[r];r++)if(!t.equals(n))return!1;return!0}displayTree(){console.info(this.displayTree_(0))}addAnnotation(e,t){t&&this.addAnnotation_(e,t)}getAnnotation(e){const t=this.annotation[e];return t||[]}hasAnnotation(e,t){const n=this.annotation[e];return!!n&&-1!==n.indexOf(t)}parseAnnotation(e){const t=e.split(";");for(let e=0,n=t.length;e<n;e++){const n=t[e].split(":");this.addAnnotation(n[0],n[1])}}meaning(){return{type:this.type,role:this.role,font:this.font}}xmlAttributes(e){const t=this.allAttributes();for(let n,r=0;n=t[r];r++)e.setAttribute(n[0],n[1]);this.addExternalAttributes(e)}addExternalAttributes(e){for(const t in this.attributes)e.setAttribute(t,this.attributes[t])}removeMathmlNodes(e){const t=this.mathml;for(let n,r=0;n=e[r];r++){const e=t.indexOf(n);-1!==e&&t.splice(e,1)}this.mathml=t}displayTree_(e){e++;const t=Array(e).join("  ");let n="";n+="\n"+t+this.toString(),n+="\n"+t+"MathmlTree:",n+="\n"+t+this.mathmlTreeString(),n+="\n"+t+"MathML:";for(let e,r=0;e=this.mathml[r];r++)n+="\n"+t+e.toString();return n+="\n"+t+"Begin Content",this.contentNodes.forEach((function(t){n+=t.displayTree_(e)})),n+="\n"+t+"End Content",n+="\n"+t+"Begin Children",this.childNodes.forEach((function(t){n+=t.displayTree_(e)})),n+="\n"+t+"End Children",n}mathmlTreeString(){return this.mathmlTree?this.mathmlTree.toString():"EMPTY"}addAnnotation_(e,t){const n=this.annotation[e];n?n.push(t):this.annotation[e]=[t]}}t.SemanticNode=s},4790:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticNodeFactory=void 0;const r=n(7405),o=n(7405),i=n(9444);t.SemanticNodeFactory=class{constructor(){this.leafMap=new o.SemanticNodeCollator,this.defaultMap=new r.SemanticDefault,this.idCounter_=-1}makeNode(e){return this.createNode_(e)}makeUnprocessed(e){const t=this.createNode_();return t.mathml=[e],t.mathmlTree=e,t}makeEmptyNode(){const e=this.createNode_();return e.type="empty",e}makeContentNode(e){const t=this.createNode_();return t.updateContent(e),t}makeMultipleContentNodes(e,t){const n=[];for(let r=0;r<e;r++)n.push(this.makeContentNode(t));return n}makeLeafNode(e,t){if(!e)return this.makeEmptyNode();const n=this.makeContentNode(e);n.font=t||n.font;const r=this.defaultMap.retrieveNode(n);return r&&(n.type=r.type,n.role=r.role,n.font=r.font),this.leafMap.addNode(n),n}makeBranchNode(e,t,n,r){const o=this.createNode_();return r&&o.updateContent(r),o.type=e,o.childNodes=t,o.contentNodes=n,t.concat(n).forEach((function(e){e.parent=o,o.addMathmlNodes(e.mathml)})),o}createNode_(e){return void 0!==e?this.idCounter_=Math.max(this.idCounter_,e):e=++this.idCounter_,new i.SemanticNode(e)}}},178:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticComparator=t.reduce=t.sort=t.apply=t.add=void 0;const n=[];function r(e){n.push(e)}function o(e,t){for(let r,o=0;r=n[o];o++){const n=r.compare(e,t);if(0!==n)return n}return 0}function i(e){e.sort(o)}t.add=r,t.apply=o,t.sort=i,t.reduce=function(e){if(e.length<=1)return e;const t=e.slice();i(t);const n=[];let r;do{r=t.pop(),n.push(r)}while(r&&t.length&&0===o(t[t.length-1],r));return n};class s{constructor(e,t=null){this.comparator=e,this.type=t,r(this)}compare(e,t){return this.type&&this.type===e.type&&this.type===t.type?this.comparator(e,t):0}}t.SemanticComparator=s,new s((function(e,t){return"simple function"===e.role?1:"simple function"===t.role?-1:0}),"identifier")},6098:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticAbstractParser=void 0;const r=n(4790);t.SemanticAbstractParser=class{constructor(e){this.type=e,this.factory_=new r.SemanticNodeFactory}getFactory(){return this.factory_}setFactory(e){this.factory_=e}getType(){return this.type}parseList(e){const t=[];for(let n,r=0;n=e[r];r++)t.push(this.parse(n));return t}}},6161:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.isMembership=t.elligibleRightNeutral=t.elligibleLeftNeutral=t.compareNeutralFences=t.isNeutralFence=t.isImplicitOp=t.isImplicit=t.isPureUnit=t.isUnitCounter=t.isNumber=t.isSingletonSetContent=t.scriptedElement_=t.illegalSingleton_=t.isSetNode=t.isRightBrace=t.isLeftBrace=t.isSimpleFunction=t.singlePunctAtPosition=t.isSimpleFunctionHead=t.isLimitBase=t.isBinomial=t.lineIsLabelled=t.tableIsMultiline=t.tableIsCases=t.isFencedElement=t.tableIsMatrixOrVector=t.isTableOrMultiline=t.isElligibleEmbellishedFence=t.isFence=t.isPunctuation=t.isRelation=t.isOperator=t.isEmbellished=t.isGeneralFunctionBoundary=t.isIntegralDxBoundarySingle=t.isIntegralDxBoundary=t.isBigOpBoundary=t.isPrefixFunctionBoundary=t.isSimpleFunctionScope=t.isAccent=t.isRole=t.embellishedType=t.isType=void 0;const r=n(4020),o=n(8901);function i(e,t){return e.type===t}function s(e,t){return e.embellished===t}function a(e,t){return e.role===t}function c(e){return u(e)||d(e)}function l(e){return i(e,"operator")||s(e,"operator")}function u(e){return i(e,"relation")||s(e,"relation")}function d(e){return i(e,"punctuation")||s(e,"punctuation")}function p(e){return i(e,"fence")||s(e,"fence")}function h(e){return!e.embellished||!function(e){return i(e,"tensor")&&(!i(e.childNodes[1],"empty")||!i(e.childNodes[2],"empty"))&&(!i(e.childNodes[3],"empty")||!i(e.childNodes[4],"empty"))}(e)&&((!a(e,"close")||!i(e,"tensor"))&&((!a(e,"open")||!i(e,"subscript")&&!i(e,"superscript"))&&h(e.childNodes[0])))}function f(e){return!!e&&(i(e,"table")||i(e,"multiline"))}function m(e){return!!e&&i(e,"fenced")&&(a(e,"leftright")||S(e))&&1===e.childNodes.length}function g(e){return!!e&&-1!==["{","\ufe5b","\uff5b"].indexOf(e.textContent)}function b(e){return!!e&&-1!==["}","\ufe5c","\uff5d"].indexOf(e.textContent)}function y(e){return"number"===e.type&&("integer"===e.role||"float"===e.role)}function S(e){return"neutral"===e.role||"metric"===e.role}t.isType=i,t.embellishedType=s,t.isRole=a,t.isAccent=function(e){const t=new RegExp("\u221e|\u1ab2");return i(e,"fence")||i(e,"punctuation")||i(e,"operator")&&!e.textContent.match(t)||i(e,"relation")||i(e,"identifier")&&a(e,"unknown")&&!e.textContent.match(r.allLettersRegExp)&&!e.textContent.match(t)},t.isSimpleFunctionScope=function(e){const t=e.childNodes;if(0===t.length)return!0;if(t.length>1)return!1;const n=t[0];if("infixop"===n.type){if("implicit"!==n.role)return!1;if(n.childNodes.some((e=>i(e,"infixop"))))return!1}return!0},t.isPrefixFunctionBoundary=function(e){return l(e)&&!a(e,"division")||i(e,"appl")||c(e)},t.isBigOpBoundary=function(e){return l(e)||c(e)},t.isIntegralDxBoundary=function(e,t){return!!t&&i(t,"identifier")&&r.lookupSecondary("d",e.textContent)},t.isIntegralDxBoundarySingle=function(e){if(i(e,"identifier")){const t=e.textContent[0];return t&&e.textContent[1]&&r.lookupSecondary("d",t)}return!1},t.isGeneralFunctionBoundary=c,t.isEmbellished=function(e){return e.embellished?e.embellished:r.isEmbellishedType(e.type)?e.type:null},t.isOperator=l,t.isRelation=u,t.isPunctuation=d,t.isFence=p,t.isElligibleEmbellishedFence=function(e){return!(!e||!p(e))&&(!e.embellished||h(e))},t.isTableOrMultiline=f,t.tableIsMatrixOrVector=function(e){return!!e&&m(e)&&f(e.childNodes[0])},t.isFencedElement=m,t.tableIsCases=function(e,t){return t.length>0&&a(t[t.length-1],"openfence")},t.tableIsMultiline=function(e){return e.childNodes.every((function(e){return e.childNodes.length<=1}))},t.lineIsLabelled=function(e){return i(e,"line")&&e.contentNodes.length&&a(e.contentNodes[0],"label")},t.isBinomial=function(e){return 2===e.childNodes.length},t.isLimitBase=function e(t){return i(t,"largeop")||i(t,"limboth")||i(t,"limlower")||i(t,"limupper")||i(t,"function")&&a(t,"limit function")||(i(t,"overscore")||i(t,"underscore"))&&e(t.childNodes[0])},t.isSimpleFunctionHead=function(e){return"identifier"===e.type||"latinletter"===e.role||"greekletter"===e.role||"otherletter"===e.role},t.singlePunctAtPosition=function(e,t,n){return 1===t.length&&("punctuation"===e[n].type||"punctuation"===e[n].embellished)&&e[n]===t[0]},t.isSimpleFunction=function(e){return i(e,"identifier")&&a(e,"simple function")},t.isLeftBrace=g,t.isRightBrace=b,t.isSetNode=function(e){return g(e.contentNodes[0])&&b(e.contentNodes[1])},t.illegalSingleton_=["punctuation","punctuated","relseq","multirel","table","multiline","cases","inference"],t.scriptedElement_=["limupper","limlower","limboth","subscript","superscript","underscore","overscore","tensor"],t.isSingletonSetContent=function e(n){const r=n.type;return-1===t.illegalSingleton_.indexOf(r)&&("infixop"!==r||"implicit"===n.role)&&("fenced"===r?"leftright"!==n.role||e(n.childNodes[0]):-1===t.scriptedElement_.indexOf(r)||e(n.childNodes[0]))},t.isNumber=y,t.isUnitCounter=function(e){return y(e)||"vulgar"===e.role||"mixed"===e.role},t.isPureUnit=function(e){const t=e.childNodes;return"unit"===e.role&&(!t.length||"unit"===t[0].role)},t.isImplicit=function(e){return"implicit"===e.role||"unit"===e.role&&!!e.contentNodes.length&&e.contentNodes[0].textContent===r.invisibleTimes()},t.isImplicitOp=function(e){return"infixop"===e.type&&"implicit"===e.role},t.isNeutralFence=S,t.compareNeutralFences=function(e,t){return S(e)&&S(t)&&(0,o.getEmbellishedInner)(e).textContent===(0,o.getEmbellishedInner)(t).textContent},t.elligibleLeftNeutral=function(e){return!!S(e)&&(!e.embellished||"superscript"!==e.type&&"subscript"!==e.type&&("tensor"!==e.type||"empty"===e.childNodes[3].type&&"empty"===e.childNodes[4].type))},t.elligibleRightNeutral=function(e){return!!S(e)&&(!e.embellished||("tensor"!==e.type||"empty"===e.childNodes[1].type&&"empty"===e.childNodes[2].type))},t.isMembership=function(e){return["element","nonelement","reelement","renonelement"].includes(e.role)}},7793:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});const r=n(6671),o=n(4020),i=n(2721),s=n(4790),a=n(6161),c=n(8901);class l{constructor(){this.funcAppls={},this.factory_=new s.SemanticNodeFactory,i.updateFactory(this.factory_)}static getInstance(){return l.instance=l.instance||new l,l.instance}static tableToMultiline(e){if(a.tableIsMultiline(e)){e.type="multiline";for(let t,n=0;t=e.childNodes[n];n++)l.rowToLine_(t,"multiline");1===e.childNodes.length&&!a.lineIsLabelled(e.childNodes[0])&&a.isFencedElement(e.childNodes[0].childNodes[0])&&l.tableToMatrixOrVector_(l.rewriteFencedLine_(e)),l.binomialForm_(e),l.classifyMultiline(e)}else l.classifyTable(e)}static number(e){"unknown"!==e.type&&"identifier"!==e.type||(e.type="number"),l.numberRole_(e),l.exprFont_(e)}static classifyMultiline(e){let t=0;const n=e.childNodes.length;let r;for(;t<n&&(!(r=e.childNodes[t])||!r.childNodes.length);)t++;if(t>=n)return;const o=r.childNodes[0].role;"unknown"!==o&&e.childNodes.every((function(e){const t=e.childNodes[0];return!t||t.role===o&&(a.isType(t,"relation")||a.isType(t,"relseq"))}))&&(e.role=o)}static classifyTable(e){const t=l.computeColumns_(e);l.classifyByColumns_(e,t,"equality")||l.classifyByColumns_(e,t,"inequality",["equality"])||l.classifyByColumns_(e,t,"arrow")||l.detectCaleyTable(e)}static detectCaleyTable(e){if(!e.mathmlTree)return!1;const t=e.mathmlTree,n=t.getAttribute("columnlines"),r=t.getAttribute("rowlines");return!(!n||!r)&&(!(!l.cayleySpacing(n)||!l.cayleySpacing(r))&&(e.role="cayley",!0))}static cayleySpacing(e){const t=e.split(" ");return("solid"===t[0]||"dashed"===t[0])&&t.slice(1).every((e=>"none"===e))}static proof(e,t,n){const r=l.separateSemantics(t);return l.getInstance().proof(e,r,n)}static findSemantics(e,t,n){const r=null==n?null:n,o=l.getSemantics(e);return!!o&&(!!o[t]&&(null==r||o[t]===r))}static getSemantics(e){const t=e.getAttribute("semantics");return t?l.separateSemantics(t):null}static removePrefix(e){const[,...t]=e.split("_");return t.join("_")}static separateSemantics(e){const t={};return e.split(";").forEach((function(e){const[n,r]=e.split(":");t[l.removePrefix(n)]=r})),t}static matchSpaces_(e,t){for(let n,r=0;n=t[r];r++){const t=e[r].mathmlTree,o=e[r+1].mathmlTree;if(!t||!o)continue;const i=t.nextSibling;if(!i||i===o)continue;const s=l.getSpacer_(i);s&&(n.mathml.push(s),n.mathmlTree=s,n.role="space")}}static getSpacer_(e){if("MSPACE"===r.tagName(e))return e;for(;c.hasEmptyTag(e)&&1===e.childNodes.length;)if(e=e.childNodes[0],"MSPACE"===r.tagName(e))return e;return null}static fenceToPunct_(e){const t=l.FENCE_TO_PUNCT_[e.role];if(t){for(;e.embellished;)e.embellished="punctuation",a.isRole(e,"subsup")||a.isRole(e,"underover")||(e.role=t),e=e.childNodes[0];e.type="punctuation",e.role=t}}static classifyFunction_(e,t){if("appl"===e.type||"bigop"===e.type||"integral"===e.type)return"";if(t[0]&&t[0].textContent===o.functionApplication()){l.getInstance().funcAppls[e.id]=t.shift();let n="simple function";return i.run("simple2prefix",e),"prefix function"!==e.role&&"limit function"!==e.role||(n=e.role),l.propagateFunctionRole_(e,n),"prefix"}const n=l.CLASSIFY_FUNCTION_[e.role];return n||(a.isSimpleFunctionHead(e)?"simple":"")}static propagateFunctionRole_(e,t){if(e){if("infixop"===e.type)return;a.isRole(e,"subsup")||a.isRole(e,"underover")||(e.role=t),l.propagateFunctionRole_(e.childNodes[0],t)}}static getFunctionOp_(e,t){if(t(e))return e;for(let n,r=0;n=e.childNodes[r];r++){const e=l.getFunctionOp_(n,t);if(e)return e}return null}static tableToMatrixOrVector_(e){const t=e.childNodes[0];a.isType(t,"multiline")?l.tableToVector_(e):l.tableToMatrix_(e),e.contentNodes.forEach(t.appendContentNode.bind(t));for(let e,n=0;e=t.childNodes[n];n++)l.assignRoleToRow_(e,l.getComponentRoles_(t));return t.parent=null,t}static tableToVector_(e){const t=e.childNodes[0];t.type="vector",1!==t.childNodes.length?l.binomialForm_(t):l.tableToSquare_(e)}static binomialForm_(e){a.isBinomial(e)&&(e.role="binomial",e.childNodes[0].role="binomial",e.childNodes[1].role="binomial")}static tableToMatrix_(e){const t=e.childNodes[0];t.type="matrix",t.childNodes&&t.childNodes.length>0&&t.childNodes[0].childNodes&&t.childNodes.length===t.childNodes[0].childNodes.length?l.tableToSquare_(e):t.childNodes&&1===t.childNodes.length&&(t.role="rowvector")}static tableToSquare_(e){const t=e.childNodes[0];a.isNeutralFence(e)?t.role="determinant":t.role="squarematrix"}static getComponentRoles_(e){const t=e.role;return t&&"unknown"!==t?t:e.type.toLowerCase()||"unknown"}static tableToCases_(e,t){for(let t,n=0;t=e.childNodes[n];n++)l.assignRoleToRow_(t,"cases");return e.type="cases",e.appendContentNode(t),a.tableIsMultiline(e)&&l.binomialForm_(e),e}static rewriteFencedLine_(e){const t=e.childNodes[0],n=e.childNodes[0].childNodes[0],r=e.childNodes[0].childNodes[0].childNodes[0];return n.parent=e.parent,e.parent=n,r.parent=t,n.childNodes=[e],t.childNodes=[r],n}static rowToLine_(e,t){const n=t||"unknown";a.isType(e,"row")&&(e.type="line",e.role=n,1===e.childNodes.length&&a.isType(e.childNodes[0],"cell")&&(e.childNodes=e.childNodes[0].childNodes,e.childNodes.forEach((function(t){t.parent=e}))))}static assignRoleToRow_(e,t){a.isType(e,"line")?e.role=t:a.isType(e,"row")&&(e.role=t,e.childNodes.forEach((function(e){a.isType(e,"cell")&&(e.role=t)})))}static nextSeparatorFunction_(e){let t;if(e){if(e.match(/^\s+$/))return null;t=e.replace(/\s/g,"").split("").filter((function(e){return e}))}else t=[","];return function(){return t.length>1?t.shift():t[0]}}static numberRole_(e){if("unknown"!==e.role)return;const t=[...e.textContent].filter((e=>e.match(/[^\s]/))),n=t.map(o.lookupMeaning);if(n.every((function(e){return"number"===e.type&&"integer"===e.role||"punctuation"===e.type&&"comma"===e.role})))return e.role="integer",void("0"===t[0]&&e.addAnnotation("general","basenumber"));n.every((function(e){return"number"===e.type&&"integer"===e.role||"punctuation"===e.type}))?e.role="float":e.role="othernumber"}static exprFont_(e){if("unknown"!==e.font)return;const t=[...e.textContent].map(o.lookupMeaning).reduce((function(e,t){return e&&t.font&&"unknown"!==t.font&&t.font!==e?"unknown"===e?t.font:null:e}),"unknown");t&&(e.font=t)}static purgeFences_(e){const t=e.rel,n=e.comp,r=[],o=[];for(;t.length>0;){const e=t.shift();let i=n.shift();a.isElligibleEmbellishedFence(e)?(r.push(e),o.push(i)):(l.fenceToPunct_(e),i.push(e),i=i.concat(n.shift()),n.unshift(i))}return o.push(n.shift()),{rel:r,comp:o}}static rewriteFencedNode_(e){const t=e.contentNodes[0],n=e.contentNodes[1];let r=l.rewriteFence_(e,t);return e.contentNodes[0]=r.fence,r=l.rewriteFence_(r.node,n),e.contentNodes[1]=r.fence,e.contentNodes[0].parent=e,e.contentNodes[1].parent=e,r.node.parent=null,r.node}static rewriteFence_(e,t){if(!t.embellished)return{node:e,fence:t};const n=t.childNodes[0],r=l.rewriteFence_(e,n);return a.isType(t,"superscript")||a.isType(t,"subscript")||a.isType(t,"tensor")?(a.isRole(t,"subsup")||(t.role=e.role),n!==r.node&&(t.replaceChild(n,r.node),n.parent=e),l.propagateFencePointer_(t,n),{node:t,fence:r.fence}):(t.replaceChild(n,r.fence),t.mathmlTree&&-1===t.mathml.indexOf(t.mathmlTree)&&t.mathml.push(t.mathmlTree),{node:r.node,fence:t})}static propagateFencePointer_(e,t){e.fencePointer=t.fencePointer||t.id.toString(),e.embellished=null}static classifyByColumns_(e,t,n,r){return!!(3===t.length&&l.testColumns_(t,1,(e=>l.isPureRelation_(e,n)))||2===t.length&&(l.testColumns_(t,1,(e=>l.isEndRelation_(e,n)||l.isPureRelation_(e,n)))||l.testColumns_(t,0,(e=>l.isEndRelation_(e,n,!0)||l.isPureRelation_(e,n)))))&&(e.role=n,!0)}static isEndRelation_(e,t,n){const r=n?e.childNodes.length-1:0;return a.isType(e,"relseq")&&a.isRole(e,t)&&a.isType(e.childNodes[r],"empty")}static isPureRelation_(e,t){return a.isType(e,"relation")&&a.isRole(e,t)}static computeColumns_(e){const t=[];for(let n,r=0;n=e.childNodes[r];r++)for(let e,r=0;e=n.childNodes[r];r++){t[r]?t[r].push(e):t[r]=[e]}return t}static testColumns_(e,t,n){const r=e[t];return!!r&&(r.some((function(e){return e.childNodes.length&&n(e.childNodes[0])}))&&r.every((function(e){return!e.childNodes.length||n(e.childNodes[0])})))}setNodeFactory(e){l.getInstance().factory_=e,i.updateFactory(l.getInstance().factory_)}getNodeFactory(){return l.getInstance().factory_}identifierNode(e,t,n){if("MathML-Unit"===n)e.type="identifier",e.role="unit";else if(!t&&1===e.textContent.length&&("integer"===e.role||"latinletter"===e.role||"greekletter"===e.role)&&"normal"===e.font)return e.font="italic",i.run("simpleNamedFunction",e);return"unknown"===e.type&&(e.type="identifier"),l.exprFont_(e),i.run("simpleNamedFunction",e)}implicitNode(e){if(e=l.getInstance().getMixedNumbers_(e),1===(e=l.getInstance().combineUnits_(e)).length)return e[0];const t=l.getInstance().implicitNode_(e);return i.run("combine_juxtaposition",t)}text(e,t){return l.exprFont_(e),e.type="text","MS"===t?(e.role="string",e):"MSPACE"===t||e.textContent.match(/^\s*$/)?(e.role="space",e):e}row(e){return 0===(e=e.filter((function(e){return!a.isType(e,"empty")}))).length?l.getInstance().factory_.makeEmptyNode():(e=l.getInstance().getFencesInRow_(e),e=l.getInstance().tablesInRow(e),e=l.getInstance().getPunctuationInRow_(e),e=l.getInstance().getTextInRow_(e),e=l.getInstance().getFunctionsInRow_(e),l.getInstance().relationsInRow_(e))}limitNode(e,t){if(!t.length)return l.getInstance().factory_.makeEmptyNode();let n,r=t[0],o="unknown";if(!t[1])return r;if(a.isLimitBase(r)){n=l.MML_TO_LIMIT_[e];const i=n.length;if(o=n.type,t=t.slice(0,n.length+1),1===i&&a.isAccent(t[1])||2===i&&a.isAccent(t[1])&&a.isAccent(t[2]))return n=l.MML_TO_BOUNDS_[e],l.getInstance().accentNode_(r,t,n.type,n.length,n.accent);if(2===i){if(a.isAccent(t[1]))return r=l.getInstance().accentNode_(r,[r,t[1]],{MSUBSUP:"subscript",MUNDEROVER:"underscore"}[e],1,!0),t[2]?l.getInstance().makeLimitNode_(r,[r,t[2]],null,"limupper"):r;if(t[2]&&a.isAccent(t[2]))return r=l.getInstance().accentNode_(r,[r,t[2]],{MSUBSUP:"superscript",MUNDEROVER:"overscore"}[e],1,!0),l.getInstance().makeLimitNode_(r,[r,t[1]],null,"limlower");t[i]||(o="limlower")}return l.getInstance().makeLimitNode_(r,t,null,o)}return n=l.MML_TO_BOUNDS_[e],l.getInstance().accentNode_(r,t,n.type,n.length,n.accent)}tablesInRow(e){let t=c.partitionNodes(e,a.tableIsMatrixOrVector),n=[];for(let e,r=0;e=t.rel[r];r++)n=n.concat(t.comp.shift()),n.push(l.tableToMatrixOrVector_(e));n=n.concat(t.comp.shift()),t=c.partitionNodes(n,a.isTableOrMultiline),n=[];for(let e,r=0;e=t.rel[r];r++){const r=t.comp.shift();a.tableIsCases(e,r)&&l.tableToCases_(e,r.pop()),n=n.concat(r),n.push(e)}return n.concat(t.comp.shift())}mfenced(e,t,n,r){if(n&&r.length>0){const e=l.nextSeparatorFunction_(n),t=[r.shift()];r.forEach((n=>{t.push(l.getInstance().factory_.makeContentNode(e())),t.push(n)})),r=t}return e&&t?l.getInstance().horizontalFencedNode_(l.getInstance().factory_.makeContentNode(e),l.getInstance().factory_.makeContentNode(t),r):(e&&r.unshift(l.getInstance().factory_.makeContentNode(e)),t&&r.push(l.getInstance().factory_.makeContentNode(t)),l.getInstance().row(r))}fractionLikeNode(e,t,n,r){let o;if(!r&&c.isZeroLength(n)){const n=l.getInstance().factory_.makeBranchNode("line",[e],[]),r=l.getInstance().factory_.makeBranchNode("line",[t],[]);return o=l.getInstance().factory_.makeBranchNode("multiline",[n,r],[]),l.binomialForm_(o),l.classifyMultiline(o),o}return o=l.getInstance().fractionNode_(e,t),r&&o.addAnnotation("general","bevelled"),o}tensor(e,t,n,r,o){const i=l.getInstance().factory_.makeBranchNode("tensor",[e,l.getInstance().scriptNode_(t,"leftsub"),l.getInstance().scriptNode_(n,"leftsuper"),l.getInstance().scriptNode_(r,"rightsub"),l.getInstance().scriptNode_(o,"rightsuper")],[]);return i.role=e.role,i.embellished=a.isEmbellished(e),i}pseudoTensor(e,t,n){const r=e=>!a.isType(e,"empty"),o=t.filter(r).length,i=n.filter(r).length;if(!o&&!i)return e;const s=o?i?"MSUBSUP":"MSUB":"MSUP",c=[e];return o&&c.push(l.getInstance().scriptNode_(t,"rightsub",!0)),i&&c.push(l.getInstance().scriptNode_(n,"rightsuper",!0)),l.getInstance().limitNode(s,c)}font(e){const t=l.MATHJAX_FONTS[e];return t||e}proof(e,t,n){if(t.inference||t.axiom||console.log("Noise"),t.axiom){const t=l.getInstance().cleanInference(e.childNodes),r=t.length?l.getInstance().factory_.makeBranchNode("inference",n(t),[]):l.getInstance().factory_.makeEmptyNode();return r.role="axiom",r.mathmlTree=e,r}const r=l.getInstance().inference(e,t,n);return t.proof&&(r.role="proof",r.childNodes[0].role="final"),r}inference(e,t,n){if(t.inferenceRule){const t=l.getInstance().getFormulas(e,[],n);return l.getInstance().factory_.makeBranchNode("inference",[t.conclusion,t.premises],[])}const o=t.labelledRule,i=r.toArray(e.childNodes),s=[];"left"!==o&&"both"!==o||s.push(l.getInstance().getLabel(e,i,n,"left")),"right"!==o&&"both"!==o||s.push(l.getInstance().getLabel(e,i,n,"right"));const a=l.getInstance().getFormulas(e,i,n),c=l.getInstance().factory_.makeBranchNode("inference",[a.conclusion,a.premises],s);return c.mathmlTree=e,c}getLabel(e,t,n,o){const i=l.getInstance().findNestedRow(t,"prooflabel",o),s=l.getInstance().factory_.makeBranchNode("rulelabel",n(r.toArray(i.childNodes)),[]);return s.role=o,s.mathmlTree=i,s}getFormulas(e,t,n){const o=t.length?l.getInstance().findNestedRow(t,"inferenceRule"):e,i="up"===l.getSemantics(o).inferenceRule,s=i?o.childNodes[1]:o.childNodes[0],a=i?o.childNodes[0]:o.childNodes[1],c=s.childNodes[0].childNodes[0],u=r.toArray(c.childNodes[0].childNodes),d=[];let p=1;for(const e of u)p%2&&d.push(e.childNodes[0]),p++;const h=n(d),f=n(r.toArray(a.childNodes[0].childNodes))[0],m=l.getInstance().factory_.makeBranchNode("premises",h,[]);m.mathmlTree=c;const g=l.getInstance().factory_.makeBranchNode("conclusion",[f],[]);return g.mathmlTree=a.childNodes[0].childNodes[0],{conclusion:g,premises:m}}findNestedRow(e,t,n){return l.getInstance().findNestedRow_(e,t,0,n)}cleanInference(e){return r.toArray(e).filter((function(e){return"MSPACE"!==r.tagName(e)}))}operatorNode(e){return"unknown"===e.type&&(e.type="operator"),i.run("multioperator",e)}implicitNode_(e){const t=l.getInstance().factory_.makeMultipleContentNodes(e.length-1,o.invisibleTimes());l.matchSpaces_(e,t);const n=l.getInstance().infixNode_(e,t[0]);return n.role="implicit",t.forEach((function(e){e.parent=n})),n.contentNodes=t,n}infixNode_(e,t){const n=l.getInstance().factory_.makeBranchNode("infixop",e,[t],c.getEmbellishedInner(t).textContent);return n.role=t.role,i.run("propagateSimpleFunction",n)}explicitMixed_(e){const t=c.partitionNodes(e,(function(e){return e.textContent===o.invisiblePlus()}));if(!t.rel.length)return e;let n=[];for(let e,r=0;e=t.rel[r];r++){const o=t.comp[r],i=t.comp[r+1],s=o.length-1;if(o[s]&&i[0]&&a.isType(o[s],"number")&&!a.isRole(o[s],"mixed")&&a.isType(i[0],"fraction")){const e=l.getInstance().factory_.makeBranchNode("number",[o[s],i[0]],[]);e.role="mixed",n=n.concat(o.slice(0,s)),n.push(e),i.shift()}else n=n.concat(o),n.push(e)}return n.concat(t.comp[t.comp.length-1])}concatNode_(e,t,n){if(0===t.length)return e;const r=t.map((function(e){return c.getEmbellishedInner(e).textContent})).join(" "),o=l.getInstance().factory_.makeBranchNode(n,[e],t,r);return t.length>1&&(o.role="multiop"),o}prefixNode_(e,t){const n=c.partitionNodes(t,(e=>a.isRole(e,"subtraction")));let r=l.getInstance().concatNode_(e,n.comp.pop(),"prefixop");for(1===r.contentNodes.length&&"addition"===r.contentNodes[0].role&&"+"===r.contentNodes[0].textContent&&(r.role="positive");n.rel.length>0;)r=l.getInstance().concatNode_(r,[n.rel.pop()],"prefixop"),r.role="negative",r=l.getInstance().concatNode_(r,n.comp.pop(),"prefixop");return r}postfixNode_(e,t){return t.length?l.getInstance().concatNode_(e,t,"postfixop"):e}combineUnits_(e){const t=c.partitionNodes(e,(function(e){return!a.isRole(e,"unit")}));if(e.length===t.rel.length)return t.rel;const n=[];let r,o;do{const e=t.comp.shift();r=t.rel.shift();let i=null;o=n.pop(),o&&(e.length&&a.isUnitCounter(o)?e.unshift(o):n.push(o)),1===e.length&&(i=e.pop()),e.length>1&&(i=l.getInstance().implicitNode_(e),i.role="unit"),i&&n.push(i),r&&n.push(r)}while(r);return n}getMixedNumbers_(e){const t=c.partitionNodes(e,(function(e){return a.isType(e,"fraction")&&a.isRole(e,"vulgar")}));if(!t.rel.length)return e;let n=[];for(let e,r=0;e=t.rel[r];r++){const o=t.comp[r],i=o.length-1;if(o[i]&&a.isType(o[i],"number")&&(a.isRole(o[i],"integer")||a.isRole(o[i],"float"))){const t=l.getInstance().factory_.makeBranchNode("number",[o[i],e],[]);t.role="mixed",n=n.concat(o.slice(0,i)),n.push(t)}else n=n.concat(o),n.push(e)}return n.concat(t.comp[t.comp.length-1])}getTextInRow_(e){if(e.length<=1)return e;const t=c.partitionNodes(e,(e=>a.isType(e,"text")));if(0===t.rel.length)return e;const n=[];let r=t.comp[0];r.length>0&&n.push(l.getInstance().row(r));for(let e,o=0;e=t.rel[o];o++)n.push(e),r=t.comp[o+1],r.length>0&&n.push(l.getInstance().row(r));return[l.getInstance().dummyNode_(n)]}relationsInRow_(e){const t=c.partitionNodes(e,a.isRelation),n=t.rel[0];if(!n)return l.getInstance().operationsInRow_(e);if(1===e.length)return e[0];const r=t.comp.map(l.getInstance().operationsInRow_);let o;return t.rel.some((function(e){return!e.equals(n)}))?(o=l.getInstance().factory_.makeBranchNode("multirel",r,t.rel),t.rel.every((function(e){return e.role===n.role}))&&(o.role=n.role),o):(o=l.getInstance().factory_.makeBranchNode("relseq",r,t.rel,c.getEmbellishedInner(n).textContent),o.role=n.role,o)}operationsInRow_(e){if(0===e.length)return l.getInstance().factory_.makeEmptyNode();if(1===(e=l.getInstance().explicitMixed_(e)).length)return e[0];const t=[];for(;e.length>0&&a.isOperator(e[0]);)t.push(e.shift());if(0===e.length)return l.getInstance().prefixNode_(t.pop(),t);if(1===e.length)return l.getInstance().prefixNode_(e[0],t);e=i.run("convert_juxtaposition",e);const n=c.sliceNodes(e,a.isOperator),r=l.getInstance().prefixNode_(l.getInstance().implicitNode(n.head),t);return n.div?l.getInstance().operationsTree_(n.tail,r,n.div):r}operationsTree_(e,t,n,r){const o=r||[];if(0===e.length){if(o.unshift(n),"infixop"===t.type){const e=l.getInstance().postfixNode_(t.childNodes.pop(),o);return t.appendChild(e),t}return l.getInstance().postfixNode_(t,o)}const i=c.sliceNodes(e,a.isOperator);if(0===i.head.length)return o.push(i.div),l.getInstance().operationsTree_(i.tail,t,n,o);const s=l.getInstance().prefixNode_(l.getInstance().implicitNode(i.head),o),u=l.getInstance().appendOperand_(t,n,s);return i.div?l.getInstance().operationsTree_(i.tail,u,i.div,[]):u}appendOperand_(e,t,n){if("infixop"!==e.type)return l.getInstance().infixNode_([e,n],t);const r=l.getInstance().appendDivisionOp_(e,t,n);return r||(l.getInstance().appendExistingOperator_(e,t,n)?e:"multiplication"===t.role?l.getInstance().appendMultiplicativeOp_(e,t,n):l.getInstance().appendAdditiveOp_(e,t,n))}appendDivisionOp_(e,t,n){return"division"===t.role?a.isImplicit(e)?l.getInstance().infixNode_([e,n],t):l.getInstance().appendLastOperand_(e,t,n):"division"===e.role?l.getInstance().infixNode_([e,n],t):null}appendLastOperand_(e,t,n){let r=e,o=e.childNodes[e.childNodes.length-1];for(;o&&"infixop"===o.type&&!a.isImplicit(o);)r=o,o=r.childNodes[e.childNodes.length-1];const i=l.getInstance().infixNode_([r.childNodes.pop(),n],t);return r.appendChild(i),e}appendMultiplicativeOp_(e,t,n){if(a.isImplicit(e))return l.getInstance().infixNode_([e,n],t);let r=e,o=e.childNodes[e.childNodes.length-1];for(;o&&"infixop"===o.type&&!a.isImplicit(o);)r=o,o=r.childNodes[e.childNodes.length-1];const i=l.getInstance().infixNode_([r.childNodes.pop(),n],t);return r.appendChild(i),e}appendAdditiveOp_(e,t,n){return l.getInstance().infixNode_([e,n],t)}appendExistingOperator_(e,t,n){return!(!e||"infixop"!==e.type||a.isImplicit(e))&&(e.contentNodes[0].equals(t)?(e.appendContentNode(t),e.appendChild(n),!0):l.getInstance().appendExistingOperator_(e.childNodes[e.childNodes.length-1],t,n))}getFencesInRow_(e){let t=c.partitionNodes(e,a.isFence);t=l.purgeFences_(t);const n=t.comp.shift();return l.getInstance().fences_(t.rel,t.comp,[],[n])}fences_(e,t,n,r){if(0===e.length&&0===n.length)return r[0];const o=e=>a.isRole(e,"open");if(0===e.length){const e=r.shift();for(;n.length>0;){if(o(n[0])){const t=n.shift();l.fenceToPunct_(t),e.push(t)}else{const t=c.sliceNodes(n,o),i=t.head.length-1,s=l.getInstance().neutralFences_(t.head,r.slice(0,i));r=r.slice(i),e.push(...s),t.div&&t.tail.unshift(t.div),n=t.tail}e.push(...r.shift())}return e}const i=n[n.length-1],s=e[0].role;if("open"===s||a.isNeutralFence(e[0])&&(!i||!a.compareNeutralFences(e[0],i))){n.push(e.shift());const o=t.shift();return o&&r.push(o),l.getInstance().fences_(e,t,n,r)}if(i&&"close"===s&&"open"===i.role){const o=l.getInstance().horizontalFencedNode_(n.pop(),e.shift(),r.pop());return r.push(r.pop().concat([o],t.shift())),l.getInstance().fences_(e,t,n,r)}if(i&&a.compareNeutralFences(e[0],i)){if(!a.elligibleLeftNeutral(i)||!a.elligibleRightNeutral(e[0])){n.push(e.shift());const o=t.shift();return o&&r.push(o),l.getInstance().fences_(e,t,n,r)}const o=l.getInstance().horizontalFencedNode_(n.pop(),e.shift(),r.pop());return r.push(r.pop().concat([o],t.shift())),l.getInstance().fences_(e,t,n,r)}if(i&&"close"===s&&a.isNeutralFence(i)&&n.some(o)){const i=c.sliceNodes(n,o,!0),s=r.pop(),a=r.length-i.tail.length+1,u=l.getInstance().neutralFences_(i.tail,r.slice(a));r=r.slice(0,a);const d=l.getInstance().horizontalFencedNode_(i.div,e.shift(),r.pop().concat(u,s));return r.push(r.pop().concat([d],t.shift())),l.getInstance().fences_(e,t,i.head,r)}const u=e.shift();return l.fenceToPunct_(u),r.push(r.pop().concat([u],t.shift())),l.getInstance().fences_(e,t,n,r)}neutralFences_(e,t){if(0===e.length)return e;if(1===e.length)return l.fenceToPunct_(e[0]),e;const n=e.shift();if(!a.elligibleLeftNeutral(n)){l.fenceToPunct_(n);const r=t.shift();return r.unshift(n),r.concat(l.getInstance().neutralFences_(e,t))}const r=c.sliceNodes(e,(function(e){return a.compareNeutralFences(e,n)}));if(!r.div){l.fenceToPunct_(n);const r=t.shift();return r.unshift(n),r.concat(l.getInstance().neutralFences_(e,t))}if(!a.elligibleRightNeutral(r.div))return l.fenceToPunct_(r.div),e.unshift(n),l.getInstance().neutralFences_(e,t);const o=l.getInstance().combineFencedContent_(n,r.div,r.head,t);if(r.tail.length>0){const e=o.shift(),t=l.getInstance().neutralFences_(r.tail,o);return e.concat(t)}return o[0]}combineFencedContent_(e,t,n,r){if(0===n.length){const n=l.getInstance().horizontalFencedNode_(e,t,r.shift());return r.length>0?r[0].unshift(n):r=[[n]],r}const o=r.shift(),i=n.length-1,s=r.slice(0,i),a=(r=r.slice(i)).shift(),c=l.getInstance().neutralFences_(n,s);o.push(...c),o.push(...a);const u=l.getInstance().horizontalFencedNode_(e,t,o);return r.length>0?r[0].unshift(u):r=[[u]],r}horizontalFencedNode_(e,t,n){const r=l.getInstance().row(n);let o=l.getInstance().factory_.makeBranchNode("fenced",[r],[e,t]);return"open"===e.role?(l.getInstance().classifyHorizontalFence_(o),o=i.run("propagateComposedFunction",o)):o.role=e.role,o=i.run("detect_cycle",o),l.rewriteFencedNode_(o)}classifyHorizontalFence_(e){e.role="leftright";const t=e.childNodes;if(!a.isSetNode(e)||t.length>1)return;if(0===t.length||"empty"===t[0].type)return void(e.role="set empty");const n=t[0].type;if(1===t.length&&a.isSingletonSetContent(t[0]))return void(e.role="set singleton");const r=t[0].role;if("punctuated"===n&&"sequence"===r){if("comma"!==t[0].contentNodes[0].role)return 1!==t[0].contentNodes.length||"vbar"!==t[0].contentNodes[0].role&&"colon"!==t[0].contentNodes[0].role?void 0:(e.role="set extended",void l.getInstance().setExtension_(e));e.role="set collection"}}setExtension_(e){const t=e.childNodes[0].childNodes[0];t&&"infixop"===t.type&&1===t.contentNodes.length&&a.isMembership(t.contentNodes[0])&&(t.addAnnotation("set","intensional"),t.contentNodes[0].addAnnotation("set","intensional"))}getPunctuationInRow_(e){if(e.length<=1)return e;const t=e=>{const t=e.type;return"punctuation"===t||"text"===t||"operator"===t||"relation"===t},n=c.partitionNodes(e,(function(n){if(!a.isPunctuation(n))return!1;if(a.isPunctuation(n)&&!a.isRole(n,"ellipsis"))return!0;const r=e.indexOf(n);if(0===r)return!e[1]||!t(e[1]);const o=e[r-1];if(r===e.length-1)return!t(o);const i=e[r+1];return!t(o)||!t(i)}));if(0===n.rel.length)return e;const r=[];let o=n.comp.shift();o.length>0&&r.push(l.getInstance().row(o));let i=0;for(;n.comp.length>0;)r.push(n.rel[i++]),o=n.comp.shift(),o.length>0&&r.push(l.getInstance().row(o));return[l.getInstance().punctuatedNode_(r,n.rel)]}punctuatedNode_(e,t){const n=l.getInstance().factory_.makeBranchNode("punctuated",e,t);if(t.length===e.length){const e=t[0].role;if("unknown"!==e&&t.every((function(t){return t.role===e})))return n.role=e,n}return a.singlePunctAtPosition(e,t,0)?n.role="startpunct":a.singlePunctAtPosition(e,t,e.length-1)?n.role="endpunct":t.every((e=>a.isRole(e,"dummy")))?n.role="text":t.every((e=>a.isRole(e,"space")))?n.role="space":n.role="sequence",n}dummyNode_(e){const t=l.getInstance().factory_.makeMultipleContentNodes(e.length-1,o.invisibleComma());return t.forEach((function(e){e.role="dummy"})),l.getInstance().punctuatedNode_(e,t)}accentRole_(e,t){if(!a.isAccent(e))return!1;const n=e.textContent,r=o.lookupSecondary("bar",n)||o.lookupSecondary("tilde",n)||e.role;return e.role="underscore"===t?"underaccent":"overaccent",e.addAnnotation("accent",r),!0}accentNode_(e,t,n,r,o){const i=(t=t.slice(0,r+1))[1],s=t[2];let a;if(!o&&s&&(a=l.getInstance().factory_.makeBranchNode("subscript",[e,i],[]),a.role="subsup",t=[a,s],n="superscript"),o){const r=l.getInstance().accentRole_(i,n);if(s){l.getInstance().accentRole_(s,"overscore")&&!r?(a=l.getInstance().factory_.makeBranchNode("overscore",[e,s],[]),t=[a,i],n="underscore"):(a=l.getInstance().factory_.makeBranchNode("underscore",[e,i],[]),t=[a,s],n="overscore"),a.role="underover"}}return l.getInstance().makeLimitNode_(e,t,a,n)}makeLimitNode_(e,t,n,r){if("limupper"===r&&"limlower"===e.type)return e.childNodes.push(t[1]),t[1].parent=e,e.type="limboth",e;if("limlower"===r&&"limupper"===e.type)return e.childNodes.splice(1,-1,t[1]),t[1].parent=e,e.type="limboth",e;const o=l.getInstance().factory_.makeBranchNode(r,t,[]),i=a.isEmbellished(e);return n&&(n.embellished=i),o.embellished=i,o.role=e.role,o}getFunctionsInRow_(e,t){const n=t||[];if(0===e.length)return n;const r=e.shift(),o=l.classifyFunction_(r,e);if(!o)return n.push(r),l.getInstance().getFunctionsInRow_(e,n);const i=l.getInstance().getFunctionsInRow_(e,[]),s=l.getInstance().getFunctionArgs_(r,i,o);return n.concat(s)}getFunctionArgs_(e,t,n){let r,o,i;switch(n){case"integral":{const n=l.getInstance().getIntegralArgs_(t);if(!n.intvar&&!n.integrand.length)return n.rest.unshift(e),n.rest;const r=l.getInstance().row(n.integrand);return i=l.getInstance().integralNode_(e,r,n.intvar),n.rest.unshift(i),n.rest}case"prefix":if(t[0]&&"fenced"===t[0].type){const n=t.shift();return a.isNeutralFence(n)||(n.role="leftright"),i=l.getInstance().functionNode_(e,n),t.unshift(i),t}if(r=c.sliceNodes(t,a.isPrefixFunctionBoundary),r.head.length)o=l.getInstance().row(r.head),r.div&&r.tail.unshift(r.div);else{if(!r.div||!a.isType(r.div,"appl"))return t.unshift(e),t;o=r.div}return i=l.getInstance().functionNode_(e,o),r.tail.unshift(i),r.tail;case"bigop":return r=c.sliceNodes(t,a.isBigOpBoundary),r.head.length?(o=l.getInstance().row(r.head),i=l.getInstance().bigOpNode_(e,o),r.div&&r.tail.unshift(r.div),r.tail.unshift(i),r.tail):(t.unshift(e),t);default:{if(0===t.length)return[e];const n=t[0];return"fenced"===n.type&&!a.isNeutralFence(n)&&a.isSimpleFunctionScope(n)?(n.role="leftright",l.propagateFunctionRole_(e,"simple function"),i=l.getInstance().functionNode_(e,t.shift()),t.unshift(i),t):(t.unshift(e),t)}}}getIntegralArgs_(e,t=[]){if(0===e.length)return{integrand:t,intvar:null,rest:e};const n=e[0];if(a.isGeneralFunctionBoundary(n))return{integrand:t,intvar:null,rest:e};if(a.isIntegralDxBoundarySingle(n))return n.role="integral",{integrand:t,intvar:n,rest:e.slice(1)};if(e[1]&&a.isIntegralDxBoundary(n,e[1])){const r=l.getInstance().prefixNode_(e[1],[n]);return r.role="integral",{integrand:t,intvar:r,rest:e.slice(2)}}return t.push(e.shift()),l.getInstance().getIntegralArgs_(e,t)}functionNode_(e,t){const n=l.getInstance().factory_.makeContentNode(o.functionApplication()),r=l.getInstance().funcAppls[e.id];r&&(n.mathmlTree=r.mathmlTree,n.mathml=r.mathml,n.annotation=r.annotation,n.attributes=r.attributes,delete l.getInstance().funcAppls[e.id]),n.type="punctuation",n.role="application";const i=l.getFunctionOp_(e,(function(e){return a.isType(e,"function")||a.isType(e,"identifier")&&a.isRole(e,"simple function")}));return l.getInstance().functionalNode_("appl",[e,t],i,[n])}bigOpNode_(e,t){const n=l.getFunctionOp_(e,(e=>a.isType(e,"largeop")));return l.getInstance().functionalNode_("bigop",[e,t],n,[])}integralNode_(e,t,n){t=t||l.getInstance().factory_.makeEmptyNode(),n=n||l.getInstance().factory_.makeEmptyNode();const r=l.getFunctionOp_(e,(e=>a.isType(e,"largeop")));return l.getInstance().functionalNode_("integral",[e,t,n],r,[])}functionalNode_(e,t,n,r){const o=t[0];let i;n&&(i=n.parent,r.push(n));const s=l.getInstance().factory_.makeBranchNode(e,t,r);return s.role=o.role,i&&(n.parent=i),s}fractionNode_(e,t){const n=l.getInstance().factory_.makeBranchNode("fraction",[e,t],[]);return n.role=n.childNodes.every((function(e){return a.isType(e,"number")&&a.isRole(e,"integer")}))?"vulgar":n.childNodes.every(a.isPureUnit)?"unit":"division",i.run("propagateSimpleFunction",n)}scriptNode_(e,t,n){let r;switch(e.length){case 0:r=l.getInstance().factory_.makeEmptyNode();break;case 1:if(r=e[0],n)return r;break;default:r=l.getInstance().dummyNode_(e)}return r.role=t,r}findNestedRow_(e,t,n,o){if(n>3)return null;for(let i,s=0;i=e[s];s++){const e=r.tagName(i);if("MSPACE"!==e){if("MROW"===e)return l.getInstance().findNestedRow_(r.toArray(i.childNodes),t,n+1,o);if(l.findSemantics(i,t,o))return i}}return null}}t.default=l,l.FENCE_TO_PUNCT_={metric:"metric",neutral:"vbar",open:"openfence",close:"closefence"},l.MML_TO_LIMIT_={MSUB:{type:"limlower",length:1},MUNDER:{type:"limlower",length:1},MSUP:{type:"limupper",length:1},MOVER:{type:"limupper",length:1},MSUBSUP:{type:"limboth",length:2},MUNDEROVER:{type:"limboth",length:2}},l.MML_TO_BOUNDS_={MSUB:{type:"subscript",length:1,accent:!1},MSUP:{type:"superscript",length:1,accent:!1},MSUBSUP:{type:"subscript",length:2,accent:!1},MUNDER:{type:"underscore",length:1,accent:!0},MOVER:{type:"overscore",length:1,accent:!0},MUNDEROVER:{type:"underscore",length:2,accent:!0}},l.CLASSIFY_FUNCTION_={integral:"integral",sum:"bigop","prefix function":"prefix","limit function":"prefix","simple function":"prefix","composed function":"prefix"},l.MATHJAX_FONTS={"-tex-caligraphic":"caligraphic","-tex-caligraphic-bold":"caligraphic-bold","-tex-calligraphic":"caligraphic","-tex-calligraphic-bold":"caligraphic-bold","-tex-oldstyle":"oldstyle","-tex-oldstyle-bold":"oldstyle-bold","-tex-mathit":"italic"}},7984:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticSkeleton=void 0;const r=n(1426),o=n(5024),i=n(8171);class s{constructor(e){this.parents=null,this.levelsMap=null,e=0===e?e:e||[],this.array=e}static fromTree(e){return s.fromNode(e.root)}static fromNode(e){return new s(s.fromNode_(e))}static fromString(e){return new s(s.fromString_(e))}static simpleCollapseStructure(e){return"number"==typeof e}static contentCollapseStructure(e){return!!e&&!s.simpleCollapseStructure(e)&&"c"===e[0]}static interleaveIds(e,t){return r.interleaveLists(s.collapsedLeafs(e),s.collapsedLeafs(t))}static collapsedLeafs(...e){return e.reduce(((e,t)=>{return e.concat((n=t,s.simpleCollapseStructure(n)?[n]:(n=n,s.contentCollapseStructure(n[1])?n.slice(2):n.slice(1))));var n}),[])}static fromStructure(e,t){return new s(s.tree_(e,t.root))}static combineContentChildren(e,t,n){switch(e.type){case"relseq":case"infixop":case"multirel":return r.interleaveLists(n,t);case"prefixop":return t.concat(n);case"postfixop":return n.concat(t);case"fenced":return n.unshift(t[0]),n.push(t[1]),n;case"appl":return[n[0],t[0],n[1]];case"root":return[n[1],n[0]];case"row":case"line":return t.length&&n.unshift(t[0]),n;default:return n}}static makeSexp_(e){return s.simpleCollapseStructure(e)?e.toString():s.contentCollapseStructure(e)?"(c "+e.slice(1).map(s.makeSexp_).join(" ")+")":"("+e.map(s.makeSexp_).join(" ")+")"}static fromString_(e){let t=e.replace(/\(/g,"[");return t=t.replace(/\)/g,"]"),t=t.replace(/ /g,","),t=t.replace(/c/g,'"c"'),JSON.parse(t)}static fromNode_(e){if(!e)return[];const t=e.contentNodes;let n;t.length&&(n=t.map(s.fromNode_),n.unshift("c"));const r=e.childNodes;if(!r.length)return t.length?[e.id,n]:e.id;const o=r.map(s.fromNode_);return t.length&&o.unshift(n),o.unshift(e.id),o}static tree_(e,t){if(!t)return[];if(!t.childNodes.length)return t.id;const n=t.id,r=[n],a=o.evalXPath(`.//self::*[@${i.Attribute.ID}=${n}]`,e)[0],c=s.combineContentChildren(t,t.contentNodes.map((function(e){return e})),t.childNodes.map((function(e){return e})));a&&s.addOwns_(a,c);for(let t,n=0;t=c[n];n++)r.push(s.tree_(e,t));return r}static addOwns_(e,t){const n=e.getAttribute(i.Attribute.COLLAPSED),r=n?s.realLeafs_(s.fromString(n).array):t.map((e=>e.id));e.setAttribute(i.Attribute.OWNS,r.join(" "))}static realLeafs_(e){if(s.simpleCollapseStructure(e))return[e];if(s.contentCollapseStructure(e))return[];e=e;let t=[];for(let n=1;n<e.length;n++)t=t.concat(s.realLeafs_(e[n]));return t}populate(){this.parents&&this.levelsMap||(this.parents={},this.levelsMap={},this.populate_(this.array,this.array,[]))}toString(){return s.makeSexp_(this.array)}populate_(e,t,n){if(s.simpleCollapseStructure(e))return e=e,this.levelsMap[e]=t,void(this.parents[e]=e===n[0]?n.slice(1):n);const r=s.contentCollapseStructure(e)?e.slice(1):e,o=[r[0]].concat(n);for(let t=0,n=r.length;t<n;t++){const n=r[t];this.populate_(n,e,o)}}isRoot(e){return e===this.levelsMap[e][0]}directChildren(e){if(!this.isRoot(e))return[];return this.levelsMap[e].slice(1).map((e=>s.simpleCollapseStructure(e)?e:s.contentCollapseStructure(e)?e[1]:e[0]))}subtreeNodes(e){if(!this.isRoot(e))return[];const t=(e,n)=>{s.simpleCollapseStructure(e)?n.push(e):(e=e,s.contentCollapseStructure(e)&&(e=e.slice(1)),e.forEach((e=>t(e,n))))},n=this.levelsMap[e],r=[];return t(n.slice(1),r),r}}t.SemanticSkeleton=s},1784:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticTree=void 0;const r=n(6671),o=n(4036),i=n(241),s=n(8122),a=n(9444),c=n(6161);n(7103);class l{constructor(e){this.mathml=e,this.parser=new s.SemanticMathml,this.root=this.parser.parse(e),this.collator=this.parser.getFactory().leafMap.collateMeaning();const t=this.collator.newDefault();t&&(this.parser=new s.SemanticMathml,this.parser.getFactory().defaultMap=t,this.root=this.parser.parse(e)),u.visit(this.root,{}),(0,o.annotate)(this.root)}static empty(){const e=r.parseInput("<math/>"),t=new l(e);return t.mathml=e,t}static fromNode(e,t){const n=l.empty();return n.root=e,t&&(n.mathml=t),n}static fromRoot(e,t){let n=e;for(;n.parent;)n=n.parent;const r=l.fromNode(n);return t&&(r.mathml=t),r}static fromXml(e){const t=l.empty();return e.childNodes[0]&&(t.root=a.SemanticNode.fromXml(e.childNodes[0])),t}xml(e){const t=r.parseInput("<stree></stree>"),n=this.root.xml(t.ownerDocument,e);return t.appendChild(n),t}toString(e){return r.serializeXml(this.xml(e))}formatXml(e){const t=this.toString(e);return r.formatXml(t)}displayTree(){this.root.displayTree()}replaceNode(e,t){const n=e.parent;n?n.replaceChild(e,t):this.root=t}toJson(){const e={};return e.stree=this.root.toJson(),e}}t.SemanticTree=l;const u=new i.SemanticVisitor("general","unit",((e,t)=>{if("infixop"===e.type&&("multiplication"===e.role||"implicit"===e.role)){const t=e.childNodes;t.length&&(c.isPureUnit(t[0])||c.isUnitCounter(t[0]))&&e.childNodes.slice(1).every(c.isPureUnit)&&(e.role="unit")}return!1}))},8901:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.partitionNodes=t.sliceNodes=t.getEmbellishedInner=t.addAttributes=t.isZeroLength=t.purgeNodes=t.isOrphanedGlyph=t.hasDisplayTag=t.hasEmptyTag=t.hasIgnoreTag=t.hasLeafTag=t.hasMathTag=t.directSpeechKeys=t.DISPLAYTAGS=t.EMPTYTAGS=t.IGNORETAGS=t.LEAFTAGS=void 0;const r=n(6671);function o(e){return!!e&&-1!==t.LEAFTAGS.indexOf(r.tagName(e))}function i(e,t,n){n&&e.reverse();const r=[];for(let o,i=0;o=e[i];i++){if(t(o))return n?{head:e.slice(i+1).reverse(),div:o,tail:r.reverse()}:{head:r,div:o,tail:e.slice(i+1)};r.push(o)}return n?{head:[],div:null,tail:r.reverse()}:{head:r,div:null,tail:[]}}t.LEAFTAGS=["MO","MI","MN","MTEXT","MS","MSPACE"],t.IGNORETAGS=["MERROR","MPHANTOM","MALIGNGROUP","MALIGNMARK","MPRESCRIPTS","ANNOTATION","ANNOTATION-XML"],t.EMPTYTAGS=["MATH","MROW","MPADDED","MACTION","NONE","MSTYLE","SEMANTICS"],t.DISPLAYTAGS=["MROOT","MSQRT"],t.directSpeechKeys=["aria-label","exact-speech","alt"],t.hasMathTag=function(e){return!!e&&"MATH"===r.tagName(e)},t.hasLeafTag=o,t.hasIgnoreTag=function(e){return!!e&&-1!==t.IGNORETAGS.indexOf(r.tagName(e))},t.hasEmptyTag=function(e){return!!e&&-1!==t.EMPTYTAGS.indexOf(r.tagName(e))},t.hasDisplayTag=function(e){return!!e&&-1!==t.DISPLAYTAGS.indexOf(r.tagName(e))},t.isOrphanedGlyph=function(e){return!!e&&"MGLYPH"===r.tagName(e)&&!o(e.parentNode)},t.purgeNodes=function(e){const n=[];for(let o,i=0;o=e[i];i++){if(o.nodeType!==r.NodeType.ELEMENT_NODE)continue;const e=r.tagName(o);-1===t.IGNORETAGS.indexOf(e)&&(-1!==t.EMPTYTAGS.indexOf(e)&&0===o.childNodes.length||n.push(o))}return n},t.isZeroLength=function(e){if(!e)return!1;if(-1!==["negativeveryverythinmathspace","negativeverythinmathspace","negativethinmathspace","negativemediummathspace","negativethickmathspace","negativeverythickmathspace","negativeveryverythickmathspace"].indexOf(e))return!0;const t=e.match(/[0-9.]+/);return!!t&&0===parseFloat(t[0])},t.addAttributes=function(e,n){if(n.hasAttributes()){const r=n.attributes;for(let n=r.length-1;n>=0;n--){const o=r[n].name;o.match(/^ext/)&&(e.attributes[o]=r[n].value,e.nobreaking=!0),-1!==t.directSpeechKeys.indexOf(o)&&(e.attributes["ext-speech"]=r[n].value,e.nobreaking=!0),o.match(/texclass$/)&&(e.attributes.texclass=r[n].value),"href"===o&&(e.attributes.href=r[n].value,e.nobreaking=!0)}}},t.getEmbellishedInner=function e(t){return t&&t.embellished&&t.childNodes.length>0?e(t.childNodes[0]):t},t.sliceNodes=i,t.partitionNodes=function(e,t){let n=e;const r=[],o=[];let s=null;do{s=i(n,t),o.push(s.head),r.push(s.div),n=s.tail}while(s.div);return r.pop(),{rel:r,comp:o}}},9135:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractSpeechGenerator=void 0;const r=n(985),o=n(8171),i=n(1848),s=n(144);t.AbstractSpeechGenerator=class{constructor(){this.modality=o.addPrefix("speech"),this.rebuilt_=null,this.options_={}}getRebuilt(){return this.rebuilt_}setRebuilt(e){this.rebuilt_=e}setOptions(e){this.options_=e||{},this.modality=o.addPrefix(this.options_.modality||"speech")}getOptions(){return this.options_}start(){}end(){}generateSpeech(e,t){return this.rebuilt_||(this.rebuilt_=new i.RebuildStree(t)),(0,r.setup)(this.options_),s.computeMarkup(this.getRebuilt().xml)}}},3153:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AdhocSpeechGenerator=void 0;const r=n(9135);class o extends r.AbstractSpeechGenerator{getSpeech(e,t){const n=this.generateSpeech(e,t);return e.setAttribute(this.modality,n),n}}t.AdhocSpeechGenerator=o},6281:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ColorGenerator=void 0;const r=n(8171),o=n(1123),i=n(1848),s=n(8835),a=n(9135);class c extends a.AbstractSpeechGenerator{constructor(){super(...arguments),this.modality=(0,r.addPrefix)("foreground"),this.contrast=new o.ContrastPicker}static visitStree_(e,t,n){if(e.childNodes.length){if(e.contentNodes.length&&("punctuated"===e.type&&e.contentNodes.forEach((e=>n[e.id]=!0)),"implicit"!==e.role&&t.push(e.contentNodes.map((e=>e.id)))),e.childNodes.length){if("implicit"===e.role){const r=[];let o=[];for(const t of e.childNodes){const e=[];c.visitStree_(t,e,n),e.length<=2&&r.push(e.shift()),o=o.concat(e)}return t.push(r),void o.forEach((e=>t.push(e)))}e.childNodes.forEach((e=>c.visitStree_(e,t,n)))}}else n[e.id]||t.push(e.id)}getSpeech(e,t){return s.getAttribute(e,this.modality)}generateSpeech(e,t){return this.getRebuilt()||this.setRebuilt(new i.RebuildStree(e)),this.colorLeaves_(e),s.getAttribute(e,this.modality)}colorLeaves_(e){const t=[];c.visitStree_(this.getRebuilt().streeRoot,t,{});for(const n of t){const t=this.contrast.generate();let r=!1;r=Array.isArray(n)?n.map((n=>this.colorLeave_(e,n,t))).reduce(((e,t)=>e||t),!1):this.colorLeave_(e,n.toString(),t),r&&this.contrast.increment()}}colorLeave_(e,t,n){const r=s.getBySemanticId(e,t);return!!r&&(r.setAttribute(this.modality,n),!0)}}t.ColorGenerator=c},1565:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DirectSpeechGenerator=void 0;const r=n(8835),o=n(9135);class i extends o.AbstractSpeechGenerator{getSpeech(e,t){return r.getAttribute(e,this.modality)}}t.DirectSpeechGenerator=i},7721:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DummySpeechGenerator=void 0;const r=n(9135);class o extends r.AbstractSpeechGenerator{getSpeech(e,t){return""}}t.DummySpeechGenerator=o},1558:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.NodeSpeechGenerator=void 0;const r=n(8835),o=n(7486);class i extends o.TreeSpeechGenerator{getSpeech(e,t){return super.getSpeech(e,t),r.getAttribute(e,this.modality)}}t.NodeSpeechGenerator=i},7317:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.generatorMapping_=t.generator=void 0;const r=n(3153),o=n(6281),i=n(1565),s=n(7721),a=n(1558),c=n(5778),l=n(7486);t.generator=function(e){return(t.generatorMapping_[e]||t.generatorMapping_.Direct)()},t.generatorMapping_={Adhoc:()=>new r.AdhocSpeechGenerator,Color:()=>new o.ColorGenerator,Direct:()=>new i.DirectSpeechGenerator,Dummy:()=>new s.DummySpeechGenerator,Node:()=>new a.NodeSpeechGenerator,Summary:()=>new c.SummarySpeechGenerator,Tree:()=>new l.TreeSpeechGenerator}},144:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.computeSummary_=t.retrieveSummary=t.connectAllMactions=t.connectMactions=t.nodeAtPosition_=t.computePrefix_=t.retrievePrefix=t.addPrefix=t.addModality=t.addSpeech=t.recomputeMarkup=t.computeMarkup=t.recomputeSpeech=t.computeSpeech=void 0;const r=n(4253),o=n(6671),i=n(5024),s=n(8171),a=n(6060),c=n(1784),l=n(8835);function u(e){return a.SpeechRuleEngine.getInstance().evaluateNode(e)}function d(e){return u(c.SemanticTree.fromNode(e).xml())}function p(e){const t=d(e);return r.markup(t)}function h(e){const t=f(e);return r.markup(t)}function f(e){const t=c.SemanticTree.fromRoot(e),n=i.evalXPath('.//*[@id="'+e.id+'"]',t.xml());let r=n[0];return n.length>1&&(r=m(e,n)||r),r?a.SpeechRuleEngine.getInstance().runInSetting({modality:"prefix",domain:"default",style:"default",strict:!0,speech:!0},(function(){return a.SpeechRuleEngine.getInstance().evaluateNode(r)})):[]}function m(e,t){const n=t[0];if(!e.parent)return n;const r=[];for(;e;)r.push(e.id),e=e.parent;const o=function(e,t){for(;t.length&&t.shift().toString()===e.getAttribute("id")&&e.parentNode&&e.parentNode.parentNode;)e=e.parentNode.parentNode;return!t.length};for(let e,n=0;e=t[n];n++)if(o(e,r.slice()))return e;return n}function g(e){return e?a.SpeechRuleEngine.getInstance().runInSetting({modality:"summary",strict:!1,speech:!0},(function(){return a.SpeechRuleEngine.getInstance().evaluateNode(e)})):[]}t.computeSpeech=u,t.recomputeSpeech=d,t.computeMarkup=function(e){const t=u(e);return r.markup(t)},t.recomputeMarkup=p,t.addSpeech=function(e,t,n){const i=o.querySelectorAllByAttrValue(n,"id",t.id.toString())[0],a=i?r.markup(u(i)):p(t);e.setAttribute(s.Attribute.SPEECH,a)},t.addModality=function(e,t,n){const r=p(t);e.setAttribute(n,r)},t.addPrefix=function(e,t){const n=h(t);n&&e.setAttribute(s.Attribute.PREFIX,n)},t.retrievePrefix=h,t.computePrefix_=f,t.nodeAtPosition_=m,t.connectMactions=function(e,t,n){const r=o.querySelectorAll(t,"maction");for(let t,i=0;t=r[i];i++){const r=t.getAttribute("id"),i=o.querySelectorAllByAttrValue(e,"id",r)[0];if(!i)continue;const a=t.childNodes[1],c=a.getAttribute(s.Attribute.ID);let u=l.getBySemanticId(e,c);if(u&&"dummy"!==u.getAttribute(s.Attribute.TYPE))continue;if(u=i.childNodes[0],u.getAttribute("sre-highlighter-added"))continue;const d=a.getAttribute(s.Attribute.PARENT);d&&u.setAttribute(s.Attribute.PARENT,d),u.setAttribute(s.Attribute.TYPE,"dummy"),u.setAttribute(s.Attribute.ID,c);o.querySelectorAllByAttrValue(n,"id",c)[0].setAttribute("alternative",c)}},t.connectAllMactions=function(e,t){const n=o.querySelectorAll(e,"maction");for(let e,r=0;e=n[r];r++){const n=e.childNodes[1].getAttribute(s.Attribute.ID);o.querySelectorAllByAttrValue(t,"id",n)[0].setAttribute("alternative",n)}},t.retrieveSummary=function(e){const t=g(e);return r.markup(t)},t.computeSummary_=g},5778:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SummarySpeechGenerator=void 0;const r=n(9135),o=n(144);class i extends r.AbstractSpeechGenerator{getSpeech(e,t){return o.connectAllMactions(t,this.getRebuilt().xml),this.generateSpeech(e,t)}}t.SummarySpeechGenerator=i},7486:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.TreeSpeechGenerator=void 0;const r=n(8171),o=n(8835),i=n(9135),s=n(144);class a extends i.AbstractSpeechGenerator{getSpeech(e,t){const n=this.generateSpeech(e,t),i=this.getRebuilt().nodeDict;for(const n in i){const a=i[n],c=o.getBySemanticId(t,n),l=o.getBySemanticId(e,n);c&&l&&(this.modality&&this.modality!==r.Attribute.SPEECH?s.addModality(l,a,this.modality):s.addSpeech(l,a,this.getRebuilt().xml),this.modality===r.Attribute.SPEECH&&s.addPrefix(l,a))}return n}}t.TreeSpeechGenerator=a},650:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.INTERVALS=t.makeLetter=t.numberRules=t.alphabetRules=t.getFont=t.makeInterval=t.generate=t.makeDomains_=t.Domains_=t.Base=t.Embellish=t.Font=void 0;const r=n(4886),o=n(2371),i=n(4524),s=n(3319),a=n(4161);var c,l,u;function d(){const e=i.LOCALE.ALPHABETS,n=(e,t)=>{const n={};return Object.keys(e).forEach((e=>n[e]=!0)),Object.keys(t).forEach((e=>n[e]=!0)),Object.keys(n)};t.Domains_.small=n(e.smallPrefix,e.letterTrans),t.Domains_.capital=n(e.capPrefix,e.letterTrans),t.Domains_.digit=n(e.digitPrefix,e.digitTrans)}function p(e){const t=e.toString(16).toUpperCase();return t.length>3?t:("000"+t).slice(-4)}function h([e,t],n){const r=parseInt(e,16),o=parseInt(t,16),i=[];for(let e=r;e<=o;e++){let t=p(e);!1!==n[t]&&(t=n[t]||t,i.push(t))}return i}function f(e){const t="normal"===e||"fullwidth"===e?"":i.LOCALE.MESSAGES.font[e]||i.LOCALE.MESSAGES.embellish[e]||"";return(0,s.localeFontCombiner)(t)}function m(e,n,r,o,s,a){const c=f(o);for(let o,l,u,d=0;o=e[d],l=n[d],u=r[d];d++){const e=a?i.LOCALE.ALPHABETS.capPrefix:i.LOCALE.ALPHABETS.smallPrefix,n=a?t.Domains_.capital:t.Domains_.small;b(c.combiner,o,l,u,c.font,e,s,i.LOCALE.ALPHABETS.letterTrans,n)}}function g(e,n,r,o,s){const a=f(r);for(let r,c,l=0;r=e[l],c=n[l];l++){const e=i.LOCALE.ALPHABETS.digitPrefix,n=l+s;b(a.combiner,r,c,n,a.font,e,o,i.LOCALE.ALPHABETS.digitTrans,t.Domains_.digit)}}function b(e,t,n,r,o,i,s,c,l){for(let u,d=0;u=l[d];d++){const l=u in c?c[u]:c.default,d=u in i?i[u]:i.default;a.defineRule(t.toString(),u,"default",s,n,e(l(r),o,d))}}!function(e){e.BOLD="bold",e.BOLDFRAKTUR="bold-fraktur",e.BOLDITALIC="bold-italic",e.BOLDSCRIPT="bold-script",e.DOUBLESTRUCK="double-struck",e.FULLWIDTH="fullwidth",e.FRAKTUR="fraktur",e.ITALIC="italic",e.MONOSPACE="monospace",e.NORMAL="normal",e.SCRIPT="script",e.SANSSERIF="sans-serif",e.SANSSERIFITALIC="sans-serif-italic",e.SANSSERIFBOLD="sans-serif-bold",e.SANSSERIFBOLDITALIC="sans-serif-bold-italic"}(c=t.Font||(t.Font={})),function(e){e.SUPER="super",e.SUB="sub",e.CIRCLED="circled",e.PARENTHESIZED="parenthesized",e.PERIOD="period",e.NEGATIVECIRCLED="negative-circled",e.DOUBLECIRCLED="double-circled",e.CIRCLEDSANSSERIF="circled-sans-serif",e.NEGATIVECIRCLEDSANSSERIF="negative-circled-sans-serif",e.COMMA="comma",e.SQUARED="squared",e.NEGATIVESQUARED="negative-squared"}(l=t.Embellish||(t.Embellish={})),function(e){e.LATINCAP="latinCap",e.LATINSMALL="latinSmall",e.GREEKCAP="greekCap",e.GREEKSMALL="greekSmall",e.DIGIT="digit"}(u=t.Base||(t.Base={})),t.Domains_={small:["default"],capital:["default"],digit:["default"]},t.makeDomains_=d,t.generate=function(e){const n=r.default.getInstance().locale;r.default.getInstance().locale=e,o.setLocale(),a.addSymbolRules({locale:e}),d();const s=t.INTERVALS;for(let e,t=0;e=s[t];t++){const t=h(e.interval,e.subst),n=t.map((function(e){return String.fromCodePoint(parseInt(e,16))}));if("offset"in e)g(t,n,e.font,e.category,e.offset||0);else{m(t,n,i.LOCALE.ALPHABETS[e.base],e.font,e.category,!!e.capital)}}r.default.getInstance().locale=n,o.setLocale()},t.makeInterval=h,t.getFont=f,t.alphabetRules=m,t.numberRules=g,t.makeLetter=b,t.INTERVALS=[{interval:["1D400","1D419"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.BOLD},{interval:["1D41A","1D433"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLD},{interval:["1D56C","1D585"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.BOLDFRAKTUR},{interval:["1D586","1D59F"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLDFRAKTUR},{interval:["1D468","1D481"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.BOLDITALIC},{interval:["1D482","1D49B"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLDITALIC},{interval:["1D4D0","1D4E9"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.BOLDSCRIPT},{interval:["1D4EA","1D503"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLDSCRIPT},{interval:["1D538","1D551"],base:u.LATINCAP,subst:{"1D53A":"2102","1D53F":"210D","1D545":"2115","1D547":"2119","1D548":"211A","1D549":"211D","1D551":"2124"},capital:!0,category:"Lu",font:c.DOUBLESTRUCK},{interval:["1D552","1D56B"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.DOUBLESTRUCK},{interval:["1D504","1D51D"],base:u.LATINCAP,subst:{"1D506":"212D","1D50B":"210C","1D50C":"2111","1D515":"211C","1D51D":"2128"},capital:!0,category:"Lu",font:c.FRAKTUR},{interval:["1D51E","1D537"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.FRAKTUR},{interval:["FF21","FF3A"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.FULLWIDTH},{interval:["FF41","FF5A"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.FULLWIDTH},{interval:["1D434","1D44D"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.ITALIC},{interval:["1D44E","1D467"],base:u.LATINSMALL,subst:{"1D455":"210E"},capital:!1,category:"Ll",font:c.ITALIC},{interval:["1D670","1D689"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.MONOSPACE},{interval:["1D68A","1D6A3"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.MONOSPACE},{interval:["0041","005A"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.NORMAL},{interval:["0061","007A"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.NORMAL},{interval:["1D49C","1D4B5"],base:u.LATINCAP,subst:{"1D49D":"212C","1D4A0":"2130","1D4A1":"2131","1D4A3":"210B","1D4A4":"2110","1D4A7":"2112","1D4A8":"2133","1D4AD":"211B"},capital:!0,category:"Lu",font:c.SCRIPT},{interval:["1D4B6","1D4CF"],base:u.LATINSMALL,subst:{"1D4BA":"212F","1D4BC":"210A","1D4C4":"2134"},capital:!1,category:"Ll",font:c.SCRIPT},{interval:["1D5A0","1D5B9"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIF},{interval:["1D5BA","1D5D3"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIF},{interval:["1D608","1D621"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIFITALIC},{interval:["1D622","1D63B"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIFITALIC},{interval:["1D5D4","1D5ED"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIFBOLD},{interval:["1D5EE","1D607"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIFBOLD},{interval:["1D63C","1D655"],base:u.LATINCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIFBOLDITALIC},{interval:["1D656","1D66F"],base:u.LATINSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIFBOLDITALIC},{interval:["0391","03A9"],base:u.GREEKCAP,subst:{"03A2":"03F4"},capital:!0,category:"Lu",font:c.NORMAL},{interval:["03B0","03D0"],base:u.GREEKSMALL,subst:{"03B0":"2207","03CA":"2202","03CB":"03F5","03CC":"03D1","03CD":"03F0","03CE":"03D5","03CF":"03F1","03D0":"03D6"},capital:!1,category:"Ll",font:c.NORMAL},{interval:["1D6A8","1D6C0"],base:u.GREEKCAP,subst:{},capital:!0,category:"Lu",font:c.BOLD},{interval:["1D6C1","1D6E1"],base:u.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLD},{interval:["1D6E2","1D6FA"],base:u.GREEKCAP,subst:{},capital:!0,category:"Lu",font:c.ITALIC},{interval:["1D6FB","1D71B"],base:u.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:c.ITALIC},{interval:["1D71C","1D734"],base:u.GREEKCAP,subst:{},capital:!0,category:"Lu",font:c.BOLDITALIC},{interval:["1D735","1D755"],base:u.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:c.BOLDITALIC},{interval:["1D756","1D76E"],base:u.GREEKCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIFBOLD},{interval:["1D76F","1D78F"],base:u.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIFBOLD},{interval:["1D790","1D7A8"],base:u.GREEKCAP,subst:{},capital:!0,category:"Lu",font:c.SANSSERIFBOLDITALIC},{interval:["1D7A9","1D7C9"],base:u.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:c.SANSSERIFBOLDITALIC},{interval:["0030","0039"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.NORMAL},{interval:["2070","2079"],base:u.DIGIT,subst:{2071:"00B9",2072:"00B2",2073:"00B3"},offset:0,category:"No",font:l.SUPER},{interval:["2080","2089"],base:u.DIGIT,subst:{},offset:0,category:"No",font:l.SUB},{interval:["245F","2473"],base:u.DIGIT,subst:{"245F":"24EA"},offset:0,category:"No",font:l.CIRCLED},{interval:["3251","325F"],base:u.DIGIT,subst:{},offset:21,category:"No",font:l.CIRCLED},{interval:["32B1","32BF"],base:u.DIGIT,subst:{},offset:36,category:"No",font:l.CIRCLED},{interval:["2474","2487"],base:u.DIGIT,subst:{},offset:1,category:"No",font:l.PARENTHESIZED},{interval:["2487","249B"],base:u.DIGIT,subst:{2487:"1F100"},offset:0,category:"No",font:l.PERIOD},{interval:["2775","277F"],base:u.DIGIT,subst:{2775:"24FF"},offset:0,category:"No",font:l.NEGATIVECIRCLED},{interval:["24EB","24F4"],base:u.DIGIT,subst:{},offset:11,category:"No",font:l.NEGATIVECIRCLED},{interval:["24F5","24FE"],base:u.DIGIT,subst:{},offset:1,category:"No",font:l.DOUBLECIRCLED},{interval:["277F","2789"],base:u.DIGIT,subst:{"277F":"1F10B"},offset:0,category:"No",font:l.CIRCLEDSANSSERIF},{interval:["2789","2793"],base:u.DIGIT,subst:{2789:"1F10C"},offset:0,category:"No",font:l.NEGATIVECIRCLEDSANSSERIF},{interval:["FF10","FF19"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.FULLWIDTH},{interval:["1D7CE","1D7D7"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.BOLD},{interval:["1D7D8","1D7E1"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.DOUBLESTRUCK},{interval:["1D7E2","1D7EB"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.SANSSERIF},{interval:["1D7EC","1D7F5"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.SANSSERIFBOLD},{interval:["1D7F6","1D7FF"],base:u.DIGIT,subst:{},offset:0,category:"Nd",font:c.MONOSPACE},{interval:["1F101","1F10A"],base:u.DIGIT,subst:{},offset:0,category:"No",font:l.COMMA},{interval:["24B6","24CF"],base:u.LATINCAP,subst:{},capital:!0,category:"So",font:l.CIRCLED},{interval:["24D0","24E9"],base:u.LATINSMALL,subst:{},capital:!1,category:"So",font:l.CIRCLED},{interval:["1F110","1F129"],base:u.LATINCAP,subst:{},capital:!0,category:"So",font:l.PARENTHESIZED},{interval:["249C","24B5"],base:u.LATINSMALL,subst:{},capital:!1,category:"So",font:l.PARENTHESIZED},{interval:["1F130","1F149"],base:u.LATINCAP,subst:{},capital:!0,category:"So",font:l.SQUARED},{interval:["1F170","1F189"],base:u.LATINCAP,subst:{},capital:!0,category:"So",font:l.NEGATIVESQUARED},{interval:["1F150","1F169"],base:u.LATINCAP,subst:{},capital:!0,category:"So",font:l.NEGATIVECIRCLED}]},3955:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=t.Comparator=t.ClearspeakPreferences=void 0;const r=n(4886),o=n(4998),i=n(8310),s=n(8310),a=n(4161),c=n(6060);class l extends i.DynamicCstr{constructor(e,t){super(e),this.preference=t}static comparator(){return new d(r.default.getInstance().dynamicCstr,s.DynamicProperties.createProp([i.DynamicCstr.DEFAULT_VALUES[s.Axis.LOCALE]],[i.DynamicCstr.DEFAULT_VALUES[s.Axis.MODALITY]],[i.DynamicCstr.DEFAULT_VALUES[s.Axis.DOMAIN]],[i.DynamicCstr.DEFAULT_VALUES[s.Axis.STYLE]]))}static fromPreference(e){const t=e.split(":"),n={},r=u.getProperties(),o=Object.keys(r);for(let e,i=0;e=t[i];i++){const t=e.split("_");if(-1===o.indexOf(t[0]))continue;const i=t[1];i&&i!==l.AUTO&&-1!==r[t[0]].indexOf(i)&&(n[t[0]]=t[1])}return n}static toPreference(e){const t=Object.keys(e),n=[];for(let r=0;r<t.length;r++)n.push(t[r]+"_"+e[t[r]]);return n.length?n.join(":"):i.DynamicCstr.DEFAULT_VALUE}static getLocalePreferences(e){const t=e||a.enumerate(c.SpeechRuleEngine.getInstance().enumerate());return l.getLocalePreferences_(t)}static smartPreferences(e,t){const n=l.getLocalePreferences()[t];if(!n)return[];const r=e.explorers.speech,i=l.relevantPreferences(r.walker.getFocus().getSemanticPrimary()),s=o.DOMAIN_TO_STYLES.clearspeak;return[{type:"radio",content:"No Preferences",id:"clearspeak-default",variable:"speechRules"},{type:"radio",content:"Current Preferences",id:"clearspeak-"+s,variable:"speechRules"},{type:"rule"},{type:"label",content:"Preferences for "+i},{type:"rule"}].concat(n[i].map((function(e){const t=e.split("_");return{type:"radio",content:t[1],id:"clearspeak-"+l.addPreference(s,t[0],t[1]),variable:"speechRules"}})))}static relevantPreferences(e){const t=f[e.type];return t&&(t[e.role]||t[""])||"ImpliedTimes"}static findPreference(e,t){if("default"===e)return l.AUTO;return l.fromPreference(e)[t]||l.AUTO}static addPreference(e,t,n){if("default"===e)return t+"_"+n;const r=l.fromPreference(e);return r[t]=n,l.toPreference(r)}static getLocalePreferences_(e){const t={};for(const n in e){if(!e[n].speech||!e[n].speech.clearspeak)continue;const r=Object.keys(e[n].speech.clearspeak),o=t[n]={};for(const e in u.getProperties()){const t=u.getProperties()[e],n=[e+"_Auto"];if(t)for(const o of t)-1!==r.indexOf(e+"_"+o)&&n.push(e+"_"+o);o[e]=n}}return t}equal(e){if(!super.equal(e))return!1;const t=Object.keys(this.preference),n=e.preference;if(t.length!==Object.keys(n).length)return!1;for(let e,r=0;e=t[r];r++)if(this.preference[e]!==n[e])return!1;return!0}}t.ClearspeakPreferences=l,l.AUTO="Auto";const u=new s.DynamicProperties({AbsoluteValue:["Auto","AbsEnd","Cardinality","Determinant"],Bar:["Auto","Conjugate"],Caps:["Auto","SayCaps"],CombinationPermutation:["Auto","ChoosePermute"],Currency:["Auto","Position","Prefix"],Ellipses:["Auto","AndSoOn"],Enclosed:["Auto","EndEnclose"],Exponent:["Auto","AfterPower","Ordinal","OrdinalPower","Exponent"],Fraction:["Auto","EndFrac","FracOver","General","GeneralEndFrac","Ordinal","Over","OverEndFrac","Per"],Functions:["Auto","None","Reciprocal"],ImpliedTimes:["Auto","MoreImpliedTimes","None"],Log:["Auto","LnAsNaturalLog"],Matrix:["Auto","Combinatoric","EndMatrix","EndVector","SilentColNum","SpeakColNum","Vector"],MultiLineLabel:["Auto","Case","Constraint","Equation","Line","None","Row","Step"],MultiLineOverview:["Auto","None"],MultiLinePausesBetweenColumns:["Auto","Long","Short"],MultsymbolDot:["Auto","Dot"],MultsymbolX:["Auto","By","Cross"],Paren:["Auto","CoordPoint","Interval","Silent","Speak","SpeakNestingLevel"],Prime:["Auto","Angle","Length"],Roots:["Auto","PosNegSqRoot","PosNegSqRootEnd","RootEnd"],SetMemberSymbol:["Auto","Belongs","Element","Member","In"],Sets:["Auto","SilentBracket","woAll"],TriangleSymbol:["Auto","Delta"],Trig:["Auto","ArcTrig","TrigInverse","Reciprocal"],VerticalLine:["Auto","Divides","Given","SuchThat"]});class d extends s.DefaultComparator{constructor(e,t){super(e,t),this.preference=e instanceof l?e.preference:{}}match(e){if(!(e instanceof l))return super.match(e);if("default"===e.getComponents()[s.Axis.STYLE])return!0;const t=Object.keys(e.preference);for(let n,r=0;n=t[r];r++)if(this.preference[n]!==e.preference[n])return!1;return!0}compare(e,t){const n=super.compare(e,t);if(0!==n)return n;const r=e instanceof l,o=t instanceof l;if(!r&&o)return 1;if(r&&!o)return-1;if(!r&&!o)return 0;const i=Object.keys(e.preference).length,s=Object.keys(t.preference).length;return i>s?-1:i<s?1:0}}t.Comparator=d;class p extends s.DynamicCstrParser{constructor(){super([s.Axis.LOCALE,s.Axis.MODALITY,s.Axis.DOMAIN,s.Axis.STYLE])}parse(e){const t=super.parse(e);let n=t.getValue(s.Axis.STYLE);const r=t.getValue(s.Axis.LOCALE),o=t.getValue(s.Axis.MODALITY);let a={};return n!==i.DynamicCstr.DEFAULT_VALUE&&(a=this.fromPreference(n),n=this.toPreference(a)),new l({locale:r,modality:o,domain:"clearspeak",style:n},a)}fromPreference(e){return l.fromPreference(e)}toPreference(e){return l.toPreference(e)}}t.Parser=p;const h=[["AbsoluteValue","fenced","neutral","metric"],["Bar","overscore","overaccent"],["Caps","identifier","latinletter"],["CombinationPermutation","appl","unknown"],["Ellipses","punctuation","ellipsis"],["Exponent","superscript",""],["Fraction","fraction",""],["Functions","appl","simple function"],["ImpliedTimes","operator","implicit"],["Log","appl","prefix function"],["Matrix","matrix",""],["Matrix","vector",""],["MultiLineLabel","multiline","label"],["MultiLineOverview","multiline","table"],["MultiLinePausesBetweenColumns","multiline","table"],["MultiLineLabel","table","label"],["MultiLineOverview","table","table"],["MultiLinePausesBetweenColumns","table","table"],["MultiLineLabel","cases","label"],["MultiLineOverview","cases","table"],["MultiLinePausesBetweenColumns","cases","table"],["MultsymbolDot","operator","multiplication"],["MultsymbolX","operator","multiplication"],["Paren","fenced","leftright"],["Prime","superscript","prime"],["Roots","root",""],["Roots","sqrt",""],["SetMemberSymbol","relation","element"],["Sets","fenced","set extended"],["TriangleSymbol","identifier","greekletter"],["Trig","appl","prefix function"],["VerticalLine","punctuated","vbar"]],f=function(){const e={};for(let t,n=0;t=h[n];n++){const n=t[0];let r=e[t[1]];r||(r={},e[t[1]]=r),r[t[2]]=n}return e}();r.default.getInstance().comparators.clearspeak=l.comparator,r.default.getInstance().parsers.clearspeak=new p},127:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ClearspeakRules=void 0;const r=n(8310),o=n(931),i=n(8662),s=n(3269),a=n(2110),c=n(7278);t.ClearspeakRules=function(){c.addStore(r.DynamicCstr.BASE_LOCALE+".speech.clearspeak","",{CTFpauseSeparator:o.pauseSeparator,CTFnodeCounter:i.nodeCounter,CTFcontentIterator:o.contentIterator,CSFvulgarFraction:a.vulgarFraction,CQFvulgarFractionSmall:i.isSmallVulgarFraction,CQFcellsSimple:i.allCellsSimple,CSFordinalExponent:i.ordinalExponent,CSFwordOrdinal:i.wordOrdinal,CQFmatchingFences:i.matchingFences,CSFnestingDepth:i.nestingDepth,CQFfencedArguments:i.fencedArguments,CQFsimpleArguments:i.simpleArguments,CQFspaceoutNumber:s.spaceoutNumber})}},8662:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.wordOrdinal=t.layoutFactor_=t.fencedFactor_=t.simpleFactor_=t.simpleArguments=t.fencedArguments=t.insertNesting=t.matchingFences=t.nestingDepth=t.NESTING_DEPTH=t.ordinalExponent=t.allTextLastContent_=t.isUnitExpression=t.isSmallVulgarFraction=t.allCellsSimple=t.allIndices_=t.isInteger_=t.simpleCell_=t.simpleNode=t.hasPreference=t.isSimpleFraction_=t.isSimpleNumber_=t.isNumber_=t.isLetter_=t.isSimple_=t.isSimpleLetters_=t.isSimpleDegree_=t.isSimpleNegative_=t.isSimpleFunction_=t.isSimpleExpression=t.nodeCounter=void 0;const r=n(6671),o=n(4886),i=n(5024),s=n(4524),a=n(9385),c=n(1058),l=n(931),u=n(4036),d=n(241),p=n(4020);function h(e){return N(e)||b(e)||g(e)||m(e)||f(e)}function f(e){return"appl"===e.type&&("prefix function"===e.childNodes[0].role||"simple function"===e.childNodes[0].role)&&(y(e.childNodes[1])||"fenced"===e.childNodes[1].type&&y(e.childNodes[1].childNodes[0]))}function m(e){return"prefixop"===e.type&&"negative"===e.role&&y(e.childNodes[0])&&"prefixop"!==e.childNodes[0].type&&"appl"!==e.childNodes[0].type&&"punctuated"!==e.childNodes[0].type}function g(e){return"punctuated"===e.type&&"endpunct"===e.role&&2===e.childNodes.length&&"degree"===e.childNodes[1].role&&(S(e.childNodes[0])||E(e.childNodes[0])||"prefixop"===e.childNodes[0].type&&"negative"===e.childNodes[0].role&&(S(e.childNodes[0].childNodes[0])||E(e.childNodes[0].childNodes[0])))}function b(e){return S(e)||"infixop"===e.type&&"implicit"===e.role&&(2===e.childNodes.length&&(S(e.childNodes[0])||N(e.childNodes[0]))&&S(e.childNodes[1])||3===e.childNodes.length&&N(e.childNodes[0])&&S(e.childNodes[1])&&S(e.childNodes[2]))}function y(e){return e.hasAnnotation("clearspeak","simple")}function S(e){return"identifier"===e.type&&("latinletter"===e.role||"greekletter"===e.role||"otherletter"===e.role||"simple function"===e.role)}function E(e){return"number"===e.type&&("integer"===e.role||"float"===e.role)}function N(e){return E(e)||A(e)}function A(e){if(_("Fraction_Over")||_("Fraction_FracOver"))return!1;if("fraction"!==e.type||"vulgar"!==e.role)return!1;if(_("Fraction_Ordinal"))return!0;const t=parseInt(e.childNodes[0].textContent,10),n=parseInt(e.childNodes[1].textContent,10);return t>0&&t<20&&n>0&&n<11}function _(e){return o.default.getInstance().style===e}function v(e){if(!e.hasAttribute("annotation"))return!1;const t=e.getAttribute("annotation");return!!/clearspeak:simple$|clearspeak:simple;/.exec(t)}function C(e){if(v(e))return!0;if("subscript"!==e.tagName)return!1;const t=e.childNodes[0].childNodes,n=t[1];return"identifier"===t[0].tagName&&(M(n)||"infixop"===n.tagName&&n.hasAttribute("role")&&"implicit"===n.getAttribute("role")&&O(n))}function M(e){return"number"===e.tagName&&e.hasAttribute("role")&&"integer"===e.getAttribute("role")}function O(e){return i.evalXPath("children/*",e).every((e=>M(e)||"identifier"===e.tagName))}function T(e){return"text"===e.type||"punctuated"===e.type&&"text"===e.role&&E(e.childNodes[0])&&x(e.childNodes.slice(1))||"identifier"===e.type&&"unit"===e.role||"infixop"===e.type&&("implicit"===e.role||"unit"===e.role)}function x(e){for(let t=0;t<e.length-1;t++)if("text"!==e[t].type||""!==e[t].textContent)return!1;return"text"===e[e.length-1].type}function I(e,t){if(!t||!e)return e;const n=e.match(/^(open|close) /);return n?n[0]+t+" "+e.substring(n[0].length):t+" "+e}function L(e){return!!e&&("number"===e.tagName||"identifier"===e.tagName||"function"===e.tagName||"appl"===e.tagName||"fraction"===e.tagName)}function P(e){return e&&("fenced"===e.tagName||e.hasAttribute("role")&&"leftright"===e.getAttribute("role")||R(e))}function R(e){return!!e&&("matrix"===e.tagName||"vector"===e.tagName)}t.nodeCounter=function(e,t){const n=t.split("-"),r=l.nodeCounter(e,n[0]||""),o=n[1]||"",i=n[2]||"";let s=!0;return function(){const e=r();return s?(s=!1,i+e+o):e+o}},t.isSimpleExpression=h,t.isSimpleFunction_=f,t.isSimpleNegative_=m,t.isSimpleDegree_=g,t.isSimpleLetters_=b,t.isSimple_=y,t.isLetter_=S,t.isNumber_=E,t.isSimpleNumber_=N,t.isSimpleFraction_=A,t.hasPreference=_,(0,u.register)(new d.SemanticAnnotator("clearspeak","simple",(function(e){return h(e)?"simple":""}))),t.simpleNode=v,t.simpleCell_=C,t.isInteger_=M,t.allIndices_=O,t.allCellsSimple=function(e){const t="matrix"===e.tagName?"children/row/children/cell/children/*":"children/line/children/*";return i.evalXPath(t,e).every(C)?[e]:[]},t.isSmallVulgarFraction=function(e){return(0,a.vulgarFractionSmall)(e,20,11)?[e]:[]},t.isUnitExpression=T,t.allTextLastContent_=x,(0,u.register)(new d.SemanticAnnotator("clearspeak","unit",(function(e){return T(e)?"unit":""}))),t.ordinalExponent=function(e){const t=parseInt(e.textContent,10);return isNaN(t)?e.textContent:t>10?s.LOCALE.NUMBERS.numericOrdinal(t):s.LOCALE.NUMBERS.wordOrdinal(t)},t.NESTING_DEPTH=null,t.nestingDepth=function(e){let n=0;const r=e.textContent,o="open"===e.getAttribute("role")?0:1;let i=e.parentNode;for(;i;)"fenced"===i.tagName&&i.childNodes[0].childNodes[o].textContent===r&&n++,i=i.parentNode;return t.NESTING_DEPTH=n>1?s.LOCALE.NUMBERS.wordOrdinal(n):"",t.NESTING_DEPTH},t.matchingFences=function(e){const t=e.previousSibling;let n,r;return t?(n=t,r=e):(n=e,r=e.nextSibling),r&&(0,p.isMatchingFence)(n.textContent,r.textContent)?[e]:[]},t.insertNesting=I,c.Grammar.getInstance().setCorrection("insertNesting",I),t.fencedArguments=function(e){const t=r.toArray(e.parentNode.childNodes),n=i.evalXPath("../../children/*",e),o=t.indexOf(e);return P(n[o])||P(n[o+1])?[e]:[]},t.simpleArguments=function(e){const t=r.toArray(e.parentNode.childNodes),n=i.evalXPath("../../children/*",e),o=t.indexOf(e);return L(n[o])&&n[o+1]&&(L(n[o+1])||"root"===n[o+1].tagName||"sqrt"===n[o+1].tagName||"superscript"===n[o+1].tagName&&n[o+1].childNodes[0].childNodes[0]&&("number"===n[o+1].childNodes[0].childNodes[0].tagName||"identifier"===n[o+1].childNodes[0].childNodes[0].tagName)&&("2"===n[o+1].childNodes[0].childNodes[1].textContent||"3"===n[o+1].childNodes[0].childNodes[1].textContent))?[e]:[]},t.simpleFactor_=L,t.fencedFactor_=P,t.layoutFactor_=R,t.wordOrdinal=function(e){return s.LOCALE.NUMBERS.wordOrdinal(parseInt(e.textContent,10))}},5659:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.loadAjax=t.loadFileSync=t.loadFile=t.parseMaps=t.retrieveFiles=t.standardLoader=t.loadLocale=t.store=void 0;const o=n(9501),i=n(4886),s=n(4998),a=n(7129),c=n(4755),l=n(8310),u=n(4161),d=n(6060),p=n(2371),h=n(650);t.store=u;const f={functions:u.addFunctionRules,symbols:u.addSymbolRules,units:u.addUnitRules,si:u.setSiPrefixes};let m=!1;function g(e=i.default.getInstance().locale){i.EnginePromise.loaded[e]||(i.EnginePromise.loaded[e]=[!1,!1],function(e){if(i.default.getInstance().isIE&&i.default.getInstance().mode===s.Mode.HTTP)return void N(e);y(e)}(e))}function b(){switch(i.default.getInstance().mode){case s.Mode.ASYNC:return A;case s.Mode.HTTP:return v;case s.Mode.SYNC:default:return _}}function y(e){const t=i.default.getInstance().customLoader?i.default.getInstance().customLoader:b(),n=new Promise((n=>{t(e).then((t=>{S(t),i.EnginePromise.loaded[e]=[!0,!0],n(e)}),(t=>{i.EnginePromise.loaded[e]=[!0,!1],console.error(`Unable to load locale: ${e}`),i.default.getInstance().locale=i.default.getInstance().defaultLocale,n(e)}))}));i.EnginePromise.promises[e]=n}function S(e){E(JSON.parse(e))}function E(e,t){let n=!0;for(let r,o=0;r=Object.keys(e)[o];o++){const o=r.split("/");t&&t!==o[0]||("rules"===o[1]?d.SpeechRuleEngine.getInstance().addStore(e[r]):"messages"===o[1]?(0,p.completeLocale)(e[r]):(n&&(h.generate(o[0]),n=!1),e[r].forEach(f[o[1]])))}}function N(e,t){let n=t||1;o.mapsForIE?E(o.mapsForIE,e):n<=5&&setTimeout((()=>N(e,n++)).bind(this),300)}function A(e){const t=a.localePath(e);return new Promise(((e,n)=>{c.default.fs.readFile(t,"utf8",((t,r)=>{if(t)return n(t);e(r)}))}))}function _(e){const t=a.localePath(e);return new Promise(((e,n)=>{let r="{}";try{r=c.default.fs.readFileSync(t,"utf8")}catch(e){return n(e)}e(r)}))}function v(e){const t=a.localePath(e),n=new XMLHttpRequest;return new Promise(((e,r)=>{n.onreadystatechange=function(){if(4===n.readyState){const t=n.status;0===t||t>=200&&t<400?e(n.responseText):r(t)}},n.open("GET",t,!0),n.send()}))}t.loadLocale=function(e=i.default.getInstance().locale){return r(this,void 0,void 0,(function*(){return m||(g(l.DynamicCstr.BASE_LOCALE),m=!0),i.EnginePromise.promises[l.DynamicCstr.BASE_LOCALE].then((()=>r(this,void 0,void 0,(function*(){const t=i.default.getInstance().defaultLocale;return t?(g(t),i.EnginePromise.promises[t].then((()=>r(this,void 0,void 0,(function*(){return g(e),i.EnginePromise.promises[e]}))))):(g(e),i.EnginePromise.promises[e])}))))}))},t.standardLoader=b,t.retrieveFiles=y,t.parseMaps=S,t.loadFile=A,t.loadFileSync=_,t.loadAjax=v},3784:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.leftSubscriptBrief=t.leftSuperscriptBrief=t.leftSubscriptVerbose=t.leftSuperscriptVerbose=t.baselineBrief=t.baselineVerbose=void 0;const r=n(3269);t.baselineVerbose=function(e){return r.baselineVerbose(e).replace(/-$/,"")},t.baselineBrief=function(e){return r.baselineBrief(e).replace(/-$/,"")},t.leftSuperscriptVerbose=function(e){return r.superscriptVerbose(e).replace(/^exposant/,"exposant gauche")},t.leftSubscriptVerbose=function(e){return r.subscriptVerbose(e).replace(/^indice/,"indice gauche")},t.leftSuperscriptBrief=function(e){return r.superscriptBrief(e).replace(/^sup/,"sup gauche")},t.leftSubscriptBrief=function(e){return r.subscriptBrief(e).replace(/^sub/,"sub gauche")}},4972:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MathspeakRules=void 0;const r=n(8310),o=n(931),i=n(3784),s=n(3269),a=n(2110),c=n(7278),l=n(9771);t.MathspeakRules=function(){c.addStore(r.DynamicCstr.BASE_LOCALE+".speech.mathspeak","",{CQFspaceoutNumber:s.spaceoutNumber,CQFspaceoutIdentifier:s.spaceoutIdentifier,CSFspaceoutText:s.spaceoutText,CSFopenFracVerbose:s.openingFractionVerbose,CSFcloseFracVerbose:s.closingFractionVerbose,CSFoverFracVerbose:s.overFractionVerbose,CSFopenFracBrief:s.openingFractionBrief,CSFcloseFracBrief:s.closingFractionBrief,CSFopenFracSbrief:s.openingFractionSbrief,CSFcloseFracSbrief:s.closingFractionSbrief,CSFoverFracSbrief:s.overFractionSbrief,CSFvulgarFraction:a.vulgarFraction,CQFvulgarFractionSmall:s.isSmallVulgarFraction,CSFopenRadicalVerbose:s.openingRadicalVerbose,CSFcloseRadicalVerbose:s.closingRadicalVerbose,CSFindexRadicalVerbose:s.indexRadicalVerbose,CSFopenRadicalBrief:s.openingRadicalBrief,CSFcloseRadicalBrief:s.closingRadicalBrief,CSFindexRadicalBrief:s.indexRadicalBrief,CSFopenRadicalSbrief:s.openingRadicalSbrief,CSFindexRadicalSbrief:s.indexRadicalSbrief,CQFisSmallRoot:s.smallRoot,CSFsuperscriptVerbose:s.superscriptVerbose,CSFsuperscriptBrief:s.superscriptBrief,CSFsubscriptVerbose:s.subscriptVerbose,CSFsubscriptBrief:s.subscriptBrief,CSFbaselineVerbose:s.baselineVerbose,CSFbaselineBrief:s.baselineBrief,CSFleftsuperscriptVerbose:s.superscriptVerbose,CSFleftsubscriptVerbose:s.subscriptVerbose,CSFrightsuperscriptVerbose:s.superscriptVerbose,CSFrightsubscriptVerbose:s.subscriptVerbose,CSFleftsuperscriptBrief:s.superscriptBrief,CSFleftsubscriptBrief:s.subscriptBrief,CSFrightsuperscriptBrief:s.superscriptBrief,CSFrightsubscriptBrief:s.subscriptBrief,CSFunderscript:s.nestedUnderscript,CSFoverscript:s.nestedOverscript,CSFendscripts:s.endscripts,CTFordinalCounter:a.ordinalCounter,CTFwordCounter:a.wordCounter,CTFcontentIterator:o.contentIterator,CQFdetIsSimple:s.determinantIsSimple,CSFRemoveParens:s.removeParens,CQFresetNesting:s.resetNestingDepth,CGFbaselineConstraint:s.generateBaselineConstraint,CGFtensorRules:s.generateTensorRules}),c.addStore("es.speech.mathspeak",r.DynamicCstr.BASE_LOCALE+".speech.mathspeak",{CTFunitMultipliers:l.unitMultipliers,CQFoneLeft:l.oneLeft}),c.addStore("fr.speech.mathspeak",r.DynamicCstr.BASE_LOCALE+".speech.mathspeak",{CSFbaselineVerbose:i.baselineVerbose,CSFbaselineBrief:i.baselineBrief,CSFleftsuperscriptVerbose:i.leftSuperscriptVerbose,CSFleftsubscriptVerbose:i.leftSubscriptVerbose,CSFleftsuperscriptBrief:i.leftSuperscriptBrief,CSFleftsubscriptBrief:i.leftSubscriptBrief})}},3269:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.smallRoot=t.generateTensorRules=t.removeParens=t.generateBaselineConstraint=t.determinantIsSimple=t.nestedOverscript=t.endscripts=t.overscoreNestingDepth=t.nestedUnderscript=t.underscoreNestingDepth=t.indexRadicalSbrief=t.openingRadicalSbrief=t.indexRadicalBrief=t.closingRadicalBrief=t.openingRadicalBrief=t.indexRadicalVerbose=t.closingRadicalVerbose=t.openingRadicalVerbose=t.getRootIndex=t.nestedRadical=t.radicalNestingDepth=t.baselineBrief=t.baselineVerbose=t.superscriptBrief=t.superscriptVerbose=t.subscriptBrief=t.subscriptVerbose=t.nestedSubSuper=t.isSmallVulgarFraction=t.overFractionSbrief=t.closingFractionSbrief=t.openingFractionSbrief=t.closingFractionBrief=t.openingFractionBrief=t.overFractionVerbose=t.closingFractionVerbose=t.openingFractionVerbose=t.nestedFraction=t.fractionNestingDepth=t.computeNestingDepth_=t.containsAttr=t.getNestingDepth=t.resetNestingDepth=t.nestingBarriers=t.spaceoutIdentifier=t.spaceoutNumber=t.spaceoutNodes=t.spaceoutText=void 0;const r=n(1426),o=n(6671),i=n(5024),s=n(4524),a=n(7793);let c={};function l(e,t){const n=Array.from(e.textContent),r=[],o=a.default.getInstance(),i=e.ownerDocument;for(let e,s=0;e=n[s];s++){const n=o.getNodeFactory().makeLeafNode(e,"unknown"),s=o.identifierNode(n,"unknown","");t(s),r.push(s.xml(i))}return r}function u(e,n,i,s,a,l){s=s||t.nestingBarriers,a=a||{},l=l||function(e){return!1};const u=o.serializeXml(n);if(c[e]||(c[e]={}),c[e][u])return c[e][u];if(l(n)||i.indexOf(n.tagName)<0)return 0;const d=p(n,i,r.setdifference(s,i),a,l,0);return c[e][u]=d,d}function d(e,t){if(!e.attributes)return!1;const n=o.toArray(e.attributes);for(let e,r=0;e=n[r];r++)if(t[e.nodeName]===e.nodeValue)return!0;return!1}function p(e,t,n,r,i,s){if(i(e)||n.indexOf(e.tagName)>-1||d(e,r))return s;if(t.indexOf(e.tagName)>-1&&s++,!e.childNodes||0===e.childNodes.length)return s;const a=o.toArray(e.childNodes);return Math.max.apply(null,a.map((function(e){return p(e,t,n,r,i,s)})))}function h(e){return u("fraction",e,["fraction"],t.nestingBarriers,{},s.LOCALE.FUNCTIONS.fracNestDepth)}function f(e,t,n){const r=h(e),o=Array(r).fill(t);return n&&o.push(n),o.join(s.LOCALE.MESSAGES.regexp.JOINER_FRAC)}function m(e,t,n){for(;e.parentNode;){const r=e.parentNode,o=r.parentNode;if(!o)break;const i=e.getAttribute&&e.getAttribute("role");("subscript"===o.tagName&&e===r.childNodes[1]||"tensor"===o.tagName&&i&&("leftsub"===i||"rightsub"===i))&&(t=n.sub+s.LOCALE.MESSAGES.regexp.JOINER_SUBSUPER+t),("superscript"===o.tagName&&e===r.childNodes[1]||"tensor"===o.tagName&&i&&("leftsuper"===i||"rightsuper"===i))&&(t=n.sup+s.LOCALE.MESSAGES.regexp.JOINER_SUBSUPER+t),e=o}return t.trim()}function g(e){return u("radical",e,["sqrt","root"],t.nestingBarriers,{})}function b(e,t,n){const r=g(e),o=y(e);return n=o?s.LOCALE.FUNCTIONS.combineRootIndex(n,o):n,1===r?n:s.LOCALE.FUNCTIONS.combineNestedRadical(t,s.LOCALE.FUNCTIONS.radicalNestDepth(r-1),n)}function y(e){const t="sqrt"===e.tagName?"2":i.evalXPath("children/*[1]",e)[0].textContent.trim();return s.LOCALE.MESSAGES.MSroots[t]||""}function S(e){return u("underscore",e,["underscore"],t.nestingBarriers,{},(function(e){return e.tagName&&"underscore"===e.tagName&&"underaccent"===e.childNodes[0].childNodes[1].getAttribute("role")}))}function E(e){return u("overscore",e,["overscore"],t.nestingBarriers,{},(function(e){return e.tagName&&"overscore"===e.tagName&&"overaccent"===e.childNodes[0].childNodes[1].getAttribute("role")}))}t.spaceoutText=function(e){return Array.from(e.textContent).join(" ")},t.spaceoutNodes=l,t.spaceoutNumber=function(e){return l(e,(function(e){e.textContent.match(/\W/)||(e.type="number")}))},t.spaceoutIdentifier=function(e){return l(e,(function(e){e.font="unknown",e.type="identifier"}))},t.nestingBarriers=["cases","cell","integral","line","matrix","multiline","overscore","root","row","sqrt","subscript","superscript","table","underscore","vector"],t.resetNestingDepth=function(e){return c={},[e]},t.getNestingDepth=u,t.containsAttr=d,t.computeNestingDepth_=p,t.fractionNestingDepth=h,t.nestedFraction=f,t.openingFractionVerbose=function(e){return f(e,s.LOCALE.MESSAGES.MS.START,s.LOCALE.MESSAGES.MS.FRAC_V)},t.closingFractionVerbose=function(e){return f(e,s.LOCALE.MESSAGES.MS.END,s.LOCALE.MESSAGES.MS.FRAC_V)},t.overFractionVerbose=function(e){return f(e,s.LOCALE.MESSAGES.MS.FRAC_OVER)},t.openingFractionBrief=function(e){return f(e,s.LOCALE.MESSAGES.MS.START,s.LOCALE.MESSAGES.MS.FRAC_B)},t.closingFractionBrief=function(e){return f(e,s.LOCALE.MESSAGES.MS.END,s.LOCALE.MESSAGES.MS.FRAC_B)},t.openingFractionSbrief=function(e){const t=h(e);return 1===t?s.LOCALE.MESSAGES.MS.FRAC_S:s.LOCALE.FUNCTIONS.combineNestedFraction(s.LOCALE.MESSAGES.MS.NEST_FRAC,s.LOCALE.FUNCTIONS.radicalNestDepth(t-1),s.LOCALE.MESSAGES.MS.FRAC_S)},t.closingFractionSbrief=function(e){const t=h(e);return 1===t?s.LOCALE.MESSAGES.MS.ENDFRAC:s.LOCALE.FUNCTIONS.combineNestedFraction(s.LOCALE.MESSAGES.MS.NEST_FRAC,s.LOCALE.FUNCTIONS.radicalNestDepth(t-1),s.LOCALE.MESSAGES.MS.ENDFRAC)},t.overFractionSbrief=function(e){const t=h(e);return 1===t?s.LOCALE.MESSAGES.MS.FRAC_OVER:s.LOCALE.FUNCTIONS.combineNestedFraction(s.LOCALE.MESSAGES.MS.NEST_FRAC,s.LOCALE.FUNCTIONS.radicalNestDepth(t-1),s.LOCALE.MESSAGES.MS.FRAC_OVER)},t.isSmallVulgarFraction=function(e){return s.LOCALE.FUNCTIONS.fracNestDepth(e)?[e]:[]},t.nestedSubSuper=m,t.subscriptVerbose=function(e){return m(e,s.LOCALE.MESSAGES.MS.SUBSCRIPT,{sup:s.LOCALE.MESSAGES.MS.SUPER,sub:s.LOCALE.MESSAGES.MS.SUB})},t.subscriptBrief=function(e){return m(e,s.LOCALE.MESSAGES.MS.SUB,{sup:s.LOCALE.MESSAGES.MS.SUP,sub:s.LOCALE.MESSAGES.MS.SUB})},t.superscriptVerbose=function(e){return m(e,s.LOCALE.MESSAGES.MS.SUPERSCRIPT,{sup:s.LOCALE.MESSAGES.MS.SUPER,sub:s.LOCALE.MESSAGES.MS.SUB})},t.superscriptBrief=function(e){return m(e,s.LOCALE.MESSAGES.MS.SUP,{sup:s.LOCALE.MESSAGES.MS.SUP,sub:s.LOCALE.MESSAGES.MS.SUB})},t.baselineVerbose=function(e){const t=m(e,"",{sup:s.LOCALE.MESSAGES.MS.SUPER,sub:s.LOCALE.MESSAGES.MS.SUB});return t?t.replace(new RegExp(s.LOCALE.MESSAGES.MS.SUB+"$"),s.LOCALE.MESSAGES.MS.SUBSCRIPT).replace(new RegExp(s.LOCALE.MESSAGES.MS.SUPER+"$"),s.LOCALE.MESSAGES.MS.SUPERSCRIPT):s.LOCALE.MESSAGES.MS.BASELINE},t.baselineBrief=function(e){return m(e,"",{sup:s.LOCALE.MESSAGES.MS.SUP,sub:s.LOCALE.MESSAGES.MS.SUB})||s.LOCALE.MESSAGES.MS.BASE},t.radicalNestingDepth=g,t.nestedRadical=b,t.getRootIndex=y,t.openingRadicalVerbose=function(e){return b(e,s.LOCALE.MESSAGES.MS.NESTED,s.LOCALE.MESSAGES.MS.STARTROOT)},t.closingRadicalVerbose=function(e){return b(e,s.LOCALE.MESSAGES.MS.NESTED,s.LOCALE.MESSAGES.MS.ENDROOT)},t.indexRadicalVerbose=function(e){return b(e,s.LOCALE.MESSAGES.MS.NESTED,s.LOCALE.MESSAGES.MS.ROOTINDEX)},t.openingRadicalBrief=function(e){return b(e,s.LOCALE.MESSAGES.MS.NEST_ROOT,s.LOCALE.MESSAGES.MS.STARTROOT)},t.closingRadicalBrief=function(e){return b(e,s.LOCALE.MESSAGES.MS.NEST_ROOT,s.LOCALE.MESSAGES.MS.ENDROOT)},t.indexRadicalBrief=function(e){return b(e,s.LOCALE.MESSAGES.MS.NEST_ROOT,s.LOCALE.MESSAGES.MS.ROOTINDEX)},t.openingRadicalSbrief=function(e){return b(e,s.LOCALE.MESSAGES.MS.NEST_ROOT,s.LOCALE.MESSAGES.MS.ROOT)},t.indexRadicalSbrief=function(e){return b(e,s.LOCALE.MESSAGES.MS.NEST_ROOT,s.LOCALE.MESSAGES.MS.INDEX)},t.underscoreNestingDepth=S,t.nestedUnderscript=function(e){const t=S(e);return Array(t).join(s.LOCALE.MESSAGES.MS.UNDER)+s.LOCALE.MESSAGES.MS.UNDERSCRIPT},t.overscoreNestingDepth=E,t.endscripts=function(e){return s.LOCALE.MESSAGES.MS.ENDSCRIPTS},t.nestedOverscript=function(e){const t=E(e);return Array(t).join(s.LOCALE.MESSAGES.MS.OVER)+s.LOCALE.MESSAGES.MS.OVERSCRIPT},t.determinantIsSimple=function(e){if("matrix"!==e.tagName||"determinant"!==e.getAttribute("role"))return[];const t=i.evalXPath("children/row/children/cell/children/*",e);for(let e,n=0;e=t[n];n++)if("number"!==e.tagName){if("identifier"===e.tagName){const t=e.getAttribute("role");if("latinletter"===t||"greekletter"===t||"otherletter"===t)continue}return[]}return[e]},t.generateBaselineConstraint=function(){const e=e=>e.map((e=>"ancestor::"+e)),t=e=>"not("+e+")",n=t(e(["subscript","superscript","tensor"]).join(" or ")),r=e(["relseq","multrel"]),o=e(["fraction","punctuation","fenced","sqrt","root"]);let i=[];for(let e,t=0;e=o[t];t++)i=i.concat(r.map((function(t){return e+"/"+t})));return[["ancestor::*/following-sibling::*",n,t(i.join(" | "))].join(" and ")]},t.removeParens=function(e){if(!e.childNodes.length||!e.childNodes[0].childNodes.length||!e.childNodes[0].childNodes[0].childNodes.length)return"";const t=e.childNodes[0].childNodes[0].childNodes[0].textContent;return t.match(/^\(.+\)$/)?t.slice(1,-1):t};const N=new Map([[3,"CSFleftsuperscript"],[4,"CSFleftsubscript"],[2,"CSFbaseline"],[1,"CSFrightsubscript"],[0,"CSFrightsuperscript"]]),A=new Map([[4,2],[3,3],[2,1],[1,4],[0,5]]);function _(e){const t=[];let n="",r="",o=parseInt(e,2);for(let e=0;e<5;e++){const i="children/*["+A.get(e)+"]";if(1&o){const t=N.get(e%5);n="[t] "+t+"Verbose; [n] "+i+";"+n,r="[t] "+t+"Brief; [n] "+i+";"+r}else t.unshift("name("+i+')="empty"');o>>=1}return[t,n,r]}t.generateTensorRules=function(e,t=!0){const n=["11111","11110","11101","11100","10111","10110","10101","10100","01111","01110","01101","01100"];for(let r,o=0;r=n[o];o++){let n="tensor"+r,[o,i,s]=_(r);e.defineRule(n,"default",i,"self::tensor",...o),t&&(e.defineRule(n,"brief",s,"self::tensor",...o),e.defineRule(n,"sbrief",s,"self::tensor",...o));const a=N.get(2);i+="; [t]"+a+"Verbose",s+="; [t]"+a+"Brief",n+="-baseline";const c="((.//*[not(*)])[last()]/@id)!=(((.//ancestor::fraction|ancestor::root|ancestor::sqrt|ancestor::cell|ancestor::line|ancestor::stree)[1]//*[not(*)])[last()]/@id)";e.defineRule(n,"default",i,"self::tensor",c,...o),t&&(e.defineRule(n,"brief",s,"self::tensor",c,...o),e.defineRule(n,"sbrief",s,"self::tensor",c,...o))}},t.smallRoot=function(e){let t=Object.keys(s.LOCALE.MESSAGES.MSroots).length;if(!t)return[];if(t++,!e.childNodes||0===e.childNodes.length||!e.childNodes[0].childNodes)return[];const n=e.childNodes[0].childNodes[0].textContent;if(!/^\d+$/.test(n))return[];const r=parseInt(n,10);return r>1&&r<=t?[e]:[]}},9570:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.implicitIterator=t.relationIterator=t.propagateNumber=t.checkParent_=t.NUMBER_INHIBITORS_=t.NUMBER_PROPAGATORS_=t.enlargeFence=t.indexRadical=t.closingRadical=t.openingRadical=t.radicalNestingDepth=t.nestedRadical=t.overBevelledFraction=t.overFraction=t.closingFraction=t.openingFraction=void 0;const r=n(4148),o=n(6671),i=n(5024),s=n(1058),a=n(4886),c=n(4036),l=n(241),u=n(4524),d=n(3269);function p(e,t){const n=h(e);return 1===n?t:new Array(n).join(u.LOCALE.MESSAGES.MS.NESTED)+t}function h(e,t){const n=t||0;return e.parentNode?h(e.parentNode,"root"===e.tagName||"sqrt"===e.tagName?n+1:n):n}function f(e){const t="\u2820";if(1===e.length)return t+e;const n=e.split("");return n.every((function(e){return"\u2833"===e}))?t+n.join(t):e.slice(0,-1)+t+e.slice(-1)}function m(e,n){const r=e.parent;if(!r)return!1;const o=r.type;return-1!==t.NUMBER_PROPAGATORS_.indexOf(o)||"prefixop"===o&&"negative"===r.role&&!n.script||"prefixop"===o&&"geometry"===r.role||!("punctuated"!==o||n.enclosed&&"text"!==r.role)}function g(e,n){return e.childNodes.length?(-1!==t.NUMBER_INHIBITORS_.indexOf(e.type)&&(n.script=!0),"fenced"===e.type?(n.number=!1,n.enclosed=!0,["",n]):(m(e,n)&&(n.number=!0,n.enclosed=!1),["",n])):(m(e,n)&&(n.number=!0,n.script=!1,n.enclosed=!1),[n.number?"number":"",{number:!1,enclosed:n.enclosed,script:n.script}])}t.openingFraction=function(e){const t=d.fractionNestingDepth(e);return new Array(t).join(u.LOCALE.MESSAGES.MS.FRACTION_REPEAT)+u.LOCALE.MESSAGES.MS.FRACTION_START},t.closingFraction=function(e){const t=d.fractionNestingDepth(e);return new Array(t).join(u.LOCALE.MESSAGES.MS.FRACTION_REPEAT)+u.LOCALE.MESSAGES.MS.FRACTION_END},t.overFraction=function(e){const t=d.fractionNestingDepth(e);return new Array(t).join(u.LOCALE.MESSAGES.MS.FRACTION_REPEAT)+u.LOCALE.MESSAGES.MS.FRACTION_OVER},t.overBevelledFraction=function(e){const t=d.fractionNestingDepth(e);return new Array(t).join(u.LOCALE.MESSAGES.MS.FRACTION_REPEAT)+"\u2838"+u.LOCALE.MESSAGES.MS.FRACTION_OVER},t.nestedRadical=p,t.radicalNestingDepth=h,t.openingRadical=function(e){return p(e,u.LOCALE.MESSAGES.MS.STARTROOT)},t.closingRadical=function(e){return p(e,u.LOCALE.MESSAGES.MS.ENDROOT)},t.indexRadical=function(e){return p(e,u.LOCALE.MESSAGES.MS.ROOTINDEX)},t.enlargeFence=f,s.Grammar.getInstance().setCorrection("enlargeFence",f),t.NUMBER_PROPAGATORS_=["multirel","relseq","appl","row","line"],t.NUMBER_INHIBITORS_=["subscript","superscript","overscore","underscore"],t.checkParent_=m,t.propagateNumber=g,(0,c.register)(new l.SemanticVisitor("nemeth","number",g,{number:!0})),t.relationIterator=function(e,t){const n=e.slice(0);let s,c=!0;return s=e.length>0?i.evalXPath("../../content/*",e[0]):[],function(){const e=s.shift(),i=n.shift(),l=n[0],p=t?[r.AuditoryDescription.create({text:t},{translate:!0})]:[];if(!e)return p;const h=i?d.nestedSubSuper(i,"",{sup:u.LOCALE.MESSAGES.MS.SUPER,sub:u.LOCALE.MESSAGES.MS.SUB}):"",f=i&&"EMPTY"!==o.tagName(i)||c&&e.parentNode.parentNode&&e.parentNode.parentNode.previousSibling?[r.AuditoryDescription.create({text:"\u2800"+h},{})]:[],m=l&&"EMPTY"!==o.tagName(l)||!s.length&&e.parentNode.parentNode&&e.parentNode.parentNode.nextSibling?[r.AuditoryDescription.create({text:"\u2800"},{})]:[],g=a.default.evaluateNode(e);return c=!1,p.concat(f,g,m)}},t.implicitIterator=function(e,t){const n=e.slice(0);let s;return s=e.length>0?i.evalXPath("../../content/*",e[0]):[],function(){const e=n.shift(),i=n[0],a=s.shift(),c=t?[r.AuditoryDescription.create({text:t},{translate:!0})]:[];if(!a)return c;const l=e&&"NUMBER"===o.tagName(e),u=i&&"NUMBER"===o.tagName(i);return c.concat(l&&u&&"space"===a.getAttribute("role")?[r.AuditoryDescription.create({text:"\u2800"},{})]:[])}}},2110:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ordinalPosition=t.vulgarFraction=t.wordCounter=t.ordinalCounter=void 0;const r=n(1930),o=n(6671),i=n(4524),s=n(9385);t.ordinalCounter=function(e,t){let n=0;return function(){return i.LOCALE.NUMBERS.numericOrdinal(++n)+" "+t}},t.wordCounter=function(e,t){let n=0;return function(){return i.LOCALE.NUMBERS.numberToOrdinal(++n,!1)+" "+t}},t.vulgarFraction=function(e){const t=(0,s.convertVulgarFraction)(e,i.LOCALE.MESSAGES.MS.FRAC_OVER);return t.convertible&&t.enumerator&&t.denominator?[new r.Span(i.LOCALE.NUMBERS.numberToWords(t.enumerator),{extid:e.childNodes[0].childNodes[0].getAttribute("extid"),separator:""}),new r.Span(i.LOCALE.NUMBERS.vulgarSep,{separator:""}),new r.Span(i.LOCALE.NUMBERS.numberToOrdinal(t.denominator,1!==t.enumerator),{extid:e.childNodes[0].childNodes[1].getAttribute("extid")})]:[new r.Span(t.content||"",{extid:e.getAttribute("extid")})]},t.ordinalPosition=function(e){const t=o.toArray(e.parentNode.childNodes);return i.LOCALE.NUMBERS.numericOrdinal(t.indexOf(e)+1).toString()}},3724:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.BrailleRules=t.OtherRules=t.PrefixRules=void 0;const r=n(8310),o=n(931),i=n(3269),s=n(9570),a=n(2110),c=n(7278);t.PrefixRules=function(){c.addStore("en.prefix.default","",{CSFordinalPosition:a.ordinalPosition})},t.OtherRules=function(){c.addStore("en.speech.chromevox","",{CTFnodeCounter:o.nodeCounter,CTFcontentIterator:o.contentIterator}),c.addStore("en.speech.emacspeak","en.speech.chromevox",{CQFvulgarFractionSmall:i.isSmallVulgarFraction,CSFvulgarFraction:a.vulgarFraction})},t.BrailleRules=function(){c.addStore("nemeth.braille.default",r.DynamicCstr.BASE_LOCALE+".speech.mathspeak",{CSFopenFraction:s.openingFraction,CSFcloseFraction:s.closingFraction,CSFoverFraction:s.overFraction,CSFoverBevFraction:s.overBevelledFraction,CSFopenRadical:s.openingRadical,CSFcloseRadical:s.closingRadical,CSFindexRadical:s.indexRadical,CSFsubscript:i.subscriptVerbose,CSFsuperscript:i.superscriptVerbose,CSFbaseline:i.baselineVerbose,CGFtensorRules:e=>i.generateTensorRules(e,!1),CTFrelationIterator:s.relationIterator,CTFimplicitIterator:s.implicitIterator})}},9805:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.init=t.INIT_=void 0;const r=n(127),o=n(4972),i=n(3724);t.INIT_=!1,t.init=function(){t.INIT_||((0,o.MathspeakRules)(),(0,r.ClearspeakRules)(),(0,i.PrefixRules)(),(0,i.OtherRules)(),(0,i.BrailleRules)(),t.INIT_=!0)}},7278:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getStore=t.addStore=t.funcStore=void 0;const r=n(8310);t.funcStore=new Map,t.addStore=function(e,n,r){const o={};if(n){const e=t.funcStore.get(n)||{};Object.assign(o,e)}t.funcStore.set(e,Object.assign(o,r))},t.getStore=function(e,n,o){return t.funcStore.get([e,n,o].join("."))||t.funcStore.get([r.DynamicCstr.DEFAULT_VALUES[r.Axis.LOCALE],n,o].join("."))||t.funcStore.get([r.DynamicCstr.BASE_LOCALE,n,o].join("."))||{}}},9771:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.oneLeft=t.leftMostUnit=t.rightMostUnit=t.unitMultipliers=void 0;const r=n(4148),o=n(5024),i=n(4524);t.unitMultipliers=function(e,t){const n=e;let o=0;return function(){const e=r.AuditoryDescription.create({text:a(n[o])&&c(n[o+1])?i.LOCALE.MESSAGES.unitTimes:""},{});return o++,[e]}};const s=["superscript","subscript","overscore","underscore"];function a(e){for(;e;){if("unit"===e.getAttribute("role"))return!0;const t=e.tagName,n=o.evalXPath("children/*",e);e=-1!==s.indexOf(t)?n[0]:n[n.length-1]}return!1}function c(e){for(;e;){if("unit"===e.getAttribute("role"))return!0;e=o.evalXPath("children/*",e)[0]}return!1}t.rightMostUnit=a,t.leftMostUnit=c,t.oneLeft=function(e){for(;e;){if("number"===e.tagName&&"1"===e.textContent)return[e];if("infixop"!==e.tagName||"multiplication"!==e.getAttribute("role")&&"implicit"!==e.getAttribute("role"))return[];e=o.evalXPath("children/*",e)[0]}return[]}},4660:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractWalker=void 0;const r=n(4148),o=n(4253),i=n(6671),s=n(4998),a=n(985),c=n(6988),l=n(8171),u=n(4524),d=n(1058),p=n(7984),h=n(7317),f=n(144),m=n(3955),g=n(5658),b=n(1848),y=n(8119),S=n(8835),E=n(5024);class N{constructor(e,t,n,r){this.node=e,this.generator=t,this.highlighter=n,this.modifier=!1,this.keyMapping=new Map([[c.KeyCode.UP,this.up.bind(this)],[c.KeyCode.DOWN,this.down.bind(this)],[c.KeyCode.RIGHT,this.right.bind(this)],[c.KeyCode.LEFT,this.left.bind(this)],[c.KeyCode.TAB,this.repeat.bind(this)],[c.KeyCode.DASH,this.expand.bind(this)],[c.KeyCode.SPACE,this.depth.bind(this)],[c.KeyCode.HOME,this.home.bind(this)],[c.KeyCode.X,this.summary.bind(this)],[c.KeyCode.Z,this.detail.bind(this)],[c.KeyCode.V,this.virtualize.bind(this)],[c.KeyCode.P,this.previous.bind(this)],[c.KeyCode.U,this.undo.bind(this)],[c.KeyCode.LESS,this.previousRules.bind(this)],[c.KeyCode.GREATER,this.nextRules.bind(this)]]),this.cursors=[],this.xml_=null,this.rebuilt_=null,this.focus_=null,this.active_=!1,this.node.id?this.id=this.node.id:this.node.hasAttribute(N.SRE_ID_ATTR)?this.id=this.node.getAttribute(N.SRE_ID_ATTR):(this.node.setAttribute(N.SRE_ID_ATTR,N.ID_COUNTER.toString()),this.id=N.ID_COUNTER++),this.rootNode=S.getSemanticRoot(e),this.rootId=this.rootNode.getAttribute(l.Attribute.ID),this.xmlString_=r,this.moved=y.WalkerMoves.ENTER}getXml(){return this.xml_||(this.xml_=i.parseInput(this.xmlString_)),this.xml_}getRebuilt(){return this.rebuilt_||this.rebuildStree(),this.rebuilt_}isActive(){return this.active_}activate(){this.isActive()||(this.generator.start(),this.toggleActive_())}deactivate(){this.isActive()&&(y.WalkerState.setState(this.id,this.primaryId()),this.generator.end(),this.toggleActive_())}getFocus(e=!1){return this.focus_||(this.focus_=this.singletonFocus(this.rootId)),e&&this.updateFocus(),this.focus_}setFocus(e){this.focus_=e}getDepth(){return this.levels.depth()-1}isSpeech(){return this.generator.modality===l.Attribute.SPEECH}focusDomNodes(){return this.getFocus().getDomNodes()}focusSemanticNodes(){return this.getFocus().getSemanticNodes()}speech(){const e=this.focusDomNodes();if(!e.length)return"";const t=this.specialMove();if(null!==t)return t;switch(this.moved){case y.WalkerMoves.DEPTH:return this.depth_();case y.WalkerMoves.SUMMARY:return this.summary_();case y.WalkerMoves.DETAIL:return this.detail_();default:{const t=[],n=this.focusSemanticNodes();for(let r=0,o=e.length;r<o;r++){const o=e[r],i=n[r];t.push(o?this.generator.getSpeech(o,this.getXml()):f.recomputeMarkup(i))}return this.mergePrefix_(t)}}}move(e){const t=this.keyMapping.get(e);if(!t)return null;const n=t();return!(!n||n===this.getFocus())&&(this.setFocus(n),this.moved===y.WalkerMoves.HOME&&(this.levels=this.initLevels()),!0)}up(){return this.moved=y.WalkerMoves.UP,this.getFocus()}down(){return this.moved=y.WalkerMoves.DOWN,this.getFocus()}left(){return this.moved=y.WalkerMoves.LEFT,this.getFocus()}right(){return this.moved=y.WalkerMoves.RIGHT,this.getFocus()}repeat(){return this.moved=y.WalkerMoves.REPEAT,this.getFocus().clone()}depth(){return this.moved=this.isSpeech()?y.WalkerMoves.DEPTH:y.WalkerMoves.REPEAT,this.getFocus().clone()}home(){this.moved=y.WalkerMoves.HOME;return this.singletonFocus(this.rootId)}getBySemanticId(e){return S.getBySemanticId(this.node,e)}primaryId(){return this.getFocus().getSemanticPrimary().id.toString()}expand(){const e=this.getFocus().getDomPrimary(),t=this.actionable_(e);return t?(this.moved=y.WalkerMoves.EXPAND,t.dispatchEvent(new Event("click")),this.getFocus().clone()):this.getFocus()}expandable(e){return!!this.actionable_(e)&&0===e.childNodes.length}collapsible(e){return!!this.actionable_(e)&&e.childNodes.length>0}restoreState(){if(!this.highlighter)return;const e=y.WalkerState.getState(this.id);if(!e)return;let t=this.getRebuilt().nodeDict[e];const n=[];for(;t;)n.push(t.id),t=t.parent;for(n.pop();n.length>0;){this.down();const e=n.pop(),t=this.findFocusOnLevel(e);if(!t)break;this.setFocus(t)}this.moved=y.WalkerMoves.ENTER}updateFocus(){this.setFocus(g.Focus.factory(this.getFocus().getSemanticPrimary().id.toString(),this.getFocus().getSemanticNodes().map((e=>e.id.toString())),this.getRebuilt(),this.node))}rebuildStree(){this.rebuilt_=new b.RebuildStree(this.getXml()),this.rootId=this.rebuilt_.stree.root.id.toString(),this.generator.setRebuilt(this.rebuilt_),this.skeleton=p.SemanticSkeleton.fromTree(this.rebuilt_.stree),this.skeleton.populate(),this.focus_=this.singletonFocus(this.rootId),this.levels=this.initLevels(),f.connectMactions(this.node,this.getXml(),this.rebuilt_.xml)}previousLevel(){const e=this.getFocus().getDomPrimary();return e?S.getAttribute(e,l.Attribute.PARENT):this.getFocus().getSemanticPrimary().parent.id.toString()}nextLevel(){const e=this.getFocus().getDomPrimary();let t,n;if(e){t=S.splitAttribute(S.getAttribute(e,l.Attribute.CHILDREN)),n=S.splitAttribute(S.getAttribute(e,l.Attribute.CONTENT));const r=S.getAttribute(e,l.Attribute.TYPE),o=S.getAttribute(e,l.Attribute.ROLE);return this.combineContentChildren(r,o,n,t)}const r=e=>e.id.toString(),o=this.getRebuilt().nodeDict[this.primaryId()];return t=o.childNodes.map(r),n=o.contentNodes.map(r),0===t.length?[]:this.combineContentChildren(o.type,o.role,n,t)}singletonFocus(e){this.getRebuilt();const t=this.retrieveVisuals(e);return this.focusFromId(e,t)}retrieveVisuals(e){if(!this.skeleton)return[e];const t=parseInt(e,10),n=this.skeleton.subtreeNodes(t);if(!n.length)return[e];n.unshift(t);const r={},o=[];E.updateEvaluator(this.getXml());for(const e of n)r[e]||(o.push(e.toString()),r[e]=!0,this.subtreeIds(e,r));return o}subtreeIds(e,t){const n=E.evalXPath(`//*[@data-semantic-id="${e}"]`,this.getXml());E.evalXPath("*//@data-semantic-id",n[0]).forEach((e=>t[parseInt(e.textContent,10)]=!0))}focusFromId(e,t){return g.Focus.factory(e,t,this.getRebuilt(),this.node)}summary(){return this.moved=this.isSpeech()?y.WalkerMoves.SUMMARY:y.WalkerMoves.REPEAT,this.getFocus().clone()}detail(){return this.moved=this.isSpeech()?y.WalkerMoves.DETAIL:y.WalkerMoves.REPEAT,this.getFocus().clone()}specialMove(){return null}virtualize(e){return this.cursors.push({focus:this.getFocus(),levels:this.levels,undo:e||!this.cursors.length}),this.levels=this.levels.clone(),this.getFocus().clone()}previous(){const e=this.cursors.pop();return e?(this.levels=e.levels,e.focus):this.getFocus()}undo(){let e;do{e=this.cursors.pop()}while(e&&!e.undo);return e?(this.levels=e.levels,e.focus):this.getFocus()}update(e){this.generator.setOptions(e),(0,a.setup)(e).then((()=>h.generator("Tree").getSpeech(this.node,this.getXml())))}nextRules(){const e=this.generator.getOptions();return"speech"!==e.modality?this.getFocus():(s.DOMAIN_TO_STYLES[e.domain]=e.style,e.domain="mathspeak"===e.domain?"clearspeak":"mathspeak",e.style=s.DOMAIN_TO_STYLES[e.domain],this.update(e),this.moved=y.WalkerMoves.REPEAT,this.getFocus().clone())}nextStyle(e,t){if("mathspeak"===e){const e=["default","brief","sbrief"],n=e.indexOf(t);return-1===n?t:n>=e.length-1?e[0]:e[n+1]}if("clearspeak"===e){const e=m.ClearspeakPreferences.getLocalePreferences().en;if(!e)return"default";const n=m.ClearspeakPreferences.relevantPreferences(this.getFocus().getSemanticPrimary()),r=m.ClearspeakPreferences.findPreference(t,n),o=e[n].map((function(e){return e.split("_")[1]})),i=o.indexOf(r);if(-1===i)return t;const s=i>=o.length-1?o[0]:o[i+1];return m.ClearspeakPreferences.addPreference(t,n,s)}return t}previousRules(){const e=this.generator.getOptions();return"speech"!==e.modality?this.getFocus():(e.style=this.nextStyle(e.domain,e.style),this.update(e),this.moved=y.WalkerMoves.REPEAT,this.getFocus().clone())}refocus(){let e,t=this.getFocus();for(;!t.getNodes().length;){e=this.levels.peek();const n=this.up();if(!n)break;this.setFocus(n),t=this.getFocus(!0)}this.levels.push(e),this.setFocus(t)}toggleActive_(){this.active_=!this.active_}mergePrefix_(e,t=[]){const n=this.isSpeech()?this.prefix_():"";n&&e.unshift(n);const r=this.isSpeech()?this.postfix_():"";return r&&e.push(r),o.finalize(o.merge(t.concat(e)))}prefix_(){const e=this.getFocus().getDomNodes(),t=this.getFocus().getSemanticNodes();return e[0]?S.getAttribute(e[0],l.Attribute.PREFIX):f.retrievePrefix(t[0])}postfix_(){const e=this.getFocus().getDomNodes();return e[0]?S.getAttribute(e[0],l.Attribute.POSTFIX):""}depth_(){const e=d.Grammar.getInstance().getParameter("depth");d.Grammar.getInstance().setParameter("depth",!0);const t=this.getFocus().getDomPrimary(),n=this.expandable(t)?u.LOCALE.MESSAGES.navigate.EXPANDABLE:this.collapsible(t)?u.LOCALE.MESSAGES.navigate.COLLAPSIBLE:"",i=u.LOCALE.MESSAGES.navigate.LEVEL+" "+this.getDepth(),s=this.getFocus().getSemanticNodes(),a=f.retrievePrefix(s[0]),c=[new r.AuditoryDescription({text:i,personality:{}}),new r.AuditoryDescription({text:a,personality:{}}),new r.AuditoryDescription({text:n,personality:{}})];return d.Grammar.getInstance().setParameter("depth",e),o.finalize(o.markup(c))}actionable_(e){const t=null==e?void 0:e.parentNode;return t&&this.highlighter.isMactionNode(t)?t:null}summary_(){const e=this.getFocus().getSemanticPrimary().id.toString(),t=this.getRebuilt().xml.getAttribute("id")===e?this.getRebuilt().xml:i.querySelectorAllByAttrValue(this.getRebuilt().xml,"id",e)[0],n=f.retrieveSummary(t);return this.mergePrefix_([n])}detail_(){const e=this.getFocus().getSemanticPrimary().id.toString(),t=this.getRebuilt().xml.getAttribute("id")===e?this.getRebuilt().xml:i.querySelectorAllByAttrValue(this.getRebuilt().xml,"id",e)[0],n=t.getAttribute("alternative");t.removeAttribute("alternative");const r=f.computeMarkup(t),o=this.mergePrefix_([r]);return t.setAttribute("alternative",n),o}}t.AbstractWalker=N,N.ID_COUNTER=0,N.SRE_ID_ATTR="sre-explorer-id"},4296:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DummyWalker=void 0;const r=n(4660);class o extends r.AbstractWalker{up(){return null}down(){return null}left(){return null}right(){return null}repeat(){return null}depth(){return null}home(){return null}getDepth(){return 0}initLevels(){return null}combineContentChildren(e,t,n,r){return[]}findFocusOnLevel(e){return null}}t.DummyWalker=o},5658:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Focus=void 0;const r=n(8835);class o{constructor(e,t){this.nodes=e,this.primary=t,this.domNodes=[],this.domPrimary_=null,this.allNodes=[]}static factory(e,t,n,i){const s=e=>r.getBySemanticId(i,e),a=n.nodeDict,c=s(e),l=t.map(s),u=t.map((function(e){return a[e]})),d=new o(u,a[e]);return d.domNodes=l,d.domPrimary_=c,d.allNodes=o.generateAllVisibleNodes_(t,l,a,i),d}static generateAllVisibleNodes_(e,t,n,i){const s=e=>r.getBySemanticId(i,e);let a=[];for(let r=0,c=e.length;r<c;r++){if(t[r]){a.push(t[r]);continue}const c=n[e[r]];if(!c)continue;const l=c.childNodes.map((function(e){return e.id.toString()})),u=l.map(s);a=a.concat(o.generateAllVisibleNodes_(l,u,n,i))}return a}getSemanticPrimary(){return this.primary}getSemanticNodes(){return this.nodes}getNodes(){return this.allNodes}getDomNodes(){return this.domNodes}getDomPrimary(){return this.domPrimary_}toString(){return"Primary:"+this.domPrimary_+" Nodes:"+this.domNodes}clone(){const e=new o(this.nodes,this.primary);return e.domNodes=this.domNodes,e.domPrimary_=this.domPrimary_,e.allNodes=this.allNodes,e}}t.Focus=o},1497:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Levels=void 0;class n{constructor(){this.level_=[]}push(e){this.level_.push(e)}pop(){return this.level_.pop()}peek(){return this.level_[this.level_.length-1]||null}indexOf(e){const t=this.peek();return t?t.indexOf(e):null}find(e){const t=this.peek();if(!t)return null;for(let n=0,r=t.length;n<r;n++)if(e(t[n]))return t[n];return null}get(e){const t=this.peek();return!t||e<0||e>=t.length?null:t[e]}depth(){return this.level_.length}clone(){const e=new n;return e.level_=this.level_.slice(0),e}toString(){let e="";for(let t,n=0;t=this.level_[n];n++)e+="\n"+t.map((function(e){return e.toString()}));return e}}t.Levels=n},1848:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.RebuildStree=void 0;const r=n(6671),o=n(8171),i=n(4020),s=n(4790),a=n(7793),c=n(7984),l=n(1784),u=n(8901),d=n(8835);class p{constructor(e){this.mathml=e,this.factory=new s.SemanticNodeFactory,this.nodeDict={},this.mmlRoot=d.getSemanticRoot(e),this.streeRoot=this.assembleTree(this.mmlRoot),this.stree=l.SemanticTree.fromNode(this.streeRoot,this.mathml),this.xml=this.stree.xml(),a.default.getInstance().setNodeFactory(this.factory)}static addAttributes(e,t,n){n&&1===t.childNodes.length&&t.childNodes[0].nodeType!==r.NodeType.TEXT_NODE&&u.addAttributes(e,t.childNodes[0]),u.addAttributes(e,t)}static textContent(e,t,n){if(!n&&t.textContent)return void(e.textContent=t.textContent);const r=d.splitAttribute(d.getAttribute(t,o.Attribute.OPERATOR));r.length>1&&(e.textContent=r[1])}static isPunctuated(e){return!c.SemanticSkeleton.simpleCollapseStructure(e)&&e[1]&&c.SemanticSkeleton.contentCollapseStructure(e[1])}getTree(){return this.stree}assembleTree(e){const t=this.makeNode(e),n=d.splitAttribute(d.getAttribute(e,o.Attribute.CHILDREN)),r=d.splitAttribute(d.getAttribute(e,o.Attribute.CONTENT));if(p.addAttributes(t,e,!(n.length||r.length)),0===r.length&&0===n.length)return p.textContent(t,e),t;if(r.length>0){const e=d.getBySemanticId(this.mathml,r[0]);e&&p.textContent(t,e,!0)}t.contentNodes=r.map((e=>this.setParent(e,t))),t.childNodes=n.map((e=>this.setParent(e,t)));const i=d.getAttribute(e,o.Attribute.COLLAPSED);return i?this.postProcess(t,i):t}makeNode(e){const t=d.getAttribute(e,o.Attribute.TYPE),n=d.getAttribute(e,o.Attribute.ROLE),r=d.getAttribute(e,o.Attribute.FONT),i=d.getAttribute(e,o.Attribute.ANNOTATION)||"",s=d.getAttribute(e,o.Attribute.ID),a=d.getAttribute(e,o.Attribute.EMBELLISHED),c=d.getAttribute(e,o.Attribute.FENCEPOINTER),l=this.createNode(parseInt(s,10));return l.type=t,l.role=n,l.font=r||"unknown",l.parseAnnotation(i),c&&(l.fencePointer=c),a&&(l.embellished=a),l}makePunctuation(e){const t=this.createNode(e);return t.updateContent((0,i.invisibleComma)()),t.role="dummy",t}makePunctuated(e,t,n){const r=this.createNode(t[0]);r.type="punctuated",r.embellished=e.embellished,r.fencePointer=e.fencePointer,r.role=n;const o=t.splice(1,1)[0].slice(1);r.contentNodes=o.map(this.makePunctuation.bind(this)),this.collapsedChildren_(t)}makeEmpty(e,t,n){const r=this.createNode(t);r.type="empty",r.embellished=e.embellished,r.fencePointer=e.fencePointer,r.role=n}makeIndex(e,t,n){if(p.isPunctuated(t))return this.makePunctuated(e,t,n),void(t=t[0]);c.SemanticSkeleton.simpleCollapseStructure(t)&&!this.nodeDict[t.toString()]&&this.makeEmpty(e,t,n)}postProcess(e,t){const n=c.SemanticSkeleton.fromString(t).array;if("subsup"===e.type){const t=this.createNode(n[1][0]);return t.type="subscript",t.role="subsup",e.type="superscript",t.embellished=e.embellished,t.fencePointer=e.fencePointer,this.makeIndex(e,n[1][2],"rightsub"),this.makeIndex(e,n[2],"rightsuper"),this.collapsedChildren_(n),e}if("subscript"===e.type)return this.makeIndex(e,n[2],"rightsub"),this.collapsedChildren_(n),e;if("superscript"===e.type)return this.makeIndex(e,n[2],"rightsuper"),this.collapsedChildren_(n),e;if("tensor"===e.type)return this.makeIndex(e,n[2],"leftsub"),this.makeIndex(e,n[3],"leftsuper"),this.makeIndex(e,n[4],"rightsub"),this.makeIndex(e,n[5],"rightsuper"),this.collapsedChildren_(n),e;if("punctuated"===e.type){if(p.isPunctuated(n)){const t=n.splice(1,1)[0].slice(1);e.contentNodes=t.map(this.makePunctuation.bind(this))}return e}if("underover"===e.type){const t=this.createNode(n[1][0]);return"overaccent"===e.childNodes[1].role?(t.type="overscore",e.type="underscore"):(t.type="underscore",e.type="overscore"),t.role="underover",t.embellished=e.embellished,t.fencePointer=e.fencePointer,this.collapsedChildren_(n),e}return e}createNode(e){const t=this.factory.makeNode(e);return this.nodeDict[e.toString()]=t,t}collapsedChildren_(e){const t=e=>{const n=this.nodeDict[e[0]];n.childNodes=[];for(let r=1,o=e.length;r<o;r++){const o=e[r];n.childNodes.push(c.SemanticSkeleton.simpleCollapseStructure(o)?this.nodeDict[o]:t(o))}return n};t(e)}setParent(e,t){const n=d.getBySemanticId(this.mathml,e),r=this.assembleTree(n);return r.parent=t,r}}t.RebuildStree=p},1937:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticWalker=void 0;const r=n(4660),o=n(1497);class i extends r.AbstractWalker{constructor(e,t,n,r){super(e,t,n,r),this.node=e,this.generator=t,this.highlighter=n,this.levels=null,this.restoreState()}initLevels(){const e=new o.Levels;return e.push([this.getFocus()]),e}up(){super.up();const e=this.previousLevel();if(!e)return null;this.levels.pop();return this.levels.find((function(t){return t.getSemanticNodes().some((function(t){return t.id.toString()===e}))}))}down(){super.down();const e=this.nextLevel();return 0===e.length?null:(this.levels.push(e),e[0])}combineContentChildren(e,t,n,r){switch(e){case"relseq":case"infixop":case"multirel":return this.makePairList(r,n);case"prefixop":return[this.focusFromId(r[0],n.concat(r))];case"postfixop":return[this.focusFromId(r[0],r.concat(n))];case"matrix":case"vector":case"fenced":return[this.focusFromId(r[0],[n[0],r[0],n[1]])];case"cases":return[this.focusFromId(r[0],[n[0],r[0]])];case"punctuated":return"text"===t?r.map(this.singletonFocus.bind(this)):r.length===n.length?n.map(this.singletonFocus.bind(this)):this.combinePunctuations(r,n,[],[]);case"appl":return[this.focusFromId(r[0],[r[0],n[0]]),this.singletonFocus(r[1])];case"root":return[this.singletonFocus(r[1]),this.singletonFocus(r[0])];default:return r.map(this.singletonFocus.bind(this))}}combinePunctuations(e,t,n,r){if(0===e.length)return r;const o=e.shift(),i=t.shift();return o===i?(n.push(i),this.combinePunctuations(e,t,n,r)):(t.unshift(i),n.push(o),e.length===t.length?(r.push(this.focusFromId(o,n.concat(t))),r):(r.push(this.focusFromId(o,n)),this.combinePunctuations(e,t,[],r)))}makePairList(e,t){if(0===e.length)return[];if(1===e.length)return[this.singletonFocus(e[0])];const n=[this.singletonFocus(e.shift())];for(let r=0,o=e.length;r<o;r++)n.push(this.focusFromId(e[r],[t[r],e[r]]));return n}left(){super.left();const e=this.levels.indexOf(this.getFocus());if(null===e)return null;const t=this.levels.get(e-1);return t||null}right(){super.right();const e=this.levels.indexOf(this.getFocus());if(null===e)return null;const t=this.levels.get(e+1);return t||null}findFocusOnLevel(e){return this.levels.find((t=>t.getSemanticPrimary().id===e))}}t.SemanticWalker=i},3531:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SyntaxWalker=void 0;const r=n(1426),o=n(4660),i=n(1497);class s extends o.AbstractWalker{constructor(e,t,n,r){super(e,t,n,r),this.node=e,this.generator=t,this.highlighter=n,this.levels=null,this.restoreState()}initLevels(){const e=new i.Levels;return e.push([this.primaryId()]),e}up(){super.up();const e=this.previousLevel();return e?(this.levels.pop(),this.singletonFocus(e)):null}down(){super.down();const e=this.nextLevel();if(0===e.length)return null;const t=this.singletonFocus(e[0]);return t&&this.levels.push(e),t}combineContentChildren(e,t,n,o){switch(e){case"relseq":case"infixop":case"multirel":return(0,r.interleaveLists)(o,n);case"prefixop":return n.concat(o);case"postfixop":return o.concat(n);case"matrix":case"vector":case"fenced":return o.unshift(n[0]),o.push(n[1]),o;case"cases":return o.unshift(n[0]),o;case"punctuated":return"text"===t?(0,r.interleaveLists)(o,n):o;case"appl":return[o[0],n[0],o[1]];case"root":return[o[1],o[0]];default:return o}}left(){super.left();const e=this.levels.indexOf(this.primaryId());if(null===e)return null;const t=this.levels.get(e-1);return t?this.singletonFocus(t):null}right(){super.right();const e=this.levels.indexOf(this.primaryId());if(null===e)return null;const t=this.levels.get(e+1);return t?this.singletonFocus(t):null}findFocusOnLevel(e){return this.singletonFocus(e.toString())}focusDomNodes(){return[this.getFocus().getDomPrimary()]}focusSemanticNodes(){return[this.getFocus().getSemanticPrimary()]}}t.SyntaxWalker=s},4051:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.TableWalker=void 0;const r=n(6671),o=n(6988),i=n(3531),s=n(8119);class a extends i.SyntaxWalker{constructor(e,t,n,r){super(e,t,n,r),this.node=e,this.generator=t,this.highlighter=n,this.firstJump=null,this.key_=null,this.row_=0,this.currentTable_=null,this.keyMapping.set(o.KeyCode.ZERO,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.ONE,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.TWO,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.THREE,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.FOUR,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.FIVE,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.SIX,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.SEVEN,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.EIGHT,this.jumpCell.bind(this)),this.keyMapping.set(o.KeyCode.NINE,this.jumpCell.bind(this))}move(e){this.key_=e;const t=super.move(e);return this.modifier=!1,t}up(){return this.moved=s.WalkerMoves.UP,this.eligibleCell_()?this.verticalMove_(!1):super.up()}down(){return this.moved=s.WalkerMoves.DOWN,this.eligibleCell_()?this.verticalMove_(!0):super.down()}jumpCell(){if(!this.isInTable_()||null===this.key_)return this.getFocus();if(this.moved===s.WalkerMoves.ROW){this.moved=s.WalkerMoves.CELL;const e=this.key_-o.KeyCode.ZERO;return this.isLegalJump_(this.row_,e)?this.jumpCell_(this.row_,e):this.getFocus()}const e=this.key_-o.KeyCode.ZERO;return e>this.currentTable_.childNodes.length?this.getFocus():(this.row_=e,this.moved=s.WalkerMoves.ROW,this.getFocus().clone())}undo(){const e=super.undo();return e===this.firstJump&&(this.firstJump=null),e}eligibleCell_(){const e=this.getFocus().getSemanticPrimary();return this.modifier&&"cell"===e.type&&-1!==a.ELIGIBLE_CELL_ROLES.indexOf(e.role)}verticalMove_(e){const t=this.previousLevel();if(!t)return null;const n=this.getFocus(),r=this.levels.indexOf(this.primaryId()),o=this.levels.pop(),i=this.levels.indexOf(t),s=this.levels.get(e?i+1:i-1);if(!s)return this.levels.push(o),null;this.setFocus(this.singletonFocus(s));const a=this.nextLevel();return a[r]?(this.levels.push(a),this.singletonFocus(a[r])):(this.setFocus(n),this.levels.push(o),null)}jumpCell_(e,t){this.firstJump?this.virtualize(!1):(this.firstJump=this.getFocus(),this.virtualize(!0));const n=this.currentTable_.id.toString();let r;do{r=this.levels.pop()}while(-1===r.indexOf(n));this.levels.push(r),this.setFocus(this.singletonFocus(n)),this.levels.push(this.nextLevel());const o=this.currentTable_.childNodes[e-1];return this.setFocus(this.singletonFocus(o.id.toString())),this.levels.push(this.nextLevel()),this.singletonFocus(o.childNodes[t-1].id.toString())}isLegalJump_(e,t){const n=r.querySelectorAllByAttrValue(this.getRebuilt().xml,"id",this.currentTable_.id.toString())[0];if(!n||n.hasAttribute("alternative"))return!1;const o=this.currentTable_.childNodes[e-1];if(!o)return!1;const i=r.querySelectorAllByAttrValue(n,"id",o.id.toString())[0];return!(!i||i.hasAttribute("alternative"))&&!(!o||!o.childNodes[t-1])}isInTable_(){let e=this.getFocus().getSemanticPrimary();for(;e;){if(-1!==a.ELIGIBLE_TABLE_TYPES.indexOf(e.type))return this.currentTable_=e,!0;e=e.parent}return!1}}t.TableWalker=a,a.ELIGIBLE_CELL_ROLES=["determinant","rowvector","binomial","squarematrix","multiline","matrix","vector","cases","table"],a.ELIGIBLE_TABLE_TYPES=["multiline","matrix","vector","cases","table"]},8119:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.WalkerState=t.WalkerMoves=void 0,function(e){e.UP="up",e.DOWN="down",e.LEFT="left",e.RIGHT="right",e.REPEAT="repeat",e.DEPTH="depth",e.ENTER="enter",e.EXPAND="expand",e.HOME="home",e.SUMMARY="summary",e.DETAIL="detail",e.ROW="row",e.CELL="cell"}(t.WalkerMoves||(t.WalkerMoves={}));class n{static resetState(e){delete n.STATE[e]}static setState(e,t){n.STATE[e]=t}static getState(e){return n.STATE[e]}}t.WalkerState=n,n.STATE={}},907:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.walkerMapping_=t.walker=void 0;const r=n(4296),o=n(1937),i=n(3531),s=n(4051);t.walker=function(e,n,r,o,i){return(t.walkerMapping_[e.toLowerCase()]||t.walkerMapping_.dummy)(n,r,o,i)},t.walkerMapping_={dummy:(e,t,n,o)=>new r.DummyWalker(e,t,n,o),semantic:(e,t,n,r)=>new o.SemanticWalker(e,t,n,r),syntax:(e,t,n,r)=>new i.SyntaxWalker(e,t,n,r),table:(e,t,n,r)=>new s.TableWalker(e,t,n,r)}},8835:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getBySemanticId=t.getSemanticRoot=t.getAttribute=t.splitAttribute=void 0;const r=n(6671),o=n(8171);t.splitAttribute=function(e){return e?e.split(/,/):[]},t.getAttribute=function(e,t){return e.getAttribute(t)},t.getSemanticRoot=function(e){if(e.hasAttribute(o.Attribute.TYPE)&&!e.hasAttribute(o.Attribute.PARENT))return e;const t=r.querySelectorAllByAttr(e,o.Attribute.TYPE);for(let e,n=0;e=t[n];n++)if(!e.hasAttribute(o.Attribute.PARENT))return e;return e},t.getBySemanticId=function(e,t){return e.getAttribute(o.Attribute.ID)===t?e:r.querySelectorAllByAttrValue(e,o.Attribute.ID,t)[0]}}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var __webpack_exports__={},global,version,MJContextMenu,Menu,MenuHandler,MmlVisitor,SelectableInfo;global=__webpack_require__(8723),version=__webpack_require__(7306),MJContextMenu=__webpack_require__(3021),Menu=__webpack_require__(1062),MenuHandler=__webpack_require__(9003),MmlVisitor=__webpack_require__(9232),SelectableInfo=__webpack_require__(7309),MathJax.loader&&MathJax.loader.checkVersion("ui/menu",version.q,"ui"),(0,global.combineWithMathJax)({_:{ui:{menu:{MJContextMenu:MJContextMenu,Menu:Menu,MenuHandler:MenuHandler,MmlVisitor:MmlVisitor,SelectableInfo:SelectableInfo}}}}),MathJax.startup&&"undefined"!=typeof window&&MathJax.startup.extendHandler((function(e){return(0,MenuHandler.MenuHandler)(e)}),20)})();