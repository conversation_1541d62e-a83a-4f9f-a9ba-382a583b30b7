{"version": 3, "file": "mglyph.js", "sourceRoot": "", "sources": ["../../../../ts/output/common/Wrappers/mglyph.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEA,SAAgB,iBAAiB,CAA+B,IAAO;IAErE;QAAqB,2BAAI;QAwBvB;YAAY,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAA1B,wDACW,IAAI,mBAEd;YADC,KAAI,CAAC,aAAa,EAAE,CAAC;;QACvB,CAAC;QAKM,+BAAa,GAApB;YACQ,IAAA,KACJ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,EADpE,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,GAAG,SAAA,EAAE,KAAK,WACmC,CAAC;YAC5E,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;aAC7C;iBAAM;gBACL,IAAM,IAAI,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAe,CAAC,CAAC,CAAC;gBAC7D,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;gBACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACpF,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAyB,CAAC;aACrD;QACH,CAAC;QAKM,6BAAW,GAAlB,UAAmB,IAAU,EAAE,UAA2B;YAA3B,2BAAA,EAAA,kBAA2B;YACxD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;aAC7C;iBAAM;gBACL,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBACpB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBACnC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;aACvB;QACH,CAAC;QAEH,cAAC;IAAD,CAAC,AA5DM,CAAc,IAAI,GA4DvB;AAEJ,CAAC;AAhED,8CAgEC"}