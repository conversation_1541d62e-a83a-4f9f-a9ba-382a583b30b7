/*************************************************************
 *
 *  Copyright (c) 2017-2022 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import * as Entities from '../Entities.js';

Entities.add({
    DD: '\u2145',
    DDotrahd: '\u2911',
    DJcy: '\u0402',
    DScy: '\u0405',
    DZcy: '\u040F',
    Darr: '\u21A1',
    Dashv: '\u2AE4',
    D<PERSON>on: '\u010E',
    Dcy: '\u0414',
    DiacriticalAcute: '\u00B4',
    DiacriticalDot: '\u02D9',
    DiacriticalDoubleAcute: '\u02DD',
    DiacriticalGrave: '\u0060',
    DiacriticalTilde: '\u02DC',
    Dot: '\u00A8',
    DotDot: '\u20DC',
    DoubleContourIntegral: '\u222F',
    DoubleDownArrow: '\u21D3',
    DoubleLeftArrow: '\u21D0',
    DoubleLeftRightArrow: '\u21D4',
    DoubleLeftTee: '\u2AE4',
    DoubleLongLeftArrow: '\u27F8',
    DoubleLongLeftRightArrow: '\u27FA',
    DoubleLongRightArrow: '\u27F9',
    DoubleRightArrow: '\u21D2',
    DoubleUpArrow: '\u21D1',
    DoubleUpDownArrow: '\u21D5',
    DownArrowBar: '\u2913',
    DownArrowUpArrow: '\u21F5',
    DownBreve: '\u0311',
    DownLeftRightVector: '\u2950',
    DownLeftTeeVector: '\u295E',
    DownLeftVectorBar: '\u2956',
    DownRightTeeVector: '\u295F',
    DownRightVectorBar: '\u2957',
    DownTeeArrow: '\u21A7',
    Dstrok: '\u0110',
    dArr: '\u21D3',
    dHar: '\u2965',
    darr: '\u2193',
    dash: '\u2010',
    dashv: '\u22A3',
    dbkarow: '\u290F',
    dblac: '\u02DD',
    dcaron: '\u010F',
    dcy: '\u0434',
    dd: '\u2146',
    ddagger: '\u2021',
    ddotseq: '\u2A77',
    demptyv: '\u29B1',
    dfisht: '\u297F',
    dharl: '\u21C3',
    dharr: '\u21C2',
    diam: '\u22C4',
    diamond: '\u22C4',
    diamondsuit: '\u2666',
    diams: '\u2666',
    die: '\u00A8',
    disin: '\u22F2',
    divide: '\u00F7',
    divonx: '\u22C7',
    djcy: '\u0452',
    dlcorn: '\u231E',
    dlcrop: '\u230D',
    dollar: '\u0024',
    doteq: '\u2250',
    dotminus: '\u2238',
    doublebarwedge: '\u2306',
    downarrow: '\u2193',
    downdownarrows: '\u21CA',
    downharpoonleft: '\u21C3',
    downharpoonright: '\u21C2',
    drbkarow: '\u2910',
    drcorn: '\u231F',
    drcrop: '\u230C',
    dscy: '\u0455',
    dsol: '\u29F6',
    dstrok: '\u0111',
    dtri: '\u25BF',
    dtrif: '\u25BE',
    duarr: '\u21F5',
    duhar: '\u296F',
    dwangle: '\u29A6',
    dzcy: '\u045F',
    dzigrarr: '\u27FF'
}, 'd');
