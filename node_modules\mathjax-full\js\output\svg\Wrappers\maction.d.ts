import { SVGWrapper, SVGConstructor } from '../Wrapper.js';
import { EventHandler } from '../../common/Wrappers/maction.js';
import { StyleList } from '../../../util/StyleList.js';
declare const SVGmaction_base: import("../../common/Wrappers/maction.js").MactionConstructor<SVGWrapper<any, any, any>> & SVGConstructor<any, any, any>;
export declare class SVGmaction<N, T, D> extends SVGmaction_base {
    static kind: string;
    static styles: StyleList;
    static actions: Map<string, [import("../../common/Wrappers/maction.js").ActionHandler<SVGmaction<any, any, any>>, import("../../common/Wrappers/maction.js").ActionData]>;
    toSVG(parent: N): void;
    setEventHandler(type: string, handler: EventHandler): void;
}
export {};
