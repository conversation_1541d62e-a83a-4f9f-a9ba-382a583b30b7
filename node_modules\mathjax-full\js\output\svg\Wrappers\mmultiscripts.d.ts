import { SVGWrapper, Constructor } from '../Wrapper.js';
import { SVGmsubsup } from './msubsup.js';
export declare type AlignFunction = (w: number, W: number) => number;
export declare function AlignX(align: string): AlignFunction;
declare const SVGmmultiscripts_base: import("../../common/Wrappers/mmultiscripts.js").MmultiscriptsConstructor<SVGWrapper<any, any, any>> & Constructor<SVGmsubsup<any, any, any>>;
export declare class SVGmmultiscripts<N, T, D> extends SVGmmultiscripts_base {
    static kind: string;
    toSVG(parent: N): void;
    protected addScripts(x: number, u: number, v: number, i: number, n: number, align: string): number;
}
export {};
